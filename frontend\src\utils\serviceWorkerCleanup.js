/**
 * Service Worker Cleanup Utility
 *
 * This utility checks for service worker issues on page load and unregisters them if needed.
 * It's used to prevent service workers from interfering with WebSocket connections.
 */

import WebSocketService from '../services/WebSocketService';

/**
 * Initialize service worker cleanup
 */
export function initServiceWorkerCleanup() {
  try {
    // Check for service worker issues on page load
    if (typeof WebSocketService.checkServiceWorkerIssues === 'function') {
      WebSocketService.checkServiceWorkerIssues()
        .then(cleaned => {
          if (cleaned) {
            console.log('Service worker cleanup completed successfully');
          }
        })
        .catch(error => {
          console.error('Error during service worker cleanup:', error);
        });
    } else {
      console.warn('WebSocketService.checkServiceWorkerIssues is not a function');

      // Fallback to our own cleanup if WebSocketService method is not available
      if ('serviceWorker' in navigator && localStorage.getItem('sw_needs_cleanup') === 'true') {
        console.log('Service worker cleanup needed, unregistering all service workers');
        unregisterAllServiceWorkers()
          .then(success => {
            console.log('Service worker cleanup completed:', success);
          })
          .catch(error => {
            console.error('Error during service worker cleanup:', error);
          });
      }
    }

    // Add event listener to handle service worker updates
    if ('serviceWorker' in navigator) {
      // Listen for service worker updates
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service worker controller changed');
      });
    }
  } catch (error) {
    console.error('Error in service worker cleanup initialization:', error);
  }
}

/**
 * Unregister all service workers
 * @returns {Promise<boolean>} Promise that resolves to true if all service workers were unregistered
 */
export function unregisterAllServiceWorkers() {
  if ('serviceWorker' in navigator) {
    console.log('Unregistering all service workers');

    return navigator.serviceWorker.getRegistrations()
      .then(registrations => {
        const unregisterPromises = registrations.map(registration => {
          return registration.unregister()
            .then(success => {
              if (success) {
                console.log('Successfully unregistered service worker:', registration.scope);
              } else {
                console.log('Failed to unregister service worker:', registration.scope);
              }
              return success;
            })
            .catch(error => {
              console.error('Error unregistering service worker:', error);
              return false;
            });
        });

        return Promise.all(unregisterPromises)
          .then(results => {
            const allSuccess = results.every(success => success);

            // Clear the flag if all service workers were unregistered
            if (allSuccess) {
              localStorage.removeItem('sw_needs_cleanup');
            }

            return allSuccess;
          });
      })
      .catch(error => {
        console.error('Error getting service worker registrations:', error);
        return false;
      });
  }

  return Promise.resolve(false);
}

// Export a function to check if service workers are registered
export function checkServiceWorkers() {
  if ('serviceWorker' in navigator) {
    return navigator.serviceWorker.getRegistrations()
      .then(registrations => {
        return registrations.length > 0;
      })
      .catch(() => {
        return false;
      });
  }

  return Promise.resolve(false);
}

// Initialize service worker cleanup on module import
initServiceWorkerCleanup();
