# App Builder 201 - Developer Guide

## Architecture Overview

App Builder 201 is built using a modern web application architecture with a React frontend and a Django backend. The application uses WebSockets for real-time communication and Redux for state management.

### Frontend Architecture

The frontend is built using React and follows a component-based architecture. The main components are:

- **App**: The main application component that handles routing and initialization.
- **AppBuilderEnhanced**: The enhanced version of the App Builder with improved UI/UX.
- **Component Builder**: A component for creating and customizing UI components.
- **Layout Designer**: A component for designing layouts.
- **Theme Manager**: A component for managing themes.
- **WebSocket Manager**: A component for managing WebSocket connections.
- **Data Management**: A component for managing data.
- **Code Exporter**: A component for exporting code.

### Backend Architecture

The backend is built using Django and follows a RESTful API architecture. The main components are:

- **API**: RESTful API endpoints for CRUD operations.
- **WebSocket**: WebSocket endpoints for real-time communication.
- **Authentication**: JWT-based authentication.
- **Database**: SQLite for development, PostgreSQL for production.

### Communication

The frontend and backend communicate using HTTP requests for CRUD operations and WebSockets for real-time updates.

## Development Setup

### Prerequisites

- Node.js (v14 or higher)
- Python (v3.8 or higher)
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/app-builder-201.git
   cd app-builder-201
   ```

2. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

3. Install backend dependencies:
   ```bash
   cd ../backend
   pip install -r requirements.txt
   ```

### Running the Application

1. Start the backend server:
   ```bash
   cd backend
   python manage.py runserver
   ```

2. Start the frontend server:
   ```bash
   cd frontend
   npm start
   ```

3. Open your browser and navigate to `http://localhost:3000`

## Code Structure

### Frontend

```
frontend/
├── public/
│   ├── index.html
│   └── ...
├── src/
│   ├── components/
│   │   ├── enhanced/
│   │   │   ├── ComponentBuilder.js
│   │   │   ├── LayoutDesigner.js
│   │   │   ├── ThemeManager.js
│   │   │   ├── FixedWebSocketManager.js
│   │   │   ├── ProjectManager.js
│   │   │   ├── CodeExporter.js
│   │   │   ├── PerformanceMonitor.js
│   │   │   ├── DataManagementDemo.js
│   │   │   └── TestingTools.js
│   │   ├── a11y/
│   │   │   └── ...
│   │   └── layout/
│   │       └── ...
│   ├── config/
│   │   ├── env.js
│   │   └── ...
│   ├── design-system/
│   │   └── ...
│   ├── hooks/
│   │   ├── useWebSocket.js
│   │   └── ...
│   ├── pages/
│   │   ├── AppBuilderPage.js
│   │   ├── AppBuilderEnhanced.js
│   │   ├── HomePage.js
│   │   ├── NotFoundPage.js
│   │   ├── SimplifiedAppBuilder.js
│   │   └── WebSocketPage.js
│   ├── redux/
│   │   ├── actions/
│   │   │   └── ...
│   │   ├── reducers/
│   │   │   ├── index.js
│   │   │   ├── uiReducer.js
│   │   │   └── ...
│   │   ├── store.js
│   │   └── minimal-store.js
│   ├── services/
│   │   ├── WebSocketClient.js
│   │   ├── EnhancedWebSocketClient.js
│   │   └── ...
│   ├── styles/
│   │   └── ...
│   ├── theme/
│   │   ├── variables.css
│   │   └── ...
│   ├── utils/
│   │   └── ...
│   ├── App.js
│   ├── App.css
│   ├── index.js
│   └── Routes.js
└── package.json
```

### Backend

```
backend/
├── app_builder_201/
│   ├── asgi.py
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── my_app/
│   ├── consumers/
│   │   ├── app_builder_consumer.py
│   │   ├── health_check_consumer.py
│   │   ├── simple_echo_consumer.py
│   │   ├── simple_test_consumer.py
│   │   └── test_consumer.py
│   ├── middleware.py
│   ├── models.py
│   ├── routing.py
│   ├── urls.py
│   └── views.py
├── websockets/
│   ├── consumers/
│   │   ├── app_builder.py
│   │   ├── base.py
│   │   ├── collaboration.py
│   │   ├── notifications.py
│   │   └── performance.py
│   └── routing.py
└── manage.py
```

## Adding New Features

### Adding a New Component

1. Create a new component in the `frontend/src/components/enhanced` directory:

```javascript
import React from 'react';
import { Card, Typography } from 'antd';

const { Title, Paragraph } = Typography;

const MyNewComponent = () => {
  return (
    <Card title="My New Component">
      <Title level={4}>Welcome to My New Component</Title>
      <Paragraph>
        This is a new component for the App Builder.
      </Paragraph>
    </Card>
  );
};

export default MyNewComponent;
```

2. Add the component to the `AppBuilderEnhanced.js` file:

```javascript
import MyNewComponent from '../components/enhanced/MyNewComponent';

// ...

const tabItems = [
  // ...
  {
    key: 'my-new-component',
    label: (
      <span>
        <CustomIcon /> My New Component
      </span>
    ),
    children: <MyNewComponent />
  },
  // ...
];
```

### Adding a New API Endpoint

1. Create a new view in the `backend/my_app/views.py` file:

```python
from rest_framework.decorators import api_view
from rest_framework.response import Response

@api_view(['GET'])
def my_new_endpoint(request):
    return Response({
        'message': 'This is a new endpoint',
        'data': {
            'key': 'value'
        }
    })
```

2. Add the endpoint to the `backend/my_app/urls.py` file:

```python
from django.urls import path
from . import views

urlpatterns = [
    # ...
    path('api/my-new-endpoint/', views.my_new_endpoint, name='my-new-endpoint'),
    # ...
]
```

### Adding a New WebSocket Consumer

1. Create a new consumer in the `backend/websockets/consumers` directory:

```python
from .base import BaseConsumer

class MyNewConsumer(BaseConsumer):
    """
    My new WebSocket consumer
    """
    
    async def connect(self):
        """
        Handle connection
        """
        await super().connect()
        
        # Send welcome message
        await self.send_json({
            'type': 'welcome',
            'message': 'Welcome to My New Consumer'
        })
    
    async def handle_message(self, message_type, data):
        """
        Handle incoming messages
        """
        if message_type == 'my_message':
            await self.handle_my_message(data)
        else:
            await super().handle_message(message_type, data)
    
    async def handle_my_message(self, data):
        """
        Handle my message
        """
        # Process the message
        response = {
            'type': 'my_response',
            'message': f'Received: {data.get("message", "")}'
        }
        
        # Send response
        await self.send_json(response)
```

2. Add the consumer to the `backend/websockets/routing.py` file:

```python
from django.urls import re_path
from .consumers import MyNewConsumer

websocket_urlpatterns = [
    # ...
    re_path(r'^ws/my-new-consumer/$', MyNewConsumer.as_asgi()),
    # ...
]
```

## Testing

### Frontend Testing

We use Jest and React Testing Library for frontend testing.

```bash
cd frontend
npm test
```

### Backend Testing

We use Django's built-in testing framework for backend testing.

```bash
cd backend
python manage.py test
```

## Deployment

### Frontend Deployment

1. Build the frontend:
   ```bash
   cd frontend
   npm run build
   ```

2. Serve the built files using a static file server like Nginx or Apache.

### Backend Deployment

1. Set up a production-ready database like PostgreSQL.
2. Configure the Django settings for production.
3. Deploy the Django application using a WSGI server like Gunicorn.
4. Set up a reverse proxy using Nginx or Apache.

## Contributing

We welcome contributions to App Builder 201! Please see the [CONTRIBUTING.md](CONTRIBUTING.md) file for more information.

## License

App Builder 201 is licensed under the MIT License. See the [LICENSE](LICENSE) file for more information.
