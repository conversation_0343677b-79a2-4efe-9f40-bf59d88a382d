import React from 'react';
import { Grid } from 'antd';
import PropTypes from 'prop-types';

const { useBreakpoint } = Grid;

/**
 * ResponsiveContainer component that applies responsive classes and provides
 * breakpoint information to child components
 */
const ResponsiveContainer = ({
  children,
  className = '',
  mobileClass = '',
  tabletClass = '',
  desktopClass = '',
  style = {},
  ...props
}) => {
  const screens = useBreakpoint();

  // Determine current breakpoint
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;
  const isDesktop = screens.lg;

  // Build responsive class names
  const getResponsiveClasses = () => {
    let classes = ['responsive-container', className];

    if (isMobile && mobileClass) {
      classes.push(mobileClass);
    }

    if (isTablet && tabletClass) {
      classes.push(tabletClass);
    }

    if (isDesktop && desktopClass) {
      classes.push(desktopClass);
    }

    return classes.filter(Boolean).join(' ');
  };

  // Responsive style adjustments
  const getResponsiveStyles = () => {
    const baseStyles = {
      width: '100%',
      maxWidth: '100%',
      overflowX: 'hidden',
      ...style
    };

    if (isMobile) {
      return {
        ...baseStyles,
        padding: '8px',
        fontSize: '14px'
      };
    }

    if (isTablet) {
      return {
        ...baseStyles,
        padding: '16px',
        fontSize: '15px'
      };
    }

    return {
      ...baseStyles,
      padding: '24px',
      fontSize: '16px'
    };
  };

  return (
    <div
      className={getResponsiveClasses()}
      style={getResponsiveStyles()}
      data-breakpoint={isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop'}
      {...props}
    >
      {typeof children === 'function'
        ? children({ isMobile, isTablet, isDesktop, screens })
        : children
      }
    </div>
  );
};

ResponsiveContainer.propTypes = {
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func]).isRequired,
  className: PropTypes.string,
  mobileClass: PropTypes.string,
  tabletClass: PropTypes.string,
  desktopClass: PropTypes.string,
  style: PropTypes.object
};

export default ResponsiveContainer;

/**
 * Hook for responsive breakpoint detection
 */
export const useResponsive = () => {
  const screens = useBreakpoint();

  return {
    isMobile: !screens.md,
    isTablet: screens.md && !screens.lg,
    isDesktop: screens.lg,
    screens
  };
};

/**
 * Higher-order component for responsive behavior
 */
export const withResponsive = (WrappedComponent) => {
  const ResponsiveComponent = (props) => {
    const responsive = useResponsive();

    return (
      <WrappedComponent
        {...props}
        responsive={responsive}
      />
    );
  };

  ResponsiveComponent.displayName = `withResponsive(${WrappedComponent.displayName || WrappedComponent.name})`;

  return ResponsiveComponent;
};

/**
 * Responsive Grid component
 */
export const ResponsiveGrid = ({
  children,
  columns = { mobile: 1, tablet: 2, desktop: 3 },
  gap = { mobile: 12, tablet: 16, desktop: 24 },
  className = '',
  ...props
}) => {
  const { isMobile, isTablet, isDesktop } = useResponsive();

  const getGridColumns = () => {
    if (isMobile) return columns.mobile;
    if (isTablet) return columns.tablet;
    return columns.desktop;
  };

  const getGridGap = () => {
    if (isMobile) return gap.mobile;
    if (isTablet) return gap.tablet;
    return gap.desktop;
  };

  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: `repeat(${getGridColumns()}, 1fr)`,
    gap: `${getGridGap()}px`,
    width: '100%'
  };

  return (
    <div
      className={`responsive-grid ${className}`}
      style={gridStyle}
      {...props}
    >
      {children}
    </div>
  );
};

ResponsiveGrid.propTypes = {
  children: PropTypes.node.isRequired,
  columns: PropTypes.shape({
    mobile: PropTypes.number,
    tablet: PropTypes.number,
    desktop: PropTypes.number
  }),
  gap: PropTypes.shape({
    mobile: PropTypes.number,
    tablet: PropTypes.number,
    desktop: PropTypes.number
  }),
  className: PropTypes.string
};
