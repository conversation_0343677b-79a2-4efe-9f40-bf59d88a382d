import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Typography,
  Button,
  Input,
  List,
  Dropdown,
  Menu,
  Modal,
  Form,
  Select,
  Tag,
  Space,
  Tooltip,
  Empty,
  message
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ExportOutlined,
  CopyOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  EllipsisOutlined
} from '@ant-design/icons';
import { styled } from '../../design-system';
import theme from '../../design-system/theme';
import {
  createProject,
  updateProject,
  deleteProject,
  setActiveProject,
  loadProjects
} from '../../redux/actions';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const ProjectCard = styled(Card)`
  margin-bottom: ${theme.spacing[3]};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.sm};
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    box-shadow: ${theme.shadows.md};
    transform: translateY(-2px);
  }
  
  &.active {
    border-left: 4px solid ${theme.colors.primary.main};
  }
`;

const ProjectHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${theme.spacing[2]};
`;

const ProjectTitle = styled(Title)`
  margin: 0 !important;
`;

const ProjectDescription = styled(Paragraph)`
  margin-bottom: ${theme.spacing[2]} !important;
  color: ${theme.colors.neutral[600]};
`;

const ProjectMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: ${theme.colors.neutral[500]};
  font-size: ${theme.typography.fontSize.sm};
`;

const ProjectTags = styled.div`
  margin-top: ${theme.spacing[2]};
`;

/**
 * ProjectManager component
 * Manages user projects
 */
const ProjectManager = () => {
  const dispatch = useDispatch();
  const { projects, activeProject } = useSelector(state => state.projects);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState(null);
  const [form] = Form.useForm();

  // Load sample projects if none exist
  useEffect(() => {
    if (projects.length === 0) {
      const sampleProjects = [
        {
          id: '1',
          name: 'E-commerce Dashboard',
          description: 'Admin dashboard for an e-commerce platform',
          tags: ['dashboard', 'e-commerce'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          components: 12,
          layouts: 5
        },
        {
          id: '2',
          name: 'Blog Template',
          description: 'Responsive blog template with multiple layouts',
          tags: ['blog', 'responsive'],
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          updatedAt: new Date(Date.now() - 86400000).toISOString(),
          components: 8,
          layouts: 3
        },
        {
          id: '3',
          name: 'Portfolio Site',
          description: 'Personal portfolio website template',
          tags: ['portfolio', 'personal'],
          createdAt: new Date(Date.now() - 172800000).toISOString(),
          updatedAt: new Date(Date.now() - 172800000).toISOString(),
          components: 6,
          layouts: 2
        }
      ];
      
      dispatch(loadProjects(sampleProjects));
      dispatch(setActiveProject('1'));
    }
  }, [dispatch, projects.length]);

  // Handle project selection
  const handleSelectProject = (projectId) => {
    dispatch(setActiveProject(projectId));
  };

  // Show create/edit project modal
  const showModal = (project = null) => {
    setEditingProject(project);
    
    if (project) {
      form.setFieldsValue({
        name: project.name,
        description: project.description,
        tags: project.tags
      });
    } else {
      form.resetFields();
    }
    
    setIsModalVisible(true);
  };

  // Handle modal cancel
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingProject(null);
    form.resetFields();
  };

  // Handle form submission
  const handleSubmit = (values) => {
    if (editingProject) {
      // Update existing project
      const updatedProject = {
        ...editingProject,
        ...values,
        updatedAt: new Date().toISOString()
      };
      
      dispatch(updateProject(updatedProject));
      message.success('Project updated successfully');
    } else {
      // Create new project
      const newProject = {
        id: Date.now().toString(),
        ...values,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        components: 0,
        layouts: 0
      };
      
      dispatch(createProject(newProject));
      message.success('Project created successfully');
    }
    
    setIsModalVisible(false);
    setEditingProject(null);
    form.resetFields();
  };

  // Handle project deletion
  const handleDeleteProject = (projectId) => {
    Modal.confirm({
      title: 'Delete Project',
      content: 'Are you sure you want to delete this project? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        dispatch(deleteProject(projectId));
        message.success('Project deleted successfully');
      }
    });
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="project-manager">
      <ProjectHeader>
        <Title level={3}>My Projects</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => showModal()}
        >
          New Project
        </Button>
      </ProjectHeader>

      {projects.length === 0 ? (
        <Empty
          description="No projects found"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <List
          dataSource={projects}
          renderItem={project => (
            <ProjectCard
              className={activeProject === project.id ? 'active' : ''}
              onClick={() => handleSelectProject(project.id)}
            >
              <ProjectHeader>
                <ProjectTitle level={4}>{project.name}</ProjectTitle>
                <Dropdown
                  overlay={
                    <Menu>
                      <Menu.Item
                        key="edit"
                        icon={<EditOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          showModal(project);
                        }}
                      >
                        Edit
                      </Menu.Item>
                      <Menu.Item
                        key="duplicate"
                        icon={<CopyOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          message.info('Duplicate feature coming soon');
                        }}
                      >
                        Duplicate
                      </Menu.Item>
                      <Menu.Item
                        key="export"
                        icon={<ExportOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          message.info('Export feature coming soon');
                        }}
                      >
                        Export
                      </Menu.Item>
                      <Menu.Divider />
                      <Menu.Item
                        key="delete"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteProject(project.id);
                        }}
                      >
                        Delete
                      </Menu.Item>
                    </Menu>
                  }
                  trigger={['click']}
                >
                  <Button
                    type="text"
                    icon={<MoreOutlined />}
                    onClick={(e) => e.stopPropagation()}
                  />
                </Dropdown>
              </ProjectHeader>
              
              <ProjectDescription>{project.description}</ProjectDescription>
              
              <ProjectTags>
                {project.tags.map(tag => (
                  <Tag key={tag} color="blue" style={{ marginBottom: '8px' }}>
                    {tag}
                  </Tag>
                ))}
              </ProjectTags>
              
              <ProjectMeta>
                <div>
                  <Text type="secondary">
                    {project.components} components · {project.layouts} layouts
                  </Text>
                </div>
                <div>
                  <Text type="secondary">
                    Updated {formatDate(project.updatedAt)}
                  </Text>
                </div>
              </ProjectMeta>
            </ProjectCard>
          )}
        />
      )}

      <Modal
        title={editingProject ? 'Edit Project' : 'Create Project'}
        visible={isModalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Project Name"
            rules={[{ required: true, message: 'Please enter a project name' }]}
          >
            <Input placeholder="Enter project name" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter a description' }]}
          >
            <Input.TextArea
              placeholder="Enter project description"
              rows={3}
            />
          </Form.Item>
          
          <Form.Item
            name="tags"
            label="Tags"
          >
            <Select
              mode="tags"
              placeholder="Add tags"
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item>
            <Space style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingProject ? 'Update' : 'Create'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectManager;
