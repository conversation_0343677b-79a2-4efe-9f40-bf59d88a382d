import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  Form,
  Input,
  Select,
  Switch,
  Slider,
  InputNumber,
  Button,
  Collapse,
  Divider,
  Typography,
  Space,
  Tabs,
  ColorPicker,
  Radio,
  Tooltip
} from 'antd';
import {
  InfoCircleOutlined,
  CodeOutlined,
  SettingOutlined,
  EyeOutlined,
  LinkOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined
} from '@ant-design/icons';
import { styled } from '../../design-system';
import theme from '../../design-system/theme';
import { updateComponent } from '../../redux/actions';

const { Title, Text } = Typography;
const { Panel } = Collapse;
const { TabPane } = Tabs;
const { Option } = Select;

const PropertiesContainer = styled.div`
  padding: ${theme.spacing[3]};
`;

const PropertyGroup = styled.div`
  margin-bottom: ${theme.spacing[4]};
`;

const PropertyLabel = styled(Text)`
  display: block;
  margin-bottom: ${theme.spacing[1]};
  font-weight: ${theme.typography.fontWeight.medium};
`;

const PropertyDescription = styled(Text)`
  display: block;
  margin-bottom: ${theme.spacing[2]};
  font-size: ${theme.typography.fontSize.sm};
  color: ${theme.colors.neutral[500]};
`;

const CodeEditor = styled.div`
  font-family: ${theme.typography.fontFamily.code};
  font-size: ${theme.typography.fontSize.sm};
  padding: ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.sm};
  background-color: ${theme.colors.neutral[100]};
  border: 1px solid ${theme.colors.neutral[300]};
  margin-bottom: ${theme.spacing[3]};
`;

/**
 * ComponentProperties component
 * Provides a UI for editing component properties
 */
const ComponentProperties = ({ component, onUpdate }) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [localComponent, setLocalComponent] = useState(component);

  // Update form when component changes
  useEffect(() => {
    if (component) {
      setLocalComponent(component);
      form.setFieldsValue({
        ...component.props,
        name: component.name
      });
    }
  }, [component, form]);

  // Handle form changes
  const handleValuesChange = (changedValues, allValues) => {
    const updatedComponent = {
      ...localComponent,
      name: allValues.name,
      props: { ...allValues }
    };

    // Remove name from props
    delete updatedComponent.props.name;

    setLocalComponent(updatedComponent);

    // Call onUpdate callback if provided
    if (onUpdate) {
      onUpdate(updatedComponent);
    }
  };

  // Handle form submission
  const handleSubmit = (values) => {
    const updatedComponent = {
      ...localComponent,
      name: values.name,
      props: { ...values }
    };

    // Remove name from props
    delete updatedComponent.props.name;

    // Dispatch update action
    dispatch(updateComponent(updatedComponent));
  };

  // If no component is selected, show a message
  if (!component) {
    return (
      <PropertiesContainer>
        <Text type="secondary">Select a component to edit its properties</Text>
      </PropertiesContainer>
    );
  }

  // Get property fields based on component type
  const getPropertyFields = () => {
    switch (component.type) {
      case 'button':
        return (
          <>
            <Form.Item name="text" label="Button Text">
              <Input
                placeholder="Enter button text"
                data-tutorial-target="property-text-input"
              />
            </Form.Item>

            <Form.Item name="variant" label="Variant">
              <Select data-tutorial-target="property-type-select">
                <Option value="primary">Primary</Option>
                <Option value="secondary">Secondary</Option>
                <Option value="text">Text</Option>
                <Option value="link">Link</Option>
                <Option value="ghost">Ghost</Option>
                <Option value="dashed">Dashed</Option>
              </Select>
            </Form.Item>

            <Form.Item name="size" label="Size">
              <Select>
                <Option value="small">Small</Option>
                <Option value="medium">Medium</Option>
                <Option value="large">Large</Option>
              </Select>
            </Form.Item>

            <Form.Item name="disabled" label="Disabled" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Form.Item name="block" label="Full Width" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Form.Item name="onClick" label="onClick Handler">
              <Input placeholder="Enter function name" />
            </Form.Item>
          </>
        );

      case 'text':
        return (
          <>
            <Form.Item name="content" label="Text Content">
              <Input.TextArea rows={3} placeholder="Enter text content" />
            </Form.Item>

            <Form.Item name="variant" label="Variant">
              <Select>
                <Option value="h1">Heading 1</Option>
                <Option value="h2">Heading 2</Option>
                <Option value="h3">Heading 3</Option>
                <Option value="h4">Heading 4</Option>
                <Option value="h5">Heading 5</Option>
                <Option value="h6">Heading 6</Option>
                <Option value="p">Paragraph</Option>
                <Option value="span">Span</Option>
              </Select>
            </Form.Item>

            <Form.Item name="color" label="Text Color">
              <ColorPicker />
            </Form.Item>

            <Form.Item name="align" label="Text Alignment">
              <Radio.Group>
                <Radio.Button value="left">Left</Radio.Button>
                <Radio.Button value="center">Center</Radio.Button>
                <Radio.Button value="right">Right</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </>
        );

      case 'input':
        return (
          <>
            <Form.Item name="label" label="Input Label">
              <Input placeholder="Enter input label" />
            </Form.Item>

            <Form.Item name="placeholder" label="Placeholder">
              <Input placeholder="Enter placeholder text" />
            </Form.Item>

            <Form.Item name="type" label="Input Type">
              <Select>
                <Option value="text">Text</Option>
                <Option value="password">Password</Option>
                <Option value="email">Email</Option>
                <Option value="number">Number</Option>
                <Option value="tel">Telephone</Option>
                <Option value="url">URL</Option>
              </Select>
            </Form.Item>

            <Form.Item name="required" label="Required" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Form.Item name="disabled" label="Disabled" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Form.Item name="validation" label="Validation">
              <Select>
                <Option value="none">None</Option>
                <Option value="email">Email</Option>
                <Option value="url">URL</Option>
                <Option value="phone">Phone</Option>
                <Option value="custom">Custom</Option>
              </Select>
            </Form.Item>
          </>
        );

      case 'card':
        return (
          <>
            <Form.Item name="title" label="Card Title">
              <Input placeholder="Enter card title" />
            </Form.Item>

            <Form.Item name="description" label="Description">
              <Input.TextArea rows={3} placeholder="Enter card description" />
            </Form.Item>

            <Form.Item name="image" label="Image URL">
              <Input placeholder="Enter image URL" />
            </Form.Item>

            <Form.Item name="elevation" label="Elevation">
              <Select>
                <Option value="none">None</Option>
                <Option value="sm">Small</Option>
                <Option value="md">Medium</Option>
                <Option value="lg">Large</Option>
              </Select>
            </Form.Item>

            <Form.Item name="bordered" label="Bordered" valuePropName="checked">
              <Switch />
            </Form.Item>
          </>
        );

      default:
        return (
          <Text type="secondary">No specific properties for this component type</Text>
        );
    }
  };

  return (
    <PropertiesContainer>
      <Title level={4}>Component Properties</Title>
      <Text type="secondary">Edit the properties of the selected component</Text>

      <Divider />

      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
        onFinish={handleSubmit}
        initialValues={{
          ...component.props,
          name: component.name
        }}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Basic" key="basic">
            <Form.Item name="name" label="Component Name" required>
              <Input placeholder="Enter component name" />
            </Form.Item>

            {getPropertyFields()}
          </TabPane>

          <TabPane tab="Style" key="style">
            <Collapse defaultActiveKey={['dimensions']}>
              <Panel header="Dimensions" key="dimensions">
                <Form.Item name="width" label="Width">
                  <Input placeholder="e.g., 100%, 200px" />
                </Form.Item>

                <Form.Item name="height" label="Height">
                  <Input placeholder="e.g., 100%, 200px" />
                </Form.Item>

                <Form.Item name="minWidth" label="Min Width">
                  <Input placeholder="e.g., 100px" />
                </Form.Item>

                <Form.Item name="maxWidth" label="Max Width">
                  <Input placeholder="e.g., 500px" />
                </Form.Item>
              </Panel>

              <Panel header="Spacing" key="spacing">
                <Form.Item name="margin" label="Margin">
                  <Input placeholder="e.g., 10px 20px" />
                </Form.Item>

                <Form.Item name="padding" label="Padding">
                  <Input placeholder="e.g., 10px 20px" />
                </Form.Item>
              </Panel>

              <Panel header="Typography" key="typography">
                <Form.Item name="fontSize" label="Font Size">
                  <Input placeholder="e.g., 16px, 1.2rem" />
                </Form.Item>

                <Form.Item name="fontWeight" label="Font Weight">
                  <Select>
                    <Option value="normal">Normal</Option>
                    <Option value="bold">Bold</Option>
                    <Option value="lighter">Lighter</Option>
                    <Option value="bolder">Bolder</Option>
                    <Option value="100">100</Option>
                    <Option value="200">200</Option>
                    <Option value="300">300</Option>
                    <Option value="400">400</Option>
                    <Option value="500">500</Option>
                    <Option value="600">600</Option>
                    <Option value="700">700</Option>
                    <Option value="800">800</Option>
                    <Option value="900">900</Option>
                  </Select>
                </Form.Item>

                <Form.Item name="lineHeight" label="Line Height">
                  <Input placeholder="e.g., 1.5, 24px" />
                </Form.Item>
              </Panel>
            </Collapse>
          </TabPane>

          <TabPane tab="Advanced" key="advanced">
            <Form.Item name="customProps" label="Custom Properties">
              <Input.TextArea
                rows={5}
                placeholder="Enter custom properties as JSON"
              />
            </Form.Item>

            <Form.Item name="customStyles" label="Custom Styles">
              <Input.TextArea
                rows={5}
                placeholder="Enter custom styles as CSS"
              />
            </Form.Item>
          </TabPane>
        </Tabs>

        <Divider />

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit">
              Apply Changes
            </Button>
            <Button onClick={() => form.resetFields()}>
              Reset
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </PropertiesContainer>
  );
};

export default ComponentProperties;
