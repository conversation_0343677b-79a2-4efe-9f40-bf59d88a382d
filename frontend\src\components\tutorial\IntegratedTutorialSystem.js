import React, { useState, useEffect, useRef } from 'react';
import { 
  <PERSON>, 
  But<PERSON>, 
  Steps, 
  Tooltip, 
  Modal, 
  Space, 
  Typography, 
  Progress,
  Drawer,
  Badge,
  Switch
} from 'antd';
import {
  QuestionCircleOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseOutlined,
  BulbOutlined,
  RocketOutlined,
  SettingOutlined,
  EyeOutlined,
  BookOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

const TutorialOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: ${props => props.active ? 'all' : 'none'};
  opacity: ${props => props.active ? 1 : 0};
  transition: all 0.3s ease;
`;

const TutorialSpotlight = styled.div`
  position: absolute;
  border: 3px solid #1890ff;
  border-radius: 8px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 10000;
`;

const TutorialCard = styled(Card)`
  position: fixed;
  max-width: 400px;
  z-index: 10001;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  
  .ant-card-head {
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    
    .ant-card-head-title {
      color: white;
    }
  }
`;

const HelpButton = styled(Button)`
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContextualHint = styled.div`
  position: absolute;
  background: #1890ff;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: #1890ff;
  }
`;

const IntegratedTutorialSystem = ({ children, onTutorialComplete }) => {
  const [tutorialActive, setTutorialActive] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [tutorialVisible, setTutorialVisible] = useState(false);
  const [helpDrawerVisible, setHelpDrawerVisible] = useState(false);
  const [tutorialSettings, setTutorialSettings] = useState({
    autoStart: false,
    showHints: true,
    skipCompleted: true
  });
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [spotlightPosition, setSpotlightPosition] = useState({ top: 0, left: 0, width: 0, height: 0 });
  
  const tutorialSteps = [
    {
      id: 'welcome',
      title: 'Welcome to App Builder',
      content: 'Let\'s take a quick tour of the main features. This tutorial will help you get started with building your first application.',
      target: null,
      action: 'Click "Next" to continue',
      position: 'center'
    },
    {
      id: 'component-palette',
      title: 'Component Palette',
      content: 'This is where you\'ll find all available components. Drag components from here to the canvas to start building your app.',
      target: '[data-tutorial="component-palette"]',
      action: 'Try dragging a component to the canvas',
      position: 'right'
    },
    {
      id: 'canvas-area',
      title: 'Design Canvas',
      content: 'This is your design canvas where you can drop components and arrange them. Components can be resized and repositioned here.',
      target: '[data-tutorial="canvas-area"]',
      action: 'Drop a component here to see it in action',
      position: 'left'
    },
    {
      id: 'property-editor',
      title: 'Property Editor',
      content: 'When you select a component, its properties appear here. You can customize colors, text, sizes, and behavior.',
      target: '[data-tutorial="property-editor"]',
      action: 'Select a component to see its properties',
      position: 'left'
    },
    {
      id: 'layout-tools',
      title: 'Layout Tools',
      content: 'Use these tools to align components, distribute spacing, and manage the layout of your design.',
      target: '[data-tutorial="layout-tools"]',
      action: 'Try the alignment tools with multiple components',
      position: 'bottom'
    },
    {
      id: 'theme-manager',
      title: 'Theme Manager',
      content: 'Create and apply themes to maintain consistent styling across your application. Themes control colors, fonts, and spacing.',
      target: '[data-tutorial="theme-manager"]',
      action: 'Create a custom theme or apply an existing one',
      position: 'top'
    },
    {
      id: 'preview-mode',
      title: 'Preview Mode',
      content: 'Switch to preview mode to see how your application will look and behave for end users.',
      target: '[data-tutorial="preview-mode"]',
      action: 'Toggle preview mode to test your app',
      position: 'bottom'
    },
    {
      id: 'completion',
      title: 'Tutorial Complete!',
      content: 'Great job! You\'ve learned the basics of App Builder. You\'re now ready to create amazing applications.',
      target: null,
      action: 'Start building your app!',
      position: 'center'
    }
  ];

  const currentTutorialStep = tutorialSteps[currentStep];

  useEffect(() => {
    if (tutorialActive && currentTutorialStep?.target) {
      const targetElement = document.querySelector(currentTutorialStep.target);
      if (targetElement) {
        const rect = targetElement.getBoundingClientRect();
        setSpotlightPosition({
          top: rect.top - 8,
          left: rect.left - 8,
          width: rect.width + 16,
          height: rect.height + 16
        });
      }
    }
  }, [tutorialActive, currentStep, currentTutorialStep]);

  const startTutorial = () => {
    setTutorialActive(true);
    setTutorialVisible(true);
    setCurrentStep(0);
  };

  const nextStep = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, tutorialSteps[currentStep].id]));
      setCurrentStep(currentStep + 1);
    } else {
      completeTutorial();
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipTutorial = () => {
    setTutorialActive(false);
    setTutorialVisible(false);
    setCurrentStep(0);
  };

  const completeTutorial = () => {
    setTutorialActive(false);
    setTutorialVisible(false);
    setCompletedSteps(prev => new Set([...prev, ...tutorialSteps.map(step => step.id)]));
    if (onTutorialComplete) {
      onTutorialComplete();
    }
  };

  const getTutorialCardPosition = () => {
    if (!currentTutorialStep?.position || currentTutorialStep.position === 'center') {
      return {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
      };
    }

    const { position } = currentTutorialStep;
    const { top, left, width, height } = spotlightPosition;

    switch (position) {
      case 'right':
        return {
          top: top + height / 2,
          left: left + width + 20,
          transform: 'translateY(-50%)'
        };
      case 'left':
        return {
          top: top + height / 2,
          right: window.innerWidth - left + 20,
          transform: 'translateY(-50%)'
        };
      case 'bottom':
        return {
          top: top + height + 20,
          left: left + width / 2,
          transform: 'translateX(-50%)'
        };
      case 'top':
        return {
          bottom: window.innerHeight - top + 20,
          left: left + width / 2,
          transform: 'translateX(-50%)'
        };
      default:
        return {
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        };
    }
  };

  const ContextualHelp = ({ target, hint, visible }) => {
    const [position, setPosition] = useState({ top: 0, left: 0 });

    useEffect(() => {
      if (visible && target) {
        const element = document.querySelector(target);
        if (element) {
          const rect = element.getBoundingClientRect();
          setPosition({
            top: rect.top - 40,
            left: rect.left + rect.width / 2
          });
        }
      }
    }, [visible, target]);

    if (!visible) return null;

    return (
      <ContextualHint
        style={{
          top: position.top,
          left: position.left,
          transform: 'translateX(-50%)'
        }}
      >
        {hint}
      </ContextualHint>
    );
  };

  return (
    <>
      {children}

      {/* Tutorial Overlay */}
      <TutorialOverlay active={tutorialActive}>
        {currentTutorialStep?.target && (
          <TutorialSpotlight
            style={{
              top: spotlightPosition.top,
              left: spotlightPosition.left,
              width: spotlightPosition.width,
              height: spotlightPosition.height
            }}
          />
        )}
      </TutorialOverlay>

      {/* Tutorial Card */}
      {tutorialVisible && (
        <TutorialCard
          title={
            <Space>
              <BookOutlined />
              {currentTutorialStep?.title}
              <Badge count={`${currentStep + 1}/${tutorialSteps.length}`} />
            </Space>
          }
          style={getTutorialCardPosition()}
          extra={
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={skipTutorial}
              style={{ color: 'white' }}
            />
          }
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Progress 
              percent={((currentStep + 1) / tutorialSteps.length) * 100} 
              size="small"
              showInfo={false}
            />
            
            <Paragraph style={{ margin: 0 }}>
              {currentTutorialStep?.content}
            </Paragraph>

            {currentTutorialStep?.action && (
              <div style={{ 
                background: '#f0f2f5', 
                padding: '8px 12px', 
                borderRadius: '6px',
                fontSize: '12px',
                color: '#666'
              }}>
                <BulbOutlined style={{ marginRight: 4 }} />
                {currentTutorialStep.action}
              </div>
            )}

            <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 16 }}>
              <Button 
                onClick={previousStep} 
                disabled={currentStep === 0}
                size="small"
              >
                Previous
              </Button>
              
              <Space>
                <Button onClick={skipTutorial} size="small">
                  Skip Tutorial
                </Button>
                <Button 
                  type="primary" 
                  onClick={nextStep}
                  icon={currentStep === tutorialSteps.length - 1 ? <CheckCircleOutlined /> : <PlayCircleOutlined />}
                  size="small"
                >
                  {currentStep === tutorialSteps.length - 1 ? 'Complete' : 'Next'}
                </Button>
              </Space>
            </div>
          </Space>
        </TutorialCard>
      )}

      {/* Help Button */}
      <Badge count={tutorialSettings.showHints ? '?' : 0} size="small">
        <HelpButton
          type="primary"
          icon={<QuestionCircleOutlined />}
          onClick={() => setHelpDrawerVisible(true)}
          title="Get Help"
        />
      </Badge>

      {/* Help Drawer */}
      <Drawer
        title={
          <Space>
            <BookOutlined />
            Help & Tutorials
          </Space>
        }
        placement="right"
        onClose={() => setHelpDrawerVisible(false)}
        visible={helpDrawerVisible}
        width={400}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Card size="small">
            <Title level={4}>
              <RocketOutlined style={{ color: '#1890ff', marginRight: 8 }} />
              Quick Start
            </Title>
            <Button 
              type="primary" 
              block 
              icon={<PlayCircleOutlined />}
              onClick={() => {
                setHelpDrawerVisible(false);
                startTutorial();
              }}
            >
              Start Interactive Tutorial
            </Button>
          </Card>

          <Card size="small">
            <Title level={4}>
              <SettingOutlined style={{ color: '#722ed1', marginRight: 8 }} />
              Tutorial Settings
            </Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>Auto-start for new users</Text>
                <Switch 
                  checked={tutorialSettings.autoStart}
                  onChange={(checked) => setTutorialSettings(prev => ({ ...prev, autoStart: checked }))}
                  size="small"
                />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>Show contextual hints</Text>
                <Switch 
                  checked={tutorialSettings.showHints}
                  onChange={(checked) => setTutorialSettings(prev => ({ ...prev, showHints: checked }))}
                  size="small"
                />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>Skip completed steps</Text>
                <Switch 
                  checked={tutorialSettings.skipCompleted}
                  onChange={(checked) => setTutorialSettings(prev => ({ ...prev, skipCompleted: checked }))}
                  size="small"
                />
              </div>
            </Space>
          </Card>

          <Card size="small">
            <Title level={4}>
              <EyeOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              Tutorial Progress
            </Title>
            <Steps direction="vertical" size="small" current={-1}>
              {tutorialSteps.map((step, index) => (
                <Step
                  key={step.id}
                  title={step.title}
                  status={completedSteps.has(step.id) ? 'finish' : 'wait'}
                  icon={completedSteps.has(step.id) ? <CheckCircleOutlined /> : undefined}
                />
              ))}
            </Steps>
          </Card>
        </Space>
      </Drawer>

      {/* Contextual Hints */}
      {tutorialSettings.showHints && (
        <>
          <ContextualHelp
            target="[data-tutorial='component-palette']"
            hint="Drag components from here"
            visible={!tutorialActive && !completedSteps.has('component-palette')}
          />
          <ContextualHelp
            target="[data-tutorial='property-editor']"
            hint="Customize component properties"
            visible={!tutorialActive && !completedSteps.has('property-editor')}
          />
        </>
      )}
    </>
  );
};

export default IntegratedTutorialSystem;
