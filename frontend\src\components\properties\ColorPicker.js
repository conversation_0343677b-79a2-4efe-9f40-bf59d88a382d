import React, { useState, useCallback } from 'react';
import { ColorPicker as AntColorPicker, Space, Button, Input, Typography, Popover, Card } from 'antd';
import { BgColorsOutlined, EyeDropperOutlined, PaletteOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Text } = Typography;

/**
 * Advanced Color Picker
 * 
 * Enhanced color picker with presets, gradients, and advanced color manipulation.
 */

const ColorContainer = styled.div`
  .ant-color-picker-trigger {
    width: 100%;
    height: 32px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      border-color: #40a9ff;
    }
  }
`;

const PresetColors = styled.div`
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  margin: 8px 0;
`;

const PresetColor = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid ${props => props.selected ? '#1890ff' : 'transparent'};
  transition: all 0.2s;
  
  &:hover {
    transform: scale(1.1);
    border-color: #40a9ff;
  }
`;

const GradientPreview = styled.div`
  width: 100%;
  height: 32px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  background: ${props => props.gradient};
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    border-color: #40a9ff;
  }
`;

const ColorPicker = ({
  value = '#1890ff',
  onChange,
  showPresets = true,
  showGradients = false,
  showAlpha = true,
  presets = [],
  disabled = false,
  size = 'default'
}) => {
  const [currentValue, setCurrentValue] = useState(value);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [gradientMode, setGradientMode] = useState(false);

  // Default color presets
  const defaultPresets = [
    '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d',
    '#bae637', '#73d13d', '#40a9ff', '#1890ff',
    '#722ed1', '#eb2f96', '#f5222d', '#fa541c',
    '#fa8c16', '#fadb14', '#a0d911', '#52c41a',
    '#13c2c2', '#1890ff', '#2f54eb', '#722ed1',
    '#eb2f96', '#000000', '#434343', '#666666',
    '#999999', '#cccccc', '#eeeeee', '#ffffff'
  ];

  const colorPresets = presets.length > 0 ? presets : defaultPresets;

  // Gradient presets
  const gradientPresets = [
    'linear-gradient(45deg, #ff6b6b, #feca57)',
    'linear-gradient(45deg, #48cae4, #023e8a)',
    'linear-gradient(45deg, #f093fb, #f5576c)',
    'linear-gradient(45deg, #4facfe, #00f2fe)',
    'linear-gradient(45deg, #43e97b, #38f9d7)',
    'linear-gradient(45deg, #fa709a, #fee140)',
    'linear-gradient(45deg, #a8edea, #fed6e3)',
    'linear-gradient(45deg, #ff9a9e, #fecfef)'
  ];

  const handleColorChange = useCallback((color, hex) => {
    const newValue = hex || color;
    setCurrentValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  }, [onChange]);

  const handlePresetClick = useCallback((color) => {
    handleColorChange(color);
  }, [handleColorChange]);

  const handleGradientClick = useCallback((gradient) => {
    handleColorChange(gradient);
  }, [handleColorChange]);

  const parseColorValue = (colorValue) => {
    if (typeof colorValue === 'string') {
      if (colorValue.startsWith('linear-gradient') || colorValue.startsWith('radial-gradient')) {
        return { type: 'gradient', value: colorValue };
      }
      return { type: 'color', value: colorValue };
    }
    return { type: 'color', value: '#1890ff' };
  };

  const colorInfo = parseColorValue(currentValue);

  const renderAdvancedPanel = () => (
    <Card size="small" style={{ width: 280 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Color Value</Text>
          <Input
            value={currentValue}
            onChange={(e) => handleColorChange(e.target.value)}
            placeholder="Enter color value"
            style={{ marginTop: 4 }}
          />
        </div>

        {showPresets && (
          <div>
            <Text strong>Color Presets</Text>
            <PresetColors>
              {colorPresets.map((color, index) => (
                <PresetColor
                  key={index}
                  style={{ backgroundColor: color }}
                  selected={currentValue === color}
                  onClick={() => handlePresetClick(color)}
                  title={color}
                />
              ))}
            </PresetColors>
          </div>
        )}

        {showGradients && (
          <div>
            <Text strong>Gradient Presets</Text>
            <Space direction="vertical" style={{ width: '100%', marginTop: 8 }}>
              {gradientPresets.map((gradient, index) => (
                <GradientPreview
                  key={index}
                  gradient={gradient}
                  onClick={() => handleGradientClick(gradient)}
                  title={gradient}
                />
              ))}
            </Space>
          </div>
        )}

        <div>
          <Space>
            <Button
              size="small"
              icon={<BgColorsOutlined />}
              onClick={() => setGradientMode(!gradientMode)}
            >
              {gradientMode ? 'Solid Color' : 'Gradient'}
            </Button>
            
            <Button
              size="small"
              icon={<EyeDropperOutlined />}
              onClick={() => {
                // Eye dropper functionality would go here
                console.log('Eye dropper not implemented');
              }}
            >
              Pick
            </Button>
          </Space>
        </div>
      </Space>
    </Card>
  );

  if (colorInfo.type === 'gradient') {
    return (
      <ColorContainer>
        <Popover
          content={renderAdvancedPanel()}
          trigger="click"
          placement="bottomLeft"
          open={showAdvanced}
          onOpenChange={setShowAdvanced}
        >
          <GradientPreview
            gradient={currentValue}
            style={{ 
              height: size === 'small' ? 24 : size === 'large' ? 40 : 32 
            }}
          />
        </Popover>
      </ColorContainer>
    );
  }

  return (
    <ColorContainer>
      <Space direction="vertical" style={{ width: '100%' }}>
        <AntColorPicker
          value={currentValue}
          onChange={handleColorChange}
          showText
          disabled={disabled}
          size={size}
          presets={showPresets ? [
            {
              label: 'Recommended',
              colors: colorPresets.slice(0, 10)
            },
            {
              label: 'Recent',
              colors: colorPresets.slice(10, 20)
            }
          ] : []}
          panelRender={(panel) => (
            <div style={{ width: 280 }}>
              {panel}
              
              {showAdvanced && (
                <div style={{ marginTop: 12, paddingTop: 12, borderTop: '1px solid #f0f0f0' }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>Hex Value</Text>
                      <Input
                        value={currentValue}
                        onChange={(e) => handleColorChange(e.target.value)}
                        placeholder="#1890ff"
                        style={{ marginTop: 4 }}
                      />
                    </div>

                    {showGradients && (
                      <div>
                        <Text strong>Gradients</Text>
                        <Space direction="vertical" style={{ width: '100%', marginTop: 8 }}>
                          {gradientPresets.slice(0, 4).map((gradient, index) => (
                            <GradientPreview
                              key={index}
                              gradient={gradient}
                              onClick={() => handleGradientClick(gradient)}
                              style={{ height: 24 }}
                            />
                          ))}
                        </Space>
                      </div>
                    )}
                  </Space>
                </div>
              )}
            </div>
          )}
        />

        <Space>
          <Button
            size="small"
            type="text"
            icon={<PaletteOutlined />}
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            {showAdvanced ? 'Simple' : 'Advanced'}
          </Button>
          
          {showGradients && (
            <Button
              size="small"
              type="text"
              onClick={() => setGradientMode(!gradientMode)}
            >
              Gradient
            </Button>
          )}
        </Space>
      </Space>
    </ColorContainer>
  );
};

export default ColorPicker;
