# Enhanced Code Export Functionality

## Overview

The Enhanced Code Export functionality provides comprehensive code generation capabilities for the App Builder application. It supports multiple frameworks, languages, and export formats with advanced configuration options, quality assurance, and modern development practices.

## Features

### 🚀 Supported Export Formats

#### Frontend Frameworks
- **React** - Modern React with hooks, TypeScript support
- **React + TypeScript** - React with full TypeScript integration
- **Vue.js** - Vue 3 with Composition API and TypeScript
- **Vue + TypeScript** - Vue 3 with full TypeScript support
- **Angular** - Angular with TypeScript and modern features
- **Svelte** - Svelte with modern JavaScript/TypeScript
- **Next.js** - Next.js with SSR and modern React features
- **Nuxt.js** - Nuxt.js for Vue applications

#### Mobile Frameworks
- **React Native** - Cross-platform mobile development with TypeScript support
- **Flutter** - Google's UI toolkit for mobile, web, and desktop (Dart)
- **Ionic** - Hybrid mobile app development

#### Static Formats
- **HTML/CSS/JavaScript** - Vanilla web technologies with modern standards
- **Bootstrap Templates** - Bootstrap-based responsive templates

#### Backend APIs
- **Express.js** - Node.js web application framework with TypeScript support
- **Django REST Framework** - Python web framework with modern patterns
- **FastAPI** - Modern Python API framework with automatic documentation

### 🎨 Style Framework Support

- **Styled Components** - CSS-in-JS with styled-components (React/React Native)
- **Emotion** - CSS-in-JS with Emotion (React)
- **Tailwind CSS** - Utility-first CSS framework (All frameworks)
- **CSS Modules** - Scoped CSS with modules (React/Vue/Angular)
- **Material-UI** - React Material Design components
- **Chakra UI** - Simple and modular React components
- **Bootstrap** - Popular CSS framework (HTML/React/Vue/Angular)

### ⚙️ Advanced Configuration Options

#### Code Quality
- **TypeScript Support** - Generate TypeScript code with type definitions and interfaces
- **Accessibility Features** - Include ARIA labels, roles, semantic HTML, and accessibility attributes
- **ESLint Configuration** - Code linting and style enforcement with framework-specific rules
- **Prettier Integration** - Code formatting with consistent style

#### Testing & Documentation
- **Unit Tests** - Generate test files for components (Jest, React Testing Library, Vue Test Utils)
- **Storybook Stories** - Component documentation and testing (React/Vue/Angular)
- **README Generation** - Comprehensive project documentation with setup instructions
- **API Documentation** - Automatic API documentation generation (OpenAPI/Swagger)

#### Project Structure Options
- **Single File** - All code in one file for simple projects and prototypes
- **Multi-File** - Organized file structure with separate components and modules
- **Full Project** - Complete project with build configs, tooling, and deployment setup

#### Development Tools
- **Build Configuration** - Webpack 5, Vite, or Parcel 2 configuration with optimization
- **Package Management** - npm, yarn, or pnpm support with lock files
- **Environment Configuration** - Development and production settings with environment variables
- **Docker Support** - Containerization with Dockerfile and docker-compose for all frameworks
- **CI/CD Pipelines** - GitHub Actions, GitLab CI, and Azure DevOps templates

## Usage

### Frontend Integration

```javascript
import EnhancedCodeExporter from './components/enhanced/EnhancedCodeExporter';

// Use in your App Builder interface
<EnhancedCodeExporter
  appData={appData}
  onExportComplete={handleExportComplete}
  onExportError={handleExportError}
/>
```

### API Endpoints

#### Enhanced Export
```http
POST /api/enhanced-export/
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "app_id": 1,
  "format": "react-ts",
  "options": {
    "typescript": true,
    "include_accessibility": true,
    "project_structure": "full-project",
    "style_framework": "styled-components",
    "state_management": "redux",
    "include_tests": true,
    "include_storybook": true,
    "include_docker": true,
    "include_ci_cd": true,
    "bundler": "vite",
    "package_manager": "pnpm"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "type": "project",
  "files": {
    "src/App.tsx": "...",
    "src/components/Button.tsx": "...",
    "package.json": "...",
    "README.md": "..."
  },
  "metadata": {
    "format": "react-ts",
    "generated_at": "2024-01-01T12:00:00Z",
    "app_name": "My App"
  }
}
```

#### Template Export
```http
POST /api/export-template/
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "template_id": 1,
  "template_type": "layout",
  "format": "vue-ts",
  "options": {
    "typescript": true,
    "project_structure": "full-project",
    "style_framework": "tailwind"
  }
}
```

#### Batch Export
```http
POST /api/batch-export/
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "app_ids": [1, 2, 3],
  "format": "react",
  "options": {
    "project_structure": "multi-file",
    "include_tests": false
  }
}
```

#### Get Available Export Formats
```http
GET /api/export-formats/
Authorization: Bearer <your-token>
```

**Response:**
```json
{
  "formats": [
    {
      "value": "react",
      "label": "React",
      "description": "Modern React with hooks",
      "icon": "⚛️",
      "supports_typescript": true,
      "supports_tests": true,
      "supports_storybook": true,
      "style_frameworks": ["styled-components", "emotion", "tailwind", "css-modules"]
    }
  ],
  "style_frameworks": [...],
  "state_management": [...],
  "bundlers": [...],
  "package_managers": [...]
}
```

### Backend Integration

```python
from core.enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat, StyleFramework
from core.code_validator import CodeValidator, ValidationLevel
from my_app.services.export_template_service import ExportTemplateService

# Generate code with full options
generator = EnhancedCodeGenerator()
options = ExportOptions(
    format=ExportFormat.REACT_TS,
    typescript=True,
    include_accessibility=True,
    include_tests=True,
    include_storybook=True,
    style_framework=StyleFramework.STYLED_COMPONENTS,
    state_management="redux",
    project_structure="full-project",
    bundler="vite",
    package_manager="pnpm",
    include_docker=True,
    include_ci_cd=True
)

# Generate code
generated_code = generator.generate_code(app_data, options)

# Validate generated code
validator = CodeValidator()
validation_results = validator.validate_code(
    code=generated_code if isinstance(generated_code, str) else generated_code.get('src/App.tsx', ''),
    language='typescript',
    framework='react'
)

# Check validation results
for result in validation_results:
    if result.level == ValidationLevel.ERROR:
        print(f"Error: {result.message}")
    elif result.level == ValidationLevel.WARNING:
        print(f"Warning: {result.message}")

# Export with templates
export_service = ExportTemplateService()
template_export = export_service.export_app_with_templates(
    app_id=1,
    export_format='react-ts',
    options={
        'typescript': True,
        'project_structure': 'full-project',
        'include_tests': True
    },
    user=request.user
)
```

## Configuration Options

### Export Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `format` | string | 'react' | Target export format (see supported formats) |
| `typescript` | boolean | false | Enable TypeScript support with type definitions |
| `include_accessibility` | boolean | true | Include ARIA labels, roles, and semantic HTML |
| `include_tests` | boolean | false | Generate test files (Jest, React Testing Library, etc.) |
| `include_storybook` | boolean | false | Generate Storybook stories for components |
| `style_framework` | string | 'styled-components' | CSS framework to use (see options below) |
| `state_management` | string | 'useState' | State management solution (see options below) |
| `project_structure` | string | 'single-file' | Project organization (single-file, multi-file, full-project) |
| `bundler` | string | 'vite' | Build tool configuration (vite, webpack, parcel) |
| `package_manager` | string | 'npm' | Package manager (npm, yarn, pnpm) |
| `include_docker` | boolean | false | Include Docker configuration and docker-compose |
| `include_ci_cd` | boolean | false | Include CI/CD pipeline templates |

### Supported Export Formats

| Format | Value | TypeScript | Tests | Storybook | Mobile | Backend |
|--------|-------|------------|-------|-----------|--------|---------|
| React | `react` | ✅ | ✅ | ✅ | ❌ | ❌ |
| React + TypeScript | `react-ts` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Vue.js | `vue` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Vue + TypeScript | `vue-ts` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Angular | `angular` | ✅ | ✅ | ❌ | ❌ | ❌ |
| Svelte | `svelte` | ✅ | ✅ | ❌ | ❌ | ❌ |
| Next.js | `next` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Nuxt.js | `nuxt` | ✅ | ✅ | ❌ | ❌ | ❌ |
| HTML/CSS/JS | `html` | ❌ | ❌ | ❌ | ❌ | ❌ |
| React Native | `react-native` | ✅ | ✅ | ✅ | ✅ | ❌ |
| Flutter | `flutter` | ❌ | ✅ | ❌ | ✅ | ❌ |
| Ionic | `ionic` | ✅ | ✅ | ❌ | ✅ | ❌ |
| Express.js API | `express-api` | ✅ | ✅ | ❌ | ❌ | ✅ |
| Django API | `django-api` | ❌ | ✅ | ❌ | ❌ | ✅ |
| FastAPI | `fastapi` | ❌ | ✅ | ❌ | ❌ | ✅ |

### Style Framework Options

| Framework | Value | React | Vue | Angular | HTML | React Native |
|-----------|-------|-------|-----|---------|------|--------------|
| Styled Components | `styled-components` | ✅ | ❌ | ❌ | ❌ | ✅ |
| Emotion | `emotion` | ✅ | ❌ | ❌ | ❌ | ❌ |
| Tailwind CSS | `tailwind` | ✅ | ✅ | ✅ | ✅ | ❌ |
| CSS Modules | `css-modules` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Material-UI | `material-ui` | ✅ | ❌ | ❌ | ❌ | ❌ |
| Chakra UI | `chakra-ui` | ✅ | ❌ | ❌ | ❌ | ❌ |
| Bootstrap | `bootstrap` | ✅ | ✅ | ✅ | ✅ | ❌ |

### State Management Options

| Solution | Value | React | Vue | Angular | Description |
|----------|-------|-------|-----|---------|-------------|
| React Hooks | `useState` | ✅ | ❌ | ❌ | Built-in React state management |
| Redux Toolkit | `redux` | ✅ | ❌ | ❌ | Predictable state container |
| Zustand | `zustand` | ✅ | ❌ | ❌ | Lightweight state management |
| React Context | `context` | ✅ | ❌ | ❌ | Built-in context API |
| Vuex | `vuex` | ❌ | ✅ | ❌ | Vue state management |
| Pinia | `pinia` | ❌ | ✅ | ❌ | Modern Vue state management |
| NgRx | `ngrx` | ❌ | ❌ | ✅ | Angular state management |

## Framework-Specific Code Examples

### React with TypeScript
```typescript
// Generated App.tsx
import React, { useState } from 'react';
import styled from 'styled-components';

interface ButtonProps {
  text: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

const StyledButton = styled.button<{ variant: string }>`
  background-color: ${props => props.variant === 'primary' ? '#007bff' : '#6c757d'};
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
`;

const Button: React.FC<ButtonProps> = ({ text, onClick, variant = 'primary' }) => (
  <StyledButton variant={variant} onClick={onClick} aria-label={text}>
    {text}
  </StyledButton>
);

const App: React.FC = () => {
  const [count, setCount] = useState(0);

  return (
    <div className="app" role="main">
      <h1>My App</h1>
      <p>Count: {count}</p>
      <Button
        text="Increment"
        onClick={() => setCount(count + 1)}
      />
    </div>
  );
};

export default App;
```

### Angular with TypeScript

#### Component (app.component.ts)
```typescript
import { Component, OnInit } from '@angular/core';

interface ButtonConfig {
  text: string;
  variant: 'primary' | 'secondary';
  disabled?: boolean;
}

interface AppData {
  title: string;
  count: number;
  buttons: ButtonConfig[];
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  appData: AppData = {
    title: 'My Angular App',
    count: 0,
    buttons: [
      { text: 'Increment', variant: 'primary' },
      { text: 'Reset', variant: 'secondary' }
    ]
  };

  ngOnInit(): void {
    // Component initialization logic
    console.log('App component initialized');
  }

  increment(): void {
    this.appData.count++;
  }

  reset(): void {
    this.appData.count = 0;
  }

  onButtonClick(action: string): void {
    switch (action) {
      case 'increment':
        this.increment();
        break;
      case 'reset':
        this.reset();
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  getButtonClass(variant: string): string {
    return `btn btn-${variant}`;
  }
}
```

#### Template (app.component.html)
```html
<div class="app-container" role="main" [attr.aria-label]="appData.title">
  <header class="app-header">
    <h1>{{ appData.title }}</h1>
    <p class="count-display"
       [attr.aria-label]="'Current count is ' + appData.count">
      Count: {{ appData.count }}
    </p>
  </header>

  <main class="app-content">
    <section class="button-group" role="group" aria-label="Counter controls">
      <button
        *ngFor="let button of appData.buttons; trackBy: trackByText"
        [class]="getButtonClass(button.variant)"
        [disabled]="button.disabled"
        [attr.aria-label]="button.text + ' button'"
        (click)="onButtonClick(button.text.toLowerCase())"
        type="button">
        {{ button.text }}
      </button>
    </section>

    <section class="info-section" aria-label="Application information">
      <p class="description">
        This is a sample Angular application generated by App Builder.
        It demonstrates modern Angular patterns with TypeScript.
      </p>
    </section>
  </main>
</div>
```

#### Styles (app.component.css)
```css
.app-container {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  padding: 2rem;
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
}

.app-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.count-display {
  font-size: 1.5rem;
  font-weight: 600;
  color: #3498db;
  margin: 0;
}

.app-content {
  max-width: 600px;
  margin: 0 auto;
}

.button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
}

.info-section {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border-left: 4px solid #3498db;
}

.description {
  margin: 0;
  color: #6c757d;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-container {
    padding: 1rem;
  }

  .button-group {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 200px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }

  .btn:hover:not(:disabled) {
    transform: none;
  }
}
```

### Vue 3 with TypeScript
```vue
<!-- Generated App.vue -->
<template>
  <div class="app" role="main">
    <h1>{{ title }}</h1>
    <p>Count: {{ count }}</p>
    <button
      @click="increment"
      :class="['btn', `btn-${variant}`]"
      :aria-label="`Increment count, current value is ${count}`"
    >
      Increment
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  title?: string;
  variant?: 'primary' | 'secondary';
}

const props = withDefaults(defineProps<Props>(), {
  title: 'My App',
  variant: 'primary'
});

const count = ref(0);

const increment = () => {
  count.value++;
};
</script>

<style scoped>
.app {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

.btn:hover {
  opacity: 0.8;
}

.btn-secondary {
  background-color: #6c757d;
}
</style>
```

### React Native
```typescript
// Generated App.tsx
import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary';
}

const Button: React.FC<ButtonProps> = ({ title, onPress, variant = 'primary' }) => (
  <TouchableOpacity
    style={[styles.button, variant === 'secondary' && styles.buttonSecondary]}
    onPress={onPress}
    accessibilityLabel={title}
    accessibilityRole="button"
  >
    <Text style={styles.buttonText}>{title}</Text>
  </TouchableOpacity>
);

const App: React.FC = () => {
  const [count, setCount] = useState(0);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <View style={styles.content}>
          <Text style={styles.title}>My App</Text>
          <Text style={styles.count}>Count: {count}</Text>
          <Button
            title="Increment"
            onPress={() => setCount(count + 1)}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  count: {
    fontSize: 18,
    marginBottom: 16,
  },
  button: {
    backgroundColor: '#007bff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  buttonSecondary: {
    backgroundColor: '#6c757d',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default App;
```

### Svelte with TypeScript

```svelte
<!-- App.svelte -->
<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { writable, derived } from 'svelte/store';

  // TypeScript interfaces
  interface ButtonConfig {
    id: string;
    text: string;
    variant: 'primary' | 'secondary';
    action: string;
  }

  interface AppState {
    title: string;
    count: number;
    isLoading: boolean;
    lastAction: string | null;
  }

  // Props with default values
  export let initialCount: number = 0;
  export let title: string = 'My Svelte App';
  export let theme: 'light' | 'dark' = 'light';

  // Reactive stores
  const appState = writable<AppState>({
    title,
    count: initialCount,
    isLoading: false,
    lastAction: null
  });

  // Derived stores for computed values
  const isEven = derived(appState, $appState => $appState.count % 2 === 0);
  const countStatus = derived(appState, $appState => {
    if ($appState.count === 0) return 'zero';
    if ($appState.count > 0) return 'positive';
    return 'negative';
  });

  // Button configuration
  const buttons: ButtonConfig[] = [
    { id: 'increment', text: 'Increment', variant: 'primary', action: 'increment' },
    { id: 'decrement', text: 'Decrement', variant: 'secondary', action: 'decrement' },
    { id: 'reset', text: 'Reset', variant: 'secondary', action: 'reset' }
  ];

  // Event dispatcher for parent communication
  const dispatch = createEventDispatcher<{
    countChanged: { count: number; action: string };
    buttonClicked: { buttonId: string };
  }>();

  // Reactive statements
  $: if ($appState.count !== initialCount) {
    dispatch('countChanged', {
      count: $appState.count,
      action: $appState.lastAction || 'unknown'
    });
  }

  // Action handlers
  function handleButtonClick(button: ButtonConfig) {
    appState.update(state => ({ ...state, isLoading: true }));

    // Simulate async operation
    setTimeout(() => {
      appState.update(state => {
        let newCount = state.count;

        switch (button.action) {
          case 'increment':
            newCount = state.count + 1;
            break;
          case 'decrement':
            newCount = state.count - 1;
            break;
          case 'reset':
            newCount = 0;
            break;
        }

        return {
          ...state,
          count: newCount,
          lastAction: button.action,
          isLoading: false
        };
      });

      dispatch('buttonClicked', { buttonId: button.id });
    }, 200);
  }

  // Lifecycle
  onMount(() => {
    console.log('Svelte app mounted');
    return () => {
      console.log('Svelte app destroyed');
    };
  });

  // Accessibility helpers
  function getAriaLabel(button: ButtonConfig): string {
    return `${button.text} button, current count is ${$appState.count}`;
  }
</script>

<main
  class="app"
  class:dark-theme={theme === 'dark'}
  role="main"
  aria-label={$appState.title}
>
  <header class="app-header">
    <h1>{$appState.title}</h1>
    <div class="count-display" aria-live="polite">
      <span class="count-value" class:even={$isEven} class:odd={!$isEven}>
        Count: {$appState.count}
      </span>
      <span class="count-status" aria-label="Count status: {$countStatus}">
        ({$countStatus})
      </span>
    </div>
  </header>

  <section class="controls" role="group" aria-label="Counter controls">
    {#each buttons as button (button.id)}
      <button
        class="btn btn-{button.variant}"
        class:loading={$appState.isLoading}
        disabled={$appState.isLoading}
        aria-label={getAriaLabel(button)}
        on:click={() => handleButtonClick(button)}
      >
        {#if $appState.isLoading}
          <span class="spinner" aria-hidden="true"></span>
        {/if}
        {button.text}
      </button>
    {/each}
  </section>

  <section class="info" aria-label="Application information">
    <p class="description">
      This Svelte application demonstrates reactive programming with TypeScript.
      The count is currently <strong>{$countStatus}</strong> and
      <strong>{$isEven ? 'even' : 'odd'}</strong>.
    </p>

    {#if $appState.lastAction}
      <p class="last-action" aria-live="polite">
        Last action: <em>{$appState.lastAction}</em>
      </p>
    {/if}
  </section>
</main>

<style>
  /* Component styles are scoped by default in Svelte */
  .app {
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
  }

  .dark-theme {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
  }

  .app-header {
    text-align: center;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .app-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .count-display {
    font-size: 1.5rem;
    font-weight: 600;
  }

  .count-value {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
  }

  .count-value.even {
    background-color: rgba(52, 152, 219, 0.3);
    border: 2px solid #3498db;
  }

  .count-value.odd {
    background-color: rgba(231, 76, 60, 0.3);
    border: 2px solid #e74c3c;
  }

  .count-status {
    font-size: 1rem;
    opacity: 0.8;
    font-style: italic;
  }

  .controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }

  .btn:active:not(:disabled) {
    transform: translateY(0);
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .btn-primary {
    background: rgba(52, 152, 219, 0.8);
    color: white;
  }

  .btn-secondary {
    background: rgba(149, 165, 166, 0.8);
    color: white;
  }

  .btn.loading {
    pointer-events: none;
  }

  .spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .info {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 600px;
    margin: 0 auto;
  }

  .description {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
  }

  .last-action {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .app {
      padding: 1rem;
    }

    .controls {
      flex-direction: column;
      align-items: center;
    }

    .btn {
      width: 100%;
      max-width: 200px;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .app,
    .btn,
    .count-value {
      transition: none;
    }

    .btn:hover:not(:disabled) {
      transform: none;
    }

    .spinner {
      animation: none;
    }
  }

  @media (prefers-contrast: high) {
    .btn {
      border: 2px solid currentColor;
    }

    .app-header,
    .info {
      border: 2px solid currentColor;
    }
  }
</style>
```

### Next.js with SSR and CSS Modules

#### Page Component (pages/index.tsx)
```typescript
import { GetServerSideProps, NextPage } from 'next';
import Head from 'next/head';
import { useState, useEffect } from 'react';
import styles from '../styles/Home.module.css';

// TypeScript interfaces
interface ButtonConfig {
  id: string;
  text: string;
  variant: 'primary' | 'secondary';
  action: string;
}

interface HomePageProps {
  initialCount: number;
  serverTime: string;
  userAgent: string;
}

interface AppState {
  count: number;
  isClient: boolean;
  lastUpdated: Date | null;
}

const HomePage: NextPage<HomePageProps> = ({
  initialCount,
  serverTime,
  userAgent
}) => {
  // State management
  const [appState, setAppState] = useState<AppState>({
    count: initialCount,
    isClient: false,
    lastUpdated: null
  });

  // Button configuration
  const buttons: ButtonConfig[] = [
    { id: 'increment', text: 'Increment', variant: 'primary', action: 'increment' },
    { id: 'decrement', text: 'Decrement', variant: 'secondary', action: 'decrement' },
    { id: 'reset', text: 'Reset', variant: 'secondary', action: 'reset' }
  ];

  // Client-side hydration
  useEffect(() => {
    setAppState(prev => ({ ...prev, isClient: true }));
  }, []);

  // Action handlers
  const handleButtonClick = (button: ButtonConfig) => {
    setAppState(prev => {
      let newCount = prev.count;

      switch (button.action) {
        case 'increment':
          newCount = prev.count + 1;
          break;
        case 'decrement':
          newCount = prev.count - 1;
          break;
        case 'reset':
          newCount = 0;
          break;
      }

      return {
        ...prev,
        count: newCount,
        lastUpdated: new Date()
      };
    });
  };

  // Computed values
  const isEven = appState.count % 2 === 0;
  const countStatus = appState.count === 0 ? 'zero' :
                     appState.count > 0 ? 'positive' : 'negative';

  return (
    <>
      <Head>
        <title>Next.js App Builder Demo</title>
        <meta name="description" content="A Next.js application generated by App Builder" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />

        {/* Open Graph meta tags */}
        <meta property="og:title" content="Next.js App Builder Demo" />
        <meta property="og:description" content="A Next.js application generated by App Builder" />
        <meta property="og:type" content="website" />

        {/* Accessibility meta tags */}
        <meta name="theme-color" content="#3498db" />
      </Head>

      <div className={styles.container} role="main">
        <header className={styles.header}>
          <h1 className={styles.title}>
            Next.js App Builder Demo
          </h1>

          <div className={styles.countDisplay} aria-live="polite">
            <span
              className={`${styles.countValue} ${isEven ? styles.even : styles.odd}`}
              aria-label={`Current count is ${appState.count}`}
            >
              Count: {appState.count}
            </span>
            <span className={styles.countStatus}>
              ({countStatus})
            </span>
          </div>
        </header>

        <main className={styles.main}>
          <section
            className={styles.controls}
            role="group"
            aria-label="Counter controls"
          >
            {buttons.map((button) => (
              <button
                key={button.id}
                className={`${styles.btn} ${styles[`btn${button.variant.charAt(0).toUpperCase() + button.variant.slice(1)}`]}`}
                onClick={() => handleButtonClick(button)}
                aria-label={`${button.text} button, current count is ${appState.count}`}
                type="button"
              >
                {button.text}
              </button>
            ))}
          </section>

          <section className={styles.info} aria-label="Application information">
            <div className={styles.infoCard}>
              <h2>Server-Side Rendered Data</h2>
              <p><strong>Server Time:</strong> {serverTime}</p>
              <p><strong>Initial Count:</strong> {initialCount}</p>
              <p><strong>User Agent:</strong> {userAgent.substring(0, 50)}...</p>
            </div>

            <div className={styles.infoCard}>
              <h2>Client-Side State</h2>
              <p><strong>Hydrated:</strong> {appState.isClient ? 'Yes' : 'No'}</p>
              <p><strong>Current Count:</strong> {appState.count}</p>
              <p><strong>Last Updated:</strong> {
                appState.lastUpdated
                  ? appState.lastUpdated.toLocaleTimeString()
                  : 'Never'
              }</p>
            </div>
          </section>
        </main>

        <footer className={styles.footer}>
          <p>
            Generated by{' '}
            <a
              href="https://app-builder.example.com"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.link}
            >
              App Builder
            </a>
            {' '}with Next.js
          </p>
        </footer>
      </div>
    </>
  );
};

// Server-side rendering
export const getServerSideProps: GetServerSideProps<HomePageProps> = async (context) => {
  // Simulate server-side data fetching
  const serverTime = new Date().toISOString();
  const userAgent = context.req.headers['user-agent'] || 'Unknown';
  const initialCount = Math.floor(Math.random() * 10); // Random initial count

  return {
    props: {
      initialCount,
      serverTime,
      userAgent
    }
  };
};

export default HomePage;
```

#### CSS Modules (styles/Home.module.css)
```css
.container {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.countDisplay {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
}

.countValue {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.countValue.even {
  background-color: rgba(52, 152, 219, 0.3);
  border-color: #3498db;
}

.countValue.odd {
  background-color: rgba(231, 76, 60, 0.3);
  border-color: #e74c3c;
}

.countStatus {
  font-size: 1rem;
  opacity: 0.8;
  font-style: italic;
}

.main {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  min-width: 120px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
}

.btnPrimary {
  background: rgba(52, 152, 219, 0.8);
}

.btnPrimary:hover {
  background: rgba(52, 152, 219, 1);
}

.btnSecondary {
  background: rgba(149, 165, 166, 0.8);
}

.btnSecondary:hover {
  background: rgba(149, 165, 166, 1);
}

.info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.infoCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.infoCard h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.infoCard p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.1);
  color: white;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.link {
  color: #3498db;
  text-decoration: none;
  font-weight: 600;
}

.link:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }

  .main {
    padding: 1rem;
  }

  .controls {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 200px;
  }

  .info {
    grid-template-columns: 1fr;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .countValue {
    transition: none;
  }

  .btn:hover {
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .btn,
  .infoCard {
    border: 2px solid currentColor;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
}
```

### Flutter with Dart

#### Main Application (lib/main.dart)
```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter App Builder Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        // Accessibility improvements
        textTheme: const TextTheme(
          headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
          bodyLarge: TextStyle(fontSize: 16),
        ),
      ),
      home: const MyHomePage(title: 'Flutter App Builder Demo'),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage>
    with TickerProviderStateMixin {
  // State variables
  int _counter = 0;
  bool _isLoading = false;
  String? _lastAction;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  // Button configuration
  final List<ButtonConfig> _buttons = [
    ButtonConfig(
      id: 'increment',
      text: 'Increment',
      icon: Icons.add,
      color: Colors.blue,
      action: ButtonAction.increment,
    ),
    ButtonConfig(
      id: 'decrement',
      text: 'Decrement',
      icon: Icons.remove,
      color: Colors.orange,
      action: ButtonAction.decrement,
    ),
    ButtonConfig(
      id: 'reset',
      text: 'Reset',
      icon: Icons.refresh,
      color: Colors.red,
      action: ButtonAction.reset,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Action handlers
  Future<void> _handleButtonPress(ButtonConfig button) async {
    // Haptic feedback for better UX
    HapticFeedback.lightImpact();

    // Trigger animation
    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    setState(() {
      _isLoading = true;
    });

    // Simulate async operation
    await Future.delayed(const Duration(milliseconds: 300));

    setState(() {
      switch (button.action) {
        case ButtonAction.increment:
          _counter++;
          break;
        case ButtonAction.decrement:
          _counter--;
          break;
        case ButtonAction.reset:
          _counter = 0;
          break;
      }
      _lastAction = button.text;
      _isLoading = false;
    });

    // Announce change for screen readers
    _announceCountChange();
  }

  void _announceCountChange() {
    final String announcement = 'Count is now $_counter';
    SemanticsService.announce(announcement, TextDirection.ltr);
  }

  // Computed properties
  bool get _isEven => _counter % 2 == 0;
  String get _countStatus {
    if (_counter == 0) return 'zero';
    return _counter > 0 ? 'positive' : 'negative';
  }

  Color get _countColor {
    if (_counter == 0) return Colors.grey;
    return _counter > 0 ? Colors.green : Colors.red;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.title,
          semanticsLabel: 'App title: ${widget.title}',
        ),
        backgroundColor: Theme.of(context).primaryColor,
        elevation: 4,
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                // Header section
                _buildHeader(),
                const SizedBox(height: 32),

                // Counter display
                _buildCounterDisplay(),
                const SizedBox(height: 32),

                // Control buttons
                _buildControlButtons(),
                const SizedBox(height: 32),

                // Information section
                _buildInfoSection(),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _handleButtonPress(_buttons[0]),
        tooltip: 'Quick increment',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: Colors.white.withOpacity(0.9),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Text(
              'Flutter Counter App',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: Colors.black87,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Built with App Builder',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.black54,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCounterDisplay() {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Card(
            elevation: 12,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            color: Colors.white.withOpacity(0.95),
            child: Container(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                children: [
                  Semantics(
                    label: 'Current count value',
                    value: _counter.toString(),
                    child: Text(
                      '$_counter',
                      style: TextStyle(
                        fontSize: 72,
                        fontWeight: FontWeight.bold,
                        color: _countColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Chip(
                        label: Text(_isEven ? 'Even' : 'Odd'),
                        backgroundColor: _isEven ? Colors.blue.shade100 : Colors.orange.shade100,
                      ),
                      const SizedBox(width: 8),
                      Chip(
                        label: Text(_countStatus.toUpperCase()),
                        backgroundColor: _countColor.withOpacity(0.1),
                      ),
                    ],
                  ),
                  if (_lastAction != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Last action: $_lastAction',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButtons() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: Colors.white.withOpacity(0.9),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Controls',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              alignment: WrapAlignment.center,
              children: _buttons.map((button) => _buildActionButton(button)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(ButtonConfig button) {
    return SizedBox(
      width: 120,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : () => _handleButtonPress(button),
        icon: _isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Icon(button.icon),
        label: Text(button.text),
        style: ElevatedButton.styleFrom(
          backgroundColor: button.color,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: Colors.white.withOpacity(0.9),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'App Information',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Framework', 'Flutter'),
            _buildInfoRow('Language', 'Dart'),
            _buildInfoRow('Platform', Theme.of(context).platform.name),
            _buildInfoRow('Current Count', _counter.toString()),
            _buildInfoRow('Count Type', _isEven ? 'Even' : 'Odd'),
            if (_lastAction != null)
              _buildInfoRow('Last Action', _lastAction!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.black54,
            ),
          ),
        ],
      ),
    );
  }
}

// Data classes
class ButtonConfig {
  final String id;
  final String text;
  final IconData icon;
  final Color color;
  final ButtonAction action;

  const ButtonConfig({
    required this.id,
    required this.text,
    required this.icon,
    required this.color,
    required this.action,
  });
}

enum ButtonAction {
  increment,
  decrement,
  reset,
}
```

#### Package Configuration (pubspec.yaml)
```yaml
name: flutter_app_builder_demo
description: A Flutter application generated by App Builder
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  # Assets (if needed)
  # assets:
  #   - images/

  # Fonts (if needed)
  # fonts:
  #   - family: CustomFont
  #     fonts:
  #       - asset: fonts/CustomFont-Regular.ttf
```

### Express.js API with TypeScript

#### Main Server (src/server.ts)
```typescript
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { body, param, query, validationResult } from 'express-validator';
import { v4 as uuidv4 } from 'uuid';

// TypeScript interfaces
interface CounterItem {
  id: string;
  name: string;
  count: number;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

interface CounterUpdateRequest {
  action: 'increment' | 'decrement' | 'reset';
  amount?: number;
}

// In-memory storage (in production, use a database)
const counters: Map<string, CounterItem> = new Map();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later.',
    timestamp: new Date().toISOString(),
  },
});

app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined'));

// Custom middleware for API responses
const apiResponse = <T>(
  res: Response,
  success: boolean,
  data?: T,
  message?: string,
  statusCode: number = 200
): Response => {
  const response: ApiResponse<T> = {
    success,
    timestamp: new Date().toISOString(),
  };

  if (data !== undefined) response.data = data;
  if (message) {
    if (success) response.message = message;
    else response.error = message;
  }

  return res.status(statusCode).json(response);
};

// Error handling middleware
const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return apiResponse(res, false, undefined, 'Validation failed', 400);
  }
  next();
};

// Async error handler
const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Routes

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  apiResponse(res, true, {
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  }, 'Service is healthy');
});

// Get all counters
app.get('/api/counters',
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 }),
  handleValidationErrors,
  asyncHandler(async (req: Request, res: Response) => {
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = parseInt(req.query.offset as string) || 0;

    const allCounters = Array.from(counters.values());
    const paginatedCounters = allCounters.slice(offset, offset + limit);

    apiResponse(res, true, {
      counters: paginatedCounters,
      total: allCounters.length,
      limit,
      offset,
    }, 'Counters retrieved successfully');
  })
);

// Get specific counter
app.get('/api/counters/:id',
  param('id').isUUID(),
  handleValidationErrors,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const counter = counters.get(id);

    if (!counter) {
      return apiResponse(res, false, undefined, 'Counter not found', 404);
    }

    apiResponse(res, true, counter, 'Counter retrieved successfully');
  })
);

// Create new counter
app.post('/api/counters',
  body('name').isString().isLength({ min: 1, max: 100 }).trim(),
  body('initialCount').optional().isInt(),
  body('metadata').optional().isObject(),
  handleValidationErrors,
  asyncHandler(async (req: Request, res: Response) => {
    const { name, initialCount = 0, metadata } = req.body;

    const counter: CounterItem = {
      id: uuidv4(),
      name,
      count: initialCount,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata,
    };

    counters.set(counter.id, counter);

    apiResponse(res, true, counter, 'Counter created successfully', 201);
  })
);

// Update counter
app.patch('/api/counters/:id',
  param('id').isUUID(),
  body('action').isIn(['increment', 'decrement', 'reset']),
  body('amount').optional().isInt({ min: 1 }),
  handleValidationErrors,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { action, amount = 1 }: CounterUpdateRequest = req.body;

    const counter = counters.get(id);
    if (!counter) {
      return apiResponse(res, false, undefined, 'Counter not found', 404);
    }

    const previousCount = counter.count;

    switch (action) {
      case 'increment':
        counter.count += amount;
        break;
      case 'decrement':
        counter.count -= amount;
        break;
      case 'reset':
        counter.count = 0;
        break;
    }

    counter.updatedAt = new Date();
    counters.set(id, counter);

    apiResponse(res, true, {
      counter,
      previousCount,
      change: counter.count - previousCount,
    }, `Counter ${action}ed successfully`);
  })
);

// Delete counter
app.delete('/api/counters/:id',
  param('id').isUUID(),
  handleValidationErrors,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    if (!counters.has(id)) {
      return apiResponse(res, false, undefined, 'Counter not found', 404);
    }

    counters.delete(id);
    apiResponse(res, true, undefined, 'Counter deleted successfully');
  })
);

// Bulk operations
app.post('/api/counters/bulk',
  body('action').isIn(['increment', 'decrement', 'reset']),
  body('counterIds').isArray(),
  body('counterIds.*').isUUID(),
  body('amount').optional().isInt({ min: 1 }),
  handleValidationErrors,
  asyncHandler(async (req: Request, res: Response) => {
    const { action, counterIds, amount = 1 } = req.body;

    const results: Array<{ id: string; success: boolean; error?: string }> = [];

    for (const id of counterIds) {
      const counter = counters.get(id);
      if (!counter) {
        results.push({ id, success: false, error: 'Counter not found' });
        continue;
      }

      switch (action) {
        case 'increment':
          counter.count += amount;
          break;
        case 'decrement':
          counter.count -= amount;
          break;
        case 'reset':
          counter.count = 0;
          break;
      }

      counter.updatedAt = new Date();
      counters.set(id, counter);
      results.push({ id, success: true });
    }

    apiResponse(res, true, { results }, 'Bulk operation completed');
  })
);

// Statistics endpoint
app.get('/api/stats',
  asyncHandler(async (req: Request, res: Response) => {
    const allCounters = Array.from(counters.values());
    const totalCount = allCounters.reduce((sum, counter) => sum + counter.count, 0);
    const averageCount = allCounters.length > 0 ? totalCount / allCounters.length : 0;

    const stats = {
      totalCounters: allCounters.length,
      totalCount,
      averageCount: Math.round(averageCount * 100) / 100,
      maxCount: allCounters.length > 0 ? Math.max(...allCounters.map(c => c.count)) : 0,
      minCount: allCounters.length > 0 ? Math.min(...allCounters.map(c => c.count)) : 0,
    };

    apiResponse(res, true, stats, 'Statistics retrieved successfully');
  })
);

// 404 handler
app.use('*', (req: Request, res: Response) => {
  apiResponse(res, false, undefined, 'Endpoint not found', 404);
});

// Global error handler
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Global error handler:', error);

  if (res.headersSent) {
    return next(error);
  }

  apiResponse(res, false, undefined, 'Internal server error', 500);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Express server running on port ${PORT}`);
  console.log(`📚 API documentation available at http://localhost:${PORT}/health`);
});

export default app;
```

#### Package Configuration (package.json)
```json
{
  "name": "express-app-builder-api",
  "version": "1.0.0",
  "description": "Express.js API generated by App Builder",
  "main": "dist/server.js",
  "scripts": {
    "start": "node dist/server.js",
    "dev": "ts-node-dev --respawn --transpile-only src/server.ts",
    "build": "tsc",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "morgan": "^1.10.0",
    "express-rate-limit": "^7.1.5",
    "express-validator": "^7.0.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "@types/express": "^4.17.21",
    "@types/cors": "^2.8.17",
    "@types/morgan": "^1.9.9",
    "@types/uuid": "^9.0.7",
    "@types/node": "^20.10.4",
    "@types/jest": "^29.5.8",
    "typescript": "^5.3.3",
    "ts-node-dev": "^2.0.0",
    "jest": "^29.7.0",
    "ts-jest": "^29.1.1",
    "eslint": "^8.55.0",
    "@typescript-eslint/eslint-plugin": "^6.13.1",
    "@typescript-eslint/parser": "^6.13.1"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
```

## Quality Assurance

### Code Validation

The export system includes comprehensive code validation:

- **Syntax Validation** - Ensures generated code has valid syntax for target language/framework
- **Framework Compliance** - Validates framework-specific patterns and best practices
- **Accessibility Checks** - Ensures WCAG 2.1 AA compliance with proper ARIA attributes
- **Security Validation** - Checks for common security issues (XSS, injection vulnerabilities)
- **Performance Optimization** - Validates performance best practices (bundle size, lazy loading)
- **Type Safety** - TypeScript type checking and interface validation
- **Code Style** - ESLint and Prettier compliance for consistent formatting

### Testing

Comprehensive test suite includes:

- **Unit Tests** - Test individual components and functions with Jest/Vitest
- **Integration Tests** - Test complete export pipeline with real app data
- **Validation Tests** - Test code quality assurance and validation rules
- **Performance Tests** - Test export performance with large applications (1000+ components)
- **Cross-Framework Tests** - Ensure consistency across different export formats
- **Accessibility Tests** - Automated accessibility testing with axe-core

## Integration with Drag-and-Drop Interface

### Component Mapping

The enhanced export system seamlessly integrates with App Builder's drag-and-drop interface:

```javascript
// Component mapping from drag-and-drop to export
const componentMapping = {
  'Button': {
    react: 'button',
    vue: 'button',
    angular: 'button',
    'react-native': 'TouchableOpacity',
    flutter: 'ElevatedButton'
  },
  'Text': {
    react: 'p',
    vue: 'p',
    angular: 'p',
    'react-native': 'Text',
    flutter: 'Text'
  },
  'Input': {
    react: 'input',
    vue: 'input',
    angular: 'input',
    'react-native': 'TextInput',
    flutter: 'TextField'
  }
};
```

### Template System Integration

Export functionality works with both Layout Templates and App Templates:

```javascript
// Export app with templates
const exportWithTemplates = async (appId, format, options) => {
  const response = await fetch('/api/export-template/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      app_id: appId,
      format: format,
      options: {
        ...options,
        include_templates: true,
        template_optimization: true
      }
    })
  });

  return response.json();
};
```

### Real-time Preview Integration

Export preview updates in real-time as users modify components:

```javascript
// Real-time export preview
const useExportPreview = (appData, format, options) => {
  const [preview, setPreview] = useState('');

  useEffect(() => {
    const generatePreview = async () => {
      try {
        const result = await exportService.clientSideExport(appData, format, options);
        setPreview(result.code);
      } catch (error) {
        console.error('Preview generation failed:', error);
      }
    };

    generatePreview();
  }, [appData, format, options]);

  return preview;
};
```

## Best Practices

### Code Generation

1. **Use TypeScript** for better type safety, IntelliSense, and developer experience
2. **Enable Accessibility** to ensure WCAG 2.1 AA compliance and inclusive applications
3. **Include Tests** for better code quality, maintainability, and confidence in deployments
4. **Use Modern Frameworks** for better performance, security, and developer ecosystem
5. **Follow Naming Conventions** for consistent code organization and team collaboration
6. **Implement Error Boundaries** for robust error handling in production applications
7. **Use Semantic HTML** for better SEO and screen reader compatibility

### Project Structure

1. **Single File** - For simple prototypes, demos, and proof-of-concepts
2. **Multi-File** - For organized development projects with component separation
3. **Full Project** - For production-ready applications with complete tooling setup

### Performance Optimization

1. **Optimize Bundle Size** - Use tree shaking, code splitting, and dynamic imports
2. **Enable Caching** - Configure proper caching strategies for static assets
3. **Use Modern Build Tools** - Vite for development speed, Webpack 5 for production optimization
4. **Minimize Dependencies** - Only include necessary packages and use bundle analyzers
5. **Implement Lazy Loading** - Load components and routes on demand
6. **Optimize Images** - Use modern formats (WebP, AVIF) and responsive images
7. **Enable Compression** - Use Gzip/Brotli compression for smaller file sizes

### Security Best Practices

1. **Sanitize User Input** - Prevent XSS attacks with proper input validation
2. **Use HTTPS** - Ensure secure communication in production
3. **Implement CSP** - Content Security Policy headers to prevent injection attacks
4. **Regular Updates** - Keep dependencies updated to patch security vulnerabilities
5. **Environment Variables** - Store sensitive configuration in environment variables
6. **Authentication** - Implement proper authentication and authorization

## Troubleshooting

### Common Issues

#### Export Fails
**Symptoms:** Export request returns error or times out
- **Check app data structure** - Ensure components, layouts, and styles are properly formatted
- **Verify export format compatibility** - Confirm the format supports your selected options
- **Review validation errors** - Check the response for specific validation failures
- **Check authentication** - Ensure valid Bearer token is provided
- **Verify app permissions** - Confirm user has access to the app being exported

#### Generated Code Issues
**Symptoms:** Code doesn't compile or run correctly
- **Run code validation** - Use the built-in validator to check for syntax errors
- **Check framework-specific requirements** - Ensure all required dependencies are included
- **Verify accessibility compliance** - Check ARIA attributes and semantic HTML
- **Review TypeScript types** - Ensure type definitions are correct and complete
- **Check import statements** - Verify all imports are valid and available

#### Performance Issues
**Symptoms:** Export takes too long or fails with timeout
- **Reduce component complexity** - Simplify complex nested structures
- **Use pagination for large apps** - Export in smaller chunks for apps with 100+ components
- **Optimize export options** - Disable unnecessary features like tests or Storybook
- **Use batch export** - For multiple apps, use the batch endpoint
- **Check server resources** - Ensure adequate memory and CPU on the server

#### Template Integration Issues
**Symptoms:** Templates not applied correctly in export
- **Verify template compatibility** - Ensure template supports the target framework
- **Check template data structure** - Confirm template JSON is valid
- **Review template permissions** - Ensure user has access to the template
- **Validate template version** - Use the latest template version

### Error Messages

| Error Code | Error Message | Cause | Solution |
|------------|---------------|-------|----------|
| `INVALID_FORMAT` | "Invalid export format" | Unsupported format specified | Use supported format from `/api/export-formats/` |
| `TEMPLATE_NOT_FOUND` | "Template not found" | Missing or inaccessible template | Check template ID and permissions |
| `VALIDATION_FAILED` | "Code validation failed" | Generated code has quality issues | Review validation results and fix issues |
| `EXPORT_TIMEOUT` | "Export operation timed out" | Large application or server overload | Reduce complexity or use batch export |
| `INSUFFICIENT_PERMISSIONS` | "Access denied" | User lacks permissions | Check app ownership or sharing settings |
| `INVALID_APP_DATA` | "Invalid app data structure" | Malformed app data | Validate app data structure |
| `DEPENDENCY_ERROR` | "Missing dependencies" | Required packages not available | Check package.json and install dependencies |
| `COMPILATION_ERROR` | "Code compilation failed" | Syntax or type errors in generated code | Review generated code and fix syntax issues |

### Debug Mode

Enable debug mode for detailed error information:

```javascript
// Frontend debug mode
const exportOptions = {
  ...options,
  debug: true,
  verbose_logging: true
};

// Backend debug logging
import logging
logging.getLogger('core.enhanced_code_generator').setLevel(logging.DEBUG)
```

### Performance Monitoring

Monitor export performance:

```javascript
// Track export performance
const startTime = performance.now();
const result = await exportService.enhancedExport(appData, format, options);
const endTime = performance.now();
console.log(`Export took ${endTime - startTime} milliseconds`);

// Server-side monitoring
from django.utils import timezone
start_time = timezone.now()
# ... export logic ...
end_time = timezone.now()
logger.info(f"Export completed in {(end_time - start_time).total_seconds()}s")
```

## API Reference

### EnhancedCodeGenerator

```python
class EnhancedCodeGenerator:
    """Enhanced code generator with support for multiple frameworks and modern practices"""

    def __init__(self):
        """Initialize the code generator with template cache"""
        self.template_cache = {}

    def generate_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str], bytes]:
        """
        Generate code based on app data and export options

        Args:
            app_data: Application data structure containing components, layouts, styles, and data
            options: Export configuration options

        Returns:
            Generated code as string (single-file), dict of files (multi-file), or zip bytes (full-project)

        Raises:
            ValueError: If app_data structure is invalid
            NotImplementedError: If export format is not supported
        """

    def _generate_react_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate React code with modern best practices"""

    def _generate_vue_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate Vue.js code with Composition API"""

    def _generate_angular_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate Angular code with TypeScript"""

    def _generate_react_native_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate React Native code for mobile development"""

    def _generate_flutter_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate Flutter code in Dart"""
```

### CodeValidator

```python
class CodeValidator:
    """Comprehensive code validator for multiple programming languages and frameworks"""

    def __init__(self):
        """Initialize validator with framework-specific rules"""
        self.validation_rules = {
            'react': self._get_react_rules(),
            'vue': self._get_vue_rules(),
            'angular': self._get_angular_rules(),
            # ... other frameworks
        }

    def validate_code(self, code: str, language: str, framework: str = None) -> List[ValidationResult]:
        """
        Validate code for syntax, style, and best practices

        Args:
            code: The code to validate
            language: Programming language (js, ts, py, dart, html, css)
            framework: Framework (react, vue, angular, etc.)

        Returns:
            List of validation results with level, message, and rule information
        """

    def validate_project_structure(self, files: Dict[str, str], framework: str) -> List[ValidationResult]:
        """
        Validate complete project structure

        Args:
            files: Dictionary of file paths to file contents
            framework: Target framework for validation

        Returns:
            List of validation results for the entire project
        """

    def validate_accessibility(self, code: str, framework: str) -> List[ValidationResult]:
        """Validate accessibility compliance (WCAG 2.1 AA)"""

    def validate_security(self, code: str, language: str) -> List[ValidationResult]:
        """Validate security best practices and common vulnerabilities"""

    def validate_performance(self, code: str, framework: str) -> List[ValidationResult]:
        """Validate performance best practices"""
```

### ExportTemplateService

```python
class ExportTemplateService:
    """Service for exporting apps and templates with enhanced functionality"""

    def __init__(self):
        """Initialize service with code generator and validator"""
        self.code_generator = EnhancedCodeGenerator()
        self.validator = CodeValidator()

    def export_app_with_templates(self, app_id: int, export_format: str, options: Dict, user=None) -> Dict:
        """
        Export app with template integration

        Args:
            app_id: ID of the app to export
            export_format: Target export format
            options: Export configuration options
            user: User performing the export (for permissions)

        Returns:
            Dictionary containing generated code, metadata, and export information
        """

    def export_template_as_project(self, template_id: int, template_type: str, export_format: str, options: Dict, user=None) -> Dict:
        """
        Export template as standalone project

        Args:
            template_id: ID of the template to export
            template_type: Type of template (layout, app, component)
            export_format: Target export format
            options: Export configuration options
            user: User performing the export

        Returns:
            Dictionary containing project files and metadata
        """

    def batch_export_apps(self, app_ids: List[int], export_format: str, options: Dict, user=None) -> bytes:
        """
        Export multiple apps as a single zip file

        Args:
            app_ids: List of app IDs to export
            export_format: Target export format for all apps
            options: Export configuration options
            user: User performing the export

        Returns:
            Zip file bytes containing all exported apps
        """
```

### ExportOptions

```python
@dataclass
class ExportOptions:
    """Configuration options for code export"""
    format: ExportFormat
    typescript: bool = False
    include_accessibility: bool = True
    include_tests: bool = False
    include_storybook: bool = False
    style_framework: StyleFramework = StyleFramework.STYLED_COMPONENTS
    state_management: str = "useState"
    project_structure: str = "single-file"
    bundler: str = "vite"
    package_manager: str = "npm"
    include_docker: bool = False
    include_ci_cd: bool = False
    target_directory: Optional[str] = None
```

### ValidationResult

```python
@dataclass
class ValidationResult:
    """Result of code validation"""
    level: ValidationLevel  # ERROR, WARNING, INFO
    message: str
    rule: str
    line_number: Optional[int] = None
    column_number: Optional[int] = None
    suggestion: Optional[str] = None
```

## Multi-File Project Structure Examples

### React TypeScript Full Project
```
my-app/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   ├── Button.stories.tsx
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── hooks/
│   │   └── useAppState.ts
│   ├── types/
│   │   └── index.ts
│   ├── utils/
│   │   └── helpers.ts
│   ├── App.tsx
│   ├── App.test.tsx
│   ├── App.css
│   └── index.tsx
├── .eslintrc.js
├── .prettierrc
├── tsconfig.json
├── package.json
├── vite.config.ts
├── Dockerfile
├── docker-compose.yml
├── .github/
│   └── workflows/
│       └── ci.yml
└── README.md
```

### Vue 3 TypeScript Full Project
```
my-vue-app/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── Button.vue
│   │   └── __tests__/
│   │       └── Button.spec.ts
│   ├── composables/
│   │   └── useCounter.ts
│   ├── types/
│   │   └── index.ts
│   ├── App.vue
│   └── main.ts
├── .eslintrc.js
├── tsconfig.json
├── vite.config.ts
├── package.json
└── README.md
```

### React Native TypeScript Project
```
MyReactNativeApp/
├── src/
│   ├── components/
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── screens/
│   │   └── HomeScreen.tsx
│   ├── navigation/
│   │   └── AppNavigator.tsx
│   ├── types/
│   │   └── index.ts
│   └── App.tsx
├── __tests__/
│   └── App.test.tsx
├── android/
├── ios/
├── metro.config.js
├── package.json
├── tsconfig.json
└── README.md
```

## Contributing

### Adding New Export Formats

1. **Implement Generator Method** - Add format-specific generation logic in `EnhancedCodeGenerator`
   ```python
   def _generate_new_framework_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
       # Implementation here
   ```

2. **Add Format to Enum** - Update `ExportFormat` enum with new format
   ```python
   class ExportFormat(Enum):
       NEW_FRAMEWORK = "new-framework"
   ```

3. **Add Validation Rules** - Define validation rules for the format in `CodeValidator`
   ```python
   def _get_new_framework_rules(self) -> List[ValidationRule]:
       # Framework-specific validation rules
   ```

4. **Create Tests** - Add comprehensive tests for the new format
   ```python
   def test_new_framework_export(self):
       # Test implementation
   ```

5. **Update Frontend** - Add format to frontend export options
   ```javascript
   const newFormat = {
     value: 'new-framework',
     label: 'New Framework',
     description: 'Description of the framework',
     icon: '🆕'
   };
   ```

6. **Update Documentation** - Document the new format and its options

### Improving Code Quality

1. **Add Validation Rules** - Enhance code quality checks for better generated code
2. **Optimize Performance** - Improve generation speed and memory usage
3. **Enhance Accessibility** - Add more accessibility features and WCAG compliance
4. **Update Dependencies** - Keep frameworks and tools current with security patches
5. **Improve Error Handling** - Add better error messages and recovery mechanisms
6. **Add Metrics** - Implement performance monitoring and usage analytics

### Development Setup

1. **Clone Repository** - Get the latest code from the repository
2. **Install Dependencies** - Run `pip install -r requirements.txt` and `npm install`
3. **Run Tests** - Execute `python manage.py test` and `npm test`
4. **Start Development Server** - Run `python manage.py runserver` and `npm start`
5. **Make Changes** - Implement your improvements
6. **Test Changes** - Ensure all tests pass and add new tests
7. **Submit Pull Request** - Create PR with detailed description

### Code Style Guidelines

- **Python**: Follow PEP 8 with Black formatting
- **JavaScript/TypeScript**: Use ESLint and Prettier
- **Documentation**: Use clear, concise language with examples
- **Tests**: Write comprehensive tests with good coverage
- **Commit Messages**: Use conventional commit format

## License

This enhanced export functionality is part of the App Builder project and follows the same licensing terms. The project is open source and welcomes contributions from the community.

## Support

For support and questions:

- **Documentation**: Check this guide and other documentation files
- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join community discussions for help and ideas
- **Email**: Contact the development team for enterprise support

## Changelog

### Version 2.0.0 (Current)
- Added TypeScript support for all frameworks
- Implemented React Native and Flutter export
- Added comprehensive validation system
- Enhanced accessibility features
- Added Docker and CI/CD support
- Improved template integration

### Version 1.0.0
- Initial release with React, Vue, and Angular support
- Basic export functionality
- Simple validation system
