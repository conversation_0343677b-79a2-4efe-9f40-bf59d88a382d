import React from 'react';
import { Card, Typography, Tabs, Row, Col, Button } from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  BgColorsOutlined,
  ApiOutlined,
  ProjectOutlined,
  CodeOutlined,
  DashboardOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * Simplified App Builder component that doesn't rely on complex dependencies
 */
const SimplifiedAppBuilder = () => {
  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>App Builder Enhanced</Title>
          <Paragraph>
            Create and manage your application components with ease
          </Paragraph>
        </div>
        <Button type="default" icon={<InfoCircleOutlined />}>Help</Button>
      </div>

      <Card style={{ marginBottom: '24px', background: 'linear-gradient(135deg, #f0f5ff 0%, #f9f9f9 100%)', border: 'none' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={16}>
            <Title level={4}>Welcome to App Builder Enhanced</Title>
            <Paragraph>
              This tool helps you create and manage your application components with ease.
              Use the tabs below to navigate between different features.
            </Paragraph>
            <Button type="primary" size="large">
              Start Building
            </Button>
          </Col>
          <Col xs={24} md={8}>
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <div style={{ fontSize: '64px', color: '#2563EB' }}>
                <AppstoreOutlined />
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="components" type="card" size="large">
        <TabPane 
          tab={<span><ProjectOutlined /> Projects</span>} 
          key="projects"
        >
          <Card>
            <Title level={4}>Project Manager</Title>
            <Paragraph>
              Create and manage your projects here. This is a simplified version of the Project Manager component.
            </Paragraph>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><AppstoreOutlined /> Component Builder</span>} 
          key="components"
        >
          <Card>
            <Title level={4}>Component Builder</Title>
            <Paragraph>
              Create and manage your UI components here. This is a simplified version of the Component Builder component.
            </Paragraph>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><LayoutOutlined /> Layout Designer</span>} 
          key="layouts"
        >
          <Card>
            <Title level={4}>Layout Designer</Title>
            <Paragraph>
              Design your application layout here. This is a simplified version of the Layout Designer component.
            </Paragraph>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><BgColorsOutlined /> Theme Manager</span>} 
          key="themes"
        >
          <Card>
            <Title level={4}>Theme Manager</Title>
            <Paragraph>
              Customize your application theme here. This is a simplified version of the Theme Manager component.
            </Paragraph>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><CodeOutlined /> Export Code</span>} 
          key="export"
        >
          <Card>
            <Title level={4}>Code Exporter</Title>
            <Paragraph>
              Export your application code here. This is a simplified version of the Code Exporter component.
            </Paragraph>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><DashboardOutlined /> Performance</span>} 
          key="performance"
        >
          <Card>
            <Title level={4}>Performance Monitor</Title>
            <Paragraph>
              Monitor your application performance here. This is a simplified version of the Performance Monitor component.
            </Paragraph>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><ApiOutlined /> WebSocket Manager</span>} 
          key="websocket"
        >
          <Card>
            <Title level={4}>WebSocket Manager</Title>
            <Paragraph>
              Manage your WebSocket connections here. This is a simplified version of the WebSocket Manager component.
            </Paragraph>
          </Card>
        </TabPane>
      </Tabs>

      <Card title="Getting Started" style={{ marginTop: '24px' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={6}>
            <Card title="Step 1" hoverable>
              <div style={{ fontSize: '24px', color: '#2563EB', marginBottom: '16px' }}>
                <AppstoreOutlined />
              </div>
              <Paragraph>
                Use the Component Builder to create UI components
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={6}>
            <Card title="Step 2" hoverable>
              <div style={{ fontSize: '24px', color: '#2563EB', marginBottom: '16px' }}>
                <LayoutOutlined />
              </div>
              <Paragraph>
                Design your layout with the Layout Designer
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={6}>
            <Card title="Step 3" hoverable>
              <div style={{ fontSize: '24px', color: '#2563EB', marginBottom: '16px' }}>
                <BgColorsOutlined />
              </div>
              <Paragraph>
                Customize your theme with the Theme Manager
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={6}>
            <Card title="Step 4" hoverable>
              <div style={{ fontSize: '24px', color: '#2563EB', marginBottom: '16px' }}>
                <ApiOutlined />
              </div>
              <Paragraph>
                Set up real-time communication with WebSocket Manager
              </Paragraph>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default SimplifiedAppBuilder;
