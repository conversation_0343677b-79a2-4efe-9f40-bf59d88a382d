"""
Unit tests for service classes in the App Builder application.
Tests business logic, data processing, and service layer functionality.
"""

import pytest
import json
from django.test import TestCase
from django.contrib.auth.models import User
from model_bakery import baker

from my_app.models import ComponentTemplate, LayoutTemplate, AppTemplate
from my_app.services.template_service import TemplateService


@pytest.mark.django_db
class TestTemplateService:
    """Test cases for the TemplateService class."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_get_component_categories(self):
        """Test getting component template categories with counts."""
        # Create component templates
        ComponentTemplate.objects.create(
            name='Button 1',
            component_type='button',
            default_props='{}',
            user=self.user1
        )
        ComponentTemplate.objects.create(
            name='Button 2',
            component_type='button',
            default_props='{}',
            user=self.user2
        )
        ComponentTemplate.objects.create(
            name='Input 1',
            component_type='input',
            default_props='{}',
            user=self.user1
        )
        
        categories = TemplateService.get_component_categories()
        
        assert len(categories) == 2
        
        # Check button category
        button_cat = next(cat for cat in categories if cat['value'] == 'button')
        assert button_cat['label'] == 'Button'
        assert button_cat['count'] == 2
        
        # Check input category
        input_cat = next(cat for cat in categories if cat['value'] == 'input')
        assert input_cat['label'] == 'Input'
        assert input_cat['count'] == 1

    def test_get_layout_categories(self):
        """Test getting layout template categories with counts."""
        # Create layout templates
        LayoutTemplate.objects.create(
            name='Business Layout 1',
            layout_type='business',
            components={},
            user=self.user1
        )
        LayoutTemplate.objects.create(
            name='Business Layout 2',
            layout_type='business',
            components={},
            user=self.user2
        )
        LayoutTemplate.objects.create(
            name='Dashboard Layout',
            layout_type='dashboard',
            components={},
            user=self.user1
        )
        
        categories = TemplateService.get_layout_categories()
        
        assert len(categories) == 2
        
        # Check business category
        business_cat = next(cat for cat in categories if cat['value'] == 'business')
        assert business_cat['label'] == 'Business'
        assert business_cat['count'] == 2
        
        # Check dashboard category
        dashboard_cat = next(cat for cat in categories if cat['value'] == 'dashboard')
        assert dashboard_cat['label'] == 'Dashboard'
        assert dashboard_cat['count'] == 1

    def test_get_app_categories(self):
        """Test getting app template categories with counts."""
        # Create app templates
        AppTemplate.objects.create(
            name='Business App 1',
            app_category='business',
            components={},
            user=self.user1
        )
        AppTemplate.objects.create(
            name='Business App 2',
            app_category='business',
            components={},
            user=self.user2
        )
        AppTemplate.objects.create(
            name='E-commerce App',
            app_category='ecommerce',
            components={},
            user=self.user1
        )
        
        categories = TemplateService.get_app_categories()
        
        assert len(categories) == 2
        
        # Check business category
        business_cat = next(cat for cat in categories if cat['value'] == 'business')
        assert business_cat['label'] == 'Business Apps'
        assert business_cat['count'] == 2
        
        # Check ecommerce category
        ecommerce_cat = next(cat for cat in categories if cat['value'] == 'ecommerce')
        assert ecommerce_cat['label'] == 'E-commerce'
        assert ecommerce_cat['count'] == 1

    def test_search_templates_no_filters(self):
        """Test searching templates without any filters."""
        # Create test templates
        ComponentTemplate.objects.create(
            name='Test Button',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        LayoutTemplate.objects.create(
            name='Test Layout',
            layout_type='business',
            components={},
            user=self.user1,
            is_public=True
        )
        AppTemplate.objects.create(
            name='Test App',
            app_category='business',
            components={},
            user=self.user1,
            is_public=True
        )
        
        results = TemplateService.search_templates()
        
        assert len(results['components']) == 1
        assert len(results['layouts']) == 1
        assert len(results['apps']) == 1
        
        assert results['components'][0]['name'] == 'Test Button'
        assert results['layouts'][0]['name'] == 'Test Layout'
        assert results['apps'][0]['name'] == 'Test App'

    def test_search_templates_with_query(self):
        """Test searching templates with a query string."""
        # Create test templates
        ComponentTemplate.objects.create(
            name='Search Button',
            description='A searchable button',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        ComponentTemplate.objects.create(
            name='Other Button',
            description='Another button',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        
        results = TemplateService.search_templates(query='search')
        
        assert len(results['components']) == 1
        assert results['components'][0]['name'] == 'Search Button'

    def test_search_templates_with_category_filter(self):
        """Test searching templates with category filter."""
        # Create test templates
        ComponentTemplate.objects.create(
            name='Button Template',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        ComponentTemplate.objects.create(
            name='Input Template',
            component_type='input',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        
        results = TemplateService.search_templates(category='button')
        
        assert len(results['components']) == 1
        assert results['components'][0]['name'] == 'Button Template'

    def test_search_templates_with_template_type_filter(self):
        """Test searching templates with template type filter."""
        # Create test templates
        ComponentTemplate.objects.create(
            name='Component Template',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        LayoutTemplate.objects.create(
            name='Layout Template',
            layout_type='business',
            components={},
            user=self.user1,
            is_public=True
        )
        
        results = TemplateService.search_templates(template_type='components')
        
        assert len(results['components']) == 1
        assert len(results['layouts']) == 0
        assert len(results['apps']) == 0

    def test_search_templates_with_user_filter(self):
        """Test searching templates with user filter."""
        # Create test templates
        ComponentTemplate.objects.create(
            name='User1 Template',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        ComponentTemplate.objects.create(
            name='User2 Template',
            component_type='button',
            default_props='{}',
            user=self.user2,
            is_public=True
        )
        
        results = TemplateService.search_templates(user=self.user1)
        
        assert len(results['components']) == 1
        assert results['components'][0]['name'] == 'User1 Template'

    def test_search_templates_with_public_filter(self):
        """Test searching templates with public filter."""
        # Create test templates
        ComponentTemplate.objects.create(
            name='Public Template',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        ComponentTemplate.objects.create(
            name='Private Template',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=False
        )
        
        results = TemplateService.search_templates(is_public=True)
        
        assert len(results['components']) == 1
        assert results['components'][0]['name'] == 'Public Template'

    def test_get_featured_templates(self):
        """Test getting featured templates."""
        # Create more than 6 templates of each type to test limit
        for i in range(8):
            ComponentTemplate.objects.create(
                name=f'Component {i}',
                component_type='button',
                default_props='{}',
                user=self.user1,
                is_public=True
            )
            LayoutTemplate.objects.create(
                name=f'Layout {i}',
                layout_type='business',
                components={},
                user=self.user1,
                is_public=True
            )
            AppTemplate.objects.create(
                name=f'App {i}',
                app_category='business',
                components={},
                user=self.user1,
                is_public=True
            )
        
        featured = TemplateService.get_featured_templates()
        
        # Should return only 6 of each type
        assert len(featured['components']) == 6
        assert len(featured['layouts']) == 6
        assert len(featured['apps']) == 6

    def test_get_template_stats(self):
        """Test getting template statistics."""
        # Create test templates
        ComponentTemplate.objects.create(
            name='Public Component',
            component_type='button',
            default_props='{}',
            user=self.user1,
            is_public=True
        )
        ComponentTemplate.objects.create(
            name='Private Component',
            component_type='input',
            default_props='{}',
            user=self.user1,
            is_public=False
        )
        LayoutTemplate.objects.create(
            name='Public Layout',
            layout_type='business',
            components={},
            user=self.user1,
            is_public=True
        )
        AppTemplate.objects.create(
            name='Public App',
            app_category='business',
            components={},
            user=self.user1,
            is_public=True
        )
        
        stats = TemplateService.get_template_stats()
        
        assert stats['total_templates'] == 4
        assert stats['public_templates'] == 3
        assert stats['component_templates'] == 2
        assert stats['layout_templates'] == 1
        assert stats['app_templates'] == 1
        assert stats['categories']['components'] == 2  # button and input
        assert stats['categories']['layouts'] == 1  # business
        assert stats['categories']['apps'] == 1  # business

    def test_clone_component_template(self):
        """Test cloning a component template."""
        original = ComponentTemplate.objects.create(
            name='Original Button',
            description='Original description',
            component_type='button',
            default_props='{"text": "Click me"}',
            user=self.user1,
            is_public=True
        )
        
        cloned = TemplateService.clone_template(
            template_id=original.id,
            template_type='component',
            user=self.user2,
            new_name='Cloned Button'
        )
        
        assert cloned.name == 'Cloned Button'
        assert cloned.description == original.description
        assert cloned.component_type == original.component_type
        assert cloned.default_props == original.default_props
        assert cloned.user == self.user2
        assert cloned.is_public is False

    def test_clone_layout_template(self):
        """Test cloning a layout template."""
        original = LayoutTemplate.objects.create(
            name='Original Layout',
            description='Original description',
            layout_type='business',
            components={'header': {'type': 'header'}},
            default_props={'theme': 'light'},
            user=self.user1,
            is_public=True
        )
        
        cloned = TemplateService.clone_template(
            template_id=original.id,
            template_type='layout',
            user=self.user2
        )
        
        assert cloned.name == 'Original Layout (Copy)'
        assert cloned.description == original.description
        assert cloned.layout_type == original.layout_type
        assert cloned.components == original.components
        assert cloned.default_props == original.default_props
        assert cloned.user == self.user2
        assert cloned.is_public is False

    def test_clone_app_template(self):
        """Test cloning an app template."""
        original = AppTemplate.objects.create(
            name='Original App',
            description='Original description',
            app_category='business',
            components={'app': {'type': 'app'}},
            default_props={'theme': 'modern'},
            required_components=['header', 'footer'],
            preview_image='https://example.com/preview.jpg',
            user=self.user1,
            is_public=True
        )
        
        cloned = TemplateService.clone_template(
            template_id=original.id,
            template_type='app',
            user=self.user2
        )
        
        assert cloned.name == 'Original App (Copy)'
        assert cloned.description == original.description
        assert cloned.app_category == original.app_category
        assert cloned.components == original.components
        assert cloned.default_props == original.default_props
        assert cloned.required_components == original.required_components
        assert cloned.preview_image == original.preview_image
        assert cloned.user == self.user2
        assert cloned.is_public is False

    def test_clone_template_invalid_type(self):
        """Test cloning with invalid template type."""
        original = ComponentTemplate.objects.create(
            name='Test Component',
            component_type='button',
            default_props='{}',
            user=self.user1
        )
        
        with pytest.raises(Exception) as exc_info:
            TemplateService.clone_template(
                template_id=original.id,
                template_type='invalid_type',
                user=self.user2
            )
        
        assert 'Invalid template type' in str(exc_info.value)

    def test_clone_template_nonexistent_id(self):
        """Test cloning with non-existent template ID."""
        with pytest.raises(Exception) as exc_info:
            TemplateService.clone_template(
                template_id=99999,
                template_type='component',
                user=self.user2
            )
        
        assert 'Failed to clone template' in str(exc_info.value)
