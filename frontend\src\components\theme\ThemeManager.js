import React, { useState, useEffect, createContext, useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Form, Input, Button, Select, Switch, Divider, Row, Col, Space, Tooltip, Popconfirm, message, Drawer } from 'antd';
import {
  BgColorsOutlined,
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';

// Default themes
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
  CUSTOM: 'custom'
};

// Default colors
const DEFAULT_COLORS = {
  [THEMES.LIGHT]: {
    primary: '#1976d2',
    secondary: '#f50057',
    background: '#ffffff',
    surface: '#f5f5f5',
    text: '#212121',
    textSecondary: '#757575',
    border: '#e0e0e0',
    error: '#d32f2f',
    warning: '#f57c00',
    info: '#0288d1',
    success: '#388e3c'
  },
  [THEMES.DARK]: {
    primary: '#90caf9',
    secondary: '#f48fb1',
    background: '#121212',
    surface: '#1e1e1e',
    text: '#ffffff',
    textSecondary: '#b0b0b0',
    border: '#333333',
    error: '#f44336',
    warning: '#ff9800',
    info: '#29b6f6',
    success: '#66bb6a'
  }
};

// Create theme context
const ThemeContext = createContext({
  theme: THEMES.LIGHT,
  colors: DEFAULT_COLORS[THEMES.LIGHT],
  setTheme: () => { },
  setCustomColors: () => { }
});

/**
 * Theme provider component
 */
export const ThemeProvider = ({ children, initialTheme = THEMES.SYSTEM }) => {
  const [theme, setThemeState] = useState(initialTheme);
  const [customColors, setCustomColors] = useState({});
  const [systemTheme, setSystemTheme] = useState(
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      ? THEMES.DARK
      : THEMES.LIGHT
  );

  // Get current theme colors
  const getThemeColors = () => {
    if (theme === THEMES.SYSTEM) {
      return DEFAULT_COLORS[systemTheme];
    }

    if (theme === THEMES.CUSTOM) {
      return { ...DEFAULT_COLORS[THEMES.LIGHT], ...customColors };
    }

    return DEFAULT_COLORS[theme];
  };

  // Set theme
  const setTheme = (newTheme) => {
    setThemeState(newTheme);
    localStorage.setItem('app_theme', newTheme);
  };

  // Apply theme to document
  useEffect(() => {
    // Get colors
    const colors = getThemeColors();

    // Apply colors to document
    Object.entries(colors).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--color-${key}`, value);
    });

    // Set data-theme attribute
    const effectiveTheme = theme === THEMES.SYSTEM ? systemTheme : theme;
    document.documentElement.setAttribute('data-theme', effectiveTheme);

    // Add/remove dark class
    if (effectiveTheme === THEMES.DARK) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [theme, systemTheme, customColors]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e) => {
      setSystemTheme(e.matches ? THEMES.DARK : THEMES.LIGHT);
    };

    // Add listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }

    // Load saved theme
    const savedTheme = localStorage.getItem('app_theme');
    if (savedTheme) {
      setThemeState(savedTheme);
    }

    // Clean up
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // Fallback for older browsers
        mediaQuery.removeListener(handleChange);
      }
    };
  }, []);

  return (
    <ThemeContext.Provider
      value={{
        theme,
        colors: getThemeColors(),
        setTheme,
        setCustomColors
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

// Hook to use theme
export const useTheme = () => {
  const context = useContext(ThemeContext);

  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  return context;
};

/**
 * ThemeManager Component
 * Manages theme selection, customization, and import/export
 */
const ThemeManager = ({ placement = 'right', width = 400, visible = false, onClose = () => { } }) => {
  // Component state
  const [editMode, setEditMode] = useState(false);
  const [currentTheme, setCurrentTheme] = useState(null);
  const [previewTheme, setPreviewTheme] = useState(null);
  const [form] = Form.useForm();

  // Redux
  const dispatch = useDispatch();
  const themes = useSelector(state => state.theme?.themes || []);
  const userPreferences = useSelector(state => state.preferences || {});

  // Placeholder functions
  const handleThemeChange = () => { };
  const handleToggleAutoApply = () => { };
  const handleEditTheme = () => { };
  const handleCancelEdit = () => { };
  const handleSaveTheme = () => { };
  const handleCreateTheme = () => { };
  const handleDeleteTheme = () => { };
  const handleFormValuesChange = () => { };
  const handleExportTheme = () => { };
  const handleImportTheme = () => { };

  return (
    <Drawer
      title="Theme Manager"
      placement={placement}
      width={width}
      onClose={onClose}
      open={visible}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormValuesChange}
      >
        <div>Theme Manager Form Content</div>
      </Form>
    </Drawer>
  );
};

/**
 * ThemeSwitcher Component
 * Simple toggle for switching between light and dark themes
 */
export const ThemeSwitcher = ({ position = 'right' }) => {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    if (theme === THEMES.DARK) {
      setTheme(THEMES.LIGHT);
    } else {
      setTheme(THEMES.DARK);
    }
  };

  return (
    <div className={`theme-switcher ${position}`}>
      <Button
        type="text"
        icon={theme === THEMES.DARK ? <BgColorsOutlined /> : <BgColorsOutlined />}
        onClick={toggleTheme}
        aria-label={`Switch to ${theme === THEMES.DARK ? 'light' : 'dark'} theme`}
      />
    </div>
  );
};

/**
 * ThemeCustomizer Component
 * Allows customization of theme colors and settings
 */
export const ThemeCustomizer = ({ onSave }) => {
  const { colors, setCustomColors } = useTheme();
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      primaryColor: colors.primary,
      secondaryColor: colors.secondary,
      backgroundColor: colors.background,
      textColor: colors.text
    });
  }, [colors, form]);

  const handleSubmit = (values) => {
    setCustomColors(values);
    if (onSave) {
      onSave(values);
    }
  };

  return (
    <div className="theme-customizer">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="primaryColor"
          label="Primary Color"
        >
          <Input type="color" />
        </Form.Item>

        <Form.Item
          name="secondaryColor"
          label="Secondary Color"
        >
          <Input type="color" />
        </Form.Item>

        <Form.Item
          name="backgroundColor"
          label="Background Color"
        >
          <Input type="color" />
        </Form.Item>

        <Form.Item
          name="textColor"
          label="Text Color"
        >
          <Input type="color" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit">
            Apply Theme
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ThemeManager;
