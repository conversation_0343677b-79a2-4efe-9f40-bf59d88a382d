import React, { Suspense } from 'react';
import { Spin, Alert, Button } from 'antd';
import { LoadingOutlined, ReloadOutlined } from '@ant-design/icons';

/**
 * Enhanced Lazy Loading Utilities
 * Provides robust lazy loading infrastructure with error handling and loading states
 */

// Default loading component
const DefaultLoadingComponent = ({ 
  size = 'large', 
  tip = 'Loading...', 
  fullPage = false,
  description = null 
}) => {
  const spinIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
  
  const spinComponent = (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center', 
      justifyContent: 'center',
      gap: '12px',
      padding: fullPage ? '60px 20px' : '20px',
      minHeight: fullPage ? '200px' : 'auto'
    }}>
      <Spin 
        indicator={spinIcon} 
        size={size} 
        tip={tip}
      />
      {description && (
        <div style={{ 
          color: '#666', 
          fontSize: '14px', 
          textAlign: 'center',
          maxWidth: '300px'
        }}>
          {description}
        </div>
      )}
    </div>
  );

  return fullPage ? (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {spinComponent}
    </div>
  ) : spinComponent;
};

// Error boundary for lazy loaded components
class LazyLoadErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Lazy loading error:', error, errorInfo);
    
    // Report to error tracking service if available
    if (window.reportError) {
      window.reportError(error, { context: 'lazy-loading', ...errorInfo });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
    // Force a re-render by updating the key
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      const { fallback, componentName = 'Component' } = this.props;
      
      if (fallback) {
        return fallback;
      }

      return (
        <Alert
          message={`Failed to load ${componentName}`}
          description={
            <div>
              <p>There was an error loading this component. This might be due to a network issue or a temporary problem.</p>
              <Button 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={this.handleRetry}
                style={{ marginTop: '8px' }}
              >
                Try Again
              </Button>
            </div>
          }
          type="error"
          showIcon
          style={{ margin: '20px' }}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Enhanced lazy loading wrapper with retry logic and error handling
 */
export const createLazyComponent = (
  importFunction,
  options = {}
) => {
  const {
    fallback = <DefaultLoadingComponent />,
    errorFallback = null,
    componentName = 'Component',
    retryAttempts = 3,
    retryDelay = 1000,
    preload = false
  } = options;

  // Create the lazy component with retry logic
  const LazyComponent = React.lazy(() => {
    let attempts = 0;
    
    const loadWithRetry = async () => {
      try {
        const module = await importFunction();
        return module;
      } catch (error) {
        attempts++;
        
        if (attempts < retryAttempts) {
          console.warn(`Failed to load ${componentName}, retrying... (${attempts}/${retryAttempts})`);
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempts));
          return loadWithRetry();
        }
        
        console.error(`Failed to load ${componentName} after ${retryAttempts} attempts:`, error);
        throw error;
      }
    };

    return loadWithRetry();
  });

  // Preload the component if requested
  if (preload) {
    // Preload after a short delay to not block initial render
    setTimeout(() => {
      importFunction().catch(err => {
        console.warn(`Preload failed for ${componentName}:`, err);
      });
    }, 100);
  }

  // Return wrapped component
  const WrappedComponent = React.forwardRef((props, ref) => (
    <LazyLoadErrorBoundary 
      componentName={componentName}
      fallback={errorFallback}
      onRetry={() => {
        // Force component re-mount by changing key
        if (props.onRetry) props.onRetry();
      }}
    >
      <Suspense fallback={fallback}>
        <LazyComponent {...props} ref={ref} />
      </Suspense>
    </LazyLoadErrorBoundary>
  ));

  WrappedComponent.displayName = `Lazy(${componentName})`;
  
  // Add preload method to component
  WrappedComponent.preload = () => importFunction();
  
  return WrappedComponent;
};

/**
 * Hook for progressive loading of components
 */
export const useProgressiveLoading = (components = [], delay = 100) => {
  const [loadedComponents, setLoadedComponents] = React.useState(new Set());

  React.useEffect(() => {
    let timeouts = [];

    components.forEach((component, index) => {
      const timeout = setTimeout(() => {
        if (component.preload) {
          component.preload().then(() => {
            setLoadedComponents(prev => new Set([...prev, component.displayName]));
          }).catch(err => {
            console.warn(`Progressive loading failed for ${component.displayName}:`, err);
          });
        }
      }, delay * (index + 1));

      timeouts.push(timeout);
    });

    return () => {
      timeouts.forEach(clearTimeout);
    };
  }, [components, delay]);

  return loadedComponents;
};

/**
 * Preload components based on user interaction
 */
export const preloadOnInteraction = (component, events = ['mouseenter', 'focus']) => {
  return (targetElement) => {
    if (!targetElement || !component.preload) return;

    const handleInteraction = () => {
      component.preload();
      // Remove listeners after first interaction
      events.forEach(event => {
        targetElement.removeEventListener(event, handleInteraction);
      });
    };

    events.forEach(event => {
      targetElement.addEventListener(event, handleInteraction, { passive: true });
    });

    return () => {
      events.forEach(event => {
        targetElement.removeEventListener(event, handleInteraction);
      });
    };
  };
};

/**
 * Intersection Observer based preloading
 */
export const preloadOnVisible = (component, options = {}) => {
  const { threshold = 0.1, rootMargin = '50px' } = options;

  return (targetElement) => {
    if (!targetElement || !component.preload || !window.IntersectionObserver) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            component.preload();
            observer.unobserve(targetElement);
          }
        });
      },
      { threshold, rootMargin }
    );

    observer.observe(targetElement);

    return () => observer.disconnect();
  };
};

// Export loading components for reuse
export { DefaultLoadingComponent, LazyLoadErrorBoundary };

// Predefined loading states for common scenarios
export const LoadingStates = {
  minimal: <DefaultLoadingComponent size="small" tip="Loading..." />,
  standard: <DefaultLoadingComponent size="large" tip="Loading component..." />,
  fullPage: <DefaultLoadingComponent size="large" tip="Loading..." fullPage={true} />,
  withDescription: (description) => (
    <DefaultLoadingComponent 
      size="large" 
      tip="Loading..." 
      description={description} 
    />
  )
};
