import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  addTheme,
  updateTheme,
  removeTheme,
  setActiveTheme,
  toggleAutoApplyTheme,
  resetThemes,
  selectThemes,
  selectActiveTheme,
  selectUserPreferences
} from '../../redux/slices/themeSlice';
import {
  Button,
  Card,
  Space,
  Typography,
  Divider,
  Switch,
  Tooltip,
  Tabs,
  ColorPicker,
  Select,
  message,
  Input,
  Row,
  Col,
  Modal,
  Drawer
} from 'antd';
import {
  BgColorsOutlined,
  FontSizeOutlined,
  SaveOutlined,
  ImportOutlined,
  ExportOutlined,
  UndoOutlined,
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// Styled components using inline styles for simplicity
const styles = {
  themeCard: {
    marginBottom: '16px',
  },
  colorGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '16px',
    marginBottom: '16px',
  },
  colorItem: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  colorLabel: {
    marginBottom: '8px',
  },
  fontItem: {
    marginBottom: '16px',
  },
  themePreview: {
    border: '1px solid #e5e7eb',
    borderRadius: '8px',
    padding: '16px',
    marginTop: '16px',
  },
  themePreviewText: {
    marginBottom: '8px',
  },
  themePreviewButton: {
    marginRight: '8px',
  },
  themeItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px',
    border: '1px solid #e5e7eb',
    borderRadius: '8px',
    marginBottom: '12px',
  },
  activeTheme: {
    borderColor: '#2563EB',
    backgroundColor: 'rgba(37, 99, 235, 0.05)',
  },
  themeActions: {
    display: 'flex',
    gap: '8px',
  },
  colorPreview: {
    width: '24px',
    height: '24px',
    borderRadius: '4px',
    display: 'inline-block',
    marginRight: '8px',
    border: '1px solid #e5e7eb',
  },
  formItem: {
    marginBottom: '16px',
  },
  formLabel: {
    display: 'block',
    marginBottom: '8px',
    fontWeight: 'bold',
  },
};

const ThemeManagerRedux = ({ visible, onClose, placement = 'right', width = 400 }) => {
  const dispatch = useDispatch();
  const themes = useSelector(selectThemes);
  const activeThemeId = useSelector(selectActiveTheme);
  const userPreferences = useSelector(selectUserPreferences);
  
  const [editingTheme, setEditingTheme] = useState(null);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isNewThemeModalVisible, setIsNewThemeModalVisible] = useState(false);
  const [newTheme, setNewTheme] = useState({
    id: '',
    name: '',
    primaryColor: '#2563EB',
    secondaryColor: '#10B981',
    backgroundColor: '#FFFFFF',
    textColor: '#111827',
    fontFamily: 'Inter, sans-serif',
  });

  // Get the active theme object
  const activeTheme = themes.find(theme => theme.id === activeThemeId) || themes[0];

  // Handle theme selection
  const handleThemeSelect = (themeId) => {
    dispatch(setActiveTheme(themeId));
    message.success(`Theme "${themes.find(t => t.id === themeId)?.name}" applied successfully`);
  };

  // Handle auto-apply toggle
  const handleAutoApplyToggle = () => {
    dispatch(toggleAutoApplyTheme());
    message.info(`Auto-apply theme ${!userPreferences.autoApplyTheme ? 'enabled' : 'disabled'}`);
  };

  // Handle theme edit
  const handleEditTheme = (theme) => {
    setEditingTheme({ ...theme });
    setIsEditModalVisible(true);
  };

  // Handle theme update
  const handleUpdateTheme = () => {
    if (editingTheme) {
      dispatch(updateTheme(editingTheme));
      setIsEditModalVisible(false);
      message.success(`Theme "${editingTheme.name}" updated successfully`);
    }
  };

  // Handle theme delete
  const handleDeleteTheme = (themeId) => {
    Modal.confirm({
      title: 'Are you sure you want to delete this theme?',
      content: 'This action cannot be undone.',
      okText: 'Yes, delete it',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        dispatch(removeTheme(themeId));
        message.success('Theme deleted successfully');
      },
    });
  };

  // Handle new theme creation
  const handleCreateTheme = () => {
    if (!newTheme.id || !newTheme.name) {
      message.error('Theme ID and name are required');
      return;
    }

    const themeWithTimestamp = {
      ...newTheme,
      createdAt: new Date().toISOString()
    };

    dispatch(addTheme(themeWithTimestamp));
    setIsNewThemeModalVisible(false);
    setNewTheme({
      id: '',
      name: '',
      primaryColor: '#2563EB',
      secondaryColor: '#10B981',
      backgroundColor: '#FFFFFF',
      textColor: '#111827',
      fontFamily: 'Inter, sans-serif',
    });
    message.success(`Theme "${newTheme.name}" created successfully`);
  };

  // Handle theme reset
  const handleResetThemes = () => {
    Modal.confirm({
      title: 'Are you sure you want to reset all themes?',
      content: 'This will restore the default themes and remove any custom themes you have created.',
      okText: 'Yes, reset themes',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        dispatch(resetThemes());
        message.success('Themes reset to defaults');
      },
    });
  };

  return (
    <Drawer
      title="Theme Manager"
      placement={placement}
      width={width}
      onClose={onClose}
      open={visible}
      extra={
        <Space>
          <Button onClick={onClose}>Close</Button>
        </Space>
      }
    >
      <Tabs defaultActiveKey="themes">
        <TabPane tab="Themes" key="themes">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setIsNewThemeModalVisible(true)}
              >
                New Theme
              </Button>
              <Button 
                icon={<UndoOutlined />}
                onClick={handleResetThemes}
              >
                Reset
              </Button>
            </div>

            <div>
              <Text type="secondary">Auto-apply selected theme</Text>
              <Switch 
                checked={userPreferences.autoApplyTheme}
                onChange={handleAutoApplyToggle}
                style={{ marginLeft: '8px' }}
              />
            </div>

            <Divider />

            {themes.map(theme => (
              <div 
                key={theme.id}
                style={{
                  ...styles.themeItem,
                  ...(theme.id === activeThemeId ? styles.activeTheme : {})
                }}
              >
                <div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div 
                      style={{
                        ...styles.colorPreview,
                        backgroundColor: theme.primaryColor
                      }}
                    />
                    <Text strong>{theme.name}</Text>
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {theme.id}
                  </Text>
                </div>
                <div style={styles.themeActions}>
                  <Button 
                    type={theme.id === activeThemeId ? "primary" : "default"}
                    size="small"
                    onClick={() => handleThemeSelect(theme.id)}
                  >
                    {theme.id === activeThemeId ? "Active" : "Apply"}
                  </Button>
                  <Button 
                    icon={<EditOutlined />}
                    size="small"
                    onClick={() => handleEditTheme(theme)}
                  />
                  <Button 
                    icon={<DeleteOutlined />}
                    size="small"
                    danger
                    disabled={theme.id === activeThemeId}
                    onClick={() => handleDeleteTheme(theme.id)}
                  />
                </div>
              </div>
            ))}
          </Space>
        </TabPane>
      </Tabs>

      {/* Edit Theme Modal */}
      <Modal
        title="Edit Theme"
        open={isEditModalVisible}
        onOk={handleUpdateTheme}
        onCancel={() => setIsEditModalVisible(false)}
      >
        {editingTheme && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={styles.formItem}>
              <label style={styles.formLabel}>Theme Name</label>
              <Input
                value={editingTheme.name}
                onChange={(e) => setEditingTheme({ ...editingTheme, name: e.target.value })}
              />
            </div>
            <div style={styles.formItem}>
              <label style={styles.formLabel}>Primary Color</label>
              <ColorPicker
                value={editingTheme.primaryColor}
                onChange={(color) => setEditingTheme({ ...editingTheme, primaryColor: color.toHexString() })}
              />
            </div>
            <div style={styles.formItem}>
              <label style={styles.formLabel}>Secondary Color</label>
              <ColorPicker
                value={editingTheme.secondaryColor}
                onChange={(color) => setEditingTheme({ ...editingTheme, secondaryColor: color.toHexString() })}
              />
            </div>
            <div style={styles.formItem}>
              <label style={styles.formLabel}>Background Color</label>
              <ColorPicker
                value={editingTheme.backgroundColor}
                onChange={(color) => setEditingTheme({ ...editingTheme, backgroundColor: color.toHexString() })}
              />
            </div>
            <div style={styles.formItem}>
              <label style={styles.formLabel}>Text Color</label>
              <ColorPicker
                value={editingTheme.textColor}
                onChange={(color) => setEditingTheme({ ...editingTheme, textColor: color.toHexString() })}
              />
            </div>
            <div style={styles.formItem}>
              <label style={styles.formLabel}>Font Family</label>
              <Select
                value={editingTheme.fontFamily}
                onChange={(value) => setEditingTheme({ ...editingTheme, fontFamily: value })}
                style={{ width: '100%' }}
              >
                <Option value="Inter, sans-serif">Inter</Option>
                <Option value="Roboto, sans-serif">Roboto</Option>
                <Option value="Poppins, sans-serif">Poppins</Option>
                <Option value="Arial, sans-serif">Arial</Option>
                <Option value="Georgia, serif">Georgia</Option>
              </Select>
            </div>
          </Space>
        )}
      </Modal>

      {/* New Theme Modal */}
      <Modal
        title="Create New Theme"
        open={isNewThemeModalVisible}
        onOk={handleCreateTheme}
        onCancel={() => setIsNewThemeModalVisible(false)}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={styles.formItem}>
            <label style={styles.formLabel}>Theme ID</label>
            <Input
              value={newTheme.id}
              onChange={(e) => setNewTheme({ ...newTheme, id: e.target.value })}
              placeholder="unique-theme-id"
            />
          </div>
          <div style={styles.formItem}>
            <label style={styles.formLabel}>Theme Name</label>
            <Input
              value={newTheme.name}
              onChange={(e) => setNewTheme({ ...newTheme, name: e.target.value })}
              placeholder="My Custom Theme"
            />
          </div>
          <div style={styles.formItem}>
            <label style={styles.formLabel}>Primary Color</label>
            <ColorPicker
              value={newTheme.primaryColor}
              onChange={(color) => setNewTheme({ ...newTheme, primaryColor: color.toHexString() })}
            />
          </div>
          <div style={styles.formItem}>
            <label style={styles.formLabel}>Secondary Color</label>
            <ColorPicker
              value={newTheme.secondaryColor}
              onChange={(color) => setNewTheme({ ...newTheme, secondaryColor: color.toHexString() })}
            />
          </div>
          <div style={styles.formItem}>
            <label style={styles.formLabel}>Background Color</label>
            <ColorPicker
              value={newTheme.backgroundColor}
              onChange={(color) => setNewTheme({ ...newTheme, backgroundColor: color.toHexString() })}
            />
          </div>
          <div style={styles.formItem}>
            <label style={styles.formLabel}>Text Color</label>
            <ColorPicker
              value={newTheme.textColor}
              onChange={(color) => setNewTheme({ ...newTheme, textColor: color.toHexString() })}
            />
          </div>
          <div style={styles.formItem}>
            <label style={styles.formLabel}>Font Family</label>
            <Select
              value={newTheme.fontFamily}
              onChange={(value) => setNewTheme({ ...newTheme, fontFamily: value })}
              style={{ width: '100%' }}
            >
              <Option value="Inter, sans-serif">Inter</Option>
              <Option value="Roboto, sans-serif">Roboto</Option>
              <Option value="Poppins, sans-serif">Poppins</Option>
              <Option value="Arial, sans-serif">Arial</Option>
              <Option value="Georgia, serif">Georgia</Option>
            </Select>
          </div>
        </Space>
      </Modal>
    </Drawer>
  );
};

export default ThemeManagerRedux;
