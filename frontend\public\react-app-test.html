<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React App Test - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 React App Loading Test</h1>
        <p>This test checks if the main React application is loading correctly.</p>
        
        <div id="test-results">
            <div class="status info">
                <strong>🔄 Running tests...</strong>
            </div>
        </div>

        <h3>📱 Main App Preview</h3>
        <iframe id="app-frame" src="/" title="Main App"></iframe>

        <h3>🔍 Test Results</h3>
        <div id="detailed-results"></div>

        <h3>📋 Console Output</h3>
        <pre id="console-output"></pre>

        <div>
            <button onclick="runTests()">🔄 Re-run Tests</button>
            <button onclick="openMainApp()">🚀 Open Main App</button>
            <button onclick="clearOutput()">🗑️ Clear Output</button>
        </div>
    </div>

    <script>
        let testOutput = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testOutput.push(`[${timestamp}] [${type.toUpperCase()}] ${message}`);
            updateConsoleOutput();
        }

        function updateConsoleOutput() {
            document.getElementById('console-output').textContent = testOutput.slice(-50).join('\n');
        }

        function clearOutput() {
            testOutput = [];
            updateConsoleOutput();
        }

        async function testMainAppLoading() {
            log('Testing main app loading...', 'info');
            
            try {
                // Test if main page loads
                const response = await fetch('/');
                if (response.ok) {
                    const html = await response.text();
                    log('✅ Main page loads successfully', 'success');
                    
                    // Check if HTML contains React root
                    if (html.includes('id="root"')) {
                        log('✅ Root element found in HTML', 'success');
                    } else {
                        log('❌ Root element not found in HTML', 'error');
                    }
                    
                    // Check if HTML contains script tags
                    const scriptMatches = html.match(/<script[^>]*src="[^"]*\.js"[^>]*>/g);
                    if (scriptMatches && scriptMatches.length > 0) {
                        log(`✅ Found ${scriptMatches.length} script tag(s)`, 'success');
                        scriptMatches.forEach((script, index) => {
                            log(`  Script ${index + 1}: ${script}`, 'info');
                        });
                    } else {
                        log('❌ No script tags found', 'error');
                    }
                    
                } else {
                    log(`❌ Main page failed to load: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Error testing main app: ${error.message}`, 'error');
            }
        }

        async function testBundleFiles() {
            log('Testing bundle files...', 'info');
            
            const bundleFiles = [
                '/static/js/main.c2e876e4.js',
                '/static/css/main.eb8080eb.css'
            ];
            
            for (const file of bundleFiles) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        log(`✅ ${file} - OK (${size ? Math.round(size/1024) + 'KB' : 'unknown size'})`, 'success');
                    } else {
                        log(`❌ ${file} - Failed (${response.status})`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${file} - Error: ${error.message}`, 'error');
                }
            }
        }

        async function testReactInFrame() {
            log('Testing React in iframe...', 'info');
            
            return new Promise((resolve) => {
                const iframe = document.getElementById('app-frame');
                let timeoutId;
                
                const checkReact = () => {
                    try {
                        const iframeWindow = iframe.contentWindow;
                        const iframeDocument = iframe.contentDocument;
                        
                        if (iframeDocument && iframeDocument.readyState === 'complete') {
                            // Check if React is available
                            if (iframeWindow.React) {
                                log('✅ React is available in iframe', 'success');
                                log(`  React version: ${iframeWindow.React.version || 'unknown'}`, 'info');
                            } else {
                                log('❌ React not available in iframe', 'error');
                            }
                            
                            // Check if ReactDOM is available
                            if (iframeWindow.ReactDOM) {
                                log('✅ ReactDOM is available in iframe', 'success');
                            } else {
                                log('❌ ReactDOM not available in iframe', 'error');
                            }
                            
                            // Check root element content
                            const rootElement = iframeDocument.getElementById('root');
                            if (rootElement) {
                                log('✅ Root element found in iframe', 'success');
                                if (rootElement.children.length > 0) {
                                    log(`✅ Root has ${rootElement.children.length} child element(s)`, 'success');
                                    log(`  Root content preview: ${rootElement.innerHTML.substring(0, 100)}...`, 'info');
                                } else {
                                    log('⚠️ Root element is empty', 'warning');
                                }
                            } else {
                                log('❌ Root element not found in iframe', 'error');
                            }
                            
                            clearTimeout(timeoutId);
                            resolve();
                        }
                    } catch (error) {
                        log(`❌ Error checking iframe: ${error.message}`, 'error');
                        clearTimeout(timeoutId);
                        resolve();
                    }
                };
                
                // Set up timeout
                timeoutId = setTimeout(() => {
                    log('⚠️ Iframe test timed out', 'warning');
                    resolve();
                }, 10000);
                
                // Check immediately and then periodically
                iframe.onload = () => {
                    setTimeout(checkReact, 1000); // Wait a bit for React to load
                };
                
                // If already loaded, check now
                if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                    setTimeout(checkReact, 1000);
                }
            });
        }

        async function runTests() {
            log('🧪 Starting React app tests...', 'info');
            
            document.getElementById('test-results').innerHTML = 
                '<div class="status info"><strong>🔄 Running tests...</strong></div>';
            
            document.getElementById('detailed-results').innerHTML = '';
            
            await testMainAppLoading();
            await testBundleFiles();
            await testReactInFrame();
            
            log('✅ All tests completed', 'success');
            
            document.getElementById('test-results').innerHTML = 
                '<div class="status success"><strong>✅ Tests completed! Check console output for details.</strong></div>';
        }

        function openMainApp() {
            window.open('/', '_blank');
        }

        // Run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000); // Wait a bit for iframe to start loading
        });
    </script>
</body>
</html>
