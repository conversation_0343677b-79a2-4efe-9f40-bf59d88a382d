/**
 * UX Enhanced Property Editor
 * 
 * A comprehensive property editor with enhanced UI/UX features:
 * - Intuitive form controls with better validation
 * - Real-time preview integration
 * - Property grouping and organization
 * - Contextual help and tooltips
 * - Improved visual hierarchy
 * - Accessibility enhancements
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Typography,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Slider,
  ColorPicker,
  Button,
  Tabs,
  Collapse,
  Space,
  Tooltip,
  Alert,
  Badge,
  Divider,
  Card,
  Row,
  Col,
  message
} from 'antd';
import {
  InfoCircleOutlined,
  EyeOutlined,
  ReloadOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  SettingOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  ExpandOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  StarOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme, a11yUtils, componentUtils } from '../../design-system';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Panel } = Collapse;
const { TabPane } = Tabs;
const { TextArea } = Input;

// Enhanced styled components
const PropertyEditorContainer = styled.div`
  background: ${theme.colors.background.paper};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid ${theme.colors.border.light};
`;

const PropertyHeader = styled.div`
  padding: ${theme.spacing[4]};
  background: linear-gradient(135deg, ${theme.colors.secondary.main} 0%, ${theme.colors.primary.main} 100%);
  color: ${theme.colors.background.paper};
  border-bottom: 1px solid ${theme.colors.border.light};
  
  .ant-typography {
    color: ${theme.colors.background.paper} !important;
    margin-bottom: ${theme.spacing[1]};
  }
`;

const PropertyContent = styled.div`
  flex: 1;
  overflow-y: auto;
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${theme.colors.background.secondary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.border.medium};
    border-radius: ${theme.borderRadius.full};
  }
`;

const PropertyGroup = styled(Card)`
  margin: ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.md};
  
  .ant-card-head {
    background: ${theme.colors.background.tertiary};
    border-bottom: 1px solid ${theme.colors.border.light};
    padding: ${theme.spacing[2]} ${theme.spacing[3]};
    min-height: auto;
  }
  
  .ant-card-body {
    padding: ${theme.spacing[3]};
  }
`;

const PropertyField = styled.div`
  margin-bottom: ${theme.spacing[3]};
  
  .ant-form-item {
    margin-bottom: ${theme.spacing[2]};
  }
  
  .ant-form-item-label {
    padding-bottom: ${theme.spacing[1]};
    
    label {
      font-weight: ${theme.typography.fontWeight.medium};
      color: ${theme.colors.text.primary};
      font-size: ${theme.typography.fontSize.sm};
    }
  }
  
  .property-help {
    margin-top: ${theme.spacing[1]};
    font-size: ${theme.typography.fontSize.xs};
    color: ${theme.colors.text.secondary};
    line-height: ${theme.typography.lineHeight.normal};
  }
`;

const ValidationMessage = styled.div`
  margin-top: ${theme.spacing[1]};
  padding: ${theme.spacing[1]} ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.typography.fontSize.xs};
  
  &.error {
    background: ${theme.colors.error.light};
    color: ${theme.colors.error.dark};
    border: 1px solid ${theme.colors.error.main};
  }
  
  &.warning {
    background: ${theme.colors.warning.light};
    color: ${theme.colors.warning.dark};
    border: 1px solid ${theme.colors.warning.main};
  }
  
  &.success {
    background: ${theme.colors.success.light};
    color: ${theme.colors.success.dark};
    border: 1px solid ${theme.colors.success.main};
  }
`;

const PreviewToggle = styled.div`
  position: absolute;
  top: ${theme.spacing[2]};
  right: ${theme.spacing[2]};
  z-index: 10;
`;

const QuickActions = styled.div`
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  background: ${theme.colors.background.secondary};
  border-top: 1px solid ${theme.colors.border.light};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

// Property validation rules
const VALIDATION_RULES = {
  required: (value) => value !== undefined && value !== null && value !== '',
  minLength: (min) => (value) => !value || value.length >= min,
  maxLength: (max) => (value) => !value || value.length <= max,
  pattern: (regex) => (value) => !value || regex.test(value),
  range: (min, max) => (value) => !value || (value >= min && value <= max),
  url: (value) => !value || /^https?:\/\/.+/.test(value),
  color: (value) => !value || /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value),
};

// Enhanced property schemas with usage frequency and better organization
const PROPERTY_SCHEMAS = {
  text: {
    content: {
      type: 'textarea',
      label: 'Text Content',
      placeholder: 'Enter your text here...',
      help: 'The main text content to display. Supports basic formatting.',
      validation: [VALIDATION_RULES.required],
      group: 'essential',
      priority: 1,
      usage: 'high'
    },
    type: {
      type: 'select',
      label: 'Text Style',
      options: [
        { value: 'paragraph', label: 'Paragraph', description: 'Regular body text' },
        { value: 'title', label: 'Title', description: 'Large heading text' },
        { value: 'secondary', label: 'Secondary', description: 'Muted text' },
        { value: 'warning', label: 'Warning', description: 'Warning text' },
        { value: 'danger', label: 'Danger', description: 'Error or danger text' }
      ],
      defaultValue: 'paragraph',
      help: 'Choose the visual style and semantic meaning of the text',
      group: 'essential',
      priority: 2,
      usage: 'high'
    },
    strong: {
      type: 'switch',
      label: 'Bold Text',
      help: 'Make text bold and emphasize importance',
      group: 'typography',
      priority: 3,
      usage: 'medium'
    },
    italic: {
      type: 'switch',
      label: 'Italic',
      help: 'Make text italic/emphasized',
      group: 'typography'
    },
    underline: {
      type: 'switch',
      label: 'Underline',
      help: 'Add underline decoration',
      group: 'typography'
    }
  },
  button: {
    text: {
      type: 'input',
      label: 'Button Text',
      placeholder: 'Enter button text...',
      help: 'The text displayed on the button',
      validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(50)],
      group: 'content'
    },
    type: {
      type: 'select',
      label: 'Button Type',
      options: [
        { value: 'default', label: 'Default' },
        { value: 'primary', label: 'Primary' },
        { value: 'dashed', label: 'Dashed' },
        { value: 'text', label: 'Text' },
        { value: 'link', label: 'Link' }
      ],
      defaultValue: 'default',
      help: 'Visual style of the button',
      group: 'appearance'
    },
    size: {
      type: 'select',
      label: 'Size',
      options: [
        { value: 'small', label: 'Small' },
        { value: 'middle', label: 'Medium' },
        { value: 'large', label: 'Large' }
      ],
      defaultValue: 'middle',
      help: 'Size of the button',
      group: 'layout'
    },
    disabled: {
      type: 'switch',
      label: 'Disabled',
      help: 'Disable button interaction',
      group: 'behavior'
    },
    action: {
      type: 'input',
      label: 'Action',
      placeholder: 'Enter action name...',
      help: 'Action to perform when clicked',
      group: 'behavior'
    }
  },
  header: {
    title: {
      type: 'input',
      label: 'Title',
      placeholder: 'Enter header title...',
      help: 'Main title text',
      validation: [VALIDATION_RULES.required],
      group: 'content'
    },
    subtitle: {
      type: 'input',
      label: 'Subtitle',
      placeholder: 'Enter subtitle...',
      help: 'Optional subtitle text',
      group: 'content'
    },
    alignment: {
      type: 'select',
      label: 'Alignment',
      options: [
        { value: 'left', label: 'Left' },
        { value: 'center', label: 'Center' },
        { value: 'right', label: 'Right' }
      ],
      defaultValue: 'center',
      help: 'Text alignment',
      group: 'layout'
    },
    background: {
      type: 'color',
      label: 'Background Color',
      help: 'Background color of the header',
      group: 'appearance'
    },
    textColor: {
      type: 'color',
      label: 'Text Color',
      help: 'Color of the text',
      group: 'appearance'
    }
  },
  input: {
    placeholder: {
      type: 'input',
      label: 'Placeholder Text',
      placeholder: 'Enter placeholder text...',
      help: 'Text shown when input is empty',
      group: 'content',
      priority: 1,
      usage: 'high'
    },
    value: {
      type: 'input',
      label: 'Default Value',
      placeholder: 'Enter default value...',
      help: 'Initial value of the input',
      group: 'content',
      priority: 2,
      usage: 'medium'
    },
    type: {
      type: 'select',
      label: 'Input Type',
      options: [
        { value: 'text', label: 'Text' },
        { value: 'email', label: 'Email' },
        { value: 'password', label: 'Password' },
        { value: 'number', label: 'Number' },
        { value: 'tel', label: 'Telephone' },
        { value: 'url', label: 'URL' },
        { value: 'search', label: 'Search' }
      ],
      defaultValue: 'text',
      help: 'Type of input field',
      group: 'behavior',
      priority: 3,
      usage: 'high'
    },
    required: {
      type: 'switch',
      label: 'Required',
      help: 'Whether the input is required',
      group: 'behavior',
      priority: 4,
      usage: 'medium'
    },
    disabled: {
      type: 'switch',
      label: 'Disabled',
      help: 'Whether the input is disabled',
      group: 'behavior',
      priority: 5,
      usage: 'low'
    },
    maxLength: {
      type: 'number',
      label: 'Max Length',
      placeholder: 'Maximum characters...',
      help: 'Maximum number of characters allowed',
      validation: [VALIDATION_RULES.positive],
      group: 'behavior',
      priority: 6,
      usage: 'low'
    }
  },
  container: {
    padding: {
      type: 'input',
      label: 'Padding',
      placeholder: 'e.g., 16px, 1rem, 10px 20px',
      help: 'Internal spacing of the container',
      group: 'layout',
      priority: 1,
      usage: 'high'
    },
    margin: {
      type: 'input',
      label: 'Margin',
      placeholder: 'e.g., 0px, 1rem, 10px 20px',
      help: 'External spacing around the container',
      group: 'layout',
      priority: 2,
      usage: 'medium'
    },
    backgroundColor: {
      type: 'color',
      label: 'Background Color',
      help: 'Background color of the container',
      group: 'appearance',
      priority: 3,
      usage: 'high'
    },
    borderRadius: {
      type: 'number',
      label: 'Border Radius (px)',
      placeholder: 'Border radius in pixels...',
      help: 'Roundness of the container corners',
      validation: [VALIDATION_RULES.nonNegative],
      group: 'appearance',
      priority: 4,
      usage: 'medium'
    },
    border: {
      type: 'input',
      label: 'Border',
      placeholder: 'e.g., 1px solid #ccc',
      help: 'Border style of the container',
      group: 'appearance',
      priority: 5,
      usage: 'medium'
    },
    width: {
      type: 'input',
      label: 'Width',
      placeholder: 'e.g., 100%, 300px, auto',
      help: 'Width of the container',
      group: 'layout',
      priority: 6,
      usage: 'medium'
    },
    height: {
      type: 'input',
      label: 'Height',
      placeholder: 'e.g., auto, 200px, 100vh',
      help: 'Height of the container',
      group: 'layout',
      priority: 7,
      usage: 'medium'
    },
    display: {
      type: 'select',
      label: 'Display',
      options: [
        { value: 'block', label: 'Block' },
        { value: 'inline', label: 'Inline' },
        { value: 'inline-block', label: 'Inline Block' },
        { value: 'flex', label: 'Flex' },
        { value: 'grid', label: 'Grid' },
        { value: 'none', label: 'Hidden' }
      ],
      defaultValue: 'block',
      help: 'Display type of the container',
      group: 'layout',
      priority: 8,
      usage: 'low'
    }
  },
  image: {
    src: {
      type: 'input',
      label: 'Image URL',
      placeholder: 'Enter image URL...',
      help: 'URL of the image to display',
      validation: [VALIDATION_RULES.required, VALIDATION_RULES.url],
      group: 'content',
      priority: 1,
      usage: 'high'
    },
    alt: {
      type: 'input',
      label: 'Alt Text',
      placeholder: 'Describe the image...',
      help: 'Alternative text for screen readers',
      validation: [VALIDATION_RULES.required],
      group: 'content',
      priority: 2,
      usage: 'high'
    },
    width: {
      type: 'input',
      label: 'Width',
      placeholder: 'e.g., 100%, 300px, auto',
      help: 'Width of the image',
      group: 'layout',
      priority: 3,
      usage: 'medium'
    },
    height: {
      type: 'input',
      label: 'Height',
      placeholder: 'e.g., auto, 200px',
      help: 'Height of the image',
      group: 'layout',
      priority: 4,
      usage: 'medium'
    },
    objectFit: {
      type: 'select',
      label: 'Object Fit',
      options: [
        { value: 'fill', label: 'Fill' },
        { value: 'contain', label: 'Contain' },
        { value: 'cover', label: 'Cover' },
        { value: 'none', label: 'None' },
        { value: 'scale-down', label: 'Scale Down' }
      ],
      defaultValue: 'cover',
      help: 'How the image should be resized',
      group: 'appearance',
      priority: 5,
      usage: 'low'
    }
  }
};

// Enhanced property groups configuration with priority and usage frequency
const PROPERTY_GROUPS = {
  essential: {
    title: 'Essential',
    icon: <StarOutlined />,
    description: 'Most commonly used properties',
    priority: 1,
    defaultExpanded: true,
    color: '#1890ff'
  },
  content: {
    title: 'Content',
    icon: <FileTextOutlined />,
    description: 'Text, images, and other content properties',
    priority: 2,
    defaultExpanded: true,
    color: '#52c41a'
  },
  appearance: {
    title: 'Appearance',
    icon: <BgColorsOutlined />,
    description: 'Visual styling and colors',
    priority: 3,
    defaultExpanded: false,
    color: '#722ed1'
  },
  layout: {
    title: 'Layout',
    icon: <ExpandOutlined />,
    description: 'Size, spacing, and positioning',
    priority: 4,
    defaultExpanded: false,
    color: '#fa8c16'
  },
  typography: {
    title: 'Typography',
    icon: <FontSizeOutlined />,
    description: 'Font styles and text formatting',
    priority: 5,
    defaultExpanded: false,
    color: '#eb2f96'
  },
  behavior: {
    title: 'Behavior',
    icon: <SettingOutlined />,
    description: 'Interactions and functionality',
    priority: 6,
    defaultExpanded: false,
    color: '#13c2c2'
  },
  advanced: {
    title: 'Advanced',
    icon: <SettingOutlined />,
    description: 'Custom CSS and advanced options',
    priority: 7,
    defaultExpanded: false,
    color: '#8c8c8c'
  }
};

export default function UXEnhancedPropertyEditor({
  component,
  onUpdateComponent,
  onPreviewChange,
  dataSources = [],
  showPreview = true,
  enableRealTimePreview = true
}) {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('properties');

  // Initialize expanded groups based on default configuration
  const defaultExpandedGroups = useMemo(() => {
    return Object.entries(PROPERTY_GROUPS)
      .filter(([_, config]) => config.defaultExpanded)
      .map(([key, _]) => key);
  }, []);

  const [expandedGroups, setExpandedGroups] = useState(defaultExpandedGroups);
  const [validationErrors, setValidationErrors] = useState({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [previewEnabled, setPreviewEnabled] = useState(enableRealTimePreview);
  const [searchTerm, setSearchTerm] = useState('');
  const [showOnlyPopular, setShowOnlyPopular] = useState(false);

  // Get property schema for current component type
  const propertySchema = useMemo(() => {
    return PROPERTY_SCHEMAS[component?.type] || {};
  }, [component?.type]);

  // Enhanced property grouping with search and filtering
  const groupedProperties = useMemo(() => {
    const groups = {};

    Object.entries(propertySchema).forEach(([key, schema]) => {
      // Filter by search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          schema.label?.toLowerCase().includes(searchLower) ||
          schema.help?.toLowerCase().includes(searchLower) ||
          key.toLowerCase().includes(searchLower);

        if (!matchesSearch) return;
      }

      // Filter by popularity if enabled
      if (showOnlyPopular && schema.usage !== 'high') {
        return;
      }

      const groupKey = schema.group || 'advanced';
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push({ key, ...schema });
    });

    // Sort properties within each group by priority
    Object.keys(groups).forEach(groupKey => {
      groups[groupKey].sort((a, b) => (a.priority || 999) - (b.priority || 999));
    });

    // Sort groups by priority
    const sortedGroups = {};
    Object.entries(PROPERTY_GROUPS)
      .sort(([, a], [, b]) => (a.priority || 999) - (b.priority || 999))
      .forEach(([groupKey]) => {
        if (groups[groupKey]) {
          sortedGroups[groupKey] = groups[groupKey];
        }
      });

    return sortedGroups;
  }, [propertySchema, searchTerm, showOnlyPopular]);

  // Validate a single property
  const validateProperty = useCallback((key, value, schema) => {
    if (!schema.validation) return null;

    for (const rule of schema.validation) {
      if (!rule(value)) {
        return `Invalid ${schema.label.toLowerCase()}`;
      }
    }
    return null;
  }, []);

  // Validate all properties
  const validateAllProperties = useCallback(() => {
    const errors = {};
    const values = form.getFieldsValue();

    Object.entries(propertySchema).forEach(([key, schema]) => {
      const error = validateProperty(key, values[key], schema);
      if (error) {
        errors[key] = error;
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [form, propertySchema, validateProperty]);

  // Handle form value changes
  const handleValuesChange = useCallback((changedValues, allValues) => {
    setHasUnsavedChanges(true);

    // Validate changed values
    const errors = { ...validationErrors };
    Object.entries(changedValues).forEach(([key, value]) => {
      const schema = propertySchema[key];
      if (schema) {
        const error = validateProperty(key, value, schema);
        if (error) {
          errors[key] = error;
        } else {
          delete errors[key];
        }
      }
    });
    setValidationErrors(errors);

    // Real-time preview update
    if (previewEnabled && onUpdateComponent) {
      onUpdateComponent(component.id, changedValues);
    }

    // Notify preview change
    if (onPreviewChange) {
      onPreviewChange(allValues);
    }
  }, [component?.id, onUpdateComponent, onPreviewChange, previewEnabled, validationErrors, propertySchema, validateProperty]);

  // Save changes
  const handleSave = useCallback(() => {
    if (validateAllProperties()) {
      const values = form.getFieldsValue();
      onUpdateComponent(component.id, values);
      setHasUnsavedChanges(false);
      message.success('Properties saved successfully');
    } else {
      message.error('Please fix validation errors before saving');
    }
  }, [component?.id, form, onUpdateComponent, validateAllProperties]);

  // Reset form
  const handleReset = useCallback(() => {
    if (!component) {
      message.warning('No component selected to reset');
      return;
    }

    try {
      // Get the original component properties
      const originalProps = component.props || {};

      // Reset form to original values
      form.setFieldsValue(originalProps);

      // Clear validation errors
      setValidationErrors({});

      // Mark as no unsaved changes
      setHasUnsavedChanges(false);

      // Notify parent component of reset if needed
      if (onUpdateComponent && previewEnabled) {
        onUpdateComponent(component.id, originalProps);
      }

      message.success('Properties reset to original values');
    } catch (error) {
      console.error('Error resetting properties:', error);
      message.error('Failed to reset properties');
    }
  }, [component, form, onUpdateComponent, previewEnabled]);

  // Set initial form values
  useEffect(() => {
    if (component?.props) {
      form.setFieldsValue(component.props);
      setHasUnsavedChanges(false);
      setValidationErrors({});
    }
  }, [component, form]);

  // Render property field based on type
  const renderPropertyField = useCallback((property) => {
    const { key, type, label, placeholder, help, options, defaultValue, validation, usage, priority } = property;
    const hasError = validationErrors[key];
    const isRequired = validation?.some(rule => rule === VALIDATION_RULES.required);

    const fieldProps = {
      name: key,
      label: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            {usage === 'high' && (
              <StarOutlined style={{ color: '#faad14', fontSize: '10px' }} />
            )}
            <span style={{
              fontWeight: usage === 'high' ? theme.typography.fontWeight.semibold : theme.typography.fontWeight.medium
            }}>
              {label}
            </span>
            {isRequired && <Text type="danger">*</Text>}
            {help && (
              <Tooltip title={help} placement="topLeft">
                <QuestionCircleOutlined style={{
                  color: theme.colors.text.tertiary,
                  cursor: 'help'
                }} />
              </Tooltip>
            )}
          </Space>
          {priority <= 3 && (
            <Tooltip title="Essential property">
              <div style={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                backgroundColor: '#1890ff',
                opacity: 0.6
              }} />
            </Tooltip>
          )}
        </div>
      ),
      validateStatus: hasError ? 'error' : '',
      help: hasError ? (
        <ValidationMessage className="error">
          <ExclamationCircleOutlined /> {hasError}
        </ValidationMessage>
      ) : help ? (
        <div className="property-help">{help}</div>
      ) : null
    };

    switch (type) {
      case 'input':
        return (
          <Form.Item {...fieldProps}>
            <Input
              placeholder={placeholder}
              style={{ borderRadius: theme.borderRadius.md }}
            />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item {...fieldProps}>
            <TextArea
              rows={4}
              placeholder={placeholder}
              style={{ borderRadius: theme.borderRadius.md }}
            />
          </Form.Item>
        );

      case 'select':
        return (
          <Form.Item {...fieldProps}>
            <Select
              placeholder={placeholder}
              style={{ borderRadius: theme.borderRadius.md }}
            >
              {options?.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'switch':
        return (
          <Form.Item {...fieldProps} valuePropName="checked">
            <Switch />
          </Form.Item>
        );

      case 'number':
        return (
          <Form.Item {...fieldProps}>
            <InputNumber
              placeholder={placeholder}
              style={{ width: '100%', borderRadius: theme.borderRadius.md }}
            />
          </Form.Item>
        );

      case 'slider':
        return (
          <Form.Item {...fieldProps}>
            <Slider />
          </Form.Item>
        );

      case 'color':
        return (
          <Form.Item {...fieldProps}>
            <ColorPicker />
          </Form.Item>
        );

      default:
        return (
          <Form.Item {...fieldProps}>
            <Input placeholder={placeholder} />
          </Form.Item>
        );
    }
  }, [validationErrors]);

  if (!component) {
    return (
      <PropertyEditorContainer>
        <div style={{
          padding: theme.spacing[8],
          textAlign: 'center',
          color: theme.colors.text.secondary
        }}>
          <SettingOutlined style={{ fontSize: 48, marginBottom: theme.spacing[4] }} />
          <Title level={4} style={{ color: theme.colors.text.secondary }}>
            No Component Selected
          </Title>
          <Paragraph>
            Select a component from the canvas to edit its properties
          </Paragraph>
        </div>
      </PropertyEditorContainer>
    );
  }

  return (
    <PropertyEditorContainer>
      <PropertyHeader>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={5} style={{ margin: 0, color: 'white' }}>
              Properties
            </Title>
            <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              {component.type} component
            </Text>
          </div>

          {showPreview && (
            <PreviewToggle>
              <Tooltip title={previewEnabled ? 'Disable real-time preview' : 'Enable real-time preview'}>
                <Switch
                  checked={previewEnabled}
                  onChange={setPreviewEnabled}
                  checkedChildren={<EyeOutlined />}
                  unCheckedChildren={<EyeOutlined />}
                  size="small"
                />
              </Tooltip>
            </PreviewToggle>
          )}
        </div>

        {/* Enhanced search and filter controls */}
        <div style={{
          display: 'flex',
          gap: theme.spacing[2],
          alignItems: 'center',
          marginTop: theme.spacing[3],
          padding: `${theme.spacing[2]} 0`,
          borderTop: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <Input
            placeholder="Search properties..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            prefix={<InfoCircleOutlined style={{ color: 'rgba(255, 255, 255, 0.6)' }} />}
            allowClear
            size="small"
            style={{
              flex: 1,
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'white'
            }}
          />

          <Tooltip title="Show only frequently used properties">
            <Switch
              checked={showOnlyPopular}
              onChange={setShowOnlyPopular}
              size="small"
              checkedChildren={<StarOutlined />}
              unCheckedChildren={<StarOutlined />}
            />
          </Tooltip>
        </div>

        {hasUnsavedChanges && (
          <Alert
            message="You have unsaved changes"
            type="warning"
            showIcon
            style={{
              marginTop: theme.spacing[2],
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'white'
            }}
          />
        )}
      </PropertyHeader>

      <PropertyContent>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          style={{ padding: theme.spacing[2] }}
        >
          <TabPane tab="Properties" key="properties">
            <Form
              form={form}
              layout="vertical"
              onValuesChange={handleValuesChange}
              initialValues={component.props}
            >
              <Collapse
                activeKey={expandedGroups}
                onChange={setExpandedGroups}
                ghost
              >
                {Object.entries(groupedProperties).map(([groupKey, properties]) => {
                  const groupConfig = PROPERTY_GROUPS[groupKey];
                  if (!groupConfig || properties.length === 0) return null;

                  return (
                    <Panel
                      header={
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Space>
                            <div
                              style={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                backgroundColor: groupConfig.color,
                                marginRight: theme.spacing[1]
                              }}
                            />
                            {groupConfig.icon}
                            <span style={{ fontWeight: theme.typography.fontWeight.semibold }}>
                              {groupConfig.title}
                            </span>
                            {groupKey === 'essential' && (
                              <StarOutlined style={{ color: '#faad14', fontSize: '12px' }} />
                            )}
                          </Space>
                          <Space>
                            <Badge
                              count={properties.length}
                              size="small"
                              style={{ backgroundColor: groupConfig.color }}
                            />
                            {properties.some(p => p.usage === 'high') && (
                              <Tooltip title="Contains frequently used properties">
                                <StarOutlined style={{ color: '#faad14', fontSize: '10px' }} />
                              </Tooltip>
                            )}
                          </Space>
                        </div>
                      }
                      key={groupKey}
                    >
                      <div style={{ padding: theme.spacing[2] }}>
                        <Text type="secondary" style={{ fontSize: theme.typography.fontSize.xs }}>
                          {groupConfig.description}
                        </Text>
                        <Divider style={{ margin: `${theme.spacing[2]} 0` }} />

                        {properties.map(property => (
                          <PropertyField key={property.key}>
                            {renderPropertyField(property)}
                          </PropertyField>
                        ))}
                      </div>
                    </Panel>
                  );
                })}
              </Collapse>
            </Form>
          </TabPane>

          <TabPane tab="Style" key="style">
            <div style={{ padding: theme.spacing[2] }}>
              <Alert
                message="Style Editor"
                description="Advanced styling options will be available in the next update."
                type="info"
                showIcon
              />
            </div>
          </TabPane>

          <TabPane tab="Advanced" key="advanced">
            <div style={{ padding: theme.spacing[2] }}>
              <Alert
                message="Advanced Options"
                description="Custom CSS and JavaScript options will be available in the next update."
                type="info"
                showIcon
              />
            </div>
          </TabPane>
        </Tabs>
      </PropertyContent>

      <QuickActions>
        <Space>
          <Button
            icon={<UndoOutlined />}
            onClick={handleReset}
            disabled={!hasUnsavedChanges}
            size="small"
          >
            Reset
          </Button>

          <Button
            icon={<ReloadOutlined />}
            onClick={() => form.resetFields()}
            size="small"
          >
            Refresh
          </Button>
        </Space>

        <Space>
          <Text style={{ fontSize: theme.typography.fontSize.xs, color: theme.colors.text.secondary }}>
            {Object.keys(validationErrors).length > 0 && (
              <span style={{ color: theme.colors.error.main }}>
                {Object.keys(validationErrors).length} error(s)
              </span>
            )}
            {Object.keys(validationErrors).length === 0 && hasUnsavedChanges && (
              <span style={{ color: theme.colors.success.main }}>
                <CheckCircleOutlined /> Ready to save
              </span>
            )}
          </Text>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={Object.keys(validationErrors).length > 0 || !hasUnsavedChanges}
            size="small"
          >
            Save
          </Button>
        </Space>
      </QuickActions>
    </PropertyEditorContainer>
  );
}
