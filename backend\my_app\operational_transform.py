"""
Operational Transformation system for real-time collaborative editing
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class OperationType(Enum):
    """Types of operations that can be performed"""
    COMPONENT_ADD = "component_add"
    COMPONENT_UPDATE = "component_update"
    COMPONENT_DELETE = "component_delete"
    COMPONENT_MOVE = "component_move"
    LAYOUT_UPDATE = "layout_update"
    STYLE_UPDATE = "style_update"
    DATA_UPDATE = "data_update"


@dataclass
class Operation:
    """Represents a single operation in the collaborative editing system"""
    id: str
    user_id: int
    operation_type: OperationType
    target_id: str
    data: Dict[str, Any]
    vector_clock: Dict[str, int]
    dependencies: List[str]
    timestamp: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert operation to dictionary for serialization"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'operation_type': self.operation_type.value,
            'target_id': self.target_id,
            'data': self.data,
            'vector_clock': self.vector_clock,
            'dependencies': self.dependencies,
            'timestamp': self.timestamp,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Operation':
        """Create operation from dictionary"""
        return cls(
            id=data['id'],
            user_id=data['user_id'],
            operation_type=OperationType(data['operation_type']),
            target_id=data['target_id'],
            data=data['data'],
            vector_clock=data['vector_clock'],
            dependencies=data['dependencies'],
            timestamp=data['timestamp'],
        )


class OperationalTransform:
    """
    Operational Transformation engine for handling concurrent operations
    """
    
    def __init__(self):
        self.operations_log: List[Operation] = []
        self.vector_clocks: Dict[int, Dict[str, int]] = {}
    
    def transform_operations(self, op1: Operation, op2: Operation) -> Tuple[Operation, Operation]:
        """
        Transform two concurrent operations to maintain consistency
        
        Args:
            op1: First operation
            op2: Second operation
            
        Returns:
            Tuple of transformed operations (op1', op2')
        """
        # If operations are on different targets, no transformation needed
        if op1.target_id != op2.target_id:
            return op1, op2
        
        # Transform based on operation types
        if op1.operation_type == OperationType.COMPONENT_MOVE and op2.operation_type == OperationType.COMPONENT_MOVE:
            return self._transform_move_move(op1, op2)
        elif op1.operation_type == OperationType.COMPONENT_UPDATE and op2.operation_type == OperationType.COMPONENT_UPDATE:
            return self._transform_update_update(op1, op2)
        elif op1.operation_type == OperationType.COMPONENT_DELETE and op2.operation_type == OperationType.COMPONENT_UPDATE:
            return self._transform_delete_update(op1, op2)
        elif op1.operation_type == OperationType.COMPONENT_UPDATE and op2.operation_type == OperationType.COMPONENT_DELETE:
            op2_prime, op1_prime = self._transform_delete_update(op2, op1)
            return op1_prime, op2_prime
        elif op1.operation_type == OperationType.COMPONENT_DELETE and op2.operation_type == OperationType.COMPONENT_DELETE:
            return self._transform_delete_delete(op1, op2)
        else:
            # Default: no transformation for other combinations
            return op1, op2
    
    def _transform_move_move(self, op1: Operation, op2: Operation) -> Tuple[Operation, Operation]:
        """Transform two concurrent move operations"""
        # For move operations, the later operation (by timestamp) takes precedence
        if op1.timestamp > op2.timestamp:
            # op1 is later, so op2 becomes a no-op
            op2_prime = Operation(
                id=op2.id,
                user_id=op2.user_id,
                operation_type=OperationType.COMPONENT_UPDATE,
                target_id=op2.target_id,
                data={'no_op': True},
                vector_clock=op2.vector_clock,
                dependencies=op2.dependencies,
                timestamp=op2.timestamp
            )
            return op1, op2_prime
        else:
            # op2 is later, so op1 becomes a no-op
            op1_prime = Operation(
                id=op1.id,
                user_id=op1.user_id,
                operation_type=OperationType.COMPONENT_UPDATE,
                target_id=op1.target_id,
                data={'no_op': True},
                vector_clock=op1.vector_clock,
                dependencies=op1.dependencies,
                timestamp=op1.timestamp
            )
            return op1_prime, op2
    
    def _transform_update_update(self, op1: Operation, op2: Operation) -> Tuple[Operation, Operation]:
        """Transform two concurrent update operations"""
        # Merge the updates, with op2 taking precedence for conflicts
        merged_data = {**op1.data, **op2.data}
        
        # Create transformed operations with merged data
        op1_prime = Operation(
            id=op1.id,
            user_id=op1.user_id,
            operation_type=op1.operation_type,
            target_id=op1.target_id,
            data=merged_data,
            vector_clock=op1.vector_clock,
            dependencies=op1.dependencies + [op2.id],
            timestamp=op1.timestamp
        )
        
        op2_prime = Operation(
            id=op2.id,
            user_id=op2.user_id,
            operation_type=op2.operation_type,
            target_id=op2.target_id,
            data=merged_data,
            vector_clock=op2.vector_clock,
            dependencies=op2.dependencies + [op1.id],
            timestamp=op2.timestamp
        )
        
        return op1_prime, op2_prime
    
    def _transform_delete_update(self, delete_op: Operation, update_op: Operation) -> Tuple[Operation, Operation]:
        """Transform delete operation against update operation"""
        # Delete takes precedence - update becomes no-op
        update_op_prime = Operation(
            id=update_op.id,
            user_id=update_op.user_id,
            operation_type=OperationType.COMPONENT_UPDATE,
            target_id=update_op.target_id,
            data={'no_op': True, 'reason': 'component_deleted'},
            vector_clock=update_op.vector_clock,
            dependencies=update_op.dependencies + [delete_op.id],
            timestamp=update_op.timestamp
        )
        
        return delete_op, update_op_prime
    
    def _transform_delete_delete(self, op1: Operation, op2: Operation) -> Tuple[Operation, Operation]:
        """Transform two concurrent delete operations"""
        # First delete succeeds, second becomes no-op
        if op1.timestamp <= op2.timestamp:
            op2_prime = Operation(
                id=op2.id,
                user_id=op2.user_id,
                operation_type=OperationType.COMPONENT_UPDATE,
                target_id=op2.target_id,
                data={'no_op': True, 'reason': 'already_deleted'},
                vector_clock=op2.vector_clock,
                dependencies=op2.dependencies + [op1.id],
                timestamp=op2.timestamp
            )
            return op1, op2_prime
        else:
            op1_prime = Operation(
                id=op1.id,
                user_id=op1.user_id,
                operation_type=OperationType.COMPONENT_UPDATE,
                target_id=op1.target_id,
                data={'no_op': True, 'reason': 'already_deleted'},
                vector_clock=op1.vector_clock,
                dependencies=op1.dependencies + [op2.id],
                timestamp=op1.timestamp
            )
            return op1_prime, op2
    
    def apply_operation(self, operation: Operation, app_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply an operation to the application state
        
        Args:
            operation: The operation to apply
            app_state: Current application state
            
        Returns:
            Updated application state
        """
        if operation.data.get('no_op'):
            logger.info(f"Skipping no-op operation {operation.id}: {operation.data.get('reason', 'unknown')}")
            return app_state
        
        new_state = json.loads(json.dumps(app_state))  # Deep copy
        
        try:
            if operation.operation_type == OperationType.COMPONENT_ADD:
                self._apply_component_add(operation, new_state)
            elif operation.operation_type == OperationType.COMPONENT_UPDATE:
                self._apply_component_update(operation, new_state)
            elif operation.operation_type == OperationType.COMPONENT_DELETE:
                self._apply_component_delete(operation, new_state)
            elif operation.operation_type == OperationType.COMPONENT_MOVE:
                self._apply_component_move(operation, new_state)
            elif operation.operation_type == OperationType.LAYOUT_UPDATE:
                self._apply_layout_update(operation, new_state)
            elif operation.operation_type == OperationType.STYLE_UPDATE:
                self._apply_style_update(operation, new_state)
            elif operation.operation_type == OperationType.DATA_UPDATE:
                self._apply_data_update(operation, new_state)
            
            # Add operation to log
            self.operations_log.append(operation)
            
        except Exception as e:
            logger.error(f"Error applying operation {operation.id}: {str(e)}")
            return app_state
        
        return new_state
    
    def _apply_component_add(self, operation: Operation, state: Dict[str, Any]):
        """Apply component add operation"""
        components = state.setdefault('components', [])
        component_data = operation.data.get('component', {})
        component_data['id'] = operation.target_id
        components.append(component_data)
    
    def _apply_component_update(self, operation: Operation, state: Dict[str, Any]):
        """Apply component update operation"""
        components = state.get('components', [])
        for i, component in enumerate(components):
            if component.get('id') == operation.target_id:
                # Merge the update data
                components[i] = {**component, **operation.data.get('updates', {})}
                break
    
    def _apply_component_delete(self, operation: Operation, state: Dict[str, Any]):
        """Apply component delete operation"""
        components = state.get('components', [])
        state['components'] = [c for c in components if c.get('id') != operation.target_id]
    
    def _apply_component_move(self, operation: Operation, state: Dict[str, Any]):
        """Apply component move operation"""
        components = state.get('components', [])
        for component in components:
            if component.get('id') == operation.target_id:
                # Update position
                position = operation.data.get('position', {})
                component.update(position)
                break
    
    def _apply_layout_update(self, operation: Operation, state: Dict[str, Any]):
        """Apply layout update operation"""
        layouts = state.setdefault('layouts', {})
        layout_id = operation.target_id
        layout_updates = operation.data.get('updates', {})
        
        if layout_id in layouts:
            layouts[layout_id].update(layout_updates)
        else:
            layouts[layout_id] = layout_updates
    
    def _apply_style_update(self, operation: Operation, state: Dict[str, Any]):
        """Apply style update operation"""
        styles = state.setdefault('styles', {})
        style_id = operation.target_id
        style_updates = operation.data.get('updates', {})
        
        if style_id in styles:
            styles[style_id].update(style_updates)
        else:
            styles[style_id] = style_updates
    
    def _apply_data_update(self, operation: Operation, state: Dict[str, Any]):
        """Apply data update operation"""
        data = state.setdefault('data', {})
        data_id = operation.target_id
        data_updates = operation.data.get('updates', {})
        
        if data_id in data:
            data[data_id].update(data_updates)
        else:
            data[data_id] = data_updates
    
    def get_operations_since(self, timestamp: float) -> List[Operation]:
        """Get all operations since a given timestamp"""
        return [op for op in self.operations_log if op.timestamp > timestamp]
    
    def update_vector_clock(self, user_id: int, operation_id: str):
        """Update vector clock for a user"""
        if user_id not in self.vector_clocks:
            self.vector_clocks[user_id] = {}
        
        self.vector_clocks[user_id][operation_id] = len(self.operations_log)


# Global instance for the operational transform engine
ot_engine = OperationalTransform()
