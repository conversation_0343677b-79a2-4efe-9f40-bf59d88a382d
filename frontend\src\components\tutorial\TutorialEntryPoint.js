/**
 * Tutorial Entry Point Component
 * 
 * A prominent, accessible entry point for new users to start the visual onboarding tutorial.
 * This component appears in the main interface and provides multiple ways to access tutorials.
 */

import React, { useState, useEffect } from 'react';
import { Button, Card, Typography, Space, Badge, Tooltip, Alert, Modal } from 'antd';
import {
  RocketOutlined,
  PlayCircleOutlined,
  BookOutlined,
  StarOutlined,
  CloseOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import { isNewUser, markOnboardingCompleted } from './VisualOnboardingHelpers';

const { Title, Text, Paragraph } = Typography;

// Styled Components
const EntryPointContainer = styled.div`
  position: relative;
  margin: 16px 0;
`;

const WelcomeCard = styled(Card)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
  
  .ant-card-body {
    padding: 24px;
  }
  
  .ant-typography {
    color: white !important;
  }
`;

const QuickStartCard = styled(Card)`
  border: 2px solid #1890ff;
  border-radius: 8px;
  background: #f0f8ff;
  
  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }
`;

const FloatingBadge = styled.div`
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
`;

const TutorialEntryPoint = ({ 
  showWelcomeCard = true,
  showQuickStart = true,
  autoShow = true,
  onDismiss
}) => {
  const { startTutorial, getTutorialProgress } = useTutorial();
  const [showWelcome, setShowWelcome] = useState(false);
  const [showQuickStartModal, setShowQuickStartModal] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  // Check if user is new and should see the welcome
  useEffect(() => {
    if (autoShow && isNewUser() && !dismissed) {
      setShowWelcome(true);
    }
  }, [autoShow, dismissed]);

  // Check tutorial progress
  const onboardingProgress = getTutorialProgress('visual_onboarding');
  const hasStartedOnboarding = onboardingProgress && onboardingProgress.status !== 'not_started';
  const hasCompletedOnboarding = onboardingProgress && onboardingProgress.status === 'completed';

  const handleStartTutorial = () => {
    startTutorial('visual_onboarding');
    setShowWelcome(false);
    setShowQuickStartModal(false);
  };

  const handleDismissWelcome = () => {
    setShowWelcome(false);
    setDismissed(true);
    if (onDismiss) {
      onDismiss();
    }
  };

  const handleShowQuickStart = () => {
    setShowQuickStartModal(true);
  };

  // Don't show if user has completed onboarding and dismissed
  if (hasCompletedOnboarding && dismissed) {
    return null;
  }

  return (
    <EntryPointContainer>
      {/* Welcome Card for New Users */}
      {showWelcome && showWelcomeCard && (
        <WelcomeCard>
          <div style={{ position: 'relative' }}>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleDismissWelcome}
              style={{
                position: 'absolute',
                top: -8,
                right: -8,
                color: 'white',
                border: 'none'
              }}
              size="small"
            />
            
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <RocketOutlined style={{ fontSize: '48px', color: 'white', marginBottom: '16px' }} />
                <Title level={2} style={{ color: 'white', margin: '0 0 8px 0' }}>
                  Welcome to App Builder! 🎉
                </Title>
                <Paragraph style={{ color: 'white', fontSize: '16px', margin: 0 }}>
                  Ready to build amazing applications? Let's start with a quick interactive tour!
                </Paragraph>
              </div>
              
              <div style={{ textAlign: 'center' }}>
                <Space size="large">
                  <Button
                    type="primary"
                    size="large"
                    icon={<PlayCircleOutlined />}
                    onClick={handleStartTutorial}
                    style={{
                      background: 'white',
                      borderColor: 'white',
                      color: '#667eea',
                      fontWeight: 'bold'
                    }}
                  >
                    Start Interactive Tutorial
                  </Button>
                  
                  <Button
                    type="text"
                    size="large"
                    onClick={handleDismissWelcome}
                    style={{ color: 'white', borderColor: 'white' }}
                  >
                    Maybe Later
                  </Button>
                </Space>
              </div>
              
              <div style={{ textAlign: 'center' }}>
                <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '14px' }}>
                  ⏱️ Takes about 8 minutes • 📚 Learn the essentials • 🎯 Interactive guidance
                </Text>
              </div>
            </Space>
          </div>
        </WelcomeCard>
      )}

      {/* Quick Start Card */}
      {showQuickStart && !showWelcome && (
        <QuickStartCard>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Space>
              <div style={{ position: 'relative' }}>
                <QuestionCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                {!hasCompletedOnboarding && (
                  <FloatingBadge>
                    <Badge dot color="#ff4d4f" />
                  </FloatingBadge>
                )}
              </div>
              
              <div>
                <Text strong style={{ color: '#1890ff' }}>
                  {hasStartedOnboarding ? 'Continue Tutorial' : 'New to App Builder?'}
                </Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {hasStartedOnboarding 
                    ? 'Pick up where you left off in the interactive tutorial'
                    : 'Take a quick tour to learn the basics'
                  }
                </Text>
              </div>
            </Space>
            
            <Space>
              <Tooltip title="Browse all tutorials">
                <Button
                  type="text"
                  icon={<BookOutlined />}
                  onClick={handleShowQuickStart}
                  size="small"
                />
              </Tooltip>
              
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartTutorial}
                size="small"
              >
                {hasStartedOnboarding ? 'Continue' : 'Start Tour'}
              </Button>
            </Space>
          </div>
        </QuickStartCard>
      )}

      {/* Quick Start Modal */}
      <Modal
        title={
          <Space>
            <BookOutlined />
            Tutorial Options
          </Space>
        }
        open={showQuickStartModal}
        onCancel={() => setShowQuickStartModal(false)}
        footer={null}
        width={500}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Alert
            message="Recommended for new users"
            description="Start with the visual onboarding tutorial to learn the App Builder basics."
            type="info"
            showIcon
          />
          
          <Card>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  <Title level={4} style={{ margin: 0 }}>
                    <RocketOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
                    Visual Onboarding Tutorial
                  </Title>
                  <Text type="secondary">Interactive guide through the main interface</Text>
                </div>
                <Badge count={hasCompletedOnboarding ? '✓' : 'NEW'} 
                       color={hasCompletedOnboarding ? '#52c41a' : '#ff4d4f'} />
              </div>
              
              <Paragraph>
                Learn how to use the Component Palette, Preview Area, and Property Editor 
                through hands-on interactive steps. Perfect for getting started!
              </Paragraph>
              
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text type="secondary">
                  ⏱️ ~8 minutes • 🎯 Interactive • ⭐ Beginner friendly
                </Text>
                
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleStartTutorial}
                >
                  {hasStartedOnboarding ? 'Continue' : 'Start Tutorial'}
                </Button>
              </div>
            </Space>
          </Card>
          
          <div style={{ textAlign: 'center' }}>
            <Text type="secondary">
              More tutorials available in the help menu
            </Text>
          </div>
        </Space>
      </Modal>
    </EntryPointContainer>
  );
};

export default TutorialEntryPoint;
