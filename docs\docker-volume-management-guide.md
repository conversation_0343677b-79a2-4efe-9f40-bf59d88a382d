# Comprehensive Docker Volume Management Guide for App Builder

This guide provides detailed instructions for managing Docker volumes in the App Builder project, including troubleshooting common issues and best practices.

## Table of Contents

1. [Overview of Docker Volumes](#overview-of-docker-volumes)
2. [Volume Configuration](#volume-configuration)
3. [Volume Management Commands](#volume-management-commands)
4. [Common Volume Management Tasks](#common-volume-management-tasks)
5. [Troubleshooting Volume Issues](#troubleshooting-volume-issues)
6. [Volume Management Scripts](#volume-management-scripts)
7. [Best Practices](#best-practices)
8. [Advanced Volume Operations](#advanced-volume-operations)

## Overview of Docker Volumes

The App Builder project uses Docker volumes to persist data and improve performance across container restarts. Understanding these volumes is crucial for effective development and deployment.

### Project Volumes

#### 1. Frontend Node Modules Volume
- **Name**: `app-builder-201_frontend_node_modules`
- **Purpose**: Stores Node.js dependencies for the React frontend
- **Mount Point**: `/app/node_modules` in the frontend container
- **Benefits**:
  - Dramatically improves build performance by caching dependencies
  - Prevents conflicts between host and container Node.js versions
  - Reduces container startup time after initial dependency installation
  - Avoids permission issues on Windows and macOS

#### 2. PostgreSQL Data Volume
- **Name**: `app-builder-201_postgres_data`
- **Purpose**: Persists database data across container restarts
- **Mount Point**: `/var/lib/postgresql/data` in the database container
- **Benefits**:
  - Ensures database data survives container recreation
  - Maintains data integrity during development
  - Enables database backups and migrations
  - Provides consistent data storage location

## Volume Configuration

The volume configuration is defined in `docker-compose.yml`:

### Frontend Service Volumes
```yaml
volumes:
  - ./frontend:/app:cached          # Bind mount for live code editing
  - frontend_node_modules:/app/node_modules  # Named volume for dependencies
```

### Backend Service Volumes
```yaml
volumes:
  - ./backend:/usr/src/app          # Bind mount for live code editing
  - ./frontend/build:/usr/src/app/frontend/build:ro  # Read-only static files
```

### Database Service Volumes
```yaml
volumes:
  - postgres_data:/var/lib/postgresql/data/  # Named volume for data persistence
```

### Named Volume Declarations
```yaml
volumes:
  postgres_data:
  frontend_node_modules:
```

## Volume Management Commands

### Basic Docker Volume Commands

#### List All Volumes
```bash
# List all Docker volumes
docker volume ls

# List only project-related volumes
docker volume ls --filter name=app-builder-201
```

#### Inspect Volume Details
```bash
# Inspect a specific volume
docker volume inspect app-builder-201_postgres_data
docker volume inspect app-builder-201_frontend_node_modules
```

#### Remove Volumes
```bash
# Remove a specific volume (WARNING: Data will be lost)
docker volume rm app-builder-201_frontend_node_modules

# Remove all unused volumes
docker volume prune
```

### Platform-Specific Commands

#### Windows (PowerShell)
```powershell
# List project volumes
docker volume ls --format "{{.Name}}" | Where-Object { $_ -like "*app-builder-201*" }

# Remove project volumes with confirmation
$volumes = docker volume ls --format "{{.Name}}" | Where-Object { $_ -like "*app-builder-201*" }
foreach ($volume in $volumes) {
    Write-Host "Removing volume: $volume"
    docker volume rm $volume
}
```

#### Linux/macOS (Bash)
```bash
# List project volumes
docker volume ls --format "{{.Name}}" | grep "app-builder-201"

# Remove project volumes
docker volume ls --format "{{.Name}}" | grep "app-builder-201" | xargs docker volume rm
```

## Common Volume Management Tasks

### 1. Listing All Project Volumes

#### Windows (PowerShell)
```powershell
# Using the management script
.\scripts\manage-volumes.ps1 list

# Manual command
docker volume ls --filter name=app-builder-201
```

#### Linux/macOS (Bash)
```bash
# Using the management script
./scripts/manage-volumes.sh list

# Manual command
docker volume ls --filter name=app-builder-201
```

### 2. Cleaning Up Unused Volumes

#### Windows (PowerShell)
```powershell
# Using the management script (with confirmation)
.\scripts\manage-volumes.ps1 clean

# Using the management script (skip confirmation)
.\scripts\manage-volumes.ps1 clean -Force

# Manual cleanup
docker volume prune -f
```

#### Linux/macOS (Bash)
```bash
# Using the management script (with confirmation)
./scripts/manage-volumes.sh clean

# Using the management script (skip confirmation)
./scripts/manage-volumes.sh clean --force

# Manual cleanup
docker volume prune -f
```

### 3. Verifying Volume Mounts

#### Windows (PowerShell)
```powershell
# Using the management script
.\scripts\manage-volumes.ps1 verify

# Manual verification
docker-compose exec frontend ls -la /app
docker-compose exec frontend ls -la /app/node_modules
docker-compose exec backend ls -la /usr/src/app
docker-compose exec db ls -la /var/lib/postgresql/data
```

#### Linux/macOS (Bash)
```bash
# Using the management script
./scripts/manage-volumes.sh verify

# Manual verification
docker-compose exec frontend ls -la /app
docker-compose exec frontend ls -la /app/node_modules
docker-compose exec backend ls -la /usr/src/app
docker-compose exec db ls -la /var/lib/postgresql/data
```

### 4. Recreating Volumes When Needed

#### Windows (PowerShell)
```powershell
# Using the management script (with confirmation)
.\scripts\manage-volumes.ps1 recreate

# Using the management script (skip confirmation)
.\scripts\manage-volumes.ps1 recreate -Force

# Manual recreation
docker-compose down
docker volume rm app-builder-201_frontend_node_modules
docker-compose up -d
```

#### Linux/macOS (Bash)
```bash
# Using the management script (with confirmation)
./scripts/manage-volumes.sh recreate

# Using the management script (skip confirmation)
./scripts/manage-volumes.sh recreate --force

# Manual recreation
docker-compose down
docker volume rm app-builder-201_frontend_node_modules
docker-compose up -d
```

## Troubleshooting Volume Issues

### 1. Node Modules Issues

**Symptoms:**
- Frontend container fails to start with dependency errors
- `npm install` or `yarn install` errors in container logs
- Missing package errors during build
- Webpack compilation failures

**Diagnostic Commands:**
```bash
# Check if node_modules volume exists
docker volume inspect app-builder-201_frontend_node_modules

# Check node_modules content in container
docker-compose exec frontend ls -la /app/node_modules

# Check container logs for dependency errors
docker-compose logs frontend
```

**Solutions:**

1. **Recreate node_modules volume:**
   ```bash
   # Windows
   .\scripts\manage-volumes.ps1 recreate -Force
   
   # Linux/macOS
   ./scripts/manage-volumes.sh recreate --force
   ```

2. **Manual recreation:**
   ```bash
   docker-compose down
   docker volume rm app-builder-201_frontend_node_modules
   docker-compose up -d --build frontend
   ```

3. **Clear npm cache in container:**
   ```bash
   docker-compose exec frontend npm cache clean --force
   docker-compose restart frontend
   ```

### 2. Database Volume Issues

**Symptoms:**
- Database connection errors
- Missing tables or data after container restart
- PostgreSQL permission errors in logs
- Database initialization failures

**Diagnostic Commands:**
```bash
# Check if postgres_data volume exists
docker volume inspect app-builder-201_postgres_data

# Check database data directory
docker-compose exec db ls -la /var/lib/postgresql/data

# Check database logs
docker-compose logs db

# Test database connection
docker-compose exec db psql -U myappuser -d myapp -c "\dt"
```

**Solutions:**

1. **Check volume permissions:**
   ```bash
   docker-compose exec db ls -la /var/lib/postgresql/data
   docker-compose exec db chown -R postgres:postgres /var/lib/postgresql/data
   ```

2. **Recreate database volume (WARNING: Data loss):**
   ```bash
   docker-compose down
   docker volume rm app-builder-201_postgres_data
   docker-compose up -d db
   # Re-run database migrations
   docker-compose exec backend python manage.py migrate
   ```

3. **Backup and restore database:**
   ```bash
   # Backup before recreating
   docker-compose exec db pg_dump -U myappuser myapp > backup.sql
   
   # After recreation, restore
   docker-compose exec -T db psql -U myappuser myapp < backup.sql
   ```

### 3. Volume Mount Path Issues

**Symptoms:**
- Changes to local files not reflected in containers
- Containers using outdated code
- File not found errors for recently added files
- Build artifacts not appearing in expected locations

**Diagnostic Commands:**
```bash
# Inspect container mounts
docker inspect app-builder-201-frontend-1 --format='{{range .Mounts}}{{.Source}} -> {{.Destination}}{{println}}{{end}}'
docker inspect app-builder-201-backend-1 --format='{{range .Mounts}}{{.Source}} -> {{.Destination}}{{println}}{{end}}'

# Check if files are accessible in container
docker-compose exec frontend ls -la /app
docker-compose exec backend ls -la /usr/src/app
```

**Solutions:**

1. **Restart containers:**
   ```bash
   docker-compose restart
   ```

2. **Rebuild containers:**
   ```bash
   docker-compose down
   docker-compose up -d --build
   ```

3. **Check docker-compose.yml syntax:**
   ```bash
   docker-compose config
   ```

4. **Verify working directory:**
   ```bash
   # Ensure you're in the project root
   pwd
   ls -la docker-compose.yml
   ```

## Volume Management Scripts

The project includes dedicated scripts for volume management that provide a user-friendly interface for common operations.

### PowerShell Script (Windows)

**Location:** `scripts/manage-volumes.ps1`

**Usage:**
```powershell
.\scripts\manage-volumes.ps1 [Action] [-Force]
```

**Available Actions:**
- `list` - List all Docker volumes related to this project
- `clean` - Remove unused Docker volumes
- `verify` - Verify volume mounts are working correctly
- `recreate` - Recreate specific volumes (e.g., node_modules)
- `help` - Show help message

**Examples:**
```powershell
# List project volumes with details
.\scripts\manage-volumes.ps1 list

# Clean unused volumes with confirmation
.\scripts\manage-volumes.ps1 clean

# Verify all volume mounts
.\scripts\manage-volumes.ps1 verify

# Recreate node_modules volume without confirmation
.\scripts\manage-volumes.ps1 recreate -Force
```

### Bash Script (Linux/macOS)

**Location:** `scripts/manage-volumes.sh`

**Usage:**
```bash
./scripts/manage-volumes.sh [action] [--force]
```

**Available Actions:**
- `list` - List all Docker volumes related to this project
- `clean` - Remove unused Docker volumes
- `verify` - Verify volume mounts are working correctly
- `recreate` - Recreate specific volumes (e.g., node_modules)
- `help` - Show help message

**Examples:**
```bash
# List project volumes with details
./scripts/manage-volumes.sh list

# Clean unused volumes with confirmation
./scripts/manage-volumes.sh clean

# Verify all volume mounts
./scripts/manage-volumes.sh verify

# Recreate node_modules volume without confirmation
./scripts/manage-volumes.sh recreate --force
```

### Script Features

Both scripts provide:

1. **Colored Output**: Easy-to-read colored terminal output
2. **Safety Confirmations**: Prompts before destructive operations
3. **Force Mode**: Skip confirmations with `--force` flag
4. **Detailed Inspection**: Volume details and mount verification
5. **Error Handling**: Graceful handling of common error conditions
6. **Container Management**: Automatic container start/stop as needed

## Best Practices

### 1. Volume Naming and Organization

- **Use consistent naming**: All project volumes follow the pattern `app-builder-201_<purpose>`
- **Document volume purposes**: Clearly document what each volume stores
- **Avoid manual volume creation**: Let Docker Compose manage volume creation

### 2. Development Workflow

- **Never modify named volumes directly**: Use containers to modify volume contents
- **Use bind mounts for active development**: Source code should use bind mounts for live editing
- **Separate data and code volumes**: Keep application data separate from source code

### 3. Volume Maintenance

- **Regular cleanup**: Periodically remove unused volumes with `docker volume prune`
- **Monitor volume sizes**: Large volumes can consume significant disk space
- **Backup important data**: Always backup database volumes before major changes

### 4. Performance Optimization

- **Use cached bind mounts**: Add `:cached` to bind mounts on macOS for better performance
- **Leverage named volumes for dependencies**: Use named volumes for `node_modules` and similar directories
- **Avoid nested bind mounts**: Don't bind mount directories that contain named volume mount points

### 5. Security Considerations

- **Limit volume access**: Only mount necessary directories
- **Use read-only mounts**: Add `:ro` flag for read-only access where appropriate
- **Avoid mounting sensitive directories**: Never mount system directories or sensitive files

### 6. Troubleshooting Workflow

1. **Check container status**: Ensure containers are running
2. **Verify volume existence**: Confirm volumes exist and are properly named
3. **Inspect volume mounts**: Check that mounts are configured correctly
4. **Test volume access**: Verify containers can read/write to volumes
5. **Check logs**: Review container logs for volume-related errors
6. **Use management scripts**: Leverage provided scripts for common operations

## Advanced Volume Operations

### Volume Backup and Restore

#### Backup Database Volume
```bash
# Create a backup container to access the volume
docker run --rm -v app-builder-201_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .
```

#### Restore Database Volume
```bash
# Restore from backup
docker run --rm -v app-builder-201_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_backup.tar.gz -C /data
```

#### Backup Node Modules Volume
```bash
# Backup node_modules (useful for offline development)
docker run --rm -v app-builder-201_frontend_node_modules:/data -v $(pwd):/backup alpine tar czf /backup/node_modules_backup.tar.gz -C /data .
```

### Volume Migration

#### Copy Volume Between Environments
```bash
# Export volume to tar file
docker run --rm -v source_volume:/from -v $(pwd):/backup alpine tar czf /backup/volume_backup.tar.gz -C /from .

# Import volume from tar file
docker run --rm -v target_volume:/to -v $(pwd):/backup alpine tar xzf /backup/volume_backup.tar.gz -C /to
```

### Volume Performance Monitoring

#### Check Volume Disk Usage
```bash
# Check volume sizes
docker system df -v

# Detailed volume information
docker volume ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"
```

#### Monitor Volume I/O
```bash
# Monitor container I/O
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.BlockIO}}"
```

### Network Diagnostics Integration

The project includes a comprehensive network diagnostics script that also checks volume mounts:

```powershell
# Windows
.\scripts\diagnose-network.ps1

# This script checks:
# - Network connectivity
# - Docker volume existence
# - Volume mount verification
# - Container status
```

This script provides a holistic view of both network and volume health, making it easier to diagnose complex issues that involve both networking and storage.

---

## Quick Reference

### Emergency Volume Recovery

If you encounter critical volume issues:

1. **Stop all containers**: `docker-compose down`
2. **List volumes**: `docker volume ls`
3. **Backup important data**: Use backup commands above
4. **Remove problematic volumes**: `docker volume rm <volume_name>`
5. **Restart services**: `docker-compose up -d`
6. **Verify functionality**: Use management scripts to verify

### Common Command Shortcuts

```bash
# Quick volume list
docker volume ls --filter name=app-builder-201

# Quick cleanup
docker volume prune -f

# Quick verification
docker-compose exec frontend ls /app/node_modules
docker-compose exec db ls /var/lib/postgresql/data

# Quick recreation
docker-compose down && docker volume rm app-builder-201_frontend_node_modules && docker-compose up -d
```

This comprehensive guide should help you effectively manage Docker volumes in the App Builder project, troubleshoot common issues, and maintain optimal performance.
