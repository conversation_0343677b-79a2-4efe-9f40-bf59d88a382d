import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { 
  Button, 
  Tooltip, 
  Dropdown, 
  Menu, 
  Badge, 
  Space, 
  Typography,
  Divider,
  Modal,
  List,
  Tag
} from 'antd';
import { 
  UndoOutlined, 
  RedoOutlined, 
  HistoryOutlined,
  ClockCircleOutlined,
  RobotOutlined,
  ClearOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import useAIUndoRedo from '../../hooks/useAIUndoRedo';

const { Text } = Typography;

/**
 * AI Undo/Redo Controls Component
 * Provides undo/redo functionality specifically for AI-applied changes
 */
const AIUndoRedoControls = ({
  size = 'default',
  showHistory = true,
  showClear = true,
  compact = false,
  style = {},
  className = ''
}) => {
  const [historyVisible, setHistoryVisible] = useState(false);
  
  const {
    undo,
    redo,
    clearHistory,
    jumpToState,
    history,
    currentIndex,
    isUndoing,
    isRedoing,
    canUndo,
    canRedo,
    historySize,
    currentPosition,
    getUndoDescription,
    getRedoDescription,
    getHistorySummary
  } = useAIUndoRedo();

  // Handle undo with confirmation for important changes
  const handleUndo = () => {
    const success = undo();
    if (!success) {
      console.warn('Cannot undo: no AI actions to undo');
    }
  };

  // Handle redo
  const handleRedo = () => {
    const success = redo();
    if (!success) {
      console.warn('Cannot redo: no AI actions to redo');
    }
  };

  // Handle clear history with confirmation
  const handleClearHistory = () => {
    Modal.confirm({
      title: 'Clear AI History',
      content: 'Are you sure you want to clear all AI action history? This cannot be undone.',
      okText: 'Clear',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: clearHistory,
      icon: <ClearOutlined />
    });
  };

  // Handle jump to specific state
  const handleJumpToState = (index) => {
    const success = jumpToState(index);
    if (success) {
      setHistoryVisible(false);
    }
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  // Get action type icon
  const getActionIcon = (action) => {
    switch (action.type) {
      case 'APPLY_AI_LAYOUT':
        return <HistoryOutlined style={{ color: '#1890ff' }} />;
      case 'APPLY_AI_COMBINATION':
        return <RobotOutlined style={{ color: '#52c41a' }} />;
      case 'BULK_AI_CHANGES':
        return <InfoCircleOutlined style={{ color: '#722ed1' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#999' }} />;
    }
  };

  // Render history dropdown menu
  const renderHistoryMenu = () => (
    <Menu style={{ maxHeight: '300px', overflowY: 'auto', width: '300px' }}>
      <Menu.Item key="header" disabled>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text strong>AI Action History</Text>
          <Badge count={historySize} style={{ backgroundColor: '#1890ff' }} />
        </div>
      </Menu.Item>
      <Menu.Divider />
      
      {history.length === 0 ? (
        <Menu.Item key="empty" disabled>
          <Text type="secondary">No AI actions yet</Text>
        </Menu.Item>
      ) : (
        history.map((entry, index) => (
          <Menu.Item 
            key={entry.id}
            onClick={() => handleJumpToState(index)}
            style={{
              backgroundColor: index === currentIndex ? '#e6f7ff' : 'transparent',
              borderLeft: index === currentIndex ? '3px solid #1890ff' : 'none'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {getActionIcon(entry.action)}
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: '12px', fontWeight: index === currentIndex ? 'bold' : 'normal' }}>
                  {entry.metadata.description}
                </div>
                <div style={{ fontSize: '10px', color: '#999' }}>
                  {formatTimestamp(entry.timestamp)}
                </div>
              </div>
              {index === currentIndex && (
                <Tag size="small" color="blue">Current</Tag>
              )}
            </div>
          </Menu.Item>
        ))
      )}
      
      {showClear && history.length > 0 && (
        <>
          <Menu.Divider />
          <Menu.Item key="clear" onClick={handleClearHistory} style={{ color: '#ff4d4f' }}>
            <ClearOutlined style={{ marginRight: '8px' }} />
            Clear History
          </Menu.Item>
        </>
      )}
    </Menu>
  );

  // Render detailed history modal
  const renderHistoryModal = () => (
    <Modal
      title={
        <Space>
          <HistoryOutlined />
          AI Action History
          <Badge count={historySize} style={{ backgroundColor: '#1890ff' }} />
        </Space>
      }
      open={historyVisible}
      onCancel={() => setHistoryVisible(false)}
      footer={[
        <Button key="clear" danger onClick={handleClearHistory} disabled={history.length === 0}>
          <ClearOutlined />
          Clear All
        </Button>,
        <Button key="close" onClick={() => setHistoryVisible(false)}>
          Close
        </Button>
      ]}
      width={600}
    >
      {history.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
          <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
          <div>No AI actions recorded yet</div>
          <div style={{ fontSize: '12px', marginTop: '8px' }}>
            AI suggestions you apply will appear here
          </div>
        </div>
      ) : (
        <List
          dataSource={history}
          renderItem={(entry, index) => (
            <List.Item
              style={{
                backgroundColor: index === currentIndex ? '#e6f7ff' : 'transparent',
                borderLeft: index === currentIndex ? '3px solid #1890ff' : 'none',
                padding: '12px 16px',
                cursor: 'pointer'
              }}
              onClick={() => handleJumpToState(index)}
              actions={[
                <Button 
                  key="jump"
                  type="link" 
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleJumpToState(index);
                  }}
                  disabled={index === currentIndex}
                >
                  {index === currentIndex ? 'Current' : 'Jump Here'}
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={getActionIcon(entry.action)}
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontWeight: index === currentIndex ? 'bold' : 'normal' }}>
                      {entry.metadata.description}
                    </span>
                    {index === currentIndex && <Tag size="small" color="blue">Current</Tag>}
                  </div>
                }
                description={
                  <Space direction="vertical" size="small">
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {formatTimestamp(entry.timestamp)}
                    </Text>
                    {entry.metadata.suggestion && (
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        Score: {entry.metadata.suggestion.score} | 
                        Type: {entry.action.type.replace('APPLY_AI_', '').toLowerCase()}
                      </Text>
                    )}
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Modal>
  );

  if (compact) {
    return (
      <>
        <Space size="small" style={style} className={className}>
          <Tooltip title={getUndoDescription()}>
            <Button
              type="text"
              size={size}
              icon={<UndoOutlined />}
              onClick={handleUndo}
              disabled={!canUndo}
              loading={isUndoing}
            />
          </Tooltip>
          
          <Tooltip title={getRedoDescription()}>
            <Button
              type="text"
              size={size}
              icon={<RedoOutlined />}
              onClick={handleRedo}
              disabled={!canRedo}
              loading={isRedoing}
            />
          </Tooltip>
          
          {showHistory && (
            <Tooltip title="AI Action History">
              <Badge count={historySize} size="small" offset={[-8, 8]}>
                <Button
                  type="text"
                  size={size}
                  icon={<HistoryOutlined />}
                  onClick={() => setHistoryVisible(true)}
                />
              </Badge>
            </Tooltip>
          )}
        </Space>
        {renderHistoryModal()}
      </>
    );
  }

  return (
    <>
      <Space style={style} className={className}>
        <Tooltip title={getUndoDescription()}>
          <Button
            size={size}
            icon={<UndoOutlined />}
            onClick={handleUndo}
            disabled={!canUndo}
            loading={isUndoing}
          >
            Undo
          </Button>
        </Tooltip>
        
        <Tooltip title={getRedoDescription()}>
          <Button
            size={size}
            icon={<RedoOutlined />}
            onClick={handleRedo}
            disabled={!canRedo}
            loading={isRedoing}
          >
            Redo
          </Button>
        </Tooltip>
        
        {showHistory && (
          <Dropdown 
            overlay={renderHistoryMenu()} 
            trigger={['click']}
            placement="bottomRight"
          >
            <Badge count={historySize} size="small" offset={[-8, 8]}>
              <Button size={size} icon={<HistoryOutlined />}>
                History
              </Button>
            </Badge>
          </Dropdown>
        )}
        
        {showClear && historySize > 0 && (
          <Tooltip title="Clear AI history">
            <Button
              size={size}
              icon={<ClearOutlined />}
              onClick={handleClearHistory}
              type="text"
              danger
            />
          </Tooltip>
        )}
      </Space>
      {renderHistoryModal()}
    </>
  );
};

AIUndoRedoControls.propTypes = {
  size: PropTypes.oneOf(['small', 'default', 'large']),
  showHistory: PropTypes.bool,
  showClear: PropTypes.bool,
  compact: PropTypes.bool,
  style: PropTypes.object,
  className: PropTypes.string
};

export default AIUndoRedoControls;
