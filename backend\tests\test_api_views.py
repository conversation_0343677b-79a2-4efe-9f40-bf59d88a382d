"""
Unit tests for API views in the App Builder application.
"""

import pytest
import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from model_bakery import baker

from my_app.models import App, LayoutTemplate, AppTemplate, Comment


@pytest.mark.django_db
class TestAppAPIViews(APITestCase):
    """Test cases for App API endpoints."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = baker.make(User, username='testuser')
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        self.app_data = {
            'name': 'Test App',
            'description': 'A test application',
            'app_data': json.dumps({
                'components': [
                    {'id': '1', 'type': 'button', 'props': {'text': 'Click me'}}
                ]
            }),
            'is_public': False
        }

    def test_create_app(self):
        """Test creating a new app via API."""
        url = reverse('app-list')  # Assuming DRF router naming
        response = self.client.post(url, self.app_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == 'Test App'
        assert response.data['description'] == 'A test application'
        assert App.objects.count() == 1

    def test_create_app_without_authentication(self):
        """Test creating app without authentication fails."""
        self.client.credentials()  # Remove authentication
        url = reverse('app-list')
        response = self.client.post(url, self.app_data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_list_apps(self):
        """Test listing apps for authenticated user."""
        # Create test apps
        baker.make(App, user=self.user, name='App 1')
        baker.make(App, user=self.user, name='App 2')
        baker.make(App, user=baker.make(User), name='Other User App')
        
        url = reverse('app-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2  # Only user's apps

    def test_retrieve_app(self):
        """Test retrieving a specific app."""
        app = baker.make(App, user=self.user, name='Retrieve Test App')
        url = reverse('app-detail', kwargs={'pk': app.pk})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Retrieve Test App'

    def test_retrieve_other_user_private_app(self):
        """Test that users can't access other users' private apps."""
        other_user = baker.make(User)
        app = baker.make(App, user=other_user, is_public=False)
        url = reverse('app-detail', kwargs={'pk': app.pk})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_retrieve_public_app(self):
        """Test that users can access public apps."""
        other_user = baker.make(User)
        app = baker.make(App, user=other_user, is_public=True, name='Public App')
        url = reverse('app-detail', kwargs={'pk': app.pk})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Public App'

    def test_update_app(self):
        """Test updating an app."""
        app = baker.make(App, user=self.user, name='Original Name')
        url = reverse('app-detail', kwargs={'pk': app.pk})
        
        update_data = {
            'name': 'Updated Name',
            'description': 'Updated description'
        }
        response = self.client.patch(url, update_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Updated Name'
        
        app.refresh_from_db()
        assert app.name == 'Updated Name'

    def test_update_other_user_app(self):
        """Test that users can't update other users' apps."""
        other_user = baker.make(User)
        app = baker.make(App, user=other_user)
        url = reverse('app-detail', kwargs={'pk': app.pk})
        
        update_data = {'name': 'Hacked Name'}
        response = self.client.patch(url, update_data, format='json')
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_app(self):
        """Test deleting an app."""
        app = baker.make(App, user=self.user)
        url = reverse('app-detail', kwargs={'pk': app.pk})
        response = self.client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert App.objects.count() == 0

    def test_app_data_validation(self):
        """Test validation of app_data field."""
        invalid_data = self.app_data.copy()
        invalid_data['app_data'] = 'invalid json'
        
        url = reverse('app-list')
        response = self.client.post(url, invalid_data, format='json')
        
        # Should handle invalid JSON gracefully
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_201_CREATED]


@pytest.mark.django_db
class TestTemplateAPIViews(APITestCase):
    """Test cases for Template API endpoints."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = baker.make(User, username='testuser')
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

    def test_list_layout_templates(self):
        """Test listing layout templates."""
        # Create test templates
        baker.make(LayoutTemplate, user=self.user, name='User Template', is_public=False)
        baker.make(LayoutTemplate, user=baker.make(User), name='Public Template', is_public=True)
        baker.make(LayoutTemplate, user=baker.make(User), name='Private Template', is_public=False)
        
        url = reverse('layouttemplate-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        # Should return user's templates + public templates
        assert len(response.data['results']) == 2

    def test_create_layout_template(self):
        """Test creating a layout template."""
        template_data = {
            'name': 'New Layout',
            'description': 'A new layout template',
            'category': 'business',
            'components': {
                'header': {'type': 'header', 'props': {'title': 'Header'}}
            },
            'is_public': False
        }
        
        url = reverse('layouttemplate-list')
        response = self.client.post(url, template_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == 'New Layout'
        assert LayoutTemplate.objects.count() == 1

    def test_list_app_templates(self):
        """Test listing app templates."""
        baker.make(AppTemplate, user=self.user, name='User App Template')
        baker.make(AppTemplate, user=baker.make(User), name='Public App Template', is_public=True)
        
        url = reverse('apptemplate-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2

    def test_filter_templates_by_category(self):
        """Test filtering templates by category."""
        baker.make(LayoutTemplate, category='business', is_public=True)
        baker.make(LayoutTemplate, category='portfolio', is_public=True)
        baker.make(LayoutTemplate, category='business', is_public=True)
        
        url = reverse('layouttemplate-list')
        response = self.client.get(url, {'category': 'business'})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2


@pytest.mark.django_db
class TestCommentAPIViews(APITestCase):
    """Test cases for Comment API endpoints."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = baker.make(User, username='testuser')
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        self.app = baker.make(App, user=self.user)

    def test_create_comment(self):
        """Test creating a comment."""
        comment_data = {
            'app': self.app.id,
            'text': 'This needs improvement',
            'component_id': 'button-1',
            'position_x': 150,
            'position_y': 200
        }
        
        url = reverse('comment-list')
        response = self.client.post(url, comment_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['text'] == 'This needs improvement'
        assert Comment.objects.count() == 1

    def test_list_comments_for_app(self):
        """Test listing comments for a specific app."""
        baker.make(Comment, app=self.app, user=self.user, text='Comment 1')
        baker.make(Comment, app=self.app, user=self.user, text='Comment 2')
        other_app = baker.make(App, user=baker.make(User))
        baker.make(Comment, app=other_app, user=self.user, text='Other app comment')
        
        url = reverse('comment-list')
        response = self.client.get(url, {'app': self.app.id})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2

    def test_update_own_comment(self):
        """Test updating own comment."""
        comment = baker.make(Comment, app=self.app, user=self.user, text='Original text')
        url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        update_data = {'text': 'Updated text'}
        response = self.client.patch(url, update_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['text'] == 'Updated text'

    def test_cannot_update_other_user_comment(self):
        """Test that users can't update other users' comments."""
        other_user = baker.make(User)
        comment = baker.make(Comment, app=self.app, user=other_user, text='Other user comment')
        url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        update_data = {'text': 'Hacked text'}
        response = self.client.patch(url, update_data, format='json')
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_own_comment(self):
        """Test deleting own comment."""
        comment = baker.make(Comment, app=self.app, user=self.user)
        url = reverse('comment-detail', kwargs={'pk': comment.pk})
        response = self.client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert Comment.objects.count() == 0


@pytest.mark.django_db
class TestAPIPermissions(APITestCase):
    """Test cases for API permissions and security."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = baker.make(User, username='testuser')
        self.token = Token.objects.create(user=self.user)

    def test_unauthenticated_access(self):
        """Test that unauthenticated requests are rejected."""
        url = reverse('app-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_invalid_token(self):
        """Test that invalid tokens are rejected."""
        self.client.credentials(HTTP_AUTHORIZATION='Token invalid-token')
        url = reverse('app-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_rate_limiting(self):
        """Test API rate limiting (if implemented)."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        url = reverse('app-list')
        
        # Make multiple requests rapidly
        responses = []
        for _ in range(10):
            response = self.client.get(url)
            responses.append(response.status_code)
        
        # All should succeed if rate limiting is not too strict
        assert all(status_code == status.HTTP_200_OK for status_code in responses)

    def test_cors_headers(self):
        """Test that CORS headers are present."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        url = reverse('app-list')
        response = self.client.get(url)
        
        # Check for CORS headers (if configured)
        assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
class TestAPIErrorHandling(APITestCase):
    """Test cases for API error handling."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = baker.make(User, username='testuser')
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

    def test_404_for_nonexistent_resource(self):
        """Test 404 response for nonexistent resources."""
        url = reverse('app-detail', kwargs={'pk': 99999})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_400_for_invalid_data(self):
        """Test 400 response for invalid data."""
        url = reverse('app-list')
        invalid_data = {
            'name': '',  # Empty name should be invalid
            'app_data': 'invalid json'
        }
        response = self.client.post(url, invalid_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_method_not_allowed(self):
        """Test 405 response for unsupported methods."""
        app = baker.make(App, user=self.user)
        url = reverse('app-detail', kwargs={'pk': app.pk})
        
        # Assuming PATCH is not allowed (depends on implementation)
        response = self.client.trace(url)
        
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
