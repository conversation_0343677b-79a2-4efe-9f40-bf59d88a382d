import { useState, useEffect, useCallback, useMemo } from 'react';
import { DEVICE_PRESETS } from '../components/preview/DevicePreviewFrame';

/**
 * Custom hook for managing responsive preview functionality
 * Handles device switching, breakpoints, and responsive behavior
 */
const useResponsivePreview = ({
  initialDevice = 'desktop',
  initialVariant = null,
  enableBreakpointDetection = true,
  customBreakpoints = null
}) => {
  // State management
  const [currentDevice, setCurrentDevice] = useState(initialDevice);
  const [currentVariant, setCurrentVariant] = useState(
    initialVariant || DEVICE_PRESETS[initialDevice]?.defaultVariant
  );
  const [orientation, setOrientation] = useState('portrait');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [viewportSize, setViewportSize] = useState({ width: 0, height: 0 });

  // Default breakpoints (can be overridden)
  const defaultBreakpoints = {
    mobile: { min: 0, max: 767 },
    tablet: { min: 768, max: 1023 },
    desktop: { min: 1024, max: Infinity }
  };

  const breakpoints = customBreakpoints || defaultBreakpoints;

  // Get current device configuration
  const deviceConfig = useMemo(() => {
    const device = DEVICE_PRESETS[currentDevice];
    if (!device) return null;
    
    const variant = device.variants[currentVariant];
    if (!variant) return null;

    return {
      ...device,
      ...variant,
      category: device.category,
      orientation
    };
  }, [currentDevice, currentVariant, orientation]);

  // Get responsive styles based on current device
  const responsiveStyles = useMemo(() => {
    if (!deviceConfig) return {};

    const baseStyles = {
      fontSize: deviceConfig.category === 'mobile' ? '14px' : 
                 deviceConfig.category === 'tablet' ? '15px' : '16px',
      padding: deviceConfig.category === 'mobile' ? '8px' : 
               deviceConfig.category === 'tablet' ? '12px' : '16px',
      margin: deviceConfig.category === 'mobile' ? '4px' : '8px',
      borderRadius: deviceConfig.category === 'mobile' ? '4px' : '6px'
    };

    // Add device-specific styles
    const deviceSpecificStyles = {
      mobile: {
        maxWidth: '100%',
        touchAction: 'manipulation',
        WebkitTapHighlightColor: 'transparent'
      },
      tablet: {
        maxWidth: '100%',
        touchAction: 'manipulation'
      },
      desktop: {
        cursor: 'pointer',
        userSelect: 'none'
      }
    };

    return {
      ...baseStyles,
      ...deviceSpecificStyles[deviceConfig.category]
    };
  }, [deviceConfig]);

  // Get component size based on device
  const getComponentSize = useCallback((componentType) => {
    if (!deviceConfig) return 'middle';

    const sizeMap = {
      mobile: {
        button: 'small',
        input: 'small',
        card: 'small',
        table: 'small',
        form: 'small'
      },
      tablet: {
        button: 'middle',
        input: 'middle',
        card: 'default',
        table: 'middle',
        form: 'middle'
      },
      desktop: {
        button: 'middle',
        input: 'middle',
        card: 'default',
        table: 'middle',
        form: 'middle'
      }
    };

    return sizeMap[deviceConfig.category]?.[componentType] || 'middle';
  }, [deviceConfig]);

  // Detect viewport size changes
  useEffect(() => {
    if (!enableBreakpointDetection) return;

    const updateViewportSize = () => {
      setViewportSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateViewportSize();
    window.addEventListener('resize', updateViewportSize);

    return () => {
      window.removeEventListener('resize', updateViewportSize);
    };
  }, [enableBreakpointDetection]);

  // Auto-detect device based on viewport size
  useEffect(() => {
    if (!enableBreakpointDetection || viewportSize.width === 0) return;

    const detectedDevice = Object.entries(breakpoints).find(([_, range]) => 
      viewportSize.width >= range.min && viewportSize.width <= range.max
    )?.[0];

    if (detectedDevice && detectedDevice !== currentDevice) {
      setCurrentDevice(detectedDevice);
      setCurrentVariant(DEVICE_PRESETS[detectedDevice]?.defaultVariant);
    }
  }, [viewportSize, breakpoints, enableBreakpointDetection, currentDevice]);

  // Handle device change
  const handleDeviceChange = useCallback((newDevice, newVariant = null) => {
    setCurrentDevice(newDevice);
    const variant = newVariant || DEVICE_PRESETS[newDevice]?.defaultVariant;
    setCurrentVariant(variant);
    
    // Reset orientation for desktop
    if (newDevice === 'desktop') {
      setOrientation('portrait');
    }
  }, []);

  // Handle variant change
  const handleVariantChange = useCallback((newVariant) => {
    setCurrentVariant(newVariant);
  }, []);

  // Handle orientation change
  const handleOrientationChange = useCallback(() => {
    if (deviceConfig?.category === 'desktop') return;
    
    setOrientation(prev => prev === 'portrait' ? 'landscape' : 'portrait');
  }, [deviceConfig]);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  // Get media queries for CSS
  const getMediaQueries = useCallback(() => {
    return {
      mobile: `@media (max-width: ${breakpoints.mobile.max}px)`,
      tablet: `@media (min-width: ${breakpoints.tablet.min}px) and (max-width: ${breakpoints.tablet.max}px)`,
      desktop: `@media (min-width: ${breakpoints.desktop.min}px)`,
      isMobile: `(max-width: ${breakpoints.mobile.max}px)`,
      isTablet: `(min-width: ${breakpoints.tablet.min}px) and (max-width: ${breakpoints.tablet.max}px)`,
      isDesktop: `(min-width: ${breakpoints.desktop.min}px)`
    };
  }, [breakpoints]);

  // Check if current device matches a category
  const isDevice = useCallback((deviceCategory) => {
    return deviceConfig?.category === deviceCategory;
  }, [deviceConfig]);

  // Get responsive props for Ant Design components
  const getResponsiveProps = useCallback((componentType) => {
    const size = getComponentSize(componentType);
    const props = { size };

    // Add device-specific props
    if (deviceConfig?.category === 'mobile') {
      if (componentType === 'table') {
        props.scroll = { x: true };
        props.pagination = { simple: true, size: 'small' };
      }
      if (componentType === 'form') {
        props.layout = 'vertical';
      }
    }

    return props;
  }, [deviceConfig, getComponentSize]);

  // Get current dimensions (considering orientation)
  const getCurrentDimensions = useCallback(() => {
    if (!deviceConfig) return { width: 0, height: 0 };

    const { width, height } = deviceConfig;
    
    if (orientation === 'landscape' && deviceConfig.category !== 'desktop') {
      return { width: height, height: width };
    }
    
    return { width, height };
  }, [deviceConfig, orientation]);

  // Preview state utilities
  const previewUtils = {
    // Check if component should be hidden on current device
    shouldHideComponent: (componentConfig) => {
      const hideOn = componentConfig?.responsive?.hideOn || [];
      return hideOn.includes(deviceConfig?.category);
    },

    // Get component-specific responsive styles
    getComponentStyles: (componentConfig) => {
      const responsive = componentConfig?.responsive || {};
      const deviceStyles = responsive[deviceConfig?.category] || {};
      
      return {
        ...responsiveStyles,
        ...deviceStyles
      };
    },

    // Check if feature is supported on current device
    isFeatureSupported: (feature) => {
      const supportMap = {
        hover: deviceConfig?.category === 'desktop',
        touch: deviceConfig?.category !== 'desktop',
        keyboard: deviceConfig?.category === 'desktop',
        contextMenu: deviceConfig?.category === 'desktop'
      };
      
      return supportMap[feature] !== false;
    }
  };

  return {
    // Current state
    currentDevice,
    currentVariant,
    orientation,
    isFullscreen,
    deviceConfig,
    viewportSize,

    // Computed values
    responsiveStyles,
    mediaQueries: getMediaQueries(),
    dimensions: getCurrentDimensions(),

    // Actions
    handleDeviceChange,
    handleVariantChange,
    handleOrientationChange,
    handleFullscreenToggle,

    // Utilities
    isDevice,
    getComponentSize,
    getResponsiveProps,
    ...previewUtils,

    // Available devices and variants
    availableDevices: Object.keys(DEVICE_PRESETS),
    availableVariants: deviceConfig ? Object.keys(deviceConfig.variants || {}) : [],
    
    // Breakpoints
    breakpoints
  };
};

export default useResponsivePreview;
