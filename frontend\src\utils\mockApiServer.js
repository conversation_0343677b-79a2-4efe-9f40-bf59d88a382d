/**
 * Mock API Server
 * 
 * This module provides mock API responses when the backend is not available.
 * It intercepts fetch requests and returns mock data for development and testing.
 */

// Mock data for app data
const mockAppData = {
  app: {
    name: 'App Builder',
    version: '1.0.0',
    components: [
      { id: 1, type: 'Button', props: { text: 'Click Me', variant: 'primary' } },
      { id: 2, type: 'Input', props: { placeholder: 'Enter text', label: 'Name' } },
      { id: 3, type: 'Text', props: { content: 'Hello World', style: { fontWeight: 'bold' } } }
    ],
    layouts: [
      { id: 1, type: 'Grid', components: [1, 2], styles: { gap: '10px' } },
      { id: 2, type: 'Flex', components: [3], styles: { justifyContent: 'center' } }
    ],
    styles: {
      '.container': { display: 'flex', flexDirection: 'column', gap: '20px' },
      '.header': { fontSize: '24px', fontWeight: 'bold', marginBottom: '16px' }
    },
    status: 'online'
  },
  _meta: {
    source: 'mock',
    timestamp: new Date().toISOString()
  }
};

// Mock data for API status
const mockApiStatus = {
  status: 'ok',
  version: '1.0.0',
  uptime: '2h 34m',
  services: {
    database: 'connected',
    cache: 'connected',
    storage: 'connected'
  },
  timestamp: new Date().toISOString()
};

// Mock data for API health
const mockApiHealth = {
  healthy: true,
  services: [
    { name: 'database', status: 'healthy', latency: 5 },
    { name: 'cache', status: 'healthy', latency: 2 },
    { name: 'storage', status: 'healthy', latency: 8 }
  ],
  timestamp: new Date().toISOString()
};

/**
 * Initialize the mock API server
 * @param {Object} options - Configuration options
 * @param {boolean} options.enabled - Whether the mock server is enabled
 * @param {number} options.delay - Delay in milliseconds to simulate network latency
 * @param {boolean} options.logRequests - Whether to log requests
 */
export function initMockApiServer(options = {}) {
  const {
    enabled = process.env.NODE_ENV === 'development',
    delay = 300,
    logRequests = true
  } = options;

  if (!enabled) {
    console.log('Mock API server is disabled');
    return;
  }

  console.log('Initializing mock API server...');

  // Store the original fetch function
  const originalFetch = window.fetch;

  // Override the fetch function
  window.fetch = async (url, options = {}) => {
    // Convert URL to string if it's a Request object
    const urlString = url instanceof Request ? url.url : url.toString();

    if (logRequests) {
      console.log(`Mock API server: ${options.method || 'GET'} ${urlString}`);
    }

    // Check if the URL matches any of our mock endpoints
    if (urlString.includes('/api/app-data') || urlString.includes('/get_app_data')) {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Return mock app data
      return new Response(JSON.stringify(mockAppData), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Mock-Server': 'true'
        }
      });
    }

    if (urlString.includes('/api/status') || urlString.includes('/status')) {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Return mock status
      return new Response(JSON.stringify(mockApiStatus), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Mock-Server': 'true'
        }
      });
    }

    if (urlString.includes('/api/health') || urlString.includes('/health')) {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Return mock health
      return new Response(JSON.stringify(mockApiHealth), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Mock-Server': 'true'
        }
      });
    }

    // Handle error reporting endpoint
    if (urlString.includes('/api/errors')) {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, delay));

      if (logRequests) {
        console.log('Mock API server: Received error report');
      }

      // Parse the request body if it's a POST request
      if (options.method === 'POST') {
        try {
          const body = options.body ? JSON.parse(options.body) : {};
          const errors = body.errors || [body];

          if (logRequests) {
            console.log(`Mock API server: Logged ${errors.length} error(s)`);
            errors.forEach((error, index) => {
              console.log(`  Error ${index + 1}:`, {
                type: error.type,
                message: error.message,
                timestamp: error.timestamp
              });
            });
          }
        } catch (parseError) {
          console.warn('Mock API server: Failed to parse error report body:', parseError);
        }
      }

      // Return success response
      return new Response(JSON.stringify({
        status: 'success',
        message: 'Errors received and logged',
        timestamp: new Date().toISOString()
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Mock-Server': 'true'
        }
      });
    }

    // For all other requests, use the original fetch
    return originalFetch(url, options);
  };

  console.log('Mock API server initialized');
}

/**
 * Disable the mock API server
 */
export function disableMockApiServer() {
  // Restore the original fetch function if it exists
  if (window._originalFetch) {
    window.fetch = window._originalFetch;
    console.log('Mock API server disabled');
  }
}

export default {
  initMockApiServer,
  disableMockApiServer,
  mockAppData,
  mockApiStatus,
  mockApiHealth
};
