"""
Code validation service for App Builder
Provides comprehensive validation and quality assurance for generated code
"""

import re
import json
import ast
import subprocess
import tempfile
import os
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class ValidationLevel(Enum):
    """Validation severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationResult:
    """Result of code validation"""
    level: ValidationLevel
    message: str
    line: Optional[int] = None
    column: Optional[int] = None
    rule: Optional[str] = None
    suggestion: Optional[str] = None


class CodeValidator:
    """
    Comprehensive code validator for multiple programming languages and frameworks
    """
    
    def __init__(self):
        self.validation_rules = {
            'react': self._get_react_rules(),
            'vue': self._get_vue_rules(),
            'angular': self._get_angular_rules(),
            'html': self._get_html_rules(),
            'javascript': self._get_javascript_rules(),
            'typescript': self._get_typescript_rules(),
            'python': self._get_python_rules(),
            'dart': self._get_dart_rules()
        }
    
    def validate_code(self, code: str, language: str, framework: str = None) -> List[ValidationResult]:
        """
        Validate code for syntax, style, and best practices
        
        Args:
            code: The code to validate
            language: Programming language (js, ts, py, dart, html, css)
            framework: Framework (react, vue, angular, etc.)
            
        Returns:
            List of validation results
        """
        results = []
        
        # Basic syntax validation
        syntax_results = self._validate_syntax(code, language)
        results.extend(syntax_results)
        
        # Framework-specific validation
        if framework and framework in self.validation_rules:
            framework_results = self._validate_framework_rules(code, framework)
            results.extend(framework_results)
        
        # General code quality checks
        quality_results = self._validate_code_quality(code, language)
        results.extend(quality_results)
        
        # Security checks
        security_results = self._validate_security(code, language)
        results.extend(security_results)
        
        # Accessibility checks (for frontend code)
        if language in ['html', 'js', 'ts'] or framework in ['react', 'vue', 'angular']:
            accessibility_results = self._validate_accessibility(code)
            results.extend(accessibility_results)
        
        return results
    
    def validate_project_structure(self, files: Dict[str, str], framework: str) -> List[ValidationResult]:
        """
        Validate project structure and file organization
        
        Args:
            files: Dictionary of filename -> content
            framework: Target framework
            
        Returns:
            List of validation results
        """
        results = []
        
        # Check required files
        required_files = self._get_required_files(framework)
        for required_file in required_files:
            if not any(filename.endswith(required_file) for filename in files.keys()):
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Missing required file: {required_file}",
                    rule="project_structure"
                ))
        
        # Validate package.json if present
        package_json_files = [f for f in files.keys() if f.endswith('package.json')]
        for package_file in package_json_files:
            package_results = self._validate_package_json(files[package_file])
            results.extend(package_results)
        
        # Check file naming conventions
        naming_results = self._validate_file_naming(list(files.keys()), framework)
        results.extend(naming_results)
        
        return results
    
    def _validate_syntax(self, code: str, language: str) -> List[ValidationResult]:
        """Validate basic syntax"""
        results = []
        
        if language in ['js', 'javascript']:
            results.extend(self._validate_javascript_syntax(code))
        elif language in ['ts', 'typescript']:
            results.extend(self._validate_typescript_syntax(code))
        elif language in ['py', 'python']:
            results.extend(self._validate_python_syntax(code))
        elif language == 'html':
            results.extend(self._validate_html_syntax(code))
        elif language == 'css':
            results.extend(self._validate_css_syntax(code))
        elif language == 'dart':
            results.extend(self._validate_dart_syntax(code))
        
        return results
    
    def _validate_javascript_syntax(self, code: str) -> List[ValidationResult]:
        """Validate JavaScript syntax"""
        results = []
        
        # Check for balanced brackets
        brackets = {'(': ')', '[': ']', '{': '}'}
        stack = []
        
        for i, char in enumerate(code):
            if char in brackets:
                stack.append((char, i))
            elif char in brackets.values():
                if not stack:
                    results.append(ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Unmatched closing bracket: {char}",
                        column=i,
                        rule="syntax_error"
                    ))
                else:
                    open_char, _ = stack.pop()
                    if brackets[open_char] != char:
                        results.append(ValidationResult(
                            level=ValidationLevel.ERROR,
                            message=f"Mismatched brackets: {open_char} and {char}",
                            column=i,
                            rule="syntax_error"
                        ))
        
        # Check for unclosed brackets
        if stack:
            for open_char, pos in stack:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Unclosed bracket: {open_char}",
                    column=pos,
                    rule="syntax_error"
                ))
        
        return results
    
    def _validate_python_syntax(self, code: str) -> List[ValidationResult]:
        """Validate Python syntax using AST"""
        results = []
        
        try:
            ast.parse(code)
        except SyntaxError as e:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message=f"Python syntax error: {e.msg}",
                line=e.lineno,
                column=e.offset,
                rule="syntax_error"
            ))
        
        return results
    
    def _validate_html_syntax(self, code: str) -> List[ValidationResult]:
        """Validate HTML syntax"""
        results = []
        
        # Check for basic HTML structure
        if not re.search(r'<!DOCTYPE\s+html>', code, re.IGNORECASE):
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message="Missing DOCTYPE declaration",
                rule="html_structure",
                suggestion="Add <!DOCTYPE html> at the beginning"
            ))
        
        # Check for balanced tags
        tag_pattern = r'<(/?)(\w+)(?:\s[^>]*)?>'
        tags = re.findall(tag_pattern, code)
        stack = []
        
        for is_closing, tag_name in tags:
            if is_closing:
                if not stack or stack[-1] != tag_name.lower():
                    results.append(ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Unmatched closing tag: </{tag_name}>",
                        rule="html_structure"
                    ))
                else:
                    stack.pop()
            else:
                # Self-closing tags
                if tag_name.lower() not in ['img', 'br', 'hr', 'input', 'meta', 'link']:
                    stack.append(tag_name.lower())
        
        return results
    
    def _validate_framework_rules(self, code: str, framework: str) -> List[ValidationResult]:
        """Validate framework-specific rules"""
        results = []
        rules = self.validation_rules.get(framework, [])
        
        for rule in rules:
            if rule['type'] == 'pattern':
                matches = re.finditer(rule['pattern'], code, re.MULTILINE)
                for match in matches:
                    line_num = code[:match.start()].count('\n') + 1
                    results.append(ValidationResult(
                        level=ValidationLevel(rule['level']),
                        message=rule['message'],
                        line=line_num,
                        rule=rule['name'],
                        suggestion=rule.get('suggestion')
                    ))
            elif rule['type'] == 'required':
                if not re.search(rule['pattern'], code):
                    results.append(ValidationResult(
                        level=ValidationLevel(rule['level']),
                        message=rule['message'],
                        rule=rule['name'],
                        suggestion=rule.get('suggestion')
                    ))
        
        return results
    
    def _validate_accessibility(self, code: str) -> List[ValidationResult]:
        """Validate accessibility features"""
        results = []
        
        # Check for images without alt text
        img_pattern = r'<img(?![^>]*alt=)[^>]*>'
        for match in re.finditer(img_pattern, code):
            line_num = code[:match.start()].count('\n') + 1
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message="Image missing alt attribute",
                line=line_num,
                rule="accessibility",
                suggestion="Add alt attribute for screen readers"
            ))
        
        # Check for buttons without accessible text
        button_pattern = r'<button[^>]*>(\s*<[^>]*>\s*)*</button>'
        for match in re.finditer(button_pattern, code):
            if not re.search(r'aria-label|title', match.group()):
                line_num = code[:match.start()].count('\n') + 1
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message="Button may need accessible text",
                    line=line_num,
                    rule="accessibility",
                    suggestion="Add aria-label or ensure button has descriptive text"
                ))
        
        return results
    
    def _validate_security(self, code: str, language: str) -> List[ValidationResult]:
        """Validate security best practices"""
        results = []
        
        # Check for potential XSS vulnerabilities
        if language in ['js', 'ts', 'html']:
            dangerous_patterns = [
                (r'innerHTML\s*=\s*[^;]+', "Potential XSS vulnerability with innerHTML"),
                (r'eval\s*\(', "Use of eval() is dangerous"),
                (r'document\.write\s*\(', "document.write can be dangerous")
            ]
            
            for pattern, message in dangerous_patterns:
                for match in re.finditer(pattern, code):
                    line_num = code[:match.start()].count('\n') + 1
                    results.append(ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=message,
                        line=line_num,
                        rule="security"
                    ))
        
        return results
    
    def _validate_package_json(self, content: str) -> List[ValidationResult]:
        """Validate package.json structure"""
        results = []
        
        try:
            package_data = json.loads(content)
            
            # Check required fields
            required_fields = ['name', 'version']
            for field in required_fields:
                if field not in package_data:
                    results.append(ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Missing required field in package.json: {field}",
                        rule="package_json"
                    ))
            
            # Check for security vulnerabilities in dependencies
            if 'dependencies' in package_data:
                vulnerable_packages = self._check_vulnerable_packages(package_data['dependencies'])
                for package, issue in vulnerable_packages:
                    results.append(ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Potentially vulnerable package: {package} - {issue}",
                        rule="security"
                    ))
        
        except json.JSONDecodeError as e:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message=f"Invalid JSON in package.json: {e.msg}",
                rule="syntax_error"
            ))
        
        return results
    
    def _get_react_rules(self) -> List[Dict]:
        """Get React-specific validation rules"""
        return [
            {
                'name': 'react_import',
                'type': 'required',
                'pattern': r'import\s+React',
                'level': 'error',
                'message': 'React must be imported',
                'suggestion': "Add 'import React from \"react\";'"
            },
            {
                'name': 'component_export',
                'type': 'required',
                'pattern': r'export\s+default',
                'level': 'error',
                'message': 'Component must have default export',
                'suggestion': 'Add "export default ComponentName;"'
            },
            {
                'name': 'jsx_key_prop',
                'type': 'pattern',
                'pattern': r'\.map\([^}]*<[^>]*(?!.*key=)',
                'level': 'warning',
                'message': 'Missing key prop in mapped JSX elements',
                'suggestion': 'Add unique key prop to mapped elements'
            }
        ]
    
    def _get_vue_rules(self) -> List[Dict]:
        """Get Vue-specific validation rules"""
        return [
            {
                'name': 'vue_template',
                'type': 'required',
                'pattern': r'<template>',
                'level': 'error',
                'message': 'Vue component must have template section'
            },
            {
                'name': 'vue_script',
                'type': 'required',
                'pattern': r'<script>',
                'level': 'error',
                'message': 'Vue component must have script section'
            }
        ]
    
    def _get_angular_rules(self) -> List[Dict]:
        """Get Angular-specific validation rules"""
        return [
            {
                'name': 'angular_component',
                'type': 'required',
                'pattern': r'@Component',
                'level': 'error',
                'message': 'Angular component must have @Component decorator'
            }
        ]
    
    def _get_html_rules(self) -> List[Dict]:
        """Get HTML-specific validation rules"""
        return [
            {
                'name': 'html_lang',
                'type': 'required',
                'pattern': r'<html[^>]*lang=',
                'level': 'warning',
                'message': 'HTML should specify language',
                'suggestion': 'Add lang attribute to html tag'
            }
        ]
    
    def _get_javascript_rules(self) -> List[Dict]:
        """Get JavaScript-specific validation rules"""
        return [
            {
                'name': 'strict_mode',
                'type': 'pattern',
                'pattern': r'["\']use strict["\']',
                'level': 'info',
                'message': 'Consider using strict mode'
            }
        ]
    
    def _get_typescript_rules(self) -> List[Dict]:
        """Get TypeScript-specific validation rules"""
        return [
            {
                'name': 'interface_naming',
                'type': 'pattern',
                'pattern': r'interface\s+[a-z]',
                'level': 'warning',
                'message': 'Interface names should start with uppercase letter'
            }
        ]
    
    def _get_python_rules(self) -> List[Dict]:
        """Get Python-specific validation rules"""
        return [
            {
                'name': 'pep8_imports',
                'type': 'pattern',
                'pattern': r'import\s+\*',
                'level': 'warning',
                'message': 'Avoid wildcard imports'
            }
        ]
    
    def _get_dart_rules(self) -> List[Dict]:
        """Get Dart-specific validation rules"""
        return [
            {
                'name': 'dart_main',
                'type': 'required',
                'pattern': r'void\s+main\s*\(',
                'level': 'error',
                'message': 'Dart app must have main function'
            }
        ]
    
    def _get_required_files(self, framework: str) -> List[str]:
        """Get required files for framework"""
        requirements = {
            'react': ['package.json'],
            'vue': ['package.json'],
            'angular': ['package.json', 'tsconfig.json'],
            'next': ['package.json'],
            'flutter': ['pubspec.yaml'],
            'express': ['package.json'],
            'django': ['requirements.txt'],
            'fastapi': ['requirements.txt']
        }
        return requirements.get(framework, [])
    
    def _validate_file_naming(self, filenames: List[str], framework: str) -> List[ValidationResult]:
        """Validate file naming conventions"""
        results = []
        
        conventions = {
            'react': {
                'components': r'^[A-Z][a-zA-Z0-9]*\.(jsx?|tsx?)$',
                'message': 'React components should use PascalCase'
            },
            'vue': {
                'components': r'^[A-Z][a-zA-Z0-9]*\.vue$',
                'message': 'Vue components should use PascalCase'
            }
        }
        
        if framework in conventions:
            convention = conventions[framework]
            for filename in filenames:
                basename = os.path.basename(filename)
                if basename.endswith(('.jsx', '.tsx', '.vue')):
                    if not re.match(convention['components'], basename):
                        results.append(ValidationResult(
                            level=ValidationLevel.WARNING,
                            message=f"{convention['message']}: {basename}",
                            rule="naming_convention"
                        ))
        
        return results
    
    def _check_vulnerable_packages(self, dependencies: Dict[str, str]) -> List[Tuple[str, str]]:
        """Check for known vulnerable packages"""
        # This would typically integrate with a vulnerability database
        # For now, return a simple check for some known issues
        vulnerable = []
        
        known_issues = {
            'lodash': 'Versions < 4.17.19 have prototype pollution vulnerability',
            'moment': 'Consider using date-fns or dayjs for better performance'
        }
        
        for package in dependencies:
            if package in known_issues:
                vulnerable.append((package, known_issues[package]))
        
        return vulnerable
    
    def _validate_typescript_syntax(self, code: str) -> List[ValidationResult]:
        """Validate TypeScript syntax"""
        # This would typically use the TypeScript compiler API
        # For now, do basic JavaScript validation
        return self._validate_javascript_syntax(code)
    
    def _validate_css_syntax(self, code: str) -> List[ValidationResult]:
        """Validate CSS syntax"""
        results = []
        
        # Check for balanced braces
        open_braces = code.count('{')
        close_braces = code.count('}')
        
        if open_braces != close_braces:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                message="Unbalanced CSS braces",
                rule="syntax_error"
            ))
        
        return results
    
    def _validate_dart_syntax(self, code: str) -> List[ValidationResult]:
        """Validate Dart syntax"""
        # This would typically use the Dart analyzer
        # For now, do basic checks
        results = []
        
        if 'void main(' not in code and 'main(' not in code:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                message="Dart file should have a main function",
                rule="dart_structure"
            ))
        
        return results
