/**
 * Tutorial Badges Component
 * 
 * Displays achievement badges, completion certificates, and
 * gamification elements for the tutorial system.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Badge,
  Typography,
  Space,
  Button,
  Modal,
  Progress,
  Empty,
  Tooltip,
  Tag,
  Timeline,
  Statistic,
  Avatar
} from 'antd';
import {
  TrophyOutlined,
  StarOutlined,
  CrownOutlined,
  FireOutlined,
  ThunderboltOutlined,
  RocketOutlined,
  BookOutlined,
  TargetOutlined,
  CheckCircleOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import { TUTORIAL_CATEGORIES_CONFIG } from './TutorialContent';
import { TUTORIAL_STATUS, TUTORIAL_CATEGORIES } from './types';

const { Title, Text, Paragraph } = Typography;

// Styled Components
const BadgeCard = styled(Card)`
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: ${props => props.earned ? '#52c41a' : '#d9d9d9'};
  }
  
  .ant-card-body {
    padding: 20px;
  }
`;

const BadgeIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin: 0 auto 16px;
  background: ${props => props.earned ? 
    `linear-gradient(135deg, ${props.color || '#faad14'}, ${props.secondaryColor || '#ffd666'})` : 
    '#f5f5f5'
  };
  color: ${props => props.earned ? 'white' : '#8c8c8c'};
  box-shadow: ${props => props.earned ? '0 4px 12px rgba(0, 0, 0, 0.15)' : 'none'};
  position: relative;
  
  ${props => props.earned && `
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border-radius: 50%;
      background: linear-gradient(45deg, #faad14, #ffd666, #faad14);
      z-index: -1;
      animation: rotate 3s linear infinite;
    }
    
    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `}
`;

const CertificateContainer = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(255,255,255,0.05) 10px,
      rgba(255,255,255,0.05) 20px
    );
    animation: shimmer 3s linear infinite;
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  }
`;

// Badge Definitions
const BADGE_DEFINITIONS = {
  first_steps: {
    id: 'first_steps',
    title: 'First Steps',
    description: 'Completed your first tutorial',
    icon: <BookOutlined />,
    color: '#52c41a',
    secondaryColor: '#73d13d',
    requirement: 'Complete 1 tutorial',
    category: 'milestone'
  },
  quick_learner: {
    id: 'quick_learner',
    title: 'Quick Learner',
    description: 'Completed 5 tutorials',
    icon: <ThunderboltOutlined />,
    color: '#1890ff',
    secondaryColor: '#40a9ff',
    requirement: 'Complete 5 tutorials',
    category: 'milestone'
  },
  tutorial_master: {
    id: 'tutorial_master',
    title: 'Tutorial Master',
    description: 'Completed all available tutorials',
    icon: <CrownOutlined />,
    color: '#faad14',
    secondaryColor: '#ffd666',
    requirement: 'Complete all tutorials',
    category: 'achievement'
  },
  beginner_graduate: {
    id: 'beginner_graduate',
    title: 'Beginner Graduate',
    description: 'Completed all beginner tutorials',
    icon: <StarOutlined />,
    color: '#52c41a',
    secondaryColor: '#73d13d',
    requirement: 'Complete all beginner tutorials',
    category: 'category'
  },
  intermediate_expert: {
    id: 'intermediate_expert',
    title: 'Intermediate Expert',
    description: 'Completed all intermediate tutorials',
    icon: <TargetOutlined />,
    color: '#1890ff',
    secondaryColor: '#40a9ff',
    requirement: 'Complete all intermediate tutorials',
    category: 'category'
  },
  advanced_master: {
    id: 'advanced_master',
    title: 'Advanced Master',
    description: 'Completed all advanced tutorials',
    icon: <RocketOutlined />,
    color: '#f5222d',
    secondaryColor: '#ff4d4f',
    requirement: 'Complete all advanced tutorials',
    category: 'category'
  },
  speed_demon: {
    id: 'speed_demon',
    title: 'Speed Demon',
    description: 'Completed a tutorial in record time',
    icon: <FireOutlined />,
    color: '#fa541c',
    secondaryColor: '#ff7a45',
    requirement: 'Complete tutorial under estimated time',
    category: 'special'
  },
  perfectionist: {
    id: 'perfectionist',
    title: 'Perfectionist',
    description: 'Completed 10 tutorials without skipping any steps',
    icon: <TrophyOutlined />,
    color: '#722ed1',
    secondaryColor: '#9254de',
    requirement: 'Complete 10 tutorials perfectly',
    category: 'special'
  }
};

const TutorialBadges = ({ userId = 'anonymous' }) => {
  const {
    getAllTutorials,
    getTutorialProgress,
    getBadges,
    getStatistics
  } = useTutorial();

  const [selectedBadge, setSelectedBadge] = useState(null);
  const [showCertificate, setShowCertificate] = useState(false);

  const allTutorials = getAllTutorials();
  const earnedBadges = getBadges();
  const statistics = getStatistics();

  // Calculate badge eligibility
  const calculateBadgeEligibility = () => {
    const eligibility = {};
    
    Object.values(BADGE_DEFINITIONS).forEach(badge => {
      let earned = false;
      let progress = 0;
      let total = 1;
      
      switch (badge.id) {
        case 'first_steps':
          earned = statistics.totalTutorialsCompleted >= 1;
          progress = Math.min(statistics.totalTutorialsCompleted, 1);
          total = 1;
          break;
          
        case 'quick_learner':
          earned = statistics.totalTutorialsCompleted >= 5;
          progress = Math.min(statistics.totalTutorialsCompleted, 5);
          total = 5;
          break;
          
        case 'tutorial_master':
          earned = statistics.totalTutorialsCompleted >= allTutorials.length;
          progress = statistics.totalTutorialsCompleted;
          total = allTutorials.length;
          break;
          
        case 'beginner_graduate':
          const beginnerTutorials = allTutorials.filter(t => t.category === TUTORIAL_CATEGORIES.BEGINNER);
          const completedBeginner = beginnerTutorials.filter(t => {
            const progress = getTutorialProgress(t.id);
            return progress?.status === TUTORIAL_STATUS.COMPLETED;
          }).length;
          earned = completedBeginner >= beginnerTutorials.length;
          progress = completedBeginner;
          total = beginnerTutorials.length;
          break;
          
        case 'intermediate_expert':
          const intermediateTutorials = allTutorials.filter(t => t.category === TUTORIAL_CATEGORIES.INTERMEDIATE);
          const completedIntermediate = intermediateTutorials.filter(t => {
            const progress = getTutorialProgress(t.id);
            return progress?.status === TUTORIAL_STATUS.COMPLETED;
          }).length;
          earned = completedIntermediate >= intermediateTutorials.length;
          progress = completedIntermediate;
          total = intermediateTutorials.length;
          break;
          
        case 'advanced_master':
          const advancedTutorials = allTutorials.filter(t => t.category === TUTORIAL_CATEGORIES.ADVANCED);
          const completedAdvanced = advancedTutorials.filter(t => {
            const progress = getTutorialProgress(t.id);
            return progress?.status === TUTORIAL_STATUS.COMPLETED;
          }).length;
          earned = completedAdvanced >= advancedTutorials.length;
          progress = completedAdvanced;
          total = advancedTutorials.length;
          break;
          
        default:
          // Check if badge is in earned badges
          earned = earnedBadges.some(b => b.id === badge.id);
          progress = earned ? 1 : 0;
          total = 1;
      }
      
      eligibility[badge.id] = { earned, progress, total };
    });
    
    return eligibility;
  };

  const badgeEligibility = calculateBadgeEligibility();

  const handleBadgeClick = (badge) => {
    setSelectedBadge(badge);
  };

  const handleDownloadCertificate = () => {
    // In a real implementation, this would generate and download a PDF certificate
    console.log('Downloading certificate...');
  };

  const handleShareBadge = (badge) => {
    // In a real implementation, this would share to social media
    console.log('Sharing badge:', badge.title);
  };

  const renderBadge = (badge) => {
    const eligibility = badgeEligibility[badge.id];
    const isEarned = eligibility.earned;
    const earnedBadge = earnedBadges.find(b => b.id === badge.id);
    
    return (
      <Col xs={12} sm={8} md={6} lg={4} key={badge.id}>
        <BadgeCard
          earned={isEarned}
          onClick={() => handleBadgeClick(badge)}
        >
          <BadgeIcon 
            earned={isEarned}
            color={badge.color}
            secondaryColor={badge.secondaryColor}
          >
            {badge.icon}
          </BadgeIcon>
          
          <Title level={5} style={{ margin: '0 0 8px 0' }}>
            {badge.title}
          </Title>
          
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {badge.description}
          </Text>
          
          {!isEarned && eligibility.total > 1 && (
            <div style={{ marginTop: 12 }}>
              <Progress 
                percent={(eligibility.progress / eligibility.total) * 100}
                size="small"
                showInfo={false}
              />
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {eligibility.progress} / {eligibility.total}
              </Text>
            </div>
          )}
          
          {isEarned && earnedBadge && (
            <div style={{ marginTop: 8 }}>
              <Tag color="success" size="small">
                <CalendarOutlined style={{ marginRight: 4 }} />
                {new Date(earnedBadge.earnedAt).toLocaleDateString()}
              </Tag>
            </div>
          )}
        </BadgeCard>
      </Col>
    );
  };

  const groupedBadges = Object.values(BADGE_DEFINITIONS).reduce((groups, badge) => {
    if (!groups[badge.category]) {
      groups[badge.category] = [];
    }
    groups[badge.category].push(badge);
    return groups;
  }, {});

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="Badges Earned"
              value={earnedBadges.length}
              suffix={`/ ${Object.keys(BADGE_DEFINITIONS).length}`}
              prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Completion Rate"
              value={(earnedBadges.length / Object.keys(BADGE_DEFINITIONS).length) * 100}
              precision={1}
              suffix="%"
              prefix={<StarOutlined style={{ color: '#52c41a' }} />}
            />
          </Col>
          <Col span={8}>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => setShowCertificate(true)}
              disabled={earnedBadges.length === 0}
            >
              View Certificate
            </Button>
          </Col>
        </Row>
      </div>

      {Object.entries(groupedBadges).map(([category, badges]) => (
        <div key={category} style={{ marginBottom: 32 }}>
          <Title level={4} style={{ textTransform: 'capitalize', marginBottom: 16 }}>
            {category} Badges
          </Title>
          <Row gutter={[16, 16]}>
            {badges.map(renderBadge)}
          </Row>
        </div>
      ))}

      {/* Badge Detail Modal */}
      <Modal
        title={selectedBadge?.title}
        open={!!selectedBadge}
        onCancel={() => setSelectedBadge(null)}
        footer={
          selectedBadge && badgeEligibility[selectedBadge.id]?.earned ? [
            <Button
              key="share"
              icon={<ShareAltOutlined />}
              onClick={() => handleShareBadge(selectedBadge)}
            >
              Share
            </Button>
          ] : null
        }
      >
        {selectedBadge && (
          <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
            <BadgeIcon 
              earned={badgeEligibility[selectedBadge.id]?.earned}
              color={selectedBadge.color}
              secondaryColor={selectedBadge.secondaryColor}
            >
              {selectedBadge.icon}
            </BadgeIcon>
            
            <Paragraph>{selectedBadge.description}</Paragraph>
            
            <Text type="secondary">
              <strong>Requirement:</strong> {selectedBadge.requirement}
            </Text>
            
            {!badgeEligibility[selectedBadge.id]?.earned && (
              <div>
                <Text>Progress:</Text>
                <Progress 
                  percent={(badgeEligibility[selectedBadge.id]?.progress / badgeEligibility[selectedBadge.id]?.total) * 100}
                  style={{ marginTop: 8 }}
                />
              </div>
            )}
          </Space>
        )}
      </Modal>

      {/* Certificate Modal */}
      <Modal
        title="Tutorial Completion Certificate"
        open={showCertificate}
        onCancel={() => setShowCertificate(false)}
        footer={[
          <Button
            key="download"
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleDownloadCertificate}
          >
            Download Certificate
          </Button>
        ]}
        width={600}
      >
        <CertificateContainer>
          <Title level={2} style={{ color: 'white', margin: '0 0 16px 0' }}>
            Certificate of Completion
          </Title>
          <Paragraph style={{ color: 'white', fontSize: '16px' }}>
            This certifies that
          </Paragraph>
          <Title level={3} style={{ color: 'white', margin: '8px 0' }}>
            {userId}
          </Title>
          <Paragraph style={{ color: 'white', fontSize: '16px' }}>
            has successfully completed <strong>{statistics.totalTutorialsCompleted}</strong> tutorials
            in the App Builder Tutorial System
          </Paragraph>
          <div style={{ marginTop: 24 }}>
            <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
              Issued on {new Date().toLocaleDateString()}
            </Text>
          </div>
        </CertificateContainer>
      </Modal>
    </div>
  );
};

export default TutorialBadges;
