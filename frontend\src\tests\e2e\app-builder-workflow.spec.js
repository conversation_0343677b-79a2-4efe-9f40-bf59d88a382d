import { test, expect } from '@playwright/test';

// Test configuration
const APP_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

test.describe('App Builder E2E Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses for consistent testing
    await page.route(`${API_URL}/api/**`, async (route) => {
      const url = route.request().url();
      
      if (url.includes('/api/apps/')) {
        if (route.request().method() === 'GET') {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              count: 1,
              results: [{
                id: '1',
                name: 'Test App',
                description: 'A test application',
                app_data: JSON.stringify({
                  components: [
                    { id: '1', type: 'button', props: { text: 'Test Button' } }
                  ]
                }),
                created_at: '2024-01-01T00:00:00Z',
                is_public: false
              }]
            })
          });
        } else if (route.request().method() === 'POST') {
          await route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              id: '2',
              name: 'New App',
              description: 'A new application',
              app_data: '{}',
              created_at: new Date().toISOString(),
              is_public: false
            })
          });
        }
      } else if (url.includes('/api/templates/')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            count: 2,
            results: [
              {
                id: '1',
                name: 'Basic Layout',
                category: 'business',
                components: {
                  header: { type: 'header', props: { title: 'Header' } },
                  content: { type: 'content', props: {} }
                },
                is_public: true
              },
              {
                id: '2',
                name: 'Portfolio Layout',
                category: 'portfolio',
                components: {
                  hero: { type: 'hero', props: { title: 'Portfolio' } },
                  gallery: { type: 'gallery', props: {} }
                },
                is_public: true
              }
            ]
          })
        });
      } else if (url.includes('/api/ai/suggestions/')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            suggestions: [
              {
                id: '1',
                type: 'layout',
                title: 'Add Container',
                description: 'Wrap components in a container',
                confidence: 0.8
              },
              {
                id: '2',
                type: 'styling',
                title: 'Improve Spacing',
                description: 'Add consistent spacing',
                confidence: 0.7
              }
            ]
          })
        });
      } else {
        await route.continue();
      }
    });

    // Navigate to the app
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
  });

  test('complete app building workflow', async ({ page }) => {
    // Test: App loads successfully
    await expect(page).toHaveTitle(/App Builder/);
    
    // Test: Main interface elements are visible
    await expect(page.locator('[data-testid="app-builder-main"]')).toBeVisible();
    
    // Test: Component palette is visible
    const componentPalette = page.locator('[data-testid="component-palette"]');
    if (await componentPalette.isVisible()) {
      await expect(componentPalette).toBeVisible();
    }

    // Test: Canvas area is visible
    const canvas = page.locator('[data-testid="canvas"]');
    if (await canvas.isVisible()) {
      await expect(canvas).toBeVisible();
    }

    // Test: Add a button component (if drag-and-drop is available)
    const buttonComponent = page.locator('[data-testid="component-button"]');
    if (await buttonComponent.isVisible()) {
      await buttonComponent.click();
      
      // Verify component was added
      await expect(page.locator('[data-testid="canvas"] button')).toBeVisible();
    }

    // Test: Property editor opens when component is selected
    const addedButton = page.locator('[data-testid="canvas"] button').first();
    if (await addedButton.isVisible()) {
      await addedButton.click();
      
      const propertyEditor = page.locator('[data-testid="property-editor"]');
      if (await propertyEditor.isVisible()) {
        await expect(propertyEditor).toBeVisible();
      }
    }
  });

  test('drag and drop functionality', async ({ page }) => {
    // Test drag and drop if elements are available
    const sourceElement = page.locator('[data-testid="draggable-button"]');
    const targetElement = page.locator('[data-testid="drop-zone"]');
    
    if (await sourceElement.isVisible() && await targetElement.isVisible()) {
      // Perform drag and drop
      await sourceElement.dragTo(targetElement);
      
      // Verify component was dropped
      await expect(targetElement.locator('button')).toBeVisible();
    }
  });

  test('template system workflow', async ({ page }) => {
    // Test: Open templates panel
    const templatesButton = page.locator('[data-testid="templates-button"]');
    if (await templatesButton.isVisible()) {
      await templatesButton.click();
      
      // Test: Templates are loaded and displayed
      await expect(page.locator('[data-testid="template-item"]')).toHaveCount(2);
      
      // Test: Apply a template
      const firstTemplate = page.locator('[data-testid="template-item"]').first();
      await firstTemplate.click();
      
      const applyButton = page.locator('[data-testid="apply-template-button"]');
      if (await applyButton.isVisible()) {
        await applyButton.click();
        
        // Verify template was applied
        await expect(page.locator('[data-testid="canvas"] [data-component-type="header"]')).toBeVisible();
      }
    }
  });

  test('AI suggestions workflow', async ({ page }) => {
    // Test: AI suggestions panel
    const aiButton = page.locator('[data-testid="ai-suggestions-button"]');
    if (await aiButton.isVisible()) {
      await aiButton.click();
      
      // Test: Generate suggestions
      const generateButton = page.locator('[data-testid="generate-suggestions-button"]');
      if (await generateButton.isVisible()) {
        await generateButton.click();
        
        // Wait for suggestions to load
        await page.waitForTimeout(1000);
        
        // Test: Suggestions are displayed
        await expect(page.locator('[data-testid="ai-suggestion"]')).toHaveCount(2);
        
        // Test: Apply a suggestion
        const firstSuggestion = page.locator('[data-testid="ai-suggestion"]').first();
        const applyButton = firstSuggestion.locator('[data-testid="apply-suggestion-button"]');
        if (await applyButton.isVisible()) {
          await applyButton.click();
          
          // Verify suggestion was applied (implementation specific)
          await page.waitForTimeout(500);
        }
      }
    }
  });

  test('code export functionality', async ({ page }) => {
    // Test: Open export panel
    const exportButton = page.locator('[data-testid="export-button"]');
    if (await exportButton.isVisible()) {
      await exportButton.click();
      
      // Test: Export format selection
      const reactOption = page.locator('[data-testid="export-format-react"]');
      if (await reactOption.isVisible()) {
        await reactOption.click();
        
        // Test: Generate export
        const generateExportButton = page.locator('[data-testid="generate-export-button"]');
        if (await generateExportButton.isVisible()) {
          await generateExportButton.click();
          
          // Wait for export to generate
          await page.waitForTimeout(2000);
          
          // Test: Download link appears
          const downloadLink = page.locator('[data-testid="download-link"]');
          if (await downloadLink.isVisible()) {
            await expect(downloadLink).toBeVisible();
          }
        }
      }
    }
  });

  test('collaboration features', async ({ page }) => {
    // Test: Collaboration panel
    const collaborationButton = page.locator('[data-testid="collaboration-button"]');
    if (await collaborationButton.isVisible()) {
      await collaborationButton.click();
      
      // Test: Add comment
      const addCommentButton = page.locator('[data-testid="add-comment-button"]');
      if (await addCommentButton.isVisible()) {
        await addCommentButton.click();
        
        // Test: Comment input
        const commentInput = page.locator('[data-testid="comment-input"]');
        if (await commentInput.isVisible()) {
          await commentInput.fill('This is a test comment');
          
          const submitButton = page.locator('[data-testid="submit-comment-button"]');
          if (await submitButton.isVisible()) {
            await submitButton.click();
            
            // Verify comment was added
            await expect(page.locator('[data-testid="comment-item"]')).toBeVisible();
          }
        }
      }
    }
  });

  test('responsive design preview', async ({ page }) => {
    // Test: Preview mode
    const previewButton = page.locator('[data-testid="preview-button"]');
    if (await previewButton.isVisible()) {
      await previewButton.click();
      
      // Test: Device size options
      const mobileButton = page.locator('[data-testid="mobile-preview"]');
      if (await mobileButton.isVisible()) {
        await mobileButton.click();
        
        // Verify mobile viewport
        const viewport = await page.viewportSize();
        // Mobile preview should change the preview area, not the actual viewport
        await expect(page.locator('[data-testid="preview-container"]')).toBeVisible();
      }
      
      const tabletButton = page.locator('[data-testid="tablet-preview"]');
      if (await tabletButton.isVisible()) {
        await tabletButton.click();
        await expect(page.locator('[data-testid="preview-container"]')).toBeVisible();
      }
      
      const desktopButton = page.locator('[data-testid="desktop-preview"]');
      if (await desktopButton.isVisible()) {
        await desktopButton.click();
        await expect(page.locator('[data-testid="preview-container"]')).toBeVisible();
      }
    }
  });

  test('save and load project', async ({ page }) => {
    // Test: Save project
    const saveButton = page.locator('[data-testid="save-button"]');
    if (await saveButton.isVisible()) {
      await saveButton.click();
      
      // Test: Save dialog
      const saveDialog = page.locator('[data-testid="save-dialog"]');
      if (await saveDialog.isVisible()) {
        const projectNameInput = page.locator('[data-testid="project-name-input"]');
        if (await projectNameInput.isVisible()) {
          await projectNameInput.fill('Test Project');
          
          const confirmSaveButton = page.locator('[data-testid="confirm-save-button"]');
          if (await confirmSaveButton.isVisible()) {
            await confirmSaveButton.click();
            
            // Wait for save to complete
            await page.waitForTimeout(1000);
            
            // Verify save success notification
            const notification = page.locator('[data-testid="notification"]');
            if (await notification.isVisible()) {
              await expect(notification).toContainText('saved');
            }
          }
        }
      }
    }

    // Test: Load project
    const loadButton = page.locator('[data-testid="load-button"]');
    if (await loadButton.isVisible()) {
      await loadButton.click();
      
      // Test: Project list
      const projectList = page.locator('[data-testid="project-list"]');
      if (await projectList.isVisible()) {
        const firstProject = page.locator('[data-testid="project-item"]').first();
        if (await firstProject.isVisible()) {
          await firstProject.click();
          
          // Verify project loaded
          await page.waitForTimeout(1000);
          await expect(page.locator('[data-testid="canvas"]')).toBeVisible();
        }
      }
    }
  });

  test('keyboard shortcuts', async ({ page }) => {
    // Test: Undo (Ctrl+Z)
    await page.keyboard.press('Control+z');
    await page.waitForTimeout(100);
    
    // Test: Redo (Ctrl+Y)
    await page.keyboard.press('Control+y');
    await page.waitForTimeout(100);
    
    // Test: Save (Ctrl+S)
    await page.keyboard.press('Control+s');
    await page.waitForTimeout(100);
    
    // Test: Copy (Ctrl+C) - if component is selected
    const component = page.locator('[data-testid="canvas"] [data-component]').first();
    if (await component.isVisible()) {
      await component.click();
      await page.keyboard.press('Control+c');
      await page.waitForTimeout(100);
      
      // Test: Paste (Ctrl+V)
      await page.keyboard.press('Control+v');
      await page.waitForTimeout(100);
    }
    
    // Test: Delete (Delete key)
    if (await component.isVisible()) {
      await component.click();
      await page.keyboard.press('Delete');
      await page.waitForTimeout(100);
    }
  });

  test('error handling', async ({ page }) => {
    // Test: Network error handling
    await page.route(`${API_URL}/api/**`, async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });

    // Try to perform an action that requires API call
    const saveButton = page.locator('[data-testid="save-button"]');
    if (await saveButton.isVisible()) {
      await saveButton.click();
      
      // Verify error message is displayed
      const errorMessage = page.locator('[data-testid="error-message"]');
      if (await errorMessage.isVisible()) {
        await expect(errorMessage).toBeVisible();
      }
    }
  });

  test('accessibility features', async ({ page }) => {
    // Test: Keyboard navigation
    await page.keyboard.press('Tab');
    await expect(page.locator(':focus')).toBeVisible();
    
    // Test: ARIA labels and roles
    const mainContent = page.locator('[role="main"]');
    if (await mainContent.isVisible()) {
      await expect(mainContent).toBeVisible();
    }
    
    // Test: Screen reader announcements
    const liveRegion = page.locator('[aria-live="polite"]');
    if (await liveRegion.isVisible()) {
      await expect(liveRegion).toBeVisible();
    }
    
    // Test: High contrast mode support
    await page.emulateMedia({ colorScheme: 'dark' });
    await page.waitForTimeout(500);
    
    // Verify dark theme is applied
    const body = page.locator('body');
    const backgroundColor = await body.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    // Dark theme should have dark background
    expect(backgroundColor).toBeDefined();
  });

  test('performance metrics', async ({ page }) => {
    // Measure page load time
    const startTime = Date.now();
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Page should load within reasonable time
    expect(loadTime).toBeLessThan(5000); // 5 seconds
    
    // Test: Memory usage
    const metrics = await page.evaluate(() => {
      return {
        memory: performance.memory ? {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        } : null,
        timing: performance.timing
      };
    });
    
    if (metrics.memory) {
      // Memory usage should be reasonable
      expect(metrics.memory.usedJSHeapSize).toBeLessThan(100 * 1024 * 1024); // 100MB
    }
  });
});
