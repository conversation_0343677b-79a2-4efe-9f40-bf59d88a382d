import React, { useState, useEffect } from 'react';
import { Select, Space, Typography, Divider } from 'antd';
import { BorderOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';
import NumberInput from './NumberInput';
import ColorInput from './ColorInput';

const { Text } = Typography;
const { Option } = Select;

const BorderContainer = styled.div`
  width: 100%;
`;

const BorderPreview = styled.div`
  width: 100%;
  height: 60px;
  margin: 12px 0;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: ${props => props.borderStyle || '1px solid #d9d9d9'};
`;

const PropertyRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
`;

const PropertyLabel = styled(Text)`
  min-width: 60px;
  font-size: 12px;
  font-weight: 500;
`;

/**
 * Visual border editor with style, width, and color controls
 */
const BorderEditor = ({
  value,
  onChange,
  showPreview = true,
  ...props
}) => {
  const [borderStyle, setBorderStyle] = useState('solid');
  const [borderWidth, setBorderWidth] = useState('1px');
  const [borderColor, setBorderColor] = useState('#d9d9d9');

  // Parse border value on mount and when value changes
  useEffect(() => {
    if (value) {
      const parsed = parseBorderValue(value);
      setBorderStyle(parsed.style);
      setBorderWidth(parsed.width);
      setBorderColor(parsed.color);
    }
  }, [value]);

  // Parse border value like "1px solid #000" or object
  const parseBorderValue = (val) => {
    if (!val) return { style: 'solid', width: '1px', color: '#d9d9d9' };
    
    if (typeof val === 'object') {
      return {
        style: val.style || 'solid',
        width: val.width || '1px',
        color: val.color || '#d9d9d9'
      };
    }
    
    if (typeof val === 'string') {
      // Parse string like "1px solid #000"
      const parts = val.split(/\s+/);
      
      let width = '1px';
      let style = 'solid';
      let color = '#d9d9d9';
      
      parts.forEach(part => {
        if (part.match(/^\d+(\.\d+)?(px|em|rem|%)$/)) {
          width = part;
        } else if (['none', 'solid', 'dashed', 'dotted', 'double', 'groove', 'ridge', 'inset', 'outset'].includes(part)) {
          style = part;
        } else if (part.startsWith('#') || part.startsWith('rgb') || part.startsWith('hsl') || isNamedColor(part)) {
          color = part;
        }
      });
      
      return { style, width, color };
    }
    
    return { style: 'solid', width: '1px', color: '#d9d9d9' };
  };

  // Check if a string is a named color
  const isNamedColor = (color) => {
    const namedColors = [
      'black', 'white', 'red', 'green', 'blue', 'yellow', 'orange', 'purple',
      'pink', 'brown', 'gray', 'grey', 'transparent'
    ];
    return namedColors.includes(color.toLowerCase());
  };

  // Format border value for output
  const formatBorderValue = (style, width, color) => {
    if (style === 'none') {
      return 'none';
    }
    return `${width} ${style} ${color}`;
  };

  // Handle style change
  const handleStyleChange = (newStyle) => {
    setBorderStyle(newStyle);
    const formattedValue = formatBorderValue(newStyle, borderWidth, borderColor);
    onChange?.(formattedValue);
  };

  // Handle width change
  const handleWidthChange = (newWidth) => {
    setBorderWidth(newWidth);
    const formattedValue = formatBorderValue(borderStyle, newWidth, borderColor);
    onChange?.(formattedValue);
  };

  // Handle color change
  const handleColorChange = (newColor) => {
    setBorderColor(newColor);
    const formattedValue = formatBorderValue(borderStyle, borderWidth, newColor);
    onChange?.(formattedValue);
  };

  const borderStyles = [
    { value: 'none', label: 'None' },
    { value: 'solid', label: 'Solid' },
    { value: 'dashed', label: 'Dashed' },
    { value: 'dotted', label: 'Dotted' },
    { value: 'double', label: 'Double' },
    { value: 'groove', label: 'Groove' },
    { value: 'ridge', label: 'Ridge' },
    { value: 'inset', label: 'Inset' },
    { value: 'outset', label: 'Outset' }
  ];

  const currentBorderStyle = formatBorderValue(borderStyle, borderWidth, borderColor);

  return (
    <BorderContainer>
      <Space direction="vertical" style={{ width: '100%' }}>
        <PropertyRow>
          <PropertyLabel>Style:</PropertyLabel>
          <Select
            value={borderStyle}
            onChange={handleStyleChange}
            style={{ flex: 1 }}
            size="small"
          >
            {borderStyles.map(style => (
              <Option key={style.value} value={style.value}>
                {style.label}
              </Option>
            ))}
          </Select>
        </PropertyRow>

        {borderStyle !== 'none' && (
          <>
            <PropertyRow>
              <PropertyLabel>Width:</PropertyLabel>
              <div style={{ flex: 1 }}>
                <NumberInput
                  value={borderWidth}
                  onChange={handleWidthChange}
                  min={0}
                  max={20}
                  step={1}
                  unit="px"
                  units={['px', 'em', 'rem']}
                  size="small"
                />
              </div>
            </PropertyRow>

            <PropertyRow>
              <PropertyLabel>Color:</PropertyLabel>
              <div style={{ flex: 1 }}>
                <ColorInput
                  value={borderColor}
                  onChange={handleColorChange}
                  placeholder="Border color"
                />
              </div>
            </PropertyRow>
          </>
        )}

        {showPreview && (
          <>
            <Divider style={{ margin: '8px 0' }} />
            <Text style={{ fontSize: '12px', marginBottom: '4px' }}>Preview:</Text>
            <BorderPreview borderStyle={currentBorderStyle}>
              <BorderOutlined style={{ fontSize: '24px', color: '#8c8c8c' }} />
            </BorderPreview>
            <Text type="secondary" style={{ fontSize: '11px', textAlign: 'center' }}>
              {currentBorderStyle}
            </Text>
          </>
        )}
      </Space>
    </BorderContainer>
  );
};

export default BorderEditor;
