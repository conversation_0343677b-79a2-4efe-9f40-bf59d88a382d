import React, { useState, useCallback, useMemo } from 'react';
import { Button, Space, Tooltip, Select, Badge } from 'antd';
import { 
  MobileOutlined, 
  TabletOutlined, 
  DesktopOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  FullscreenOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Option } = Select;

// Device configurations with detailed specifications
export const DEVICE_PRESETS = {
  mobile: {
    name: 'Mobile',
    icon: <MobileOutlined />,
    variants: {
      'iphone-se': { name: 'iPhone SE', width: 375, height: 667, scale: 0.8 },
      'iphone-12': { name: 'iPhone 12', width: 390, height: 844, scale: 0.7 },
      'pixel-5': { name: 'Pixel 5', width: 393, height: 851, scale: 0.7 },
      'samsung-s21': { name: 'Samsung S21', width: 384, height: 854, scale: 0.7 }
    },
    defaultVariant: 'iphone-12',
    frame: true,
    category: 'mobile'
  },
  tablet: {
    name: 'Tablet',
    icon: <TabletOutlined />,
    variants: {
      'ipad': { name: 'iPad', width: 768, height: 1024, scale: 0.6 },
      'ipad-pro': { name: 'iPad Pro', width: 1024, height: 1366, scale: 0.5 },
      'surface': { name: 'Surface Pro', width: 912, height: 1368, scale: 0.5 },
      'galaxy-tab': { name: 'Galaxy Tab', width: 800, height: 1280, scale: 0.6 }
    },
    defaultVariant: 'ipad',
    frame: true,
    category: 'tablet'
  },
  desktop: {
    name: 'Desktop',
    icon: <DesktopOutlined />,
    variants: {
      'laptop': { name: 'Laptop', width: 1366, height: 768, scale: 0.7 },
      'desktop': { name: 'Desktop', width: 1920, height: 1080, scale: 0.5 },
      'ultrawide': { name: 'Ultrawide', width: 2560, height: 1080, scale: 0.4 },
      'custom': { name: 'Custom', width: 1200, height: 800, scale: 0.8 }
    },
    defaultVariant: 'laptop',
    frame: false,
    category: 'desktop'
  }
};

// Styled components
const DeviceFrameContainer = styled.div`
  position: relative;
  margin: 20px auto;
  transition: all 0.3s ease;
  transform: ${props => props.orientation === 'landscape' ? 'rotate(0deg)' : 'rotate(0deg)'};
`;

const DeviceFrame = styled.div`
  position: relative;
  background: ${props => {
    if (props.category === 'mobile') return '#333';
    if (props.category === 'tablet') return '#444';
    return 'transparent';
  }};
  border-radius: ${props => {
    if (props.category === 'mobile') return '25px';
    if (props.category === 'tablet') return '15px';
    return '8px';
  }};
  padding: ${props => {
    if (props.category === 'mobile') return '20px 10px';
    if (props.category === 'tablet') return '15px';
    return '0';
  }};
  box-shadow: ${props => props.frame ? '0 8px 32px rgba(0, 0, 0, 0.3)' : 'none'};
  transition: all 0.3s ease;
  
  ${props => props.category === 'mobile' && `
    &::before {
      content: '';
      position: absolute;
      top: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: #666;
      border-radius: 2px;
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 40px;
      border: 2px solid #666;
      border-radius: 50%;
    }
  `}
  
  ${props => props.category === 'tablet' && `
    &::before {
      content: '';
      position: absolute;
      bottom: 6px;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 30px;
      border: 2px solid #666;
      border-radius: 50%;
    }
  `}
`;

const DeviceScreen = styled.div`
  width: ${props => props.orientation === 'landscape' ? props.height : props.width}px;
  height: ${props => props.orientation === 'landscape' ? props.width : props.height}px;
  max-width: 100%;
  max-height: 100%;
  background: white;
  border-radius: ${props => {
    if (props.category === 'mobile') return '8px';
    if (props.category === 'tablet') return '6px';
    return '4px';
  }};
  overflow: auto;
  position: relative;
  transform: scale(${props => props.scale});
  transform-origin: top center;
  transition: all 0.3s ease;
  
  @media (max-width: 1200px) {
    transform: scale(${props => Math.min(props.scale, 0.8)});
  }
  
  @media (max-width: 768px) {
    transform: scale(${props => Math.min(props.scale, 0.6)});
  }
`;

const DeviceControls = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
`;

const DeviceInfo = styled.div`
  position: absolute;
  top: -30px;
  left: 0;
  font-size: 12px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

/**
 * DevicePreviewFrame Component
 * Provides device-specific preview frames with orientation controls
 */
const DevicePreviewFrame = ({ 
  children, 
  onDeviceChange,
  initialDevice = 'desktop',
  initialVariant = null,
  showControls = true,
  showInfo = true,
  className 
}) => {
  const [currentDevice, setCurrentDevice] = useState(initialDevice);
  const [currentVariant, setCurrentVariant] = useState(
    initialVariant || DEVICE_PRESETS[initialDevice].defaultVariant
  );
  const [orientation, setOrientation] = useState('portrait');
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Get current device configuration
  const deviceConfig = useMemo(() => {
    const device = DEVICE_PRESETS[currentDevice];
    const variant = device.variants[currentVariant];
    return {
      ...device,
      ...variant,
      category: device.category
    };
  }, [currentDevice, currentVariant]);

  // Handle device type change
  const handleDeviceChange = useCallback((newDevice) => {
    setCurrentDevice(newDevice);
    const newVariant = DEVICE_PRESETS[newDevice].defaultVariant;
    setCurrentVariant(newVariant);
    
    if (onDeviceChange) {
      onDeviceChange({
        device: newDevice,
        variant: newVariant,
        config: {
          ...DEVICE_PRESETS[newDevice],
          ...DEVICE_PRESETS[newDevice].variants[newVariant]
        }
      });
    }
  }, [onDeviceChange]);

  // Handle variant change
  const handleVariantChange = useCallback((newVariant) => {
    setCurrentVariant(newVariant);
    
    if (onDeviceChange) {
      onDeviceChange({
        device: currentDevice,
        variant: newVariant,
        config: {
          ...DEVICE_PRESETS[currentDevice],
          ...DEVICE_PRESETS[currentDevice].variants[newVariant]
        }
      });
    }
  }, [currentDevice, onDeviceChange]);

  // Handle orientation change
  const handleOrientationChange = useCallback(() => {
    const newOrientation = orientation === 'portrait' ? 'landscape' : 'portrait';
    setOrientation(newOrientation);
  }, [orientation]);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  return (
    <div className={className}>
      {showControls && (
        <DeviceControls>
          {/* Device Type Selector */}
          <Space>
            {Object.entries(DEVICE_PRESETS).map(([key, device]) => (
              <Button
                key={key}
                type={currentDevice === key ? 'primary' : 'default'}
                icon={device.icon}
                size="small"
                onClick={() => handleDeviceChange(key)}
              >
                {device.name}
              </Button>
            ))}
          </Space>

          {/* Device Variant Selector */}
          <Select
            value={currentVariant}
            onChange={handleVariantChange}
            size="small"
            style={{ minWidth: 120 }}
          >
            {Object.entries(deviceConfig.variants || {}).map(([key, variant]) => (
              <Option key={key} value={key}>
                {variant.name}
              </Option>
            ))}
          </Select>

          {/* Orientation Controls */}
          {deviceConfig.category !== 'desktop' && (
            <Tooltip title={`Switch to ${orientation === 'portrait' ? 'landscape' : 'portrait'}`}>
              <Button
                icon={orientation === 'portrait' ? <RotateRightOutlined /> : <RotateLeftOutlined />}
                size="small"
                onClick={handleOrientationChange}
              />
            </Tooltip>
          )}

          {/* Fullscreen Toggle */}
          <Tooltip title="Toggle Fullscreen">
            <Button
              icon={<FullscreenOutlined />}
              size="small"
              onClick={handleFullscreenToggle}
              type={isFullscreen ? 'primary' : 'default'}
            />
          </Tooltip>

          {/* Device Info Badge */}
          <Badge 
            count={`${deviceConfig.width}×${deviceConfig.height}`}
            style={{ backgroundColor: '#108ee9' }}
          />
        </DeviceControls>
      )}

      <DeviceFrameContainer orientation={orientation}>
        {showInfo && (
          <DeviceInfo>
            {deviceConfig.name} - {deviceConfig.width}×{deviceConfig.height} 
            {orientation === 'landscape' && ' (Landscape)'}
          </DeviceInfo>
        )}
        
        <DeviceFrame
          category={deviceConfig.category}
          frame={deviceConfig.frame}
        >
          <DeviceScreen
            width={deviceConfig.width}
            height={deviceConfig.height}
            scale={deviceConfig.scale}
            orientation={orientation}
            category={deviceConfig.category}
          >
            {children}
          </DeviceScreen>
        </DeviceFrame>
      </DeviceFrameContainer>
    </div>
  );
};

export default DevicePreviewFrame;
