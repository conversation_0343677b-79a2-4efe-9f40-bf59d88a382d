import WebSocketClient from '../services/WebSocketClient';
import { getWebSocketUrl } from '../config/env';

// Mock the WebSocket class
class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = 0; // CONNECTING
    this.CONNECTING = 0;
    this.OPEN = 1;
    this.CLOSING = 2;
    this.CLOSED = 3;
    
    // Callbacks
    this.onopen = null;
    this.onclose = null;
    this.onmessage = null;
    this.onerror = null;
    
    // Methods
    this.close = jest.fn();
    this.send = jest.fn();
    
    // Simulate connection
    setTimeout(() => {
      this.readyState = 1; // OPEN
      if (this.onopen) this.onopen({ target: this });
    }, 0);
  }
}

// Mock the getWebSocketUrl function
jest.mock('../config/env', () => ({
  getWebSocketUrl: jest.fn(() => 'ws://localhost:8000/ws/app_builder/'),
  WS_URL: 'ws://localhost:8000/ws',
  WS_HOST: 'localhost:8000',
  WS_ENDPOINT: 'app_builder',
}));

describe('WebSocketClient', () => {
  let client;
  let mockWebSocket;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock the WebSocket constructor
    global.WebSocket = jest.fn((url) => {
      mockWebSocket = new MockWebSocket(url);
      return mockWebSocket;
    });
    
    // Create a new client
    client = new WebSocketClient();
  });
  
  afterEach(() => {
    // Clean up
    if (client) {
      client.disconnect();
    }
  });
  
  test('connects to the WebSocket server', () => {
    // Connect to the server
    client.connect();
    
    // Check if the WebSocket constructor was called with the correct URL
    expect(getWebSocketUrl).toHaveBeenCalled();
    expect(global.WebSocket).toHaveBeenCalledWith('ws://localhost:8000/ws/app_builder/');
  });
  
  test('sends messages to the server', () => {
    // Connect to the server
    client.connect();
    
    // Send a message
    const message = { type: 'test', data: { foo: 'bar' } };
    client.send(message);
    
    // Check if the send method was called with the correct message
    expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify(message));
  });
  
  test('receives messages from the server', () => {
    // Connect to the server
    client.connect();
    
    // Create a mock message handler
    const messageHandler = jest.fn();
    client.onMessage(messageHandler);
    
    // Simulate receiving a message
    const message = { type: 'test', data: { foo: 'bar' } };
    mockWebSocket.onmessage({ data: JSON.stringify(message) });
    
    // Check if the message handler was called with the correct message
    expect(messageHandler).toHaveBeenCalledWith(message);
  });
  
  test('handles connection open', () => {
    // Create a mock open handler
    const openHandler = jest.fn();
    client.onOpen(openHandler);
    
    // Connect to the server
    client.connect();
    
    // Simulate connection open
    mockWebSocket.onopen({ target: mockWebSocket });
    
    // Check if the open handler was called
    expect(openHandler).toHaveBeenCalled();
  });
  
  test('handles connection close', () => {
    // Connect to the server
    client.connect();
    
    // Create a mock close handler
    const closeHandler = jest.fn();
    client.onClose(closeHandler);
    
    // Simulate connection close
    mockWebSocket.onclose({ code: 1000, reason: 'Normal closure' });
    
    // Check if the close handler was called with the correct code and reason
    expect(closeHandler).toHaveBeenCalledWith(1000, 'Normal closure');
  });
  
  test('handles connection error', () => {
    // Connect to the server
    client.connect();
    
    // Create a mock error handler
    const errorHandler = jest.fn();
    client.onError(errorHandler);
    
    // Simulate connection error
    const error = new Error('Connection error');
    mockWebSocket.onerror({ error });
    
    // Check if the error handler was called with the correct error
    expect(errorHandler).toHaveBeenCalledWith(error);
  });
  
  test('disconnects from the server', () => {
    // Connect to the server
    client.connect();
    
    // Disconnect from the server
    client.disconnect();
    
    // Check if the close method was called
    expect(mockWebSocket.close).toHaveBeenCalled();
  });
  
  test('reconnects to the server', () => {
    // Connect to the server
    client.connect();
    
    // Reset the mock
    global.WebSocket.mockClear();
    
    // Reconnect to the server
    client.reconnect();
    
    // Check if the WebSocket constructor was called again
    expect(global.WebSocket).toHaveBeenCalledWith('ws://localhost:8000/ws/app_builder/');
  });
  
  test('gets the connection state', () => {
    // Connect to the server
    client.connect();
    
    // Check the connection state
    expect(client.getState()).toBe(mockWebSocket.readyState);
  });
});
