import React, { useState, useMemo } from 'react';
import { Card, List, Input, Select, Button, Space, Typography, Badge, Empty, Tooltip } from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined, 
  EyeOutlined, 
  DeleteOutlined,
  FilterOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  MobileOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

/**
 * Template Gallery
 * 
 * Displays a gallery of available templates with search, filter, and preview functionality.
 */

const TemplateGallery = ({
  templates = [],
  loading = false,
  onLoad,
  onDelete,
  onPreview,
  showActions = true,
  selectable = false,
  selectedTemplates = [],
  onSelectionChange
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // Get unique categories and types
  const categories = useMemo(() => {
    const cats = [...new Set(templates.map(t => t.category).filter(Boolean))];
    return cats.sort();
  }, [templates]);

  const types = useMemo(() => {
    const typeSet = [...new Set(templates.map(t => t.type || 'layout').filter(Boolean))];
    return typeSet.sort();
  }, [templates]);

  // Filter templates
  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      const matchesSearch = !searchTerm || 
        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
      const matchesType = typeFilter === 'all' || (template.type || 'layout') === typeFilter;
      
      return matchesSearch && matchesCategory && matchesType;
    });
  }, [templates, searchTerm, categoryFilter, typeFilter]);

  const getTypeIcon = (type) => {
    switch (type) {
      case 'app': return <AppstoreOutlined />;
      case 'mobile': return <MobileOutlined />;
      default: return <LayoutOutlined />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'app': return '#722ed1';
      case 'mobile': return '#13c2c2';
      default: return '#1890ff';
    }
  };

  const renderTemplateCard = (template) => (
    <Card
      hoverable
      cover={
        template.thumbnail ? (
          <img
            alt={template.name}
            src={template.thumbnail}
            style={{ height: 160, objectFit: 'cover' }}
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />
        ) : (
          <div style={{
            height: 160,
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#8c8c8c'
          }}>
            {getTypeIcon(template.type)}
          </div>
        )
      }
      actions={showActions ? [
        <Tooltip key="load" title="Load Template">
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => onLoad && onLoad(template)}
          />
        </Tooltip>,
        <Tooltip key="preview" title="Preview">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => onPreview && onPreview(template)}
          />
        </Tooltip>,
        ...(template.canDelete ? [
          <Tooltip key="delete" title="Delete">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => onDelete && onDelete(template)}
            />
          </Tooltip>
        ] : [])
      ] : []}
      style={{ height: '100%' }}
    >
      <Card.Meta
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{template.name}</span>
            <Badge
              color={getTypeColor(template.type)}
              text={template.type || 'layout'}
              style={{ fontSize: 11 }}
            />
          </div>
        }
        description={
          <div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {template.description}
            </Text>
            <div style={{ marginTop: 8 }}>
              <Space size={4}>
                {template.category && (
                  <Badge status="default" text={template.category} />
                )}
                {template.isPublic && (
                  <Badge status="success" text="Public" />
                )}
                {template.componentCount && (
                  <Text type="secondary" style={{ fontSize: 11 }}>
                    {template.componentCount} components
                  </Text>
                )}
              </Space>
            </div>
          </div>
        }
      />
    </Card>
  );

  return (
    <div>
      {/* Filters */}
      <div style={{ marginBottom: 16 }}>
        <Space wrap>
          <Search
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ width: 200 }}
            prefix={<SearchOutlined />}
          />
          
          <Select
            value={categoryFilter}
            onChange={setCategoryFilter}
            style={{ width: 120 }}
            suffixIcon={<FilterOutlined />}
          >
            <Option value="all">All Categories</Option>
            {categories.map(category => (
              <Option key={category} value={category}>
                {category}
              </Option>
            ))}
          </Select>
          
          <Select
            value={typeFilter}
            onChange={setTypeFilter}
            style={{ width: 100 }}
            suffixIcon={<FilterOutlined />}
          >
            <Option value="all">All Types</Option>
            {types.map(type => (
              <Option key={type} value={type}>
                {type}
              </Option>
            ))}
          </Select>
        </Space>
      </div>

      {/* Results count */}
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} found
        </Text>
      </div>

      {/* Template grid */}
      {filteredTemplates.length > 0 ? (
        <List
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 2,
            lg: 3,
            xl: 3,
            xxl: 4
          }}
          dataSource={filteredTemplates}
          loading={loading}
          renderItem={(template) => (
            <List.Item>
              {renderTemplateCard(template)}
            </List.Item>
          )}
          pagination={{
            pageSize: 12,
            showSizeChanger: false,
            showQuickJumper: true,
            hideOnSinglePage: true
          }}
        />
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            searchTerm || categoryFilter !== 'all' || typeFilter !== 'all'
              ? "No templates match your filters"
              : "No templates available"
          }
          style={{ padding: '40px 0' }}
        >
          {(searchTerm || categoryFilter !== 'all' || typeFilter !== 'all') && (
            <Button
              type="primary"
              onClick={() => {
                setSearchTerm('');
                setCategoryFilter('all');
                setTypeFilter('all');
              }}
            >
              Clear Filters
            </Button>
          )}
        </Empty>
      )}
    </div>
  );
};

export default TemplateGallery;
