/**
 * Performance optimization utilities for React applications
 */

// Initialize performance optimization
export const initPerformanceOptimizer = () => {
  // Set up performance observers
  if (typeof PerformanceObserver !== 'undefined') {
    try {
      // Create observer for long tasks
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.warn(`Long task detected: ${entry.duration.toFixed(2)}ms`, entry);
        });
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });

      // Create observer for resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 1000) {
            console.warn(`Slow resource load: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });

      // Create observer for navigation timing
      const navigationObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.log('Navigation timing:', {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.navigationStart,
            load: entry.loadEventEnd - entry.navigationStart,
            firstPaint: entry.responseEnd - entry.navigationStart,
            ttfb: entry.responseStart - entry.requestStart
          });
        });
      });
      navigationObserver.observe({ entryTypes: ['navigation'] });
    } catch (error) {
      console.error('Error setting up performance observers:', error);
    }
  }

  // Set up error tracking
  window.addEventListener('error', (event) => {
    console.error('Global error caught:', event.error);
    // You could send this to an error tracking service
  });

  // Set up unhandled promise rejection tracking
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    // You could send this to an error tracking service
  });

  return {
    clearPerformanceMetrics: () => {
      if (typeof performance !== 'undefined' && performance.clearMarks) {
        performance.clearMarks();
        performance.clearMeasures();
      }
    }
  };
};

// Track component render times
export const trackRenderTime = (componentName) => {
  const startTime = performance.now();

  return () => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    if (renderTime > 16) { // 60fps threshold (16.67ms)
      console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms to render`);
    }

    return renderTime;
  };
};

// Custom hook for monitoring component performance
export const usePerformanceMonitor = (componentName) => {
  if (process.env.NODE_ENV === 'production') {
    return { startMeasure: () => { }, endMeasure: () => { } };
  }

  return {
    startMeasure: () => {
      performance.mark(`${componentName}-start`);
    },
    endMeasure: () => {
      performance.mark(`${componentName}-end`);
      performance.measure(
        `${componentName} render time`,
        `${componentName}-start`,
        `${componentName}-end`
      );

      const measurements = performance.getEntriesByName(`${componentName} render time`);
      const lastMeasurement = measurements[measurements.length - 1];

      if (lastMeasurement && lastMeasurement.duration > 16) {
        console.warn(`Slow render detected: ${componentName} took ${lastMeasurement.duration.toFixed(2)}ms to render`);
      }

      // Clean up marks and measures
      performance.clearMarks(`${componentName}-start`);
      performance.clearMarks(`${componentName}-end`);
      performance.clearMeasures(`${componentName} render time`);
    }
  };
};

// Debounce function to limit how often a function can be called
export const debounce = (func, wait = 300) => {
  let timeout;

  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle function to limit how often a function can be called
export const throttle = (func, limit = 300) => {
  let inThrottle;

  return function executedFunction(...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

// Memoize function results
export const memoize = (func) => {
  const cache = new Map();

  return (...args) => {
    const key = JSON.stringify(args);

    if (cache.has(key)) {
      return cache.get(key);
    }

    const result = func(...args);
    cache.set(key, result);

    return result;
  };
};

// Lazy load components or resources
export const lazyLoad = (importFunc) => {
  return React.lazy(importFunc);
};

// Check if the browser supports requestIdleCallback
export const hasIdleCallback = () => {
  return typeof window !== 'undefined' && 'requestIdleCallback' in window;
};

// Run a function during browser idle time
export const runWhenIdle = (callback, options = { timeout: 1000 }) => {
  if (hasIdleCallback()) {
    return window.requestIdleCallback(callback, options);
  } else {
    return setTimeout(callback, 1);
  }
};

// Cancel a previously scheduled idle callback
export const cancelIdleCallback = (id) => {
  if (hasIdleCallback()) {
    window.cancelIdleCallback(id);
  } else {
    clearTimeout(id);
  }
};

// Default export
export default {
  trackRenderTime,
  usePerformanceMonitor,
  debounce,
  throttle,
  memoize,
  lazyLoad,
  runWhenIdle,
  cancelIdleCallback
};
