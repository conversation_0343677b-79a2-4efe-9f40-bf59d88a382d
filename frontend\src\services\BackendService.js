/**
 * Backend Service
 * 
 * This service provides methods for interacting with the backend API.
 * It uses the ApiClient for making requests and handles data transformation.
 */

import apiClient from './ApiClient';
import { handleApiError } from '../utils/errorHandler';

class BackendService {
  constructor() {
    this.apiClient = apiClient;
    this.endpoints = {
      appData: '/api/app-data',
      components: '/api/components',
      templates: '/api/templates',
      projects: '/api/projects',
      users: '/api/users',
      auth: '/api/auth'
    };
  }

  /**
   * Initialize the backend service
   * @returns {Promise<Object>} Initialization result
   */
  async initialize() {
    try {
      // Check if backend is available
      const status = await this.getStatus();
      
      console.log('Backend service initialized:', status);
      
      return {
        initialized: true,
        status
      };
    } catch (error) {
      console.warn('Backend service initialization failed:', error);
      
      // In development mode, we can continue with mock data
      if (process.env.NODE_ENV === 'development') {
        console.log('Development mode: Using mock data');
        
        return {
          initialized: true,
          mock: true
        };
      }
      
      throw error;
    }
  }

  /**
   * Get backend status
   * @returns {Promise<Object>} Backend status
   */
  async getStatus() {
    try {
      return await this.apiClient.get('/status');
    } catch (error) {
      return handleApiError(error, '/status', {
        fallbackData: {
          status: 'unknown',
          version: '1.0.0',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  /**
   * Get app data
   * @returns {Promise<Object>} App data
   */
  async getAppData() {
    try {
      return await this.apiClient.get(this.endpoints.appData);
    } catch (error) {
      return handleApiError(error, this.endpoints.appData, {
        fallbackData: {
          app: {
            name: 'App Builder',
            version: '1.0.0',
            components: [],
            layouts: [],
            styles: {},
            status: 'offline'
          }
        }
      });
    }
  }

  /**
   * Get components
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Components
   */
  async getComponents(options = {}) {
    const { page = 1, limit = 20, search = '', type = '' } = options;
    
    try {
      return await this.apiClient.get(this.endpoints.components, {
        params: { page, limit, search, type }
      });
    } catch (error) {
      return handleApiError(error, this.endpoints.components, {
        fallbackData: { components: [], total: 0 }
      });
    }
  }

  /**
   * Get component by ID
   * @param {string} id - Component ID
   * @returns {Promise<Object>} Component
   */
  async getComponent(id) {
    try {
      return await this.apiClient.get(`${this.endpoints.components}/${id}`);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.components}/${id}`, {
        fallbackData: null
      });
    }
  }

  /**
   * Create component
   * @param {Object} component - Component data
   * @returns {Promise<Object>} Created component
   */
  async createComponent(component) {
    try {
      return await this.apiClient.post(this.endpoints.components, component);
    } catch (error) {
      return handleApiError(error, this.endpoints.components);
    }
  }

  /**
   * Update component
   * @param {string} id - Component ID
   * @param {Object} component - Component data
   * @returns {Promise<Object>} Updated component
   */
  async updateComponent(id, component) {
    try {
      return await this.apiClient.put(`${this.endpoints.components}/${id}`, component);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.components}/${id}`);
    }
  }

  /**
   * Delete component
   * @param {string} id - Component ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteComponent(id) {
    try {
      return await this.apiClient.delete(`${this.endpoints.components}/${id}`);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.components}/${id}`);
    }
  }

  /**
   * Get templates
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Templates
   */
  async getTemplates(options = {}) {
    const { page = 1, limit = 20, search = '', category = '' } = options;
    
    try {
      return await this.apiClient.get(this.endpoints.templates, {
        params: { page, limit, search, category }
      });
    } catch (error) {
      return handleApiError(error, this.endpoints.templates, {
        fallbackData: { templates: [], total: 0 }
      });
    }
  }

  /**
   * Get template by ID
   * @param {string} id - Template ID
   * @returns {Promise<Object>} Template
   */
  async getTemplate(id) {
    try {
      return await this.apiClient.get(`${this.endpoints.templates}/${id}`);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.templates}/${id}`, {
        fallbackData: null
      });
    }
  }

  /**
   * Create project from template
   * @param {string} templateId - Template ID
   * @param {Object} projectData - Project data
   * @returns {Promise<Object>} Created project
   */
  async createProjectFromTemplate(templateId, projectData) {
    try {
      return await this.apiClient.post(`${this.endpoints.templates}/${templateId}/create-project`, projectData);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.templates}/${templateId}/create-project`);
    }
  }

  /**
   * Get projects
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Projects
   */
  async getProjects(options = {}) {
    const { page = 1, limit = 20, search = '', status = '' } = options;
    
    try {
      return await this.apiClient.get(this.endpoints.projects, {
        params: { page, limit, search, status }
      });
    } catch (error) {
      return handleApiError(error, this.endpoints.projects, {
        fallbackData: { projects: [], total: 0 }
      });
    }
  }

  /**
   * Get project by ID
   * @param {string} id - Project ID
   * @returns {Promise<Object>} Project
   */
  async getProject(id) {
    try {
      return await this.apiClient.get(`${this.endpoints.projects}/${id}`);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.projects}/${id}`, {
        fallbackData: null
      });
    }
  }

  /**
   * Create project
   * @param {Object} project - Project data
   * @returns {Promise<Object>} Created project
   */
  async createProject(project) {
    try {
      return await this.apiClient.post(this.endpoints.projects, project);
    } catch (error) {
      return handleApiError(error, this.endpoints.projects);
    }
  }

  /**
   * Update project
   * @param {string} id - Project ID
   * @param {Object} project - Project data
   * @returns {Promise<Object>} Updated project
   */
  async updateProject(id, project) {
    try {
      return await this.apiClient.put(`${this.endpoints.projects}/${id}`, project);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.projects}/${id}`);
    }
  }

  /**
   * Delete project
   * @param {string} id - Project ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteProject(id) {
    try {
      return await this.apiClient.delete(`${this.endpoints.projects}/${id}`);
    } catch (error) {
      return handleApiError(error, `${this.endpoints.projects}/${id}`);
    }
  }
}

// Create singleton instance
const backendService = new BackendService();

export default backendService;
