import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { checkServiceAccessibility } from '../utils/serviceAccessibilityChecker';

/**
 * Service Status Indicator Component
 * 
 * This component displays the status of all services in a compact way.
 * It shows a colored indicator for each service and provides a link to the Service Status Page.
 */
const ServiceStatusIndicator = ({ showLabels = false, compact = true }) => {
  const [serviceStatus, setServiceStatus] = useState({
    api: { status: 'pending' },
    websocket: { status: 'pending' },
    graphql: { status: 'pending' },
    serviceWorker: { status: 'pending' }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check service status on mount and periodically
  useEffect(() => {
    let mounted = true;
    let intervalId;

    const checkStatus = async () => {
      try {
        setLoading(true);
        
        // Create a new checker instance
        const checker = new checkServiceAccessibility();
        
        // Check all services
        const results = await checker.checkAll();
        
        // Update state if component is still mounted
        if (mounted) {
          setServiceStatus(results);
          setLoading(false);
          setError(null);
        }
      } catch (err) {
        if (mounted) {
          setError(err.message);
          setLoading(false);
        }
      }
    };

    // Check status initially
    checkStatus();

    // Check status every 60 seconds
    intervalId = setInterval(checkStatus, 60000);

    return () => {
      mounted = false;
      clearInterval(intervalId);
    };
  }, []);

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return '#52c41a'; // green
      case 'warning':
        return '#faad14'; // yellow
      case 'error':
        return '#f5222d'; // red
      case 'pending':
      default:
        return '#8c8c8c'; // gray
    }
  };

  // Get overall status
  const getOverallStatus = () => {
    const statuses = Object.values(serviceStatus).map(s => s.status);
    
    if (statuses.includes('error')) {
      return 'error';
    }
    
    if (statuses.includes('warning')) {
      return 'warning';
    }
    
    if (statuses.every(s => s === 'success')) {
      return 'success';
    }
    
    return 'pending';
  };

  // Styles
  const styles = {
    container: {
      display: 'flex',
      alignItems: 'center',
      padding: compact ? '4px 8px' : '8px 12px',
      borderRadius: '4px',
      backgroundColor: 'rgba(0, 0, 0, 0.05)',
      cursor: 'pointer',
      transition: 'background-color 0.3s',
      textDecoration: 'none',
      color: 'inherit'
    },
    indicator: {
      display: 'inline-block',
      width: '10px',
      height: '10px',
      borderRadius: '50%',
      marginRight: '8px'
    },
    serviceIndicator: {
      display: 'inline-block',
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      marginRight: '4px'
    },
    servicesContainer: {
      display: 'flex',
      alignItems: 'center',
      marginLeft: '8px'
    },
    serviceItem: {
      display: 'flex',
      alignItems: 'center',
      marginRight: '8px',
      fontSize: '12px'
    },
    label: {
      fontSize: '12px',
      fontWeight: 'bold'
    }
  };

  // Render compact indicator
  if (compact) {
    return (
      <Link to="/service-status" style={{ textDecoration: 'none' }}>
        <div style={styles.container}>
          <div 
            style={{ 
              ...styles.indicator, 
              backgroundColor: getStatusColor(getOverallStatus()) 
            }} 
          />
          <div style={styles.label}>Services</div>
        </div>
      </Link>
    );
  }

  // Render detailed indicator
  return (
    <Link to="/service-status" style={{ textDecoration: 'none' }}>
      <div style={styles.container}>
        <div 
          style={{ 
            ...styles.indicator, 
            backgroundColor: getStatusColor(getOverallStatus()) 
          }} 
        />
        <div style={styles.label}>Services</div>
        
        <div style={styles.servicesContainer}>
          <div style={styles.serviceItem}>
            <div 
              style={{ 
                ...styles.serviceIndicator, 
                backgroundColor: getStatusColor(serviceStatus.api.status) 
              }} 
            />
            {showLabels && <span>API</span>}
          </div>
          
          <div style={styles.serviceItem}>
            <div 
              style={{ 
                ...styles.serviceIndicator, 
                backgroundColor: getStatusColor(serviceStatus.websocket.status) 
              }} 
            />
            {showLabels && <span>WebSocket</span>}
          </div>
          
          <div style={styles.serviceItem}>
            <div 
              style={{ 
                ...styles.serviceIndicator, 
                backgroundColor: getStatusColor(serviceStatus.graphql.status) 
              }} 
            />
            {showLabels && <span>GraphQL</span>}
          </div>
          
          <div style={styles.serviceItem}>
            <div 
              style={{ 
                ...styles.serviceIndicator, 
                backgroundColor: getStatusColor(serviceStatus.serviceWorker.status) 
              }} 
            />
            {showLabels && <span>SW</span>}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ServiceStatusIndicator;
