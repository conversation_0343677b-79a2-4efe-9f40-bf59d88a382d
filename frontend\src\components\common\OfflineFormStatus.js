import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Badge, Drawer, List, Typography, Space, Tag } from 'antd';
import { 
  CloudUploadOutlined, 
  CloudSyncOutlined, 
  ClockCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { getPendingFormSubmissions, syncOfflineForms } from '../../utils/offlineFormHandler';

const { Text, Title } = Typography;

/**
 * Component to display the status of offline form submissions
 * and allow users to manually trigger sync
 */
const OfflineFormStatus = ({ showBadge = true }) => {
  const [pendingForms, setPendingForms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [syncStatus, setSyncStatus] = useState(null);

  // Load pending forms on mount and when online status changes
  useEffect(() => {
    loadPendingForms();

    // Listen for online/offline events
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
    
    // Listen for sync completion events
    window.addEventListener('offlineSync', handleSyncComplete);

    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
      window.removeEventListener('offlineSync', handleSyncComplete);
    };
  }, []);

  // Handle online/offline status changes
  const handleOnlineStatusChange = () => {
    loadPendingForms();
    
    if (navigator.onLine && pendingForms.length > 0) {
      // Auto-sync when coming back online
      handleSync();
    }
  };

  // Handle sync completion events
  const handleSyncComplete = (event) => {
    const { results } = event.detail;
    
    setSyncStatus({
      success: results.successful > 0,
      message: `Sync completed: ${results.successful} submitted, ${results.failed} failed, ${results.remaining} remaining.`,
      timestamp: new Date().toLocaleTimeString()
    });
    
    // Reload pending forms
    loadPendingForms();
    
    // Clear status after 5 seconds
    setTimeout(() => {
      setSyncStatus(null);
    }, 5000);
  };

  // Load pending form submissions
  const loadPendingForms = async () => {
    try {
      const forms = await getPendingFormSubmissions();
      setPendingForms(forms);
    } catch (error) {
      console.error('Error loading pending forms:', error);
    }
  };

  // Manually trigger sync
  const handleSync = async () => {
    if (!navigator.onLine) {
      setSyncStatus({
        success: false,
        message: 'Cannot sync while offline. Please connect to the internet and try again.',
        timestamp: new Date().toLocaleTimeString()
      });
      return;
    }
    
    setLoading(true);
    setSyncStatus({
      success: true,
      message: 'Syncing...',
      timestamp: new Date().toLocaleTimeString()
    });
    
    try {
      const result = await syncOfflineForms();
      
      if (!result) {
        setSyncStatus({
          success: false,
          message: 'Sync failed. Service worker not available.',
          timestamp: new Date().toLocaleTimeString()
        });
      }
    } catch (error) {
      console.error('Error syncing forms:', error);
      setSyncStatus({
        success: false,
        message: `Sync failed: ${error.message}`,
        timestamp: new Date().toLocaleTimeString()
      });
    } finally {
      setLoading(false);
    }
  };

  // Show the drawer with pending forms
  const showDrawer = () => {
    loadPendingForms();
    setDrawerVisible(true);
  };

  // Hide the drawer
  const hideDrawer = () => {
    setDrawerVisible(false);
  };

  // If there are no pending forms and we're not showing the badge, don't render anything
  if (pendingForms.length === 0 && !showBadge) {
    return null;
  }

  // Render the component
  return (
    <>
      {/* Badge with count of pending forms */}
      {showBadge && (
        <Badge count={pendingForms.length} overflowCount={99}>
          <Button 
            icon={<CloudUploadOutlined />} 
            onClick={showDrawer}
            type={pendingForms.length > 0 ? 'primary' : 'default'}
          >
            {pendingForms.length > 0 ? 'Pending Forms' : 'No Pending Forms'}
          </Button>
        </Badge>
      )}

      {/* Drawer with pending forms */}
      <Drawer
        title={
          <Space>
            <CloudUploadOutlined />
            <span>Offline Form Submissions</span>
            <Badge 
              count={pendingForms.length} 
              style={{ backgroundColor: pendingForms.length > 0 ? '#1890ff' : '#52c41a' }}
            />
          </Space>
        }
        placement="right"
        onClose={hideDrawer}
        open={drawerVisible}
        width={400}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button 
              onClick={hideDrawer} 
              style={{ marginRight: 8 }}
            >
              Close
            </Button>
            <Button 
              type="primary" 
              icon={<CloudSyncOutlined />} 
              onClick={handleSync}
              loading={loading}
              disabled={!navigator.onLine || pendingForms.length === 0}
            >
              Sync Now
            </Button>
          </div>
        }
      >
        {/* Connection status */}
        <Alert
          type={navigator.onLine ? 'success' : 'warning'}
          message={navigator.onLine ? 'Online' : 'Offline'}
          description={
            navigator.onLine 
              ? 'You are connected to the internet. Pending forms can be synchronized.'
              : 'You are offline. Forms will be synchronized when you reconnect.'
          }
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* Sync status */}
        {syncStatus && (
          <Alert
            type={syncStatus.success ? 'success' : 'error'}
            message={syncStatus.success ? 'Sync Successful' : 'Sync Failed'}
            description={syncStatus.message}
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* List of pending forms */}
        {pendingForms.length > 0 ? (
          <List
            dataSource={pendingForms}
            renderItem={(form) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<ClockCircleOutlined style={{ fontSize: 24 }} />}
                  title={`Form Submission (${new Date(form.timestamp).toLocaleString()})`}
                  description={
                    <>
                      <Text type="secondary">URL: {form.url}</Text>
                      <br />
                      <Text type="secondary">Method: {form.method}</Text>
                      <br />
                      <Tag color="blue">{Object.keys(form.data).length} fields</Tag>
                    </>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
            <Title level={4}>No Pending Forms</Title>
            <Text type="secondary">
              All form submissions have been synchronized with the server.
            </Text>
          </div>
        )}
      </Drawer>
    </>
  );
};

export default OfflineFormStatus;
