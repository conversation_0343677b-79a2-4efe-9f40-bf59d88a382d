import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { Typography, Tabs, Space, Button, Divider, Switch, Badge, Tooltip } from 'antd';
import { SaveOutlined, UndoOutlined, SyncOutlined, EyeOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';
import theme from '../../../design-system/theme';
import { updateComponent } from '../../../redux/actions';
import { debounce } from 'lodash';

// Import enhanced property editor components
import {
  PropertySearch,
  PropertyGroup,
  PropertyPreview,
  getComponentProperties,
  getStylePropertiesGrouped,
  ComponentSchemas,
  StyleSchemas
} from './index';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const PropertiesContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const PropertiesContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0;
`;

const PropertiesActions = styled.div`
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
`;

const TabContent = styled.div`
  padding: 0;
`;

/**
 * Enhanced ComponentProperties component with advanced property editing capabilities
 * Now includes real-time preview updates and collaborative editing support
 */
const EnhancedComponentProperties = ({
  component,
  onUpdate,
  onRealTimeUpdate,
  enableRealTimePreview = true,
  enableCollaboration = false,
  collaborativeSession = null
}) => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('basic');
  const [propertyValues, setPropertyValues] = useState({});
  const [filteredProperties, setFilteredProperties] = useState({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(enableRealTimePreview);
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);

  // Refs for debouncing and performance
  const updateTimeoutRef = useRef(null);
  const lastValuesRef = useRef({});

  // Initialize property values when component changes
  useEffect(() => {
    if (component) {
      const initialValues = {
        name: component.name,
        ...component.props,
        ...component.style
      };
      setPropertyValues(initialValues);
      setHasUnsavedChanges(false);
    }
  }, [component]);

  // Get component-specific properties
  const componentProperties = useMemo(() => {
    if (!component?.type) return {};
    return getComponentProperties(component.type);
  }, [component?.type]);

  // Get style properties grouped by category
  const stylePropertiesGrouped = useMemo(() => {
    return getStylePropertiesGrouped();
  }, []);

  // Debounced real-time update function
  const debouncedRealTimeUpdate = useMemo(
    () => debounce((updatedComponent) => {
      if (isRealTimeEnabled && onRealTimeUpdate) {
        setIsUpdating(true);
        onRealTimeUpdate(updatedComponent);
        setLastUpdateTime(new Date());

        // Clear updating state after a short delay
        setTimeout(() => setIsUpdating(false), 300);
      }
    }, 300),
    [isRealTimeEnabled, onRealTimeUpdate]
  );

  // Immediate visual update function (no debounce)
  const immediateUpdate = useCallback((updatedComponent) => {
    if (onUpdate) {
      onUpdate(updatedComponent);
    }
  }, [onUpdate]);

  // Handle property value change with real-time updates
  const handlePropertyChange = useCallback((propertyName, newValue, schema) => {
    const newPropertyValues = {
      ...propertyValues,
      [propertyName]: newValue
    };

    setPropertyValues(newPropertyValues);
    setHasUnsavedChanges(true);

    // Create updated component
    const updatedComponent = {
      ...component,
      name: propertyName === 'name' ? newValue : newPropertyValues.name,
      props: {
        ...component.props,
        ...(propertyName !== 'name' && !StyleSchemas[propertyName] ? { [propertyName]: newValue } : {})
      },
      style: {
        ...component.style,
        ...(StyleSchemas[propertyName] ? { [propertyName]: newValue } : {})
      }
    };

    // Immediate visual update for responsive UI
    immediateUpdate(updatedComponent);

    // Debounced real-time update for performance
    if (isRealTimeEnabled) {
      debouncedRealTimeUpdate(updatedComponent);
    }

    // Store last values for comparison
    lastValuesRef.current = newPropertyValues;
  }, [propertyValues, component, isRealTimeEnabled, immediateUpdate, debouncedRealTimeUpdate]);

  // Handle save changes
  const handleSave = () => {
    if (!component) return;

    const { name, ...otherValues } = propertyValues;
    const props = {};
    const style = {};

    // Separate props and styles
    Object.entries(otherValues).forEach(([key, value]) => {
      if (StyleSchemas[key]) {
        style[key] = value;
      } else {
        props[key] = value;
      }
    });

    const updatedComponent = {
      ...component,
      name: name || component.name,
      props: { ...component.props, ...props },
      style: { ...component.style, ...style }
    };

    // Dispatch update action
    dispatch(updateComponent(updatedComponent));
    setHasUnsavedChanges(false);

    // Call onUpdate callback
    if (onUpdate) {
      onUpdate(updatedComponent);
    }
  };

  // Handle reset all changes
  const handleResetAll = () => {
    if (component) {
      const initialValues = {
        name: component.name,
        ...component.props,
        ...component.style
      };
      setPropertyValues(initialValues);
      setHasUnsavedChanges(false);
    }
  };

  // Handle property filter
  const handlePropertyFilter = (filtered, filterInfo) => {
    setFilteredProperties(filtered);
  };

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      debouncedRealTimeUpdate.cancel();
    };
  }, [debouncedRealTimeUpdate]);

  // Handle real-time toggle
  const handleRealTimeToggle = useCallback((enabled) => {
    setIsRealTimeEnabled(enabled);

    if (enabled && hasUnsavedChanges) {
      // Trigger immediate update when enabling real-time mode
      const updatedComponent = {
        ...component,
        name: propertyValues.name || component.name,
        props: { ...component.props },
        style: { ...component.style }
      };

      // Separate props and styles from propertyValues
      Object.entries(propertyValues).forEach(([key, value]) => {
        if (key === 'name') return;
        if (StyleSchemas[key]) {
          updatedComponent.style[key] = value;
        } else {
          updatedComponent.props[key] = value;
        }
      });

      debouncedRealTimeUpdate(updatedComponent);
    }
  }, [hasUnsavedChanges, component, propertyValues, debouncedRealTimeUpdate]);

  // If no component is selected, show a message
  if (!component) {
    return (
      <PropertiesContainer>
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Text type="secondary">Select a component to edit its properties</Text>
        </div>
      </PropertiesContainer>
    );
  }

  return (
    <PropertiesContainer>
      {/* Real-time Preview Header */}
      <div style={{
        padding: '12px 16px',
        borderBottom: '1px solid #f0f0f0',
        background: '#fafafa',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Space>
          <Text strong style={{ fontSize: '14px' }}>
            {component.name || component.type} Properties
          </Text>
          {isUpdating && <SyncOutlined spin style={{ color: '#1890ff' }} />}
        </Space>

        <Space>
          {enableRealTimePreview && (
            <Tooltip title="Toggle real-time preview updates">
              <Switch
                size="small"
                checked={isRealTimeEnabled}
                onChange={handleRealTimeToggle}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeOutlined />}
              />
            </Tooltip>
          )}

          {lastUpdateTime && isRealTimeEnabled && (
            <Badge
              status="success"
              text={`Updated ${lastUpdateTime.toLocaleTimeString()}`}
              style={{ fontSize: '11px' }}
            />
          )}

          {enableCollaboration && collaborativeSession && (
            <Badge
              count={collaborativeSession.collaboratorCount || 0}
              showZero={false}
              style={{ backgroundColor: '#52c41a' }}
            >
              <Tooltip title="Active collaborators">
                <Button size="small" type="text" icon={<SyncOutlined />} />
              </Tooltip>
            </Badge>
          )}
        </Space>
      </div>

      {/* Real-time Preview */}
      <PropertyPreview
        component={component}
        properties={{ ...componentProperties, ...StyleSchemas }}
        values={propertyValues}
        onReset={handleResetAll}
        showPreview={true}
        showCode={false}
        showValidation={true}
        realTimeEnabled={isRealTimeEnabled}
      />

      <PropertiesContent>
        <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
          {/* Basic Properties Tab */}
          <TabPane tab="Properties" key="basic">
            <TabContent>
              {/* Property Search */}
              <PropertySearch
                properties={componentProperties}
                onFilter={handlePropertyFilter}
                placeholder="Search component properties..."
              />

              {/* Component Name */}
              <PropertyGroup
                groupName="basic"
                properties={{
                  name: {
                    type: 'text',
                    label: 'Component Name',
                    required: true,
                    placeholder: 'Enter component name'
                  }
                }}
                values={propertyValues}
                onChange={handlePropertyChange}
                componentType={component.type}
                defaultExpanded={true}
              />

              {/* Component-specific Properties */}
              {Object.keys(componentProperties).length > 0 && (
                <PropertyGroup
                  groupName="component"
                  properties={componentProperties}
                  values={propertyValues}
                  onChange={handlePropertyChange}
                  componentType={component.type}
                  defaultExpanded={true}
                />
              )}
            </TabContent>
          </TabPane>

          {/* Style Properties Tab */}
          <TabPane tab="Styling" key="style">
            <TabContent>
              {/* Property Search for Styles */}
              <PropertySearch
                properties={StyleSchemas}
                onFilter={handlePropertyFilter}
                placeholder="Search style properties..."
              />

              {/* Render Style Property Groups */}
              {Object.entries(stylePropertiesGrouped).map(([groupName, groupProperties]) => (
                <PropertyGroup
                  key={groupName}
                  groupName={groupName}
                  properties={groupProperties}
                  values={propertyValues}
                  onChange={handlePropertyChange}
                  componentType={component.type}
                  defaultExpanded={groupName === 'dimensions' || groupName === 'colors'}
                />
              ))}
            </TabContent>
          </TabPane>

          {/* Advanced Tab */}
          <TabPane tab="Advanced" key="advanced">
            <TabContent>
              <PropertyGroup
                groupName="advanced"
                properties={{
                  customProps: {
                    type: 'json',
                    label: 'Custom Properties',
                    placeholder: 'Enter custom properties as JSON',
                    description: 'Additional properties not covered by the standard options'
                  },
                  customStyles: {
                    type: 'json',
                    label: 'Custom Styles',
                    placeholder: 'Enter custom styles as JSON',
                    description: 'Additional CSS styles not covered by the standard options'
                  }
                }}
                values={propertyValues}
                onChange={handlePropertyChange}
                componentType={component.type}
                defaultExpanded={true}
              />
            </TabContent>
          </TabPane>
        </Tabs>
      </PropertiesContent>

      {/* Actions */}
      <PropertiesActions>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            {hasUnsavedChanges && (
              <Text type="warning" style={{ fontSize: '12px' }}>
                Unsaved changes
              </Text>
            )}
          </Space>

          <Space>
            <Button
              size="small"
              icon={<UndoOutlined />}
              onClick={handleResetAll}
              disabled={!hasUnsavedChanges}
            >
              Reset
            </Button>
            <Button
              type="primary"
              size="small"
              icon={<SaveOutlined />}
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
            >
              Apply Changes
            </Button>
          </Space>
        </Space>
      </PropertiesActions>
    </PropertiesContainer>
  );
};

export default EnhancedComponentProperties;
