/**
 * Tutorial Performance Optimization
 * 
 * Provides performance optimizations for the tutorial system including
 * lazy loading, virtual scrolling, memory management, and efficient event handling.
 */

import React, { 
  useState, 
  useEffect, 
  useCallback, 
  useMemo, 
  useRef,
  memo,
  lazy,
  Suspense
} from 'react';
import { Spin } from 'antd';

// Lazy-loaded components
const LazyTutorialLauncher = lazy(() => import('./TutorialLauncher'));
const LazyTutorialDashboard = lazy(() => import('./TutorialDashboard'));
const LazyTutorialBadges = lazy(() => import('./TutorialBadges'));
const LazyTutorialAdmin = lazy(() => import('./TutorialAdmin'));

// Performance monitoring hook
export const usePerformanceMonitoring = (componentName) => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    memoryUsage: 0,
    frameRate: 60,
    eventCount: 0
  });

  const startTime = useRef(performance.now());
  const frameCount = useRef(0);
  const lastFrameTime = useRef(performance.now());

  useEffect(() => {
    const measureRenderTime = () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime.current;
      
      setMetrics(prev => ({
        ...prev,
        renderTime: Math.round(renderTime * 100) / 100
      }));
    };

    const measureMemoryUsage = () => {
      if (performance.memory) {
        const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
        setMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(memoryUsage * 100) / 100
        }));
      }
    };

    const measureFrameRate = () => {
      const now = performance.now();
      frameCount.current++;
      
      if (now - lastFrameTime.current >= 1000) {
        const fps = frameCount.current;
        frameCount.current = 0;
        lastFrameTime.current = now;
        
        setMetrics(prev => ({
          ...prev,
          frameRate: fps
        }));
      }
      
      requestAnimationFrame(measureFrameRate);
    };

    measureRenderTime();
    measureMemoryUsage();
    requestAnimationFrame(measureFrameRate);

    const memoryInterval = setInterval(measureMemoryUsage, 5000);
    
    return () => {
      clearInterval(memoryInterval);
    };
  }, []);

  const incrementEventCount = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      eventCount: prev.eventCount + 1
    }));
  }, []);

  return { metrics, incrementEventCount };
};

// Debounced function utility
export const useDebounce = (callback, delay) => {
  const timeoutRef = useRef(null);

  return useCallback((...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

// Throttled function utility
export const useThrottle = (callback, delay) => {
  const lastRun = useRef(Date.now());

  return useCallback((...args) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = Date.now();
    }
  }, [callback, delay]);
};

// Virtual scrolling hook for large lists
export const useVirtualScrolling = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return { visibleItems, handleScroll };
};

// Memory-efficient event manager
export class EventManager {
  constructor() {
    this.listeners = new Map();
    this.delegatedListeners = new Map();
  }

  // Add event listener with automatic cleanup
  addEventListener(element, event, handler, options = {}) {
    const key = `${element}_${event}`;
    
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    
    this.listeners.get(key).add(handler);
    element.addEventListener(event, handler, options);
    
    // Return cleanup function
    return () => {
      this.removeEventListener(element, event, handler);
    };
  }

  removeEventListener(element, event, handler) {
    const key = `${element}_${event}`;
    const handlers = this.listeners.get(key);
    
    if (handlers) {
      handlers.delete(handler);
      element.removeEventListener(event, handler);
      
      if (handlers.size === 0) {
        this.listeners.delete(key);
      }
    }
  }

  // Event delegation for better performance
  addDelegatedListener(container, selector, event, handler) {
    const key = `${container}_${event}`;
    
    if (!this.delegatedListeners.has(key)) {
      const delegatedHandler = (e) => {
        const target = e.target.closest(selector);
        if (target) {
          handler(e, target);
        }
      };
      
      container.addEventListener(event, delegatedHandler);
      this.delegatedListeners.set(key, delegatedHandler);
    }
  }

  cleanup() {
    this.listeners.clear();
    this.delegatedListeners.forEach((handler, key) => {
      const [container, event] = key.split('_');
      container.removeEventListener(event, handler);
    });
    this.delegatedListeners.clear();
  }
}

// Optimized tutorial step component
export const OptimizedTutorialStep = memo(({ step, onNext, onPrevious, onSkip }) => {
  const { metrics, incrementEventCount } = usePerformanceMonitoring('TutorialStep');
  
  const handleNext = useCallback(() => {
    incrementEventCount();
    onNext();
  }, [onNext, incrementEventCount]);

  const handlePrevious = useCallback(() => {
    incrementEventCount();
    onPrevious();
  }, [onPrevious, incrementEventCount]);

  const handleSkip = useCallback(() => {
    incrementEventCount();
    onSkip();
  }, [onSkip, incrementEventCount]);

  return (
    <div className="tutorial-step" data-performance-metrics={JSON.stringify(metrics)}>
      <h3>{step.title}</h3>
      <p>{step.content}</p>
      <div className="tutorial-controls">
        <button onClick={handlePrevious}>Previous</button>
        <button onClick={handleNext}>Next</button>
        <button onClick={handleSkip}>Skip</button>
      </div>
    </div>
  );
});

// Lazy loading wrapper for tutorial components
export const LazyTutorialComponent = ({ component: Component, fallback, ...props }) => (
  <Suspense fallback={fallback || <Spin size="large" />}>
    <Component {...props} />
  </Suspense>
);

// Performance-optimized tutorial list
export const OptimizedTutorialList = memo(({ tutorials, onSelectTutorial }) => {
  const containerRef = useRef(null);
  const [containerHeight, setContainerHeight] = useState(400);
  const itemHeight = 80;

  const { visibleItems, handleScroll } = useVirtualScrolling(
    tutorials,
    itemHeight,
    containerHeight
  );

  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  const debouncedSelectTutorial = useDebounce(onSelectTutorial, 150);

  return (
    <div
      ref={containerRef}
      className="tutorial-list-container"
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: visibleItems.totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${visibleItems.offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.items.map((tutorial, index) => (
            <div
              key={tutorial.id}
              style={{ height: itemHeight }}
              className="tutorial-list-item"
              onClick={() => debouncedSelectTutorial(tutorial)}
            >
              <h4>{tutorial.title}</h4>
              <p>{tutorial.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

// Resource preloader for tutorial assets
export class TutorialResourcePreloader {
  constructor() {
    this.preloadedResources = new Map();
    this.preloadQueue = [];
    this.isPreloading = false;
  }

  preloadTutorialAssets(tutorial) {
    const assets = this.extractAssets(tutorial);
    assets.forEach(asset => this.addToPreloadQueue(asset));
    this.processPreloadQueue();
  }

  extractAssets(tutorial) {
    const assets = [];
    
    tutorial.steps.forEach(step => {
      // Extract images from content
      const imageMatches = step.content.match(/<img[^>]+src="([^"]+)"/g);
      if (imageMatches) {
        imageMatches.forEach(match => {
          const src = match.match(/src="([^"]+)"/)[1];
          assets.push({ type: 'image', url: src });
        });
      }
      
      // Extract videos
      const videoMatches = step.content.match(/<video[^>]+src="([^"]+)"/g);
      if (videoMatches) {
        videoMatches.forEach(match => {
          const src = match.match(/src="([^"]+)"/)[1];
          assets.push({ type: 'video', url: src });
        });
      }
    });
    
    return assets;
  }

  addToPreloadQueue(asset) {
    if (!this.preloadedResources.has(asset.url)) {
      this.preloadQueue.push(asset);
    }
  }

  async processPreloadQueue() {
    if (this.isPreloading || this.preloadQueue.length === 0) return;
    
    this.isPreloading = true;
    
    while (this.preloadQueue.length > 0) {
      const asset = this.preloadQueue.shift();
      await this.preloadAsset(asset);
    }
    
    this.isPreloading = false;
  }

  preloadAsset(asset) {
    return new Promise((resolve) => {
      if (asset.type === 'image') {
        const img = new Image();
        img.onload = () => {
          this.preloadedResources.set(asset.url, img);
          resolve();
        };
        img.onerror = () => resolve(); // Continue even if asset fails to load
        img.src = asset.url;
      } else if (asset.type === 'video') {
        const video = document.createElement('video');
        video.onloadeddata = () => {
          this.preloadedResources.set(asset.url, video);
          resolve();
        };
        video.onerror = () => resolve();
        video.src = asset.url;
        video.preload = 'metadata';
      } else {
        resolve();
      }
    });
  }
}

// Performance-optimized tutorial provider
export const OptimizedTutorialProvider = memo(({ children, ...props }) => {
  const eventManagerRef = useRef(new EventManager());
  const preloaderRef = useRef(new TutorialResourcePreloader());
  
  useEffect(() => {
    return () => {
      eventManagerRef.current.cleanup();
    };
  }, []);

  const contextValue = useMemo(() => ({
    eventManager: eventManagerRef.current,
    preloader: preloaderRef.current,
    ...props
  }), [props]);

  return (
    <div className="optimized-tutorial-provider" data-context={JSON.stringify(contextValue)}>
      {children}
    </div>
  );
});

// Bundle size analyzer
export const analyzeBundleSize = () => {
  const components = {
    TutorialManager: () => import('./TutorialManager'),
    TutorialOverlay: () => import('./TutorialOverlay'),
    TutorialProgress: () => import('./TutorialProgress'),
    ContextualHelp: () => import('./ContextualHelp'),
    TutorialLauncher: () => import('./TutorialLauncher'),
    TutorialDashboard: () => import('./TutorialDashboard'),
    TutorialBadges: () => import('./TutorialBadges')
  };

  const sizes = {};
  
  Object.entries(components).forEach(async ([name, importFn]) => {
    const start = performance.now();
    await importFn();
    const end = performance.now();
    sizes[name] = end - start;
  });

  return sizes;
};

// Performance monitoring component
export const TutorialPerformanceMonitor = ({ enabled = false }) => {
  const [performanceData, setPerformanceData] = useState({});
  
  useEffect(() => {
    if (!enabled) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const tutorialEntries = entries.filter(entry => 
        entry.name.includes('tutorial') || 
        entry.detail?.component?.includes('Tutorial')
      );
      
      setPerformanceData(prev => ({
        ...prev,
        [Date.now()]: tutorialEntries
      }));
    });

    observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
    
    return () => observer.disconnect();
  }, [enabled]);

  if (!enabled) return null;

  return (
    <div className="tutorial-performance-monitor" style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: 10,
      borderRadius: 4,
      fontSize: 12,
      zIndex: 10000
    }}>
      <div>Performance Monitor</div>
      <div>Entries: {Object.keys(performanceData).length}</div>
    </div>
  );
};

// Lazy component exports
export const LazyComponents = {
  TutorialLauncher: (props) => (
    <LazyTutorialComponent component={LazyTutorialLauncher} {...props} />
  ),
  TutorialDashboard: (props) => (
    <LazyTutorialComponent component={LazyTutorialDashboard} {...props} />
  ),
  TutorialBadges: (props) => (
    <LazyTutorialComponent component={LazyTutorialBadges} {...props} />
  ),
  TutorialAdmin: (props) => (
    <LazyTutorialComponent component={LazyTutorialAdmin} {...props} />
  )
};

export default {
  usePerformanceMonitoring,
  useDebounce,
  useThrottle,
  useVirtualScrolling,
  EventManager,
  OptimizedTutorialStep,
  LazyTutorialComponent,
  OptimizedTutorialList,
  TutorialResourcePreloader,
  OptimizedTutorialProvider,
  analyzeBundleSize,
  TutorialPerformanceMonitor,
  LazyComponents
};
