import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  But<PERSON>, 
  Badge, 
  Tooltip, 
  Collapse, 
  Empty, 
  Switch,
  Space,
  Divider,
  Typography
} from 'antd';
import { 
  BulbOutlined, 
  CloseOutlined, 
  SettingOutlined,
  LayoutOutlined,
  AppstoreOutlined,
  AnalysisOutlined,
  RobotOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';
import EnhancedAIPlugin from '../../plugins/EnhancedAIPlugin';

const { Panel } = Collapse;
const { Text, Title } = Typography;

/**
 * AI Suggestions Panel Component
 * Contextual sidebar that appears based on user actions and app state
 */
const AISuggestionsPanel = ({
  visible,
  onClose,
  components = [],
  layouts = [],
  selectedComponent = null,
  onApplyLayoutSuggestion,
  onApplyComponentCombination,
  onComponentAdd,
  placement = 'right',
  width = 400,
  autoShow = true,
  showSettings = true
}) => {
  // Local state
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [panelSettings, setPanelSettings] = useState({
    showLayoutSuggestions: true,
    showComponentCombinations: true,
    showAnalysis: true,
    autoHide: false
  });

  // AI suggestions hook
  const {
    suggestions,
    loading,
    error,
    lastRefresh,
    loadSuggestions,
    applyLayoutSuggestion,
    applyComponentCombination,
    refresh,
    clearError,
    hasLayoutSuggestions,
    hasCombinationSuggestions,
    hasAnalysis,
    isLoading,
    componentCount
  } = useAIDesignSuggestions({
    autoRefresh,
    refreshInterval: 30000,
    enableCache: true,
    context: { selectedComponent }
  });

  // Auto-show panel when suggestions are available
  useEffect(() => {
    if (autoShow && !visible && (hasLayoutSuggestions || hasCombinationSuggestions)) {
      // Don't auto-show immediately, give user time to work
      const timer = setTimeout(() => {
        if (componentCount > 2) { // Only auto-show after user has added some components
          // You would call a parent function here to show the panel
          // onAutoShow?.();
        }
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [autoShow, visible, hasLayoutSuggestions, hasCombinationSuggestions, componentCount]);

  // Handle applying layout suggestion
  const handleApplyLayoutSuggestion = (suggestion) => {
    const success = applyLayoutSuggestion(suggestion);
    if (success && onApplyLayoutSuggestion) {
      onApplyLayoutSuggestion(suggestion);
    }
  };

  // Handle applying component combination
  const handleApplyComponentCombination = (suggestion) => {
    const success = applyComponentCombination(suggestion);
    if (success && onApplyComponentCombination) {
      onApplyComponentCombination(suggestion);
    }
  };

  // Render panel header
  const renderHeader = () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between',
      padding: '16px 24px',
      borderBottom: '1px solid #f0f0f0'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <RobotOutlined style={{ fontSize: '20px', color: '#1890ff', marginRight: '8px' }} />
        <Title level={4} style={{ margin: 0 }}>
          AI Assistant
        </Title>
        {isLoading && (
          <Badge 
            status="processing" 
            style={{ marginLeft: '8px' }}
            title="Analyzing your app..."
          />
        )}
      </div>
      
      <Space>
        {showSettings && (
          <Tooltip title="Settings">
            <Button 
              type="text" 
              size="small" 
              icon={<SettingOutlined />}
              onClick={() => setShowAdvanced(!showAdvanced)}
            />
          </Tooltip>
        )}
        <Tooltip title="Refresh suggestions">
          <Button 
            type="text" 
            size="small" 
            icon={<ThunderboltOutlined />}
            onClick={refresh}
            loading={isLoading}
          />
        </Tooltip>
        <Button 
          type="text" 
          size="small" 
          icon={<CloseOutlined />}
          onClick={onClose}
        />
      </Space>
    </div>
  );

  // Render settings panel
  const renderSettings = () => (
    showAdvanced && (
      <Card size="small" style={{ margin: '16px', marginTop: 0 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>Auto-refresh suggestions</Text>
            <Switch 
              size="small" 
              checked={autoRefresh} 
              onChange={setAutoRefresh} 
            />
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>Show layout suggestions</Text>
            <Switch 
              size="small" 
              checked={panelSettings.showLayoutSuggestions} 
              onChange={(checked) => setPanelSettings(prev => ({ ...prev, showLayoutSuggestions: checked }))} 
            />
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>Show component combinations</Text>
            <Switch 
              size="small" 
              checked={panelSettings.showComponentCombinations} 
              onChange={(checked) => setPanelSettings(prev => ({ ...prev, showComponentCombinations: checked }))} 
            />
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>Show analysis</Text>
            <Switch 
              size="small" 
              checked={panelSettings.showAnalysis} 
              onChange={(checked) => setPanelSettings(prev => ({ ...prev, showAnalysis: checked }))} 
            />
          </div>
        </Space>
      </Card>
    )
  );

  // Render quick stats
  const renderQuickStats = () => (
    <Card size="small" style={{ margin: '16px', marginTop: showAdvanced ? '8px' : '16px' }}>
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '8px', textAlign: 'center' }}>
        <div>
          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
            {suggestions.layout.length}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>Layouts</div>
        </div>
        <div>
          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a' }}>
            {suggestions.combinations.length}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>Combos</div>
        </div>
        <div>
          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#722ed1' }}>
            {componentCount}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>Components</div>
        </div>
      </div>
      
      {lastRefresh && (
        <div style={{ textAlign: 'center', marginTop: '8px' }}>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            Last updated: {lastRefresh.toLocaleTimeString()}
          </Text>
        </div>
      )}
    </Card>
  );

  // Render suggestions content
  const renderSuggestionsContent = () => {
    if (componentCount === 0) {
      return (
        <div style={{ padding: '32px 16px', textAlign: 'center' }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span>
                Start building your app!<br />
                Add components to get AI suggestions.
              </span>
            }
          />
        </div>
      );
    }

    if (!hasLayoutSuggestions && !hasCombinationSuggestions && !hasAnalysis) {
      return (
        <div style={{ padding: '32px 16px', textAlign: 'center' }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No suggestions available"
          />
          <Button 
            type="primary" 
            icon={<ThunderboltOutlined />}
            onClick={refresh}
            style={{ marginTop: '16px' }}
          >
            Generate Suggestions
          </Button>
        </div>
      );
    }

    return (
      <div style={{ padding: '0 16px 16px' }}>
        <EnhancedAIPlugin
          components={components}
          layouts={layouts}
          selectedComponent={selectedComponent}
          onApplyLayoutSuggestion={handleApplyLayoutSuggestion}
          onApplyComponentCombination={handleApplyComponentCombination}
          onComponentAdd={onComponentAdd}
          context={{ selectedComponent }}
        />
      </div>
    );
  };

  return (
    <Drawer
      title={null}
      placement={placement}
      width={width}
      onClose={onClose}
      open={visible}
      headerStyle={{ display: 'none' }}
      bodyStyle={{ padding: 0 }}
      className="ai-suggestions-panel"
    >
      {renderHeader()}
      {renderSettings()}
      {renderQuickStats()}
      
      <div style={{ 
        height: 'calc(100vh - 200px)', 
        overflowY: 'auto',
        paddingBottom: '16px'
      }}>
        {renderSuggestionsContent()}
      </div>
    </Drawer>
  );
};

AISuggestionsPanel.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  components: PropTypes.array,
  layouts: PropTypes.array,
  selectedComponent: PropTypes.object,
  onApplyLayoutSuggestion: PropTypes.func,
  onApplyComponentCombination: PropTypes.func,
  onComponentAdd: PropTypes.func,
  placement: PropTypes.oneOf(['left', 'right', 'top', 'bottom']),
  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  autoShow: PropTypes.bool,
  showSettings: PropTypes.bool,
};

export default AISuggestionsPanel;
