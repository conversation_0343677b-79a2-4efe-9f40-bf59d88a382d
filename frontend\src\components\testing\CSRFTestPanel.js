/**
 * CSRF Test Panel Component
 * 
 * A React component for testing CSRF protection implementation
 * in the App Builder application.
 */

import React, { useState } from 'react';
import { Button, Card, Typography, Alert, Collapse, Space, Tag, Spin } from 'antd';
import { 
  SecurityScanOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { runCSRFTestSuite } from '../../utils/testCSRFProtection';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const CSRFTestPanel = () => {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [error, setError] = useState(null);

  const runTests = async () => {
    setIsRunning(true);
    setError(null);
    setTestResults(null);

    try {
      const results = await runCSRFTestSuite();
      setTestResults(results);
    } catch (err) {
      console.error('Error running CSRF tests:', err);
      setError(err.message);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success) => {
    if (success === true) return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    if (success === false) return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
  };

  const getStatusTag = (success) => {
    if (success === true) return <Tag color="success">PASS</Tag>;
    if (success === false) return <Tag color="error">FAIL</Tag>;
    return <Tag color="warning">WARNING</Tag>;
  };

  const renderTestResult = (title, result) => {
    if (!result) return null;

    const success = Array.isArray(result) 
      ? result.every(r => r.success !== false)
      : result.success;

    return (
      <Card 
        size="small" 
        title={
          <Space>
            {getStatusIcon(success)}
            {title}
            {getStatusTag(success)}
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        {Array.isArray(result) ? (
          <div>
            {result.map((item, index) => (
              <div key={index} style={{ marginBottom: 8 }}>
                <Space>
                  {getStatusIcon(item.success !== false)}
                  <Text code>{item.endpoint || item.method || `Test ${index + 1}`}</Text>
                  {item.status && <Tag>{item.status}</Tag>}
                </Space>
                {item.error && (
                  <div style={{ marginLeft: 24, marginTop: 4 }}>
                    <Text type="danger" style={{ fontSize: '12px' }}>
                      {item.error}
                    </Text>
                  </div>
                )}
                {item.message && (
                  <div style={{ marginLeft: 24, marginTop: 4 }}>
                    <Text style={{ fontSize: '12px' }}>
                      {item.message}
                    </Text>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div>
            {result.message && <Paragraph>{result.message}</Paragraph>}
            {result.error && (
              <Alert 
                message={result.error} 
                type="error" 
                size="small" 
                style={{ marginTop: 8 }}
              />
            )}
            {result.token && (
              <div>
                <Text strong>Token: </Text>
                <Text code>{result.token}</Text>
              </div>
            )}
            {result.cookieToken !== undefined && (
              <div>
                <Text strong>Cookie Token: </Text>
                <Tag color={result.cookieToken ? 'success' : 'warning'}>
                  {result.cookieToken ? 'Found' : 'Not Found'}
                </Tag>
              </div>
            )}
          </div>
        )}
      </Card>
    );
  };

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <Card>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <SecurityScanOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={2}>CSRF Protection Test Suite</Title>
          <Paragraph>
            Test the CSRF protection implementation across all forms and API endpoints
            to ensure proper security measures are in place.
          </Paragraph>
        </div>

        {error && (
          <Alert
            message="Test Error"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={runTests}
            loading={isRunning}
            disabled={isRunning}
          >
            {isRunning ? 'Running Tests...' : 'Run CSRF Tests'}
          </Button>
        </div>

        {isRunning && (
          <div style={{ textAlign: 'center', marginBottom: 24 }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>Running comprehensive CSRF protection tests...</Text>
            </div>
          </div>
        )}

        {testResults && (
          <div>
            <Title level={3}>Test Results</Title>
            
            {renderTestResult('CSRF Token Retrieval', testResults.tokenRetrieval)}
            {renderTestResult('API Endpoint Protection', testResults.protectionWithToken)}
            {renderTestResult('Protection Without Token', testResults.protectionWithoutToken)}
            {renderTestResult('Token Refresh Mechanism', testResults.tokenRefresh)}
            {renderTestResult('Authentication Protection', testResults.authProtection)}

            <Collapse style={{ marginTop: 24 }}>
              <Panel header="Raw Test Results (JSON)" key="raw">
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 16, 
                  borderRadius: 4,
                  fontSize: '12px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(testResults, null, 2)}
                </pre>
              </Panel>
            </Collapse>
          </div>
        )}

        <div style={{ marginTop: 24, padding: 16, background: '#f9f9f9', borderRadius: 4 }}>
          <Title level={4}>About CSRF Protection</Title>
          <Paragraph>
            Cross-Site Request Forgery (CSRF) protection prevents malicious websites from 
            making unauthorized requests on behalf of authenticated users. This test suite 
            verifies that:
          </Paragraph>
          <ul>
            <li>CSRF tokens are properly generated and retrieved</li>
            <li>API endpoints require valid CSRF tokens for state-changing operations</li>
            <li>Requests without CSRF tokens are properly rejected</li>
            <li>Token refresh mechanisms work correctly</li>
            <li>Authentication endpoints are properly protected</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default CSRFTestPanel;
