/**
 * Service Initialization Script
 *
 * This script initializes all services when the application starts.
 * It ensures that all services are properly initialized and accessible.
 */

import { initializeServices } from '../services/serviceInitializer';
import { getWebSocketUrl } from './websocket';
import API_ENDPOINTS from '../config/api';
import { initializeCSRF } from '../services/csrfService';

/**
 * Initialize all services when the application starts
 * @returns {Promise<void>}
 */
export async function initializeAllServices() {
  console.log('Initializing all services...');

  try {
    // Check if we're in development mode
    const isDev = process.env.NODE_ENV === 'development';

    // In development mode, we can continue even if services fail to initialize
    if (isDev) {
      console.log('Development mode: Services will be mocked if initialization fails');
    }

    // Initialize CSRF service first
    try {
      console.log('Initializing CSRF service...');
      await initializeCSRF();
      console.log('CSRF service initialized successfully');
    } catch (error) {
      console.warn('CSRF service initialization failed:', error);
      // Continue anyway as CSRF is not critical for app startup
    }

    // Initialize services with retry logic
    let services = null;
    let retryCount = 0;
    const maxRetries = isDev ? 1 : 3; // Fewer retries in development mode

    while (!services && retryCount < maxRetries) {
      try {
        console.log(`Attempting to initialize services (attempt ${retryCount + 1}/${maxRetries})...`);
        services = await initializeServices();
        console.log('Services initialized:', services);
      } catch (error) {
        console.error(`Error initializing services (attempt ${retryCount + 1}/${maxRetries}):`, error);
        retryCount++;

        if (retryCount < maxRetries) {
          // Wait before retrying
          const delay = 1000 * Math.pow(2, retryCount - 1); // Exponential backoff
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    if (!services && retryCount >= maxRetries) {
      console.error('Failed to initialize services after multiple attempts');

      // In development mode, create mock services
      if (isDev) {
        console.log('Development mode: Creating mock services');
        services = {
          websocket: { status: 'mocked' },
          serviceWorker: { status: 'mocked' }
        };
      }
      // Continue anyway to allow the app to load partially
    }

    // Register service worker if not already registered
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/service-worker.js');
        console.log('Service Worker registered:', registration);
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }

    // Check API endpoints with a timeout
    try {
      const apiCheckPromise = checkApiEndpoints();
      const apiCheckTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('API check timeout')), 5000)
      );
      await Promise.race([apiCheckPromise, apiCheckTimeout]);
    } catch (error) {
      console.warn('API endpoint check failed or timed out:', error);
      // Continue anyway
    }

    // Check WebSocket connections with a timeout
    try {
      const wsCheckPromise = checkWebSocketConnections();
      const wsCheckTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('WebSocket check timeout')), 5000)
      );
      await Promise.race([wsCheckPromise, wsCheckTimeout]);
    } catch (error) {
      console.warn('WebSocket connection check failed or timed out:', error);
      // Continue anyway
    }

    console.log('All services initialization process completed');
  } catch (error) {
    console.error('Error in service initialization process:', error);
    // Continue anyway to allow the app to load partially
  }
}

/**
 * Check if API endpoints are accessible
 * @returns {Promise<void>}
 */
async function checkApiEndpoints() {
  console.log('Checking API endpoints...');

  const endpoints = [
    'STATUS',
    'HEALTH',
    'APP_DATA'
  ];

  for (const endpoint of endpoints) {
    try {
      const url = API_ENDPOINTS[endpoint];
      if (!url) {
        console.warn(`API endpoint ${endpoint} not defined`);
        continue;
      }

      const response = await fetch(url, { method: 'GET' });
      console.log(`API endpoint ${endpoint} (${url}): ${response.ok ? 'OK' : 'Error'}`);
    } catch (error) {
      console.error(`Error checking API endpoint ${endpoint}:`, error);
    }
  }
}

/**
 * Check if WebSocket connections are accessible
 * @returns {Promise<void>}
 */
async function checkWebSocketConnections() {
  console.log('Checking WebSocket connections...');

  const endpoints = [
    'app_builder',
    'test',
    'echo'
  ];

  // Create an array of promises for each endpoint check
  const connectionPromises = endpoints.map(endpoint => {
    return new Promise((resolve) => {
      try {
        const url = getWebSocketUrl(endpoint);
        console.log(`Testing WebSocket connection to ${url}...`);

        // Try to create a WebSocket connection
        const socket = new WebSocket(url);
        let connectionSuccessful = false;

        // Set up event handlers
        socket.onopen = () => {
          console.log(`WebSocket connection to ${endpoint} established`);
          connectionSuccessful = true;

          // Send a test message
          try {
            socket.send(JSON.stringify({ type: 'ping', message: 'Test connection' }));
          } catch (sendError) {
            console.warn(`Error sending test message to ${endpoint}:`, sendError);
          }

          // Close the connection after 1 second
          setTimeout(() => {
            try {
              socket.close();
            } catch (closeError) {
              console.warn(`Error closing WebSocket connection to ${endpoint}:`, closeError);
            }
            resolve({ endpoint, success: true });
          }, 1000);
        };

        socket.onmessage = (event) => {
          console.log(`WebSocket message from ${endpoint}:`, event.data);
        };

        socket.onerror = (error) => {
          console.error(`WebSocket error for ${endpoint}:`, error);
          // Don't resolve here, wait for onclose
        };

        socket.onclose = () => {
          console.log(`WebSocket connection to ${endpoint} closed`);
          if (!connectionSuccessful) {
            resolve({ endpoint, success: false });
          }
        };

        // Set a timeout to close the connection if it doesn't connect
        setTimeout(() => {
          if (socket.readyState !== WebSocket.CLOSED && socket.readyState !== WebSocket.CLOSING) {
            console.warn(`WebSocket connection to ${endpoint} timed out`);
            try {
              socket.close();
            } catch (closeError) {
              console.warn(`Error closing timed out WebSocket connection to ${endpoint}:`, closeError);
            }
            resolve({ endpoint, success: false, reason: 'timeout' });
          }
        }, 3000); // Shorter timeout for faster checking
      } catch (error) {
        console.error(`Error creating WebSocket connection to ${endpoint}:`, error);
        resolve({ endpoint, success: false, reason: 'creation_error', error: error.message });
      }
    });
  });

  // Wait for all connection checks to complete
  const results = await Promise.all(connectionPromises);

  // Log the results
  const successfulConnections = results.filter(result => result.success);
  console.log(`WebSocket connection check completed. ${successfulConnections.length}/${endpoints.length} connections successful.`);

  // Try fallback URLs if no connections were successful
  if (successfulConnections.length === 0 && window.WEBSOCKET_FALLBACK_URLS && window.WEBSOCKET_FALLBACK_URLS.length > 0) {
    console.log('No WebSocket connections successful. Trying fallback URLs...');

    // Store the original getWebSocketUrl function
    const originalGetWebSocketUrl = getWebSocketUrl;

    // Try each fallback URL
    for (let i = 0; i < Math.min(window.WEBSOCKET_FALLBACK_URLS.length, 2); i++) {
      const fallbackUrl = window.WEBSOCKET_FALLBACK_URLS[i];
      console.log(`Trying fallback WebSocket URL: ${fallbackUrl}`);

      // Override the getWebSocketUrl function to return the fallback URL
      window.getWebSocketUrl = () => fallbackUrl;

      // Try to connect to the fallback URL
      try {
        const socket = new WebSocket(fallbackUrl);

        await new Promise((resolve) => {
          socket.onopen = () => {
            console.log(`Fallback WebSocket connection to ${fallbackUrl} established`);
            socket.close();
            resolve();
          };

          socket.onerror = () => {
            console.error(`Fallback WebSocket connection to ${fallbackUrl} failed`);
            resolve();
          };

          socket.onclose = () => {
            console.log(`Fallback WebSocket connection to ${fallbackUrl} closed`);
            resolve();
          };

          // Set a timeout
          setTimeout(() => {
            if (socket.readyState !== WebSocket.CLOSED) {
              console.warn(`Fallback WebSocket connection to ${fallbackUrl} timed out`);
              socket.close();
              resolve();
            }
          }, 3000);
        });
      } catch (error) {
        console.error(`Error creating fallback WebSocket connection to ${fallbackUrl}:`, error);
      }
    }

    // Restore the original getWebSocketUrl function
    window.getWebSocketUrl = originalGetWebSocketUrl;
  }
}

/**
 * Check if Service Worker is registered
 * @returns {Promise<boolean>} Whether Service Worker is registered
 */
export async function isServiceWorkerRegistered() {
  if (!('serviceWorker' in navigator)) {
    return false;
  }

  try {
    const registrations = await navigator.serviceWorker.getRegistrations();
    return registrations.length > 0;
  } catch (error) {
    console.error('Error checking Service Worker registration:', error);
    return false;
  }
}

/**
 * Register Service Worker
 * @returns {Promise<ServiceWorkerRegistration|null>} Service Worker registration
 */
export async function registerServiceWorker() {
  if (!('serviceWorker' in navigator)) {
    console.warn('Service Worker is not supported in this browser');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/service-worker.js');
    console.log('Service Worker registered successfully:', registration);
    return registration;
  } catch (error) {
    console.error('Error registering Service Worker:', error);
    return null;
  }
}

export default {
  initializeAllServices,
  isServiceWorkerRegistered,
  registerServiceWorker
};
