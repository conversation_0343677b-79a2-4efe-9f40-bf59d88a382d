/**
 * Tutorial Analytics and Insights System
 * 
 * Comprehensive analytics system to track tutorial effectiveness,
 * user engagement, completion rates, and learning patterns.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, Row, Col, Statistic, Progress, Table, Chart } from 'antd';
import {
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  Pie<PERSON><PERSON>Outlined,
  TrendingUpOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import tutorialStorage from './TutorialStorage';
import { TUTORIAL_STATUS, TUTORIAL_EVENTS } from './types';

// Analytics Event Types
export const ANALYTICS_EVENTS = {
  TUTORIAL_STARTED: 'tutorial_started',
  TUTORIAL_COMPLETED: 'tutorial_completed',
  TUTORIAL_ABANDONED: 'tutorial_abandoned',
  STEP_STARTED: 'step_started',
  STEP_COMPLETED: 'step_completed',
  STEP_SKIPPED: 'step_skipped',
  HELP_REQUESTED: 'help_requested',
  ERROR_OCCURRED: 'error_occurred',
  PERFORMANCE_METRIC: 'performance_metric',
  USER_FEEDBACK: 'user_feedback',
  ENGAGEMENT_METRIC: 'engagement_metric'
};

// Analytics Data Collector
export class TutorialAnalyticsCollector {
  constructor(userId = 'anonymous') {
    this.userId = userId;
    this.sessionId = this.generateSessionId();
    this.events = [];
    this.startTime = Date.now();
    this.isCollecting = true;
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  trackEvent(eventType, data = {}) {
    if (!this.isCollecting) return;

    const event = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: eventType,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: Date.now(),
      data: {
        ...data,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        url: window.location.href
      }
    };

    this.events.push(event);
    this.persistEvent(event);
    this.sendToAnalyticsService(event);
  }

  persistEvent(event) {
    try {
      const existingEvents = JSON.parse(
        localStorage.getItem('tutorial_analytics_events') || '[]'
      );
      existingEvents.push(event);
      
      // Keep only last 1000 events to prevent storage overflow
      if (existingEvents.length > 1000) {
        existingEvents.splice(0, existingEvents.length - 1000);
      }
      
      localStorage.setItem('tutorial_analytics_events', JSON.stringify(existingEvents));
    } catch (error) {
      console.warn('Failed to persist analytics event:', error);
    }
  }

  sendToAnalyticsService(event) {
    // In a real implementation, this would send to an analytics service
    if (window.gtag) {
      window.gtag('event', event.type, {
        event_category: 'tutorial',
        event_label: event.data.tutorialId || 'unknown',
        value: event.data.value || 1
      });
    }
    
    // Custom analytics endpoint
    if (process.env.REACT_APP_ANALYTICS_ENDPOINT) {
      fetch(process.env.REACT_APP_ANALYTICS_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event)
      }).catch(error => {
        console.warn('Failed to send analytics event:', error);
      });
    }
  }

  trackTutorialStart(tutorialId, tutorialData) {
    this.trackEvent(ANALYTICS_EVENTS.TUTORIAL_STARTED, {
      tutorialId,
      tutorialTitle: tutorialData.title,
      tutorialCategory: tutorialData.category,
      tutorialDifficulty: tutorialData.difficulty,
      estimatedDuration: tutorialData.estimatedDuration,
      stepCount: tutorialData.steps.length
    });
  }

  trackTutorialComplete(tutorialId, completionData) {
    this.trackEvent(ANALYTICS_EVENTS.TUTORIAL_COMPLETED, {
      tutorialId,
      timeSpent: completionData.timeSpent,
      stepsCompleted: completionData.stepsCompleted,
      stepsSkipped: completionData.stepsSkipped,
      helpRequests: completionData.helpRequests,
      errors: completionData.errors
    });
  }

  trackStepInteraction(stepId, action, data = {}) {
    this.trackEvent(`step_${action}`, {
      stepId,
      stepIndex: data.stepIndex,
      timeOnStep: data.timeOnStep,
      attempts: data.attempts,
      ...data
    });
  }

  trackPerformanceMetric(metric, value, context = {}) {
    this.trackEvent(ANALYTICS_EVENTS.PERFORMANCE_METRIC, {
      metric,
      value,
      context
    });
  }

  trackUserFeedback(rating, feedback, context = {}) {
    this.trackEvent(ANALYTICS_EVENTS.USER_FEEDBACK, {
      rating,
      feedback,
      context
    });
  }

  getSessionEvents() {
    return this.events;
  }

  stopCollection() {
    this.isCollecting = false;
  }
}

// Analytics Data Processor
export class TutorialAnalyticsProcessor {
  constructor() {
    this.cache = new Map();
  }

  async getAnalyticsData(timeRange = '7d', filters = {}) {
    const cacheKey = `${timeRange}_${JSON.stringify(filters)}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const events = this.loadEvents(timeRange, filters);
    const processedData = this.processEvents(events);
    
    this.cache.set(cacheKey, processedData);
    
    // Cache for 5 minutes
    setTimeout(() => this.cache.delete(cacheKey), 5 * 60 * 1000);
    
    return processedData;
  }

  loadEvents(timeRange, filters) {
    try {
      const allEvents = JSON.parse(
        localStorage.getItem('tutorial_analytics_events') || '[]'
      );
      
      const cutoffTime = this.getTimeRangeCutoff(timeRange);
      
      return allEvents.filter(event => {
        if (event.timestamp < cutoffTime) return false;
        
        // Apply filters
        if (filters.tutorialId && event.data.tutorialId !== filters.tutorialId) return false;
        if (filters.userId && event.userId !== filters.userId) return false;
        if (filters.eventType && event.type !== filters.eventType) return false;
        
        return true;
      });
    } catch (error) {
      console.error('Failed to load analytics events:', error);
      return [];
    }
  }

  getTimeRangeCutoff(timeRange) {
    const now = Date.now();
    const ranges = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000
    };
    
    return now - (ranges[timeRange] || ranges['7d']);
  }

  processEvents(events) {
    const metrics = {
      overview: this.calculateOverviewMetrics(events),
      engagement: this.calculateEngagementMetrics(events),
      completion: this.calculateCompletionMetrics(events),
      performance: this.calculatePerformanceMetrics(events),
      userBehavior: this.analyzeUserBehavior(events),
      troubleSpots: this.identifyTroubleSpots(events),
      trends: this.calculateTrends(events)
    };

    return metrics;
  }

  calculateOverviewMetrics(events) {
    const tutorialStarts = events.filter(e => e.type === ANALYTICS_EVENTS.TUTORIAL_STARTED);
    const tutorialCompletions = events.filter(e => e.type === ANALYTICS_EVENTS.TUTORIAL_COMPLETED);
    const uniqueUsers = new Set(events.map(e => e.userId)).size;
    const totalSessions = new Set(events.map(e => e.sessionId)).size;

    return {
      totalTutorialStarts: tutorialStarts.length,
      totalTutorialCompletions: tutorialCompletions.length,
      completionRate: tutorialStarts.length > 0 ? 
        (tutorialCompletions.length / tutorialStarts.length) * 100 : 0,
      uniqueUsers,
      totalSessions,
      averageSessionDuration: this.calculateAverageSessionDuration(events)
    };
  }

  calculateEngagementMetrics(events) {
    const helpRequests = events.filter(e => e.type === ANALYTICS_EVENTS.HELP_REQUESTED);
    const stepInteractions = events.filter(e => e.type.startsWith('step_'));
    const feedbackEvents = events.filter(e => e.type === ANALYTICS_EVENTS.USER_FEEDBACK);

    return {
      helpRequestRate: helpRequests.length,
      averageStepInteractions: stepInteractions.length / Math.max(1, new Set(events.map(e => e.sessionId)).size),
      feedbackSubmissions: feedbackEvents.length,
      averageRating: this.calculateAverageRating(feedbackEvents),
      engagementScore: this.calculateEngagementScore(events)
    };
  }

  calculateCompletionMetrics(events) {
    const tutorialsByCompletion = {};
    
    events.forEach(event => {
      if (event.type === ANALYTICS_EVENTS.TUTORIAL_STARTED) {
        const tutorialId = event.data.tutorialId;
        if (!tutorialsByCompletion[tutorialId]) {
          tutorialsByCompletion[tutorialId] = { starts: 0, completions: 0 };
        }
        tutorialsByCompletion[tutorialId].starts++;
      } else if (event.type === ANALYTICS_EVENTS.TUTORIAL_COMPLETED) {
        const tutorialId = event.data.tutorialId;
        if (!tutorialsByCompletion[tutorialId]) {
          tutorialsByCompletion[tutorialId] = { starts: 0, completions: 0 };
        }
        tutorialsByCompletion[tutorialId].completions++;
      }
    });

    const completionRates = Object.entries(tutorialsByCompletion).map(([tutorialId, data]) => ({
      tutorialId,
      starts: data.starts,
      completions: data.completions,
      completionRate: data.starts > 0 ? (data.completions / data.starts) * 100 : 0
    }));

    return {
      byTutorial: completionRates,
      averageCompletionRate: completionRates.reduce((sum, item) => sum + item.completionRate, 0) / Math.max(1, completionRates.length)
    };
  }

  calculatePerformanceMetrics(events) {
    const performanceEvents = events.filter(e => e.type === ANALYTICS_EVENTS.PERFORMANCE_METRIC);
    const errorEvents = events.filter(e => e.type === ANALYTICS_EVENTS.ERROR_OCCURRED);

    const metrics = {};
    performanceEvents.forEach(event => {
      const metric = event.data.metric;
      if (!metrics[metric]) {
        metrics[metric] = [];
      }
      metrics[metric].push(event.data.value);
    });

    const averageMetrics = {};
    Object.entries(metrics).forEach(([metric, values]) => {
      averageMetrics[metric] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    return {
      averageMetrics,
      errorRate: errorEvents.length,
      performanceScore: this.calculatePerformanceScore(averageMetrics)
    };
  }

  analyzeUserBehavior(events) {
    const userSessions = {};
    
    events.forEach(event => {
      const sessionId = event.sessionId;
      if (!userSessions[sessionId]) {
        userSessions[sessionId] = {
          userId: event.userId,
          events: [],
          startTime: event.timestamp,
          endTime: event.timestamp
        };
      }
      
      userSessions[sessionId].events.push(event);
      userSessions[sessionId].endTime = Math.max(userSessions[sessionId].endTime, event.timestamp);
    });

    const behaviorPatterns = Object.values(userSessions).map(session => {
      const duration = session.endTime - session.startTime;
      const eventCount = session.events.length;
      const helpRequests = session.events.filter(e => e.type === ANALYTICS_EVENTS.HELP_REQUESTED).length;
      const completions = session.events.filter(e => e.type === ANALYTICS_EVENTS.TUTORIAL_COMPLETED).length;
      
      return {
        sessionId: session.sessionId,
        userId: session.userId,
        duration,
        eventCount,
        helpRequests,
        completions,
        engagementLevel: this.categorizeEngagement(duration, eventCount, helpRequests)
      };
    });

    return {
      totalSessions: behaviorPatterns.length,
      averageSessionDuration: behaviorPatterns.reduce((sum, p) => sum + p.duration, 0) / Math.max(1, behaviorPatterns.length),
      engagementDistribution: this.calculateEngagementDistribution(behaviorPatterns),
      commonPatterns: this.identifyCommonPatterns(behaviorPatterns)
    };
  }

  identifyTroubleSpots(events) {
    const stepEvents = events.filter(e => e.type.startsWith('step_'));
    const stepAnalysis = {};

    stepEvents.forEach(event => {
      const stepId = event.data.stepId;
      if (!stepAnalysis[stepId]) {
        stepAnalysis[stepId] = {
          starts: 0,
          completions: 0,
          skips: 0,
          helpRequests: 0,
          totalTime: 0,
          errors: 0
        };
      }

      switch (event.type) {
        case 'step_started':
          stepAnalysis[stepId].starts++;
          break;
        case 'step_completed':
          stepAnalysis[stepId].completions++;
          if (event.data.timeOnStep) {
            stepAnalysis[stepId].totalTime += event.data.timeOnStep;
          }
          break;
        case 'step_skipped':
          stepAnalysis[stepId].skips++;
          break;
        case 'step_help_requested':
          stepAnalysis[stepId].helpRequests++;
          break;
        case 'step_error':
          stepAnalysis[stepId].errors++;
          break;
      }
    });

    const troubleSpots = Object.entries(stepAnalysis)
      .map(([stepId, data]) => ({
        stepId,
        ...data,
        completionRate: data.starts > 0 ? (data.completions / data.starts) * 100 : 0,
        skipRate: data.starts > 0 ? (data.skips / data.starts) * 100 : 0,
        helpRequestRate: data.starts > 0 ? (data.helpRequests / data.starts) * 100 : 0,
        averageTime: data.completions > 0 ? data.totalTime / data.completions : 0,
        troubleScore: this.calculateTroubleScore(data)
      }))
      .sort((a, b) => b.troubleScore - a.troubleScore);

    return troubleSpots.slice(0, 10); // Top 10 trouble spots
  }

  calculateTrends(events) {
    const timeGroups = this.groupEventsByTime(events);
    
    return {
      dailyStarts: this.calculateDailyTrend(timeGroups, ANALYTICS_EVENTS.TUTORIAL_STARTED),
      dailyCompletions: this.calculateDailyTrend(timeGroups, ANALYTICS_EVENTS.TUTORIAL_COMPLETED),
      dailyUsers: this.calculateDailyUserTrend(timeGroups),
      growthRate: this.calculateGrowthRate(timeGroups)
    };
  }

  // Helper methods
  calculateAverageSessionDuration(events) {
    const sessions = {};
    events.forEach(event => {
      if (!sessions[event.sessionId]) {
        sessions[event.sessionId] = { start: event.timestamp, end: event.timestamp };
      }
      sessions[event.sessionId].end = Math.max(sessions[event.sessionId].end, event.timestamp);
    });

    const durations = Object.values(sessions).map(s => s.end - s.start);
    return durations.reduce((sum, d) => sum + d, 0) / Math.max(1, durations.length);
  }

  calculateAverageRating(feedbackEvents) {
    if (feedbackEvents.length === 0) return 0;
    const ratings = feedbackEvents.map(e => e.data.rating).filter(r => typeof r === 'number');
    return ratings.reduce((sum, r) => sum + r, 0) / Math.max(1, ratings.length);
  }

  calculateEngagementScore(events) {
    // Complex engagement calculation based on various factors
    const factors = {
      eventCount: events.length,
      uniqueSessions: new Set(events.map(e => e.sessionId)).size,
      completions: events.filter(e => e.type === ANALYTICS_EVENTS.TUTORIAL_COMPLETED).length,
      helpRequests: events.filter(e => e.type === ANALYTICS_EVENTS.HELP_REQUESTED).length,
      feedback: events.filter(e => e.type === ANALYTICS_EVENTS.USER_FEEDBACK).length
    };

    // Weighted score calculation
    return Math.min(100, 
      (factors.eventCount * 0.1) +
      (factors.uniqueSessions * 2) +
      (factors.completions * 5) +
      (factors.helpRequests * 1) +
      (factors.feedback * 3)
    );
  }

  calculatePerformanceScore(metrics) {
    // Calculate overall performance score based on various metrics
    const weights = {
      renderTime: -0.1, // Lower is better
      memoryUsage: -0.05, // Lower is better
      frameRate: 0.1, // Higher is better
      loadTime: -0.2 // Lower is better
    };

    let score = 50; // Base score
    Object.entries(weights).forEach(([metric, weight]) => {
      if (metrics[metric] !== undefined) {
        score += metrics[metric] * weight;
      }
    });

    return Math.max(0, Math.min(100, score));
  }

  calculateTroubleScore(stepData) {
    const weights = {
      skipRate: 0.3,
      helpRequestRate: 0.2,
      errorRate: 0.4,
      lowCompletionRate: 0.1
    };

    const skipRate = stepData.starts > 0 ? (stepData.skips / stepData.starts) * 100 : 0;
    const helpRequestRate = stepData.starts > 0 ? (stepData.helpRequests / stepData.starts) * 100 : 0;
    const errorRate = stepData.starts > 0 ? (stepData.errors / stepData.starts) * 100 : 0;
    const completionRate = stepData.starts > 0 ? (stepData.completions / stepData.starts) * 100 : 0;
    const lowCompletionRate = Math.max(0, 100 - completionRate);

    return (
      skipRate * weights.skipRate +
      helpRequestRate * weights.helpRequestRate +
      errorRate * weights.errorRate +
      lowCompletionRate * weights.lowCompletionRate
    );
  }

  categorizeEngagement(duration, eventCount, helpRequests) {
    if (duration < 30000) return 'low'; // Less than 30 seconds
    if (duration > 300000 && eventCount > 20) return 'high'; // More than 5 minutes and many events
    if (helpRequests > 3) return 'struggling';
    return 'medium';
  }

  calculateEngagementDistribution(patterns) {
    const distribution = { low: 0, medium: 0, high: 0, struggling: 0 };
    patterns.forEach(pattern => {
      distribution[pattern.engagementLevel]++;
    });
    
    const total = patterns.length;
    Object.keys(distribution).forEach(key => {
      distribution[key] = total > 0 ? (distribution[key] / total) * 100 : 0;
    });
    
    return distribution;
  }

  identifyCommonPatterns(patterns) {
    // Identify common user behavior patterns
    const patternTypes = {
      quickCompleters: patterns.filter(p => p.duration < 60000 && p.completions > 0).length,
      thoroughLearners: patterns.filter(p => p.duration > 300000 && p.helpRequests > 2).length,
      strugglers: patterns.filter(p => p.helpRequests > 5 && p.completions === 0).length,
      browsers: patterns.filter(p => p.eventCount < 5 && p.duration < 30000).length
    };

    return patternTypes;
  }

  groupEventsByTime(events) {
    const groups = {};
    events.forEach(event => {
      const date = new Date(event.timestamp).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(event);
    });
    return groups;
  }

  calculateDailyTrend(timeGroups, eventType) {
    const trend = {};
    Object.entries(timeGroups).forEach(([date, events]) => {
      trend[date] = events.filter(e => e.type === eventType).length;
    });
    return trend;
  }

  calculateDailyUserTrend(timeGroups) {
    const trend = {};
    Object.entries(timeGroups).forEach(([date, events]) => {
      trend[date] = new Set(events.map(e => e.userId)).size;
    });
    return trend;
  }

  calculateGrowthRate(timeGroups) {
    const dates = Object.keys(timeGroups).sort();
    if (dates.length < 2) return 0;

    const firstPeriod = timeGroups[dates[0]].length;
    const lastPeriod = timeGroups[dates[dates.length - 1]].length;

    return firstPeriod > 0 ? ((lastPeriod - firstPeriod) / firstPeriod) * 100 : 0;
  }
}

// Analytics Dashboard Component
export const TutorialAnalyticsDashboard = ({ timeRange = '7d', filters = {} }) => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const processor = useMemo(() => new TutorialAnalyticsProcessor(), []);

  useEffect(() => {
    const loadAnalytics = async () => {
      setLoading(true);
      try {
        const data = await processor.getAnalyticsData(timeRange, filters);
        setAnalyticsData(data);
      } catch (error) {
        console.error('Failed to load analytics data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAnalytics();
  }, [timeRange, filters, processor]);

  if (loading || !analyticsData) {
    return <div>Loading analytics...</div>;
  }

  return (
    <div className="tutorial-analytics-dashboard">
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tutorial Starts"
              value={analyticsData.overview.totalTutorialStarts}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Completion Rate"
              value={analyticsData.overview.completionRate}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Unique Users"
              value={analyticsData.overview.uniqueUsers}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Avg Session Duration"
              value={Math.round(analyticsData.overview.averageSessionDuration / 1000 / 60)}
              suffix="min"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="Completion Rates by Tutorial">
            <Table
              dataSource={analyticsData.completion.byTutorial}
              columns={[
                { title: 'Tutorial', dataIndex: 'tutorialId', key: 'tutorialId' },
                { title: 'Starts', dataIndex: 'starts', key: 'starts' },
                { title: 'Completions', dataIndex: 'completions', key: 'completions' },
                { 
                  title: 'Rate', 
                  dataIndex: 'completionRate', 
                  key: 'completionRate',
                  render: (rate) => `${rate.toFixed(1)}%`
                }
              ]}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Trouble Spots">
            <Table
              dataSource={analyticsData.troubleSpots.slice(0, 5)}
              columns={[
                { title: 'Step', dataIndex: 'stepId', key: 'stepId' },
                { 
                  title: 'Skip Rate', 
                  dataIndex: 'skipRate', 
                  key: 'skipRate',
                  render: (rate) => `${rate.toFixed(1)}%`
                },
                { 
                  title: 'Help Requests', 
                  dataIndex: 'helpRequestRate', 
                  key: 'helpRequestRate',
                  render: (rate) => `${rate.toFixed(1)}%`
                },
                { 
                  title: 'Trouble Score', 
                  dataIndex: 'troubleScore', 
                  key: 'troubleScore',
                  render: (score) => (
                    <Progress 
                      percent={score} 
                      size="small" 
                      status={score > 70 ? 'exception' : score > 40 ? 'active' : 'success'}
                      showInfo={false}
                    />
                  )
                }
              ]}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// Global analytics instance
let globalAnalyticsCollector = null;

export const initializeAnalytics = (userId) => {
  globalAnalyticsCollector = new TutorialAnalyticsCollector(userId);
  return globalAnalyticsCollector;
};

export const getAnalyticsCollector = () => {
  if (!globalAnalyticsCollector) {
    globalAnalyticsCollector = new TutorialAnalyticsCollector();
  }
  return globalAnalyticsCollector;
};

export default {
  TutorialAnalyticsCollector,
  TutorialAnalyticsProcessor,
  TutorialAnalyticsDashboard,
  ANALYTICS_EVENTS,
  initializeAnalytics,
  getAnalyticsCollector
};
