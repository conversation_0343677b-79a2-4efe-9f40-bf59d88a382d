/**
 * Tutorial Visual Enhancements
 * 
 * Enhanced visual feedback, animations, and highlighting for the tutorial system.
 * Provides better user guidance through improved visual cues and interactions.
 */

import React, { useEffect, useRef } from 'react';
import styled, { keyframes, css } from 'styled-components';

// Enhanced Animations
const pulseGlow = keyframes`
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0.3);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    transform: scale(1);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

const bounceIn = keyframes`
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
  }
  70% {
    transform: scale(0.9) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
`;

const slideInFromRight = keyframes`
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
`;

const fadeInUp = keyframes`
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
`;

// Enhanced Highlight Overlay
const EnhancedHighlightOverlay = styled.div`
  position: absolute;
  pointer-events: none;
  border-radius: ${props => props.borderRadius || '8px'};
  z-index: 9999;
  
  ${props => props.highlightType === 'pulse' && css`
    animation: ${pulseGlow} 2s infinite;
    border: 3px solid #1890ff;
    background: rgba(24, 144, 255, 0.1);
  `}
  
  ${props => props.highlightType === 'shimmer' && css`
    border: 2px solid #1890ff;
    background: linear-gradient(
      90deg,
      rgba(24, 144, 255, 0.1) 0%,
      rgba(24, 144, 255, 0.3) 50%,
      rgba(24, 144, 255, 0.1) 100%
    );
    background-size: 200px 100%;
    animation: ${shimmer} 2s infinite;
  `}
  
  ${props => props.highlightType === 'glow' && css`
    border: 2px solid #1890ff;
    background: rgba(24, 144, 255, 0.05);
    box-shadow: 
      0 0 20px rgba(24, 144, 255, 0.3),
      inset 0 0 20px rgba(24, 144, 255, 0.1);
  `}
  
  ${props => props.highlightType === 'dashed' && css`
    border: 3px dashed #1890ff;
    background: rgba(24, 144, 255, 0.05);
    animation: ${pulseGlow} 3s infinite;
  `}
`;

// Enhanced Tooltip with Animations
const EnhancedTooltip = styled.div`
  position: absolute;
  background: white;
  border-radius: 12px;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  max-width: 350px;
  z-index: 10000;
  
  animation: ${props => {
    switch (props.animationType) {
      case 'bounceIn':
        return css`${bounceIn} 0.6s ease-out`;
      case 'slideIn':
        return css`${slideInFromRight} 0.4s ease-out`;
      case 'fadeIn':
      default:
        return css`${fadeInUp} 0.3s ease-out`;
    }
  }};
  
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    
    ${props => props.position === 'top' && css`
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      border-width: 10px 10px 0 10px;
      border-color: white transparent transparent transparent;
    `}
    
    ${props => props.position === 'bottom' && css`
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      border-width: 0 10px 10px 10px;
      border-color: transparent transparent white transparent;
    `}
    
    ${props => props.position === 'left' && css`
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
      border-width: 10px 0 10px 10px;
      border-color: transparent transparent transparent white;
    `}
    
    ${props => props.position === 'right' && css`
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
      border-width: 10px 10px 10px 0;
      border-color: transparent white transparent transparent;
    `}
  }
`;

// Progress Indicator with Enhanced Visuals
const ProgressRing = styled.div`
  position: relative;
  width: 60px;
  height: 60px;
  
  svg {
    transform: rotate(-90deg);
    width: 100%;
    height: 100%;
  }
  
  .progress-ring-background {
    fill: none;
    stroke: #f0f0f0;
    stroke-width: 4;
  }
  
  .progress-ring-progress {
    fill: none;
    stroke: #1890ff;
    stroke-width: 4;
    stroke-linecap: round;
    transition: stroke-dashoffset 0.5s ease-in-out;
  }
  
  .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: bold;
    color: #1890ff;
  }
`;

// Interactive Element Highlighter
const InteractiveHighlighter = styled.div`
  position: absolute;
  pointer-events: none;
  z-index: 9998;
  
  &::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border: 2px solid #52c41a;
    border-radius: 12px;
    background: rgba(82, 196, 26, 0.1);
    animation: ${pulseGlow} 1.5s infinite;
  }
  
  &::after {
    content: '👆 Click here';
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: #52c41a;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    animation: ${bounceIn} 0.6s ease-out;
  }
`;

// Spotlight Effect
const SpotlightOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9997;
  pointer-events: none;
  
  ${props => props.spotlightArea && css`
    mask: radial-gradient(
      circle at ${props.spotlightArea.x}px ${props.spotlightArea.y}px,
      transparent ${props.spotlightArea.radius}px,
      black ${props.spotlightArea.radius + 20}px
    );
    -webkit-mask: radial-gradient(
      circle at ${props.spotlightArea.x}px ${props.spotlightArea.y}px,
      transparent ${props.spotlightArea.radius}px,
      black ${props.spotlightArea.radius + 20}px
    );
  `}
`;

// Component for Enhanced Visual Feedback
export const TutorialVisualFeedback = ({
  targetElement,
  highlightType = 'pulse',
  showSpotlight = false,
  showInteractiveHint = false,
  animationType = 'fadeIn'
}) => {
  const highlightRef = useRef(null);
  const [elementRect, setElementRect] = React.useState(null);

  useEffect(() => {
    if (targetElement) {
      const updatePosition = () => {
        const rect = targetElement.getBoundingClientRect();
        setElementRect(rect);
      };

      updatePosition();
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition);

      return () => {
        window.removeEventListener('resize', updatePosition);
        window.removeEventListener('scroll', updatePosition);
      };
    }
  }, [targetElement]);

  if (!elementRect) return null;

  const spotlightArea = showSpotlight ? {
    x: elementRect.left + elementRect.width / 2,
    y: elementRect.top + elementRect.height / 2,
    radius: Math.max(elementRect.width, elementRect.height) / 2 + 20
  } : null;

  return (
    <>
      {showSpotlight && <SpotlightOverlay spotlightArea={spotlightArea} />}
      
      <EnhancedHighlightOverlay
        ref={highlightRef}
        highlightType={highlightType}
        style={{
          top: elementRect.top - 4,
          left: elementRect.left - 4,
          width: elementRect.width + 8,
          height: elementRect.height + 8
        }}
      />
      
      {showInteractiveHint && (
        <InteractiveHighlighter
          style={{
            top: elementRect.top,
            left: elementRect.left,
            width: elementRect.width,
            height: elementRect.height
          }}
        />
      )}
    </>
  );
};

// Enhanced Progress Ring Component
export const TutorialProgressRing = ({ progress, total, size = 60 }) => {
  const radius = (size - 8) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDashoffset = circumference - (progress / total) * circumference;

  return (
    <ProgressRing style={{ width: size, height: size }}>
      <svg>
        <circle
          className="progress-ring-background"
          cx={size / 2}
          cy={size / 2}
          r={radius}
        />
        <circle
          className="progress-ring-progress"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
        />
      </svg>
      <div className="progress-text">
        {progress}/{total}
      </div>
    </ProgressRing>
  );
};

// CSS Injection for Global Tutorial Styles
export const injectTutorialStyles = () => {
  const styleId = 'tutorial-visual-enhancements';
  
  if (document.getElementById(styleId)) {
    return;
  }

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    .tutorial-highlight-pulse {
      animation: tutorial-pulse 2s infinite !important;
    }
    
    .tutorial-highlight-glow {
      box-shadow: 0 0 20px rgba(24, 144, 255, 0.5) !important;
      border: 2px solid #1890ff !important;
    }
    
    .tutorial-interactive-element {
      position: relative !important;
      z-index: 10001 !important;
    }
    
    .tutorial-disabled-overlay {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0, 0, 0, 0.3) !important;
      z-index: 9996 !important;
      pointer-events: auto !important;
    }
    
    @keyframes tutorial-pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
      }
    }
  `;
  
  document.head.appendChild(style);
};

export default {
  TutorialVisualFeedback,
  TutorialProgressRing,
  EnhancedHighlightOverlay,
  EnhancedTooltip,
  injectTutorialStyles
};
