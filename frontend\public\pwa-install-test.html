<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Installation Test - App Builder 201</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#2563EB">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .status-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #10b981;
            background: #f0fdf4;
        }
        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
        .warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }
        .info {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .install-button {
            background: #10b981;
            font-size: 18px;
            padding: 15px 30px;
        }
        .install-button:hover {
            background: #059669;
        }
        .code {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #10b981;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 PWA Installation Test - App Builder 201</h1>
        
        <div id="installation-status" class="status-section">
            <h3>Installation Status</h3>
            <div id="status-info">Checking PWA installation status...</div>
        </div>

        <div id="manifest-info" class="status-section">
            <h3>Manifest Information</h3>
            <div id="manifest-details">Loading manifest details...</div>
        </div>

        <div id="install-prompt" class="status-section" style="display: none;">
            <h3>🚀 Install App Builder 201</h3>
            <p>Install this app on your device for a better experience!</p>
            <button id="install-btn" class="install-button">Install App</button>
        </div>

        <div class="status-section">
            <h3>PWA Features</h3>
            <ul class="feature-list">
                <li>Offline functionality with cached content</li>
                <li>Fast loading with service worker caching</li>
                <li>Native app-like experience</li>
                <li>Home screen installation</li>
                <li>Standalone display mode</li>
                <li>Push notifications support</li>
            </ul>
        </div>

        <div class="status-section">
            <h3>Test Actions</h3>
            <button onclick="checkInstallation()">Check Installation Status</button>
            <button onclick="testManifest()">Test Manifest</button>
            <button onclick="testServiceWorker()">Test Service Worker</button>
            <button onclick="simulateInstall()">Simulate Install Prompt</button>
        </div>

        <div id="test-results" class="status-section">
            <h3>Test Results</h3>
            <div id="results-content">Click test buttons to see results...</div>
        </div>

        <div id="console-output" class="status-section">
            <h3>Console Output</h3>
            <div class="code" id="console-log"></div>
        </div>
    </div>

    <script>
        let deferredPrompt;
        let consoleOutput = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            consoleOutput.push(logEntry);
            
            const consoleDiv = document.getElementById('console-log');
            consoleDiv.innerHTML = consoleOutput.slice(-15).join('<br>');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            
            console.log(logEntry);
        }

        function updateResults(content) {
            document.getElementById('results-content').innerHTML = content;
        }

        // Listen for the beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            log('Install prompt available');
            e.preventDefault();
            deferredPrompt = e;
            
            const installPrompt = document.getElementById('install-prompt');
            installPrompt.style.display = 'block';
        });

        // Handle install button click
        document.getElementById('install-btn').addEventListener('click', async () => {
            if (deferredPrompt) {
                log('Showing install prompt');
                deferredPrompt.prompt();
                
                const { outcome } = await deferredPrompt.userChoice;
                log(`Install prompt result: ${outcome}`);
                
                if (outcome === 'accepted') {
                    updateResults('<div class="status-section">✅ App installation accepted!</div>');
                } else {
                    updateResults('<div class="status-section warning">❌ App installation declined</div>');
                }
                
                deferredPrompt = null;
                document.getElementById('install-prompt').style.display = 'none';
            } else {
                log('No install prompt available');
                updateResults('<div class="status-section error">No install prompt available</div>');
            }
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', (evt) => {
            log('App was installed successfully');
            updateResults('<div class="status-section">🎉 App installed successfully!</div>');
        });

        async function checkInstallation() {
            log('Checking installation status...');
            
            try {
                const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                                   window.navigator.standalone === true;
                
                const statusInfo = document.getElementById('status-info');
                
                if (isInstalled) {
                    statusInfo.innerHTML = `
                        <div class="status-section">
                            ✅ App is currently installed and running in standalone mode
                        </div>
                    `;
                    log('App is installed');
                } else {
                    statusInfo.innerHTML = `
                        <div class="status-section info">
                            ℹ️ App is running in browser mode (not installed)
                        </div>
                    `;
                    log('App is not installed');
                }
                
                updateResults(`Installation Status: ${isInstalled ? 'Installed' : 'Not Installed'}`);
                
            } catch (error) {
                log(`Error checking installation: ${error.message}`, 'error');
                updateResults(`<div class="error">Error: ${error.message}</div>`);
            }
        }

        async function testManifest() {
            log('Testing manifest...');
            
            try {
                const response = await fetch('/manifest.json');
                if (!response.ok) {
                    throw new Error('Manifest not found');
                }
                
                const manifest = await response.json();
                
                const manifestDetails = document.getElementById('manifest-details');
                manifestDetails.innerHTML = `
                    <div class="code">
                        <strong>Name:</strong> ${manifest.name || 'Not specified'}<br>
                        <strong>Short Name:</strong> ${manifest.short_name || 'Not specified'}<br>
                        <strong>Display:</strong> ${manifest.display || 'Not specified'}<br>
                        <strong>Theme Color:</strong> ${manifest.theme_color || 'Not specified'}<br>
                        <strong>Background Color:</strong> ${manifest.background_color || 'Not specified'}<br>
                        <strong>Start URL:</strong> ${manifest.start_url || 'Not specified'}<br>
                        <strong>Icons:</strong> ${manifest.icons ? manifest.icons.length : 0} defined<br>
                        <strong>Shortcuts:</strong> ${manifest.shortcuts ? manifest.shortcuts.length : 0} defined
                    </div>
                `;
                
                log('Manifest loaded successfully');
                updateResults('✅ Manifest is valid and accessible');
                
            } catch (error) {
                log(`Manifest test failed: ${error.message}`, 'error');
                updateResults(`<div class="error">Manifest Error: ${error.message}</div>`);
            }
        }

        async function testServiceWorker() {
            log('Testing service worker...');
            
            try {
                if (!('serviceWorker' in navigator)) {
                    throw new Error('Service Worker not supported');
                }
                
                const registration = await navigator.serviceWorker.getRegistration();
                
                if (registration) {
                    updateResults(`
                        <div class="status-section">
                            ✅ Service Worker registered<br>
                            Scope: ${registration.scope}<br>
                            State: ${registration.active ? registration.active.state : 'No active worker'}
                        </div>
                    `);
                    log('Service Worker is registered and active');
                } else {
                    updateResults('<div class="warning">⚠️ Service Worker not registered</div>');
                    log('Service Worker not found');
                }
                
            } catch (error) {
                log(`Service Worker test failed: ${error.message}`, 'error');
                updateResults(`<div class="error">Service Worker Error: ${error.message}</div>`);
            }
        }

        function simulateInstall() {
            log('Simulating install prompt...');
            
            if (deferredPrompt) {
                document.getElementById('install-btn').click();
            } else {
                updateResults(`
                    <div class="warning">
                        ⚠️ Install prompt not available<br>
                        This could be because:<br>
                        • App is already installed<br>
                        • Browser doesn't support PWA installation<br>
                        • PWA criteria not met<br>
                        • User has previously dismissed the prompt
                    </div>
                `);
                log('Install prompt not available');
            }
        }

        // Initialize on page load
        window.addEventListener('load', async () => {
            log('PWA Installation Test page loaded');
            await checkInstallation();
            await testManifest();
            await testServiceWorker();
        });
    </script>
</body>
</html>
