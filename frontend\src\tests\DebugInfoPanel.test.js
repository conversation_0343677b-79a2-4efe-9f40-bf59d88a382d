import React from 'react';
import { render, screen } from '@testing-library/react';
import DebugInfoPanel from '../components/debug/DebugInfoPanel';

describe('DebugInfoPanel Component', () => {
  // Mock window.MOCK_SERVERS_ENABLED
  const originalMockServersEnabled = window.MOCK_SERVERS_ENABLED;

  beforeEach(() => {
    window.MOCK_SERVERS_ENABLED = false;
  });

  afterEach(() => {
    window.MOCK_SERVERS_ENABLED = originalMockServersEnabled;
  });

  test('renders WebSocket status indicator when websocketStatus is provided', () => {
    render(<DebugInfoPanel websocketStatus="connected" />);
    
    expect(screen.getByText(/WebSocket: connected/i)).toBeInTheDocument();
    expect(screen.getByText(/WebSocket: connected/i).className).toContain('connected');
  });

  test('does not render WebSocket status indicator when websocketStatus is not provided', () => {
    render(<DebugInfoPanel />);
    
    expect(screen.queryByText(/WebSocket:/i)).not.toBeInTheDocument();
  });

  test('renders API endpoints status indicator when apiCheckResults is provided and endpoints are available', () => {
    const apiCheckResults = {
      anyAvailable: true,
      available: [{ endpoint: '/api/test' }, { endpoint: '/api/users' }]
    };
    
    render(<DebugInfoPanel apiCheckResults={apiCheckResults} />);
    
    expect(screen.getByText(/API: 2 endpoints/i)).toBeInTheDocument();
    expect(screen.getByText(/API: 2 endpoints/i).className).toContain('available');
  });

  test('renders API endpoints status indicator when apiCheckResults is provided and no endpoints are available', () => {
    const apiCheckResults = {
      anyAvailable: false,
      available: []
    };
    
    render(<DebugInfoPanel apiCheckResults={apiCheckResults} />);
    
    expect(screen.getByText(/API: Unavailable/i)).toBeInTheDocument();
    expect(screen.getByText(/API: Unavailable/i).className).toContain('unavailable');
  });

  test('does not render API endpoints status indicator when apiCheckResults is not provided', () => {
    render(<DebugInfoPanel />);
    
    expect(screen.queryByText(/API:/i)).not.toBeInTheDocument();
  });

  test('renders Backend status indicator when backendStatus is provided and backend is available', () => {
    const backendStatus = {
      available: true
    };
    
    render(<DebugInfoPanel backendStatus={backendStatus} />);
    
    expect(screen.getByText(/Backend: Available/i)).toBeInTheDocument();
    expect(screen.getByText(/Backend: Available/i).className).toContain('available');
  });

  test('renders Backend status indicator when backendStatus is provided and backend is not available', () => {
    const backendStatus = {
      available: false
    };
    
    render(<DebugInfoPanel backendStatus={backendStatus} />);
    
    expect(screen.getByText(/Backend: Unavailable/i)).toBeInTheDocument();
    expect(screen.getByText(/Backend: Unavailable/i).className).toContain('unavailable');
  });

  test('does not render Backend status indicator when backendStatus is not provided', () => {
    render(<DebugInfoPanel />);
    
    expect(screen.queryByText(/Backend:/i)).not.toBeInTheDocument();
  });

  test('renders WebSocket server status indicator when wsStatus is provided and server is available', () => {
    const wsStatus = {
      available: true
    };
    
    render(<DebugInfoPanel wsStatus={wsStatus} />);
    
    expect(screen.getByText(/WS Server: Available/i)).toBeInTheDocument();
    expect(screen.getByText(/WS Server: Available/i).className).toContain('available');
  });

  test('renders WebSocket server status indicator when wsStatus is provided and server is not available', () => {
    const wsStatus = {
      available: false
    };
    
    render(<DebugInfoPanel wsStatus={wsStatus} />);
    
    expect(screen.getByText(/WS Server: Unavailable/i)).toBeInTheDocument();
    expect(screen.getByText(/WS Server: Unavailable/i).className).toContain('unavailable');
  });

  test('does not render WebSocket server status indicator when wsStatus is not provided', () => {
    render(<DebugInfoPanel />);
    
    expect(screen.queryByText(/WS Server:/i)).not.toBeInTheDocument();
  });

  test('renders Mock servers indicator when window.MOCK_SERVERS_ENABLED is true', () => {
    window.MOCK_SERVERS_ENABLED = true;
    
    render(<DebugInfoPanel />);
    
    expect(screen.getByText(/Using Mock Servers/i)).toBeInTheDocument();
  });

  test('does not render Mock servers indicator when window.MOCK_SERVERS_ENABLED is false', () => {
    window.MOCK_SERVERS_ENABLED = false;
    
    render(<DebugInfoPanel />);
    
    expect(screen.queryByText(/Using Mock Servers/i)).not.toBeInTheDocument();
  });

  test('renders Theme mode indicator with the provided theme mode', () => {
    render(<DebugInfoPanel themeMode="dark" />);
    
    expect(screen.getByText(/Theme: dark/i)).toBeInTheDocument();
    
    // Check if the background color is set correctly for dark mode
    const themeModeIndicator = screen.getByText(/Theme: dark/i);
    expect(themeModeIndicator.style.backgroundColor).toBe('#111827');
  });

  test('renders Theme mode indicator with light theme mode by default', () => {
    render(<DebugInfoPanel />);
    
    expect(screen.getByText(/Theme:/i)).toBeInTheDocument();
    
    // Check if the background color is set correctly for light mode
    const themeModeIndicator = screen.getByText(/Theme:/i);
    expect(themeModeIndicator.style.backgroundColor).toBe('#60A5FA');
  });
});
