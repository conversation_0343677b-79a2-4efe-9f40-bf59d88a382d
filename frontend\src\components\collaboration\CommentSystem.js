import React, { useState, useRef, useEffect } from 'react';
import { 
  Button, 
  Input, 
  Avatar, 
  Tooltip, 
  Popover, 
  Badge, 
  Dropdown, 
  Menu,
  message,
  Modal,
  List,
  Typography
} from 'antd';
import { 
  CommentOutlined, 
  SendOutlined, 
  MoreOutlined, 
  CheckOutlined,
  EditOutlined,
  DeleteOutlined,
  ReplyArrowOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useCollaboration } from '../../contexts/CollaborationContext';
import { useAuth } from '../../contexts/AuthContext';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

// Styled components
const CommentBubble = styled.div`
  position: absolute;
  width: 24px;
  height: 24px;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
  
  .anticon {
    color: white;
    font-size: 12px;
  }
`;

const CommentPanel = styled.div`
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
`;

const CommentHeader = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafafa;
`;

const CommentList = styled.div`
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
`;

const CommentItem = styled.div`
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 8px;
  background: ${props => props.isReply ? '#f8f9fa' : 'white'};
  border: 1px solid ${props => props.isReply ? '#e9ecef' : '#f0f0f0'};
  margin-left: ${props => props.isReply ? '20px' : '0'};
`;

const CommentMeta = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const CommentAuthor = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CommentContent = styled.div`
  margin-bottom: 8px;
  line-height: 1.5;
`;

const CommentActions = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
`;

const CommentInput = styled.div`
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
`;

const MentionSuggestions = styled.div`
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 150px;
  overflow-y: auto;
  z-index: 1001;
`;

const MentionItem = styled.div`
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background: #f5f5f5;
  }
`;

// Comment bubble component that appears on the canvas
export const CommentBubble = ({ 
  comment, 
  position, 
  onClick, 
  hasUnread = false 
}) => {
  return (
    <CommentBubble
      style={{
        left: position.x,
        top: position.y,
        backgroundColor: comment.status === 'resolved' ? '#52c41a' : '#1890ff'
      }}
      onClick={onClick}
    >
      <Badge dot={hasUnread} offset={[-2, 2]}>
        <CommentOutlined />
      </Badge>
    </CommentBubble>
  );
};

// Individual comment component
const Comment = ({ 
  comment, 
  onReply, 
  onEdit, 
  onDelete, 
  onResolve, 
  isReply = false,
  currentUser 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [showReplyInput, setShowReplyInput] = useState(false);

  const handleEdit = () => {
    onEdit(comment.id, editContent);
    setIsEditing(false);
  };

  const handleResolve = () => {
    onResolve(comment.id);
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return date.toLocaleDateString();
  };

  const menu = (
    <Menu>
      {!isReply && (
        <Menu.Item key="reply" icon={<ReplyArrowOutlined />} onClick={() => setShowReplyInput(true)}>
          Reply
        </Menu.Item>
      )}
      {comment.author.username === currentUser?.username && (
        <>
          <Menu.Item key="edit" icon={<EditOutlined />} onClick={() => setIsEditing(true)}>
            Edit
          </Menu.Item>
          <Menu.Item key="delete" icon={<DeleteOutlined />} onClick={() => onDelete(comment.id)}>
            Delete
          </Menu.Item>
        </>
      )}
      {!isReply && comment.status !== 'resolved' && (
        <Menu.Item key="resolve" icon={<CheckOutlined />} onClick={handleResolve}>
          Resolve
        </Menu.Item>
      )}
    </Menu>
  );

  return (
    <CommentItem isReply={isReply}>
      <CommentMeta>
        <CommentAuthor>
          <Avatar size="small" style={{ backgroundColor: '#1890ff' }}>
            {comment.author.username.charAt(0).toUpperCase()}
          </Avatar>
          <Text strong>{comment.author.username}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {formatTime(comment.created_at)}
          </Text>
        </CommentAuthor>
        <Dropdown overlay={menu} trigger={['click']}>
          <Button type="text" size="small" icon={<MoreOutlined />} />
        </Dropdown>
      </CommentMeta>
      
      <CommentContent>
        {isEditing ? (
          <div>
            <TextArea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              autoSize={{ minRows: 2, maxRows: 4 }}
            />
            <div style={{ marginTop: 8, display: 'flex', gap: 8 }}>
              <Button size="small" type="primary" onClick={handleEdit}>
                Save
              </Button>
              <Button size="small" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <Paragraph style={{ margin: 0 }}>
            {comment.content}
          </Paragraph>
        )}
      </CommentContent>

      {comment.status === 'resolved' && (
        <div style={{ fontSize: '12px', color: '#52c41a', fontStyle: 'italic' }}>
          ✓ Resolved
        </div>
      )}

      {/* Replies */}
      {comment.replies && comment.replies.map(reply => (
        <Comment
          key={reply.id}
          comment={reply}
          onEdit={onEdit}
          onDelete={onDelete}
          isReply={true}
          currentUser={currentUser}
        />
      ))}

      {/* Reply input */}
      {showReplyInput && (
        <div style={{ marginTop: 8 }}>
          <CommentInput
            onSubmit={(content) => {
              onReply(comment.id, content);
              setShowReplyInput(false);
            }}
            onCancel={() => setShowReplyInput(false)}
            placeholder="Write a reply..."
            autoFocus
          />
        </div>
      )}
    </CommentItem>
  );
};

// Comment input component with @mention support
const CommentInput = ({ 
  onSubmit, 
  onCancel, 
  placeholder = "Add a comment...",
  autoFocus = false 
}) => {
  const [content, setContent] = useState('');
  const [showMentions, setShowMentions] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionPosition, setMentionPosition] = useState(0);
  const inputRef = useRef();
  const { activeParticipants } = useCollaboration();

  const handleSubmit = () => {
    if (content.trim()) {
      onSubmit(content);
      setContent('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setContent(value);

    // Check for @mentions
    const cursorPosition = e.target.selectionStart;
    const textBeforeCursor = value.substring(0, cursorPosition);
    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      setShowMentions(true);
      setMentionQuery(mentionMatch[1]);
      setMentionPosition(cursorPosition - mentionMatch[0].length);
    } else {
      setShowMentions(false);
    }
  };

  const insertMention = (username) => {
    const beforeMention = content.substring(0, mentionPosition);
    const afterMention = content.substring(mentionPosition + mentionQuery.length + 1);
    const newContent = `${beforeMention}@${username} ${afterMention}`;
    
    setContent(newContent);
    setShowMentions(false);
    inputRef.current?.focus();
  };

  const filteredParticipants = activeParticipants.filter(p =>
    p.username.toLowerCase().includes(mentionQuery.toLowerCase())
  );

  return (
    <div style={{ position: 'relative' }}>
      {showMentions && filteredParticipants.length > 0 && (
        <MentionSuggestions>
          {filteredParticipants.map(participant => (
            <MentionItem
              key={participant.user_id}
              onClick={() => insertMention(participant.username)}
            >
              <Avatar size="small" style={{ backgroundColor: '#1890ff' }}>
                {participant.username.charAt(0).toUpperCase()}
              </Avatar>
              {participant.username}
            </MentionItem>
          ))}
        </MentionSuggestions>
      )}
      
      <TextArea
        ref={inputRef}
        value={content}
        onChange={handleInputChange}
        onKeyPress={handleKeyPress}
        placeholder={placeholder}
        autoSize={{ minRows: 2, maxRows: 4 }}
        autoFocus={autoFocus}
      />
      
      <div style={{ marginTop: 8, display: 'flex', gap: 8, justifyContent: 'flex-end' }}>
        <Button size="small" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          size="small" 
          type="primary" 
          icon={<SendOutlined />}
          onClick={handleSubmit}
          disabled={!content.trim()}
        >
          Send
        </Button>
      </div>
    </div>
  );
};

// Main comment panel component
export const CommentPanel = ({ 
  comments, 
  onAddComment, 
  onEditComment, 
  onDeleteComment, 
  onResolveComment,
  onReplyToComment,
  visible,
  onClose,
  title = "Comments"
}) => {
  const { user } = useAuth();
  const [showInput, setShowInput] = useState(false);

  const handleAddComment = (content) => {
    onAddComment(content);
    setShowInput(false);
  };

  const handleReply = (parentId, content) => {
    onReplyToComment(parentId, content);
  };

  return (
    <CommentPanel>
      <CommentHeader>
        <Text strong>{title}</Text>
        <Button 
          type="primary" 
          size="small" 
          icon={<CommentOutlined />}
          onClick={() => setShowInput(true)}
        >
          Add Comment
        </Button>
      </CommentHeader>

      <CommentList>
        {comments.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
            No comments yet. Be the first to comment!
          </div>
        ) : (
          comments.map(comment => (
            <Comment
              key={comment.id}
              comment={comment}
              onEdit={onEditComment}
              onDelete={onDeleteComment}
              onResolve={onResolveComment}
              onReply={handleReply}
              currentUser={user}
            />
          ))
        )}
      </CommentList>

      {showInput && (
        <CommentInput>
          <CommentInput
            onSubmit={handleAddComment}
            onCancel={() => setShowInput(false)}
            autoFocus
          />
        </CommentInput>
      )}
    </CommentPanel>
  );
};

export default {
  CommentBubble,
  CommentPanel,
  CommentInput
};
