#!/bin/bash
# Script to manage Docker volumes for the App Builder project
# This script helps with listing, cleaning, and verifying Docker volumes

# Define colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Define project name for volume prefixes
PROJECT_NAME="app-builder-201"

# Function to display help
show_help() {
    echo -e "\n${MAGENTA}Docker Volume Management Script${NC}"
    echo -e "${MAGENTA}==============================${NC}"
    echo -e "${CYAN}This script helps manage Docker volumes for the App Builder project.${NC}\n"
    
    echo -e "${MAGENTA}Usage:${NC}"
    echo -e "${CYAN}  ./manage-volumes.sh [action] [--force]${NC}\n"
    
    echo -e "${MAGENTA}Actions:${NC}"
    echo -e "${CYAN}  list     - List all Docker volumes related to this project${NC}"
    echo -e "${CYAN}  clean    - Remove unused Docker volumes${NC}"
    echo -e "${CYAN}  verify   - Verify volume mounts are working correctly${NC}"
    echo -e "${CYAN}  recreate - Recreate specific volumes (e.g., node_modules)${NC}"
    echo -e "${CYAN}  help     - Show this help message${NC}\n"
    
    echo -e "${MAGENTA}Options:${NC}"
    echo -e "${CYAN}  --force  - Skip confirmation prompts for destructive actions${NC}\n"
    
    echo -e "${MAGENTA}Examples:${NC}"
    echo -e "${CYAN}  ./manage-volumes.sh list${NC}"
    echo -e "${CYAN}  ./manage-volumes.sh clean${NC}"
    echo -e "${CYAN}  ./manage-volumes.sh recreate --force${NC}"
}

# Function to list volumes
list_volumes() {
    echo -e "\n${CYAN}Listing Docker volumes for $PROJECT_NAME...${NC}"
    
    volumes=$(docker volume ls --format "{{.Name}}" | grep "$PROJECT_NAME")
    
    if [ -n "$volumes" ]; then
        echo -e "\n${MAGENTA}Project volumes:${NC}"
        echo "$volumes" | while read volume; do
            echo -e "  - ${GREEN}$volume${NC}"
        done
        
        count=$(echo "$volumes" | wc -l)
        echo -e "\n${CYAN}Total project volumes: $count${NC}"
    else
        echo -e "${YELLOW}No volumes found for $PROJECT_NAME${NC}"
    fi
    
    # Show volume details
    echo -e "\n${MAGENTA}Volume details:${NC}"
    echo "$volumes" | while read volume; do
        echo -e "\n${CYAN}Inspecting $volume:${NC}"
        docker volume inspect "$volume"
    done
}

# Function to clean volumes
clean_volumes() {
    echo -e "\n${CYAN}Cleaning unused Docker volumes...${NC}"
    
    if [ "$FORCE" != "true" ]; then
        read -p "This will remove all unused volumes. Continue? (y/n) " confirmation
        if [ "$confirmation" != "y" ]; then
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return
        fi
    fi
    
    echo -e "${CYAN}Removing unused volumes...${NC}"
    docker volume prune -f
    
    echo -e "${GREEN}Unused volumes removed.${NC}"
}

# Function to verify volume mounts
verify_volumes() {
    echo -e "\n${CYAN}Verifying Docker volume mounts...${NC}"
    
    # Check if containers are running
    running_containers=$(docker-compose ps --services --filter "status=running")
    
    if [ -z "$running_containers" ]; then
        echo -e "${YELLOW}No containers are running. Starting containers...${NC}"
        docker-compose up -d
    fi
    
    # Verify frontend volume
    echo -e "\n${MAGENTA}Verifying frontend volume mount:${NC}"
    if docker-compose exec frontend ls -la /app > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend volume mount is working${NC}"
    else
        echo -e "${RED}❌ Frontend volume mount is NOT working${NC}"
    fi
    
    # Verify node_modules volume
    echo -e "\n${MAGENTA}Verifying node_modules volume mount:${NC}"
    if docker-compose exec frontend ls -la /app/node_modules > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Node modules volume mount is working${NC}"
    else
        echo -e "${RED}❌ Node modules volume mount is NOT working${NC}"
    fi
    
    # Verify backend volume
    echo -e "\n${MAGENTA}Verifying backend volume mount:${NC}"
    if docker-compose exec backend ls -la /app > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend volume mount is working${NC}"
    else
        echo -e "${RED}❌ Backend volume mount is NOT working${NC}"
    fi
    
    # Verify database volume
    echo -e "\n${MAGENTA}Verifying database volume mount:${NC}"
    if docker-compose exec db ls -la /var/lib/postgresql/data > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Database volume mount is working${NC}"
    else
        echo -e "${RED}❌ Database volume mount is NOT working${NC}"
    fi
}

# Function to recreate specific volumes
recreate_volumes() {
    echo -e "\n${CYAN}Recreating Docker volumes...${NC}"
    
    if [ "$FORCE" != "true" ]; then
        read -p "This will stop containers and recreate volumes. Continue? (y/n) " confirmation
        if [ "$confirmation" != "y" ]; then
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return
        fi
    fi
    
    # Stop containers
    echo -e "${CYAN}Stopping containers...${NC}"
    docker-compose down
    
    # Remove node_modules volume
    echo -e "${CYAN}Removing node_modules volume...${NC}"
    docker volume rm "${PROJECT_NAME}_frontend_node_modules" -f
    
    # Start containers
    echo -e "${CYAN}Starting containers...${NC}"
    docker-compose up -d
    
    echo -e "${GREEN}Volumes recreated successfully.${NC}"
}

# Parse arguments
ACTION="help"
FORCE="false"

for arg in "$@"; do
    case $arg in
        list|clean|verify|recreate|help)
            ACTION="$arg"
            ;;
        --force)
            FORCE="true"
            ;;
        *)
            echo -e "${RED}Unknown argument: $arg${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Main script execution
case $ACTION in
    list)
        list_volumes
        ;;
    clean)
        clean_volumes
        ;;
    verify)
        verify_volumes
        ;;
    recreate)
        recreate_volumes
        ;;
    help|*)
        show_help
        ;;
esac
