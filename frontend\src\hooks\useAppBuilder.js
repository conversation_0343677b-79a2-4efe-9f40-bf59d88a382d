import { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';

/**
 * Custom hook for managing App Builder state and operations
 */
export const useAppBuilder = (options = {}) => {
  const {
    projectId,
    initialComponents = [],
    autoSave = false,
    onSave,
    onLoad,
    onError
  } = options;

  const dispatch = useDispatch();
  
  // Local state
  const [components, setComponents] = useState(initialComponents);
  const [isModified, setIsModified] = useState(false);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Redux state
  const project = useSelector(state => state.projects?.current);

  // Add component
  const addComponent = useCallback(async (componentType, position = {}) => {
    try {
      const newComponent = {
        id: `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: componentType,
        props: {},
        position: position,
        children: [],
        createdAt: new Date().toISOString()
      };

      setComponents(prev => {
        const updated = [...prev, newComponent];
        setIsModified(true);
        
        // Add to history
        setHistory(prevHistory => [...prevHistory.slice(0, historyIndex + 1), updated]);
        setHistoryIndex(prev => prev + 1);
        
        return updated;
      });

      message.success(`Added ${componentType} component`);
      return newComponent;
    } catch (error) {
      console.error('Error adding component:', error);
      if (onError) onError(error);
      message.error('Failed to add component');
      return null;
    }
  }, [historyIndex, onError]);

  // Update component
  const updateComponent = useCallback((componentId, updates) => {
    try {
      setComponents(prev => {
        const updated = prev.map(comp => 
          comp.id === componentId 
            ? { ...comp, ...updates, updatedAt: new Date().toISOString() }
            : comp
        );
        
        setIsModified(true);
        
        // Add to history
        setHistory(prevHistory => [...prevHistory.slice(0, historyIndex + 1), updated]);
        setHistoryIndex(prev => prev + 1);
        
        return updated;
      });

      message.success('Component updated');
    } catch (error) {
      console.error('Error updating component:', error);
      if (onError) onError(error);
      message.error('Failed to update component');
    }
  }, [historyIndex, onError]);

  // Delete component
  const deleteComponent = useCallback((componentId) => {
    try {
      setComponents(prev => {
        const updated = prev.filter(comp => comp.id !== componentId);
        setIsModified(true);
        
        // Add to history
        setHistory(prevHistory => [...prevHistory.slice(0, historyIndex + 1), updated]);
        setHistoryIndex(prev => prev + 1);
        
        return updated;
      });

      message.success('Component deleted');
    } catch (error) {
      console.error('Error deleting component:', error);
      if (onError) onError(error);
      message.error('Failed to delete component');
    }
  }, [historyIndex, onError]);

  // Move component
  const moveComponent = useCallback((componentId, newPosition) => {
    try {
      setComponents(prev => {
        const updated = prev.map(comp => 
          comp.id === componentId 
            ? { ...comp, position: newPosition, updatedAt: new Date().toISOString() }
            : comp
        );
        
        setIsModified(true);
        
        // Add to history
        setHistory(prevHistory => [...prevHistory.slice(0, historyIndex + 1), updated]);
        setHistoryIndex(prev => prev + 1);
        
        return updated;
      });

      message.success('Component moved');
    } catch (error) {
      console.error('Error moving component:', error);
      if (onError) onError(error);
      message.error('Failed to move component');
    }
  }, [historyIndex, onError]);

  // Duplicate component
  const duplicateComponent = useCallback((componentId) => {
    try {
      const component = components.find(comp => comp.id === componentId);
      if (!component) return null;

      const duplicated = {
        ...component,
        id: `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        position: {
          ...component.position,
          x: (component.position?.x || 0) + 20,
          y: (component.position?.y || 0) + 20
        },
        createdAt: new Date().toISOString()
      };

      setComponents(prev => {
        const updated = [...prev, duplicated];
        setIsModified(true);
        
        // Add to history
        setHistory(prevHistory => [...prevHistory.slice(0, historyIndex + 1), updated]);
        setHistoryIndex(prev => prev + 1);
        
        return updated;
      });

      message.success('Component duplicated');
      return duplicated;
    } catch (error) {
      console.error('Error duplicating component:', error);
      if (onError) onError(error);
      message.error('Failed to duplicate component');
      return null;
    }
  }, [components, historyIndex, onError]);

  // Undo action
  const undoAction = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(prev => prev - 1);
      setComponents(history[historyIndex - 1]);
      setIsModified(true);
      message.info('Action undone');
    }
  }, [history, historyIndex]);

  // Redo action
  const redoAction = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(prev => prev + 1);
      setComponents(history[historyIndex + 1]);
      setIsModified(true);
      message.info('Action redone');
    }
  }, [history, historyIndex]);

  // Can undo/redo
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  // Save project
  const saveProject = useCallback(async () => {
    try {
      if (onSave) {
        await onSave({ components, projectId });
      }
      setIsModified(false);
      message.success('Project saved');
    } catch (error) {
      console.error('Error saving project:', error);
      if (onError) onError(error);
      message.error('Failed to save project');
    }
  }, [components, projectId, onSave, onError]);

  // Load project
  const loadProject = useCallback(async (id) => {
    try {
      if (onLoad) {
        const projectData = await onLoad(id);
        if (projectData?.components) {
          setComponents(projectData.components);
          setIsModified(false);
          
          // Reset history
          setHistory([projectData.components]);
          setHistoryIndex(0);
        }
      }
      message.success('Project loaded');
    } catch (error) {
      console.error('Error loading project:', error);
      if (onError) onError(error);
      message.error('Failed to load project');
    }
  }, [onLoad, onError]);

  // Auto-save effect
  useEffect(() => {
    if (autoSave && isModified && components.length > 0) {
      const timer = setTimeout(() => {
        saveProject();
      }, 5000); // Auto-save after 5 seconds of inactivity

      return () => clearTimeout(timer);
    }
  }, [autoSave, isModified, components, saveProject]);

  // Initialize history
  useEffect(() => {
    if (history.length === 0 && components.length > 0) {
      setHistory([components]);
      setHistoryIndex(0);
    }
  }, [components, history.length]);

  return {
    // State
    components,
    isModified,
    project,

    // Actions
    addComponent,
    updateComponent,
    deleteComponent,
    moveComponent,
    duplicateComponent,
    undoAction,
    redoAction,
    saveProject,
    loadProject,

    // Computed values
    canUndo,
    canRedo,
    componentCount: components.length
  };
};

export default useAppBuilder;
