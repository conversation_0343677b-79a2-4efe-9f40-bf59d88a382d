import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Typography, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Checkbox, 
  Divider, 
  Alert, 
  Tooltip,
  Popconfirm,
  Switch,
  Tabs,
  Tree
} from 'antd';
import { 
  UserOutlined, 
  TeamOutlined, 
  LockOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  PlusOutlined,
  SearchOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  KeyOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import axios from 'axios';
import { LiveRegion } from '../a11y/LiveRegion';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// Styled components
const RoleCard = styled(Card)`
  margin-bottom: 16px;
`;

const PermissionTree = styled(Tree)`
  margin-bottom: 16px;
`;

const RoleTag = styled(Tag)`
  margin: 4px;
`;

// Role colors
const ROLE_COLORS = {
  admin: 'red',
  manager: 'blue',
  editor: 'green',
  viewer: 'orange',
  user: 'default'
};

// Permission structure
const PERMISSIONS = [
  {
    key: 'dashboard',
    title: 'Dashboard',
    children: [
      { key: 'dashboard.view', title: 'View Dashboard' },
      { key: 'dashboard.edit', title: 'Edit Dashboard' }
    ]
  },
  {
    key: 'users',
    title: 'Users',
    children: [
      { key: 'users.view', title: 'View Users' },
      { key: 'users.create', title: 'Create Users' },
      { key: 'users.edit', title: 'Edit Users' },
      { key: 'users.delete', title: 'Delete Users' }
    ]
  },
  {
    key: 'roles',
    title: 'Roles',
    children: [
      { key: 'roles.view', title: 'View Roles' },
      { key: 'roles.create', title: 'Create Roles' },
      { key: 'roles.edit', title: 'Edit Roles' },
      { key: 'roles.delete', title: 'Delete Roles' }
    ]
  },
  {
    key: 'settings',
    title: 'Settings',
    children: [
      { key: 'settings.view', title: 'View Settings' },
      { key: 'settings.edit', title: 'Edit Settings' }
    ]
  }
];

/**
 * RoleBasedAccess component
 * Manages roles and permissions for users
 */
const RoleBasedAccess = () => {
  // State
  const [roles, setRoles] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [editingUser, setEditingUser] = useState(null);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('roles');
  const [announceMessage, setAnnounceMessage] = useState('');
  
  // Form
  const [roleForm] = Form.useForm();
  const [userForm] = Form.useForm();
  
  // Fetch roles and users
  useEffect(() => {
    fetchRoles();
    fetchUsers();
  }, []);
  
  // Fetch roles
  const fetchRoles = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // For demo purposes, using mock data
      const mockRoles = [
        {
          id: 1,
          name: 'Admin',
          description: 'Full access to all features',
          permissions: ['dashboard.view', 'dashboard.edit', 'users.view', 'users.create', 'users.edit', 'users.delete', 'roles.view', 'roles.create', 'roles.edit', 'roles.delete', 'settings.view', 'settings.edit'],
          isSystem: true
        },
        {
          id: 2,
          name: 'Manager',
          description: 'Can manage users and content',
          permissions: ['dashboard.view', 'users.view', 'users.create', 'users.edit', 'roles.view', 'settings.view'],
          isSystem: false
        },
        {
          id: 3,
          name: 'Editor',
          description: 'Can edit content',
          permissions: ['dashboard.view', 'users.view', 'settings.view'],
          isSystem: false
        },
        {
          id: 4,
          name: 'Viewer',
          description: 'Can view content only',
          permissions: ['dashboard.view'],
          isSystem: false
        }
      ];
      
      setRoles(mockRoles);
    } catch (err) {
      console.error('Error fetching roles:', err);
      setError('Failed to fetch roles. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch users
  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // For demo purposes, using mock data
      const mockUsers = [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          roles: [1],
          isActive: true
        },
        {
          id: 2,
          username: 'manager',
          email: '<EMAIL>',
          roles: [2],
          isActive: true
        },
        {
          id: 3,
          username: 'editor',
          email: '<EMAIL>',
          roles: [3],
          isActive: true
        },
        {
          id: 4,
          username: 'viewer',
          email: '<EMAIL>',
          roles: [4],
          isActive: false
        }
      ];
      
      setUsers(mockUsers);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to fetch users. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle role form submission
  const handleRoleSubmit = async (values) => {
    setLoading(true);
    setError(null);
    
    try {
      if (editingRole) {
        // Update existing role
        const updatedRole = {
          ...editingRole,
          name: values.name,
          description: values.description,
          permissions: selectedPermissions
        };
        
        // Update roles
        setRoles(roles.map(role => role.id === editingRole.id ? updatedRole : role));
        setAnnounceMessage(`Role ${values.name} updated successfully`);
      } else {
        // Create new role
        const newRole = {
          id: roles.length + 1,
          name: values.name,
          description: values.description,
          permissions: selectedPermissions,
          isSystem: false
        };
        
        // Add to roles
        setRoles([...roles, newRole]);
        setAnnounceMessage(`Role ${values.name} created successfully`);
      }
      
      // Close modal
      setShowRoleModal(false);
      
      // Reset form
      roleForm.resetFields();
      setSelectedPermissions([]);
      setEditingRole(null);
    } catch (err) {
      console.error('Error saving role:', err);
      setError('Failed to save role. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle user form submission
  const handleUserSubmit = async (values) => {
    setLoading(true);
    setError(null);
    
    try {
      if (editingUser) {
        // Update existing user
        const updatedUser = {
          ...editingUser,
          username: values.username,
          email: values.email,
          roles: values.roles,
          isActive: values.isActive
        };
        
        // Update users
        setUsers(users.map(user => user.id === editingUser.id ? updatedUser : user));
        setAnnounceMessage(`User ${values.username} updated successfully`);
      } else {
        // Create new user
        const newUser = {
          id: users.length + 1,
          username: values.username,
          email: values.email,
          roles: values.roles,
          isActive: values.isActive
        };
        
        // Add to users
        setUsers([...users, newUser]);
        setAnnounceMessage(`User ${values.username} created successfully`);
      }
      
      // Close modal
      setShowUserModal(false);
      
      // Reset form
      userForm.resetFields();
      setEditingUser(null);
    } catch (err) {
      console.error('Error saving user:', err);
      setError('Failed to save user. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle role deletion
  const handleDeleteRole = (role) => {
    // Check if role is used by any users
    const usersWithRole = users.filter(user => user.roles.includes(role.id));
    
    if (usersWithRole.length > 0) {
      setError(`Cannot delete role "${role.name}" because it is assigned to ${usersWithRole.length} user(s).`);
      return;
    }
    
    // Remove role
    setRoles(roles.filter(r => r.id !== role.id));
    setAnnounceMessage(`Role ${role.name} deleted successfully`);
  };
  
  // Handle user deletion
  const handleDeleteUser = (user) => {
    setUsers(users.filter(u => u.id !== user.id));
    setAnnounceMessage(`User ${user.username} deleted successfully`);
  };
  
  // Handle edit role
  const handleEditRole = (role) => {
    setEditingRole(role);
    setSelectedPermissions(role.permissions);
    
    roleForm.setFieldsValue({
      name: role.name,
      description: role.description
    });
    
    setShowRoleModal(true);
  };
  
  // Handle edit user
  const handleEditUser = (user) => {
    setEditingUser(user);
    
    userForm.setFieldsValue({
      username: user.username,
      email: user.email,
      roles: user.roles,
      isActive: user.isActive
    });
    
    setShowUserModal(true);
  };
  
  // Handle permission selection
  const handlePermissionSelect = (selectedKeys) => {
    setSelectedPermissions(selectedKeys);
  };
  
  // Get role name by ID
  const getRoleName = (roleId) => {
    const role = roles.find(r => r.id === roleId);
    return role ? role.name : '';
  };
  
  // Filter roles by search text
  const filteredRoles = roles.filter(role => 
    role.name.toLowerCase().includes(searchText.toLowerCase()) ||
    role.description.toLowerCase().includes(searchText.toLowerCase())
  );
  
  // Filter users by search text
  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchText.toLowerCase()) ||
    user.email.toLowerCase().includes(searchText.toLowerCase())
  );
  
  // Role columns
  const roleColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Text strong>{text}</Text>
          {record.isSystem && (
            <Tooltip title="System role cannot be deleted">
              <Tag color="blue">System</Tag>
            </Tooltip>
          )}
        </Space>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Permissions',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => (
        <div>
          {permissions.length > 0 ? (
            <Tooltip title={permissions.join(', ')}>
              <Tag color="green">{permissions.length} permissions</Tag>
            </Tooltip>
          ) : (
            <Tag color="red">No permissions</Tag>
          )}
        </div>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => handleEditRole(record)}
            aria-label={`Edit role ${record.name}`}
          />
          {!record.isSystem && (
            <Popconfirm
              title="Are you sure you want to delete this role?"
              onConfirm={() => handleDeleteRole(record)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                icon={<DeleteOutlined />}
                danger
                aria-label={`Delete role ${record.name}`}
              />
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];
  
  // User columns
  const userColumns = [
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: 'Roles',
      dataIndex: 'roles',
      key: 'roles',
      render: (roleIds) => (
        <div>
          {roleIds.map(roleId => {
            const roleName = getRoleName(roleId);
            const roleColor = ROLE_COLORS[roleName.toLowerCase()] || 'default';
            
            return (
              <RoleTag color={roleColor} key={roleId}>
                {roleName}
              </RoleTag>
            );
          })}
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        isActive ? (
          <Tag color="green" icon={<CheckCircleOutlined />}>Active</Tag>
        ) : (
          <Tag color="red" icon={<CloseCircleOutlined />}>Inactive</Tag>
        )
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
            aria-label={`Edit user ${record.username}`}
          />
          <Popconfirm
            title="Are you sure you want to delete this user?"
            onConfirm={() => handleDeleteUser(record)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              icon={<DeleteOutlined />}
              danger
              aria-label={`Delete user ${record.username}`}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];
  
  // Render role modal
  const renderRoleModal = () => {
    return (
      <Modal
        title={editingRole ? `Edit Role: ${editingRole.name}` : 'Create New Role'}
        open={showRoleModal}
        onCancel={() => {
          setShowRoleModal(false);
          setEditingRole(null);
          roleForm.resetFields();
          setSelectedPermissions([]);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={roleForm}
          layout="vertical"
          onFinish={handleRoleSubmit}
        >
          <Form.Item
            name="name"
            label="Role Name"
            rules={[{ required: true, message: 'Please enter role name' }]}
          >
            <Input placeholder="Enter role name" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea placeholder="Enter role description" rows={3} />
          </Form.Item>
          
          <Form.Item
            label="Permissions"
            required
          >
            <PermissionTree
              checkable
              treeData={PERMISSIONS}
              checkedKeys={selectedPermissions}
              onCheck={handlePermissionSelect}
              defaultExpandAll
            />
          </Form.Item>
          
          <Form.Item>
            <Space style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  setShowRoleModal(false);
                  setEditingRole(null);
                  roleForm.resetFields();
                  setSelectedPermissions([]);
                }}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                {editingRole ? 'Update Role' : 'Create Role'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    );
  };
  
  // Render user modal
  const renderUserModal = () => {
    return (
      <Modal
        title={editingUser ? `Edit User: ${editingUser.username}` : 'Create New User'}
        open={showUserModal}
        onCancel={() => {
          setShowUserModal(false);
          setEditingUser(null);
          userForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={userForm}
          layout="vertical"
          onFinish={handleUserSubmit}
        >
          <Form.Item
            name="username"
            label="Username"
            rules={[{ required: true, message: 'Please enter username' }]}
          >
            <Input placeholder="Enter username" />
          </Form.Item>
          
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Please enter email' },
              { type: 'email', message: 'Please enter a valid email' }
            ]}
          >
            <Input placeholder="Enter email" />
          </Form.Item>
          
          <Form.Item
            name="roles"
            label="Roles"
            rules={[{ required: true, message: 'Please select at least one role' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select roles"
              style={{ width: '100%' }}
            >
              {roles.map(role => (
                <Option key={role.id} value={role.id}>
                  {role.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="isActive"
            label="Status"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </Form.Item>
          
          <Form.Item>
            <Space style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  setShowUserModal(false);
                  setEditingUser(null);
                  userForm.resetFields();
                }}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                {editingUser ? 'Update User' : 'Create User'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    );
  };
  
  return (
    <div>
      {/* Accessibility announcement */}
      <LiveRegion id="rbac-announcer" ariaLive="polite">
        {announceMessage}
      </LiveRegion>
      
      <Title level={2}>
        <LockOutlined /> Role-Based Access Control
      </Title>
      
      <Paragraph>
        Manage roles and permissions for users in the system.
      </Paragraph>
      
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><TeamOutlined /> Roles</span>}
          key="roles"
        >
          <RoleCard
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>Roles</span>
                <Space>
                  <Input
                    placeholder="Search roles"
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={e => setSearchText(e.target.value)}
                    style={{ width: 200 }}
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setEditingRole(null);
                      setSelectedPermissions([]);
                      roleForm.resetFields();
                      setShowRoleModal(true);
                    }}
                  >
                    Add Role
                  </Button>
                </Space>
              </div>
            }
          >
            <Table
              columns={roleColumns}
              dataSource={filteredRoles}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 5 }}
            />
          </RoleCard>
        </TabPane>
        
        <TabPane
          tab={<span><UserOutlined /> Users</span>}
          key="users"
        >
          <RoleCard
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>Users</span>
                <Space>
                  <Input
                    placeholder="Search users"
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={e => setSearchText(e.target.value)}
                    style={{ width: 200 }}
                  />
                  <Button
                    type="primary"
                    icon={<UserAddOutlined />}
                    onClick={() => {
                      setEditingUser(null);
                      userForm.resetFields();
                      setShowUserModal(true);
                    }}
                  >
                    Add User
                  </Button>
                </Space>
              </div>
            }
          >
            <Table
              columns={userColumns}
              dataSource={filteredUsers}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 5 }}
            />
          </RoleCard>
        </TabPane>
      </Tabs>
      
      {renderRoleModal()}
      {renderUserModal()}
    </div>
  );
};

export default RoleBasedAccess;
