import socket
import sys

def check_port(host, port, timeout=2):
    """
    Check if a port is open on a host.
    
    Args:
        host: The host to check
        port: The port to check
        timeout: The timeout in seconds
        
    Returns:
        bool: True if the port is open, False otherwise
    """
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"Port {port} is OPEN on {host}")
            return True
        else:
            print(f"Port {port} is CLOSED on {host} (Error code: {result})")
            return False
    except socket.gaierror:
        print(f"Hostname {host} could not be resolved")
        return False
    except socket.error as e:
        print(f"Socket error: {e}")
        return False

def main():
    """
    Main function.
    """
    if len(sys.argv) < 3:
        print("Usage: python check_port.py <host> <port>")
        sys.exit(1)
        
    host = sys.argv[1]
    port = int(sys.argv[2])
    
    check_port(host, port)

if __name__ == "__main__":
    main()
