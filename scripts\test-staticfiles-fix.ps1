# Test Django Staticfiles Fix
Write-Host "Testing Django Staticfiles Configuration Fix" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

# Test 1: Check frontend build exists
Write-Host "`n1. Checking frontend build directory..." -ForegroundColor Yellow
if (Test-Path "frontend/build/static") {
    $fileCount = (Get-ChildItem -Path "frontend/build/static" -Recurse -File).Count
    Write-Host "✅ Frontend build exists with $fileCount static files" -ForegroundColor Green
}
else {
    Write-Host "❌ Frontend build directory missing" -ForegroundColor Red
    Write-Host "💡 Run: cd frontend && npm run build" -ForegroundColor Blue
    exit 1
}

# Test 2: Check Docker Compose configuration
Write-Host "`n2. Checking Docker Compose configuration..." -ForegroundColor Yellow
$dockerComposeContent = Get-Content "docker-compose.yml" -Raw

if ($dockerComposeContent -match "DJANGO_SETTINGS_MODULE=app_builder_201\.settings") {
    Write-Host "✅ Correct Django settings module configured" -ForegroundColor Green
}
else {
    Write-Host "❌ Django settings module not correctly configured" -ForegroundColor Red
}

if ($dockerComposeContent -match "./frontend/build:/usr/src/app/frontend/build:ro") {
    Write-Host "✅ Frontend build volume mount configured" -ForegroundColor Green
}
else {
    Write-Host "❌ Frontend build volume mount missing" -ForegroundColor Red
}

# Test 3: Check Django settings file
Write-Host "`n3. Checking Django settings configuration..." -ForegroundColor Yellow
$settingsPath = "backend/app_builder_201/settings.py"
if (Test-Path $settingsPath) {
    $settingsContent = Get-Content $settingsPath -Raw
    
    if ($settingsContent -match "STATICFILES_DIRS.*frontend.*build.*static") {
        Write-Host "✅ Django STATICFILES_DIRS correctly configured" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Django STATICFILES_DIRS not correctly configured" -ForegroundColor Red
    }
    
    if ($settingsContent -match "django\.contrib\.staticfiles") {
        Write-Host "✅ Django staticfiles app enabled" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Django staticfiles app not enabled" -ForegroundColor Red
    }
}
else {
    Write-Host "❌ Django settings file not found" -ForegroundColor Red
}

# Test 4: Check backend startup script
Write-Host "`n4. Checking backend startup script..." -ForegroundColor Yellow
$startScriptPath = "backend/start.sh"
if (Test-Path $startScriptPath) {
    $startScriptContent = Get-Content $startScriptPath -Raw
    
    if ($startScriptContent -match "collectstatic") {
        Write-Host "✅ Backend startup includes collectstatic command" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ Backend startup doesn't include collectstatic (optional)" -ForegroundColor Yellow
    }
    
    if ($startScriptContent -match "frontend/build/static") {
        Write-Host "✅ Backend startup checks for frontend static files" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ Backend startup doesn't check frontend static files" -ForegroundColor Yellow
    }
}
else {
    Write-Host "❌ Backend startup script not found" -ForegroundColor Red
}

# Test 5: Simulate Docker volume mount test
Write-Host "`n5. Simulating volume mount test..." -ForegroundColor Yellow
$frontendBuildPath = "frontend/build"
$backendExpectedPath = "backend/../frontend/build"  # Simulated container path

if (Test-Path $frontendBuildPath) {
    Write-Host "✅ Source path exists: $frontendBuildPath" -ForegroundColor Green
    
    # Check if the relative path would work in container
    $relativePath = Resolve-Path $frontendBuildPath -Relative
    Write-Host "📁 Relative path: $relativePath" -ForegroundColor Blue
    
    if (Test-Path "$frontendBuildPath/static") {
        Write-Host "✅ Static subdirectory exists" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Static subdirectory missing" -ForegroundColor Red
    }
}
else {
    Write-Host "ERROR: Source path doesn't exist" -ForegroundColor Red
}

# Summary
Write-Host "`n📊 Test Summary:" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

$allTestsPassed = $true

# Count successful tests
$tests = @(
    (Test-Path "frontend/build/static"),
    ($dockerComposeContent -match "DJANGO_SETTINGS_MODULE=app_builder_201\.settings"),
    ($dockerComposeContent -match "./frontend/build:/usr/src/app/frontend/build:ro"),
    (Test-Path $settingsPath),
    (Test-Path $startScriptPath)
)

$passedTests = ($tests | Where-Object { $_ }).Count
$totalTests = $tests.Count

Write-Host "Passed: $passedTests/$totalTests tests" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 All tests passed! Django staticfiles fix is ready." -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "1. Start containers: powershell scripts/start-containers.ps1" -ForegroundColor Blue
    Write-Host "2. Check logs: docker-compose logs backend | grep -i static" -ForegroundColor Blue
    Write-Host "3. Verify no warnings in Django startup" -ForegroundColor Blue
}
else {
    Write-Host "`n⚠️ Some tests failed. Please review the issues above." -ForegroundColor Yellow
    $allTestsPassed = $false
}

if (-not $allTestsPassed) {
    exit 1
}
