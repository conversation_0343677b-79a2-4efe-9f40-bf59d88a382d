import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Input, Select, Switch, Typography, Space, Divider, Alert, List, Badge, Tooltip } from 'antd';
import {
  SendOutlined,
  ReloadOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  SyncOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import useWebSocket from '../../hooks/useWebSocket';
import { getWebSocketUrl } from '../../config/env';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const DemoContainer = styled.div`
  padding: 20px;
`;

const DemoCard = styled(Card)`
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;

  .label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const InfoBox = styled(Alert)`
  margin-bottom: 16px;
`;

const MessageList = styled(List)`
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 16px;

  .ant-list-item {
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-color);
  }

  .message-direction {
    font-weight: bold;
    margin-right: 8px;
  }

  .message-time {
    color: var(--text-secondary);
    font-size: 12px;
  }

  .message-content {
    margin-top: 4px;
    word-break: break-word;
  }
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: ${props => {
    switch (props.status) {
      case 'connected': return 'rgba(82, 196, 26, 0.1)';
      case 'connecting': return 'rgba(250, 173, 20, 0.1)';
      case 'disconnected': return 'rgba(245, 34, 45, 0.1)';
      default: return 'rgba(0, 0, 0, 0.05)';
    }
  }};

  .status-icon {
    margin-right: 8px;
    font-size: 16px;
    color: ${props => {
    switch (props.status) {
      case 'connected': return '#52c41a';
      case 'connecting': return '#faad14';
      case 'disconnected': return '#f5222d';
      default: return '#8c8c8c';
    }
  }};
  }
`;

/**
 * WebSocketDemo component
 * Demonstrates the use of WebSockets with the useWebSocket hook
 */
const WebSocketDemo = () => {
  // WebSocket URL
  const [url, setUrl] = useState(getWebSocketUrl('app_builder'));

  // WebSocket options
  const [autoReconnect, setAutoReconnect] = useState(true);
  const [debug, setDebug] = useState(true);

  // Message state
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const messagesEndRef = useRef(null);

  // Use our WebSocket hook
  const {
    connectionState,
    lastMessage,
    lastError,
    isConnecting,
    isOpen,
    isClosed,
    hasError,
    connect,
    disconnect,
    send,
    clearError
  } = useWebSocket({
    url,
    autoConnect: false,
    reconnect: autoReconnect,
    debug,
    onMessage: (data) => {
      // Add received message to the list
      const newMessage = {
        id: Date.now(),
        direction: 'received',
        content: typeof data === 'object' ? JSON.stringify(data) : data,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, newMessage]);
    }
  });

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle connecting to WebSocket
  const handleConnect = () => {
    connect();
  };

  // Handle disconnecting from WebSocket
  const handleDisconnect = () => {
    disconnect(1000, 'User initiated disconnect');
  };

  // Handle sending a message
  const handleSend = () => {
    if (!message.trim()) return;

    // Send the message
    const success = send(message);

    if (success) {
      // Add sent message to the list
      const newMessage = {
        id: Date.now(),
        direction: 'sent',
        content: message,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, newMessage]);
      setMessage('');
    }
  };

  // Handle clearing messages
  const handleClearMessages = () => {
    setMessages([]);
  };

  // Get connection status
  const getConnectionStatus = () => {
    if (isOpen) return 'connected';
    if (isConnecting) return 'connecting';
    return 'disconnected';
  };

  // Get connection status icon
  const getStatusIcon = () => {
    if (isOpen) return <CheckCircleOutlined className="status-icon" />;
    if (isConnecting) return <SyncOutlined spin className="status-icon" />;
    return <CloseCircleOutlined className="status-icon" />;
  };

  // Get connection status text
  const getStatusText = () => {
    if (isOpen) return 'Connected';
    if (isConnecting) return 'Connecting...';
    if (hasError) return `Disconnected (${lastError?.message || 'Error'})`;
    return 'Disconnected';
  };

  return (
    <DemoContainer>
      <Title level={3}>WebSocket Demo</Title>
      <Paragraph>
        This demo shows how to use WebSockets with the useWebSocket hook.
      </Paragraph>

      <DemoCard title="WebSocket Connection">
        <InfoBox
          type="info"
          message="WebSocket Configuration"
          description="Configure and manage your WebSocket connection."
          icon={<InfoCircleOutlined />}
        />

        <ConnectionStatus status={getConnectionStatus()}>
          {getStatusIcon()}
          <Text strong>{getStatusText()}</Text>
        </ConnectionStatus>

        {hasError && (
          <Alert
            type="error"
            message="Connection Error"
            description={lastError?.message || 'An error occurred with the WebSocket connection'}
            style={{ marginBottom: '16px' }}
            closable
            onClose={clearError}
          />
        )}

        <FormGroup>
          <div className="label">WebSocket URL:</div>
          <Input
            value={url}
            onChange={e => setUrl(e.target.value)}
            placeholder="Enter WebSocket URL"
            disabled={isOpen || isConnecting}
          />
        </FormGroup>

        <FormGroup>
          <div className="label">Auto Reconnect:</div>
          <Switch
            checked={autoReconnect}
            onChange={setAutoReconnect}
            disabled={isOpen || isConnecting}
          />
        </FormGroup>

        <FormGroup>
          <div className="label">Debug Mode:</div>
          <Switch
            checked={debug}
            onChange={setDebug}
          />
        </FormGroup>

        <ButtonGroup>
          {!isOpen ? (
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleConnect}
              loading={isConnecting}
              disabled={isConnecting}
            >
              Connect
            </Button>
          ) : (
            <Button
              danger
              icon={<CloseCircleOutlined />}
              onClick={handleDisconnect}
            >
              Disconnect
            </Button>
          )}
        </ButtonGroup>
      </DemoCard>

      <DemoCard title="Messages">
        <InfoBox
          type="info"
          message="Send and Receive Messages"
          description="Send messages to the WebSocket server and view received messages."
          icon={<InfoCircleOutlined />}
        />

        <MessageList
          dataSource={messages}
          locale={{ emptyText: 'No messages yet' }}
          renderItem={item => (
            <List.Item>
              <div style={{ width: '100%' }}>
                <div>
                  <Badge
                    color={item.direction === 'sent' ? 'blue' : 'green'}
                    text={<span className="message-direction">{item.direction === 'sent' ? 'Sent' : 'Received'}</span>}
                  />
                  <span className="message-time">{new Date(item.timestamp).toLocaleTimeString()}</span>
                </div>
                <div className="message-content">
                  <Text code>{item.content}</Text>
                </div>
              </div>
            </List.Item>
          )}
        />
        <div ref={messagesEndRef} />

        <FormGroup>
          <div className="label">Message:</div>
          <TextArea
            value={message}
            onChange={e => setMessage(e.target.value)}
            placeholder="Enter message to send"
            rows={4}
            disabled={!isOpen}
          />
        </FormGroup>

        <ButtonGroup>
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            disabled={!isOpen || !message.trim()}
          >
            Send
          </Button>
          <Button
            icon={<ClearOutlined />}
            onClick={handleClearMessages}
            disabled={messages.length === 0}
          >
            Clear Messages
          </Button>
        </ButtonGroup>
      </DemoCard>
    </DemoContainer>
  );
};

export default WebSocketDemo;
