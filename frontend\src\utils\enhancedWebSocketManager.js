/**
 * Enhanced WebSocket Manager
 * 
 * A robust WebSocket manager with advanced features:
 * - Automatic reconnection with exponential backoff
 * - Heartbeat mechanism to detect connection issues
 * - Message batching for performance optimization
 * - Message queuing during disconnections
 * - Rate limiting to prevent flooding
 * - Compression for large messages
 * - Event-based API
 */

class EnhancedWebSocketManager {
  constructor(options = {}) {
    // Configuration options with defaults
    this.config = {
      url: options.url || 'ws://localhost:8000/ws/',
      protocols: options.protocols || [],
      autoReconnect: options.autoReconnect !== false,
      debug: options.debug || false,
      reconnectInterval: options.reconnectInterval || 1000,
      maxReconnectAttempts: options.maxReconnectAttempts || 10,
      heartbeatInterval: options.heartbeatInterval || 30000,
      connectionTimeout: options.connectionTimeout || 10000,
      rateLimiting: {
        enabled: options.rateLimiting?.enabled || false,
        maxMessagesPerSecond: options.rateLimiting?.maxMessagesPerSecond || 10,
        burstSize: options.rateLimiting?.burstSize || 20
      },
      compression: {
        enabled: options.compression?.enabled || false,
        threshold: options.compression?.threshold || 1024
      },
      batchingEnabled: options.batchingEnabled || false,
      batchInterval: options.batchInterval || 100,
      maxBatchSize: options.maxBatchSize || 10
    };
    
    // WebSocket instance
    this.socket = null;
    
    // Connection state
    this.connected = false;
    this.connecting = false;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.heartbeatTimer = null;
    this.connectionTimeoutTimer = null;
    
    // Message handling
    this.messageQueue = [];
    this.messageBuffer = [];
    this.batchTimer = null;
    this.messagesSentThisSecond = 0;
    this.rateLimitTimer = null;
    this.tokenBucket = this.config.rateLimiting.burstSize;
    
    // Event listeners
    this.eventListeners = {
      open: [],
      close: [],
      error: [],
      message: [],
      reconnect: [],
      reconnectFailed: []
    };
    
    // Bind methods to maintain 'this' context
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.send = this.send.bind(this);
    this.handleOpen = this.handleOpen.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.reconnect = this.reconnect.bind(this);
    this.sendHeartbeat = this.sendHeartbeat.bind(this);
    this.flushMessageBuffer = this.flushMessageBuffer.bind(this);
    this.replenishTokens = this.replenishTokens.bind(this);
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise} Resolves when connected, rejects on error or timeout
   */
  connect() {
    if (this.connected || this.connecting) {
      return Promise.resolve();
    }
    
    this.connecting = true;
    
    return new Promise((resolve, reject) => {
      try {
        this.log('Connecting to WebSocket server:', this.config.url);
        
        // Create new WebSocket instance
        this.socket = new WebSocket(this.config.url, this.config.protocols);
        
        // Set up event handlers
        this.socket.onopen = (event) => {
          clearTimeout(this.connectionTimeoutTimer);
          this.handleOpen(event);
          resolve();
        };
        
        this.socket.onclose = this.handleClose;
        this.socket.onerror = (error) => {
          this.handleError(error);
          if (!this.connected) {
            reject(error);
          }
        };
        
        this.socket.onmessage = this.handleMessage;
        
        // Set connection timeout
        this.connectionTimeoutTimer = setTimeout(() => {
          if (!this.connected) {
            this.log('Connection timeout');
            this.socket.close();
            reject(new Error('Connection timeout'));
          }
        }, this.config.connectionTimeout);
        
        // Start token replenishment for rate limiting
        if (this.config.rateLimiting.enabled) {
          this.rateLimitTimer = setInterval(this.replenishTokens, 1000);
        }
      } catch (error) {
        this.connecting = false;
        reject(error);
      }
    });
  }
  
  /**
   * Disconnect from the WebSocket server
   * @param {number} code - Close code
   * @param {string} reason - Close reason
   */
  disconnect(code = 1000, reason = 'Normal closure') {
    if (!this.socket) return;
    
    this.log('Disconnecting from WebSocket server');
    
    // Clear timers
    clearTimeout(this.reconnectTimer);
    clearTimeout(this.heartbeatTimer);
    clearTimeout(this.connectionTimeoutTimer);
    clearTimeout(this.batchTimer);
    clearInterval(this.rateLimitTimer);
    
    // Reset state
    this.reconnectAttempts = 0;
    this.connecting = false;
    
    // Close socket
    try {
      this.socket.close(code, reason);
    } catch (error) {
      this.log('Error closing WebSocket:', error);
    }
    
    this.socket = null;
  }
  
  /**
   * Send a message through the WebSocket
   * @param {string|object} message - Message to send
   * @param {Object} options - Send options
   * @returns {boolean} Success status
   */
  send(message, options = {}) {
    const { priority = false, compress = this.config.compression.enabled } = options;
    
    // Convert objects to JSON strings
    const data = typeof message === 'object' ? JSON.stringify(message) : message;
    
    // If not connected, queue the message
    if (!this.connected) {
      this.log('Not connected, queueing message');
      this.messageQueue.push({ data, options });
      return false;
    }
    
    // Apply rate limiting if enabled
    if (this.config.rateLimiting.enabled && !priority) {
      if (this.tokenBucket <= 0) {
        this.log('Rate limit exceeded, queueing message');
        this.messageQueue.push({ data, options });
        return false;
      }
      this.tokenBucket--;
    }
    
    // Apply batching if enabled
    if (this.config.batchingEnabled && !priority) {
      this.messageBuffer.push(data);
      
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(this.flushMessageBuffer, this.config.batchInterval);
      }
      
      if (this.messageBuffer.length >= this.config.maxBatchSize) {
        this.flushMessageBuffer();
      }
      
      return true;
    }
    
    // Apply compression if enabled and message is large enough
    if (compress && data.length > this.config.compression.threshold) {
      // In a real implementation, we would compress the data here
      // For this example, we'll just log it
      this.log('Would compress message of size:', data.length);
    }
    
    // Send the message
    try {
      this.socket.send(data);
      return true;
    } catch (error) {
      this.log('Error sending message:', error);
      this.messageQueue.push({ data, options });
      return false;
    }
  }
  
  /**
   * Add an event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  addEventListener(event, callback) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].push(callback);
    }
  }
  
  /**
   * Remove an event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  removeEventListener(event, callback) {
    if (this.eventListeners[event]) {
      this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    }
  }
  
  /**
   * Handle WebSocket open event
   * @param {Event} event - WebSocket event
   */
  handleOpen(event) {
    this.connected = true;
    this.connecting = false;
    this.reconnectAttempts = 0;
    
    this.log('WebSocket connected');
    
    // Start heartbeat
    this.heartbeatTimer = setTimeout(this.sendHeartbeat, this.config.heartbeatInterval);
    
    // Process queued messages
    this.processQueue();
    
    // Trigger event listeners
    this.eventListeners.open.forEach(callback => callback(event));
  }
  
  /**
   * Handle WebSocket close event
   * @param {CloseEvent} event - WebSocket close event
   */
  handleClose(event) {
    this.connected = false;
    this.connecting = false;
    
    this.log('WebSocket closed:', event.code, event.reason);
    
    // Clear timers
    clearTimeout(this.heartbeatTimer);
    clearTimeout(this.batchTimer);
    
    // Trigger event listeners
    this.eventListeners.close.forEach(callback => callback(event));
    
    // Attempt to reconnect if enabled
    if (this.config.autoReconnect) {
      this.reconnect();
    }
  }
  
  /**
   * Handle WebSocket error event
   * @param {Event} event - WebSocket error event
   */
  handleError(event) {
    this.log('WebSocket error:', event);
    
    // Trigger event listeners
    this.eventListeners.error.forEach(callback => callback(event));
  }
  
  /**
   * Handle WebSocket message event
   * @param {MessageEvent} event - WebSocket message event
   */
  handleMessage(event) {
    let data = event.data;
    
    // Parse JSON if needed
    if (typeof data === 'string' && data.startsWith('{')) {
      try {
        data = JSON.parse(data);
      } catch (error) {
        this.log('Error parsing message:', error);
      }
    }
    
    // Handle heartbeat response
    if (data === 'pong' || (data && data.type === 'pong')) {
      this.log('Received heartbeat response');
      return;
    }
    
    // Trigger event listeners
    this.eventListeners.message.forEach(callback => callback(data, event));
  }
  
  /**
   * Attempt to reconnect to the WebSocket server
   */
  reconnect() {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.log('Max reconnect attempts reached');
      this.eventListeners.reconnectFailed.forEach(callback => callback());
      return;
    }
    
    const delay = Math.min(
      30000, // Max 30 seconds
      this.config.reconnectInterval * Math.pow(1.5, this.reconnectAttempts)
    );
    
    this.reconnectAttempts++;
    
    this.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
    
    this.reconnectTimer = setTimeout(() => {
      this.eventListeners.reconnect.forEach(callback => callback(this.reconnectAttempts));
      this.connect().catch(() => {
        // Connection failed, will retry
      });
    }, delay);
  }
  
  /**
   * Send a heartbeat message to keep the connection alive
   */
  sendHeartbeat() {
    if (!this.connected) return;
    
    this.log('Sending heartbeat');
    this.send({ type: 'ping' }, { priority: true });
    
    // Set up next heartbeat
    this.heartbeatTimer = setTimeout(this.sendHeartbeat, this.config.heartbeatInterval);
  }
  
  /**
   * Process queued messages
   */
  processQueue() {
    if (!this.connected || this.messageQueue.length === 0) return;
    
    this.log(`Processing ${this.messageQueue.length} queued messages`);
    
    // Process up to 10 messages at a time to avoid blocking
    const batch = this.messageQueue.splice(0, 10);
    
    batch.forEach(({ data, options }) => {
      this.send(data, options);
    });
    
    // If there are more messages, process them in the next tick
    if (this.messageQueue.length > 0) {
      setTimeout(() => this.processQueue(), 0);
    }
  }
  
  /**
   * Flush the message buffer (for batching)
   */
  flushMessageBuffer() {
    clearTimeout(this.batchTimer);
    this.batchTimer = null;
    
    if (this.messageBuffer.length === 0 || !this.connected) return;
    
    this.log(`Flushing ${this.messageBuffer.length} messages`);
    
    // In a real implementation, we would batch these messages
    // For this example, we'll just send them individually
    this.messageBuffer.forEach(message => {
      try {
        this.socket.send(message);
      } catch (error) {
        this.log('Error sending batched message:', error);
        this.messageQueue.push({ data: message, options: {} });
      }
    });
    
    this.messageBuffer = [];
  }
  
  /**
   * Replenish rate limiting tokens
   */
  replenishTokens() {
    this.tokenBucket = Math.min(
      this.config.rateLimiting.burstSize,
      this.tokenBucket + this.config.rateLimiting.maxMessagesPerSecond
    );
  }
  
  /**
   * Log a message if debug is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.config.debug) {
      console.log('[WebSocketManager]', ...args);
    }
  }
  
  /**
   * Get the current connection state
   * @returns {Object} Connection state
   */
  getState() {
    return {
      connected: this.connected,
      connecting: this.connecting,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      bufferedMessages: this.messageBuffer.length
    };
  }
}

export default EnhancedWebSocketManager;
