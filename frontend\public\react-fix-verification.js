/**
 * React Fix Verification Script
 * 
 * This script addresses all the critical React detection failures and provides
 * comprehensive verification and troubleshooting capabilities.
 */

(async function reactFixVerification() {
  console.log('🔧 Starting React Fix Verification...');
  console.log('='.repeat(60));
  
  const results = {
    reactGlobal: false,
    reactDOMGlobal: false,
    bundleLoading: false,
    domRendering: false,
    reactAttributes: false,
    cacheCleared: false,
    serviceWorkerFixed: false
  };
  
  let testsPassed = 0;
  let testsFailed = 0;
  
  function logResult(test, passed, message, details = '') {
    const icon = passed ? '✅' : '❌';
    const status = passed ? 'PASS' : 'FAIL';
    console.log(`${icon} ${test}: ${status} - ${message}`);
    if (details) console.log(`   Details: ${details}`);
    
    if (passed) {
      testsPassed++;
    } else {
      testsFailed++;
    }
  }
  
  // Step 1: Clear all caches first
  console.log('\n🧹 Step 1: Clearing all caches...');
  try {
    const cacheNames = await caches.keys();
    console.log(`Found ${cacheNames.length} cache(s): ${cacheNames.join(', ')}`);
    
    for (const cacheName of cacheNames) {
      await caches.delete(cacheName);
      console.log(`Deleted cache: ${cacheName}`);
    }
    
    localStorage.clear();
    sessionStorage.clear();
    
    results.cacheCleared = true;
    logResult('Cache Clearing', true, 'All caches cleared successfully');
  } catch (error) {
    logResult('Cache Clearing', false, `Error clearing caches: ${error.message}`);
  }
  
  // Step 2: Check React Global Availability
  console.log('\n🌐 Step 2: Testing React Global Availability...');
  if (typeof window.React !== 'undefined') {
    results.reactGlobal = true;
    const reactVersion = window.React.version || 'Unknown';
    logResult('React Global Availability', true, `React available globally - Version: ${reactVersion}`);
    console.log(`   React object keys: ${Object.keys(window.React).slice(0, 10).join(', ')}...`);
  } else {
    logResult('React Global Availability', false, 'React is not available in global scope');
    console.log('   This indicates the React global exposure code is not working');
  }
  
  // Step 3: Check ReactDOM Global Availability
  console.log('\n🏗️ Step 3: Testing ReactDOM Global Availability...');
  if (typeof window.ReactDOM !== 'undefined') {
    results.reactDOMGlobal = true;
    logResult('ReactDOM Global Availability', true, 'ReactDOM available globally');
    console.log(`   ReactDOM keys: ${Object.keys(window.ReactDOM).join(', ')}`);
  } else {
    logResult('ReactDOM Global Availability', false, 'ReactDOM is not available in global scope');
  }
  
  // Step 4: Check Bundle Loading
  console.log('\n📦 Step 4: Testing Bundle Loading...');
  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const reactScripts = scripts.filter(script => 
    script.src.includes('main') || 
    script.src.includes('bundle') || 
    script.src.includes('chunk')
  );
  
  if (reactScripts.length > 0) {
    results.bundleLoading = true;
    logResult('Bundle Loading', true, `Found ${reactScripts.length} bundle scripts`);
    reactScripts.forEach((script, index) => {
      console.log(`   ${index + 1}. ${script.src}`);
      console.log(`      Loaded: ${script.readyState === 'complete' || !script.readyState}`);
    });
  } else {
    logResult('Bundle Loading', false, 'No React bundle scripts detected');
    console.log('   This indicates webpack bundles are not loading correctly');
  }
  
  // Step 5: Check DOM Rendering
  console.log('\n🏗️ Step 5: Testing DOM Rendering...');
  const rootElement = document.getElementById('root');
  if (rootElement) {
    results.domRendering = true;
    const hasContent = rootElement.children.length > 0;
    logResult('React Root Element', true, `Root element found with ${rootElement.children.length} children`);
    console.log(`   Root element innerHTML length: ${rootElement.innerHTML.length}`);
    
    if (hasContent) {
      logResult('React Content Rendering', true, 'Content is rendered in root element');
    } else {
      logResult('React Content Rendering', false, 'Root element is empty - React app may not be mounted');
    }
  } else {
    logResult('React Root Element', false, 'Root element with id="root" not found');
    console.log('   This indicates the HTML template is missing the root element');
  }
  
  // Step 6: Check React DOM Attributes
  console.log('\n🔍 Step 6: Testing React DOM Attributes...');
  const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet], [data-react-component]');
  if (reactElements.length > 0) {
    results.reactAttributes = true;
    logResult('React DOM Attributes', true, `Found ${reactElements.length} React DOM attributes`);
  } else {
    logResult('React DOM Attributes', false, 'No React DOM attributes found');
    console.log('   This might be normal in React 18+ or indicates React is not rendering');
  }
  
  // Step 7: Check Service Worker Status
  console.log('\n🔧 Step 7: Testing Service Worker Status...');
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration) {
        results.serviceWorkerFixed = true;
        logResult('Service Worker Status', true, 'Service Worker registered and should not interfere with React');
        console.log(`   Scope: ${registration.scope}`);
        console.log(`   Active: ${registration.active ? 'Yes' : 'No'}`);
      } else {
        logResult('Service Worker Status', true, 'No Service Worker registered');
      }
    } catch (error) {
      logResult('Service Worker Status', false, `Service Worker error: ${error.message}`);
    }
  } else {
    logResult('Service Worker Status', true, 'Service Worker not supported');
  }
  
  // Step 8: Attempt React Component Creation
  console.log('\n🧪 Step 8: Testing React Component Creation...');
  if (results.reactGlobal && results.reactDOMGlobal) {
    try {
      const TestComponent = window.React.createElement('div', {
        style: { 
          position: 'fixed',
          top: '20px',
          right: '20px',
          padding: '15px',
          background: '#4caf50',
          color: 'white',
          borderRadius: '8px',
          zIndex: '9999',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
        }
      }, 
        window.React.createElement('strong', null, '🎉 React is Working!'),
        window.React.createElement('br'),
        window.React.createElement('span', null, `Version: ${window.React.version || 'Unknown'}`)
      );
      
      let testContainer = document.getElementById('react-test-success');
      if (!testContainer) {
        testContainer = document.createElement('div');
        testContainer.id = 'react-test-success';
        document.body.appendChild(testContainer);
      }
      
      const root = window.ReactDOM.createRoot(testContainer);
      root.render(TestComponent);
      
      logResult('React Component Creation', true, 'React component created and rendered successfully!');
      console.log('   Check the top-right corner for the success indicator');
      
      // Auto-remove after 10 seconds
      setTimeout(() => {
        if (testContainer && testContainer.parentNode) {
          testContainer.parentNode.removeChild(testContainer);
        }
      }, 10000);
      
    } catch (error) {
      logResult('React Component Creation', false, `Error creating React component: ${error.message}`);
    }
  } else {
    logResult('React Component Creation', false, 'Cannot test - React or ReactDOM not available');
  }
  
  // Final Results
  console.log('\n📊 REACT FIX VERIFICATION RESULTS');
  console.log('='.repeat(60));
  console.log(`React Global Available: ${results.reactGlobal ? '✅ YES' : '❌ NO'}`);
  console.log(`ReactDOM Global Available: ${results.reactDOMGlobal ? '✅ YES' : '❌ NO'}`);
  console.log(`Bundle Scripts Loaded: ${results.bundleLoading ? '✅ YES' : '❌ NO'}`);
  console.log(`Root Element Found: ${results.domRendering ? '✅ YES' : '❌ NO'}`);
  console.log(`React DOM Attributes: ${results.reactAttributes ? '✅ YES' : '❌ NO'}`);
  console.log(`Cache Cleared: ${results.cacheCleared ? '✅ YES' : '❌ NO'}`);
  console.log(`Service Worker OK: ${results.serviceWorkerFixed ? '✅ YES' : '❌ NO'}`);
  console.log(`Tests Passed: ${testsPassed}`);
  console.log(`Tests Failed: ${testsFailed}`);
  
  const criticalIssuesFixed = results.reactGlobal && results.reactDOMGlobal && results.bundleLoading && results.domRendering;
  
  if (criticalIssuesFixed) {
    console.log('\n🎉 SUCCESS! All critical React issues have been resolved!');
    console.log('✅ React framework is properly loaded and accessible');
    console.log('✅ ReactDOM is available for component rendering');
    console.log('✅ Bundle scripts are loading correctly');
    console.log('✅ DOM root element is present and ready');
    console.log('✅ Your App Builder should now work correctly!');
    
    // Show success notification
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('React Fix Complete', {
        body: 'All React issues have been resolved! 🎉',
        icon: '/logo192.png'
      });
    }
  } else {
    console.log('\n⚠️ Critical issues still detected:');
    if (!results.reactGlobal) {
      console.log('❌ React not available globally');
      console.log('   Solution: Check if index.js changes were applied and container restarted');
    }
    if (!results.reactDOMGlobal) {
      console.log('❌ ReactDOM not available globally');
      console.log('   Solution: Verify ReactDOM global exposure in index.js');
    }
    if (!results.bundleLoading) {
      console.log('❌ Bundle scripts not loading');
      console.log('   Solution: Check webpack build and network tab for 404 errors');
    }
    if (!results.domRendering) {
      console.log('❌ Root element issues');
      console.log('   Solution: Verify HTML template has <div id="root"></div>');
    }
    
    console.log('\n🔧 Recommended actions:');
    console.log('1. Clear browser cache completely (Ctrl+Shift+Delete)');
    console.log('2. Restart the frontend Docker container');
    console.log('3. Check browser console for JavaScript errors');
    console.log('4. Verify webpack build completed successfully');
    console.log('5. Try opening in an incognito/private browser window');
  }
  
  return results;
})();

// Make the function available globally for repeated testing
window.reactFixVerification = reactFixVerification;
