import React, { useState } from 'react';
import { Collapse, Typography, Space, Badge, Button, Tooltip } from 'antd';
import {
  DownOutlined,
  RightOutlined,
  SettingOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  ColumnWidthOutlined,
  LayoutOutlined,
  HighlightOutlined,
  UndoOutlined
} from '@ant-design/icons';
import { styled } from '../../../design-system';
import PropertyRenderer from './PropertyRenderer';

const { Panel } = Collapse;
const { Text } = Typography;

const GroupContainer = styled.div`
  margin-bottom: 16px;
`;

const GroupHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  
  &:hover {
    background: #f5f5f5;
  }
`;

const GroupTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const GroupActions = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const PropertyItem = styled.div`
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
`;

const PropertyLabel = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const PropertyName = styled(Text)`
  font-weight: 500;
  font-size: 13px;
`;

const PropertyDescription = styled(Text)`
  font-size: 12px;
  color: #8c8c8c;
  display: block;
  margin-top: 2px;
`;

/**
 * Property group component for organizing and displaying properties
 */
const PropertyGroup = ({
  groupName,
  properties = {},
  values = {},
  onChange,
  componentType,
  collapsible = true,
  defaultExpanded = true,
  showResetAll = true,
  showPropertyCount = true,
  ...props
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // Get group icon based on group name
  const getGroupIcon = (name) => {
    const iconMap = {
      basic: <SettingOutlined />,
      dimensions: <ColumnWidthOutlined />,
      spacing: <LayoutOutlined />,
      typography: <FontSizeOutlined />,
      colors: <BgColorsOutlined />,
      border: <BorderOutlined />,
      shadow: <HighlightOutlined />,
      layout: <LayoutOutlined />
    };

    return iconMap[name.toLowerCase()] || <SettingOutlined />;
  };

  // Format group name for display
  const formatGroupName = (name) => {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
  };

  // Handle property value change
  const handlePropertyChange = (newValue, propertyName, schema) => {
    if (onChange) {
      onChange(propertyName, newValue, schema);
    }
  };

  // Handle reset all properties in group
  const handleResetAll = (e) => {
    e.stopPropagation();

    Object.keys(properties).forEach(propertyName => {
      const schema = properties[propertyName];
      const defaultValue = schema.defaultValue || '';
      handlePropertyChange(defaultValue, propertyName, schema);
    });
  };

  // Handle reset individual property
  const handleResetProperty = (propertyName, e) => {
    e.stopPropagation();

    const schema = properties[propertyName];
    const defaultValue = schema.defaultValue || '';
    handlePropertyChange(defaultValue, propertyName, schema);
  };

  // Toggle group expansion
  const toggleExpanded = () => {
    if (collapsible) {
      setIsExpanded(!isExpanded);
    }
  };

  // Count properties with non-default values
  const modifiedCount = Object.keys(properties).filter(key => {
    const value = values[key];
    const defaultValue = properties[key].defaultValue || '';
    return value !== defaultValue && value !== '' && value !== null && value !== undefined;
  }).length;

  const propertyCount = Object.keys(properties).length;

  if (propertyCount === 0) {
    return null;
  }

  return (
    <GroupContainer>
      <GroupHeader onClick={toggleExpanded}>
        <GroupTitle>
          {collapsible && (
            isExpanded ? <DownOutlined style={{ fontSize: '12px' }} /> : <RightOutlined style={{ fontSize: '12px' }} />
          )}
          {getGroupIcon(groupName)}
          <Text strong style={{ fontSize: '14px' }}>
            {formatGroupName(groupName)}
          </Text>
          {showPropertyCount && (
            <Space size={4}>
              <Badge count={propertyCount} size="small" color="#f0f0f0" style={{ color: '#8c8c8c' }} />
              {modifiedCount > 0 && (
                <Badge count={modifiedCount} size="small" color="#1890ff" />
              )}
            </Space>
          )}
        </GroupTitle>

        <GroupActions>
          {showResetAll && modifiedCount > 0 && (
            <Tooltip title="Reset all properties in this group">
              <Button
                type="text"
                size="small"
                icon={<UndoOutlined />}
                onClick={handleResetAll}
                style={{ fontSize: '12px' }}
              />
            </Tooltip>
          )}
        </GroupActions>
      </GroupHeader>

      {isExpanded && (
        <div style={{ border: '1px solid #f0f0f0', borderTop: 'none', borderRadius: '0 0 4px 4px' }}>
          {Object.entries(properties).map(([propertyName, schema]) => {
            const currentValue = values[propertyName];
            const hasValue = currentValue !== undefined && currentValue !== '' && currentValue !== null;
            const isModified = hasValue && currentValue !== (schema.defaultValue || '');

            return (
              <PropertyItem key={propertyName}>
                <PropertyLabel>
                  <div>
                    <PropertyName>
                      {schema.label || propertyName}
                      {schema.required && <Text type="danger"> *</Text>}
                    </PropertyName>
                    {schema.description && (
                      <PropertyDescription>{schema.description}</PropertyDescription>
                    )}
                  </div>

                  {isModified && (
                    <Tooltip title="Reset to default">
                      <Button
                        type="text"
                        size="small"
                        icon={<UndoOutlined />}
                        onClick={(e) => handleResetProperty(propertyName, e)}
                        style={{ fontSize: '12px' }}
                      />
                    </Tooltip>
                  )}
                </PropertyLabel>

                <PropertyRenderer
                  propertyName={propertyName}
                  value={currentValue}
                  onChange={handlePropertyChange}
                  componentType={componentType}
                  schema={schema}
                  size="small"
                  {...props}
                />
              </PropertyItem>
            );
          })}
        </div>
      )}
    </GroupContainer>
  );
};

export default PropertyGroup;
