import React, { useEffect, useState } from 'react';
import { Modal, Button, Typography, Space } from 'antd';
import { QuestionCircleOutlined, CloseOutlined } from '@ant-design/icons';
import { useTutorial } from './TutorialManager';

const { Title, Paragraph } = Typography;

const FirstTimeUserPrompt = () => {
  const [visible, setVisible] = useState(false);
  const { startTutorial } = useTutorial();
  
  useEffect(() => {
    // Check if this is the first visit
    const hasVisitedBefore = localStorage.getItem('app_builder_visited');
    
    if (!hasVisitedBefore) {
      // Set a small delay to show the prompt after the app loads
      const timer = setTimeout(() => {
        setVisible(true);
        localStorage.setItem('app_builder_visited', 'true');
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, []);
  
  const handleStartTutorial = () => {
    setVisible(false);
    startTutorial('app_builder_intro');
  };
  
  const handleSkip = () => {
    setVisible(false);
  };
  
  return (
    <Modal
      open={visible}
      footer={null}
      closable={true}
      closeIcon={<CloseOutlined />}
      onCancel={handleSkip}
      width={500}
      centered
    >
      <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
        <Title level={3}>Welcome to App Builder!</Title>
        <Paragraph>
          Would you like to take a quick tour to learn the basics?
        </Paragraph>
        <Space>
          <Button 
            type="primary" 
            size="large" 
            icon={<QuestionCircleOutlined />}
            onClick={handleStartTutorial}
          >
            Start Tutorial
          </Button>
          <Button size="large" onClick={handleSkip}>
            Skip for Now
          </Button>
        </Space>
      </Space>
    </Modal>
  );
};

export default FirstTimeUserPrompt;