import React, { memo } from 'react';
import PropTypes from 'prop-types';

/**
 * DebugInfoPanel - A component to display debug information
 * 
 * This component is memoized to prevent unnecessary re-renders
 * and is only displayed in development mode.
 */
const DebugInfoPanel = memo(({
  websocketStatus,
  apiCheckResults,
  backendStatus,
  wsStatus,
  themeMode
}) => {
  return (
    <>
      {/* WebSocket status indicator */}
      {websocketStatus && (
        <div className={`connection-status ${websocketStatus}`}>
          WebSocket: {websocketStatus}
        </div>
      )}

      {/* API endpoints status indicator */}
      {apiCheckResults && (
        <div className={`api-status ${apiCheckResults.anyAvailable ? 'available' : 'unavailable'}`}>
          API: {apiCheckResults.anyAvailable
            ? `${apiCheckResults.available.length} endpoints`
            : 'Unavailable'}
        </div>
      )}

      {/* Backend status indicator */}
      {backendStatus && (
        <div className={`backend-status ${backendStatus.available ? 'available' : 'unavailable'}`}
          style={{ top: '50px', right: '0' }}>
          Backend: {backendStatus.available ? 'Available' : 'Unavailable'}
        </div>
      )}

      {/* WebSocket server status indicator */}
      {wsStatus && (
        <div className={`ws-server-status ${wsStatus.available ? 'available' : 'unavailable'}`}
          style={{ top: '75px', right: '0' }}>
          WS Server: {wsStatus.available ? 'Available' : 'Unavailable'}
        </div>
      )}

      {/* Mock servers indicator */}
      {window.MOCK_SERVERS_ENABLED && (
        <div className="mock-servers-status"
          style={{ top: '100px', right: '0', backgroundColor: '#722ed1' }}>
          Using Mock Servers
        </div>
      )}

      {/* Theme mode indicator */}
      <div className="theme-mode-status"
        style={{ top: '125px', right: '0', backgroundColor: themeMode === 'dark' ? '#111827' : '#60A5FA' }}>
        Theme: {themeMode}
      </div>
    </>
  );
});

DebugInfoPanel.propTypes = {
  websocketStatus: PropTypes.string,
  apiCheckResults: PropTypes.shape({
    anyAvailable: PropTypes.bool,
    available: PropTypes.array
  }),
  backendStatus: PropTypes.shape({
    available: PropTypes.bool
  }),
  wsStatus: PropTypes.shape({
    available: PropTypes.bool
  }),
  themeMode: PropTypes.string
};

// Display name for debugging
DebugInfoPanel.displayName = 'DebugInfoPanel';

export default DebugInfoPanel;
