<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Performance Monitor - App Builder 201</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }

        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .monitor-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }

        .metric-card {
            background: white;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #10b981;
        }

        .metric-card.warning {
            border-left-color: #f59e0b;
        }

        .metric-card.error {
            border-left-color: #ef4444;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }

        .metric-label {
            font-size: 14px;
            color: #6b7280;
            margin-top: 5px;
        }

        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #1d4ed8;
        }

        .cache-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            font-size: 12px;
        }

        .cache-item.cached {
            border-left: 4px solid #10b981;
        }

        .cache-item.network {
            border-left: 4px solid #3b82f6;
        }

        .cache-item.error {
            border-left: 4px solid #ef4444;
        }

        .performance-chart {
            height: 200px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            position: relative;
        }

        .chart-bar {
            background: #2563eb;
            height: 20px;
            margin: 5px 0;
            border-radius: 2px;
            position: relative;
        }

        .chart-label {
            position: absolute;
            left: 5px;
            top: 2px;
            color: white;
            font-size: 12px;
        }

        .real-time-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .code {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }

        .status-good {
            color: #10b981;
        }

        .status-warning {
            color: #f59e0b;
        }

        .status-error {
            color: #ef4444;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>📊 Cache Performance Monitor - App Builder 201</h1>

        <div class="monitor-section">
            <h3><span class="real-time-indicator"></span> Real-time Monitoring</h3>
            <button onclick="startMonitoring()" id="start-btn">Start Monitoring</button>
            <button onclick="stopMonitoring()" id="stop-btn" disabled>Stop Monitoring</button>
            <button onclick="clearData()">Clear Data</button>
            <button onclick="exportData()">Export Data</button>
        </div>

        <div class="monitor-grid">
            <div class="monitor-section">
                <h3>📈 Performance Metrics</h3>
                <div class="metric-card">
                    <div class="metric-value" id="cache-hit-rate">0%</div>
                    <div class="metric-label">Cache Hit Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="avg-response-time">0ms</div>
                    <div class="metric-label">Average Response Time</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="total-requests">0</div>
                    <div class="metric-label">Total Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="cache-size">0</div>
                    <div class="metric-label">Total Cache Items</div>
                </div>
            </div>

            <div class="monitor-section">
                <h3>💾 Cache Status</h3>
                <div id="cache-status">
                    <div class="code">Initializing cache monitoring...</div>
                </div>
                <button onclick="refreshCacheStatus()">Refresh Cache Status</button>
            </div>

            <div class="monitor-section">
                <h3>🔄 Request Timeline</h3>
                <div class="performance-chart" id="request-timeline">
                    <div class="code">No requests monitored yet</div>
                </div>
            </div>

            <div class="monitor-section">
                <h3>📋 Recent Requests</h3>
                <div id="recent-requests">
                    <div class="code">Start monitoring to see requests</div>
                </div>
            </div>
        </div>

        <div class="monitor-section">
            <h3>🔍 Cache Inspection</h3>
            <div id="cache-inspection">
                <button onclick="inspectAllCaches()">Inspect All Caches</button>
                <button onclick="testCacheStrategies()">Test Cache Strategies</button>
                <button onclick="simulateOffline()">Simulate Offline</button>
            </div>
            <div id="inspection-results"></div>
        </div>

        <div class="monitor-section">
            <h3>⚡ Performance Tests</h3>
            <button onclick="runPerformanceTest()">Run Performance Test</button>
            <button onclick="testCacheInvalidation()">Test Cache Invalidation</button>
            <button onclick="benchmarkCacheStrategies()">Benchmark Strategies</button>
            <div id="performance-results"></div>
        </div>

        <div class="monitor-section">
            <h3>📝 DevTools Integration</h3>
            <div class="code">
                <strong>Manual DevTools Inspection:</strong><br>
                1. Open DevTools (F12)<br>
                2. Go to Application tab<br>
                3. Check Service Workers section<br>
                4. Inspect Cache Storage<br>
                5. Monitor Network tab for cache hits<br><br>

                <strong>Cache Storage Location:</strong><br>
                Application → Storage → Cache Storage → app-builder-cache-v*
            </div>
        </div>
    </div>

    <script>
        let monitoringActive = false;
        let monitoringInterval;
        let requestLog = [];
        let performanceData = {
            cacheHits: 0,
            cacheMisses: 0,
            totalRequests: 0,
            responseTimes: [],
            cacheSize: 0
        };

        function log(message) {
            console.log(`[Cache Monitor] ${message}`);
        }

        function updateMetrics() {
            const hitRate = performanceData.totalRequests > 0 ?
                Math.round((performanceData.cacheHits / performanceData.totalRequests) * 100) : 0;

            const avgResponseTime = performanceData.responseTimes.length > 0 ?
                Math.round(performanceData.responseTimes.reduce((a, b) => a + b, 0) / performanceData.responseTimes.length) : 0;

            document.getElementById('cache-hit-rate').textContent = hitRate + '%';
            document.getElementById('avg-response-time').textContent = avgResponseTime + 'ms';
            document.getElementById('total-requests').textContent = performanceData.totalRequests;
            document.getElementById('cache-size').textContent = performanceData.cacheSize;

            // Update metric card colors based on performance
            const hitRateCard = document.getElementById('cache-hit-rate').parentElement;
            hitRateCard.className = 'metric-card ' + (hitRate > 80 ? '' : hitRate > 50 ? 'warning' : 'error');
        }

        async function startMonitoring() {
            if (monitoringActive) return;

            monitoringActive = true;
            document.getElementById('start-btn').disabled = true;
            document.getElementById('stop-btn').disabled = false;

            log('Starting performance monitoring...');

            // Monitor fetch requests
            const originalFetch = window.fetch;
            window.fetch = async function (...args) {
                const startTime = performance.now();
                const url = args[0];

                try {
                    const response = await originalFetch.apply(this, args);
                    const endTime = performance.now();
                    const responseTime = endTime - startTime;

                    // Determine if response came from cache
                    const fromCache = response.headers.get('x-cache') === 'HIT' ||
                        response.type === 'cached' ||
                        responseTime < 10; // Very fast responses likely from cache

                    // Log request
                    const requestInfo = {
                        url: url,
                        method: args[1]?.method || 'GET',
                        status: response.status,
                        responseTime: responseTime,
                        fromCache: fromCache,
                        timestamp: new Date()
                    };

                    requestLog.unshift(requestInfo);
                    if (requestLog.length > 50) requestLog.pop(); // Keep last 50 requests

                    // Update performance data
                    performanceData.totalRequests++;
                    if (fromCache) {
                        performanceData.cacheHits++;
                    } else {
                        performanceData.cacheMisses++;
                    }
                    performanceData.responseTimes.push(responseTime);
                    if (performanceData.responseTimes.length > 100) {
                        performanceData.responseTimes.shift(); // Keep last 100 response times
                    }

                    updateMetrics();
                    updateRequestTimeline();
                    updateRecentRequests();

                    return response;
                } catch (error) {
                    const endTime = performance.now();
                    const responseTime = endTime - startTime;

                    requestLog.unshift({
                        url: url,
                        method: args[1]?.method || 'GET',
                        status: 'ERROR',
                        responseTime: responseTime,
                        fromCache: false,
                        error: error.message,
                        timestamp: new Date()
                    });

                    throw error;
                }
            };

            // Start periodic cache size monitoring
            monitoringInterval = setInterval(async () => {
                await updateCacheSize();
            }, 5000);

            await refreshCacheStatus();
        }

        function stopMonitoring() {
            monitoringActive = false;
            document.getElementById('start-btn').disabled = false;
            document.getElementById('stop-btn').disabled = true;

            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }

            log('Monitoring stopped');
        }

        async function updateCacheSize() {
            try {
                const cacheNames = await caches.keys();
                let totalSize = 0;

                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const keys = await cache.keys();
                    totalSize += keys.length;
                }

                performanceData.cacheSize = totalSize;
                updateMetrics();
            } catch (error) {
                log(`Error updating cache size: ${error.message}`);
            }
        }

        function updateRequestTimeline() {
            const timeline = document.getElementById('request-timeline');
            const recentRequests = requestLog.slice(0, 10);

            if (recentRequests.length === 0) {
                timeline.innerHTML = '<div class="code">No requests monitored yet</div>';
                return;
            }

            let html = '';
            const maxTime = Math.max(...recentRequests.map(r => r.responseTime));

            recentRequests.forEach(req => {
                const width = (req.responseTime / maxTime) * 100;
                const color = req.fromCache ? '#10b981' : req.status === 'ERROR' ? '#ef4444' : '#3b82f6';

                html += `
                    <div class="chart-bar" style="width: ${width}%; background: ${color};">
                        <div class="chart-label">${req.responseTime.toFixed(1)}ms - ${req.url.split('/').pop()}</div>
                    </div>
                `;
            });

            timeline.innerHTML = html;
        }

        function updateRecentRequests() {
            const container = document.getElementById('recent-requests');
            const recentRequests = requestLog.slice(0, 10);

            if (recentRequests.length === 0) {
                container.innerHTML = '<div class="code">Start monitoring to see requests</div>';
                return;
            }

            let html = '';
            recentRequests.forEach(req => {
                const className = req.fromCache ? 'cached' : req.status === 'ERROR' ? 'error' : 'network';
                const statusText = req.fromCache ? 'CACHE' : req.status;

                html += `
                    <div class="cache-item ${className}">
                        <strong>${req.method} ${req.url.split('/').pop()}</strong><br>
                        Status: ${statusText} | Time: ${req.responseTime.toFixed(1)}ms | 
                        ${req.timestamp.toLocaleTimeString()}
                        ${req.error ? `<br><span class="status-error">Error: ${req.error}</span>` : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        async function refreshCacheStatus() {
            try {
                const cacheNames = await caches.keys();
                const statusContainer = document.getElementById('cache-status');

                if (cacheNames.length === 0) {
                    statusContainer.innerHTML = '<div class="code status-warning">No caches found</div>';
                    return;
                }

                let html = '<div class="code">';
                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const keys = await cache.keys();
                    const isAppCache = cacheName.includes('app-builder-cache');

                    html += `<div class="${isAppCache ? 'status-good' : ''}">
                        <strong>${cacheName}</strong>: ${keys.length} items
                        ${isAppCache ? ' (App Cache)' : ''}
                    </div>`;
                }
                html += '</div>';

                statusContainer.innerHTML = html;
                log(`Found ${cacheNames.length} cache(s)`);
            } catch (error) {
                document.getElementById('cache-status').innerHTML =
                    `<div class="code status-error">Error: ${error.message}</div>`;
                log(`Error refreshing cache status: ${error.message}`);
            }
        }

        async function inspectAllCaches() {
            log('Inspecting all caches...');
            const resultsContainer = document.getElementById('inspection-results');

            try {
                const cacheNames = await caches.keys();

                if (cacheNames.length === 0) {
                    resultsContainer.innerHTML = '<div class="code status-warning">No caches to inspect</div>';
                    return;
                }

                let html = '<h4>Cache Inspection Results:</h4>';

                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const keys = await cache.keys();

                    html += `<div class="metric-card">
                        <strong>${cacheName}</strong> (${keys.length} items)<br>
                        <div class="code">`;

                    keys.slice(0, 10).forEach(request => {
                        html += `${request.method} ${request.url}<br>`;
                    });

                    if (keys.length > 10) {
                        html += `... and ${keys.length - 10} more items`;
                    }

                    html += '</div></div>';
                }

                resultsContainer.innerHTML = html;
                log('Cache inspection completed');
            } catch (error) {
                resultsContainer.innerHTML = `<div class="code status-error">Inspection failed: ${error.message}</div>`;
                log(`Cache inspection failed: ${error.message}`);
            }
        }

        async function testCacheStrategies() {
            log('Testing cache strategies...');
            const resultsContainer = document.getElementById('inspection-results');

            try {
                const testUrls = [
                    '/manifest.json',
                    '/offline.html',
                    '/api-offline.json',
                    '/favicon.ico'
                ];

                let html = '<h4>Cache Strategy Test Results:</h4>';

                for (const url of testUrls) {
                    const startTime = performance.now();

                    try {
                        const response = await fetch(url);
                        const endTime = performance.now();
                        const responseTime = endTime - startTime;

                        const strategy = responseTime < 10 ? 'Cache First' :
                            responseTime < 100 ? 'Stale While Revalidate' : 'Network First';

                        html += `<div class="cache-item cached">
                            <strong>${url}</strong><br>
                            Response Time: ${responseTime.toFixed(1)}ms<br>
                            Likely Strategy: ${strategy}<br>
                            Status: ${response.status}
                        </div>`;
                    } catch (error) {
                        html += `<div class="cache-item error">
                            <strong>${url}</strong><br>
                            Error: ${error.message}
                        </div>`;
                    }
                }

                resultsContainer.innerHTML = html;
                log('Cache strategy testing completed');
            } catch (error) {
                resultsContainer.innerHTML = `<div class="code status-error">Strategy test failed: ${error.message}</div>`;
                log(`Cache strategy test failed: ${error.message}`);
            }
        }

        async function runPerformanceTest() {
            log('Running performance test...');
            const resultsContainer = document.getElementById('performance-results');

            try {
                const testUrls = [
                    '/manifest.json',
                    '/offline.html',
                    '/api-offline.json',
                    '/service-worker.js'
                ];

                let totalTime = 0;
                let cacheHits = 0;
                let html = '<h4>Performance Test Results:</h4>';

                for (let i = 0; i < 3; i++) { // Run 3 iterations
                    html += `<div class="metric-card"><strong>Iteration ${i + 1}:</strong><br>`;

                    for (const url of testUrls) {
                        const startTime = performance.now();
                        const response = await fetch(url);
                        const endTime = performance.now();
                        const responseTime = endTime - startTime;

                        totalTime += responseTime;
                        if (responseTime < 10) cacheHits++;

                        html += `${url}: ${responseTime.toFixed(1)}ms<br>`;
                    }

                    html += '</div>';
                }

                const avgTime = totalTime / (testUrls.length * 3);
                const hitRate = (cacheHits / (testUrls.length * 3)) * 100;

                html += `<div class="metric-card">
                    <strong>Summary:</strong><br>
                    Average Response Time: ${avgTime.toFixed(1)}ms<br>
                    Cache Hit Rate: ${hitRate.toFixed(1)}%<br>
                    Performance Rating: ${avgTime < 50 ? 'Excellent' : avgTime < 200 ? 'Good' : 'Needs Improvement'}
                </div>`;

                resultsContainer.innerHTML = html;
                log('Performance test completed');
            } catch (error) {
                resultsContainer.innerHTML = `<div class="code status-error">Performance test failed: ${error.message}</div>`;
                log(`Performance test failed: ${error.message}`);
            }
        }

        async function testCacheInvalidation() {
            log('Testing cache invalidation...');
            const resultsContainer = document.getElementById('performance-results');

            try {
                // Create a test cache
                const testCacheName = 'test-invalidation-' + Date.now();
                const testCache = await caches.open(testCacheName);

                // Add a test item
                const testUrl = '/test-invalidation-item';
                const testResponse = new Response('Test content', {
                    headers: { 'Content-Type': 'text/plain' }
                });

                await testCache.put(testUrl, testResponse);

                // Verify it exists
                const cachedItem = await testCache.match(testUrl);
                if (!cachedItem) {
                    throw new Error('Failed to cache test item');
                }

                // Delete the cache
                const deleted = await caches.delete(testCacheName);

                // Verify it's gone
                const cacheNames = await caches.keys();
                const stillExists = cacheNames.includes(testCacheName);

                resultsContainer.innerHTML = `
                    <div class="metric-card">
                        <strong>Cache Invalidation Test:</strong><br>
                        Cache Created: ✅<br>
                        Item Cached: ✅<br>
                        Cache Deleted: ${deleted ? '✅' : '❌'}<br>
                        Cache Removed: ${!stillExists ? '✅' : '❌'}<br>
                        Result: ${deleted && !stillExists ? 'PASS' : 'FAIL'}
                    </div>
                `;

                log('Cache invalidation test completed');
            } catch (error) {
                resultsContainer.innerHTML = `<div class="code status-error">Invalidation test failed: ${error.message}</div>`;
                log(`Cache invalidation test failed: ${error.message}`);
            }
        }

        function clearData() {
            requestLog = [];
            performanceData = {
                cacheHits: 0,
                cacheMisses: 0,
                totalRequests: 0,
                responseTimes: [],
                cacheSize: 0
            };

            updateMetrics();
            document.getElementById('request-timeline').innerHTML = '<div class="code">No requests monitored yet</div>';
            document.getElementById('recent-requests').innerHTML = '<div class="code">Start monitoring to see requests</div>';

            log('Performance data cleared');
        }

        function exportData() {
            const exportData = {
                timestamp: new Date().toISOString(),
                performanceData: performanceData,
                requestLog: requestLog.slice(0, 100), // Export last 100 requests
                userAgent: navigator.userAgent,
                url: window.location.href
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cache-performance-data-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            log('Performance data exported');
        }

        function simulateOffline() {
            log('Simulating offline mode...');

            // This is a simulation - in real testing, you'd use DevTools
            const resultsContainer = document.getElementById('inspection-results');
            resultsContainer.innerHTML = `
                <div class="metric-card warning">
                    <strong>Offline Simulation:</strong><br>
                    To properly test offline functionality:<br>
                    1. Open DevTools (F12)<br>
                    2. Go to Network tab<br>
                    3. Check "Offline" checkbox<br>
                    4. Try navigating or making requests<br>
                    5. Verify service worker serves cached content
                </div>
            `;
        }

        async function benchmarkCacheStrategies() {
            log('Benchmarking cache strategies...');
            const resultsContainer = document.getElementById('performance-results');

            try {
                const strategies = [
                    { name: 'Cache First', urls: ['/manifest.json', '/offline.html'] },
                    { name: 'Network First', urls: ['/api-offline.json'] },
                    { name: 'Stale While Revalidate', urls: ['/service-worker.js'] }
                ];

                let html = '<h4>Cache Strategy Benchmark:</h4>';

                for (const strategy of strategies) {
                    let totalTime = 0;
                    let requestCount = 0;

                    for (const url of strategy.urls) {
                        for (let i = 0; i < 5; i++) { // 5 requests per URL
                            const startTime = performance.now();
                            await fetch(url);
                            const endTime = performance.now();
                            totalTime += (endTime - startTime);
                            requestCount++;
                        }
                    }

                    const avgTime = totalTime / requestCount;

                    html += `<div class="metric-card">
                        <strong>${strategy.name}</strong><br>
                        Average Response Time: ${avgTime.toFixed(1)}ms<br>
                        Requests Tested: ${requestCount}<br>
                        Performance: ${avgTime < 50 ? 'Excellent' : avgTime < 200 ? 'Good' : 'Poor'}
                    </div>`;
                }

                resultsContainer.innerHTML = html;
                log('Cache strategy benchmark completed');
            } catch (error) {
                resultsContainer.innerHTML = `<div class="code status-error">Benchmark failed: ${error.message}</div>`;
                log(`Cache strategy benchmark failed: ${error.message}`);
            }
        }

        // Initialize on page load
        window.addEventListener('load', async () => {
            log('Cache Performance Monitor loaded');
            await refreshCacheStatus();
            updateMetrics();
        });
    </script>
</body>

</html>