/**
 * Test Utilities
 * 
 * This module provides utilities for testing the application.
 */

import { message } from 'antd';

/**
 * Test the WebSocket connection
 * @param {string} url - WebSocket URL to test
 * @returns {Promise<Object>} Test results
 */
export const testWebSocketConnection = async (url) => {
  return new Promise((resolve) => {
    try {
      const startTime = performance.now();
      const ws = new WebSocket(url);
      
      const timeout = setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          ws.close();
          resolve({
            success: false,
            error: 'Connection timeout',
            time: performance.now() - startTime
          });
        }
      }, 5000);
      
      ws.onopen = () => {
        clearTimeout(timeout);
        const connectionTime = performance.now() - startTime;
        
        // Send a ping message
        ws.send(JSON.stringify({ type: 'ping' }));
        
        // Wait for a response or timeout
        const responseTimeout = setTimeout(() => {
          ws.close();
          resolve({
            success: true,
            error: null,
            connectionTime,
            responseTime: null,
            message: 'Connected but no response to ping'
          });
        }, 2000);
        
        ws.onmessage = (event) => {
          clearTimeout(responseTimeout);
          const responseTime = performance.now() - startTime;
          
          ws.close();
          resolve({
            success: true,
            error: null,
            connectionTime,
            responseTime,
            message: 'Connection successful'
          });
        };
        
        ws.onerror = (error) => {
          clearTimeout(responseTimeout);
          ws.close();
          resolve({
            success: false,
            error: 'Error after connection: ' + error.message,
            connectionTime,
            responseTime: null
          });
        };
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        ws.close();
        resolve({
          success: false,
          error: 'Connection error: ' + error.message,
          time: performance.now() - startTime
        });
      };
      
      ws.onclose = (event) => {
        clearTimeout(timeout);
        if (event.code !== 1000 && event.code !== 1001) {
          resolve({
            success: false,
            error: `Connection closed with code ${event.code}: ${event.reason}`,
            time: performance.now() - startTime
          });
        }
      };
    } catch (error) {
      resolve({
        success: false,
        error: 'Exception: ' + error.message,
        time: 0
      });
    }
  });
};

/**
 * Test the API connection
 * @param {string} url - API URL to test
 * @returns {Promise<Object>} Test results
 */
export const testApiConnection = async (url) => {
  try {
    const startTime = performance.now();
    const response = await fetch(url, { 
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });
    
    const time = performance.now() - startTime;
    
    if (response.ok) {
      let data;
      try {
        data = await response.json();
      } catch (error) {
        data = await response.text();
      }
      
      return {
        success: true,
        status: response.status,
        statusText: response.statusText,
        time,
        data
      };
    } else {
      return {
        success: false,
        status: response.status,
        statusText: response.statusText,
        time,
        error: `HTTP error: ${response.status} ${response.statusText}`
      };
    }
  } catch (error) {
    return {
      success: false,
      error: 'Exception: ' + error.message,
      time: 0
    };
  }
};

/**
 * Test browser performance
 * @returns {Object} Performance metrics
 */
export const testBrowserPerformance = () => {
  try {
    // Get performance metrics
    const metrics = {};
    
    // Navigation timing
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      
      metrics.pageLoad = timing.loadEventEnd - timing.navigationStart;
      metrics.domReady = timing.domComplete - timing.domLoading;
      metrics.networkLatency = timing.responseEnd - timing.fetchStart;
      metrics.processingTime = timing.domComplete - timing.responseEnd;
      metrics.backendTime = timing.responseStart - timing.navigationStart;
      metrics.frontendTime = timing.loadEventEnd - timing.responseStart;
    }
    
    // Memory info (Chrome only)
    if (window.performance && window.performance.memory) {
      metrics.memory = {
        jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit,
        totalJSHeapSize: window.performance.memory.totalJSHeapSize,
        usedJSHeapSize: window.performance.memory.usedJSHeapSize
      };
    }
    
    // Frame rate (if available)
    if (window.requestAnimationFrame) {
      metrics.frameRate = {
        current: 0,
        average: 0
      };
      
      let frameCount = 0;
      let lastTime = performance.now();
      
      const countFrames = (time) => {
        frameCount++;
        const elapsed = time - lastTime;
        
        if (elapsed >= 1000) {
          metrics.frameRate.current = Math.round((frameCount * 1000) / elapsed);
          
          if (!metrics.frameRate.average) {
            metrics.frameRate.average = metrics.frameRate.current;
          } else {
            metrics.frameRate.average = Math.round((metrics.frameRate.average + metrics.frameRate.current) / 2);
          }
          
          frameCount = 0;
          lastTime = time;
        }
        
        window.requestAnimationFrame(countFrames);
      };
      
      window.requestAnimationFrame(countFrames);
    }
    
    return {
      success: true,
      metrics
    };
  } catch (error) {
    return {
      success: false,
      error: 'Exception: ' + error.message
    };
  }
};

/**
 * Run all tests
 * @param {Object} options - Test options
 * @returns {Promise<Object>} Test results
 */
export const runAllTests = async (options = {}) => {
  const results = {
    websocket: null,
    api: null,
    performance: null
  };
  
  try {
    // Test WebSocket connection
    if (options.websocketUrl) {
      message.info('Testing WebSocket connection...');
      results.websocket = await testWebSocketConnection(options.websocketUrl);
      
      if (results.websocket.success) {
        message.success(`WebSocket connection successful (${Math.round(results.websocket.connectionTime)}ms)`);
      } else {
        message.error(`WebSocket connection failed: ${results.websocket.error}`);
      }
    }
    
    // Test API connection
    if (options.apiUrl) {
      message.info('Testing API connection...');
      results.api = await testApiConnection(options.apiUrl);
      
      if (results.api.success) {
        message.success(`API connection successful (${Math.round(results.api.time)}ms)`);
      } else {
        message.error(`API connection failed: ${results.api.error}`);
      }
    }
    
    // Test browser performance
    if (options.testPerformance) {
      message.info('Testing browser performance...');
      results.performance = testBrowserPerformance();
      
      if (results.performance.success) {
        message.success('Performance test completed');
      } else {
        message.error(`Performance test failed: ${results.performance.error}`);
      }
    }
    
    return results;
  } catch (error) {
    message.error(`Test failed: ${error.message}`);
    return {
      ...results,
      error: error.message
    };
  }
};

export default {
  testWebSocketConnection,
  testApiConnection,
  testBrowserPerformance,
  runAllTests
};
