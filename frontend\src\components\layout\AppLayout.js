import React, { useState } from 'react';
import { ThemeSwitcher } from '../theme/ThemeManager';
import ConnectionStatus from '../ConnectionStatus';

/**
 * App Layout Component
 * 
 * This component provides a consistent layout for the application.
 */
const AppLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showConnectionDetails, setShowConnectionDetails] = useState(false);
  
  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  
  return (
    <div className={`app-layout ${sidebarOpen ? 'sidebar-open' : ''}`}>
      <header className="app-header">
        <div className="header-left">
          <button
            className="sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
          >
            <span className="toggle-icon"></span>
          </button>
          <h1 className="app-title">App Builder 201</h1>
        </div>
        <div className="header-right">
          <ThemeSwitcher />
          <div className="connection-indicator">
            <ConnectionStatus 
              showDetails={showConnectionDetails}
              onStatusChange={(type, newStatus) => {
                console.log(`Connection status changed: ${type} -> ${newStatus}`);
              }}
            />
            <button
              className="connection-toggle"
              onClick={() => setShowConnectionDetails(!showConnectionDetails)}
            >
              {showConnectionDetails ? 'Hide Details' : 'Show Details'}
            </button>
          </div>
        </div>
      </header>
      
      <div className="app-container">
        <aside className={`app-sidebar ${sidebarOpen ? 'open' : ''}`}>
          <nav className="sidebar-nav">
            <ul className="nav-list">
              <li className="nav-item">
                <a href="/" className="nav-link">
                  <span className="nav-icon">🏠</span>
                  <span className="nav-text">Home</span>
                </a>
              </li>
              <li className="nav-item">
                <a href="/builder" className="nav-link">
                  <span className="nav-icon">🔨</span>
                  <span className="nav-text">App Builder</span>
                </a>
              </li>
              <li className="nav-item">
                <a href="/components" className="nav-link">
                  <span className="nav-icon">🧩</span>
                  <span className="nav-text">Components</span>
                </a>
              </li>
              <li className="nav-item">
                <a href="/templates" className="nav-link">
                  <span className="nav-icon">📋</span>
                  <span className="nav-text">Templates</span>
                </a>
              </li>
              <li className="nav-item">
                <a href="/settings" className="nav-link">
                  <span className="nav-icon">⚙️</span>
                  <span className="nav-text">Settings</span>
                </a>
              </li>
              <li className="nav-item">
                <a href="/tests" className="nav-link">
                  <span className="nav-icon">🧪</span>
                  <span className="nav-text">Tests</span>
                </a>
              </li>
            </ul>
          </nav>
          
          <div className="sidebar-footer">
            <p>App Builder 201</p>
            <p className="version">v1.0.0</p>
          </div>
        </aside>
        
        <main className="app-content">
          {children}
        </main>
      </div>
      
      <footer className="app-footer">
        <p>&copy; {new Date().getFullYear()} App Builder 201</p>
      </footer>
      
      {sidebarOpen && (
        <div className="sidebar-backdrop" onClick={toggleSidebar}></div>
      )}
      
      <style jsx>{`
        .app-layout {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
          background-color: var(--color-background);
          color: var(--color-text);
        }
        
        .app-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-md);
          background-color: var(--color-surface);
          box-shadow: var(--shadow-sm);
          z-index: var(--z-index-sticky);
        }
        
        .header-left, .header-right {
          display: flex;
          align-items: center;
          gap: var(--spacing-md);
        }
        
        .app-title {
          margin: 0;
          font-size: var(--font-size-lg);
        }
        
        .sidebar-toggle {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          padding: 0;
          background: none;
          border: none;
          cursor: pointer;
          color: var(--color-text);
        }
        
        .toggle-icon {
          position: relative;
          width: 20px;
          height: 2px;
          background-color: currentColor;
        }
        
        .toggle-icon::before,
        .toggle-icon::after {
          content: '';
          position: absolute;
          width: 20px;
          height: 2px;
          background-color: currentColor;
          transition: transform var(--transition-fast) var(--transition-timing-function);
        }
        
        .toggle-icon::before {
          top: -6px;
        }
        
        .toggle-icon::after {
          bottom: -6px;
        }
        
        .sidebar-open .toggle-icon {
          background-color: transparent;
        }
        
        .sidebar-open .toggle-icon::before {
          transform: rotate(45deg);
          top: 0;
        }
        
        .sidebar-open .toggle-icon::after {
          transform: rotate(-45deg);
          bottom: 0;
        }
        
        .connection-indicator {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }
        
        .connection-toggle {
          margin-top: var(--spacing-xs);
          padding: var(--spacing-xs) var(--spacing-sm);
          font-size: var(--font-size-xs);
          background: none;
          border: 1px solid var(--color-border);
          color: var(--color-text);
        }
        
        .app-container {
          display: flex;
          flex: 1;
        }
        
        .app-sidebar {
          width: 250px;
          background-color: var(--color-surface);
          box-shadow: var(--shadow-sm);
          display: flex;
          flex-direction: column;
          transition: transform var(--transition-normal) var(--transition-timing-function);
          z-index: var(--z-index-fixed);
        }
        
        .sidebar-nav {
          flex: 1;
          padding: var(--spacing-md) 0;
        }
        
        .nav-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }
        
        .nav-item {
          margin-bottom: var(--spacing-xs);
        }
        
        .nav-link {
          display: flex;
          align-items: center;
          padding: var(--spacing-sm) var(--spacing-md);
          color: var(--color-text);
          text-decoration: none;
          transition: background-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .nav-link:hover {
          background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
          text-decoration: none;
        }
        
        .nav-icon {
          margin-right: var(--spacing-sm);
          font-size: var(--font-size-lg);
        }
        
        .sidebar-footer {
          padding: var(--spacing-md);
          border-top: 1px solid var(--color-border);
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .sidebar-footer p {
          margin: 0;
        }
        
        .version {
          font-size: var(--font-size-xs);
        }
        
        .app-content {
          flex: 1;
          padding: var(--spacing-md);
          overflow-y: auto;
        }
        
        .app-footer {
          padding: var(--spacing-md);
          background-color: var(--color-surface);
          text-align: center;
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .sidebar-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: var(--z-index-modal-backdrop);
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
          .app-sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            transform: translateX(-100%);
          }
          
          .app-sidebar.open {
            transform: translateX(0);
          }
        }
      `}</style>
    </div>
  );
};

export default AppLayout;
