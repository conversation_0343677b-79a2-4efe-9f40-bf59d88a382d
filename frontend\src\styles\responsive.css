/* Mobile-first responsive design */

/* Base responsive utilities */
.responsive-container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.responsive-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

/* Tablet breakpoint (768px and below) */
@media (max-width: 768px) {

  /* Dashboard Layout */
  .dashboard-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .sidebar {
    width: 100%;
    position: static;
    height: auto;
    min-height: auto;
    order: 2;
    /* Move sidebar below main content on mobile */
  }

  .main-content {
    margin-left: 0;
    width: 100%;
    order: 1;
    padding: 12px;
  }

  /* Header adjustments */
  .header {
    padding: 8px 16px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .header .search-container {
    width: 100%;
    order: 3;
    margin-top: 8px;
  }

  /* Navigation improvements */
  .nav-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    padding: 8px 0;
  }

  .nav-item {
    flex: 1;
    min-width: 80px;
    text-align: center;
  }

  .nav-text {
    font-size: 12px;
    display: block;
    margin-top: 4px;
  }

  /* Data visualization improvements */
  .data-visualization {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 0 -12px;
    padding: 0 12px;
  }

  .chart-container {
    min-width: 320px;
    height: 250px;
  }

  /* Card layouts */
  .card-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .dashboard-card {
    margin-bottom: 12px;
    border-radius: 8px;
  }

  .dashboard-card .card-header {
    padding: 12px 16px;
    font-size: 16px;
  }

  .dashboard-card .card-content {
    padding: 12px 16px;
  }

  /* Form improvements */
  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .form-group {
    width: 100%;
  }

  /* Button groups */
  .button-group {
    flex-direction: column;
    gap: 8px;
  }

  .button-group .ant-btn {
    width: 100%;
    margin: 0;
  }

  /* Table responsiveness */
  .ant-table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .ant-table {
    min-width: 600px;
  }

  .ant-table-thead>tr>th {
    padding: 8px 12px;
    font-size: 14px;
  }

  .ant-table-tbody>tr>td {
    padding: 8px 12px;
    font-size: 14px;
  }

  /* Modal adjustments */
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .ant-modal-content {
    border-radius: 8px;
  }

  /* Drawer improvements */
  .ant-drawer-content-wrapper {
    width: 280px !important;
  }

  /* Typography adjustments */
  h1 {
    font-size: 24px;
    line-height: 1.3;
  }

  h2 {
    font-size: 20px;
    line-height: 1.3;
  }

  h3 {
    font-size: 18px;
    line-height: 1.3;
  }

  /* Spacing adjustments */
  .content-container {
    padding: 12px;
  }

  .section-spacing {
    margin-bottom: 16px;
  }

  /* Touch-friendly improvements */
  .ant-btn {
    min-height: 44px;
    padding: 8px 16px;
  }

  .ant-input {
    min-height: 44px;
    padding: 8px 12px;
  }

  .ant-select-selector {
    min-height: 44px;
    padding: 8px 12px;
  }
}

/* Small mobile devices (480px and below) */
@media (max-width: 480px) {

  /* Further reduce spacing */
  .main-content {
    padding: 8px;
  }

  .content-container {
    padding: 8px;
  }

  /* Smaller chart containers */
  .chart-container {
    height: 200px;
    min-width: 280px;
  }

  /* Stack form elements more tightly */
  .form-row {
    gap: 8px;
  }

  /* Reduce card padding */
  .dashboard-card .card-header {
    padding: 8px 12px;
    font-size: 14px;
  }

  .dashboard-card .card-content {
    padding: 8px 12px;
  }

  /* Smaller typography */
  h1 {
    font-size: 20px;
  }

  h2 {
    font-size: 18px;
  }

  h3 {
    font-size: 16px;
  }

  /* Reduce drawer width */
  .ant-drawer-content-wrapper {
    width: 260px !important;
  }

  /* Smaller modal margins */
  .ant-modal {
    margin: 8px;
    max-width: calc(100vw - 16px);
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .dashboard-container {
    flex-direction: row;
  }

  .sidebar {
    width: 200px;
    position: fixed;
    height: 100vh;
    left: -200px;
    transition: left 0.3s ease;
    z-index: 1000;
    order: 1;
  }

  .sidebar.open {
    left: 0;
  }

  .main-content {
    margin-left: 0;
    width: 100%;
    order: 2;
  }

  .chart-container {
    height: 180px;
  }
}

/* Large tablets (1024px and below) */
@media (max-width: 1024px) and (min-width: 769px) {
  .sidebar {
    width: 200px;
  }

  .main-content {
    padding: 16px;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .chart-container {
    height: 300px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .chart-container {
    /* Ensure crisp rendering on high DPI displays */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .sidebar {
    transition: none;
  }

  .dashboard-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode responsive adjustments */
@media (prefers-color-scheme: dark) {
  .sidebar {
    background-color: #1f1f1f;
    color: #ffffff;
  }

  .main-content {
    background-color: #121212;
    color: #ffffff;
  }

  .dashboard-card {
    background-color: #1e1e1e;
    border-color: #333333;
  }
}