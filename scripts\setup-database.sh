#!/bin/bash

# Database Setup Script
# This script helps you choose and configure the database for the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}🚀 $1${NC}"
    echo "=================================="
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if PostgreSQL client is available
check_psql() {
    if command -v psql > /dev/null 2>&1; then
        print_success "PostgreSQL client (psql) is available"
        return 0
    else
        print_warning "PostgreSQL client (psql) is not available"
        return 1
    fi
}

# Function to setup SQLite
setup_sqlite() {
    print_header "Setting up SQLite Database"
    
    # Create .env file with SQLite configuration
    cat > .env << EOF
# Database Configuration - Using SQLite
USE_POSTGRES=false

# Django Configuration
DJANGO_DEBUG=true
DJANGO_SECRET_KEY=dev-secret-key-change-in-production

# Frontend Configuration
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_API_URL=
REACT_APP_BACKEND_HOST=backend

# Development Settings
NODE_ENV=development
CHOKIDAR_USEPOLLING=true
EOF

    print_success "SQLite configuration created in .env file"
    
    # Run migrations using the setup script
    print_info "Running database setup..."
    docker-compose run --rm backend python setup_db.py
    
    if [ $? -eq 0 ]; then
        print_success "SQLite database setup completed successfully!"
        print_info "You can now run: docker-compose up"
    else
        print_error "Database setup failed"
        exit 1
    fi
}

# Function to setup PostgreSQL
setup_postgresql() {
    print_header "Setting up PostgreSQL Database"
    
    # Create .env file with PostgreSQL configuration
    cat > .env << EOF
# Database Configuration - Using PostgreSQL
USE_POSTGRES=true
POSTGRES_DB=myapp
POSTGRES_USER=myappuser
POSTGRES_PASSWORD=myapppassword

# Django Configuration
DJANGO_DEBUG=true
DJANGO_SECRET_KEY=dev-secret-key-change-in-production

# Frontend Configuration
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_API_URL=
REACT_APP_BACKEND_HOST=backend

# Development Settings
NODE_ENV=development
CHOKIDAR_USEPOLLING=true
EOF

    print_success "PostgreSQL configuration created in .env file"
    
    # Start PostgreSQL container first
    print_info "Starting PostgreSQL container..."
    docker-compose up -d db
    
    # Wait for PostgreSQL to be ready
    print_info "Waiting for PostgreSQL to be ready..."
    sleep 10
    
    # Run migrations
    print_info "Running database setup..."
    docker-compose run --rm backend python setup_db.py
    
    if [ $? -eq 0 ]; then
        print_success "PostgreSQL database setup completed successfully!"
        print_info "You can now run: docker-compose up"
    else
        print_error "Database setup failed"
        print_warning "You might want to try SQLite instead"
        exit 1
    fi
}

# Main menu
show_menu() {
    echo
    print_header "Database Setup for App Builder 201"
    echo
    echo "Choose your database option:"
    echo "1) SQLite (Recommended for development - no external dependencies)"
    echo "2) PostgreSQL (Production-like setup - requires Docker)"
    echo "3) Check current configuration"
    echo "4) Exit"
    echo
}

# Function to check current configuration
check_config() {
    print_header "Current Configuration"
    
    if [ -f .env ]; then
        print_info "Found .env file with the following database configuration:"
        echo
        grep -E "USE_POSTGRES|POSTGRES_" .env || echo "No database configuration found"
        echo
    else
        print_warning "No .env file found"
    fi
    
    if [ -f docker-compose.yml ]; then
        print_info "Docker Compose configuration:"
        echo "USE_POSTGRES setting: $(grep -A 5 -B 5 USE_POSTGRES docker-compose.yml | head -10)"
    fi
}

# Main script
main() {
    # Check if we're in the right directory
    if [ ! -f docker-compose.yml ]; then
        print_error "docker-compose.yml not found. Please run this script from the project root directory."
        exit 1
    fi
    
    # Check Docker
    check_docker
    
    while true; do
        show_menu
        read -p "Enter your choice (1-4): " choice
        
        case $choice in
            1)
                setup_sqlite
                break
                ;;
            2)
                if check_psql; then
                    setup_postgresql
                else
                    print_warning "PostgreSQL client not found, but we'll try anyway using Docker"
                    setup_postgresql
                fi
                break
                ;;
            3)
                check_config
                ;;
            4)
                print_info "Exiting..."
                exit 0
                ;;
            *)
                print_error "Invalid choice. Please enter 1, 2, 3, or 4."
                ;;
        esac
    done
}

# Run main function
main "$@"
