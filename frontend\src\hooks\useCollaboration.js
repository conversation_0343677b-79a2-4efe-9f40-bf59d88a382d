/**
 * React hook for real-time collaboration
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import collaborationService from '../services/CollaborationService';

/**
 * Hook for managing real-time collaboration
 * @param {string} documentId - Document/app ID to collaborate on
 * @param {Object} options - Configuration options
 * @returns {Object} Collaboration state and methods
 */
export const useCollaboration = (documentId, options = {}) => {
  const { user } = useAuth();
  const {
    autoConnect = true,
    onOperation = null,
    onCollaboratorJoined = null,
    onCollaboratorLeft = null,
    onCursorUpdate = null,
    onPresenceUpdate = null
  } = options;

  // State
  const [isConnected, setIsConnected] = useState(false);
  const [collaborators, setCollaborators] = useState([]);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState(null);
  const [operations, setOperations] = useState([]);
  const [cursors, setCursors] = useState(new Map());
  const [presence, setPresence] = useState(new Map());

  // Refs
  const isInitializedRef = useRef(false);
  const eventListenersRef = useRef(new Map());

  /**
   * Initialize collaboration
   */
  const initializeCollaboration = useCallback(async () => {
    if (!documentId || !user || isInitializedRef.current) {
      return;
    }

    setIsInitializing(true);
    setError(null);

    try {
      await collaborationService.initializeCollaboration(documentId, user);
      isInitializedRef.current = true;
      setIsConnected(true);
    } catch (err) {
      setError(err.message);
      console.error('Failed to initialize collaboration:', err);
    } finally {
      setIsInitializing(false);
    }
  }, [documentId, user]);

  /**
   * Disconnect from collaboration
   */
  const disconnect = useCallback(() => {
    if (isInitializedRef.current) {
      collaborationService.leaveDocument();
      collaborationService.disconnect();
      isInitializedRef.current = false;
      setIsConnected(false);
      setCollaborators([]);
      setOperations([]);
      setCursors(new Map());
      setPresence(new Map());
    }
  }, []);

  /**
   * Send an operation
   */
  const sendOperation = useCallback((operation) => {
    if (isConnected) {
      collaborationService.sendOperation(operation);
    }
  }, [isConnected]);

  /**
   * Send cursor update
   */
  const sendCursorUpdate = useCallback((cursor) => {
    if (isConnected) {
      collaborationService.sendCursorUpdate(cursor);
    }
  }, [isConnected]);

  /**
   * Send presence update
   */
  const sendPresenceUpdate = useCallback((presenceData) => {
    if (isConnected) {
      collaborationService.sendPresenceUpdate(presenceData);
    }
  }, [isConnected]);

  /**
   * Get current collaborators
   */
  const getCollaborators = useCallback(() => {
    return collaborationService.getCollaborators();
  }, []);

  /**
   * Set up event listeners
   */
  useEffect(() => {
    const listeners = eventListenersRef.current;

    // Connected event
    const handleConnected = () => {
      setIsConnected(true);
      setError(null);
    };

    // Disconnected event
    const handleDisconnected = () => {
      setIsConnected(false);
    };

    // Error event
    const handleError = (error) => {
      setError(error.message || 'Collaboration error');
    };

    // Operation event
    const handleOperation = (operation) => {
      setOperations(prev => [...prev, operation]);
      if (onOperation) {
        onOperation(operation);
      }
    };

    // Collaborator joined event
    const handleCollaboratorJoined = (collaborator) => {
      setCollaborators(prev => {
        const existing = prev.find(c => c.id === collaborator.id);
        if (existing) {
          return prev.map(c => c.id === collaborator.id ? collaborator : c);
        }
        return [...prev, collaborator];
      });
      
      if (onCollaboratorJoined) {
        onCollaboratorJoined(collaborator);
      }
    };

    // Collaborator left event
    const handleCollaboratorLeft = ({ userId, collaborator }) => {
      setCollaborators(prev => prev.filter(c => c.id !== userId));
      setCursors(prev => {
        const newCursors = new Map(prev);
        newCursors.delete(userId);
        return newCursors;
      });
      setPresence(prev => {
        const newPresence = new Map(prev);
        newPresence.delete(userId);
        return newPresence;
      });
      
      if (onCollaboratorLeft) {
        onCollaboratorLeft({ userId, collaborator });
      }
    };

    // Cursor update event
    const handleCursorUpdate = ({ cursor, userId }) => {
      setCursors(prev => {
        const newCursors = new Map(prev);
        newCursors.set(userId, cursor);
        return newCursors;
      });
      
      if (onCursorUpdate) {
        onCursorUpdate({ cursor, userId });
      }
    };

    // Presence update event
    const handlePresenceUpdate = ({ presence: presenceData, userId }) => {
      setPresence(prev => {
        const newPresence = new Map(prev);
        newPresence.set(userId, presenceData);
        return newPresence;
      });
      
      if (onPresenceUpdate) {
        onPresenceUpdate({ presence: presenceData, userId });
      }
    };

    // Document joined event
    const handleDocumentJoined = (data) => {
      setCollaborators(data.collaborators || []);
    };

    // Add event listeners
    collaborationService.addEventListener('connected', handleConnected);
    collaborationService.addEventListener('disconnected', handleDisconnected);
    collaborationService.addEventListener('error', handleError);
    collaborationService.addEventListener('operation', handleOperation);
    collaborationService.addEventListener('collaborator_joined', handleCollaboratorJoined);
    collaborationService.addEventListener('collaborator_left', handleCollaboratorLeft);
    collaborationService.addEventListener('cursor_update', handleCursorUpdate);
    collaborationService.addEventListener('presence_update', handlePresenceUpdate);
    collaborationService.addEventListener('document_joined', handleDocumentJoined);

    // Store listeners for cleanup
    listeners.set('connected', handleConnected);
    listeners.set('disconnected', handleDisconnected);
    listeners.set('error', handleError);
    listeners.set('operation', handleOperation);
    listeners.set('collaborator_joined', handleCollaboratorJoined);
    listeners.set('collaborator_left', handleCollaboratorLeft);
    listeners.set('cursor_update', handleCursorUpdate);
    listeners.set('presence_update', handlePresenceUpdate);
    listeners.set('document_joined', handleDocumentJoined);

    return () => {
      // Remove event listeners
      listeners.forEach((listener, event) => {
        collaborationService.removeEventListener(event, listener);
      });
      listeners.clear();
    };
  }, [onOperation, onCollaboratorJoined, onCollaboratorLeft, onCursorUpdate, onPresenceUpdate]);

  /**
   * Auto-connect when documentId and user are available
   */
  useEffect(() => {
    if (autoConnect && documentId && user && !isInitializedRef.current) {
      initializeCollaboration();
    }

    return () => {
      if (isInitializedRef.current) {
        disconnect();
      }
    };
  }, [autoConnect, documentId, user, initializeCollaboration, disconnect]);

  /**
   * Update collaborators list periodically
   */
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(() => {
        const currentCollaborators = getCollaborators();
        setCollaborators(currentCollaborators);
      }, 5000); // Update every 5 seconds

      return () => clearInterval(interval);
    }
  }, [isConnected, getCollaborators]);

  return {
    // State
    isConnected,
    collaborators,
    isInitializing,
    error,
    operations,
    cursors,
    presence,

    // Methods
    initializeCollaboration,
    disconnect,
    sendOperation,
    sendCursorUpdate,
    sendPresenceUpdate,
    getCollaborators,

    // Computed
    collaboratorCount: collaborators.length,
    hasCollaborators: collaborators.length > 0,
    isReady: isConnected && !isInitializing && !error
  };
};

export default useCollaboration;
