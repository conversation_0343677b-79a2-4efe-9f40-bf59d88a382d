# Environment Configuration Guide

This document provides a comprehensive guide to environment variable configuration for the App Builder project.

## Overview

The App Builder project uses environment variables to configure:
- Database connections
- Django settings
- Frontend API endpoints
- WebSocket connections
- Development/production modes

## Environment Variable Sources

### 1. Docker Compose Configuration (`docker-compose.yml`)

The primary source of environment variables for containerized deployment.

#### Backend Service Environment Variables

```yaml
environment:
  - DJANGO_SETTINGS_MODULE=app_builder_201.settings
  - USE_POSTGRES=true
  - POSTGRES_DB=myapp
  - POSTGRES_USER=myappuser
  - POSTGRES_PASSWORD=myapppassword
  - POSTGRES_HOST=db
  - POSTGRES_PORT=5432
  - DJANGO_SECRET_KEY=your-secret-key-for-development
  - DJANGO_DEBUG=True
  - DJANGO_ALLOWED_HOSTS=*
  - DJANGO_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
  - DJ<PERSON>G<PERSON>_CORS_ALLOW_CREDENTIALS=True
  - DJANGO_WEBSOCKET_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
  - DJANGO_LOG_LEVEL=DEBUG
```

#### Frontend Service Environment Variables

```yaml
environment:
  - NODE_ENV=development
  - REACT_APP_API_BASE_URL=http://localhost:8000
  - REACT_APP_API_URL=http://localhost:8000
  - REACT_APP_WS_URL=ws://localhost:8000
  - REACT_APP_WS_ENDPOINT=app_builder
  - REACT_APP_BACKEND_HOST=backend
  - REACT_APP_ENV=development
  - REACT_APP_DEBUG=true
  - REACT_APP_USE_REAL_API=true
  - API_TARGET=http://backend:8000
  - REACT_APP_WS_PROXY_TARGET=http://backend:8000
  - CHOKIDAR_USEPOLLING=true
  - WATCHPACK_POLLING=true
  - FAST_REFRESH=false
  - WDS_SOCKET_HOST=localhost
  - WDS_SOCKET_PATH=/sockjs-node
  - WDS_SOCKET_PORT=3000
```

#### Database Service Environment Variables

```yaml
environment:
  POSTGRES_DB: myapp
  POSTGRES_USER: myappuser
  POSTGRES_PASSWORD: myapppassword
```

### 2. Environment Files

#### Root `.env` File

Contains development defaults and API keys:

```env
# API Keys for AI features
OPENAI_API_KEY=sk-dummy-openai-key-for-development-only
AI_STUDIO_API_KEY=sk-dummy-ai-studio-key-for-development-only
STABILITY_API_KEY=sk-dummy-stability-key-for-development-only
ELEVENLABS_API_KEY=sk-dummy-elevenlabs-key-for-development-only

# Django Configuration
DJANGO_SECRET_KEY=your-secret-key-for-development
DJANGO_DEBUG=True
DJANGO_ALLOWED_HOSTS=*
DJANGO_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
DJANGO_CORS_ALLOW_CREDENTIALS=True

# Database Configuration
DJANGO_DB_ENGINE=django.db.backends.postgresql
DJANGO_DB_NAME=myapp
DJANGO_DB_USER=myappuser
DJANGO_DB_PASSWORD=myapppassword
DJANGO_DB_HOST=db
DJANGO_DB_PORT=5432

# WebSocket Configuration
DJANGO_WEBSOCKET_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_WS_ENDPOINT=app_builder
REACT_APP_BACKEND_HOST=backend
REACT_APP_ENV=development
REACT_APP_DEBUG=true
```

#### Frontend `.env` File (`frontend/.env`)

Frontend-specific configuration:

```env
# API Configuration
REACT_APP_API_URL=http://localhost:8000

# WebSocket Configuration
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_WSS_URL=wss://localhost:8000
REACT_APP_WS_HOST=localhost:8000
REACT_APP_WS_ENDPOINT=app_builder

# Backend Configuration
REACT_APP_BACKEND_HOST=backend

# Environment Configuration
REACT_APP_ENV=development
REACT_APP_DEBUG=true
REACT_APP_USE_REAL_API=true

# Development Server Configuration
WDS_SOCKET_HOST=localhost
WDS_SOCKET_PATH=/sockjs-node
WDS_SOCKET_PORT=3000
```

## Environment Variable Categories

### Database Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `USE_POSTGRES` | Enable PostgreSQL | `false` | No |
| `POSTGRES_DB` | Database name | `myapp` | Yes |
| `POSTGRES_USER` | Database user | `myappuser` | Yes |
| `POSTGRES_PASSWORD` | Database password | `myapppassword` | Yes |
| `POSTGRES_HOST` | Database host | `db` | Yes |
| `POSTGRES_PORT` | Database port | `5432` | No |

### Django Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DJANGO_SETTINGS_MODULE` | Django settings module | `app_builder_201.settings` | Yes |
| `DJANGO_SECRET_KEY` | Django secret key | Generated | Yes |
| `DJANGO_DEBUG` | Debug mode | `True` | No |
| `DJANGO_ALLOWED_HOSTS` | Allowed hosts | `*` | No |
| `DJANGO_LOG_LEVEL` | Logging level | `DEBUG` | No |

### CORS Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DJANGO_CORS_ALLOWED_ORIGINS` | Allowed CORS origins | `http://localhost:3000,http://frontend:3000` | Yes |
| `DJANGO_CORS_ALLOW_CREDENTIALS` | Allow credentials | `True` | No |

### WebSocket Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DJANGO_WEBSOCKET_ALLOWED_ORIGINS` | WebSocket allowed origins | `http://localhost:3000,http://frontend:3000` | Yes |
| `REACT_APP_WS_URL` | WebSocket URL | `ws://localhost:8000` | Yes |
| `REACT_APP_WS_ENDPOINT` | WebSocket endpoint | `app_builder` | Yes |
| `REACT_APP_WS_PROXY_TARGET` | WebSocket proxy target | `http://backend:8000` | Yes |

### Frontend Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `REACT_APP_API_URL` | API base URL | `http://localhost:8000` | Yes |
| `REACT_APP_BACKEND_HOST` | Backend host | `backend` | Yes |
| `REACT_APP_ENV` | Environment | `development` | No |
| `REACT_APP_DEBUG` | Debug mode | `true` | No |
| `REACT_APP_USE_REAL_API` | Use real API | `true` | No |
| `NODE_ENV` | Node environment | `development` | No |

## Port Configuration

| Service | Internal Port | External Port | Description |
|---------|---------------|---------------|-------------|
| Backend | 8000 | 8000 | Django/ASGI server |
| Frontend | 3000 | 3000 | React development server |
| Database | 5432 | 5432 | PostgreSQL database |

## Validation and Testing

### Environment Validation Script

Run the environment validation script to check configuration:

```bash
python scripts/validate-environment.py
```

### Environment Loading Test

Test that containers can access environment variables:

```bash
python scripts/test-environment-loading.py
```

## Troubleshooting

### Common Issues

1. **Environment variables not loading in containers**
   - Restart containers: `docker-compose down && docker-compose up`
   - Check Docker Compose environment sections

2. **Database connection failures**
   - Verify `USE_POSTGRES=true` is set
   - Check database credentials match between services
   - Ensure database container is healthy

3. **Frontend proxy errors**
   - Verify `API_TARGET` and `REACT_APP_WS_PROXY_TARGET` are set
   - Check backend container is accessible

4. **WebSocket connection issues**
   - Verify WebSocket URLs use correct protocol (`ws://` or `wss://`)
   - Check WebSocket allowed origins include frontend URLs

### Environment Variable Priority

1. Docker Compose `environment` section (highest priority)
2. `.env` files in service directories
3. Root `.env` file
4. Default values in code (lowest priority)

## Production Considerations

For production deployment:

1. **Security**
   - Use strong, unique secret keys
   - Set `DJANGO_DEBUG=False`
   - Use HTTPS URLs for all external connections
   - Restrict `DJANGO_ALLOWED_HOSTS` to specific domains

2. **Database**
   - Use managed database service
   - Set strong database passwords
   - Enable SSL connections

3. **CORS and WebSocket**
   - Restrict allowed origins to production domains
   - Use WSS (secure WebSocket) protocol

4. **Environment Files**
   - Never commit production `.env` files to version control
   - Use container orchestration secrets management
   - Rotate API keys and secrets regularly
