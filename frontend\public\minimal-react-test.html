<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal React Test - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid;
            background: rgba(255, 255, 255, 0.1);
        }
        .success { border-color: #28a745; }
        .error { border-color: #dc3545; }
        .warning { border-color: #ffc107; }
        .info { border-color: #17a2b8; }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        #test-root {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            min-height: 200px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }
        .results {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Minimal React Test</h1>
        <p>This test loads React directly from the main bundle and tests basic rendering.</p>
        
        <div id="status">
            <div class="status info">🔄 Initializing test...</div>
        </div>

        <div>
            <button onclick="loadReactBundle()">📦 Load React Bundle</button>
            <button onclick="testReactRendering()">⚛️ Test React Rendering</button>
            <button onclick="testMainApp()">🚀 Test Main App</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <h3>⚛️ React Test Area</h3>
        <div id="test-root">
            <p style="color: rgba(255,255,255,0.7); text-align: center; margin: 80px 0;">
                React components will render here...
            </p>
        </div>

        <h3>📋 Test Results</h3>
        <div class="results" id="results">
            Click "Load React Bundle" to start testing...
        </div>
    </div>

    <script>
        let reactLoaded = false;
        let reactDOMLoaded = false;

        function updateStatus(message, type = 'info') {
            document.getElementById('status').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
        }

        function log(message) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `[${timestamp}] ${message}<br>`;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function loadReactBundle() {
            updateStatus('📦 Loading React bundle...', 'info');
            log('Starting React bundle loading...');
            
            try {
                // First, get the main page HTML to find the actual bundle filename
                const htmlResponse = await fetch('/');
                if (!htmlResponse.ok) {
                    throw new Error(`Failed to fetch main page: ${htmlResponse.status}`);
                }
                
                const html = await htmlResponse.text();
                const bundleMatch = html.match(/static\/js\/main\.[a-f0-9]+\.js/);
                
                if (!bundleMatch) {
                    throw new Error('No main bundle found in HTML');
                }
                
                const bundlePath = `/${bundleMatch[0]}`;
                log(`Found bundle: ${bundlePath}`);
                
                // Load the bundle
                const script = document.createElement('script');
                script.src = bundlePath;
                script.onload = () => {
                    log('✅ Bundle loaded successfully');
                    
                    // Check if React is available
                    if (window.React) {
                        log(`✅ React is available! Version: ${window.React.version || 'unknown'}`);
                        reactLoaded = true;
                    } else {
                        log('❌ React not found in global scope');
                    }
                    
                    // Check if ReactDOM is available
                    if (window.ReactDOM) {
                        log('✅ ReactDOM is available!');
                        reactDOMLoaded = true;
                    } else {
                        log('❌ ReactDOM not found in global scope');
                    }
                    
                    if (reactLoaded && reactDOMLoaded) {
                        updateStatus('✅ React bundle loaded successfully!', 'success');
                    } else {
                        updateStatus('⚠️ Bundle loaded but React/ReactDOM not available', 'warning');
                    }
                };
                
                script.onerror = () => {
                    log('❌ Failed to load bundle');
                    updateStatus('❌ Failed to load React bundle', 'error');
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ Error loading bundle: ${error.message}`);
                updateStatus('❌ Error loading React bundle', 'error');
            }
        }

        function testReactRendering() {
            if (!reactLoaded || !reactDOMLoaded) {
                updateStatus('❌ React not loaded. Load bundle first.', 'error');
                log('❌ React or ReactDOM not available. Load bundle first.');
                return;
            }
            
            updateStatus('⚛️ Testing React rendering...', 'info');
            log('Starting React rendering test...');
            
            try {
                const testRoot = document.getElementById('test-root');
                
                // Create a simple React component
                const TestComponent = () => {
                    const [count, setCount] = window.React.useState(0);
                    
                    return window.React.createElement('div', {
                        style: {
                            textAlign: 'center',
                            padding: '20px',
                            background: 'rgba(255,255,255,0.1)',
                            borderRadius: '8px'
                        }
                    }, [
                        window.React.createElement('h3', { key: 'title' }, '🎉 React is Working!'),
                        window.React.createElement('p', { key: 'desc' }, 'This component was rendered by React'),
                        window.React.createElement('p', { key: 'count' }, `Count: ${count}`),
                        window.React.createElement('button', {
                            key: 'button',
                            onClick: () => setCount(count + 1),
                            style: {
                                background: '#007bff',
                                color: 'white',
                                border: 'none',
                                padding: '8px 16px',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }
                        }, 'Increment')
                    ]);
                };
                
                // Create React root and render
                const root = window.ReactDOM.createRoot(testRoot);
                root.render(window.React.createElement(TestComponent));
                
                log('✅ React component rendered successfully!');
                updateStatus('🎉 React rendering test passed!', 'success');
                
            } catch (error) {
                log(`❌ React rendering failed: ${error.message}`);
                updateStatus('❌ React rendering test failed', 'error');
            }
        }

        function testMainApp() {
            window.open('/', '_blank');
        }

        // Auto-start the test
        window.addEventListener('load', () => {
            setTimeout(loadReactBundle, 1000);
        });
    </script>
</body>
</html>
