import '@testing-library/jest-dom';
import { setupServer } from 'msw/node';
import { rest } from 'msw';

// Mock API service
import apiService from '../../services/apiService';

// Mock data
const mockApps = [
  {
    id: '1',
    name: 'Test App 1',
    description: 'A test application',
    app_data: JSON.stringify({
      components: [
        { id: '1', type: 'button', props: { text: 'Click me' } }
      ]
    }),
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_public: false
  },
  {
    id: '2',
    name: 'Test App 2',
    description: 'Another test application',
    app_data: JSON.stringify({
      components: [
        { id: '1', type: 'input', props: { placeholder: 'Enter text' } }
      ]
    }),
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_public: true
  }
];

const mockTemplates = [
  {
    id: '1',
    name: 'Basic Layout',
    description: 'A basic layout template',
    category: 'business',
    components: {
      header: { type: 'header', props: { title: 'Header' } },
      content: { type: 'content', props: {} }
    },
    is_public: true
  }
];

// Setup MSW server
const server = setupServer(
  // Apps endpoints
  rest.get('/api/apps/', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '1';
    const pageSize = 10;
    const start = (parseInt(page) - 1) * pageSize;
    const end = start + pageSize;
    
    return res(
      ctx.json({
        count: mockApps.length,
        next: null,
        previous: null,
        results: mockApps.slice(start, end)
      })
    );
  }),

  rest.get('/api/apps/:id/', (req, res, ctx) => {
    const { id } = req.params;
    const app = mockApps.find(app => app.id === id);
    
    if (!app) {
      return res(ctx.status(404), ctx.json({ detail: 'Not found' }));
    }
    
    return res(ctx.json(app));
  }),

  rest.post('/api/apps/', (req, res, ctx) => {
    const newApp = {
      id: String(mockApps.length + 1),
      ...req.body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    mockApps.push(newApp);
    return res(ctx.status(201), ctx.json(newApp));
  }),

  rest.patch('/api/apps/:id/', (req, res, ctx) => {
    const { id } = req.params;
    const appIndex = mockApps.findIndex(app => app.id === id);
    
    if (appIndex === -1) {
      return res(ctx.status(404), ctx.json({ detail: 'Not found' }));
    }
    
    mockApps[appIndex] = {
      ...mockApps[appIndex],
      ...req.body,
      updated_at: new Date().toISOString()
    };
    
    return res(ctx.json(mockApps[appIndex]));
  }),

  rest.delete('/api/apps/:id/', (req, res, ctx) => {
    const { id } = req.params;
    const appIndex = mockApps.findIndex(app => app.id === id);
    
    if (appIndex === -1) {
      return res(ctx.status(404), ctx.json({ detail: 'Not found' }));
    }
    
    mockApps.splice(appIndex, 1);
    return res(ctx.status(204));
  }),

  // Templates endpoints
  rest.get('/api/templates/', (req, res, ctx) => {
    const category = req.url.searchParams.get('category');
    let filteredTemplates = mockTemplates;
    
    if (category) {
      filteredTemplates = mockTemplates.filter(template => template.category === category);
    }
    
    return res(
      ctx.json({
        count: filteredTemplates.length,
        results: filteredTemplates
      })
    );
  }),

  rest.post('/api/templates/', (req, res, ctx) => {
    const newTemplate = {
      id: String(mockTemplates.length + 1),
      ...req.body,
      created_at: new Date().toISOString()
    };
    
    mockTemplates.push(newTemplate);
    return res(ctx.status(201), ctx.json(newTemplate));
  }),

  // AI endpoints
  rest.post('/api/ai/suggestions/', (req, res, ctx) => {
    const suggestions = [
      {
        id: '1',
        type: 'layout',
        title: 'Add Container',
        description: 'Wrap components in a container for better organization',
        confidence: 0.8
      },
      {
        id: '2',
        type: 'styling',
        title: 'Improve Spacing',
        description: 'Add consistent spacing between elements',
        confidence: 0.7
      }
    ];
    
    return res(ctx.json({ suggestions }));
  }),

  rest.post('/api/ai/apply-suggestion/', (req, res, ctx) => {
    return res(ctx.json({ success: true, message: 'Suggestion applied successfully' }));
  }),

  // Export endpoints
  rest.post('/api/export/', (req, res, ctx) => {
    const { format, app_id } = req.body;
    
    return res(
      ctx.json({
        download_url: `https://example.com/exports/${app_id}.${format}.zip`,
        file_size: 1024,
        format: format
      })
    );
  }),

  // Error simulation endpoints
  rest.get('/api/error-test/', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),

  rest.get('/api/timeout-test/', (req, res, ctx) => {
    return res(ctx.delay(10000), ctx.json({ message: 'This will timeout' }));
  })
);

// Mock the actual API service
jest.mock('../../services/apiService', () => ({
  // Apps
  getApps: jest.fn(),
  getApp: jest.fn(),
  createApp: jest.fn(),
  updateApp: jest.fn(),
  deleteApp: jest.fn(),
  
  // Templates
  getTemplates: jest.fn(),
  createTemplate: jest.fn(),
  
  // AI
  generateAISuggestions: jest.fn(),
  applyAISuggestion: jest.fn(),
  
  // Export
  exportApp: jest.fn(),
  
  // Utility
  setAuthToken: jest.fn(),
  clearAuthToken: jest.fn(),
}));

describe('API Integration Tests', () => {
  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
    jest.clearAllMocks();
  });

  afterAll(() => {
    server.close();
  });

  describe('Apps API', () => {
    test('fetches apps list successfully', async () => {
      // Mock the API service implementation
      apiService.getApps.mockImplementation(async (params = {}) => {
        const response = await fetch('/api/apps/?' + new URLSearchParams(params));
        return response.json();
      });

      const result = await apiService.getApps();
      
      expect(apiService.getApps).toHaveBeenCalled();
      expect(result.results).toHaveLength(2);
      expect(result.results[0].name).toBe('Test App 1');
    });

    test('fetches single app successfully', async () => {
      apiService.getApp.mockImplementation(async (id) => {
        const response = await fetch(`/api/apps/${id}/`);
        if (!response.ok) throw new Error('App not found');
        return response.json();
      });

      const result = await apiService.getApp('1');
      
      expect(apiService.getApp).toHaveBeenCalledWith('1');
      expect(result.name).toBe('Test App 1');
    });

    test('handles app not found error', async () => {
      apiService.getApp.mockImplementation(async (id) => {
        const response = await fetch(`/api/apps/${id}/`);
        if (!response.ok) throw new Error('App not found');
        return response.json();
      });

      await expect(apiService.getApp('999')).rejects.toThrow('App not found');
    });

    test('creates new app successfully', async () => {
      const newAppData = {
        name: 'New Test App',
        description: 'A new test application',
        app_data: JSON.stringify({ components: [] }),
        is_public: false
      };

      apiService.createApp.mockImplementation(async (data) => {
        const response = await fetch('/api/apps/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        return response.json();
      });

      const result = await apiService.createApp(newAppData);
      
      expect(apiService.createApp).toHaveBeenCalledWith(newAppData);
      expect(result.name).toBe('New Test App');
      expect(result.id).toBeDefined();
    });

    test('updates app successfully', async () => {
      const updateData = {
        name: 'Updated App Name',
        description: 'Updated description'
      };

      apiService.updateApp.mockImplementation(async (id, data) => {
        const response = await fetch(`/api/apps/${id}/`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        return response.json();
      });

      const result = await apiService.updateApp('1', updateData);
      
      expect(apiService.updateApp).toHaveBeenCalledWith('1', updateData);
      expect(result.name).toBe('Updated App Name');
    });

    test('deletes app successfully', async () => {
      apiService.deleteApp.mockImplementation(async (id) => {
        const response = await fetch(`/api/apps/${id}/`, {
          method: 'DELETE'
        });
        return response.ok;
      });

      const result = await apiService.deleteApp('1');
      
      expect(apiService.deleteApp).toHaveBeenCalledWith('1');
      expect(result).toBe(true);
    });
  });

  describe('Templates API', () => {
    test('fetches templates successfully', async () => {
      apiService.getTemplates.mockImplementation(async (params = {}) => {
        const response = await fetch('/api/templates/?' + new URLSearchParams(params));
        return response.json();
      });

      const result = await apiService.getTemplates();
      
      expect(apiService.getTemplates).toHaveBeenCalled();
      expect(result.results).toHaveLength(1);
      expect(result.results[0].name).toBe('Basic Layout');
    });

    test('filters templates by category', async () => {
      apiService.getTemplates.mockImplementation(async (params = {}) => {
        const response = await fetch('/api/templates/?' + new URLSearchParams(params));
        return response.json();
      });

      const result = await apiService.getTemplates({ category: 'business' });
      
      expect(apiService.getTemplates).toHaveBeenCalledWith({ category: 'business' });
      expect(result.results).toHaveLength(1);
      expect(result.results[0].category).toBe('business');
    });

    test('creates template successfully', async () => {
      const templateData = {
        name: 'New Template',
        description: 'A new template',
        category: 'portfolio',
        components: { header: { type: 'header' } },
        is_public: true
      };

      apiService.createTemplate.mockImplementation(async (data) => {
        const response = await fetch('/api/templates/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        return response.json();
      });

      const result = await apiService.createTemplate(templateData);
      
      expect(apiService.createTemplate).toHaveBeenCalledWith(templateData);
      expect(result.name).toBe('New Template');
    });
  });

  describe('AI API', () => {
    test('generates AI suggestions successfully', async () => {
      const requestData = {
        components: [
          { id: '1', type: 'button', props: { text: 'Button' } }
        ],
        context: 'layout_improvement'
      };

      apiService.generateAISuggestions.mockImplementation(async (data) => {
        const response = await fetch('/api/ai/suggestions/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        return response.json();
      });

      const result = await apiService.generateAISuggestions(requestData);
      
      expect(apiService.generateAISuggestions).toHaveBeenCalledWith(requestData);
      expect(result.suggestions).toHaveLength(2);
      expect(result.suggestions[0].type).toBe('layout');
    });

    test('applies AI suggestion successfully', async () => {
      const suggestionData = {
        suggestion_id: '1',
        app_id: '1'
      };

      apiService.applyAISuggestion.mockImplementation(async (data) => {
        const response = await fetch('/api/ai/apply-suggestion/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        return response.json();
      });

      const result = await apiService.applyAISuggestion(suggestionData);
      
      expect(apiService.applyAISuggestion).toHaveBeenCalledWith(suggestionData);
      expect(result.success).toBe(true);
    });
  });

  describe('Export API', () => {
    test('exports app successfully', async () => {
      const exportData = {
        app_id: '1',
        format: 'react'
      };

      apiService.exportApp.mockImplementation(async (data) => {
        const response = await fetch('/api/export/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        return response.json();
      });

      const result = await apiService.exportApp(exportData);
      
      expect(apiService.exportApp).toHaveBeenCalledWith(exportData);
      expect(result.download_url).toContain('react.zip');
      expect(result.format).toBe('react');
    });
  });

  describe('Error Handling', () => {
    test('handles server errors gracefully', async () => {
      apiService.getApps.mockImplementation(async () => {
        const response = await fetch('/api/error-test/');
        if (!response.ok) throw new Error('Server error');
        return response.json();
      });

      await expect(apiService.getApps()).rejects.toThrow('Server error');
    });

    test('handles network timeouts', async () => {
      apiService.getApps.mockImplementation(async () => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 1000);
        
        try {
          const response = await fetch('/api/timeout-test/', {
            signal: controller.signal
          });
          clearTimeout(timeoutId);
          return response.json();
        } catch (error) {
          if (error.name === 'AbortError') {
            throw new Error('Request timeout');
          }
          throw error;
        }
      });

      await expect(apiService.getApps()).rejects.toThrow('Request timeout');
    });
  });

  describe('Authentication', () => {
    test('sets auth token correctly', () => {
      const token = 'test-auth-token';
      apiService.setAuthToken(token);
      
      expect(apiService.setAuthToken).toHaveBeenCalledWith(token);
    });

    test('clears auth token correctly', () => {
      apiService.clearAuthToken();
      
      expect(apiService.clearAuthToken).toHaveBeenCalled();
    });
  });

  describe('Pagination', () => {
    test('handles paginated responses', async () => {
      apiService.getApps.mockImplementation(async (params = {}) => {
        const response = await fetch('/api/apps/?' + new URLSearchParams(params));
        return response.json();
      });

      const result = await apiService.getApps({ page: 1 });
      
      expect(result.count).toBeDefined();
      expect(result.results).toBeDefined();
      expect(Array.isArray(result.results)).toBe(true);
    });
  });
});
