import React, { useState, useEffect } from 'react';
import ServiceDiagnostics from '../components/ServiceDiagnostics';
import WebSocketConnectionTester from '../components/WebSocketConnectionTester';
import { initializeServices, checkServiceAccessibility } from '../services/serviceInitializer';

/**
 * Service Status Page
 *
 * This page displays the status of all services and allows users to diagnose and fix any issues.
 */
const ServiceStatusPage = () => {
  const [servicesInitialized, setServicesInitialized] = useState(false);
  const [serviceStatus, setServiceStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [showWebSocketTester, setShowWebSocketTester] = useState(false);

  // Initialize services on component mount
  useEffect(() => {
    async function initialize() {
      try {
        setLoading(true);

        // Initialize services
        await initializeServices();
        setServicesInitialized(true);

        // Check service accessibility
        const status = await checkServiceAccessibility();
        setServiceStatus(status);

        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    }

    initialize();
  }, []);

  // Render status indicator
  const renderStatusIndicator = (isAccessible) => {
    const style = {
      display: 'inline-block',
      width: '12px',
      height: '12px',
      borderRadius: '50%',
      backgroundColor: isAccessible ? '#10b981' : '#ef4444',
      marginRight: '8px'
    };

    return <span style={style}></span>;
  };

  // Render service status
  const renderServiceStatus = () => {
    if (!serviceStatus) return null;

    const allServicesAccessible =
      serviceStatus.websocket &&
      serviceStatus.api &&
      serviceStatus.serviceWorker;

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>Service Status</h3>

        <div style={{
          padding: '16px',
          backgroundColor: allServicesAccessible ? '#ecfdf5' : '#fef2f2',
          borderRadius: '8px',
          marginBottom: '16px'
        }}>
          <p style={{
            fontWeight: 'bold',
            color: allServicesAccessible ? '#10b981' : '#ef4444'
          }}>
            {allServicesAccessible
              ? 'All services are accessible'
              : 'Some services are not accessible'}
          </p>
        </div>

        <ul style={{ listStyle: 'none', padding: 0 }}>
          <li style={{
            padding: '12px',
            marginBottom: '8px',
            backgroundColor: '#f3f4f6',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center'
          }}>
            {renderStatusIndicator(serviceStatus.websocket)}
            <span>
              <strong>WebSocket:</strong> {serviceStatus.websocket ? 'Accessible' : 'Not accessible'}
            </span>
          </li>

          <li style={{
            padding: '12px',
            marginBottom: '8px',
            backgroundColor: '#f3f4f6',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center'
          }}>
            {renderStatusIndicator(serviceStatus.api)}
            <span>
              <strong>API:</strong> {serviceStatus.api ? 'Accessible' : 'Not accessible'}
            </span>
          </li>

          <li style={{
            padding: '12px',
            marginBottom: '8px',
            backgroundColor: '#f3f4f6',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center'
          }}>
            {renderStatusIndicator(serviceStatus.serviceWorker)}
            <span>
              <strong>Service Worker:</strong> {serviceStatus.serviceWorker ? 'Registered' : 'Not registered'}
            </span>
          </li>
        </ul>
      </div>
    );
  };

  // Render troubleshooting tips
  const renderTroubleshootingTips = () => {
    if (!serviceStatus) return null;

    const needsTroubleshooting =
      !serviceStatus.websocket ||
      !serviceStatus.api ||
      !serviceStatus.serviceWorker;

    if (!needsTroubleshooting) return null;

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>Troubleshooting Tips</h3>

        <ul style={{ paddingLeft: '20px' }}>
          {!serviceStatus.websocket && (
            <li style={{ marginBottom: '8px' }}>
              <strong>WebSocket issues:</strong>
              <ul>
                <li>Check if the backend server is running</li>
                <li>Verify that the WebSocket server is properly configured</li>
                <li>Check for any network issues or firewalls blocking WebSocket connections</li>
                <li>Try refreshing the page</li>
              </ul>
            </li>
          )}

          {!serviceStatus.api && (
            <li style={{ marginBottom: '8px' }}>
              <strong>API issues:</strong>
              <ul>
                <li>Check if the backend server is running</li>
                <li>Verify that the API endpoints are properly configured</li>
                <li>Check for any network issues</li>
                <li>Try refreshing the page</li>
              </ul>
            </li>
          )}

          {!serviceStatus.serviceWorker && (
            <li style={{ marginBottom: '8px' }}>
              <strong>Service Worker issues:</strong>
              <ul>
                <li>Check if your browser supports Service Workers</li>
                <li>Try clearing your browser cache and refreshing the page</li>
                <li>Check for any errors in the browser console</li>
              </ul>
            </li>
          )}
        </ul>
      </div>
    );
  };

  return (
    <div style={{
      maxWidth: '800px',
      margin: '0 auto',
      padding: '20px'
    }}>
      <h1>Service Status</h1>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <p>Loading service status...</p>
        </div>
      ) : error ? (
        <div style={{
          padding: '16px',
          backgroundColor: '#fee2e2',
          color: '#b91c1c',
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <p><strong>Error:</strong> {error}</p>
          <p>There was an error initializing services. Please try refreshing the page.</p>
        </div>
      ) : (
        <div>
          <div style={{
            padding: '16px',
            backgroundColor: '#f0f9ff',
            borderRadius: '8px',
            marginBottom: '20px'
          }}>
            <p>
              <strong>Services Initialized:</strong> {servicesInitialized ? 'Yes' : 'No'}
            </p>
          </div>

          {renderServiceStatus()}

          {renderTroubleshootingTips()}

          <div style={{ marginBottom: '20px', display: 'flex', gap: '10px' }}>
            <button
              onClick={() => setShowDiagnostics(!showDiagnostics)}
              style={{
                padding: '8px 16px',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {showDiagnostics ? 'Hide Diagnostics' : 'Show Diagnostics'}
            </button>

            <button
              onClick={() => setShowWebSocketTester(!showWebSocketTester)}
              style={{
                padding: '8px 16px',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {showWebSocketTester ? 'Hide WebSocket Tester' : 'Show WebSocket Tester'}
            </button>
          </div>

          {showDiagnostics && (
            <div style={{ marginBottom: '20px' }}>
              <ServiceDiagnostics />
            </div>
          )}

          {showWebSocketTester && (
            <div style={{ marginBottom: '20px' }}>
              <WebSocketConnectionTester />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ServiceStatusPage;
