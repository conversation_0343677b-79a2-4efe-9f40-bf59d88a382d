/**
 * Accessible Component Wrapper
 * 
 * A comprehensive wrapper component that adds accessibility features
 * to any component, ensuring WCAG 2.1 AA compliance.
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { theme } from '../../design-system';
import a11yUtils from '../../utils/accessibility';

const AccessibleWrapper = styled.div`
  /* Ensure focus is visible */
  &:focus-visible {
    outline: 2px solid ${theme.colors.primary.main};
    outline-offset: 2px;
    border-radius: ${theme.borderRadius.sm};
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    border: 1px solid;
    
    &:focus-visible {
      outline: 3px solid;
      outline-offset: 2px;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    transition: none !important;
    animation: none !important;
  }
  
  /* Ensure minimum touch target size */
  ${props => props.interactive && `
    min-width: ${theme.accessibility.minTouchTarget.width};
    min-height: ${theme.accessibility.minTouchTarget.height};
    display: inline-flex;
    align-items: center;
    justify-content: center;
  `}
`;

const SkipLink = styled.a`
  position: absolute;
  top: -40px;
  left: 6px;
  background: ${theme.colors.primary.main};
  color: ${theme.colors.primary.contrastText};
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  border-radius: ${theme.borderRadius.md};
  text-decoration: none;
  font-weight: ${theme.typography.fontWeight.medium};
  z-index: ${theme.zIndex.skipLink};
  transition: ${theme.transitions.default};
  
  &:focus {
    top: 6px;
  }
`;

const LiveRegion = styled.div`
  ${theme.accessibility.srOnly};
`;

const AccessibleComponent = ({
  children,
  // Accessibility props
  role,
  ariaLabel,
  ariaLabelledBy,
  ariaDescribedBy,
  ariaExpanded,
  ariaSelected,
  ariaChecked,
  ariaDisabled,
  ariaHidden,
  ariaLive,
  ariaAtomic,
  
  // Keyboard navigation props
  tabIndex,
  onKeyDown,
  onKeyUp,
  onKeyPress,
  
  // Focus management props
  autoFocus,
  onFocus,
  onBlur,
  focusTrap,
  restoreFocus,
  
  // Interactive props
  interactive = false,
  disabled = false,
  onClick,
  onDoubleClick,
  
  // Drag and drop props
  draggable = false,
  onDragStart,
  onDragEnd,
  onDrop,
  onDragOver,
  onDragEnter,
  onDragLeave,
  
  // Skip link props
  skipLink,
  skipTarget,
  
  // Live region props
  announcements = [],
  
  // Validation props
  invalid = false,
  errorMessage,
  
  // Loading state props
  loading = false,
  loadingText = 'Loading...',
  
  // Component identification
  componentId,
  componentType,
  
  // Style props
  className,
  style,
  
  // Testing props
  testId,
  enableA11yTesting = process.env.NODE_ENV === 'development',
  
  ...otherProps
}) => {
  const wrapperRef = useRef(null);
  const [focusTrapInstance, setFocusTrapInstance] = useState(null);
  const [restoreFocusFunction, setRestoreFocusFunction] = useState(null);
  const [a11yViolations, setA11yViolations] = useState([]);

  // Generate unique IDs for ARIA relationships
  const elementId = componentId || a11yUtils.aria.generateId('accessible-component');
  const errorId = `${elementId}-error`;
  const descriptionId = `${elementId}-description`;

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e) => {
    // Handle escape key for modals/dropdowns
    if (e.key === a11yUtils.keyboard.KEYS.ESCAPE && focusTrap) {
      e.preventDefault();
      if (focusTrapInstance) {
        focusTrapInstance.deactivate();
      }
    }
    
    // Handle activation keys for interactive elements
    if (interactive && (e.key === a11yUtils.keyboard.KEYS.ENTER || e.key === a11yUtils.keyboard.KEYS.SPACE)) {
      if (!disabled && onClick) {
        e.preventDefault();
        onClick(e);
      }
    }
    
    // Call custom keyDown handler
    if (onKeyDown) {
      onKeyDown(e);
    }
  }, [interactive, disabled, onClick, onKeyDown, focusTrap, focusTrapInstance]);

  // Handle focus events
  const handleFocus = useCallback((e) => {
    // Announce focus change for screen readers
    if (ariaLabel || ariaLabelledBy) {
      const label = ariaLabel || document.getElementById(ariaLabelledBy)?.textContent;
      if (label) {
        a11yUtils.aria.announce(`Focused on ${label}`, 'polite');
      }
    }
    
    if (onFocus) {
      onFocus(e);
    }
  }, [ariaLabel, ariaLabelledBy, onFocus]);

  // Handle blur events
  const handleBlur = useCallback((e) => {
    if (onBlur) {
      onBlur(e);
    }
  }, [onBlur]);

  // Handle drag and drop with accessibility
  const handleDragStart = useCallback((e) => {
    if (draggable && !disabled) {
      // Set accessible drag data
      const dragData = {
        id: elementId,
        type: componentType,
        label: ariaLabel || 'Component'
      };
      
      e.dataTransfer.setData('application/json', JSON.stringify(dragData));
      e.dataTransfer.effectAllowed = 'move';
      
      // Announce drag start
      a11yUtils.aria.announce(`Started dragging ${dragData.label}`, 'assertive');
      
      if (onDragStart) {
        onDragStart(e);
      }
    }
  }, [draggable, disabled, elementId, componentType, ariaLabel, onDragStart]);

  const handleDragEnd = useCallback((e) => {
    if (draggable) {
      // Announce drag end
      a11yUtils.aria.announce('Drag operation completed', 'polite');
      
      if (onDragEnd) {
        onDragEnd(e);
      }
    }
  }, [draggable, onDragEnd]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    
    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
      
      // Announce successful drop
      a11yUtils.aria.announce(`Dropped ${dragData.label}`, 'assertive');
      
      if (onDrop) {
        onDrop(e, dragData);
      }
    } catch (error) {
      console.warn('Invalid drag data:', error);
    }
  }, [onDrop]);

  // Set up focus trap
  useEffect(() => {
    if (focusTrap && wrapperRef.current) {
      const trap = a11yUtils.focus.createFocusTrap(wrapperRef);
      setFocusTrapInstance(trap);
      trap.activate();
      
      return () => {
        trap.deactivate();
      };
    }
  }, [focusTrap]);

  // Set up focus restoration
  useEffect(() => {
    if (restoreFocus) {
      const restoreFunction = a11yUtils.focus.createFocusRestore();
      setRestoreFocusFunction(() => restoreFunction);
      
      return restoreFunction;
    }
  }, [restoreFocus]);

  // Auto focus on mount
  useEffect(() => {
    if (autoFocus && wrapperRef.current) {
      wrapperRef.current.focus();
    }
  }, [autoFocus]);

  // Accessibility testing in development
  useEffect(() => {
    if (enableA11yTesting && wrapperRef.current) {
      const audit = a11yUtils.testing.auditElement(wrapperRef.current);
      if (audit.issues.length > 0) {
        setA11yViolations(audit.issues);
        a11yUtils.monitoring.logViolations(audit.issues.map(issue => ({
          description: issue,
          element: wrapperRef.current
        })));
      }
    }
  }, [enableA11yTesting, children]);

  // Handle live region announcements
  useEffect(() => {
    announcements.forEach(announcement => {
      if (announcement.message) {
        a11yUtils.aria.announce(announcement.message, announcement.priority || 'polite');
      }
    });
  }, [announcements]);

  // Build ARIA attributes
  const ariaAttributes = {
    role,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledBy,
    'aria-describedby': [
      ariaDescribedBy,
      errorMessage ? errorId : null,
      invalid ? descriptionId : null
    ].filter(Boolean).join(' ') || undefined,
    'aria-expanded': ariaExpanded,
    'aria-selected': ariaSelected,
    'aria-checked': ariaChecked,
    'aria-disabled': ariaDisabled || disabled,
    'aria-hidden': ariaHidden,
    'aria-live': ariaLive,
    'aria-atomic': ariaAtomic,
    'aria-invalid': invalid,
    'aria-busy': loading,
  };

  // Build drag and drop attributes
  const dragDropAttributes = draggable ? {
    draggable: !disabled,
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd,
    onDrop: handleDrop,
    onDragOver: onDragOver,
    onDragEnter: onDragEnter,
    onDragLeave: onDragLeave,
    ...a11yUtils.dragDrop.createDragDropAttributes({ id: elementId }, false, !!onDrop)
  } : {};

  // Build event handlers
  const eventHandlers = {
    onKeyDown: handleKeyDown,
    onKeyUp,
    onKeyPress,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onClick: interactive && !disabled ? onClick : undefined,
    onDoubleClick: interactive && !disabled ? onDoubleClick : undefined,
    ...dragDropAttributes,
  };

  return (
    <>
      {/* Skip link */}
      {skipLink && skipTarget && (
        <SkipLink href={`#${skipTarget}`}>
          {skipLink}
        </SkipLink>
      )}
      
      {/* Main accessible wrapper */}
      <AccessibleWrapper
        ref={wrapperRef}
        id={elementId}
        className={className}
        style={style}
        interactive={interactive}
        tabIndex={interactive ? (tabIndex !== undefined ? tabIndex : 0) : tabIndex}
        data-testid={testId}
        {...ariaAttributes}
        {...eventHandlers}
        {...otherProps}
      >
        {children}
        
        {/* Error message for screen readers */}
        {errorMessage && (
          <div id={errorId} className="sr-only" role="alert">
            {errorMessage}
          </div>
        )}
        
        {/* Loading state for screen readers */}
        {loading && (
          <div className="sr-only" aria-live="polite">
            {loadingText}
          </div>
        )}
        
        {/* Drag instructions for screen readers */}
        {draggable && (
          <div {...a11yUtils.dragDrop.createDragInstructions(elementId)} />
        )}
      </AccessibleWrapper>
      
      {/* Live region for announcements */}
      {announcements.length > 0 && (
        <LiveRegion aria-live="polite" aria-atomic="true">
          {announcements.map((announcement, index) => (
            <div key={index}>{announcement.message}</div>
          ))}
        </LiveRegion>
      )}
      
      {/* Development accessibility warnings */}
      {process.env.NODE_ENV === 'development' && a11yViolations.length > 0 && (
        <div style={{ 
          position: 'fixed', 
          bottom: 0, 
          right: 0, 
          background: 'red', 
          color: 'white', 
          padding: '8px',
          fontSize: '12px',
          zIndex: 9999 
        }}>
          A11y Issues: {a11yViolations.length}
        </div>
      )}
    </>
  );
};

AccessibleComponent.propTypes = {
  children: PropTypes.node.isRequired,
  
  // Accessibility props
  role: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaLabelledBy: PropTypes.string,
  ariaDescribedBy: PropTypes.string,
  ariaExpanded: PropTypes.bool,
  ariaSelected: PropTypes.bool,
  ariaChecked: PropTypes.bool,
  ariaDisabled: PropTypes.bool,
  ariaHidden: PropTypes.bool,
  ariaLive: PropTypes.oneOf(['polite', 'assertive', 'off']),
  ariaAtomic: PropTypes.bool,
  
  // Keyboard navigation props
  tabIndex: PropTypes.number,
  onKeyDown: PropTypes.func,
  onKeyUp: PropTypes.func,
  onKeyPress: PropTypes.func,
  
  // Focus management props
  autoFocus: PropTypes.bool,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  focusTrap: PropTypes.bool,
  restoreFocus: PropTypes.bool,
  
  // Interactive props
  interactive: PropTypes.bool,
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
  onDoubleClick: PropTypes.func,
  
  // Drag and drop props
  draggable: PropTypes.bool,
  onDragStart: PropTypes.func,
  onDragEnd: PropTypes.func,
  onDrop: PropTypes.func,
  onDragOver: PropTypes.func,
  onDragEnter: PropTypes.func,
  onDragLeave: PropTypes.func,
  
  // Skip link props
  skipLink: PropTypes.string,
  skipTarget: PropTypes.string,
  
  // Live region props
  announcements: PropTypes.arrayOf(PropTypes.shape({
    message: PropTypes.string.isRequired,
    priority: PropTypes.oneOf(['polite', 'assertive'])
  })),
  
  // Validation props
  invalid: PropTypes.bool,
  errorMessage: PropTypes.string,
  
  // Loading state props
  loading: PropTypes.bool,
  loadingText: PropTypes.string,
  
  // Component identification
  componentId: PropTypes.string,
  componentType: PropTypes.string,
  
  // Style props
  className: PropTypes.string,
  style: PropTypes.object,
  
  // Testing props
  testId: PropTypes.string,
  enableA11yTesting: PropTypes.bool,
};

export default AccessibleComponent;
