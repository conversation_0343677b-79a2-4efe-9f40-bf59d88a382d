#!/usr/bin/env python3
"""
Development Environment Setup Script

This script helps set up the development environment for the App Builder project,
particularly handling API key configuration for AI features.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header():
    print("=" * 60)
    print("🚀 App Builder Development Environment Setup")
    print("=" * 60)
    print()

def check_api_keys():
    """Check current API key configuration"""
    print("📋 Checking API Key Configuration...")
    print()
    
    # Check environment variables
    env_keys = {
        'OPENAI_API_KEY': os.environ.get('OPENAI_API_KEY'),
        'AI_STUDIO_API_KEY': os.environ.get('AI_STUDIO_API_KEY'),
        'STABILITY_API_KEY': os.environ.get('STABILITY_API_KEY'),
        'ELEVENLABS_API_KEY': os.environ.get('ELEVENLABS_API_KEY')
    }
    
    # Check .env file
    env_file = Path('.env')
    env_file_keys = {}
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    if key in env_keys:
                        env_file_keys[key] = value
    
    print("Environment Variables:")
    for key, value in env_keys.items():
        status = "✅ Set" if value and not value.startswith('sk-dummy') else "❌ Missing/Dummy"
        print(f"  {key}: {status}")
    
    print("\n.env File:")
    for key in env_keys.keys():
        value = env_file_keys.get(key, 'Not found')
        status = "✅ Set" if value and value != 'Not found' and not value.startswith('sk-dummy') else "❌ Missing/Dummy"
        print(f"  {key}: {status}")
    
    return env_keys, env_file_keys

def setup_development_mode():
    """Set up development mode with AI features disabled"""
    print("\n🔧 Setting up Development Mode (AI features disabled)...")
    
    # Update .env file to use development-friendly settings
    env_content = """# Development Environment Configuration
# AI features will be disabled with these dummy keys

# API Keys (dummy values for development)
OPENAI_API_KEY=dev-mode-disabled
AI_STUDIO_API_KEY=dev-mode-disabled
STABILITY_API_KEY=dev-mode-disabled
ELEVENLABS_API_KEY=dev-mode-disabled

# Django Configuration
DJANGO_SECRET_KEY=your-secret-key-for-development
DJANGO_DEBUG=True
DJANGO_ALLOWED_HOSTS=*
DJANGO_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
DJANGO_CORS_ALLOW_CREDENTIALS=True

# Database Configuration
DJANGO_DB_ENGINE=django.db.backends.postgresql
DJANGO_DB_NAME=myapp
DJANGO_DB_USER=myappuser
DJANGO_DB_PASSWORD=myapppassword
DJANGO_DB_HOST=db
DJANGO_DB_PORT=5432

# Cache Configuration
DJANGO_CACHE_BACKEND=django.core.cache.backends.redis.RedisCache
DJANGO_CACHE_LOCATION=redis://redis:6379/1

# WebSocket Configuration
DJANGO_WEBSOCKET_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
DJANGO_LOG_LEVEL=DEBUG

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_WS_ENDPOINT=app_builder
REACT_APP_BACKEND_HOST=backend
REACT_APP_ENV=development
REACT_APP_DEBUG=true
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ Updated .env file for development mode")
    print("   AI features will gracefully degrade to fallback functionality")

def setup_production_keys():
    """Guide user through setting up production API keys"""
    print("\n🔑 Setting up Production API Keys...")
    print("You'll need to obtain API keys from the following services:")
    print()
    print("1. OpenAI API Key:")
    print("   - Visit: https://platform.openai.com/api-keys")
    print("   - Create a new API key")
    print("   - Copy the key (starts with 'sk-')")
    print()
    print("2. AI Studio API Key (optional):")
    print("   - Visit your AI Studio provider")
    print("   - Generate an API key")
    print()
    print("3. Stability AI API Key (optional):")
    print("   - Visit: https://platform.stability.ai/")
    print("   - Create an API key")
    print()
    print("4. ElevenLabs API Key (optional):")
    print("   - Visit: https://elevenlabs.io/")
    print("   - Generate an API key")
    print()
    
    # Interactive key input
    keys = {}
    for key_name in ['OPENAI_API_KEY', 'AI_STUDIO_API_KEY', 'STABILITY_API_KEY', 'ELEVENLABS_API_KEY']:
        value = input(f"Enter {key_name} (or press Enter to skip): ").strip()
        if value:
            keys[key_name] = value
        else:
            keys[key_name] = 'dev-mode-disabled'
    
    # Update .env file
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            content = f.read()
    else:
        content = ""
    
    # Update or add keys
    for key, value in keys.items():
        if f"{key}=" in content:
            # Replace existing
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith(f"{key}="):
                    lines[i] = f"{key}={value}"
                    break
            content = '\n'.join(lines)
        else:
            # Add new
            content += f"\n{key}={value}"
    
    with open('.env', 'w') as f:
        f.write(content)
    
    print("✅ Updated .env file with your API keys")

def test_setup():
    """Test the current setup"""
    print("\n🧪 Testing Setup...")
    
    try:
        # Test API key validation
        sys.path.append('backend')
        from core.config import get_api_config
        
        config = get_api_config()
        print("✅ API configuration loaded successfully")
        
        # Test if keys are valid (not dummy)
        real_keys = []
        if config.openai_api_key and not config.openai_api_key.startswith(('sk-dummy', 'dev-mode')):
            real_keys.append('OpenAI')
        if config.ai_studio_api_key and not config.ai_studio_api_key.startswith(('sk-dummy', 'dev-mode')):
            real_keys.append('AI Studio')
        if config.stability_api_key and not config.stability_api_key.startswith(('sk-dummy', 'dev-mode')):
            real_keys.append('Stability')
        if config.elevenlabs_api_key and not config.elevenlabs_api_key.startswith(('sk-dummy', 'dev-mode')):
            real_keys.append('ElevenLabs')
        
        if real_keys:
            print(f"✅ Real API keys detected for: {', '.join(real_keys)}")
        else:
            print("ℹ️  Development mode - AI features will use fallback functionality")
        
    except Exception as e:
        print(f"❌ Error testing setup: {e}")

def main():
    print_header()
    
    # Check current status
    env_keys, env_file_keys = check_api_keys()
    
    print("\n" + "=" * 60)
    print("Choose an option:")
    print("1. Set up Development Mode (AI features disabled)")
    print("2. Set up Production API Keys")
    print("3. Test current setup")
    print("4. Exit")
    print("=" * 60)
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == '1':
        setup_development_mode()
    elif choice == '2':
        setup_production_keys()
    elif choice == '3':
        test_setup()
    elif choice == '4':
        print("👋 Goodbye!")
        return
    else:
        print("❌ Invalid choice")
        return
    
    # Test the setup after changes
    if choice in ['1', '2']:
        test_setup()
    
    print("\n" + "=" * 60)
    print("🎉 Setup complete!")
    print("You can now run the application with:")
    print("  docker-compose up --build")
    print("=" * 60)

if __name__ == "__main__":
    main()
