import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Card,
  Input,
  Button,
  Select,
  Divider,
  Row,
  Col,
  Typography,
  Tabs,
  Space,
  Tooltip,
  message
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  CloseOutlined,
  CheckOutlined,
  BgColorsOutlined,
  EyeOutlined
} from '@ant-design/icons';

// Import Redux actions
import { addTheme, updateTheme, removeTheme, setActiveTheme } from '../redux/actions';

const { Title, Text } = Typography;
const { Option } = Select;

// Color palettes for quick theme creation
const colorPalettes = [
  {
    name: 'Blue',
    primary: '#2563EB',
    secondary: '#10B981',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: 'Purple',
    primary: '#8B5CF6',
    secondary: '#EC4899',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: '<PERSON>',
    primary: '#10B981',
    secondary: '#3B82F6',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: 'Red',
    primary: '#EF4444',
    secondary: '#F59E0B',
    background: '#FFFFFF',
    text: '#111827'
  },
  {
    name: 'Dark',
    primary: '#3B82F6',
    secondary: '#10B981',
    background: '#111827',
    text: '#F9FAFB'
  }
];

// Font families
const fontFamilies = [
  'Inter, sans-serif',
  'Arial, sans-serif',
  'Helvetica, sans-serif',
  'Georgia, serif',
  'Times New Roman, serif',
  'Courier New, monospace',
  'Verdana, sans-serif',
  'Roboto, sans-serif',
  'Open Sans, sans-serif',
  'Lato, sans-serif'
];

/**
 * ThemeManagerComponent
 *
 * A comprehensive theme manager that allows users to create, edit, and manage themes.
 * Themes can be customized with different colors, fonts, and other properties.
 */
const ThemeManagerComponent = () => {
  const dispatch = useDispatch();
  // Fix: Access themes from the correct path in the Redux store
  // The themeReducer is mounted at state.themes, so we need to access state.themes.themes
  const themes = useSelector(state => (state.themes && state.themes.themes) || []);
  const activeTheme = useSelector(state => state.themes && state.themes.activeTheme);

  // Form state
  const [themeName, setThemeName] = useState('');
  const [primaryColor, setPrimaryColor] = useState('#2563EB');
  const [secondaryColor, setSecondaryColor] = useState('#10B981');
  const [backgroundColor, setBackgroundColor] = useState('#FFFFFF');
  const [textColor, setTextColor] = useState('#111827');
  const [fontFamily, setFontFamily] = useState('Inter, sans-serif');

  // UI state
  const [selectedTheme, setSelectedTheme] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('1');

  // Default theme
  const defaultTheme = {
    id: 'default',
    name: 'Default Theme',
    primaryColor: '#2563EB',
    secondaryColor: '#10B981',
    backgroundColor: '#FFFFFF',
    textColor: '#111827',
    fontFamily: 'Inter, sans-serif'
  };

  // Add default theme if it doesn't exist - only run once on mount
  useEffect(() => {
    // Ensure themes is an array before checking its length
    if (!Array.isArray(themes) || themes.length === 0) {
      dispatch(addTheme(defaultTheme));
    }

    if (!activeTheme) {
      dispatch(setActiveTheme('default'));
    }
    // Only run this effect once on component mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!themeName.trim()) {
      newErrors.name = 'Theme name is required';
    }

    // Validate color formats
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (!colorRegex.test(primaryColor)) {
      newErrors.primaryColor = 'Invalid color format';
    }
    if (!colorRegex.test(secondaryColor)) {
      newErrors.secondaryColor = 'Invalid color format';
    }
    if (!colorRegex.test(backgroundColor)) {
      newErrors.backgroundColor = 'Invalid color format';
    }
    if (!colorRegex.test(textColor)) {
      newErrors.textColor = 'Invalid color format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Reset form to default values
  const resetForm = () => {
    setThemeName('');
    setPrimaryColor('#2563EB');
    setSecondaryColor('#10B981');
    setBackgroundColor('#FFFFFF');
    setTextColor('#111827');
    setFontFamily('Inter, sans-serif');
    setSelectedTheme(null);
    setEditMode(false);
    setErrors({});
  };

  // Handle adding a new theme
  const handleAddTheme = () => {
    if (!validateForm()) return;

    const newTheme = {
      id: Date.now().toString(),
      name: themeName.trim(),
      primaryColor,
      secondaryColor,
      backgroundColor,
      textColor,
      fontFamily,
      createdAt: new Date().toISOString()
    };

    dispatch(addTheme(newTheme));
    message.success(`Theme "${themeName}" created successfully`);
    resetForm();
  };

  // Handle updating an existing theme
  const handleUpdateTheme = () => {
    if (!selectedTheme || !validateForm()) return;

    const updatedTheme = {
      ...selectedTheme,
      name: themeName.trim(),
      primaryColor,
      secondaryColor,
      backgroundColor,
      textColor,
      fontFamily,
      updatedAt: new Date().toISOString()
    };

    dispatch(updateTheme(updatedTheme));
    message.success(`Theme "${themeName}" updated successfully`);
    resetForm();
  };

  // Handle removing a theme
  const handleRemoveTheme = (id) => {
    // Don't allow removing the default theme
    if (id === 'default') {
      message.error('Cannot delete the default theme');
      return;
    }

    // Ensure themes is an array before using find
    const themeToRemove = Array.isArray(themes) ? themes.find(theme => theme.id === id) : null;
    if (!themeToRemove) return;

    dispatch(removeTheme(id));
    message.success(`Theme "${themeToRemove.name}" deleted successfully`);

    // If the removed theme was selected, reset the form
    if (selectedTheme && selectedTheme.id === id) {
      resetForm();
    }

    // If the removed theme was active, set the default theme as active
    if (activeTheme === id) {
      dispatch(setActiveTheme('default'));
    }
  };

  // Handle selecting a theme for editing
  const handleSelectTheme = (theme) => {
    setSelectedTheme(theme);
    setThemeName(theme.name);
    setPrimaryColor(theme.primaryColor);
    setSecondaryColor(theme.secondaryColor);
    setBackgroundColor(theme.backgroundColor);
    setTextColor(theme.textColor);
    setFontFamily(theme.fontFamily);
    setEditMode(true);
    setErrors({});
    setActiveTab('1'); // Switch to the editor tab
  };

  // Handle canceling edit mode
  const handleCancelEdit = () => {
    resetForm();
  };

  // Handle duplicating a theme
  const handleDuplicateTheme = (theme) => {
    const duplicatedTheme = {
      ...theme,
      id: Date.now().toString(),
      name: `${theme.name} (Copy)`,
      createdAt: new Date().toISOString()
    };

    dispatch(addTheme(duplicatedTheme));
    message.success(`Theme "${theme.name}" duplicated successfully`);
  };

  // Handle setting the active theme
  const handleSetActiveTheme = (themeId) => {
    dispatch(setActiveTheme(themeId));
    message.success('Theme activated successfully');
  };

  // Handle applying a color palette
  const handleApplyPalette = (palette) => {
    setPrimaryColor(palette.primary);
    setSecondaryColor(palette.secondary);
    setBackgroundColor(palette.background);
    setTextColor(palette.text);
  };

  // Get the active theme object
  const getActiveThemeObject = () => {
    // Safely handle themes array and avoid spread operator on potentially non-iterable values
    const safeThemes = Array.isArray(themes) ? themes : [];
    return safeThemes.concat([defaultTheme]).find(theme => theme.id === activeTheme) || defaultTheme;
  };

  return (
    <div style={{ padding: '16px' }}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: '1',
            label: 'Theme Editor',
            children: (
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Card title={editMode ? "Edit Theme" : "Create Theme"}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Input
                        placeholder="Theme Name"
                        value={themeName}
                        onChange={(e) => setThemeName(e.target.value)}
                        status={errors.name ? 'error' : ''}
                        prefix={<BgColorsOutlined />}
                        addonAfter={
                          editMode && (
                            <Button
                              type="text"
                              icon={<CloseOutlined />}
                              onClick={handleCancelEdit}
                              size="small"
                            />
                          )
                        }
                      />
                      {errors.name && <Text type="danger">{errors.name}</Text>}

                      <Divider orientation="left">Color Palettes</Divider>
                      <Space wrap>
                        {colorPalettes.map((palette, index) => (
                          <Tooltip key={index} title={palette.name}>
                            <Button
                              onClick={() => handleApplyPalette(palette)}
                              style={{
                                background: palette.background,
                                borderColor: palette.primary,
                              }}
                            >
                              <div style={{ display: 'flex', gap: '4px' }}>
                                <div style={{ width: 16, height: 16, background: palette.primary, borderRadius: '50%' }} />
                                <div style={{ width: 16, height: 16, background: palette.secondary, borderRadius: '50%' }} />
                              </div>
                            </Button>
                          </Tooltip>
                        ))}
                      </Space>

                      <Row gutter={16}>
                        <Col span={12}>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Text>Primary Color</Text>
                            <Input
                              type="color"
                              value={primaryColor}
                              onChange={(e) => setPrimaryColor(e.target.value)}
                              style={{ width: '100%' }}
                              status={errors.primaryColor ? 'error' : ''}
                              addonAfter={
                                <Input
                                  value={primaryColor}
                                  onChange={(e) => setPrimaryColor(e.target.value)}
                                  style={{ width: 80 }}
                                />
                              }
                            />
                            {errors.primaryColor && <Text type="danger">{errors.primaryColor}</Text>}
                          </Space>
                        </Col>
                        <Col span={12}>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Text>Secondary Color</Text>
                            <Input
                              type="color"
                              value={secondaryColor}
                              onChange={(e) => setSecondaryColor(e.target.value)}
                              style={{ width: '100%' }}
                              status={errors.secondaryColor ? 'error' : ''}
                              addonAfter={
                                <Input
                                  value={secondaryColor}
                                  onChange={(e) => setSecondaryColor(e.target.value)}
                                  style={{ width: 80 }}
                                />
                              }
                            />
                            {errors.secondaryColor && <Text type="danger">{errors.secondaryColor}</Text>}
                          </Space>
                        </Col>
                      </Row>

                      <Row gutter={16} style={{ marginTop: 16 }}>
                        <Col span={12}>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Text>Background Color</Text>
                            <Input
                              type="color"
                              value={backgroundColor}
                              onChange={(e) => setBackgroundColor(e.target.value)}
                              style={{ width: '100%' }}
                              status={errors.backgroundColor ? 'error' : ''}
                              addonAfter={
                                <Input
                                  value={backgroundColor}
                                  onChange={(e) => setBackgroundColor(e.target.value)}
                                  style={{ width: 80 }}
                                />
                              }
                            />
                            {errors.backgroundColor && <Text type="danger">{errors.backgroundColor}</Text>}
                          </Space>
                        </Col>
                        <Col span={12}>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Text>Text Color</Text>
                            <Input
                              type="color"
                              value={textColor}
                              onChange={(e) => setTextColor(e.target.value)}
                              style={{ width: '100%' }}
                              status={errors.textColor ? 'error' : ''}
                              addonAfter={
                                <Input
                                  value={textColor}
                                  onChange={(e) => setTextColor(e.target.value)}
                                  style={{ width: 80 }}
                                />
                              }
                            />
                            {errors.textColor && <Text type="danger">{errors.textColor}</Text>}
                          </Space>
                        </Col>
                      </Row>

                      <Space direction="vertical" style={{ width: '100%', marginTop: 16 }}>
                        <Text>Font Family</Text>
                        <Select
                          value={fontFamily}
                          onChange={setFontFamily}
                          style={{ width: '100%' }}
                        >
                          {fontFamilies.map(font => (
                            <Option key={font} value={font}>
                              <span style={{ fontFamily: font }}>{font.split(',')[0]}</span>
                            </Option>
                          ))}
                        </Select>
                        <div
                          style={{
                            padding: '8px',
                            border: '1px solid #d9d9d9',
                            borderRadius: '2px',
                            fontFamily: fontFamily,
                            marginTop: '8px'
                          }}
                        >
                          The quick brown fox jumps over the lazy dog.
                        </div>
                      </Space>

                      <div style={{ marginTop: 16 }}>
                        {editMode ? (
                          <Button
                            type="primary"
                            icon={<SaveOutlined />}
                            onClick={handleUpdateTheme}
                            disabled={selectedTheme?.id === 'default'}
                          >
                            Update Theme
                          </Button>
                        ) : (
                          <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleAddTheme}
                          >
                            Create Theme
                          </Button>
                        )}
                      </div>
                    </Space>
                  </Card>
                </Col>
                <Col xs={24} md={12}>
                  <Card title="Theme Preview">
                    <div
                      style={{
                        backgroundColor,
                        color: textColor,
                        fontFamily,
                        padding: '16px',
                        borderRadius: '4px',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <Title level={4} style={{ color: textColor }}>
                        Sample Heading
                      </Title>
                      <Text style={{ color: textColor }}>
                        This is a sample paragraph showing how text will look with the selected theme.
                        The quick brown fox jumps over the lazy dog.
                      </Text>
                      <div style={{ marginTop: '16px', display: 'flex', gap: '8px' }}>
                        <Button
                          style={{
                            backgroundColor: primaryColor,
                            borderColor: primaryColor,
                            color: '#fff'
                          }}
                        >
                          Primary Button
                        </Button>
                        <Button
                          style={{
                            backgroundColor: secondaryColor,
                            borderColor: secondaryColor,
                            color: '#fff'
                          }}
                        >
                          Secondary Button
                        </Button>
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>
            ),
          },
          {
            key: '2',
            label: 'Theme Gallery',
            children: (
              <Card title="Available Themes">
                <Row gutter={[16, 16]}>
                  {(() => {
                    // Safely handle themes array without using spread operator
                    const safeThemes = Array.isArray(themes) ? themes.filter(theme => theme.id !== 'default') : [];
                    return [defaultTheme].concat(safeThemes).map(theme => (
                      <Col xs={24} sm={12} md={8} lg={6} key={theme.id}>
                        <Card
                          hoverable
                          style={{
                            borderColor: activeTheme === theme.id ? primaryColor : undefined,
                            transition: 'all 0.3s ease'
                          }}
                          actions={[
                            <Tooltip title="Preview">
                              <EyeOutlined key="preview" onClick={() => handleSelectTheme(theme)} />
                            </Tooltip>,
                            <Tooltip title="Duplicate">
                              <CopyOutlined key="duplicate" onClick={() => handleDuplicateTheme(theme)} />
                            </Tooltip>,
                            theme.id !== 'default' && (
                              <Tooltip title="Edit">
                                <EditOutlined key="edit" onClick={() => handleSelectTheme(theme)} />
                              </Tooltip>
                            ),
                            theme.id !== 'default' && (
                              <Tooltip title="Delete">
                                <DeleteOutlined
                                  key="delete"
                                  onClick={() => handleRemoveTheme(theme.id)}
                                  style={{ color: '#ff4d4f' }}
                                />
                              </Tooltip>
                            )
                          ].filter(Boolean)}
                          cover={
                            <div
                              style={{
                                height: '80px',
                                backgroundColor: theme.backgroundColor,
                                padding: '8px',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'space-between'
                              }}
                            >
                              <div
                                style={{
                                  color: theme.textColor,
                                  fontFamily: theme.fontFamily,
                                  fontSize: '14px'
                                }}
                              >
                                Sample Text
                              </div>
                              <div style={{ display: 'flex', gap: '4px' }}>
                                <div
                                  style={{
                                    width: '20px',
                                    height: '20px',
                                    backgroundColor: theme.primaryColor,
                                    borderRadius: '50%'
                                  }}
                                />
                                <div
                                  style={{
                                    width: '20px',
                                    height: '20px',
                                    backgroundColor: theme.secondaryColor,
                                    borderRadius: '50%'
                                  }}
                                />
                              </div>
                            </div>
                          }
                        >
                          <Card.Meta
                            title={theme.name}
                            description={
                              <Space direction="vertical" style={{ width: '100%' }}>
                                <Text type="secondary">{theme.fontFamily.split(',')[0]}</Text>
                                {activeTheme === theme.id ? (
                                  <Button type="text" icon={<CheckOutlined />} style={{ color: '#52c41a' }}>
                                    Active
                                  </Button>
                                ) : (
                                  <Button type="primary" size="small" onClick={() => handleSetActiveTheme(theme.id)}>
                                    Activate
                                  </Button>
                                )}
                              </Space>
                            }
                          />
                        </Card>
                      </Col>
                    ))
                  })()}
                </Row>
              </Card>
            ),
          },
        ]}
      />
    </div>
  );
};

export default ThemeManagerComponent;
