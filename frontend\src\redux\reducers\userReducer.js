import { ActionTypes } from '../actions';

/**
 * Initial state for user reducer
 */
const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null
};

/**
 * User reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
const userReducer = (state = initialState, action) => {
  switch (action.type) {
    case ActionTypes.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        loading: false,
        error: null
      };
    
    case ActionTypes.CLEAR_USER:
      // Clear user data from localStorage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        loading: false,
        error: null
      };
    
    case ActionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };
    
    case ActionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };
    
    case ActionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };
    
    default:
      return state;
  }
};

export default userReducer;
