import React, { useState, useEffect, createContext, useContext } from 'react';

// Create notification context
const NotificationContext = createContext({
  showNotification: () => {},
  hideNotification: () => {}
});

/**
 * Notification Provider Component
 * 
 * This component provides notification functionality to the application.
 */
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  
  // Show notification
  const showNotification = ({ type = 'info', title, message, duration = 5000 }) => {
    // Generate unique ID
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Add notification
    setNotifications(prev => [
      ...prev,
      { id, type, title, message, duration }
    ]);
    
    // Return notification ID
    return id;
  };
  
  // Hide notification
  const hideNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };
  
  // Add showNotification to window for global access
  useEffect(() => {
    window.showNotification = showNotification;
    
    return () => {
      delete window.showNotification;
    };
  }, []);
  
  return (
    <NotificationContext.Provider value={{ showNotification, hideNotification }}>
      {children}
      
      {/* Notification container */}
      {notifications.length > 0 && (
        <div className="notification-container">
          {notifications.map(notification => (
            <Notification
              key={notification.id}
              notification={notification}
              onClose={() => hideNotification(notification.id)}
            />
          ))}
        </div>
      )}
      
      <style jsx>{`
        .notification-container {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: var(--z-index-tooltip);
          display: flex;
          flex-direction: column;
          gap: 10px;
          max-width: 400px;
        }
      `}</style>
    </NotificationContext.Provider>
  );
};

/**
 * Notification Component
 * 
 * This component displays a single notification.
 */
const Notification = ({ notification, onClose }) => {
  const { id, type, title, message, duration } = notification;
  
  // Auto-close notification after duration
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);
  
  // Get notification color based on type
  const getColor = () => {
    switch (type) {
      case 'success':
        return 'var(--color-success)';
      case 'error':
        return 'var(--color-error)';
      case 'warning':
        return 'var(--color-warning)';
      case 'info':
      default:
        return 'var(--color-info)';
    }
  };
  
  // Get notification icon based on type
  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✗';
      case 'warning':
        return '⚠';
      case 'info':
      default:
        return 'ℹ';
    }
  };
  
  return (
    <div className="notification" data-id={id}>
      <div className="notification-icon" style={{ backgroundColor: getColor() }}>
        {getIcon()}
      </div>
      <div className="notification-content">
        {title && <div className="notification-title">{title}</div>}
        {message && <div className="notification-message">{message}</div>}
      </div>
      <button className="notification-close" onClick={onClose}>
        ×
      </button>
      
      <style jsx>{`
        .notification {
          display: flex;
          align-items: flex-start;
          background-color: var(--color-surface);
          border-radius: var(--border-radius-md);
          box-shadow: var(--shadow-md);
          overflow: hidden;
          animation: slide-in 0.3s ease-out;
        }
        
        @keyframes slide-in {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        .notification-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 100%;
          color: white;
          font-size: 20px;
          padding: 10px 0;
        }
        
        .notification-content {
          flex: 1;
          padding: 10px;
        }
        
        .notification-title {
          font-weight: var(--font-weight-medium);
          margin-bottom: 5px;
        }
        
        .notification-message {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .notification-close {
          background: none;
          border: none;
          color: var(--color-textSecondary);
          font-size: 20px;
          cursor: pointer;
          padding: 5px 10px;
        }
        
        .notification-close:hover {
          color: var(--color-text);
        }
      `}</style>
    </div>
  );
};

/**
 * Use Notification Hook
 * 
 * This hook provides access to the notification context.
 */
export const useNotification = () => {
  const context = useContext(NotificationContext);
  
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  
  return context;
};

export default Notification;
