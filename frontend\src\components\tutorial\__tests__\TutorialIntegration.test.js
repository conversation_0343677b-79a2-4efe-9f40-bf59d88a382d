/**
 * Tutorial System Integration Tests
 * 
 * Tests integration with existing App Builder components and
 * end-to-end tutorial workflows.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// Mock Redux store
const mockStore = configureStore({
  reducer: {
    ui: (state = { currentView: 'components', previewMode: false }, action) => state,
    components: (state = [], action) => state,
    websocket: (state = { connected: false }, action) => state
  }
});

// Tutorial System
import { TutorialProvider } from '../TutorialManager';
import TutorialOverlay from '../TutorialOverlay';
import ContextualHelp from '../ContextualHelp';
import TutorialTrigger from '../TutorialTrigger';
import TutorialRegistration from '../TutorialRegistration';

// Mock App Builder components
const MockComponentPalette = ({ onAddComponent }) => (
  <div data-help-context="component-palette" data-testid="component-palette">
    <h3>Component Palette</h3>
    <button onClick={() => onAddComponent('button')}>Add Button</button>
    <button onClick={() => onAddComponent('text')}>Add Text</button>
  </div>
);

const MockPreviewArea = ({ components, onSelectComponent }) => (
  <div data-help-context="preview-area" data-testid="preview-area">
    <h3>Preview Area</h3>
    {components.length === 0 ? (
      <div>No components added yet</div>
    ) : (
      components.map(comp => (
        <div 
          key={comp.id} 
          data-component-id={comp.id}
          onClick={() => onSelectComponent(comp)}
        >
          {comp.type} component
        </div>
      ))
    )}
  </div>
);

const MockPropertyEditor = ({ component }) => (
  <div data-help-context="property-editor" data-testid="property-editor">
    <h3>Property Editor</h3>
    {component ? (
      <div>
        <p>Editing: {component.type}</p>
        <input placeholder="Component name" />
        <input placeholder="Component color" />
      </div>
    ) : (
      <div>No component selected</div>
    )}
  </div>
);

// Mock App Builder Interface
const MockAppBuilderInterface = () => {
  const [components, setComponents] = React.useState([]);
  const [selectedComponent, setSelectedComponent] = React.useState(null);

  const handleAddComponent = (type) => {
    const newComponent = {
      id: `${type}-${Date.now()}`,
      type,
      props: {}
    };
    setComponents([...components, newComponent]);
  };

  const handleSelectComponent = (component) => {
    setSelectedComponent(component);
  };

  return (
    <div data-testid="app-builder-interface">
      <div style={{ display: 'flex' }}>
        <MockComponentPalette onAddComponent={handleAddComponent} />
        <MockPreviewArea 
          components={components} 
          onSelectComponent={handleSelectComponent}
        />
        {selectedComponent && (
          <MockPropertyEditor component={selectedComponent} />
        )}
      </div>
    </div>
  );
};

const renderWithProviders = (component) => {
  return render(
    <Provider store={mockStore}>
      <TutorialProvider userId="test-user">
        {component}
        <TutorialRegistration />
        <TutorialOverlay />
        <ContextualHelp />
        <TutorialTrigger />
      </TutorialProvider>
    </Provider>
  );
};

describe('Tutorial System Integration', () => {
  beforeEach(() => {
    // Clear any existing tutorials
    localStorage.clear();
  });

  describe('App Builder Integration', () => {
    test('should integrate with App Builder interface without conflicts', async () => {
      renderWithProviders(<MockAppBuilderInterface />);

      // Check that all components render
      expect(screen.getByTestId('app-builder-interface')).toBeInTheDocument();
      expect(screen.getByTestId('component-palette')).toBeInTheDocument();
      expect(screen.getByTestId('preview-area')).toBeInTheDocument();

      // Check that tutorial trigger is present
      expect(document.querySelector('.ant-float-btn')).toBeInTheDocument();
    });

    test('should provide contextual help for App Builder components', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<MockAppBuilderInterface />);

      // Hover over component palette
      const componentPalette = screen.getByTestId('component-palette');
      await user.hover(componentPalette);

      // Should trigger contextual help (after delay)
      await waitFor(() => {
        // Check for any help-related elements
        const helpElements = document.querySelectorAll('[data-tutorial-help], .ant-tooltip, [aria-live]');
        expect(helpElements.length).toBeGreaterThan(0);
      }, { timeout: 3000 });
    });

    test('should guide user through adding first component', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<MockAppBuilderInterface />);

      // Wait for tutorial system to initialize
      await waitFor(() => {
        expect(document.querySelector('.ant-float-btn')).toBeInTheDocument();
      });

      // Click tutorial trigger to open launcher
      const tutorialTrigger = document.querySelector('.ant-float-btn');
      await user.click(tutorialTrigger);

      // Should show tutorial options
      await waitFor(() => {
        const tutorialElements = document.querySelectorAll('[data-tutorial], .ant-modal, .ant-drawer');
        expect(tutorialElements.length).toBeGreaterThan(0);
      });
    });

    test('should handle component addition workflow', async () => {
      renderWithProviders(<MockAppBuilderInterface />);

      // Add a component
      const addButton = screen.getByText('Add Button');
      fireEvent.click(addButton);

      // Should show component in preview area
      await waitFor(() => {
        expect(screen.getByText('button component')).toBeInTheDocument();
      });

      // Click on component to select it
      const component = screen.getByText('button component');
      fireEvent.click(component);

      // Should show property editor
      await waitFor(() => {
        expect(screen.getByText('Editing: button')).toBeInTheDocument();
      });
    });
  });

  describe('Tutorial Workflow Integration', () => {
    test('should complete getting started tutorial workflow', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<MockAppBuilderInterface />);

      // Wait for tutorials to be registered
      await waitFor(() => {
        expect(document.querySelector('.ant-float-btn')).toBeInTheDocument();
      });

      // Start getting started tutorial (simulate)
      // In a real scenario, this would be triggered through the UI
      const mockStartTutorial = jest.fn();
      
      // Simulate tutorial steps
      const tutorialSteps = [
        'Welcome to App Builder',
        'Component Palette',
        'Preview Area',
        'Property Editor',
        'Try adding a component'
      ];

      // Each step should be navigable
      tutorialSteps.forEach((step, index) => {
        // Simulate step progression
        expect(step).toBeTruthy();
      });
    });

    test('should handle tutorial interruption and resumption', async () => {
      renderWithProviders(<MockAppBuilderInterface />);

      // Simulate starting a tutorial
      const tutorialData = {
        id: 'test_tutorial',
        currentStep: 2,
        status: 'in_progress'
      };

      // Store tutorial state
      localStorage.setItem('app_builder_tutorial_progress', JSON.stringify({
        'test-user': {
          'test_tutorial': tutorialData
        }
      }));

      // Should be able to resume tutorial
      expect(localStorage.getItem('app_builder_tutorial_progress')).toBeTruthy();
    });
  });

  describe('Accessibility Integration', () => {
    test('should maintain accessibility when tutorial is active', async () => {
      renderWithProviders(<MockAppBuilderInterface />);

      // Check initial accessibility
      const componentPalette = screen.getByTestId('component-palette');
      expect(componentPalette).toBeInTheDocument();

      // Simulate tutorial activation
      // Tutorial should not break existing accessibility
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeVisible();
      });
    });

    test('should provide keyboard navigation for tutorials', async () => {
      renderWithProviders(<MockAppBuilderInterface />);

      // Test keyboard navigation
      const keyboardEvents = [
        { key: 'Tab', description: 'Tab navigation' },
        { key: 'Enter', description: 'Enter activation' },
        { key: 'Escape', description: 'Escape to close' }
      ];

      keyboardEvents.forEach(({ key, description }) => {
        fireEvent.keyDown(document, { key });
        // Should handle keyboard events gracefully
        expect(document.body).toBeInTheDocument();
      });
    });

    test('should announce tutorial state changes', async () => {
      // Mock screen reader announcements
      const announcements = [];
      const mockAnnounce = jest.fn((text) => announcements.push(text));
      
      // Mock aria-live regions
      const mockAriaLive = document.createElement('div');
      mockAriaLive.setAttribute('aria-live', 'polite');
      document.body.appendChild(mockAriaLive);

      renderWithProviders(<MockAppBuilderInterface />);

      // Simulate tutorial state changes
      const stateChanges = [
        'Tutorial started',
        'Step 1 of 5',
        'Step 2 of 5',
        'Tutorial completed'
      ];

      stateChanges.forEach(change => {
        mockAriaLive.textContent = change;
        expect(mockAriaLive.textContent).toBe(change);
      });

      document.body.removeChild(mockAriaLive);
    });
  });

  describe('Performance Integration', () => {
    test('should not impact App Builder performance significantly', async () => {
      const startTime = performance.now();
      
      renderWithProviders(<MockAppBuilderInterface />);

      // Wait for everything to render
      await waitFor(() => {
        expect(screen.getByTestId('app-builder-interface')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (less than 1 second)
      expect(renderTime).toBeLessThan(1000);
    });

    test('should handle multiple tutorial components efficiently', async () => {
      const MultipleComponentsTest = () => (
        <div>
          {Array.from({ length: 50 }, (_, i) => (
            <div key={i} data-help-context="test-component">
              Component {i}
            </div>
          ))}
        </div>
      );

      const startTime = performance.now();
      
      renderWithProviders(<MultipleComponentsTest />);

      await waitFor(() => {
        expect(screen.getByText('Component 0')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should handle multiple components efficiently
      expect(renderTime).toBeLessThan(2000);
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle component errors gracefully', async () => {
      const ErrorComponent = () => {
        throw new Error('Test error');
      };

      const ErrorBoundary = ({ children }) => {
        const [hasError, setHasError] = React.useState(false);

        React.useEffect(() => {
          const handleError = () => setHasError(true);
          window.addEventListener('error', handleError);
          return () => window.removeEventListener('error', handleError);
        }, []);

        if (hasError) {
          return <div>Something went wrong</div>;
        }

        return children;
      };

      // Should not crash the entire tutorial system
      expect(() => {
        render(
          <ErrorBoundary>
            <TutorialProvider>
              <ErrorComponent />
              <TutorialTrigger />
            </TutorialProvider>
          </ErrorBoundary>
        );
      }).not.toThrow();
    });

    test('should handle missing DOM elements gracefully', async () => {
      renderWithProviders(<MockAppBuilderInterface />);

      // Simulate tutorial trying to target non-existent element
      const nonExistentSelector = '[data-non-existent]';
      
      // Should not throw error when element is not found
      expect(() => {
        document.querySelector(nonExistentSelector);
      }).not.toThrow();
    });
  });

  describe('Theme Integration', () => {
    test('should work with different themes', async () => {
      const ThemedComponent = ({ theme }) => (
        <div data-theme={theme} className={`theme-${theme}`}>
          <MockAppBuilderInterface />
        </div>
      );

      const themes = ['light', 'dark', 'high-contrast'];

      themes.forEach(theme => {
        const { unmount } = renderWithProviders(<ThemedComponent theme={theme} />);
        
        // Should render without errors for each theme
        expect(screen.getByTestId('app-builder-interface')).toBeInTheDocument();
        
        unmount();
      });
    });
  });
});
