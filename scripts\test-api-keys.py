#!/usr/bin/env python
"""
API Key Verification Script
This script tests if all required API keys are properly configured and accessible.
"""

import os
import sys
import requests
from colorama import init, Fore, Style

# Initialize colorama for cross-platform colored terminal output
init()

def print_result(success, message):
    """Print a formatted result message"""
    if success:
        print(f"{Fore.GREEN}✅ {message}{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ {message}{Style.RESET_ALL}")

def check_api_key(key_name, validation_func=None):
    """Check if an API key is set and optionally validate it"""
    key_value = os.environ.get(key_name)
    
    if not key_value:
        print_result(False, f"{key_name} is not set in environment")
        return False
    
    # Mask the key for display
    masked_key = key_value[:4] + '*' * (len(key_value) - 8) + key_value[-4:] if len(key_value) > 8 else '****'
    print(f"{Fore.BLUE}🔑 Found {key_name}: {masked_key}{Style.RESET_ALL}")
    
    # If a validation function is provided, use it to test the key
    if validation_func:
        try:
            is_valid = validation_func(key_value)
            print_result(is_valid, f"{key_name} validation {'successful' if is_valid else 'failed'}")
            return is_valid
        except Exception as e:
            print_result(False, f"{key_name} validation error: {str(e)}")
            return False
    
    return True

def validate_openai_key(key_value):
    """Validate OpenAI API key by making a test request"""
    headers = {'Authorization': f'Bearer {key_value}'}
    response = requests.get('https://api.openai.com/v1/models', headers=headers, timeout=10)
    return response.status_code == 200

def validate_stability_key(key_value):
    """Validate Stability API key by making a test request"""
    headers = {'Authorization': f'Bearer {key_value}'}
    response = requests.get('https://api.stability.ai/v1/engines/list', headers=headers, timeout=10)
    return response.status_code == 200

def validate_elevenlabs_key(key_value):
    """Validate ElevenLabs API key by making a test request"""
    headers = {'xi-api-key': key_value}
    response = requests.get('https://api.elevenlabs.io/v1/voices', headers=headers, timeout=10)
    return response.status_code == 200

def main():
    """Main function to test API keys"""
    print(f"{Fore.CYAN}🔍 API Key Verification Test{Style.RESET_ALL}")
    print("=" * 50)
    
    # Define the API keys to check and their validation functions
    api_keys = [
        ('OPENAI_API_KEY', validate_openai_key),
        ('AI_STUDIO_API_KEY', None),  # Add validation if available
        ('STABILITY_API_KEY', validate_stability_key),
        ('ELEVENLABS_API_KEY', validate_elevenlabs_key)
    ]
    
    # Check each API key
    results = []
    for key_name, validation_func in api_keys:
        results.append(check_api_key(key_name, validation_func))
        print("-" * 50)
    
    # Print summary
    print(f"\n{Fore.CYAN}📊 Summary:{Style.RESET_ALL}")
    if all(results):
        print(f"{Fore.GREEN}✅ All API keys are properly configured and accessible.{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}⚠️ Some API keys are missing or invalid. Please check the results above.{Style.RESET_ALL}")
    
    return 0 if all(results) else 1

if __name__ == "__main__":
    sys.exit(main())