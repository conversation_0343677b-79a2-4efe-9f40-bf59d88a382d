/**
 * WebSocket initialization service
 * This file initializes the WebSocket connection and sets up event listeners
 */

import WebSocketService from './WebSocketService';
import { getWebSocketUrl } from '../utils/websocket';

/**
 * Initialize the WebSocket service
 * @returns {WebSocketService} The initialized WebSocket service
 */
export function initializeWebSocket() {
  console.log('Initializing WebSocket service...');
  
  // Get the WebSocket URL
  const wsUrl = getWebSocketUrl('app_builder');
  console.log('WebSocket URL:', wsUrl);
  
  // Initialize the WebSocket service
  const wsService = WebSocketService.getInstance({
    baseUrl: wsUrl.substring(0, wsUrl.lastIndexOf('/')),
    endpoint: wsUrl.substring(wsUrl.lastIndexOf('/'))
  });
  
  // Set up event listeners
  wsService.addEventListener('open', (event) => {
    console.log('WebSocket connected:', event);
    
    // Show success message
    showNotification('success', 'WebSocket Connected', 'Successfully connected to the WebSocket server');
    
    // Send a ping message to test the connection
    wsService.sendMessage({ type: 'ping', message: 'Hello from App Builder' })
      .catch(error => console.error('Error sending ping message:', error));
  });
  
  wsService.addEventListener('close', (event) => {
    console.log('WebSocket disconnected:', event);
    
    // Show warning message
    showNotification('warning', 'WebSocket Disconnected', 'Connection to the WebSocket server was lost');
  });
  
  wsService.addEventListener('error', (error) => {
    console.error('WebSocket error:', error);
    
    // Show error message
    showNotification('error', 'WebSocket Error', 'An error occurred with the WebSocket connection');
  });
  
  wsService.addEventListener('message', (data) => {
    console.log('WebSocket message received:', data);
    
    // Handle different message types
    if (data.type === 'pong') {
      console.log('Received pong response:', data);
    } else if (data.type === 'error') {
      console.error('Received error message:', data);
      showNotification('error', 'WebSocket Error', data.message || 'Unknown error');
    }
  });
  
  return wsService;
}

/**
 * Connect to the WebSocket server
 * @returns {Promise<WebSocketService>} Promise that resolves when connected
 */
export function connectToWebSocket() {
  const wsService = WebSocketService.getInstance();
  
  return wsService.connect()
    .then(() => {
      console.log('WebSocket connected successfully');
      return wsService;
    })
    .catch(error => {
      console.error('Failed to connect to WebSocket:', error);
      showNotification('error', 'Connection Failed', 'Failed to connect to the WebSocket server');
      throw error;
    });
}

/**
 * Show a notification message
 * @param {string} type - Notification type (success, error, warning, info)
 * @param {string} title - Notification title
 * @param {string} message - Notification message
 */
function showNotification(type, title, message) {
  // Create notification element
  const notification = document.createElement('div');
  notification.style.position = 'fixed';
  notification.style.top = '20px';
  notification.style.right = '20px';
  notification.style.padding = '15px 20px';
  notification.style.borderRadius = '4px';
  notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  notification.style.zIndex = '1000';
  notification.style.minWidth = '300px';
  notification.style.maxWidth = '400px';
  notification.style.display = 'flex';
  notification.style.flexDirection = 'column';
  notification.style.transition = 'all 0.3s ease';
  
  // Set background color based on type
  switch (type) {
    case 'success':
      notification.style.backgroundColor = '#52c41a';
      notification.style.color = 'white';
      break;
    case 'error':
      notification.style.backgroundColor = '#f5222d';
      notification.style.color = 'white';
      break;
    case 'warning':
      notification.style.backgroundColor = '#faad14';
      notification.style.color = 'white';
      break;
    case 'info':
    default:
      notification.style.backgroundColor = '#1890ff';
      notification.style.color = 'white';
      break;
  }
  
  // Create title element
  const titleElement = document.createElement('div');
  titleElement.textContent = title;
  titleElement.style.fontWeight = 'bold';
  titleElement.style.marginBottom = '5px';
  
  // Create message element
  const messageElement = document.createElement('div');
  messageElement.textContent = message;
  messageElement.style.fontSize = '14px';
  
  // Add elements to notification
  notification.appendChild(titleElement);
  notification.appendChild(messageElement);
  
  // Add notification to document
  document.body.appendChild(notification);
  
  // Remove notification after 5 seconds
  setTimeout(() => {
    notification.style.opacity = '0';
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 5000);
}

export default {
  initializeWebSocket,
  connectToWebSocket
};
