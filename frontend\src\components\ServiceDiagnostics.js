import React, { useState, useEffect } from 'react';
import ServiceAccessibilityChecker from '../utils/serviceAccessibilityChecker';

/**
 * Component to check and display the status of all services and functions
 */
const ServiceDiagnostics = () => {
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [checker, setChecker] = useState(null);

  useEffect(() => {
    // Create the checker instance
    const serviceChecker = new ServiceAccessibilityChecker();
    setChecker(serviceChecker);

    // Clean up on unmount
    return () => {
      if (serviceChecker) {
        serviceChecker.cleanup();
      }
    };
  }, []);

  const runDiagnostics = async () => {
    if (!checker) return;

    setLoading(true);
    setError(null);

    try {
      const diagnosticResults = await checker.checkAll();
      setResults(diagnosticResults);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeStyle = {
      display: 'inline-block',
      padding: '4px 8px',
      borderRadius: '4px',
      fontWeight: 'bold',
      color: 'white',
      backgroundColor: 
        status === 'success' ? '#10b981' : 
        status === 'warning' ? '#f59e0b' : 
        status === 'error' ? '#ef4444' : 
        '#6b7280'
    };

    return (
      <span style={badgeStyle}>
        {status === 'success' ? 'Success' : 
         status === 'warning' ? 'Warning' : 
         status === 'error' ? 'Error' : 
         'Pending'}
      </span>
    );
  };

  // Render API endpoints section
  const renderApiSection = () => {
    if (!results || !results.api) return null;

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>API Endpoints {renderStatusBadge(results.api.status)}</h3>
        <p>{results.api.message}</p>
        <ul style={{ listStyle: 'none', padding: 0 }}>
          {Object.entries(results.api.endpoints).map(([endpoint, data]) => (
            <li key={endpoint} style={{ 
              padding: '8px', 
              marginBottom: '4px', 
              backgroundColor: '#f3f4f6',
              borderRadius: '4px'
            }}>
              <strong>{endpoint}:</strong> {renderStatusBadge(data.status)} {data.message}
            </li>
          ))}
        </ul>
      </div>
    );
  };

  // Render WebSocket section
  const renderWebSocketSection = () => {
    if (!results || !results.websocket) return null;

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>WebSocket Connections {renderStatusBadge(results.websocket.status)}</h3>
        <p>{results.websocket.message}</p>
        <ul style={{ listStyle: 'none', padding: 0 }}>
          {Object.entries(results.websocket.endpoints).map(([endpoint, data]) => (
            <li key={endpoint} style={{ 
              padding: '8px', 
              marginBottom: '4px', 
              backgroundColor: '#f3f4f6',
              borderRadius: '4px'
            }}>
              <strong>{endpoint}:</strong> {renderStatusBadge(data.status)} {data.message}
            </li>
          ))}
        </ul>
      </div>
    );
  };

  // Render GraphQL section
  const renderGraphQLSection = () => {
    if (!results || !results.graphql) return null;

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>GraphQL API {renderStatusBadge(results.graphql.status)}</h3>
        <p>{results.graphql.message}</p>
      </div>
    );
  };

  // Render Service Worker section
  const renderServiceWorkerSection = () => {
    if (!results || !results.serviceWorker) return null;

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>Service Worker {renderStatusBadge(results.serviceWorker.status)}</h3>
        <p>{results.serviceWorker.message}</p>
      </div>
    );
  };

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: 'white', 
      borderRadius: '8px',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
    }}>
      <h2>Service Diagnostics</h2>
      
      <button 
        onClick={runDiagnostics}
        disabled={loading}
        style={{
          padding: '8px 16px',
          backgroundColor: '#3b82f6',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: loading ? 'not-allowed' : 'pointer',
          opacity: loading ? 0.7 : 1,
          marginBottom: '20px'
        }}
      >
        {loading ? 'Running Diagnostics...' : 'Run Diagnostics'}
      </button>

      {error && (
        <div style={{ 
          padding: '12px', 
          backgroundColor: '#fee2e2', 
          color: '#b91c1c',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      {results ? (
        <div>
          {renderApiSection()}
          {renderWebSocketSection()}
          {renderGraphQLSection()}
          {renderServiceWorkerSection()}
        </div>
      ) : (
        <p>Click the button above to check service accessibility.</p>
      )}
    </div>
  );
};

export default ServiceDiagnostics;
