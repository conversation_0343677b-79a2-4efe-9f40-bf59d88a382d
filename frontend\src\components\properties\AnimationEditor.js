import React, { useState } from 'react';
import { Select, InputNumber, Space, Typography, Card, Button, Slider, Switch } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

/**
 * Animation Editor
 * 
 * Editor for CSS animations and transitions with live preview.
 */

const AnimationEditor = ({
  value = {},
  onChange,
  showPreview = true
}) => {
  const [isPlaying, setIsPlaying] = useState(false);

  const animationTypes = [
    { label: 'Fade In', value: 'fadeIn' },
    { label: 'Fade Out', value: 'fadeOut' },
    { label: 'Slide In Left', value: 'slideInLeft' },
    { label: 'Slide In Right', value: 'slideInRight' },
    { label: 'Slide In Up', value: 'slideInUp' },
    { label: 'Slide In Down', value: 'slideInDown' },
    { label: 'Bounce In', value: 'bounceIn' },
    { label: 'Zoom In', value: 'zoomIn' },
    { label: 'Rotate In', value: 'rotateIn' },
    { label: 'Pulse', value: 'pulse' },
    { label: 'Shake', value: 'shake' },
    { label: 'Wobble', value: 'wobble' }
  ];

  const easingFunctions = [
    { label: 'Linear', value: 'linear' },
    { label: 'Ease', value: 'ease' },
    { label: 'Ease In', value: 'ease-in' },
    { label: 'Ease Out', value: 'ease-out' },
    { label: 'Ease In Out', value: 'ease-in-out' },
    { label: 'Cubic Bezier', value: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)' }
  ];

  const handleChange = (property, newValue) => {
    const updated = { ...value, [property]: newValue };
    if (onChange) onChange(updated);
  };

  const getAnimationCSS = () => {
    const {
      type = 'fadeIn',
      duration = 1,
      delay = 0,
      easing = 'ease',
      iterations = 1,
      direction = 'normal',
      fillMode = 'both'
    } = value;

    return {
      animation: `${type} ${duration}s ${easing} ${delay}s ${iterations === -1 ? 'infinite' : iterations} ${direction} ${fillMode}`,
      animationPlayState: isPlaying ? 'running' : 'paused'
    };
  };

  const keyframes = {
    fadeIn: `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    `,
    fadeOut: `
      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }
    `,
    slideInLeft: `
      @keyframes slideInLeft {
        from { transform: translateX(-100%); }
        to { transform: translateX(0); }
      }
    `,
    slideInRight: `
      @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
      }
    `,
    slideInUp: `
      @keyframes slideInUp {
        from { transform: translateY(100%); }
        to { transform: translateY(0); }
      }
    `,
    slideInDown: `
      @keyframes slideInDown {
        from { transform: translateY(-100%); }
        to { transform: translateY(0); }
      }
    `,
    bounceIn: `
      @keyframes bounceIn {
        0%, 20%, 40%, 60%, 80%, 100% { animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
        0% { opacity: 0; transform: scale3d(0.3, 0.3, 0.3); }
        20% { transform: scale3d(1.1, 1.1, 1.1); }
        40% { transform: scale3d(0.9, 0.9, 0.9); }
        60% { opacity: 1; transform: scale3d(1.03, 1.03, 1.03); }
        80% { transform: scale3d(0.97, 0.97, 0.97); }
        100% { opacity: 1; transform: scale3d(1, 1, 1); }
      }
    `,
    zoomIn: `
      @keyframes zoomIn {
        from { opacity: 0; transform: scale3d(0.3, 0.3, 0.3); }
        50% { opacity: 1; }
      }
    `,
    rotateIn: `
      @keyframes rotateIn {
        from { transform: rotate3d(0, 0, 1, -200deg); opacity: 0; }
        to { transform: translate3d(0, 0, 0); opacity: 1; }
      }
    `,
    pulse: `
      @keyframes pulse {
        from { transform: scale3d(1, 1, 1); }
        50% { transform: scale3d(1.05, 1.05, 1.05); }
        to { transform: scale3d(1, 1, 1); }
      }
    `,
    shake: `
      @keyframes shake {
        from, to { transform: translate3d(0, 0, 0); }
        10%, 30%, 50%, 70%, 90% { transform: translate3d(-10px, 0, 0); }
        20%, 40%, 60%, 80% { transform: translate3d(10px, 0, 0); }
      }
    `,
    wobble: `
      @keyframes wobble {
        from { transform: translate3d(0, 0, 0); }
        15% { transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }
        30% { transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }
        45% { transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }
        60% { transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }
        75% { transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }
        to { transform: translate3d(0, 0, 0); }
      }
    `
  };

  const playAnimation = () => {
    setIsPlaying(true);
    setTimeout(() => setIsPlaying(false), (value.duration || 1) * 1000);
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Text strong>Animation</Text>

      {/* Animation Type */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Animation Type</Text>
        <Select
          value={value.type || 'fadeIn'}
          onChange={(val) => handleChange('type', val)}
          style={{ width: '100%', marginTop: 4 }}
        >
          {animationTypes.map(type => (
            <Option key={type.value} value={type.value}>
              {type.label}
            </Option>
          ))}
        </Select>
      </div>

      {/* Duration and Delay */}
      <Space style={{ width: '100%' }}>
        <div style={{ flex: 1 }}>
          <Text style={{ fontSize: 12, color: '#666' }}>Duration</Text>
          <InputNumber
            value={value.duration || 1}
            onChange={(val) => handleChange('duration', val)}
            min={0.1}
            max={10}
            step={0.1}
            style={{ width: '100%', marginTop: 4 }}
            addonAfter="s"
          />
        </div>
        <div style={{ flex: 1 }}>
          <Text style={{ fontSize: 12, color: '#666' }}>Delay</Text>
          <InputNumber
            value={value.delay || 0}
            onChange={(val) => handleChange('delay', val)}
            min={0}
            max={5}
            step={0.1}
            style={{ width: '100%', marginTop: 4 }}
            addonAfter="s"
          />
        </div>
      </Space>

      {/* Easing */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Easing</Text>
        <Select
          value={value.easing || 'ease'}
          onChange={(val) => handleChange('easing', val)}
          style={{ width: '100%', marginTop: 4 }}
        >
          {easingFunctions.map(easing => (
            <Option key={easing.value} value={easing.value}>
              {easing.label}
            </Option>
          ))}
        </Select>
      </div>

      {/* Iterations */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Iterations</Text>
        <Space style={{ width: '100%', marginTop: 4 }}>
          <InputNumber
            value={value.iterations === -1 ? 'infinite' : value.iterations || 1}
            onChange={(val) => handleChange('iterations', val === 'infinite' ? -1 : val)}
            min={1}
            max={10}
            style={{ flex: 1 }}
          />
          <Switch
            checked={value.iterations === -1}
            onChange={(checked) => handleChange('iterations', checked ? -1 : 1)}
            checkedChildren="∞"
            unCheckedChildren="1"
          />
        </Space>
      </div>

      {/* Direction */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Direction</Text>
        <Select
          value={value.direction || 'normal'}
          onChange={(val) => handleChange('direction', val)}
          style={{ width: '100%', marginTop: 4 }}
        >
          <Option value="normal">Normal</Option>
          <Option value="reverse">Reverse</Option>
          <Option value="alternate">Alternate</Option>
          <Option value="alternate-reverse">Alternate Reverse</Option>
        </Select>
      </div>

      {/* Preview */}
      {showPreview && (
        <Card 
          size="small" 
          title="Preview"
          extra={
            <Space>
              <Button
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={playAnimation}
                disabled={isPlaying}
              >
                Play
              </Button>
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => {
                  setIsPlaying(false);
                  setTimeout(() => playAnimation(), 100);
                }}
              >
                Replay
              </Button>
            </Space>
          }
        >
          <style>
            {keyframes[value.type || 'fadeIn']}
          </style>
          <div
            style={{
              width: 100,
              height: 60,
              backgroundColor: '#1890ff',
              borderRadius: 4,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: 12,
              margin: '20px auto',
              ...getAnimationCSS()
            }}
          >
            Preview
          </div>
        </Card>
      )}

      {/* CSS Output */}
      <Card size="small" title="CSS Output">
        <pre style={{ 
          fontSize: 11, 
          background: '#f5f5f5', 
          padding: 8, 
          borderRadius: 4,
          margin: 0,
          overflow: 'auto'
        }}>
          {`animation: ${value.type || 'fadeIn'} ${value.duration || 1}s ${value.easing || 'ease'} ${value.delay || 0}s ${value.iterations === -1 ? 'infinite' : value.iterations || 1} ${value.direction || 'normal'} ${value.fillMode || 'both'};`}
        </pre>
      </Card>
    </Space>
  );
};

export default AnimationEditor;
