/**
 * Test WebSocket URL generation
 * This script tests the WebSocket URL generation logic
 */

import { getWebSocketUrl } from './config/env.js';

// Mock window object for testing
global.window = {
  location: {
    hostname: 'localhost',
    port: '3000',
    protocol: 'http:'
  }
};

// Mock process.env
process.env.NODE_ENV = 'development';

console.log('🔌 Testing WebSocket URL generation...');
console.log('=' * 50);

// Test different endpoints
const endpoints = ['app_builder', 'test', 'simple', 'collaboration'];

endpoints.forEach(endpoint => {
  console.log(`\n🔌 Testing endpoint: ${endpoint}`);
  try {
    const url = getWebSocketUrl(endpoint);
    console.log(`✅ Generated URL: ${url}`);
  } catch (error) {
    console.error(`❌ Error generating URL: ${error.message}`);
  }
});

console.log('\n🔌 WebSocket URL generation test completed!');
