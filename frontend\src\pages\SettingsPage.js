import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { ThemeCustomizer } from '../components/theme/ThemeManager';
import Profile from '../components/auth/Profile';

/**
 * Settings Page
 * 
 * This page allows users to configure application settings.
 */
const SettingsPage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  
  // Settings tabs
  const tabs = [
    { id: 'profile', label: 'Profile', icon: '👤' },
    { id: 'appearance', label: 'Appearance', icon: '🎨' },
    { id: 'notifications', label: 'Notifications', icon: '🔔' },
    { id: 'security', label: 'Security', icon: '🔒' },
    { id: 'integrations', label: 'Integrations', icon: '🔌' }
  ];
  
  return (
    <div className="settings-container">
      <div className="settings-header">
        <h1>Settings</h1>
        <p className="settings-subtitle">Manage your account and application preferences</p>
      </div>
      
      <div className="settings-content">
        <div className="settings-sidebar">
          <div className="user-info">
            <div className="user-avatar">
              {user?.avatar ? (
                <img src={user.avatar} alt={user.name || user.username} />
              ) : (
                <div className="avatar-placeholder">
                  {(user?.name || user?.username || 'User').charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <div className="user-details">
              <div className="user-name">{user?.name || user?.username || 'User'}</div>
              <div className="user-email">{user?.email || 'No email'}</div>
            </div>
          </div>
          
          <nav className="settings-nav">
            <ul className="nav-list">
              {tabs.map(tab => (
                <li key={tab.id} className="nav-item">
                  <button
                    className={`nav-button ${activeTab === tab.id ? 'active' : ''}`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    <span className="nav-icon">{tab.icon}</span>
                    <span className="nav-label">{tab.label}</span>
                  </button>
                </li>
              ))}
            </ul>
          </nav>
        </div>
        
        <div className="settings-main">
          {activeTab === 'profile' && (
            <div className="settings-tab">
              <h2>Profile Settings</h2>
              <Profile />
            </div>
          )}
          
          {activeTab === 'appearance' && (
            <div className="settings-tab">
              <h2>Appearance Settings</h2>
              <div className="appearance-settings">
                <ThemeCustomizer />
              </div>
            </div>
          )}
          
          {activeTab === 'notifications' && (
            <div className="settings-tab">
              <h2>Notification Settings</h2>
              <div className="notification-settings">
                <div className="settings-section">
                  <h3>Email Notifications</h3>
                  <div className="settings-options">
                    <div className="settings-option">
                      <label className="toggle-switch">
                        <input type="checkbox" defaultChecked />
                        <span className="toggle-slider"></span>
                      </label>
                      <div className="option-details">
                        <div className="option-label">Project updates</div>
                        <div className="option-description">Receive emails about updates to your projects</div>
                      </div>
                    </div>
                    <div className="settings-option">
                      <label className="toggle-switch">
                        <input type="checkbox" defaultChecked />
                        <span className="toggle-slider"></span>
                      </label>
                      <div className="option-details">
                        <div className="option-label">Security alerts</div>
                        <div className="option-description">Receive emails about security issues</div>
                      </div>
                    </div>
                    <div className="settings-option">
                      <label className="toggle-switch">
                        <input type="checkbox" />
                        <span className="toggle-slider"></span>
                      </label>
                      <div className="option-details">
                        <div className="option-label">Marketing emails</div>
                        <div className="option-description">Receive emails about new features and offers</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="settings-section">
                  <h3>In-App Notifications</h3>
                  <div className="settings-options">
                    <div className="settings-option">
                      <label className="toggle-switch">
                        <input type="checkbox" defaultChecked />
                        <span className="toggle-slider"></span>
                      </label>
                      <div className="option-details">
                        <div className="option-label">Project comments</div>
                        <div className="option-description">Receive notifications when someone comments on your projects</div>
                      </div>
                    </div>
                    <div className="settings-option">
                      <label className="toggle-switch">
                        <input type="checkbox" defaultChecked />
                        <span className="toggle-slider"></span>
                      </label>
                      <div className="option-details">
                        <div className="option-label">Project shares</div>
                        <div className="option-description">Receive notifications when someone shares a project with you</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'security' && (
            <div className="settings-tab">
              <h2>Security Settings</h2>
              <div className="security-settings">
                <div className="settings-section">
                  <h3>Two-Factor Authentication</h3>
                  <p className="section-description">
                    Add an extra layer of security to your account by requiring a verification code in addition to your password.
                  </p>
                  <button className="primary-button">Enable 2FA</button>
                </div>
                
                <div className="settings-section">
                  <h3>Session Management</h3>
                  <p className="section-description">
                    Manage your active sessions and sign out from other devices.
                  </p>
                  <div className="session-list">
                    <div className="session-item current">
                      <div className="session-details">
                        <div className="session-device">Current Browser (Chrome)</div>
                        <div className="session-info">Windows • Last active: Just now</div>
                      </div>
                      <div className="session-actions">
                        <span className="current-label">Current</span>
                      </div>
                    </div>
                    <div className="session-item">
                      <div className="session-details">
                        <div className="session-device">Mobile App</div>
                        <div className="session-info">Android • Last active: 2 hours ago</div>
                      </div>
                      <div className="session-actions">
                        <button className="text-button">Sign Out</button>
                      </div>
                    </div>
                  </div>
                  <button className="secondary-button">Sign Out All Other Devices</button>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'integrations' && (
            <div className="settings-tab">
              <h2>Integrations</h2>
              <div className="integrations-settings">
                <div className="settings-section">
                  <h3>Connected Services</h3>
                  <p className="section-description">
                    Connect your account to other services to import or export data.
                  </p>
                  <div className="integration-list">
                    <div className="integration-item">
                      <div className="integration-icon">
                        <span className="github-icon">G</span>
                      </div>
                      <div className="integration-details">
                        <div className="integration-name">GitHub</div>
                        <div className="integration-status">Not connected</div>
                      </div>
                      <div className="integration-actions">
                        <button className="primary-button">Connect</button>
                      </div>
                    </div>
                    <div className="integration-item">
                      <div className="integration-icon">
                        <span className="google-icon">G</span>
                      </div>
                      <div className="integration-details">
                        <div className="integration-name">Google Drive</div>
                        <div className="integration-status">Not connected</div>
                      </div>
                      <div className="integration-actions">
                        <button className="primary-button">Connect</button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="settings-section">
                  <h3>API Access</h3>
                  <p className="section-description">
                    Manage your API keys and access tokens.
                  </p>
                  <button className="primary-button">Generate API Key</button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <style jsx>{`
        .settings-container {
          padding: var(--spacing-lg);
          max-width: 1200px;
          margin: 0 auto;
        }
        
        .settings-header {
          margin-bottom: var(--spacing-lg);
        }
        
        .settings-subtitle {
          color: var(--color-textSecondary);
          margin-top: var(--spacing-xs);
        }
        
        .settings-content {
          display: grid;
          grid-template-columns: 250px 1fr;
          gap: var(--spacing-lg);
        }
        
        .settings-sidebar {
          background-color: var(--color-surface);
          border-radius: var(--border-radius-md);
          box-shadow: var(--shadow-sm);
          overflow: hidden;
        }
        
        .user-info {
          display: flex;
          align-items: center;
          padding: var(--spacing-md);
          border-bottom: 1px solid var(--color-border);
        }
        
        .user-avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: var(--spacing-md);
        }
        
        .user-avatar img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .avatar-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--color-primary);
          color: white;
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-bold);
        }
        
        .user-details {
          overflow: hidden;
        }
        
        .user-name {
          font-weight: var(--font-weight-medium);
          margin-bottom: var(--spacing-xs);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .user-email {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .settings-nav {
          padding: var(--spacing-md);
        }
        
        .nav-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }
        
        .nav-item {
          margin-bottom: var(--spacing-xs);
        }
        
        .nav-button {
          display: flex;
          align-items: center;
          width: 100%;
          padding: var(--spacing-sm) var(--spacing-md);
          background: none;
          border: none;
          border-radius: var(--border-radius-md);
          color: var(--color-text);
          cursor: pointer;
          text-align: left;
          transition: background-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .nav-button:hover {
          background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
        }
        
        .nav-button.active {
          background-color: color-mix(in srgb, var(--color-primary) 15%, transparent);
          color: var(--color-primary);
          font-weight: var(--font-weight-medium);
        }
        
        .nav-icon {
          margin-right: var(--spacing-sm);
          font-size: var(--font-size-lg);
        }
        
        .settings-main {
          background-color: var(--color-surface);
          border-radius: var(--border-radius-md);
          box-shadow: var(--shadow-sm);
          padding: var(--spacing-lg);
        }
        
        .settings-tab h2 {
          margin-top: 0;
          margin-bottom: var(--spacing-lg);
          padding-bottom: var(--spacing-sm);
          border-bottom: 1px solid var(--color-border);
        }
        
        .settings-section {
          margin-bottom: var(--spacing-lg);
        }
        
        .settings-section h3 {
          margin-bottom: var(--spacing-sm);
        }
        
        .section-description {
          color: var(--color-textSecondary);
          margin-bottom: var(--spacing-md);
        }
        
        .settings-options {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);
        }
        
        .settings-option {
          display: flex;
          align-items: flex-start;
          gap: var(--spacing-md);
        }
        
        .option-details {
          flex: 1;
        }
        
        .option-label {
          font-weight: var(--font-weight-medium);
          margin-bottom: var(--spacing-xs);
        }
        
        .option-description {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .toggle-switch {
          position: relative;
          display: inline-block;
          width: 40px;
          height: 24px;
        }
        
        .toggle-switch input {
          opacity: 0;
          width: 0;
          height: 0;
        }
        
        .toggle-slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: var(--color-border);
          transition: var(--transition-fast);
          border-radius: 34px;
        }
        
        .toggle-slider:before {
          position: absolute;
          content: "";
          height: 16px;
          width: 16px;
          left: 4px;
          bottom: 4px;
          background-color: white;
          transition: var(--transition-fast);
          border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
          background-color: var(--color-primary);
        }
        
        input:checked + .toggle-slider:before {
          transform: translateX(16px);
        }
        
        .primary-button {
          padding: var(--spacing-sm) var(--spacing-md);
          background-color: var(--color-primary);
          color: white;
          border: none;
          border-radius: var(--border-radius-md);
          cursor: pointer;
          transition: background-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .primary-button:hover {
          background-color: color-mix(in srgb, var(--color-primary) 80%, black);
        }
        
        .secondary-button {
          padding: var(--spacing-sm) var(--spacing-md);
          background-color: transparent;
          color: var(--color-primary);
          border: 1px solid var(--color-primary);
          border-radius: var(--border-radius-md);
          cursor: pointer;
          transition: all var(--transition-fast) var(--transition-timing-function);
        }
        
        .secondary-button:hover {
          background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
        }
        
        .text-button {
          background: none;
          border: none;
          color: var(--color-primary);
          cursor: pointer;
          padding: 0;
        }
        
        .text-button:hover {
          text-decoration: underline;
        }
        
        .session-list, .integration-list {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);
          margin-bottom: var(--spacing-md);
        }
        
        .session-item, .integration-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-md);
          border: 1px solid var(--color-border);
          border-radius: var(--border-radius-md);
        }
        
        .session-item.current {
          border-color: var(--color-primary);
          background-color: color-mix(in srgb, var(--color-primary) 5%, transparent);
        }
        
        .session-device, .integration-name {
          font-weight: var(--font-weight-medium);
          margin-bottom: var(--spacing-xs);
        }
        
        .session-info, .integration-status {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .current-label {
          font-size: var(--font-size-xs);
          color: var(--color-primary);
          border: 1px solid var(--color-primary);
          padding: 2px 8px;
          border-radius: var(--border-radius-sm);
        }
        
        .integration-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--border-radius-md);
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--color-border);
          margin-right: var(--spacing-md);
        }
        
        .github-icon, .google-icon {
          font-weight: var(--font-weight-bold);
        }
        
        @media (max-width: 768px) {
          .settings-content {
            grid-template-columns: 1fr;
          }
          
          .settings-sidebar {
            margin-bottom: var(--spacing-md);
          }
        }
      `}</style>
    </div>
  );
};

export default SettingsPage;
