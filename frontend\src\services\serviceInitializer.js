/**
 * Service Initializer
 *
 * This module initializes all services and ensures they are properly configured.
 * It handles WebSocket connections, API services, and other application services.
 */

import WebSocketService from './WebSocketService';
import { getWebSocketUrl } from '../utils/websocket';
import API_ENDPOINTS from '../config/api';

// Configuration for service initialization
const config = {
  // WebSocket configuration
  websocket: {
    autoConnect: true,
    reconnectOptions: {
      maxAttempts: 10,
      initialDelay: 1000,
      maxDelay: 30000,
      useExponentialBackoff: true,
      jitter: 0.2
    },
    securityOptions: {
      validateMessages: true,
      sanitizeMessages: true,
      rateLimiting: {
        enabled: true,
        maxMessagesPerSecond: 20,
        burstSize: 50
      }
    },
    performanceOptions: {
      compression: {
        enabled: true,
        threshold: 1024,
        level: 6
      },
      batchingEnabled: true,
      batchInterval: 50,
      maxBatchSize: 20,
      offlineQueueEnabled: true,
      offlineStorage: {
        enabled: true,
        persistKey: 'websocket_offline_queue'
      }
    }
  },

  // Service Worker configuration
  serviceWorker: {
    enabled: true,
    updateInterval: 60 * 60 * 1000, // 1 hour
    scope: '/'
  }
};

/**
 * Initialize all services
 * @returns {Promise<Object>} Initialized services
 */
export async function initializeServices() {
  console.log('Initializing services...');

  const services = {};

  try {
    // Initialize WebSocket service
    services.websocket = await initializeWebSocketService();

    // Initialize Service Worker
    services.serviceWorker = await initializeServiceWorker();

    console.log('All services initialized successfully');
    return services;
  } catch (error) {
    console.error('Error initializing services:', error);
    throw error;
  }
}

/**
 * Initialize WebSocket service
 * @returns {Promise<WebSocketService>} Initialized WebSocket service
 */
async function initializeWebSocketService() {
  console.log('Initializing WebSocket service...');

  try {
    // Get WebSocket URL
    const wsUrl = getWebSocketUrl('app_builder');
    console.log('WebSocket URL:', wsUrl);

    // Initialize WebSocket service
    const wsService = WebSocketService.getInstance({
      baseUrl: wsUrl.substring(0, wsUrl.lastIndexOf('/app_builder')),
      endpoint: '/app_builder/'
    });

    // Configure WebSocket service with reconnect options
    if (config.websocket.reconnectOptions) {
      wsService.maxReconnectAttempts = 20; // Increase from 10
      wsService.reconnectInterval = 2000; // Increase from 1000
      wsService.maxReconnectInterval = 60000; // Increase from 30000
      wsService.connectionTimeout = 10000; // Increase from 5000
      wsService.reconnectDecay = config.websocket.reconnectOptions.useExponentialBackoff ?
        (config.websocket.reconnectOptions.reconnectDecay || 1.5) : 1;
    }

    // Add event listeners
    wsService.addEventListener('open', (event) => {
      console.log('WebSocket connected:', event);

      // Send a ping message to test the connection
      wsService.sendMessage({
        type: 'ping',
        message: 'Hello from App Builder',
        timestamp: Date.now(),
        id: `initial-ping-${Date.now()}`
      }).catch(error => console.error('Error sending ping message:', error));
    });

    wsService.addEventListener('close', (event) => {
      console.log('WebSocket disconnected:', event);
    });

    wsService.addEventListener('error', (error) => {
      console.error('WebSocket error:', error);
    });

    // Add reconnection event listeners
    wsService.addEventListener('reconnecting', (data) => {
      console.log(`WebSocket reconnecting (attempt ${data.attempt}/${data.maxAttempts})...`);
    });

    wsService.addEventListener('reconnected', (data) => {
      console.log(`WebSocket reconnected after ${data.attempt} attempts`);
    });

    wsService.addEventListener('reconnect_failed', (data) => {
      console.error(`WebSocket reconnection failed after ${data.attempt} attempts:`, data.error);
    });

    wsService.addEventListener('heartbeat_failure', (data) => {
      console.warn(`WebSocket heartbeat failure detected: ${data.missedHeartbeats} missed heartbeats`);
    });

    // Connect to WebSocket server if autoConnect is enabled
    if (config.websocket.autoConnect) {
      try {
        await wsService.connect();
        console.log('WebSocket connected successfully');
      } catch (error) {
        console.error('Initial WebSocket connection failed:', error);
        console.log('Will attempt to reconnect automatically...');
        // Don't throw here, let the service handle reconnection
      }
    }

    // Set up a periodic check to ensure connection is still alive
    setInterval(() => {
      if (!wsService.isConnected()) {
        console.log('WebSocket connection check: Not connected, attempting to reconnect...');
        wsService.reconnect().catch(error => {
          console.error('Error during periodic reconnection:', error);
        });
      } else {
        console.log('WebSocket connection check: Connected');
      }
    }, 60000); // Check every minute

    return wsService;
  } catch (error) {
    console.error('Error initializing WebSocket service:', error);
    throw error;
  }
}

/**
 * Initialize Service Worker
 * @returns {Promise<ServiceWorkerRegistration|null>} Service Worker registration
 */
async function initializeServiceWorker() {
  console.log('Initializing Service Worker...');

  if (!config.serviceWorker.enabled) {
    console.log('Service Worker is disabled');
    return null;
  }

  if (!('serviceWorker' in navigator)) {
    console.warn('Service Worker is not supported in this browser');
    return null;
  }

  try {
    // Register Service Worker
    const registration = await navigator.serviceWorker.register('/service-worker.js', {
      scope: config.serviceWorker.scope
    });

    console.log('Service Worker registered successfully:', registration);

    // Set up periodic updates
    setInterval(() => {
      registration.update()
        .then(() => console.log('Service Worker updated'))
        .catch(error => console.error('Error updating Service Worker:', error));
    }, config.serviceWorker.updateInterval);

    // Handle Service Worker updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;

      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          // New Service Worker is installed and ready to take over
          console.log('New Service Worker installed and ready');

          // Notify the user about the update
          if (window.confirm('New version available! Reload to update?')) {
            window.location.reload();
          }
        }
      });
    });

    return registration;
  } catch (error) {
    console.error('Error registering Service Worker:', error);
    return null;
  }
}

/**
 * Check if all services are accessible
 * @returns {Promise<Object>} Service accessibility status
 */
export async function checkServiceAccessibility() {
  console.log('Checking service accessibility...');

  const status = {
    websocket: false,
    api: false,
    serviceWorker: false
  };

  try {
    // Check WebSocket accessibility
    status.websocket = await checkWebSocketAccessibility();

    // Check API accessibility
    status.api = await checkApiAccessibility();

    // Check Service Worker accessibility
    status.serviceWorker = await checkServiceWorkerAccessibility();

    console.log('Service accessibility check completed:', status);
    return status;
  } catch (error) {
    console.error('Error checking service accessibility:', error);
    throw error;
  }
}

/**
 * Check if WebSocket is accessible
 * @returns {Promise<boolean>} Whether WebSocket is accessible
 */
async function checkWebSocketAccessibility() {
  return new Promise((resolve) => {
    try {
      const url = getWebSocketUrl('app_builder');
      const socket = new WebSocket(url);

      // Set a timeout
      const timeoutId = setTimeout(() => {
        socket.close();
        resolve(false);
      }, 5000);

      socket.onopen = () => {
        clearTimeout(timeoutId);
        socket.close();
        resolve(true);
      };

      socket.onerror = () => {
        clearTimeout(timeoutId);
        socket.close();
        resolve(false);
      };
    } catch (error) {
      console.error('Error checking WebSocket accessibility:', error);
      resolve(false);
    }
  });
}

/**
 * Check if API is accessible
 * @returns {Promise<boolean>} Whether API is accessible
 */
async function checkApiAccessibility() {
  try {
    const response = await fetch(API_ENDPOINTS.STATUS);
    return response.ok;
  } catch (error) {
    console.error('Error checking API accessibility:', error);
    return false;
  }
}

/**
 * Check if Service Worker is accessible
 * @returns {Promise<boolean>} Whether Service Worker is accessible
 */
async function checkServiceWorkerAccessibility() {
  if (!('serviceWorker' in navigator)) {
    return false;
  }

  try {
    const registrations = await navigator.serviceWorker.getRegistrations();
    return registrations.length > 0;
  } catch (error) {
    console.error('Error checking Service Worker accessibility:', error);
    return false;
  }
}

export default {
  initializeServices,
  checkServiceAccessibility
};

