import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Import components to test
import {
  EnhancedDraggable,
  EnhancedDropZone,
  DragDropProvider,
} from '../../../components/dragdrop/EnhancedDragDropSystem';

// Mock styled-components
jest.mock('styled-components', () => ({
  styled: {
    div: (styles) => ({ children, ...props }) => (
      <div {...props}>{children}</div>
    ),
  },
  createPortal: jest.fn((element) => element),
}));

// Mock react-dom
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: jest.fn((element) => element),
}));

// Mock Ant Design icons
jest.mock('@ant-design/icons', () => ({
  DragOutlined: () => <span data-testid="drag-icon">⋮⋮</span>,
}));

describe('EnhancedDragDropSystem', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('EnhancedDraggable', () => {
    test('renders draggable element', () => {
      render(
        <EnhancedDraggable data={{ type: 'button', id: '1' }}>
          <div>Draggable Content</div>
        </EnhancedDraggable>
      );

      expect(screen.getByText('Draggable Content')).toBeInTheDocument();
      expect(screen.getByTestId('drag-icon')).toBeInTheDocument();
    });

    test('handles drag start event', () => {
      const onDragStart = jest.fn();
      const mockData = { type: 'button', id: '1' };

      render(
        <EnhancedDraggable data={mockData} onDragStart={onDragStart}>
          <div>Draggable Content</div>
        </EnhancedDraggable>
      );

      const draggableElement = screen.getByText('Draggable Content').closest('[draggable]');
      
      const dragEvent = new DragEvent('dragstart', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });

      fireEvent(draggableElement, dragEvent);

      expect(onDragStart).toHaveBeenCalledWith(
        expect.objectContaining({
          data: mockData,
          element: draggableElement,
        })
      );
    });

    test('handles drag end event', () => {
      const onDragEnd = jest.fn();

      render(
        <EnhancedDraggable data={{ type: 'button' }} onDragEnd={onDragEnd}>
          <div>Draggable Content</div>
        </EnhancedDraggable>
      );

      const draggableElement = screen.getByText('Draggable Content').closest('[draggable]');
      
      fireEvent.dragEnd(draggableElement);

      expect(onDragEnd).toHaveBeenCalled();
    });

    test('disables dragging when disabled prop is true', () => {
      render(
        <EnhancedDraggable data={{ type: 'button' }} disabled>
          <div>Disabled Draggable</div>
        </EnhancedDraggable>
      );

      const draggableElement = screen.getByText('Disabled Draggable').closest('div');
      
      expect(draggableElement).toHaveAttribute('draggable', 'false');
      expect(draggableElement).toHaveAttribute('tabIndex', '-1');
    });

    test('shows ghost content during drag', () => {
      const ghostContent = <div>Ghost Content</div>;

      render(
        <EnhancedDraggable data={{ type: 'button' }} ghostContent={ghostContent}>
          <div>Draggable Content</div>
        </EnhancedDraggable>
      );

      const draggableElement = screen.getByText('Draggable Content').closest('[draggable]');
      
      fireEvent.dragStart(draggableElement);

      // Ghost content should be rendered (mocked createPortal will render it)
      expect(screen.getByText('Ghost Content')).toBeInTheDocument();
    });

    test('supports keyboard accessibility', async () => {
      render(
        <EnhancedDraggable data={{ type: 'button' }} ariaLabel="Draggable button">
          <div>Draggable Content</div>
        </EnhancedDraggable>
      );

      const draggableElement = screen.getByText('Draggable Content').closest('[draggable]');
      
      expect(draggableElement).toHaveAttribute('role', 'button');
      expect(draggableElement).toHaveAttribute('aria-label', 'Draggable button');
      expect(draggableElement).toHaveAttribute('tabIndex', '0');

      // Test keyboard focus
      await user.tab();
      expect(draggableElement).toHaveFocus();
    });
  });

  describe('EnhancedDropZone', () => {
    test('renders drop zone', () => {
      render(
        <EnhancedDropZone onDrop={jest.fn()}>
          <div>Drop Zone Content</div>
        </EnhancedDropZone>
      );

      expect(screen.getByText('Drop Zone Content')).toBeInTheDocument();
    });

    test('handles drop event', () => {
      const onDrop = jest.fn();
      const mockData = { type: 'button', id: '1' };

      render(
        <EnhancedDropZone onDrop={onDrop}>
          <div>Drop Zone</div>
        </EnhancedDropZone>
      );

      const dropZone = screen.getByText('Drop Zone').closest('div');
      
      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });
      
      // Mock dataTransfer.getData
      Object.defineProperty(dropEvent, 'dataTransfer', {
        value: {
          getData: jest.fn(() => JSON.stringify(mockData)),
        },
      });

      fireEvent(dropZone, dropEvent);

      expect(onDrop).toHaveBeenCalledWith(
        expect.objectContaining({
          data: mockData,
          position: expect.any(Object),
        })
      );
    });

    test('handles drag over event', () => {
      const onDragOver = jest.fn();

      render(
        <EnhancedDropZone onDrop={jest.fn()} onDragOver={onDragOver}>
          <div>Drop Zone</div>
        </EnhancedDropZone>
      );

      const dropZone = screen.getByText('Drop Zone').closest('div');
      
      fireEvent.dragOver(dropZone);

      expect(onDragOver).toHaveBeenCalled();
    });

    test('shows visual feedback during drag over', () => {
      render(
        <EnhancedDropZone onDrop={jest.fn()} showDropIndicator>
          <div>Drop Zone</div>
        </EnhancedDropZone>
      );

      const dropZone = screen.getByText('Drop Zone').closest('div');
      
      fireEvent.dragEnter(dropZone);
      
      // Visual feedback should be applied (in real implementation)
      expect(dropZone).toBeInTheDocument();
    });

    test('validates drop data when validator is provided', () => {
      const onDrop = jest.fn();
      const validator = jest.fn(() => false);

      render(
        <EnhancedDropZone onDrop={onDrop} validator={validator}>
          <div>Drop Zone</div>
        </EnhancedDropZone>
      );

      const dropZone = screen.getByText('Drop Zone').closest('div');
      
      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });
      
      Object.defineProperty(dropEvent, 'dataTransfer', {
        value: {
          getData: jest.fn(() => JSON.stringify({ type: 'invalid' })),
        },
      });

      fireEvent(dropZone, dropEvent);

      expect(validator).toHaveBeenCalledWith({ type: 'invalid' });
      expect(onDrop).not.toHaveBeenCalled();
    });

    test('supports keyboard accessibility', () => {
      render(
        <EnhancedDropZone onDrop={jest.fn()} ariaLabel="Drop zone for components">
          <div>Drop Zone</div>
        </EnhancedDropZone>
      );

      const dropZone = screen.getByText('Drop Zone').closest('div');
      
      expect(dropZone).toHaveAttribute('role', 'region');
      expect(dropZone).toHaveAttribute('aria-label', 'Drop zone for components');
    });
  });

  describe('DragDropProvider', () => {
    test('provides drag and drop context', () => {
      render(
        <DragDropProvider>
          <div>Provider Content</div>
        </DragDropProvider>
      );

      expect(screen.getByText('Provider Content')).toBeInTheDocument();
    });

    test('shows overlay during drag operations', () => {
      render(
        <DragDropProvider showOverlay>
          <EnhancedDraggable data={{ type: 'button' }}>
            <div>Draggable</div>
          </EnhancedDraggable>
        </DragDropProvider>
      );

      const draggableElement = screen.getByText('Draggable').closest('[draggable]');
      
      fireEvent.dragStart(draggableElement);
      
      // Overlay should be shown (implementation detail)
      expect(screen.getByText('Draggable')).toBeInTheDocument();
    });

    test('handles global drag events', () => {
      render(
        <DragDropProvider>
          <div>Provider Content</div>
        </DragDropProvider>
      );

      // Simulate global drag start
      fireEvent(document, new DragEvent('dragstart', { bubbles: true }));
      
      // Simulate global drag end
      fireEvent(document, new DragEvent('dragend', { bubbles: true }));

      expect(screen.getByText('Provider Content')).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    test('complete drag and drop workflow', async () => {
      const onDrop = jest.fn();
      const onDragStart = jest.fn();

      render(
        <DragDropProvider>
          <EnhancedDraggable 
            data={{ type: 'button', id: '1' }} 
            onDragStart={onDragStart}
          >
            <div>Draggable Button</div>
          </EnhancedDraggable>
          <EnhancedDropZone onDrop={onDrop}>
            <div>Drop Zone</div>
          </EnhancedDropZone>
        </DragDropProvider>
      );

      const draggableElement = screen.getByText('Draggable Button').closest('[draggable]');
      const dropZone = screen.getByText('Drop Zone').closest('div');

      // Start drag
      fireEvent.dragStart(draggableElement);
      expect(onDragStart).toHaveBeenCalled();

      // Drag over drop zone
      fireEvent.dragEnter(dropZone);
      fireEvent.dragOver(dropZone);

      // Drop
      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });
      
      Object.defineProperty(dropEvent, 'dataTransfer', {
        value: {
          getData: jest.fn(() => JSON.stringify({ type: 'button', id: '1' })),
        },
      });

      fireEvent(dropZone, dropEvent);

      expect(onDrop).toHaveBeenCalledWith(
        expect.objectContaining({
          data: { type: 'button', id: '1' },
        })
      );
    });

    test('handles multiple draggable items', () => {
      render(
        <DragDropProvider>
          <EnhancedDraggable data={{ type: 'button', id: '1' }}>
            <div>Button 1</div>
          </EnhancedDraggable>
          <EnhancedDraggable data={{ type: 'input', id: '2' }}>
            <div>Input 2</div>
          </EnhancedDraggable>
          <EnhancedDropZone onDrop={jest.fn()}>
            <div>Drop Zone</div>
          </EnhancedDropZone>
        </DragDropProvider>
      );

      expect(screen.getByText('Button 1')).toBeInTheDocument();
      expect(screen.getByText('Input 2')).toBeInTheDocument();
      expect(screen.getByText('Drop Zone')).toBeInTheDocument();
    });
  });
});
