<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Functionality Test - App Builder 201</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background: #10b981; }
        .status-connecting { background: #f59e0b; animation: pulse 1s infinite; }
        .status-disconnected { background: #ef4444; }
        .status-error { background: #dc2626; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
        .warning { background: #fef3c7; color: #92400e; }
        .info { background: #dbeafe; color: #1e40af; }
        .message-log {
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .metric {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }
        .metric-label {
            font-size: 12px;
            color: #6b7280;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket Functionality Test - App Builder 201</h1>
        
        <div class="test-grid">
            <div class="test-section">
                <h3>Connection Status</h3>
                <div id="connection-status">
                    <span class="status-indicator status-disconnected" id="status-indicator"></span>
                    <span id="status-text">Disconnected</span>
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-value" id="messages-sent">0</div>
                        <div class="metric-label">Messages Sent</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="messages-received">0</div>
                        <div class="metric-label">Messages Received</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="reconnect-count">0</div>
                        <div class="metric-label">Reconnects</div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>Connection Controls</h3>
                <div>
                    <label>WebSocket URL:</label>
                    <input type="text" id="ws-url" value="ws://localhost:8000/ws/test/">
                </div>
                <div>
                    <button onclick="connectWebSocket()" id="connect-btn">Connect</button>
                    <button onclick="disconnectWebSocket()" id="disconnect-btn" disabled>Disconnect</button>
                    <button onclick="clearLogs()">Clear Logs</button>
                </div>
            </div>

            <div class="test-section">
                <h3>Message Testing</h3>
                <div>
                    <label>Message Type:</label>
                    <select id="message-type">
                        <option value="test">Test Message</option>
                        <option value="echo">Echo Test</option>
                        <option value="ping">Ping</option>
                        <option value="app_data">App Data</option>
                        <option value="collaboration">Collaboration</option>
                    </select>
                </div>
                <div>
                    <label>Message Content:</label>
                    <textarea id="message-content" rows="3" placeholder="Enter message content...">{"test": "Hello WebSocket!"}</textarea>
                </div>
                <div>
                    <button onclick="sendMessage()" id="send-btn" disabled>Send Message</button>
                    <button onclick="sendPing()">Send Ping</button>
                    <button onclick="runEchoTest()">Run Echo Test</button>
                </div>
            </div>

            <div class="test-section">
                <h3>Endpoint Testing</h3>
                <div>
                    <button onclick="testEndpoint('ws://localhost:8000/ws/test/')">Test /ws/test/</button>
                    <button onclick="testEndpoint('ws://localhost:8000/ws/app_builder/')">Test /ws/app_builder/</button>
                    <button onclick="testEndpoint('ws://localhost:8000/ws/echo/')">Test /ws/echo/</button>
                    <button onclick="testEndpoint('ws://localhost:8000/ws/health/')">Test /ws/health/</button>
                </div>
                <div id="endpoint-results"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>Message Log</h3>
            <div class="message-log" id="message-log"></div>
        </div>

        <div class="test-section">
            <h3>Automated Tests</h3>
            <button onclick="runAllTests()">Run All WebSocket Tests</button>
            <button onclick="runStressTest()">Run Stress Test</button>
            <button onclick="runReconnectionTest()">Test Reconnection</button>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let messagesSent = 0;
        let messagesReceived = 0;
        let reconnectCount = 0;
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const logElement = document.getElementById('message-log');
            const logLine = document.createElement('div');
            logLine.className = type;
            logLine.textContent = logEntry;
            logElement.appendChild(logLine);
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function updateStatus(status, text) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        function updateMetrics() {
            document.getElementById('messages-sent').textContent = messagesSent;
            document.getElementById('messages-received').textContent = messagesReceived;
            document.getElementById('reconnect-count').textContent = reconnectCount;
        }

        function connectWebSocket() {
            const url = document.getElementById('ws-url').value;
            
            if (ws) {
                ws.close();
            }
            
            log(`Connecting to ${url}...`, 'info');
            updateStatus('connecting', 'Connecting...');
            
            ws = new WebSocket(url);
            
            ws.onopen = function(event) {
                log('WebSocket connected successfully', 'success');
                updateStatus('connected', 'Connected');
                
                document.getElementById('connect-btn').disabled = true;
                document.getElementById('disconnect-btn').disabled = false;
                document.getElementById('send-btn').disabled = false;
            };
            
            ws.onmessage = function(event) {
                messagesReceived++;
                updateMetrics();
                
                try {
                    const data = JSON.parse(event.data);
                    log(`Received: ${JSON.stringify(data, null, 2)}`, 'success');
                } catch (e) {
                    log(`Received (raw): ${event.data}`, 'success');
                }
            };
            
            ws.onclose = function(event) {
                log(`WebSocket closed: Code ${event.code}, Reason: ${event.reason}`, 'warning');
                updateStatus('disconnected', 'Disconnected');
                
                document.getElementById('connect-btn').disabled = false;
                document.getElementById('disconnect-btn').disabled = true;
                document.getElementById('send-btn').disabled = true;
            };
            
            ws.onerror = function(event) {
                log('WebSocket error occurred', 'error');
                updateStatus('error', 'Error');
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket not connected', 'error');
                return;
            }
            
            const messageType = document.getElementById('message-type').value;
            const messageContent = document.getElementById('message-content').value;
            
            let message;
            try {
                message = {
                    type: messageType,
                    data: JSON.parse(messageContent),
                    timestamp: new Date().toISOString()
                };
            } catch (e) {
                message = {
                    type: messageType,
                    data: messageContent,
                    timestamp: new Date().toISOString()
                };
            }
            
            ws.send(JSON.stringify(message));
            messagesSent++;
            updateMetrics();
            
            log(`Sent: ${JSON.stringify(message, null, 2)}`, 'info');
        }

        function sendPing() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket not connected', 'error');
                return;
            }
            
            const pingMessage = {
                type: 'ping',
                timestamp: new Date().toISOString()
            };
            
            ws.send(JSON.stringify(pingMessage));
            messagesSent++;
            updateMetrics();
            
            log('Ping sent', 'info');
        }

        function runEchoTest() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket not connected', 'error');
                return;
            }
            
            const echoMessage = {
                type: 'echo',
                data: {
                    test: 'Echo test message',
                    timestamp: new Date().toISOString(),
                    random: Math.random()
                }
            };
            
            ws.send(JSON.stringify(echoMessage));
            messagesSent++;
            updateMetrics();
            
            log('Echo test sent', 'info');
        }

        function testEndpoint(url) {
            log(`Testing endpoint: ${url}`, 'info');
            
            const testWs = new WebSocket(url);
            
            testWs.onopen = function() {
                log(`✅ ${url} - Connection successful`, 'success');
                testWs.send(JSON.stringify({type: 'test', data: 'endpoint test'}));
            };
            
            testWs.onmessage = function(event) {
                log(`✅ ${url} - Message received: ${event.data}`, 'success');
                testWs.close();
            };
            
            testWs.onerror = function() {
                log(`❌ ${url} - Connection failed`, 'error');
            };
            
            testWs.onclose = function(event) {
                if (event.code === 1000) {
                    log(`✅ ${url} - Test completed successfully`, 'success');
                } else {
                    log(`⚠️ ${url} - Closed with code ${event.code}`, 'warning');
                }
            };
        }

        function clearLogs() {
            document.getElementById('message-log').innerHTML = '';
            messagesSent = 0;
            messagesReceived = 0;
            reconnectCount = 0;
            updateMetrics();
        }

        async function runAllTests() {
            log('Starting comprehensive WebSocket tests...', 'info');
            
            const endpoints = [
                'ws://localhost:8000/ws/test/',
                'ws://localhost:8000/ws/app_builder/',
                'ws://localhost:8000/ws/echo/',
                'ws://localhost:8000/ws/health/'
            ];
            
            for (const endpoint of endpoints) {
                await testEndpointAsync(endpoint);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            log('All WebSocket tests completed', 'success');
        }

        function testEndpointAsync(url) {
            return new Promise((resolve) => {
                const testWs = new WebSocket(url);
                let resolved = false;
                
                const timeout = setTimeout(() => {
                    if (!resolved) {
                        log(`⏰ ${url} - Test timeout`, 'warning');
                        testWs.close();
                        resolved = true;
                        resolve();
                    }
                }, 5000);
                
                testWs.onopen = function() {
                    log(`✅ ${url} - Connected`, 'success');
                    testWs.send(JSON.stringify({type: 'test', data: 'automated test'}));
                };
                
                testWs.onmessage = function(event) {
                    log(`✅ ${url} - Response received`, 'success');
                    clearTimeout(timeout);
                    testWs.close();
                    if (!resolved) {
                        resolved = true;
                        resolve();
                    }
                };
                
                testWs.onerror = function() {
                    log(`❌ ${url} - Connection error`, 'error');
                    clearTimeout(timeout);
                    if (!resolved) {
                        resolved = true;
                        resolve();
                    }
                };
            });
        }

        function runStressTest() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('Please connect to WebSocket first', 'error');
                return;
            }
            
            log('Starting stress test - sending 100 messages...', 'info');
            
            for (let i = 0; i < 100; i++) {
                setTimeout(() => {
                    const message = {
                        type: 'stress_test',
                        data: {
                            sequence: i,
                            timestamp: new Date().toISOString(),
                            payload: 'x'.repeat(100) // 100 character payload
                        }
                    };
                    
                    ws.send(JSON.stringify(message));
                    messagesSent++;
                    updateMetrics();
                    
                    if (i === 99) {
                        log('Stress test completed', 'success');
                    }
                }, i * 10); // Send every 10ms
            }
        }

        function runReconnectionTest() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('Please connect to WebSocket first', 'error');
                return;
            }
            
            log('Testing reconnection - closing connection...', 'info');
            
            ws.close();
            
            setTimeout(() => {
                log('Attempting to reconnect...', 'info');
                reconnectCount++;
                updateMetrics();
                connectWebSocket();
            }, 2000);
        }

        // Initialize
        window.addEventListener('load', () => {
            log('WebSocket test page loaded', 'info');
            updateMetrics();
        });
    </script>
</body>
</html>
