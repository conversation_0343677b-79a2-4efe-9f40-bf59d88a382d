# Simple Docker Fix Script for App Builder 201
Write-Host "Docker Connection Fix Script" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

# Check if Docker CLI is available
Write-Host "`nChecking Docker CLI..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Docker CLI found - $dockerVersion" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Docker CLI not found" -ForegroundColor Red
        Write-Host "Please install Docker Desktop from: https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "ERROR: Docker CLI not available" -ForegroundColor Red
    exit 1
}

# Check if Docker service is running
Write-Host "`nChecking Docker service..." -ForegroundColor Yellow
try {
    docker info | Out-Null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Docker service is running" -ForegroundColor Green
        
        # Check containers
        Write-Host "`nChecking containers..." -ForegroundColor Yellow
        docker ps -a
        
        Write-Host "`nDocker is ready! You can now run:" -ForegroundColor Green
        Write-Host "  docker-compose up -d" -ForegroundColor Blue
        exit 0
    } else {
        Write-Host "ERROR: Docker service is not running" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: Cannot connect to Docker service" -ForegroundColor Red
}

# Try to start Docker Desktop
Write-Host "`nAttempting to start Docker Desktop..." -ForegroundColor Yellow

# Common Docker Desktop paths
$dockerPaths = @(
    "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
    "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
    "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
)

$dockerFound = $false
foreach ($path in $dockerPaths) {
    if (Test-Path $path) {
        Write-Host "Found Docker Desktop at: $path" -ForegroundColor Blue
        $dockerFound = $true
        
        try {
            Start-Process -FilePath $path -WindowStyle Hidden
            Write-Host "Docker Desktop start command executed" -ForegroundColor Green
            Write-Host "Please wait 30-60 seconds for Docker Desktop to start..." -ForegroundColor Yellow
            Write-Host "Then run this script again or try: docker-compose up -d" -ForegroundColor Blue
            break
        } catch {
            Write-Host "Failed to start Docker Desktop: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

if (-not $dockerFound) {
    Write-Host "Docker Desktop executable not found" -ForegroundColor Red
    Write-Host "`nTroubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Make sure Docker Desktop is installed" -ForegroundColor White
    Write-Host "2. Try starting Docker Desktop manually from Start menu" -ForegroundColor White
    Write-Host "3. If not installed, download from:" -ForegroundColor White
    Write-Host "   https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe" -ForegroundColor Blue
    Write-Host "4. After installation, restart your computer" -ForegroundColor White
    Write-Host "5. Enable WSL2 if prompted" -ForegroundColor White
}

Write-Host "`nIf Docker Desktop is starting, please wait and then try:" -ForegroundColor Cyan
Write-Host "  docker-compose up -d" -ForegroundColor Blue
