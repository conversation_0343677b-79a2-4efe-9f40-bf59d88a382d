/**
 * Security tests for the App Builder application
 * Tests for XSS, CSRF, injection attacks, and other security vulnerabilities
 */

import { test, expect } from '@playwright/test';

const APP_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Common XSS payloads for testing
const XSS_PAYLOADS = [
  '<script>alert("xss")</script>',
  '<img src="x" onerror="alert(\'xss\')">',
  'javascript:alert("xss")',
  '<svg onload="alert(\'xss\')">',
  '"><script>alert("xss")</script>',
  '\';alert("xss");//',
  '<iframe src="javascript:alert(\'xss\')"></iframe>',
  '<body onload="alert(\'xss\')">',
  '<input onfocus="alert(\'xss\')" autofocus>',
  '<select onfocus="alert(\'xss\')" autofocus>'
];

// SQL injection payloads
const SQL_INJECTION_PAYLOADS = [
  "' OR '1'='1",
  "'; DROP TABLE users; --",
  "' UNION SELECT * FROM users --",
  "admin'--",
  "admin'/*",
  "' OR 1=1#",
  "' OR 'a'='a",
  "') OR ('1'='1",
  "' OR 1=1 LIMIT 1 --"
];

// Command injection payloads
const COMMAND_INJECTION_PAYLOADS = [
  "; ls -la",
  "| cat /etc/passwd",
  "&& whoami",
  "`id`",
  "$(whoami)",
  "; cat /etc/hosts",
  "| ping -c 1 127.0.0.1"
];

test.describe('Security Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
  });

  test.describe('XSS Protection', () => {
    test('input fields are protected against XSS', async ({ page }) => {
      // Find all input fields
      const inputs = await page.locator('input[type="text"], input[type="email"], textarea').all();
      
      for (const input of inputs) {
        if (await input.isVisible() && await input.isEnabled()) {
          for (const payload of XSS_PAYLOADS.slice(0, 3)) { // Test first 3 payloads
            await input.fill(payload);
            await page.waitForTimeout(500);
            
            // Check that script didn't execute
            const alertDialogs = [];
            page.on('dialog', dialog => {
              alertDialogs.push(dialog.message());
              dialog.dismiss();
            });
            
            await page.waitForTimeout(1000);
            expect(alertDialogs).toHaveLength(0);
            
            // Check that payload is properly escaped in DOM
            const value = await input.inputValue();
            if (value.includes('<script>')) {
              const parentText = await input.locator('..').textContent();
              expect(parentText).not.toContain('<script>alert');
            }
            
            await input.clear();
          }
        }
      }
    });

    test('URL parameters are protected against XSS', async ({ page }) => {
      // Test XSS in URL parameters
      const xssUrl = `${APP_URL}?name=<script>alert('xss')</script>&id=123`;
      
      const alertDialogs = [];
      page.on('dialog', dialog => {
        alertDialogs.push(dialog.message());
        dialog.dismiss();
      });
      
      await page.goto(xssUrl);
      await page.waitForTimeout(2000);
      
      expect(alertDialogs).toHaveLength(0);
      
      // Check that XSS payload is not reflected in page content
      const pageContent = await page.content();
      expect(pageContent).not.toContain('<script>alert(\'xss\')</script>');
    });

    test('dynamic content rendering is safe', async ({ page }) => {
      // Test XSS in dynamically rendered content
      const componentName = '<img src="x" onerror="alert(\'xss\')">';
      
      // Try to create a component with malicious name
      const nameInput = page.locator('input[placeholder*="name"], input[name*="name"]').first();
      
      if (await nameInput.isVisible()) {
        const alertDialogs = [];
        page.on('dialog', dialog => {
          alertDialogs.push(dialog.message());
          dialog.dismiss();
        });
        
        await nameInput.fill(componentName);
        await page.keyboard.press('Enter');
        await page.waitForTimeout(2000);
        
        expect(alertDialogs).toHaveLength(0);
        
        // Check that malicious content is escaped
        const pageContent = await page.content();
        expect(pageContent).not.toContain('onerror="alert');
      }
    });
  });

  test.describe('CSRF Protection', () => {
    test('forms include CSRF tokens', async ({ page }) => {
      // Check for CSRF tokens in forms
      const forms = await page.locator('form').all();
      
      for (const form of forms) {
        if (await form.isVisible()) {
          // Look for CSRF token fields
          const csrfTokens = await form.locator('input[name*="csrf"], input[name*="token"]').all();
          
          if (csrfTokens.length === 0) {
            // Check if form uses other CSRF protection (headers, etc.)
            const formAction = await form.getAttribute('action');
            const formMethod = await form.getAttribute('method');
            
            if (formMethod && formMethod.toLowerCase() !== 'get') {
              console.warn(`Form without visible CSRF token: ${formAction}`);
              // This might be okay if using header-based CSRF protection
            }
          }
        }
      }
    });

    test('API requests include CSRF protection', async ({ page }) => {
      // Monitor network requests for CSRF headers
      const requests = [];
      
      page.on('request', request => {
        if (request.url().includes('/api/') && 
            ['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method())) {
          requests.push({
            url: request.url(),
            method: request.method(),
            headers: request.headers()
          });
        }
      });
      
      // Trigger some API calls by interacting with the app
      const buttons = await page.locator('button').all();
      for (const button of buttons.slice(0, 3)) {
        if (await button.isVisible()) {
          await button.click();
          await page.waitForTimeout(1000);
        }
      }
      
      // Check that API requests include CSRF protection
      for (const request of requests) {
        const hasCSRFHeader = 
          request.headers['x-csrftoken'] || 
          request.headers['x-csrf-token'] ||
          request.headers['csrf-token'];
        
        if (!hasCSRFHeader) {
          console.warn(`API request without CSRF header: ${request.method} ${request.url}`);
        }
      }
    });
  });

  test.describe('Input Validation', () => {
    test('SQL injection protection', async ({ page }) => {
      // Test SQL injection in search/filter inputs
      const searchInputs = await page.locator('input[type="search"], input[placeholder*="search"]').all();
      
      for (const input of searchInputs) {
        if (await input.isVisible()) {
          for (const payload of SQL_INJECTION_PAYLOADS.slice(0, 3)) {
            await input.fill(payload);
            await page.keyboard.press('Enter');
            await page.waitForTimeout(1000);
            
            // Check for SQL error messages
            const pageContent = await page.textContent('body');
            const sqlErrorPatterns = [
              /sql.*error/i,
              /mysql.*error/i,
              /postgresql.*error/i,
              /sqlite.*error/i,
              /syntax.*error/i,
              /database.*error/i
            ];
            
            for (const pattern of sqlErrorPatterns) {
              expect(pageContent).not.toMatch(pattern);
            }
            
            await input.clear();
          }
        }
      }
    });

    test('command injection protection', async ({ page }) => {
      // Test command injection in file upload or processing inputs
      const fileInputs = await page.locator('input[type="file"]').all();
      const textInputs = await page.locator('input[type="text"]').all();
      
      const testInputs = [...fileInputs, ...textInputs.slice(0, 2)];
      
      for (const input of testInputs) {
        if (await input.isVisible() && await input.isEnabled()) {
          for (const payload of COMMAND_INJECTION_PAYLOADS.slice(0, 2)) {
            if (input.getAttribute('type') !== 'file') {
              await input.fill(payload);
              await page.keyboard.press('Enter');
              await page.waitForTimeout(1000);
              
              // Check for command execution indicators
              const pageContent = await page.textContent('body');
              const commandOutputPatterns = [
                /root:/,
                /bin\/bash/,
                /etc\/passwd/,
                /127\.0\.0\.1/,
                /uid=\d+/
              ];
              
              for (const pattern of commandOutputPatterns) {
                expect(pageContent).not.toMatch(pattern);
              }
              
              await input.clear();
            }
          }
        }
      }
    });

    test('file upload security', async ({ page }) => {
      const fileInputs = await page.locator('input[type="file"]').all();
      
      for (const input of fileInputs) {
        if (await input.isVisible()) {
          // Test malicious file types
          const maliciousFiles = [
            'test.php',
            'test.jsp',
            'test.asp',
            'test.exe',
            'test.bat',
            'test.sh'
          ];
          
          // Note: In a real test, you'd create actual files with these extensions
          // For now, just check that the input has proper accept attributes
          const acceptAttr = await input.getAttribute('accept');
          
          if (acceptAttr) {
            // Should not accept executable file types
            expect(acceptAttr).not.toContain('.exe');
            expect(acceptAttr).not.toContain('.bat');
            expect(acceptAttr).not.toContain('.sh');
            expect(acceptAttr).not.toContain('.php');
          }
        }
      }
    });
  });

  test.describe('Authentication and Authorization', () => {
    test('sensitive pages require authentication', async ({ page }) => {
      // Test access to admin or sensitive areas without authentication
      const sensitiveUrls = [
        '/admin',
        '/dashboard',
        '/settings',
        '/api/admin',
        '/api/users'
      ];
      
      for (const url of sensitiveUrls) {
        try {
          const response = await page.goto(`${APP_URL}${url}`);
          
          if (response && response.status() === 200) {
            // Check if redirected to login
            const currentUrl = page.url();
            const isLoginPage = currentUrl.includes('login') || 
                              currentUrl.includes('auth') ||
                              currentUrl.includes('signin');
            
            if (!isLoginPage) {
              // Check for authentication indicators
              const pageContent = await page.textContent('body');
              const hasAuthContent = pageContent.includes('login') ||
                                   pageContent.includes('sign in') ||
                                   pageContent.includes('unauthorized');
              
              expect(hasAuthContent).toBeTruthy();
            }
          }
        } catch (error) {
          // 404 or other errors are acceptable for non-existent endpoints
          console.log(`URL ${url} returned error: ${error.message}`);
        }
      }
    });

    test('session management is secure', async ({ page }) => {
      // Check for secure session cookies
      const cookies = await page.context().cookies();
      
      const sessionCookies = cookies.filter(cookie => 
        cookie.name.toLowerCase().includes('session') ||
        cookie.name.toLowerCase().includes('auth') ||
        cookie.name.toLowerCase().includes('token')
      );
      
      for (const cookie of sessionCookies) {
        // Session cookies should be secure and httpOnly
        expect(cookie.secure).toBeTruthy();
        expect(cookie.httpOnly).toBeTruthy();
        
        // Should have SameSite protection
        expect(['Strict', 'Lax']).toContain(cookie.sameSite);
      }
    });
  });

  test.describe('Content Security Policy', () => {
    test('CSP headers are present and restrictive', async ({ page }) => {
      const response = await page.goto(APP_URL);
      const headers = response.headers();
      
      const cspHeader = headers['content-security-policy'] || 
                       headers['content-security-policy-report-only'];
      
      if (cspHeader) {
        // Check for restrictive CSP directives
        expect(cspHeader).toContain('default-src');
        expect(cspHeader).not.toContain("'unsafe-eval'");
        expect(cspHeader).not.toContain("'unsafe-inline'");
        
        console.log('CSP Header:', cspHeader);
      } else {
        console.warn('No Content Security Policy header found');
      }
    });

    test('inline scripts are blocked by CSP', async ({ page }) => {
      // Try to inject inline script
      await page.addScriptTag({
        content: 'window.inlineScriptExecuted = true;'
      }).catch(() => {
        // CSP should block this
      });
      
      await page.waitForTimeout(1000);
      
      const scriptExecuted = await page.evaluate(() => 
        window.inlineScriptExecuted === true
      );
      
      // Inline script should be blocked
      expect(scriptExecuted).toBeFalsy();
    });
  });

  test.describe('Information Disclosure', () => {
    test('error pages do not reveal sensitive information', async ({ page }) => {
      // Test 404 page
      const response = await page.goto(`${APP_URL}/nonexistent-page`);
      
      if (response && response.status() === 404) {
        const pageContent = await page.textContent('body');
        
        // Should not reveal server information
        const sensitivePatterns = [
          /apache/i,
          /nginx/i,
          /iis/i,
          /server.*version/i,
          /stack.*trace/i,
          /debug.*info/i,
          /database.*error/i,
          /file.*path/i,
          /c:\\/i,
          /\/var\/www/i,
          /\/home\//i
        ];
        
        for (const pattern of sensitivePatterns) {
          expect(pageContent).not.toMatch(pattern);
        }
      }
    });

    test('source maps are not exposed in production', async ({ page }) => {
      // Check for source map files
      const sourceMapUrls = [
        '/static/js/main.js.map',
        '/static/css/main.css.map',
        '/build/static/js/main.js.map'
      ];
      
      for (const url of sourceMapUrls) {
        try {
          const response = await page.goto(`${APP_URL}${url}`);
          
          if (response && response.status() === 200) {
            console.warn(`Source map exposed: ${url}`);
            // In production, source maps should not be accessible
            expect(response.status()).not.toBe(200);
          }
        } catch (error) {
          // 404 is expected for source maps in production
        }
      }
    });

    test('debug information is not exposed', async ({ page }) => {
      const pageContent = await page.content();
      
      // Check for debug information in page source
      const debugPatterns = [
        /console\.log/g,
        /console\.debug/g,
        /debugger;/g,
        /__DEV__/g,
        /development.*mode/i,
        /debug.*true/i
      ];
      
      for (const pattern of debugPatterns) {
        const matches = pageContent.match(pattern);
        if (matches && matches.length > 5) {
          console.warn(`Excessive debug code found: ${pattern} (${matches.length} instances)`);
        }
      }
    });
  });

  test.describe('Clickjacking Protection', () => {
    test('X-Frame-Options header is set', async ({ page }) => {
      const response = await page.goto(APP_URL);
      const headers = response.headers();
      
      const frameOptions = headers['x-frame-options'];
      
      if (frameOptions) {
        expect(['DENY', 'SAMEORIGIN']).toContain(frameOptions.toUpperCase());
      } else {
        // Check for CSP frame-ancestors directive
        const csp = headers['content-security-policy'];
        if (csp) {
          expect(csp).toContain('frame-ancestors');
        } else {
          console.warn('No clickjacking protection found');
        }
      }
    });
  });

  test.describe('HTTPS and Transport Security', () => {
    test('HSTS header is present', async ({ page }) => {
      const response = await page.goto(APP_URL);
      const headers = response.headers();
      
      const hsts = headers['strict-transport-security'];
      
      if (APP_URL.startsWith('https://')) {
        expect(hsts).toBeDefined();
        if (hsts) {
          expect(hsts).toContain('max-age=');
          console.log('HSTS Header:', hsts);
        }
      }
    });

    test('secure cookies are used over HTTPS', async ({ page }) => {
      if (APP_URL.startsWith('https://')) {
        await page.goto(APP_URL);
        
        const cookies = await page.context().cookies();
        
        for (const cookie of cookies) {
          if (cookie.name.toLowerCase().includes('session') ||
              cookie.name.toLowerCase().includes('auth')) {
            expect(cookie.secure).toBeTruthy();
          }
        }
      }
    });
  });
});
