"""
WebSocket consumer for AI suggestions
Handles real-time AI layout and component suggestions
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone
from core.app_logic import AILayoutSuggestionsEngine, AIComponentCombinationEngine
import os

logger = logging.getLogger(__name__)


class AISuggestionsConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for AI suggestions
    Provides real-time layout and component combination suggestions
    """

    async def connect(self):
        """
        Handle WebSocket connection
        """
        self.group_name = "ai_suggestions"
        self.user = self.scope.get('user')
        
        # Join the AI suggestions group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        # Accept the connection
        await self.accept()
        
        # Send welcome message
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connected to AI Suggestions WebSocket',
            'timestamp': timezone.now().isoformat(),
            'user': self.user.username if self.user and self.user.is_authenticated else 'anonymous'
        }))
        
        logger.info(f"AI Suggestions WebSocket connected: {self.channel_name}")

    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection
        """
        # Leave the AI suggestions group
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        
        logger.info(f"AI Suggestions WebSocket disconnected: {self.channel_name}")

    async def receive(self, text_data):
        """
        Handle incoming WebSocket messages
        """
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            logger.info(f"Received AI suggestions message: {message_type}")
            
            if message_type == 'get_layout_suggestions':
                await self.handle_layout_suggestions(data)
            elif message_type == 'get_component_combinations':
                await self.handle_component_combinations(data)
            elif message_type == 'analyze_app_structure':
                await self.handle_app_analysis(data)
            elif message_type == 'subscribe_to_updates':
                await self.handle_subscription(data)
            elif message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': data.get('timestamp', timezone.now().isoformat())
                }))
            else:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': f'Unknown message type: {message_type}',
                    'timestamp': timezone.now().isoformat()
                }))
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format',
                'timestamp': timezone.now().isoformat()
            }))
        except Exception as e:
            logger.error(f"Error handling AI suggestions message: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Internal error: {str(e)}',
                'timestamp': timezone.now().isoformat()
            }))

    async def handle_layout_suggestions(self, data):
        """
        Handle layout suggestions request
        """
        try:
            components = data.get('components', [])
            layouts = data.get('layouts', [])
            context = data.get('context', {})
            
            # Get API key
            api_key = os.environ.get('OPENAI_API_KEY')
            
            # Create layout engine and get suggestions
            layout_engine = AILayoutSuggestionsEngine(api_key=api_key)
            suggestions = await database_sync_to_async(layout_engine.suggest_layouts)(
                components, layouts, context
            )
            
            # Send suggestions back
            await self.send(text_data=json.dumps({
                'type': 'layout_suggestions',
                'suggestions': suggestions,
                'component_count': len(components),
                'timestamp': timezone.now().isoformat(),
                'status': 'success'
            }))
            
            # Broadcast to group if requested
            if data.get('broadcast', False):
                await self.channel_layer.group_send(
                    self.group_name,
                    {
                        'type': 'layout_suggestions_broadcast',
                        'suggestions': suggestions,
                        'component_count': len(components),
                        'sender': self.channel_name
                    }
                )
                
        except Exception as e:
            logger.error(f"Error generating layout suggestions: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Failed to generate layout suggestions: {str(e)}',
                'timestamp': timezone.now().isoformat()
            }))

    async def handle_component_combinations(self, data):
        """
        Handle component combinations request
        """
        try:
            components = data.get('components', [])
            selected_component = data.get('selected_component')
            context = data.get('context', {})
            
            # Get API key
            api_key = os.environ.get('OPENAI_API_KEY')
            
            # Create combination engine and get suggestions
            combination_engine = AIComponentCombinationEngine(api_key=api_key)
            suggestions = await database_sync_to_async(combination_engine.suggest_combinations)(
                components, selected_component, context
            )
            
            # Send suggestions back
            await self.send(text_data=json.dumps({
                'type': 'component_combinations',
                'suggestions': suggestions,
                'component_count': len(components),
                'selected_component': selected_component.get('type') if selected_component else None,
                'timestamp': timezone.now().isoformat(),
                'status': 'success'
            }))
            
            # Broadcast to group if requested
            if data.get('broadcast', False):
                await self.channel_layer.group_send(
                    self.group_name,
                    {
                        'type': 'component_combinations_broadcast',
                        'suggestions': suggestions,
                        'component_count': len(components),
                        'sender': self.channel_name
                    }
                )
                
        except Exception as e:
            logger.error(f"Error generating component combinations: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Failed to generate component combinations: {str(e)}',
                'timestamp': timezone.now().isoformat()
            }))

    async def handle_app_analysis(self, data):
        """
        Handle app structure analysis request
        """
        try:
            components = data.get('components', [])
            layouts = data.get('layouts', [])
            
            # Create layout engine for analysis
            layout_engine = AILayoutSuggestionsEngine()
            analysis = await database_sync_to_async(layout_engine.analyze_app_structure)(
                components, layouts
            )
            
            # Send analysis back
            await self.send(text_data=json.dumps({
                'type': 'app_analysis',
                'analysis': analysis,
                'timestamp': timezone.now().isoformat(),
                'status': 'success'
            }))
            
        except Exception as e:
            logger.error(f"Error analyzing app structure: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Failed to analyze app structure: {str(e)}',
                'timestamp': timezone.now().isoformat()
            }))

    async def handle_subscription(self, data):
        """
        Handle subscription to AI updates
        """
        subscription_type = data.get('subscription_type', 'all')
        
        await self.send(text_data=json.dumps({
            'type': 'subscription_confirmed',
            'subscription_type': subscription_type,
            'message': f'Subscribed to {subscription_type} AI updates',
            'timestamp': timezone.now().isoformat()
        }))

    # Group message handlers
    async def layout_suggestions_broadcast(self, event):
        """
        Handle layout suggestions broadcast to group
        """
        # Don't send to the sender
        if event.get('sender') != self.channel_name:
            await self.send(text_data=json.dumps({
                'type': 'layout_suggestions_broadcast',
                'suggestions': event['suggestions'],
                'component_count': event['component_count'],
                'timestamp': timezone.now().isoformat()
            }))

    async def component_combinations_broadcast(self, event):
        """
        Handle component combinations broadcast to group
        """
        # Don't send to the sender
        if event.get('sender') != self.channel_name:
            await self.send(text_data=json.dumps({
                'type': 'component_combinations_broadcast',
                'suggestions': event['suggestions'],
                'component_count': event['component_count'],
                'timestamp': timezone.now().isoformat()
            }))

    async def ai_suggestion_update(self, event):
        """
        Handle general AI suggestion updates
        """
        await self.send(text_data=json.dumps({
            'type': 'ai_suggestion_update',
            'update_type': event.get('update_type'),
            'data': event.get('data'),
            'timestamp': timezone.now().isoformat()
        }))
