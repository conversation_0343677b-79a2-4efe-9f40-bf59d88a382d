/**
 * Error Tracking System
 * 
 * This utility provides comprehensive error tracking and reporting functionality.
 * It captures unhandled errors, console errors, network errors, and more.
 */

// Configuration
const config = {
  // Whether error tracking is enabled
  enabled: true,

  // Sampling rate (0.0 to 1.0) - what percentage of errors to track
  samplingRate: 1.0,

  // Maximum number of errors to store
  errorLimit: 100,

  // Maximum number of breadcrumbs to store
  breadcrumbLimit: 50,

  // Errors to ignore (regexes)
  ignoredErrors: [
    /ResizeObserver loop limit exceeded/,
    /Loading chunk \d+ failed/,
    /Network request failed/,
    /Script error/,
    /Extension context invalidated/,
    /Failed to report error/,
    /Error reporting failed/,
    /TypeError: Failed to fetch/,
    /TypeError: NetworkError when attempting to fetch resource/,
    /AbortError/,
    /Request aborted/,
    /Request timed out/,
    /Load failed/
  ],

  // Endpoint to report errors to
  reportingEndpoint: '/api/errors',

  // Whether to log errors to console
  logToConsole: true,

  // Whether to capture console errors
  captureConsoleErrors: true,

  // Whether to capture network errors
  captureNetworkErrors: true,

  // Whether to capture unhandled rejections
  captureUnhandledRejections: true,

  // Whether to capture breadcrumbs
  captureBreadcrumbs: true
};

// Circuit breaker for error reporting
const circuitBreaker = {
  state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
  failureCount: 0,
  lastFailureTime: null,
  failureThreshold: 5, // Open circuit after 5 consecutive failures
  timeout: 60000, // 1 minute timeout before trying again
  successThreshold: 2 // Number of successes needed to close circuit
};

// Error storage
const errorStore = {
  errors: [],
  breadcrumbs: [],
  sessionId: generateSessionId(),
  startTime: new Date().toISOString(),
  reportingQueue: [], // Queue for failed reports
  lastReportAttempt: null,
  reportingInProgress: false
};

/**
 * Generate a unique session ID
 * 
 * @returns {string} - A unique session ID
 */
function generateSessionId() {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Initialize the error tracking system
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - The error tracking API
 */
export function initErrorTracking(options = {}) {
  // Merge options with default config
  Object.assign(config, options);

  if (!config.enabled) {
    console.log('Error tracking is disabled');
    return createPublicApi();
  }

  // Set up global error handlers
  setupErrorHandlers();

  // Log initialization
  console.log('Error tracking initialized');

  // Return the public API
  return createPublicApi();
}

/**
 * Set up global error handlers
 */
function setupErrorHandlers() {
  // Handle unhandled errors
  window.addEventListener('error', handleGlobalError);

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', handleUnhandledRejection);

  // Capture console errors
  if (config.captureConsoleErrors) {
    setupConsoleCapture();
  }

  // Capture network errors
  if (config.captureNetworkErrors) {
    setupNetworkCapture();
  }

  // Capture breadcrumbs
  if (config.captureBreadcrumbs) {
    setupBreadcrumbCapture();
  }
}

/**
 * Handle global errors
 * 
 * @param {ErrorEvent} event - The error event
 */
function handleGlobalError(event) {
  // Prevent default browser error handling
  event.preventDefault();

  // Track the error
  trackError({
    type: 'uncaught_error',
    message: event.message || 'Unknown error',
    stack: event.error ? event.error.stack : null,
    source: event.filename,
    line: event.lineno,
    column: event.colno,
    timestamp: new Date().toISOString()
  });

  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Uncaught error:', event.message);
  }
}

/**
 * Handle unhandled promise rejections
 * 
 * @param {PromiseRejectionEvent} event - The rejection event
 */
function handleUnhandledRejection(event) {
  // Prevent default browser error handling
  event.preventDefault();

  // Get error details
  const error = event.reason;
  const message = error instanceof Error ? error.message : String(error);
  const stack = error instanceof Error ? error.stack : null;

  // Track the error
  trackError({
    type: 'unhandled_rejection',
    message: message || 'Unhandled promise rejection',
    stack: stack,
    timestamp: new Date().toISOString()
  });

  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Unhandled rejection:', message);
  }
}

/**
 * Set up console error capture
 */
function setupConsoleCapture() {
  // Save original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  // Override console.error
  console.error = function (...args) {
    // Call original method
    originalConsoleError.apply(console, args);

    // Track the error
    const message = args.map(arg =>
      typeof arg === 'string' ? arg : JSON.stringify(arg)
    ).join(' ');

    trackError({
      type: 'console_error',
      message: message,
      timestamp: new Date().toISOString()
    });
  };

  // Override console.warn
  console.warn = function (...args) {
    // Call original method
    originalConsoleWarn.apply(console, args);

    // Add breadcrumb
    if (config.captureBreadcrumbs) {
      addBreadcrumb({
        type: 'console_warn',
        message: args.map(arg =>
          typeof arg === 'string' ? arg : JSON.stringify(arg)
        ).join(' '),
        timestamp: new Date().toISOString()
      });
    }
  };
}

/**
 * Set up network error capture
 */
function setupNetworkCapture() {
  // Store original fetch for error reporting
  if (!window._originalFetch) {
    window._originalFetch = window.fetch;
  }
  const originalFetch = window._originalFetch;

  // Override fetch
  window.fetch = async function (...args) {
    try {
      const response = await originalFetch.apply(window, args);

      // Add breadcrumb for successful requests
      if (config.captureBreadcrumbs) {
        addBreadcrumb({
          type: 'network',
          category: 'fetch',
          data: {
            url: typeof args[0] === 'string' ? args[0] : args[0].url,
            method: args[1]?.method || 'GET',
            status: response.status
          },
          timestamp: new Date().toISOString()
        });
      }

      // Track error responses
      if (!response.ok) {
        trackError({
          type: 'network_error',
          message: `Fetch error: ${response.status} ${response.statusText}`,
          data: {
            url: typeof args[0] === 'string' ? args[0] : args[0].url,
            method: args[1]?.method || 'GET',
            status: response.status,
            statusText: response.statusText
          },
          timestamp: new Date().toISOString()
        });
      }

      return response;
    } catch (error) {
      // Track network errors
      trackError({
        type: 'network_error',
        message: `Fetch failed: ${error.message}`,
        stack: error.stack,
        data: {
          url: typeof args[0] === 'string' ? args[0] : args[0]?.url
        },
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  };

  // Override XMLHttpRequest
  const originalXhrOpen = XMLHttpRequest.prototype.open;
  const originalXhrSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function (method, url) {
    this._errorTracking = {
      method,
      url
    };
    return originalXhrOpen.apply(this, arguments);
  };

  XMLHttpRequest.prototype.send = function () {
    // Add event listeners
    this.addEventListener('load', function () {
      // Add breadcrumb for successful requests
      if (config.captureBreadcrumbs) {
        addBreadcrumb({
          type: 'network',
          category: 'xhr',
          data: {
            url: this._errorTracking?.url,
            method: this._errorTracking?.method,
            status: this.status
          },
          timestamp: new Date().toISOString()
        });
      }

      // Track error responses
      if (this.status >= 400) {
        trackError({
          type: 'network_error',
          message: `XHR error: ${this.status} ${this.statusText}`,
          data: {
            url: this._errorTracking?.url,
            method: this._errorTracking?.method,
            status: this.status,
            statusText: this.statusText
          },
          timestamp: new Date().toISOString()
        });
      }
    });

    this.addEventListener('error', function () {
      // Track network errors
      trackError({
        type: 'network_error',
        message: 'XHR failed',
        data: {
          url: this._errorTracking?.url,
          method: this._errorTracking?.method
        },
        timestamp: new Date().toISOString()
      });
    });

    return originalXhrSend.apply(this, arguments);
  };
}

/**
 * Set up breadcrumb capture
 */
function setupBreadcrumbCapture() {
  // Capture clicks
  document.addEventListener('click', function (event) {
    // Get the clicked element
    const element = event.target;

    // Get element details
    const tagName = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const classes = Array.from(element.classList).map(c => `.${c}`).join('');
    const text = element.innerText?.substring(0, 50);

    // Add breadcrumb
    addBreadcrumb({
      type: 'user',
      category: 'click',
      data: {
        element: `${tagName}${id}${classes}`,
        text: text
      },
      timestamp: new Date().toISOString()
    });
  });

  // Capture navigation
  window.addEventListener('popstate', function () {
    addBreadcrumb({
      type: 'navigation',
      data: {
        from: document.referrer,
        to: window.location.href
      },
      timestamp: new Date().toISOString()
    });
  });
}

/**
 * Track an error
 * 
 * @param {Object} error - The error to track
 */
function trackError(error) {
  // Check if error tracking is enabled
  if (!config.enabled) {
    return;
  }

  // Apply sampling rate
  if (Math.random() > config.samplingRate) {
    return;
  }

  // Check if error should be ignored
  if (shouldIgnoreError(error)) {
    return;
  }

  // Add session information
  error.sessionId = errorStore.sessionId;

  // Add user agent
  error.userAgent = navigator.userAgent;

  // Add URL
  error.url = window.location.href;

  // Add breadcrumbs
  error.breadcrumbs = [...errorStore.breadcrumbs];

  // Add to error store
  errorStore.errors.push(error);

  // Limit the number of stored errors
  if (errorStore.errors.length > config.errorLimit) {
    errorStore.errors.shift();
  }

  // Report the error
  reportError(error);
}

/**
 * Check if an error should be ignored
 * 
 * @param {Object} error - The error to check
 * @returns {boolean} - Whether the error should be ignored
 */
function shouldIgnoreError(error) {
  // Check against ignored errors
  return config.ignoredErrors.some(pattern => {
    if (pattern instanceof RegExp) {
      return pattern.test(error.message);
    }
    return error.message.includes(pattern);
  });
}

/**
 * Add a breadcrumb
 * 
 * @param {Object} breadcrumb - The breadcrumb to add
 */
function addBreadcrumb(breadcrumb) {
  // Check if breadcrumb tracking is enabled
  if (!config.captureBreadcrumbs) {
    return;
  }

  // Add to breadcrumb store
  errorStore.breadcrumbs.push(breadcrumb);

  // Limit the number of stored breadcrumbs
  if (errorStore.breadcrumbs.length > config.breadcrumbLimit) {
    errorStore.breadcrumbs.shift();
  }
}

/**
 * Check if circuit breaker allows error reporting
 * @returns {boolean} - Whether reporting is allowed
 */
function canReportError() {
  const now = Date.now();

  switch (circuitBreaker.state) {
    case 'OPEN':
      // Check if timeout has passed
      if (now - circuitBreaker.lastFailureTime >= circuitBreaker.timeout) {
        circuitBreaker.state = 'HALF_OPEN';
        return true;
      }
      return false;

    case 'HALF_OPEN':
    case 'CLOSED':
      return true;

    default:
      return true;
  }
}

/**
 * Handle successful error reporting
 */
function onReportSuccess() {
  if (circuitBreaker.state === 'HALF_OPEN') {
    circuitBreaker.successThreshold--;
    if (circuitBreaker.successThreshold <= 0) {
      circuitBreaker.state = 'CLOSED';
      circuitBreaker.failureCount = 0;
      circuitBreaker.successThreshold = 2; // Reset
    }
  } else if (circuitBreaker.state === 'CLOSED') {
    circuitBreaker.failureCount = 0;
  }
}

/**
 * Handle failed error reporting
 */
function onReportFailure() {
  circuitBreaker.failureCount++;
  circuitBreaker.lastFailureTime = Date.now();

  if (circuitBreaker.failureCount >= circuitBreaker.failureThreshold) {
    circuitBreaker.state = 'OPEN';
  }
}

/**
 * Store error locally as fallback when reporting fails
 * @param {Object} error - The error to store
 */
function storeErrorLocally(error) {
  try {
    const key = 'errorTracker_failedReports';
    const stored = localStorage.getItem(key);
    const failedReports = stored ? JSON.parse(stored) : [];

    // Add timestamp and limit storage
    failedReports.push({
      ...error,
      storedAt: new Date().toISOString()
    });

    // Keep only last 50 failed reports
    if (failedReports.length > 50) {
      failedReports.splice(0, failedReports.length - 50);
    }

    localStorage.setItem(key, JSON.stringify(failedReports));
  } catch (storageError) {
    // Ignore storage errors
    if (config.logToConsole && process.env.NODE_ENV === 'development') {
      console.warn('Failed to store error locally:', storageError);
    }
  }
}

/**
 * Report an error to the server with circuit breaker and retry logic
 *
 * @param {Object} error - The error to report
 * @param {number} retryCount - Current retry attempt (default: 0)
 * @param {number} maxRetries - Maximum number of retries (default: 3)
 */
function reportError(error, retryCount = 0, maxRetries = 3) {
  // Check if reporting is enabled
  if (!config.reportingEndpoint) {
    return;
  }

  // Check circuit breaker
  if (!canReportError()) {
    // Add to queue for later retry
    errorStore.reportingQueue.push(error);
    return;
  }

  // Prevent reporting errors about error reporting to avoid infinite loops
  if (error.type === 'error_reporting_failure' ||
    error.message?.includes('Failed to report error') ||
    error.url?.includes(config.reportingEndpoint)) {
    return;
  }

  // Prevent concurrent reporting
  if (errorStore.reportingInProgress) {
    errorStore.reportingQueue.push(error);
    return;
  }

  errorStore.reportingInProgress = true;
  errorStore.lastReportAttempt = Date.now();

  // Calculate delay for exponential backoff
  const delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Max 30 seconds

  const attemptReport = () => {
    // Use original fetch to avoid infinite loops with network error tracking
    const originalFetch = window._originalFetch || window.fetch;

    originalFetch(config.reportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(error),
      keepalive: true
    })
      .then(response => {
        if (response.ok) {
          onReportSuccess();

          // Process queued errors if circuit is now closed
          if (circuitBreaker.state === 'CLOSED' && errorStore.reportingQueue.length > 0) {
            const queuedError = errorStore.reportingQueue.shift();
            setTimeout(() => reportError(queuedError), 100);
          }
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      })
      .catch(err => {
        onReportFailure();

        // Retry with exponential backoff if we haven't exceeded max retries
        if (retryCount < maxRetries) {
          setTimeout(() => {
            reportError(error, retryCount + 1, maxRetries);
          }, delay);
        } else {
          // Store in local storage as fallback
          storeErrorLocally(error);

          // Log final failure only in development
          if (config.logToConsole && process.env.NODE_ENV === 'development') {
            console.warn('Error reporting failed after all retries:', err.message);
          }
        }
      })
      .finally(() => {
        errorStore.reportingInProgress = false;
      });
  };

  // Apply initial delay for retries
  if (retryCount > 0) {
    setTimeout(attemptReport, delay);
  } else {
    attemptReport();
  }
}

/**
 * Create the public API
 * 
 * @returns {Object} - The public API
 */
function createPublicApi() {
  return {
    /**
     * Track an error manually
     * 
     * @param {Error|string} error - The error to track
     * @param {Object} additionalData - Additional data to include
     */
    trackError: function (error, additionalData = {}) {
      // Convert error to the right format
      const errorObj = error instanceof Error
        ? {
          type: 'manual',
          message: error.message,
          stack: error.stack,
          ...additionalData,
          timestamp: new Date().toISOString()
        }
        : {
          type: 'manual',
          message: String(error),
          ...additionalData,
          timestamp: new Date().toISOString()
        };

      // Track the error
      trackError(errorObj);
    },

    /**
     * Add a breadcrumb manually
     * 
     * @param {string} message - The breadcrumb message
     * @param {string} category - The breadcrumb category
     * @param {Object} data - Additional data to include
     */
    addBreadcrumb: function (message, category = 'manual', data = {}) {
      addBreadcrumb({
        type: 'manual',
        category,
        message,
        data,
        timestamp: new Date().toISOString()
      });
    },

    /**
     * Get all tracked errors
     * 
     * @returns {Array} - The tracked errors
     */
    getErrors: function () {
      return [...errorStore.errors];
    },

    /**
     * Get all breadcrumbs
     * 
     * @returns {Array} - The breadcrumbs
     */
    getBreadcrumbs: function () {
      return [...errorStore.breadcrumbs];
    },

    /**
     * Clear all tracked errors
     */
    clearErrors: function () {
      errorStore.errors = [];
    },

    /**
     * Clear all breadcrumbs
     */
    clearBreadcrumbs: function () {
      errorStore.breadcrumbs = [];
    },

    /**
     * Get the current configuration
     * 
     * @returns {Object} - The current configuration
     */
    getConfig: function () {
      return { ...config };
    },

    /**
     * Update the configuration
     *
     * @param {Object} options - The new configuration options
     */
    updateConfig: function (options) {
      Object.assign(config, options);
    },

    /**
     * Get circuit breaker status
     *
     * @returns {Object} - Circuit breaker status
     */
    getCircuitBreakerStatus: function () {
      return {
        state: circuitBreaker.state,
        failureCount: circuitBreaker.failureCount,
        lastFailureTime: circuitBreaker.lastFailureTime,
        queueLength: errorStore.reportingQueue.length
      };
    },

    /**
     * Manually retry queued errors
     *
     * @returns {Promise<void>} - Promise that resolves when retry is complete
     */
    retryQueuedErrors: function () {
      return new Promise((resolve) => {
        if (errorStore.reportingQueue.length === 0) {
          resolve();
          return;
        }

        // Reset circuit breaker to allow retries
        if (circuitBreaker.state === 'OPEN') {
          circuitBreaker.state = 'HALF_OPEN';
        }

        // Process one queued error
        const queuedError = errorStore.reportingQueue.shift();
        reportError(queuedError);

        // Wait a bit and resolve
        setTimeout(resolve, 1000);
      });
    },

    /**
     * Get locally stored failed reports
     *
     * @returns {Array} - Array of failed reports from localStorage
     */
    getLocallyStoredErrors: function () {
      try {
        const key = 'errorTracker_failedReports';
        const stored = localStorage.getItem(key);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        return [];
      }
    },

    /**
     * Clear locally stored failed reports
     */
    clearLocallyStoredErrors: function () {
      try {
        localStorage.removeItem('errorTracker_failedReports');
      } catch (error) {
        // Ignore storage errors
      }
    }
  };
}

// Create and export the default instance
export default initErrorTracking();
