/**
 * Service Accessibility Checker
 *
 * This utility checks if all services and functions in the application are accessible.
 * It tests API endpoints, WebSocket connections, GraphQL API, and other services.
 */

import API_ENDPOINTS from '../config/api';
import { getWebSocketUrl } from './websocket';

// Export the class directly for use in other components
export { ServiceAccessibilityChecker as checkServiceAccessibility };

class ServiceAccessibilityChecker {
  constructor() {
    this.results = {
      api: {
        status: 'pending',
        endpoints: {},
        message: ''
      },
      websocket: {
        status: 'pending',
        endpoints: {},
        message: ''
      },
      graphql: {
        status: 'pending',
        message: ''
      },
      serviceWorker: {
        status: 'pending',
        message: ''
      }
    };

    this.websocketConnections = {};
  }

  /**
   * Check if all services are accessible
   * @returns {Promise<Object>} Results of all checks
   */
  async checkAll() {
    try {
      // Run all checks in parallel
      await Promise.all([
        this.checkApiEndpoints(),
        this.checkWebSocketConnections(),
        this.checkGraphQLEndpoint(),
        this.checkServiceWorker()
      ]);

      return this.results;
    } catch (error) {
      console.error('Error checking services:', error);
      return {
        ...this.results,
        error: error.message
      };
    }
  }

  /**
   * Check if API endpoints are accessible
   * @returns {Promise<void>}
   */
  async checkApiEndpoints() {
    try {
      const endpoints = [
        'STATUS',
        'HEALTH',
        'APP_DATA',
        'API_V1',
        'GRAPHQL'
      ];

      const results = await Promise.all(
        endpoints.map(async (endpoint) => {
          try {
            const url = API_ENDPOINTS[endpoint];
            if (!url) {
              return { endpoint, status: 'error', message: 'Endpoint URL not defined' };
            }

            const response = await fetch(url, { method: 'GET' });
            return {
              endpoint,
              status: response.ok ? 'success' : 'error',
              statusCode: response.status,
              message: response.ok ? 'Accessible' : `HTTP ${response.status}`
            };
          } catch (error) {
            return { endpoint, status: 'error', message: error.message };
          }
        })
      );

      // Update results
      const endpointResults = {};
      results.forEach(result => {
        endpointResults[result.endpoint] = {
          status: result.status,
          message: result.message
        };
      });

      this.results.api = {
        status: results.every(r => r.status === 'success') ? 'success' : 'error',
        endpoints: endpointResults,
        message: results.every(r => r.status === 'success')
          ? 'All API endpoints are accessible'
          : 'Some API endpoints are not accessible'
      };
    } catch (error) {
      this.results.api = {
        status: 'error',
        endpoints: {},
        message: `Error checking API endpoints: ${error.message}`
      };
    }
  }

  /**
   * Check if WebSocket connections are accessible
   * @returns {Promise<void>}
   */
  async checkWebSocketConnections() {
    try {
      const endpoints = [
        'app_builder',
        'test',
        'health',
        'echo'
      ];

      const results = await Promise.all(
        endpoints.map(endpoint => this.testWebSocketConnection(endpoint))
      );

      // Update results
      const endpointResults = {};
      results.forEach(result => {
        endpointResults[result.endpoint] = {
          status: result.status,
          message: result.message
        };
      });

      this.results.websocket = {
        status: results.every(r => r.status === 'success') ? 'success' : 'error',
        endpoints: endpointResults,
        message: results.every(r => r.status === 'success')
          ? 'All WebSocket endpoints are accessible'
          : 'Some WebSocket endpoints are not accessible'
      };
    } catch (error) {
      this.results.websocket = {
        status: 'error',
        endpoints: {},
        message: `Error checking WebSocket connections: ${error.message}`
      };
    }
  }

  /**
   * Test a WebSocket connection
   * @param {string} endpoint - WebSocket endpoint
   * @returns {Promise<Object>} Test result
   */
  testWebSocketConnection(endpoint) {
    return new Promise((resolve) => {
      try {
        const url = getWebSocketUrl(endpoint);
        const socket = new WebSocket(url);
        let timeoutId;

        // Store the socket
        this.websocketConnections[endpoint] = socket;

        // Set a timeout
        timeoutId = setTimeout(() => {
          socket.close();
          resolve({
            endpoint,
            status: 'error',
            message: 'Connection timeout'
          });
        }, 5000);

        socket.onopen = () => {
          clearTimeout(timeoutId);

          // Send a test message
          socket.send(JSON.stringify({
            type: 'ping',
            message: 'Test connection'
          }));

          // Set a timeout for response
          timeoutId = setTimeout(() => {
            socket.close();
            resolve({
              endpoint,
              status: 'warning',
              message: 'Connected but no response to ping'
            });
          }, 2000);
        };

        socket.onmessage = (event) => {
          clearTimeout(timeoutId);
          socket.close();
          resolve({
            endpoint,
            status: 'success',
            message: 'Connected and received response'
          });
        };

        socket.onerror = (error) => {
          clearTimeout(timeoutId);
          socket.close();
          resolve({
            endpoint,
            status: 'error',
            message: 'Connection error'
          });
        };
      } catch (error) {
        resolve({
          endpoint,
          status: 'error',
          message: error.message
        });
      }
    });
  }

  /**
   * Check if GraphQL endpoint is accessible
   * @returns {Promise<void>}
   */
  async checkGraphQLEndpoint() {
    try {
      const url = API_ENDPOINTS.GRAPHQL;
      if (!url) {
        this.results.graphql = {
          status: 'error',
          message: 'GraphQL endpoint URL not defined'
        };
        return;
      }

      // Simple introspection query
      const query = `
        {
          __schema {
            queryType {
              name
            }
          }
        }
      `;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      if (response.ok) {
        const data = await response.json();
        this.results.graphql = {
          status: 'success',
          message: 'GraphQL endpoint is accessible'
        };
      } else {
        this.results.graphql = {
          status: 'error',
          message: `GraphQL endpoint returned HTTP ${response.status}`
        };
      }
    } catch (error) {
      this.results.graphql = {
        status: 'error',
        message: `Error checking GraphQL endpoint: ${error.message}`
      };
    }
  }

  /**
   * Check if Service Worker is registered and active
   * @returns {Promise<void>}
   */
  async checkServiceWorker() {
    try {
      if (!('serviceWorker' in navigator)) {
        this.results.serviceWorker = {
          status: 'error',
          message: 'Service Worker is not supported in this browser'
        };
        return;
      }

      const registrations = await navigator.serviceWorker.getRegistrations();

      if (registrations.length === 0) {
        this.results.serviceWorker = {
          status: 'warning',
          message: 'No Service Worker is registered'
        };
        return;
      }

      // Check if any service worker is active
      const activeWorker = registrations.find(reg =>
        reg.active && reg.active.state === 'activated'
      );

      if (activeWorker) {
        this.results.serviceWorker = {
          status: 'success',
          message: 'Service Worker is registered and active'
        };
      } else {
        this.results.serviceWorker = {
          status: 'warning',
          message: 'Service Worker is registered but not active'
        };
      }
    } catch (error) {
      this.results.serviceWorker = {
        status: 'error',
        message: `Error checking Service Worker: ${error.message}`
      };
    }
  }

  /**
   * Clean up resources
   */
  cleanup() {
    // Close any open WebSocket connections
    Object.values(this.websocketConnections).forEach(socket => {
      if (socket && socket.readyState !== WebSocket.CLOSED) {
        socket.close();
      }
    });
  }
}

export default ServiceAccessibilityChecker;
