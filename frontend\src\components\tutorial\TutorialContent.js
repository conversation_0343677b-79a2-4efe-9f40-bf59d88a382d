/**
 * Tutorial Content Definitions
 * 
 * Contains all predefined tutorials for the App Builder application,
 * organized by difficulty level and feature area.
 */

import React from 'react';
import { Typography, Space, Alert } from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  BgColorsOutlined,
  CodeOutlined,
  DragOutlined,
  SettingOutlined,
  EyeOutlined,
  ApiOutlined,
  BookOutlined,
  RocketOutlined
} from '@ant-design/icons';
import {
  createTutorial,
  createTutorialStep,
  TUTORIAL_CATEGORIES,
  TUTORIAL_STEP_TYPES
} from './types';
import { tutorialValidations, tutorialEventHandlers } from './VisualOnboardingHelpers';

const { Text, Title } = Typography;

// Tutorial Content Definitions
export const TUTORIAL_DEFINITIONS = {
  // BEGINNER TUTORIALS
  getting_started: createTutorial({
    id: 'getting_started',
    title: 'Getting Started with App Builder',
    description: 'Learn the basics of creating applications with our drag-and-drop interface.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 5,
    difficulty: 1,
    icon: <BookOutlined />,
    isRequired: true,
    steps: [
      createTutorialStep({
        id: 'welcome',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Welcome to App Builder!',
        content: 'App Builder lets you create applications visually using drag-and-drop components. Let\'s start with a quick tour of the interface.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'component_palette',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Component Palette',
        content: 'This is the component palette. Here you\'ll find all the building blocks for your application - buttons, text, forms, and more.',
        targetSelector: '[data-help-context="component-palette"]',
        position: 'right'
      }),
      createTutorialStep({
        id: 'preview_area',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Preview Area',
        content: 'This is your canvas! Drag components from the palette and drop them here to build your application.',
        targetSelector: '[data-help-context="preview-area"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'property_editor',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Property Editor',
        content: 'When you select a component, its properties appear here. You can customize colors, text, sizes, and behavior.',
        targetSelector: '[data-help-context="property-editor"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'first_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Try It Yourself!',
        content: 'Now try adding your first component! Drag a Button from the component palette to the preview area.',
        targetSelector: '[data-help-context="preview-area"]',
        requiredAction: 'add_component',
        validationFn: () => document.querySelectorAll('[data-component-id]').length > 0
      })
    ]
  }),

  drag_drop_basics: createTutorial({
    id: 'drag_drop_basics',
    title: 'Mastering Drag & Drop',
    description: 'Learn advanced drag-and-drop techniques for building complex layouts.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 7,
    difficulty: 2,
    icon: <DragOutlined />,
    prerequisites: ['getting_started'],
    steps: [
      createTutorialStep({
        id: 'drag_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Drag & Drop Mastery',
        content: 'Let\'s explore the powerful drag-and-drop features that make App Builder so intuitive.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'drag_component',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Dragging Components',
        content: 'Click and hold on any component in the palette, then drag it to the preview area. You\'ll see a visual indicator showing where it will be placed.',
        targetSelector: '[data-component-type="button"]',
        position: 'right'
      }),
      createTutorialStep({
        id: 'drop_zones',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Drop Zones',
        content: 'As you drag, you\'ll see highlighted drop zones. These show you where you can place the component.',
        targetSelector: '[data-help-context="preview-area"]',
        position: 'top'
      }),
      createTutorialStep({
        id: 'rearrange_components',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Rearranging Components',
        content: 'You can also rearrange existing components! Try dragging a component to a new position.',
        targetSelector: '[data-component-id]',
        requiredAction: 'rearrange_component'
      })
    ]
  }),

  component_properties: createTutorial({
    id: 'component_properties',
    title: 'Customizing Component Properties',
    description: 'Learn how to customize components using the property editor.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 8,
    difficulty: 2,
    icon: <SettingOutlined />,
    prerequisites: ['getting_started'],
    steps: [
      createTutorialStep({
        id: 'select_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Selecting Components',
        content: 'First, click on a component in the preview area to select it. You\'ll see it highlighted and its properties will appear on the right.',
        targetSelector: '[data-component-id]',
        requiredAction: 'select_component'
      }),
      createTutorialStep({
        id: 'basic_properties',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Basic Properties',
        content: 'Every component has basic properties like name and type. These help you organize your application.',
        targetSelector: '[data-property-group="basic"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'style_properties',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Style Properties',
        content: 'Style properties control how your component looks - colors, sizes, spacing, and more.',
        targetSelector: '[data-property-group="style"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'behavior_properties',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Behavior Properties',
        content: 'Behavior properties control how your component acts - click handlers, form validation, and interactions.',
        targetSelector: '[data-property-group="behavior"]',
        position: 'left'
      })
    ]
  }),

  // INTERMEDIATE TUTORIALS
  layout_design: createTutorial({
    id: 'layout_design',
    title: 'Creating Responsive Layouts',
    description: 'Master the layout system to create responsive, professional-looking applications.',
    category: TUTORIAL_CATEGORIES.INTERMEDIATE,
    estimatedDuration: 12,
    difficulty: 3,
    icon: <LayoutOutlined />,
    prerequisites: ['getting_started', 'component_properties'],
    steps: [
      createTutorialStep({
        id: 'layout_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Layout Design',
        content: 'Layouts help you organize components in rows, columns, and grids. Let\'s learn how to create professional layouts.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'container_components',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Container Components',
        content: 'Container components like Flex and Grid help you organize other components. They act as invisible structures that control positioning.',
        targetSelector: '[data-component-category="layout"]',
        position: 'right'
      }),
      createTutorialStep({
        id: 'responsive_design',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Responsive Design',
        content: 'Use the device preview to see how your layout looks on different screen sizes. Your layouts should work on mobile, tablet, and desktop.',
        targetSelector: '[data-device-selector]',
        position: 'bottom'
      })
    ]
  }),

  theme_customization: createTutorial({
    id: 'theme_customization',
    title: 'Theme Customization',
    description: 'Learn to create and apply custom themes to give your application a unique look.',
    category: TUTORIAL_CATEGORIES.INTERMEDIATE,
    estimatedDuration: 10,
    difficulty: 3,
    icon: <BgColorsOutlined />,
    prerequisites: ['component_properties'],
    steps: [
      createTutorialStep({
        id: 'theme_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Theme System',
        content: 'Themes let you define consistent colors, fonts, and styles across your entire application.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'theme_manager',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Theme Manager',
        content: 'Access the Theme Manager to create, edit, and apply themes to your application.',
        targetSelector: '[data-view="themes"]',
        position: 'bottom'
      })
    ]
  }),

  preview_features: createTutorial({
    id: 'preview_features',
    title: 'Real-time Preview Features',
    description: 'Explore the powerful preview system with multi-device support and real-time updates.',
    category: TUTORIAL_CATEGORIES.INTERMEDIATE,
    estimatedDuration: 8,
    difficulty: 3,
    icon: <EyeOutlined />,
    prerequisites: ['layout_design'],
    steps: [
      createTutorialStep({
        id: 'preview_modes',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Preview Modes',
        content: 'Switch between different device views to see how your application looks on mobile, tablet, and desktop.',
        targetSelector: '[data-device-controls]',
        position: 'bottom'
      }),
      createTutorialStep({
        id: 'real_time_updates',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Real-time Updates',
        content: 'Changes you make are instantly reflected in the preview. This helps you see exactly how your application will look to users.',
        autoAdvance: true,
        autoAdvanceDelay: 4000
      })
    ]
  }),

  // ADVANCED TUTORIALS
  code_export: createTutorial({
    id: 'code_export',
    title: 'Code Export & Deployment',
    description: 'Learn to export your application as code and deploy it to production.',
    category: TUTORIAL_CATEGORIES.ADVANCED,
    estimatedDuration: 15,
    difficulty: 4,
    icon: <CodeOutlined />,
    prerequisites: ['layout_design', 'theme_customization'],
    steps: [
      createTutorialStep({
        id: 'export_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Code Export',
        content: 'App Builder can export your visual design as clean, production-ready code in multiple frameworks.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'export_options',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Export Options',
        content: 'Choose from React, Vue, Angular, and more. Each export includes all your components, styles, and layouts.',
        targetSelector: '[data-export-framework]',
        position: 'left'
      })
    ]
  }),

  websocket_features: createTutorial({
    id: 'websocket_features',
    title: 'Real-time Features with WebSockets',
    description: 'Implement real-time functionality using WebSocket connections.',
    category: TUTORIAL_CATEGORIES.ADVANCED,
    estimatedDuration: 12,
    difficulty: 4,
    icon: <ApiOutlined />,
    prerequisites: ['preview_features'],
    steps: [
      createTutorialStep({
        id: 'websocket_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'WebSocket Integration',
        content: 'WebSockets enable real-time communication between your application and server for live updates and collaboration.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'websocket_manager',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'WebSocket Manager',
        content: 'Use the WebSocket Manager to configure and test real-time connections.',
        targetSelector: '[data-view="websocket"]',
        position: 'bottom'
      })
    ]
  }),

  advanced_components: createTutorial({
    id: 'advanced_components',
    title: 'Advanced Component Techniques',
    description: 'Master advanced component features like custom properties and complex interactions.',
    category: TUTORIAL_CATEGORIES.ADVANCED,
    estimatedDuration: 18,
    difficulty: 5,
    icon: <RocketOutlined />,
    prerequisites: ['component_properties', 'code_export'],
    steps: [
      createTutorialStep({
        id: 'custom_properties',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Custom Properties',
        content: 'Learn to create custom properties for components to extend their functionality beyond the built-in options.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'component_interactions',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Component Interactions',
        content: 'Set up complex interactions between components using events and state management.',
        targetSelector: '[data-property-type="interaction"]',
        position: 'left'
      })
    ]
  }),

  // VISUAL ONBOARDING TUTORIAL
  visual_onboarding: createTutorial({
    id: 'visual_onboarding',
    title: 'Visual App Builder Onboarding',
    description: 'A comprehensive visual guide through the App Builder interface with interactive demonstrations.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 8,
    difficulty: 1,
    icon: <RocketOutlined />,
    isRequired: true,
    isOnboarding: true,
    steps: [
      createTutorialStep({
        id: 'welcome',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: '🎉 Welcome to App Builder!',
        content: 'Welcome to the most intuitive way to build applications! This interactive tutorial will guide you through the main features of App Builder. You\'ll learn how to create beautiful applications using our drag-and-drop interface.',
        showPrevious: false,
        autoAdvance: false,
        customComponent: 'WelcomeModal'
      }),

      createTutorialStep({
        id: 'interface_overview',
        type: TUTORIAL_STEP_TYPES.OVERLAY,
        title: '🏗️ App Builder Interface Overview',
        content: 'Let\'s start with a quick overview of the App Builder interface. The interface is divided into three main areas that work together to help you build amazing applications.',
        showPrevious: true,
        autoAdvance: false
      }),

      createTutorialStep({
        id: 'component_palette_intro',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: '🎨 Component Palette',
        content: 'This is the Component Palette - your toolbox for building applications! Here you\'ll find all the components you can add to your app, organized by categories like Layout, Forms, and Data Display.',
        targetSelector: '[data-tutorial-target="component-palette"]',
        position: 'right',
        highlightPadding: 12,
        highlightBorderRadius: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'preview_area_intro',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: '👁️ Preview Area',
        content: 'This is the Preview Area - where your application comes to life! This is where you\'ll see your components as you build them. You can interact with components here and see exactly how your app will look.',
        targetSelector: '[data-tutorial-target="preview-area"]',
        position: 'left',
        highlightPadding: 12,
        highlightBorderRadius: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'property_editor_intro',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: '⚙️ Property Editor',
        content: 'This is the Property Editor - your control center for customizing components! When you select a component, this panel will show all the properties you can modify, like colors, text, sizes, and behavior.',
        targetSelector: '[data-tutorial-target="property-editor"]',
        position: 'left',
        highlightPadding: 12,
        highlightBorderRadius: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'add_first_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: '🚀 Add Your First Component',
        content: 'Now let\'s add your first component! Click on the "Button" component in the Component Palette to add it to your application. Look for the button icon in the "Basic Components" section.',
        targetSelector: '[data-tutorial-target="component-button"]',
        position: 'right',
        requiredAction: 'click',
        validationFn: tutorialValidations.validateComponentAdded,
        highlightPadding: 8,
        showPrevious: true,
        onEnter: tutorialEventHandlers.highlightComponentButton,
        onExit: tutorialEventHandlers.clearHighlights
      }),

      createTutorialStep({
        id: 'component_added_success',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: '🎉 Great Job!',
        content: 'Excellent! You\'ve successfully added your first component. Notice how the button appeared in the Preview Area and is now selected (highlighted with a blue border). The Property Editor on the right is now showing the button\'s properties.',
        showPrevious: false,
        autoAdvance: false,
        customComponent: 'SuccessModal'
      }),

      createTutorialStep({
        id: 'select_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: '🎯 Select Components',
        content: 'Click on the button you just added in the Preview Area to select it. When a component is selected, you\'ll see a blue border around it and its properties will appear in the Property Editor.',
        targetSelector: '[data-tutorial-target="preview-area"] .component-wrapper:first-child',
        position: 'top',
        requiredAction: 'click',
        validationFn: tutorialValidations.validateComponentSelected,
        highlightPadding: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'edit_properties',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: '✏️ Edit Component Properties',
        content: 'Now let\'s customize your button! In the Property Editor, find the "Text" field and change it from "Button" to "My First Button". This will update the button text in real-time.',
        targetSelector: '[data-tutorial-target="property-text-input"]',
        position: 'left',
        requiredAction: 'input',
        validationFn: tutorialValidations.validateTextChanged,
        highlightPadding: 8,
        showPrevious: true,
        onEnter: tutorialEventHandlers.focusTextInput
      }),

      createTutorialStep({
        id: 'change_button_type',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: '🎨 Change Button Style',
        content: 'Let\'s make your button stand out! In the Property Editor, find the "Type" dropdown and change it from "default" to "primary". Watch how the button\'s appearance changes instantly!',
        targetSelector: '[data-tutorial-target="property-type-select"]',
        position: 'left',
        requiredAction: 'select',
        validationFn: tutorialValidations.validateButtonTypeChanged,
        highlightPadding: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'add_second_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: '📝 Add a Text Component',
        content: 'Let\'s add another component! Click on the "Typography" component in the Component Palette to add some text above your button. You\'ll find it in the "Basic Components" section.',
        targetSelector: '[data-tutorial-target="component-typography"]',
        position: 'right',
        requiredAction: 'click',
        validationFn: tutorialValidations.validateSecondComponentAdded,
        highlightPadding: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'preview_mode',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: '👀 Preview Your App',
        content: 'Now let\'s see how your app looks to end users! Click the "Preview" button in the top toolbar to switch to preview mode. This hides the editing interface and shows your app as users will see it.',
        targetSelector: '[data-tutorial-target="preview-mode-button"]',
        position: 'bottom',
        requiredAction: 'click',
        validationFn: tutorialValidations.validatePreviewMode,
        highlightPadding: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'exit_preview',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: '🔧 Return to Edit Mode',
        content: 'Great! This is how your app looks to users. To continue editing, click the "Edit" button to return to the editing interface.',
        targetSelector: '[data-tutorial-target="edit-mode-button"]',
        position: 'bottom',
        requiredAction: 'click',
        validationFn: tutorialValidations.validateEditMode,
        highlightPadding: 8,
        showPrevious: true
      }),

      createTutorialStep({
        id: 'drag_and_drop_intro',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: '🖱️ Drag and Drop',
        content: 'Pro tip: You can also drag components directly from the Component Palette to the Preview Area! Try dragging a component to see this in action. You can also rearrange components by dragging them within the Preview Area.',
        targetSelector: '[data-tutorial-target="component-palette"]',
        position: 'right',
        highlightPadding: 12,
        showPrevious: true,
        autoAdvance: true,
        autoAdvanceDelay: 4000
      }),

      createTutorialStep({
        id: 'tutorial_complete',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: '🎊 Congratulations!',
        content: 'You\'ve completed the App Builder onboarding tutorial! You now know how to:\n\n• Navigate the three main areas\n• Add components to your app\n• Select and edit component properties\n• Preview your application\n• Use drag and drop\n\nYou\'re ready to start building amazing applications!',
        showPrevious: false,
        autoAdvance: false,
        customComponent: 'CompletionModal',
        onEnter: tutorialEventHandlers.celebrateCompletion
      })
    ]
  }),

  app_builder_intro: createTutorial({
    id: 'app_builder_intro',
    title: 'App Builder Introduction',
    description: 'Learn the basics of App Builder with this interactive tutorial.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 6,
    difficulty: 1,
    icon: <AppstoreOutlined />,
    isRequired: true,
    steps: [
      createTutorialStep({
        id: 'welcome',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Welcome to App Builder!',
        content: 'This tutorial will guide you through the basics of creating applications. Let\'s get started!',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'component_palette_intro',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Component Palette',
        content: 'This is the Component Palette. You\'ll find all the building blocks for your application here.',
        targetSelector: '[data-help-context="component-palette"]',
        position: 'right'
      }),
      createTutorialStep({
        id: 'add_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Add Your First Component',
        content: 'Try adding a component! Drag a Button from the palette and drop it onto the preview area.',
        targetSelector: '[data-component-type="button"]',
        position: 'right',
        requiredAction: 'add_component',
        validationFn: () => document.querySelectorAll('[data-component-id]').length > 0
      }),
      createTutorialStep({
        id: 'preview_area_intro',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Preview Area',
        content: 'This is the Preview Area where you can see your application taking shape.',
        targetSelector: '[data-help-context="preview-area"]',
        position: 'bottom'
      }),
      createTutorialStep({
        id: 'select_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Select Your Component',
        content: 'Click on the component you just added to select it and view its properties.',
        targetSelector: '[data-component-id]',
        position: 'bottom',
        requiredAction: 'select_component'
      }),
      createTutorialStep({
        id: 'property_editor_intro',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Property Editor',
        content: 'The Property Editor lets you customize your component. Try changing some properties!',
        targetSelector: '[data-help-context="property-editor"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'modify_property',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Customize Your Component',
        content: 'Change the text of your button or its color using the property editor.',
        targetSelector: '[data-property-group="style"]',
        position: 'left',
        requiredAction: 'modify_property'
      }),
      createTutorialStep({
        id: 'preview_device',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Device Preview',
        content: 'You can preview how your app will look on different devices using these controls.',
        targetSelector: '[data-device-selector]',
        position: 'top'
      }),
      createTutorialStep({
        id: 'completion',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Congratulations!',
        content: 'You\'ve completed the App Builder introduction! Continue exploring or check out more tutorials from the help menu.',
        showNext: false
      })
    ]
  })
};

// Tutorial Categories for Organization
export const TUTORIAL_CATEGORIES_CONFIG = {
  [TUTORIAL_CATEGORIES.BEGINNER]: {
    title: 'Beginner',
    description: 'Start here if you\'re new to App Builder',
    color: '#52c41a',
    icon: <BookOutlined />,
    tutorials: ['getting_started', 'drag_drop_basics', 'component_properties']
  },
  [TUTORIAL_CATEGORIES.INTERMEDIATE]: {
    title: 'Intermediate',
    description: 'Build more complex applications',
    color: '#1890ff',
    icon: <LayoutOutlined />,
    tutorials: ['layout_design', 'theme_customization', 'preview_features']
  },
  [TUTORIAL_CATEGORIES.ADVANCED]: {
    title: 'Advanced',
    description: 'Master advanced features and deployment',
    color: '#f5222d',
    icon: <RocketOutlined />,
    tutorials: ['code_export', 'websocket_features', 'advanced_components']
  }
};

// Feature-specific tutorials
export const FEATURE_TUTORIALS = {
  'component-palette': ['getting_started', 'drag_drop_basics'],
  'preview-area': ['getting_started', 'preview_features'],
  'property-editor': ['component_properties', 'advanced_components'],
  'theme-manager': ['theme_customization'],
  'layout-designer': ['layout_design'],
  'code-export': ['code_export'],
  'websocket': ['websocket_features']
};

// Tutorial Learning Paths
export const LEARNING_PATHS = {
  complete_beginner: {
    title: 'Complete Beginner Path',
    description: 'Perfect for users who are new to visual app building',
    tutorials: ['getting_started', 'drag_drop_basics', 'component_properties', 'layout_design'],
    estimatedDuration: 32
  },
  designer_path: {
    title: 'Designer Path',
    description: 'Focus on visual design and user experience',
    tutorials: ['getting_started', 'component_properties', 'theme_customization', 'layout_design', 'preview_features'],
    estimatedDuration: 43
  },
  developer_path: {
    title: 'Developer Path',
    description: 'Learn to export code and implement advanced features',
    tutorials: ['getting_started', 'layout_design', 'code_export', 'websocket_features', 'advanced_components'],
    estimatedDuration: 62
  }
};

// Helper function to get tutorials by category
export const getTutorialsByCategory = (category) => {
  return Object.values(TUTORIAL_DEFINITIONS).filter(tutorial => tutorial.category === category);
};

// Helper function to get recommended next tutorials
export const getRecommendedTutorials = (completedTutorials = []) => {
  const available = Object.values(TUTORIAL_DEFINITIONS).filter(tutorial => {
    // Check if tutorial is already completed
    if (completedTutorials.includes(tutorial.id)) return false;

    // Check if prerequisites are met
    return tutorial.prerequisites.every(prereq => completedTutorials.includes(prereq));
  });

  // Sort by difficulty and category
  return available.sort((a, b) => {
    if (a.difficulty !== b.difficulty) return a.difficulty - b.difficulty;
    return a.category.localeCompare(b.category);
  });
};

export default TUTORIAL_DEFINITIONS;

