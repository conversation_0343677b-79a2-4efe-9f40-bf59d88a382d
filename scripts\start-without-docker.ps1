# Start App Builder 201 without Docker
Write-Host "Starting App Builder 201 (No Docker Mode)" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

# Check if we're in the right directory
if (-not (Test-Path "backend") -or -not (Test-Path "frontend")) {
    Write-Host "ERROR: Please run this script from the project root directory" -ForegroundColor Red
    exit 1
}

Write-Host "`nThis will start the application components individually:" -ForegroundColor Yellow
Write-Host "- Backend (Django) on port 8000" -ForegroundColor White
Write-Host "- Frontend (React) on port 3000" -ForegroundColor White
Write-Host "- Database (SQLite - no setup needed)" -ForegroundColor White

$response = Read-Host "`nContinue? (y/N)"
if ($response -ne "y" -and $response -ne "Y") {
    Write-Host "Cancelled" -ForegroundColor Yellow
    exit 0
}

# Start Backend
Write-Host "`nStarting Backend..." -ForegroundColor Green
try {
    # Check if Python is available
    python --version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Python not found. Please install Python 3.9+" -ForegroundColor Red
        exit 1
    }
    
    # Navigate to backend
    Push-Location backend
    
    # Create virtual environment if it doesn't exist
    if (-not (Test-Path "venv")) {
        Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
        python -m venv venv
    }
    
    # Activate virtual environment
    Write-Host "Activating virtual environment..." -ForegroundColor Yellow
    & "venv\Scripts\Activate.ps1"
    
    # Install dependencies
    Write-Host "Installing Python dependencies..." -ForegroundColor Yellow
    pip install -r requirements.txt
    
    # Run migrations
    Write-Host "Running database migrations..." -ForegroundColor Yellow
    python manage.py migrate
    
    # Start backend server in background
    Write-Host "Starting Django server on port 8000..." -ForegroundColor Yellow
    Start-Process -FilePath "python" -ArgumentList "manage.py", "runserver", "0.0.0.0:8000" -WindowStyle Hidden
    
    Pop-Location
    Write-Host "Backend started successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "ERROR starting backend: $($_.Exception.Message)" -ForegroundColor Red
    Pop-Location
    exit 1
}

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start Frontend
Write-Host "`nStarting Frontend..." -ForegroundColor Green
try {
    # Check if Node.js is available
    node --version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Node.js not found. Please install Node.js 18+" -ForegroundColor Red
        exit 1
    }
    
    # Navigate to frontend
    Push-Location frontend
    
    # Install dependencies
    Write-Host "Installing Node.js dependencies..." -ForegroundColor Yellow
    npm install
    
    # Start frontend server
    Write-Host "Starting React development server on port 3000..." -ForegroundColor Yellow
    Start-Process -FilePath "npm" -ArgumentList "start" -WindowStyle Hidden
    
    Pop-Location
    Write-Host "Frontend started successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "ERROR starting frontend: $($_.Exception.Message)" -ForegroundColor Red
    Pop-Location
    exit 1
}

# Wait for services to be ready
Write-Host "`nWaiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if services are running
Write-Host "`nChecking service status..." -ForegroundColor Blue

# Check backend
try {
    $backendResponse = Invoke-WebRequest -Uri "http://localhost:8000/health/" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($backendResponse.StatusCode -eq 200) {
        Write-Host "SUCCESS: Backend is running on http://localhost:8000" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Backend may still be starting..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "WARNING: Backend not ready yet (this is normal)" -ForegroundColor Yellow
}

# Check frontend
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "SUCCESS: Frontend is running on http://localhost:3000" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Frontend may still be starting..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "WARNING: Frontend not ready yet (this is normal)" -ForegroundColor Yellow
}

Write-Host "`nApplication Started!" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host ""
Write-Host "URLs:" -ForegroundColor Cyan
Write-Host "  Frontend: http://localhost:3000" -ForegroundColor Blue
Write-Host "  Backend:  http://localhost:8000" -ForegroundColor Blue
Write-Host "  API Health: http://localhost:8000/health/" -ForegroundColor Blue
Write-Host ""
Write-Host "The application may take 1-2 minutes to fully start." -ForegroundColor Yellow
Write-Host "Check the URLs above to verify everything is working." -ForegroundColor Yellow
Write-Host ""
Write-Host "To stop the application:" -ForegroundColor Cyan
Write-Host "  1. Close this PowerShell window" -ForegroundColor White
Write-Host "  2. Or run: Get-Process python,node | Stop-Process" -ForegroundColor White
Write-Host ""
Write-Host "Press Ctrl+C to exit this script (services will continue running)" -ForegroundColor Yellow

# Keep script running to show status
try {
    while ($true) {
        Start-Sleep -Seconds 30
        Write-Host "Services still running... (Press Ctrl+C to exit)" -ForegroundColor Gray
    }
} catch {
    Write-Host "`nScript terminated. Services are still running in background." -ForegroundColor Yellow
}
