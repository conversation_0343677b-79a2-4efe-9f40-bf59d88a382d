<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Buster & React Debug Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .success { 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
        }
        .error { 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #dc3545;
        }
        .warning { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffc107;
        }
        .info { 
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-color: #17a2b8;
        }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-weight: 500;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 13px;
            line-height: 1.4;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Cache Buster & React Debug Test</h1>
        <p>This test helps identify caching issues and verifies React loading</p>

        <div class="test-section info">
            <h3>📋 What This Test Does</h3>
            <ul>
                <li>🧹 Clears browser cache and service worker cache</li>
                <li>🔄 Forces fresh reload of all resources</li>
                <li>🔍 Checks React global availability</li>
                <li>📦 Verifies bundle loading</li>
                <li>🏗️ Tests DOM mounting</li>
                <li>⚠️ Identifies caching issues</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎮 Cache & Debug Controls</h3>
            <button onclick="clearAllCaches()">🧹 Clear All Caches</button>
            <button onclick="forceReload()">🔄 Force Reload</button>
            <button onclick="testReactAfterClear()">🔍 Test React After Clear</button>
            <button onclick="checkServiceWorkerCache()">📦 Check SW Cache</button>
            <button onclick="runFullDiagnostic()">🚀 Run Full Diagnostic</button>
        </div>

        <div class="test-section" id="resultsSection">
            <h3>📊 Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>📝 Diagnostic Log</h3>
            <div id="log" class="log">Ready to run cache and React diagnostics...\n</div>
        </div>

        <div class="test-section">
            <h3>🔧 Manual Commands</h3>
            <p>Copy and paste these commands into the browser console:</p>
            <div class="code-block">
// Clear all caches
caches.keys().then(names => names.forEach(name => caches.delete(name)));

// Check React availability
console.log('React available:', typeof window.React);
console.log('ReactDOM available:', typeof window.ReactDOM);

// Force reload without cache
location.reload(true);
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function addResult(testName, success, message, details = '') {
            const resultsElement = document.getElementById('results');
            const statusClass = success ? 'success' : 'error';
            const statusIcon = success ? '✅' : '❌';
            
            const resultHtml = `
                <div class="test-section ${statusClass}">
                    <h4>${statusIcon} ${testName}</h4>
                    <p><strong>Result:</strong> ${message}</p>
                    ${details ? `<div class="code-block">${details}</div>` : ''}
                </div>
            `;
            
            resultsElement.innerHTML += resultHtml;
        }

        async function clearAllCaches() {
            log('🧹 Clearing all caches...');
            
            try {
                // Clear Cache API
                const cacheNames = await caches.keys();
                log(`Found ${cacheNames.length} cache(s): ${cacheNames.join(', ')}`);
                
                for (const cacheName of cacheNames) {
                    await caches.delete(cacheName);
                    log(`✅ Deleted cache: ${cacheName}`);
                }
                
                // Clear localStorage
                localStorage.clear();
                log('✅ Cleared localStorage');
                
                // Clear sessionStorage
                sessionStorage.clear();
                log('✅ Cleared sessionStorage');
                
                // Clear IndexedDB (if possible)
                if ('indexedDB' in window) {
                    log('ℹ️ IndexedDB detected (manual clearing may be needed)');
                }
                
                addResult('Cache Clearing', true, 'All caches cleared successfully');
                log('🎉 All caches cleared successfully!');
                
            } catch (error) {
                log(`❌ Error clearing caches: ${error.message}`);
                addResult('Cache Clearing', false, `Error: ${error.message}`);
            }
        }

        function forceReload() {
            log('🔄 Forcing page reload without cache...');
            
            // Add cache-busting parameter
            const url = new URL(window.location);
            url.searchParams.set('cacheBust', Date.now());
            
            // Force reload
            window.location.href = url.toString();
        }

        function testReactAfterClear() {
            log('🔍 Testing React availability after cache clear...');
            
            // Test React global
            const reactAvailable = typeof window.React !== 'undefined';
            const reactDOMAvailable = typeof window.ReactDOM !== 'undefined';
            
            if (reactAvailable) {
                const version = window.React.version || 'Unknown';
                addResult('React After Clear', true, `React available - Version: ${version}`);
                log(`✅ React available globally - Version: ${version}`);
            } else {
                addResult('React After Clear', false, 'React still not available after cache clear');
                log('❌ React still not available after cache clear');
            }
            
            if (reactDOMAvailable) {
                addResult('ReactDOM After Clear', true, 'ReactDOM available');
                log('✅ ReactDOM available globally');
            } else {
                addResult('ReactDOM After Clear', false, 'ReactDOM not available');
                log('❌ ReactDOM not available');
            }
            
            // Check root element
            const rootElement = document.getElementById('root');
            if (rootElement) {
                addResult('Root Element', true, `Root element found with ${rootElement.children.length} children`);
                log(`✅ Root element found with ${rootElement.children.length} children`);
            } else {
                addResult('Root Element', false, 'Root element not found');
                log('❌ Root element not found');
            }
        }

        async function checkServiceWorkerCache() {
            log('📦 Checking Service Worker cache...');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        log(`✅ Service Worker registered: ${registration.scope}`);
                        
                        // Check cache contents
                        const cacheNames = await caches.keys();
                        for (const cacheName of cacheNames) {
                            const cache = await caches.open(cacheName);
                            const requests = await cache.keys();
                            log(`Cache "${cacheName}" contains ${requests.length} entries`);
                            
                            // Check for main bundle
                            const mainBundles = requests.filter(req => 
                                req.url.includes('main') || 
                                req.url.includes('bundle') ||
                                req.url.includes('chunk')
                            );
                            
                            if (mainBundles.length > 0) {
                                log(`  Found ${mainBundles.length} bundle(s) in cache`);
                                mainBundles.forEach(bundle => {
                                    log(`    - ${bundle.url}`);
                                });
                            }
                        }
                        
                        addResult('Service Worker Cache', true, `Found ${cacheNames.length} cache(s)`);
                    } else {
                        log('ℹ️ No Service Worker registered');
                        addResult('Service Worker Cache', true, 'No Service Worker registered');
                    }
                } catch (error) {
                    log(`❌ Error checking Service Worker: ${error.message}`);
                    addResult('Service Worker Cache', false, `Error: ${error.message}`);
                }
            } else {
                log('❌ Service Worker not supported');
                addResult('Service Worker Cache', false, 'Service Worker not supported');
            }
        }

        async function runFullDiagnostic() {
            log('🚀 Running full diagnostic...');
            document.getElementById('results').innerHTML = '';
            
            // Step 1: Check current state
            log('\n1️⃣ Checking current state...');
            testReactAfterClear();
            
            // Step 2: Check service worker cache
            log('\n2️⃣ Checking Service Worker cache...');
            await checkServiceWorkerCache();
            
            // Step 3: Clear caches
            log('\n3️⃣ Clearing all caches...');
            await clearAllCaches();
            
            // Step 4: Wait and test again
            log('\n4️⃣ Waiting 2 seconds and testing again...');
            setTimeout(() => {
                testReactAfterClear();
                log('\n📊 Full diagnostic complete!');
                log('If React is still not available, try the force reload button.');
            }, 2000);
        }

        // Auto-run basic check when page loads
        window.addEventListener('load', () => {
            log('🔧 Cache Buster & React Debug Test loaded');
            log('Click "Run Full Diagnostic" to start comprehensive testing');
            
            // Quick initial check
            setTimeout(() => {
                const reactAvailable = typeof window.React !== 'undefined';
                log(`Initial React check: ${reactAvailable ? '✅ Available' : '❌ Not Available'}`);
                
                if (!reactAvailable) {
                    log('⚠️ React not available - this may be a caching issue');
                    log('Try running "Clear All Caches" followed by "Force Reload"');
                }
            }, 1000);
        });
    </script>
</body>
</html>
