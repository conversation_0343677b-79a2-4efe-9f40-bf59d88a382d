/**
 * Auto-save hook for App Builder
 * 
 * Provides automatic saving functionality with debouncing and error handling
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import dataSyncService from '../services/DataSyncService';

/**
 * Hook for automatic saving of data
 * @param {Object} options - Configuration options
 * @returns {Object} Auto-save state and methods
 */
export const useAutoSave = (options = {}) => {
  const {
    data = null,
    saveFunction = null,
    debounceMs = 2000,
    enabled = true,
    onSave = null,
    onError = null,
    showMessages = true
  } = options;

  // State
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [saveError, setSaveError] = useState(null);

  // Refs
  const debounceTimeoutRef = useRef(null);
  const lastDataRef = useRef(null);
  const saveCountRef = useRef(0);

  /**
   * Perform the save operation
   */
  const performSave = useCallback(async (dataToSave) => {
    if (!saveFunction || !dataToSave) {
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      const result = await saveFunction(dataToSave);
      
      setLastSaved(Date.now());
      setHasUnsavedChanges(false);
      saveCountRef.current += 1;

      if (showMessages) {
        message.success('Changes saved automatically', 2);
      }

      if (onSave) {
        onSave(result, dataToSave);
      }

      return result;
    } catch (error) {
      setSaveError(error);
      
      if (showMessages) {
        message.error('Failed to save changes', 3);
      }

      if (onError) {
        onError(error, dataToSave);
      }

      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [saveFunction, onSave, onError, showMessages]);

  /**
   * Debounced save function
   */
  const debouncedSave = useCallback((dataToSave) => {
    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      performSave(dataToSave);
    }, debounceMs);
  }, [performSave, debounceMs]);

  /**
   * Force immediate save
   */
  const forceSave = useCallback(async (dataToSave = data) => {
    // Clear any pending debounced save
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }

    return await performSave(dataToSave);
  }, [performSave, data]);

  /**
   * Check if data has changed and trigger save
   */
  useEffect(() => {
    if (!enabled || !data || !saveFunction) {
      return;
    }

    // Compare with last data
    const dataString = JSON.stringify(data);
    const lastDataString = lastDataRef.current ? JSON.stringify(lastDataRef.current) : null;

    if (dataString !== lastDataString) {
      setHasUnsavedChanges(true);
      lastDataRef.current = data;
      
      // Trigger debounced save
      debouncedSave(data);
    }
  }, [data, enabled, saveFunction, debouncedSave]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  /**
   * Handle page unload - try to save unsaved changes
   */
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (hasUnsavedChanges && enabled) {
        // Try to save immediately (synchronously)
        if (data && saveFunction) {
          try {
            // For page unload, we need to use synchronous save
            // This is a best-effort attempt
            forceSave(data);
          } catch (error) {
            console.error('Failed to save on page unload:', error);
          }
        }

        // Show browser warning
        const message = 'You have unsaved changes. Are you sure you want to leave?';
        event.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, enabled, data, saveFunction, forceSave]);

  /**
   * Get save status text
   */
  const getSaveStatusText = useCallback(() => {
    if (isSaving) {
      return 'Saving...';
    } else if (saveError) {
      return 'Save failed';
    } else if (hasUnsavedChanges) {
      return 'Unsaved changes';
    } else if (lastSaved) {
      const timeDiff = Date.now() - lastSaved;
      if (timeDiff < 60000) { // Less than 1 minute
        return 'Saved just now';
      } else if (timeDiff < 3600000) { // Less than 1 hour
        const minutes = Math.floor(timeDiff / 60000);
        return `Saved ${minutes}m ago`;
      } else {
        const hours = Math.floor(timeDiff / 3600000);
        return `Saved ${hours}h ago`;
      }
    } else {
      return 'No changes';
    }
  }, [isSaving, saveError, hasUnsavedChanges, lastSaved]);

  /**
   * Get save status color
   */
  const getSaveStatusColor = useCallback(() => {
    if (isSaving) {
      return '#1890ff'; // Blue
    } else if (saveError) {
      return '#f5222d'; // Red
    } else if (hasUnsavedChanges) {
      return '#faad14'; // Orange
    } else {
      return '#52c41a'; // Green
    }
  }, [isSaving, saveError, hasUnsavedChanges]);

  return {
    // State
    isSaving,
    lastSaved,
    hasUnsavedChanges,
    saveError,
    saveCount: saveCountRef.current,

    // Methods
    forceSave,
    performSave,

    // Status helpers
    getSaveStatusText,
    getSaveStatusColor,

    // Computed
    canSave: !isSaving && hasUnsavedChanges,
    isReady: !isSaving && !saveError
  };
};

/**
 * Hook specifically for App Builder auto-save
 * @param {string} appId - App ID
 * @param {Object} appData - App data to save
 * @param {Object} options - Additional options
 * @returns {Object} Auto-save state and methods
 */
export const useAppBuilderAutoSave = (appId, appData, options = {}) => {
  const saveFunction = useCallback(async (data) => {
    if (!appId) {
      throw new Error('App ID is required for saving');
    }

    // Use data sync service for saving
    const operationId = dataSyncService.saveAppConfig(appId, data);
    
    return {
      operationId,
      appId,
      data,
      timestamp: Date.now()
    };
  }, [appId]);

  return useAutoSave({
    data: appData,
    saveFunction,
    debounceMs: 3000, // 3 seconds for app builder
    showMessages: true,
    ...options
  });
};

/**
 * Hook for saving user preferences
 * @param {Object} preferences - User preferences
 * @param {Object} options - Additional options
 * @returns {Object} Auto-save state and methods
 */
export const usePreferencesAutoSave = (preferences, options = {}) => {
  const saveFunction = useCallback(async (data) => {
    // Save to local storage immediately
    localStorage.setItem('user_preferences', JSON.stringify(data));
    
    // Also sync to server if user is logged in
    try {
      const response = await dataSyncService.queueOperation({
        type: 'update',
        endpoint: '/api/user/preferences/',
        data,
        description: 'Update user preferences'
      });
      return response;
    } catch (error) {
      // Preferences save to local storage succeeded, server sync can fail
      console.warn('Failed to sync preferences to server:', error);
      return { localOnly: true };
    }
  }, []);

  return useAutoSave({
    data: preferences,
    saveFunction,
    debounceMs: 1000, // 1 second for preferences
    showMessages: false, // Don't show messages for preferences
    ...options
  });
};

export default useAutoSave;
