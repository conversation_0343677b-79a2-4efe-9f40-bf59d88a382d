# Start App Builder 201 Containers
param(
    [switch]$Build,
    [switch]$Fresh,
    [switch]$Logs
)

Write-Host "🚀 Starting App Builder 201 Containers" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Check if Docker is running
Write-Host "`n🔍 Checking Docker status..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
        Write-Host "💡 Run: scripts/fix-docker-connection.ps1" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "✅ Docker is running" -ForegroundColor Green
}
catch {
    Write-Host "❌ Cannot connect to Docker. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Navigate to project root
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

Write-Host "📁 Working directory: $projectRoot" -ForegroundColor Blue

# Ensure frontend build exists
Write-Host "`n🏗️ Ensuring frontend build exists..." -ForegroundColor Yellow
try {
    & "scripts/ensure-frontend-build.ps1"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Frontend build check failed" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "⚠️ Could not verify frontend build: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "💡 Continuing anyway - you may need to build frontend manually" -ForegroundColor Blue
}

# Stop existing containers if Fresh flag is used
if ($Fresh) {
    Write-Host "`n🛑 Stopping and removing existing containers..." -ForegroundColor Yellow
    docker-compose down -v --remove-orphans
    docker system prune -f
}

# Build containers if Build flag is used
if ($Build) {
    Write-Host "`n🔨 Building containers..." -ForegroundColor Yellow
    docker-compose build --no-cache
}

# Start containers
Write-Host "`n🚀 Starting containers..." -ForegroundColor Yellow
try {
    if ($Build) {
        docker-compose up -d --build
    }
    else {
        docker-compose up -d
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Containers started successfully!" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Failed to start containers" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "❌ Error starting containers: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Wait for services to be ready
Write-Host "`n⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check container status
Write-Host "`n📦 Container Status:" -ForegroundColor Blue
docker-compose ps

# Check service health
Write-Host "`n🏥 Health Checks:" -ForegroundColor Blue

# Check backend
try {
    $backendHealth = Invoke-RestMethod -Uri "http://localhost:8000/health/" -TimeoutSec 5 -ErrorAction SilentlyContinue
    Write-Host "✅ Backend: Healthy" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ Backend: Not ready yet (this is normal during startup)" -ForegroundColor Yellow
}

# Check frontend
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend: Healthy" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ Frontend: Not ready yet" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "⚠️ Frontend: Not ready yet (this is normal during startup)" -ForegroundColor Yellow
}

# Check database
try {
    $dbCheck = docker-compose exec -T db pg_isready -U myappuser -d myapp 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database: Healthy" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ Database: Not ready yet" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "⚠️ Database: Not ready yet" -ForegroundColor Yellow
}

Write-Host "`n🎉 Startup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📱 Application URLs:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:3000" -ForegroundColor Blue
Write-Host "   Backend:  http://localhost:8000" -ForegroundColor Blue
Write-Host "   Database: localhost:5432" -ForegroundColor Blue
Write-Host ""
Write-Host "🔧 Useful Commands:" -ForegroundColor Cyan
Write-Host "   View logs:     docker-compose logs -f" -ForegroundColor Blue
Write-Host "   Stop all:      docker-compose down" -ForegroundColor Blue
Write-Host "   Restart:       docker-compose restart" -ForegroundColor Blue
Write-Host "   Shell access:  docker-compose exec backend bash" -ForegroundColor Blue

# Show logs if requested
if ($Logs) {
    Write-Host "`n📋 Container Logs:" -ForegroundColor Yellow
    docker-compose logs --tail=50
}

Write-Host "`n✨ Ready to develop! ✨" -ForegroundColor Green
