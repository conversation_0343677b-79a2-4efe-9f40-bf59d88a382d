import { lazy } from 'react';
import { createLazyComponent, LoadingStates } from '../utils/lazyLoading';

/**
 * Enhanced Route Configuration with Priority-Based Loading
 * 
 * Routes are categorized by priority and loading strategy to optimize
 * initial bundle size and user experience.
 */

// High Priority Routes (Core functionality - load immediately)
export const HighPriorityRoutes = {
  HomePage: createLazyComponent(
    () => import('../pages/HomePage'),
    {
      componentName: 'HomePage',
      fallback: LoadingStates.fullPage,
      retryAttempts: 3,
      preload: true // Preload since it's likely to be accessed
    }
  ),

  HomePageMVP: createLazyComponent(
    () => import('../pages/HomePageMVP'),
    {
      componentName: 'HomePageMVP',
      fallback: LoadingStates.fullPage,
      retryAttempts: 3,
      preload: true
    }
  ),

  AppBuilderMVP: createLazyComponent(
    () => import('../pages/AppBuilderMVP'),
    {
      componentName: 'AppBuilderMVP',
      fallback: LoadingStates.withDescription('Loading App Builder...'),
      retryAttempts: 3,
      preload: false // Load on demand
    }
  )
};

// Medium Priority Routes (Important features - load after initial render)
export const MediumPriorityRoutes = {
  AppBuilderPage: createLazyComponent(
    () => import('../pages/AppBuilderPage'),
    {
      componentName: 'AppBuilderPage',
      fallback: LoadingStates.withDescription('Loading App Builder...'),
      retryAttempts: 3,
      preload: false
    }
  ),

  AppBuilderWithTheme: createLazyComponent(
    () => import('../pages/AppBuilderWithTheme'),
    {
      componentName: 'AppBuilderWithTheme',
      fallback: LoadingStates.withDescription('Loading App Builder...'),
      retryAttempts: 3,
      preload: false
    }
  ),

  DashboardPage: createLazyComponent(
    () => import('../pages/DashboardPage'),
    {
      componentName: 'DashboardPage',
      fallback: LoadingStates.withDescription('Loading dashboard...'),
      retryAttempts: 2,
      preload: false
    }
  ),

  ProjectsPage: createLazyComponent(
    () => import('../pages/ProjectsPage'),
    {
      componentName: 'ProjectsPage',
      fallback: LoadingStates.withDescription('Loading projects...'),
      retryAttempts: 2,
      preload: false
    }
  )
};

// Low Priority Routes (Secondary features - load on interaction)
export const LowPriorityRoutes = {
  WebSocketPage: createLazyComponent(
    () => import('../pages/WebSocketPage'),
    {
      componentName: 'WebSocketPage',
      fallback: LoadingStates.withDescription('Loading WebSocket tools...'),
      retryAttempts: 2,
      preload: false
    }
  ),

  TemplatesPage: createLazyComponent(
    () => import('../pages/TemplatesPage'),
    {
      componentName: 'TemplatesPage',
      fallback: LoadingStates.withDescription('Loading templates...'),
      retryAttempts: 2,
      preload: false
    }
  ),

  ProfilePage: createLazyComponent(
    () => import('../pages/ProfilePage'),
    {
      componentName: 'ProfilePage',
      fallback: LoadingStates.standard,
      retryAttempts: 2,
      preload: false
    }
  ),

  SettingsPage: createLazyComponent(
    () => import('../pages/SettingsPage'),
    {
      componentName: 'SettingsPage',
      fallback: LoadingStates.standard,
      retryAttempts: 2,
      preload: false
    }
  )
};

// Authentication Routes (Load on demand)
export const AuthRoutes = {
  LoginPage: createLazyComponent(
    () => import('../pages/LoginPage'),
    {
      componentName: 'LoginPage',
      fallback: LoadingStates.standard,
      retryAttempts: 2,
      preload: false
    }
  ),

  RegisterPage: createLazyComponent(
    () => import('../pages/RegisterPage'),
    {
      componentName: 'RegisterPage',
      fallback: LoadingStates.standard,
      retryAttempts: 2,
      preload: false
    }
  ),

  ForgotPasswordPage: createLazyComponent(
    () => import('../pages/ForgotPasswordPage'),
    {
      componentName: 'ForgotPasswordPage',
      fallback: LoadingStates.standard,
      retryAttempts: 2,
      preload: false
    }
  ),

  ResetPasswordPage: createLazyComponent(
    () => import('../pages/ResetPasswordPage'),
    {
      componentName: 'ResetPasswordPage',
      fallback: LoadingStates.standard,
      retryAttempts: 2,
      preload: false
    }
  )
};

// Error and Utility Routes (Very low priority)
export const UtilityRoutes = {
  NotFoundPage: createLazyComponent(
    () => import('../pages/NotFoundPage'),
    {
      componentName: 'NotFoundPage',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  ),

  UnauthorizedPage: createLazyComponent(
    () => import('../pages/UnauthorizedPage'),
    {
      componentName: 'UnauthorizedPage',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  )
};

// Test Routes (Development only)
export const TestRoutes = {
  ThemeTest: createLazyComponent(
    () => import('../components/test/ThemeTest'),
    {
      componentName: 'ThemeTest',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  ),

  DarkModeTest: createLazyComponent(
    () => import('../components/test/DarkModeTest'),
    {
      componentName: 'DarkModeTest',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  ),

  ContrastTest: createLazyComponent(
    () => import('../components/test/ContrastTest'),
    {
      componentName: 'ContrastTest',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  ),

  HeaderContrastTest: createLazyComponent(
    () => import('../components/test/HeaderContrastTest'),
    {
      componentName: 'HeaderContrastTest',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  ),

  ServiceWorkerTest: createLazyComponent(
    () => import('../pages/ServiceWorkerTest'),
    {
      componentName: 'ServiceWorkerTest',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  ),

  ResponsiveDemo: createLazyComponent(
    () => import('../components/demo/ResponsiveDemo'),
    {
      componentName: 'ResponsiveDemo',
      fallback: LoadingStates.minimal,
      retryAttempts: 1,
      preload: false
    }
  )
};

// Combined routes object for easy access
export const AllRoutes = {
  ...HighPriorityRoutes,
  ...MediumPriorityRoutes,
  ...LowPriorityRoutes,
  ...AuthRoutes,
  ...UtilityRoutes,
  ...TestRoutes
};

// Route priority configuration for progressive loading
export const RoutePriority = {
  high: Object.keys(HighPriorityRoutes),
  medium: Object.keys(MediumPriorityRoutes),
  low: Object.keys(LowPriorityRoutes),
  auth: Object.keys(AuthRoutes),
  utility: Object.keys(UtilityRoutes),
  test: Object.keys(TestRoutes)
};

// Route metadata for analytics and optimization
export const RouteMetadata = {
  '/': { priority: 'high', category: 'landing', preload: true },
  '/home': { priority: 'high', category: 'landing', preload: true },
  '/home-mvp': { priority: 'high', category: 'landing', preload: true },
  '/mvp': { priority: 'high', category: 'app-builder', preload: false },
  '/app-builder': { priority: 'medium', category: 'app-builder', preload: false },
  '/dashboard': { priority: 'medium', category: 'user', preload: false },
  '/projects': { priority: 'medium', category: 'user', preload: false },
  '/websocket': { priority: 'low', category: 'tools', preload: false },
  '/templates': { priority: 'low', category: 'resources', preload: false },
  '/profile': { priority: 'low', category: 'user', preload: false },
  '/settings': { priority: 'low', category: 'user', preload: false },
  '/login': { priority: 'auth', category: 'auth', preload: false },
  '/register': { priority: 'auth', category: 'auth', preload: false }
};

// Preload strategy based on route priority
export const preloadRoutesByPriority = () => {
  // Preload high priority routes immediately
  setTimeout(() => {
    RoutePriority.high.forEach(routeName => {
      const route = HighPriorityRoutes[routeName];
      if (route && route.preload) {
        route.preload();
      }
    });
  }, 100);

  // Preload medium priority routes after a delay
  setTimeout(() => {
    RoutePriority.medium.forEach(routeName => {
      const route = MediumPriorityRoutes[routeName];
      if (route && route.preload) {
        route.preload();
      }
    });
  }, 2000);
};

export default AllRoutes;
