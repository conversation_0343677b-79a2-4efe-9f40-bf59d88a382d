import { useState, useRef, useCallback, useEffect } from 'react';

/**
 * Enhanced drag and drop hook with visual feedback and animations
 * @param {Object} options - Configuration options
 * @param {Function} options.onDrop - Callback when item is dropped
 * @param {Function} options.onDragStart - Callback when drag starts
 * @param {Function} options.onDragEnd - Callback when drag ends
 * @param {Function} options.onDragOver - Callback when dragging over
 * @param {Function} options.onDragLeave - Callback when leaving drag area
 * @param {boolean} options.snapToGrid - Enable snap to grid functionality
 * @param {number} options.gridSize - Grid size for snapping
 * @param {boolean} options.showDropZones - Show visual drop zones
 * @param {Array} options.acceptedTypes - Accepted drag data types
 * @returns {Object} Drag and drop state and handlers
 */
export const useEnhancedDragDrop = (options = {}) => {
  const {
    onDrop,
    onDragStart,
    onDragEnd,
    onDragOver,
    onDragLeave,
    snapToGrid = false,
    gridSize = 20,
    showDropZones = true,
    acceptedTypes = ['application/json']
  } = options;

  // State
  const [isDragging, setIsDragging] = useState(false);
  const [isOver, setIsOver] = useState(false);
  const [dragData, setDragData] = useState(null);
  const [dropPosition, setDropPosition] = useState({ x: 0, y: 0 });
  const [validDropZone, setValidDropZone] = useState(true);
  const [dragPreview, setDragPreview] = useState(null);

  // Refs
  const dropZoneRef = useRef(null);
  const dragPreviewRef = useRef(null);

  // Handle drag start
  const handleDragStart = useCallback((e, data) => {
    setIsDragging(true);
    setDragData(data);
    
    // Set drag data
    if (data) {
      e.dataTransfer.setData('application/json', JSON.stringify(data));
    }
    e.dataTransfer.effectAllowed = 'copy';
    
    // Create custom drag preview if provided
    if (dragPreviewRef.current) {
      const preview = dragPreviewRef.current.cloneNode(true);
      preview.style.position = 'absolute';
      preview.style.top = '-1000px';
      preview.style.left = '-1000px';
      preview.style.opacity = '0.8';
      preview.style.transform = 'rotate(5deg) scale(0.9)';
      preview.style.pointerEvents = 'none';
      preview.style.zIndex = '9999';
      document.body.appendChild(preview);
      
      e.dataTransfer.setDragImage(preview, 50, 25);
      
      // Clean up preview after drag
      setTimeout(() => {
        if (document.body.contains(preview)) {
          document.body.removeChild(preview);
        }
      }, 0);
    }
    
    if (onDragStart) {
      onDragStart(e, data);
    }
  }, [onDragStart]);

  // Handle drag end
  const handleDragEnd = useCallback((e) => {
    setIsDragging(false);
    setDragData(null);
    setDropPosition({ x: 0, y: 0 });
    setValidDropZone(true);
    
    if (onDragEnd) {
      onDragEnd(e);
    }
  }, [onDragEnd]);

  // Handle drag enter
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    setIsOver(true);
  }, []);

  // Handle drag over
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    
    // Calculate drop position
    if (dropZoneRef.current) {
      const rect = dropZoneRef.current.getBoundingClientRect();
      let x = e.clientX - rect.left;
      let y = e.clientY - rect.top;
      
      // Apply snap to grid if enabled
      if (snapToGrid) {
        x = Math.round(x / gridSize) * gridSize;
        y = Math.round(y / gridSize) * gridSize;
      }
      
      setDropPosition({ x, y });
    }
    
    // Check if drop is valid
    const dragType = e.dataTransfer.types[0];
    const isValidType = acceptedTypes.includes(dragType) || acceptedTypes.length === 0;
    setValidDropZone(isValidType);
    
    e.dataTransfer.dropEffect = isValidType ? 'copy' : 'none';
    
    if (onDragOver) {
      onDragOver(e);
    }
  }, [snapToGrid, gridSize, acceptedTypes, onDragOver]);

  // Handle drag leave
  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    
    // Only set isOver to false if we're actually leaving the drop zone
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsOver(false);
      setValidDropZone(true);
      
      if (onDragLeave) {
        onDragLeave(e);
      }
    }
  }, [onDragLeave]);

  // Handle drop
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsOver(false);
    setValidDropZone(true);
    
    try {
      // Get dropped data
      const jsonData = e.dataTransfer.getData('application/json');
      let droppedData = null;
      
      if (jsonData) {
        droppedData = JSON.parse(jsonData);
      }
      
      // Calculate final position
      let finalPosition = dropPosition;
      if (snapToGrid) {
        finalPosition = {
          x: Math.round(dropPosition.x / gridSize) * gridSize,
          y: Math.round(dropPosition.y / gridSize) * gridSize
        };
      }
      
      if (onDrop) {
        onDrop(e, droppedData, finalPosition);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  }, [dropPosition, snapToGrid, gridSize, onDrop]);

  // Set up event listeners
  useEffect(() => {
    const dropZone = dropZoneRef.current;
    if (!dropZone) return;

    dropZone.addEventListener('dragenter', handleDragEnter);
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);

    return () => {
      dropZone.removeEventListener('dragenter', handleDragEnter);
      dropZone.removeEventListener('dragover', handleDragOver);
      dropZone.removeEventListener('dragleave', handleDragLeave);
      dropZone.removeEventListener('drop', handleDrop);
    };
  }, [handleDragEnter, handleDragOver, handleDragLeave, handleDrop]);

  return {
    // State
    isDragging,
    isOver,
    dragData,
    dropPosition,
    validDropZone,
    
    // Refs
    dropZoneRef,
    dragPreviewRef,
    
    // Handlers
    handleDragStart,
    handleDragEnd,
    
    // Utilities
    reset: () => {
      setIsDragging(false);
      setIsOver(false);
      setDragData(null);
      setDropPosition({ x: 0, y: 0 });
      setValidDropZone(true);
    }
  };
};

/**
 * Hook for managing drag visual feedback
 * @param {Object} options - Configuration options
 * @returns {Object} Visual feedback utilities
 */
export const useDragVisualFeedback = (options = {}) => {
  const {
    showGhost = true,
    showDropIndicator = true,
    animationDuration = 300
  } = options;

  const [ghostPosition, setGhostPosition] = useState({ x: 0, y: 0 });
  const [showGhostElement, setShowGhostElement] = useState(false);
  const [dropIndicatorPosition, setDropIndicatorPosition] = useState(null);

  // Update ghost position during drag
  const updateGhostPosition = useCallback((x, y) => {
    if (showGhost) {
      setGhostPosition({ x, y });
    }
  }, [showGhost]);

  // Show/hide ghost element
  const toggleGhost = useCallback((show) => {
    setShowGhostElement(show);
  }, []);

  // Update drop indicator
  const updateDropIndicator = useCallback((position) => {
    if (showDropIndicator) {
      setDropIndicatorPosition(position);
    }
  }, [showDropIndicator]);

  // Clear all visual feedback
  const clearFeedback = useCallback(() => {
    setShowGhostElement(false);
    setDropIndicatorPosition(null);
    setGhostPosition({ x: 0, y: 0 });
  }, []);

  return {
    ghostPosition,
    showGhostElement,
    dropIndicatorPosition,
    updateGhostPosition,
    toggleGhost,
    updateDropIndicator,
    clearFeedback
  };
};

/**
 * Hook for managing component reordering with drag and drop
 * @param {Array} items - Array of items to reorder
 * @param {Function} onReorder - Callback when items are reordered
 * @returns {Object} Reordering utilities
 */
export const useDragReorder = (items, onReorder) => {
  const [draggedItem, setDraggedItem] = useState(null);
  const [draggedOverItem, setDraggedOverItem] = useState(null);
  const [dropPosition, setDropPosition] = useState('after'); // 'before' or 'after'

  const handleDragStart = useCallback((e, item) => {
    setDraggedItem(item);
    e.dataTransfer.setData('application/json', JSON.stringify(item));
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleDragOver = useCallback((e, item) => {
    e.preventDefault();
    
    if (draggedItem && draggedItem.id !== item.id) {
      setDraggedOverItem(item);
      
      // Determine drop position based on mouse position
      const rect = e.currentTarget.getBoundingClientRect();
      const midpoint = rect.top + rect.height / 2;
      setDropPosition(e.clientY < midpoint ? 'before' : 'after');
    }
    
    e.dataTransfer.dropEffect = 'move';
  }, [draggedItem]);

  const handleDrop = useCallback((e, targetItem) => {
    e.preventDefault();
    
    if (draggedItem && targetItem && draggedItem.id !== targetItem.id) {
      const draggedIndex = items.findIndex(item => item.id === draggedItem.id);
      const targetIndex = items.findIndex(item => item.id === targetItem.id);
      
      if (draggedIndex !== -1 && targetIndex !== -1) {
        const newItems = [...items];
        const [removed] = newItems.splice(draggedIndex, 1);
        
        const insertIndex = dropPosition === 'before' ? targetIndex : targetIndex + 1;
        newItems.splice(insertIndex, 0, removed);
        
        if (onReorder) {
          onReorder(newItems);
        }
      }
    }
    
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, [items, draggedItem, dropPosition, onReorder]);

  const handleDragEnd = useCallback(() => {
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, []);

  return {
    draggedItem,
    draggedOverItem,
    dropPosition,
    handleDragStart,
    handleDragOver,
    handleDrop,
    handleDragEnd
  };
};

export default useEnhancedDragDrop;
