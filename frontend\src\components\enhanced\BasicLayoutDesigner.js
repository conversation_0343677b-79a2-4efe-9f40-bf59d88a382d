import React, { useState } from 'react';
import { Card, Button, Input, Select, Row, Col, Typography, Space, Alert } from 'antd';
import {
  PlusOutlined,
  LayoutOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;
const { Option } = Select;

/**
 * Basic Layout Designer - Fallback component
 * Simple layout designer that always works
 */
const BasicLayoutDesigner = () => {
  const [layouts, setLayouts] = useState([
    {
      id: '1',
      name: 'Header Layout',
      type: 'flex',
      description: 'Simple header with navigation',
      items: ['Header', 'Navigation', 'Content']
    },
    {
      id: '2',
      name: 'Sidebar Layout',
      type: 'grid',
      description: 'Layout with sidebar and main content',
      items: ['Sidebar', 'Main Content', 'Footer']
    },
    {
      id: '3',
      name: 'Card Grid',
      type: 'grid',
      description: 'Responsive card grid layout',
      items: ['Card 1', 'Card 2', 'Card 3', 'Card 4']
    }
  ]);

  const [newLayoutName, setNewLayoutName] = useState('');
  const [newLayoutType, setNewLayoutType] = useState('grid');
  const [selectedLayout, setSelectedLayout] = useState(null);

  const handleAddLayout = () => {
    if (!newLayoutName.trim()) return;

    const newLayout = {
      id: Date.now().toString(),
      name: newLayoutName.trim(),
      type: newLayoutType,
      description: 'Custom layout',
      items: ['Item 1', 'Item 2']
    };

    setLayouts([...layouts, newLayout]);
    setNewLayoutName('');
    setNewLayoutType('grid');
  };

  const handleDeleteLayout = (id) => {
    setLayouts(layouts.filter(layout => layout.id !== id));
    if (selectedLayout && selectedLayout.id === id) {
      setSelectedLayout(null);
    }
  };

  const handleSelectLayout = (layout) => {
    setSelectedLayout(layout);
  };

  return (
    <div style={{ padding: '20px' }}>
      <Title level={3}>Layout Designer</Title>
      <Paragraph>
        Create and manage layout templates for your application. This is a basic version of the layout designer.
      </Paragraph>

      <Alert
        message="Basic Layout Designer"
        description="This is a simplified version of the layout designer. It provides basic layout management functionality."
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="Create New Layout" style={{ marginBottom: '24px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Input
                placeholder="Layout name"
                value={newLayoutName}
                onChange={(e) => setNewLayoutName(e.target.value)}
                prefix={<LayoutOutlined />}
              />
              <Select
                value={newLayoutType}
                onChange={setNewLayoutType}
                style={{ width: '100%' }}
              >
                <Option value="grid">Grid Layout</Option>
                <Option value="flex">Flex Layout</Option>
                <Option value="custom">Custom Layout</Option>
              </Select>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddLayout}
                disabled={!newLayoutName.trim()}
                block
              >
                Add Layout
              </Button>
            </Space>
          </Card>

          <Card title="Layout Library">
            <Space direction="vertical" style={{ width: '100%' }}>
              {layouts.map(layout => (
                <Card
                  key={layout.id}
                  size="small"
                  style={{
                    cursor: 'pointer',
                    border: selectedLayout?.id === layout.id ? '2px solid #1890ff' : '1px solid #d9d9d9'
                  }}
                  onClick={() => handleSelectLayout(layout)}
                  actions={[
                    <EditOutlined key="edit" onClick={(e) => {
                      e.stopPropagation();
                      handleSelectLayout(layout);
                    }} />,
                    <DeleteOutlined key="delete" onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteLayout(layout.id);
                    }} />
                  ]}
                >
                  <Card.Meta
                    title={layout.name}
                    description={
                      <div>
                        <div>Type: {layout.type}</div>
                        <div>{layout.description}</div>
                        <div>Items: {layout.items.join(', ')}</div>
                      </div>
                    }
                  />
                </Card>
              ))}
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="Layout Preview">
            {selectedLayout ? (
              <div>
                <Title level={4}>{selectedLayout.name}</Title>
                <Paragraph>{selectedLayout.description}</Paragraph>
                
                <div style={{
                  border: '2px dashed #d9d9d9',
                  borderRadius: '8px',
                  padding: '20px',
                  minHeight: '300px',
                  background: '#fafafa'
                }}>
                  <div style={{
                    display: selectedLayout.type === 'flex' ? 'flex' : 'grid',
                    gridTemplateColumns: selectedLayout.type === 'grid' ? 'repeat(auto-fit, minmax(150px, 1fr))' : 'none',
                    gap: '16px',
                    height: '100%'
                  }}>
                    {selectedLayout.items.map((item, index) => (
                      <div
                        key={index}
                        style={{
                          background: 'white',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          padding: '16px',
                          textAlign: 'center',
                          minHeight: '80px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        {item}
                      </div>
                    ))}
                  </div>
                </div>

                <div style={{ marginTop: '16px' }}>
                  <Button type="primary" icon={<SaveOutlined />}>
                    Save Layout
                  </Button>
                </div>
              </div>
            ) : (
              <div style={{
                textAlign: 'center',
                padding: '60px 20px',
                color: '#999'
              }}>
                <LayoutOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>Select a layout to preview</div>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default BasicLayoutDesigner;
