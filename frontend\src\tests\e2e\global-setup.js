/**
 * Global setup for Playwright E2E tests
 * This file runs once before all tests
 */

const { chromium } = require('@playwright/test');
const path = require('path');
const fs = require('fs');

async function globalSetup(config) {
  console.log('🚀 Starting global setup for E2E tests...');

  // Create test reports directory
  const reportsDir = path.join(__dirname, '../../../test-results');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  // Create screenshots directory
  const screenshotsDir = path.join(reportsDir, 'screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }

  // Create videos directory
  const videosDir = path.join(reportsDir, 'videos');
  if (!fs.existsSync(videosDir)) {
    fs.mkdirSync(videosDir, { recursive: true });
  }

  // Set up environment variables for tests
  process.env.PLAYWRIGHT_TEST_BASE_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
  process.env.REACT_APP_API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
  process.env.REACT_APP_WS_URL = process.env.REACT_APP_WS_URL || 'ws://localhost:8000';

  // Check if the application is running
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    console.log(`📡 Checking if app is running at ${process.env.PLAYWRIGHT_TEST_BASE_URL}...`);
    
    // Try to connect to the application
    const response = await page.goto(process.env.PLAYWRIGHT_TEST_BASE_URL, {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    if (!response || !response.ok()) {
      throw new Error(`Application not responding at ${process.env.PLAYWRIGHT_TEST_BASE_URL}`);
    }

    console.log('✅ Application is running and accessible');

    // Perform any additional setup tasks
    await setupTestData(page);
    await setupMockServices(page);

  } catch (error) {
    console.error('❌ Global setup failed:', error.message);
    console.log('💡 Make sure the application is running before executing E2E tests');
    console.log(`   Frontend: ${process.env.PLAYWRIGHT_TEST_BASE_URL}`);
    console.log(`   Backend: ${process.env.REACT_APP_API_URL}`);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('✅ Global setup completed successfully');
}

/**
 * Set up test data and mock responses
 */
async function setupTestData(page) {
  console.log('📝 Setting up test data...');

  // Set up localStorage with test data
  await page.addInitScript(() => {
    // Mock user authentication
    localStorage.setItem('auth_token', 'test-auth-token');
    localStorage.setItem('user_id', 'test-user-123');
    localStorage.setItem('user_name', 'Test User');

    // Mock app preferences
    localStorage.setItem('app_preferences', JSON.stringify({
      theme: 'light',
      sidebarOpen: true,
      autoSave: true,
      showTutorial: false
    }));

    // Mock recent projects
    localStorage.setItem('recent_projects', JSON.stringify([
      {
        id: 'project-1',
        name: 'Test Project 1',
        lastModified: new Date().toISOString(),
        thumbnail: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      },
      {
        id: 'project-2',
        name: 'Test Project 2',
        lastModified: new Date(Date.now() - 86400000).toISOString(),
        thumbnail: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      }
    ]));

    // Mock tutorial progress
    localStorage.setItem('tutorial_progress', JSON.stringify({
      completed: false,
      currentStep: 0,
      completedSteps: []
    }));
  });

  console.log('✅ Test data setup completed');
}

/**
 * Set up mock services and API responses
 */
async function setupMockServices(page) {
  console.log('🔧 Setting up mock services...');

  // Set up service worker for offline testing
  await page.addInitScript(() => {
    // Mock service worker registration
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register = async () => ({
        installing: null,
        waiting: null,
        active: {
          postMessage: () => {},
          scriptURL: '/sw.js'
        },
        scope: '/',
        update: async () => {},
        unregister: async () => true,
        addEventListener: () => {},
        removeEventListener: () => {}
      });
    }
  });

  // Set up WebSocket mock
  await page.addInitScript(() => {
    class MockWebSocket {
      constructor(url) {
        this.url = url;
        this.readyState = WebSocket.CONNECTING;
        this.onopen = null;
        this.onclose = null;
        this.onmessage = null;
        this.onerror = null;

        // Simulate connection after a short delay
        setTimeout(() => {
          this.readyState = WebSocket.OPEN;
          if (this.onopen) {
            this.onopen({ target: this });
          }
        }, 100);
      }

      send(data) {
        // Echo back for testing
        setTimeout(() => {
          if (this.onmessage) {
            this.onmessage({
              data: JSON.stringify({
                type: 'echo',
                original: JSON.parse(data)
              }),
              target: this
            });
          }
        }, 50);
      }

      close() {
        this.readyState = WebSocket.CLOSED;
        if (this.onclose) {
          this.onclose({ target: this });
        }
      }

      addEventListener(event, handler) {
        this[`on${event}`] = handler;
      }

      removeEventListener(event, handler) {
        this[`on${event}`] = null;
      }
    }

    // Replace WebSocket with mock
    window.WebSocket = MockWebSocket;
    MockWebSocket.CONNECTING = 0;
    MockWebSocket.OPEN = 1;
    MockWebSocket.CLOSING = 2;
    MockWebSocket.CLOSED = 3;
  });

  // Set up performance monitoring
  await page.addInitScript(() => {
    // Mock performance observer
    window.PerformanceObserver = class MockPerformanceObserver {
      constructor(callback) {
        this.callback = callback;
      }
      
      observe() {}
      disconnect() {}
    };

    // Enhanced performance tracking
    window.testPerformance = {
      marks: {},
      measures: {},
      
      mark(name) {
        this.marks[name] = performance.now();
      },
      
      measure(name, startMark, endMark) {
        const start = this.marks[startMark] || 0;
        const end = this.marks[endMark] || performance.now();
        this.measures[name] = end - start;
        return this.measures[name];
      },
      
      getMetrics() {
        return {
          marks: this.marks,
          measures: this.measures,
          memory: performance.memory ? {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
          } : null
        };
      }
    };
  });

  console.log('✅ Mock services setup completed');
}

/**
 * Check system requirements
 */
async function checkSystemRequirements() {
  console.log('🔍 Checking system requirements...');

  // Check Node.js version
  const nodeVersion = process.version;
  console.log(`Node.js version: ${nodeVersion}`);

  // Check available memory
  const memoryUsage = process.memoryUsage();
  console.log(`Memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`);

  // Check disk space (simplified)
  try {
    const stats = fs.statSync(__dirname);
    console.log('✅ File system accessible');
  } catch (error) {
    console.warn('⚠️ File system check failed:', error.message);
  }

  console.log('✅ System requirements check completed');
}

/**
 * Set up test environment
 */
async function setupTestEnvironment() {
  console.log('🌍 Setting up test environment...');

  // Set timezone for consistent date/time testing
  process.env.TZ = 'UTC';

  // Set up test database (if needed)
  // This would typically involve creating a test database
  // and running migrations

  // Set up test file uploads directory
  const uploadsDir = path.join(__dirname, '../../../test-uploads');
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }

  console.log('✅ Test environment setup completed');
}

/**
 * Main setup function
 */
module.exports = async function(config) {
  try {
    await checkSystemRequirements();
    await setupTestEnvironment();
    await globalSetup(config);
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    process.exit(1);
  }
};
