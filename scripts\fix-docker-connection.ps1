# Docker Connection Fix Script for App Builder 201
# This script diagnoses and fixes Docker connection issues on Windows

Write-Host "Docker Connection Diagnostic and Fix Script" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Function to check if Docker Desktop is installed
function Test-DockerInstallation {
    Write-Host "`n📋 Checking Docker Installation..." -ForegroundColor Yellow
    
    $dockerPath = Get-Command docker -ErrorAction SilentlyContinue
    if ($dockerPath) {
        Write-Host "✅ Docker CLI found at: $($dockerPath.Source)" -ForegroundColor Green
        return $true
    }
    else {
        Write-Host "❌ Docker CLI not found in PATH" -ForegroundColor Red
        return $false
    }
}

# Function to check if Docker Desktop is running
function Test-DockerService {
    Write-Host "`n🔄 Checking Docker Service Status..." -ForegroundColor Yellow
    
    try {
        $dockerInfo = docker info 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker service is running" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Docker service is not running" -ForegroundColor Red
            Write-Host "Error: $dockerInfo" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Cannot connect to Docker service" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check Docker Desktop processes
function Test-DockerProcesses {
    Write-Host "`n🔍 Checking Docker Desktop Processes..." -ForegroundColor Yellow
    
    $dockerProcesses = @(
        "Docker Desktop",
        "com.docker.backend",
        "com.docker.proxy",
        "dockerd"
    )
    
    $runningProcesses = @()
    foreach ($process in $dockerProcesses) {
        $proc = Get-Process -Name $process -ErrorAction SilentlyContinue
        if ($proc) {
            $runningProcesses += $process
            Write-Host "✅ $process is running" -ForegroundColor Green
        }
        else {
            Write-Host "❌ $process is not running" -ForegroundColor Red
        }
    }
    
    return $runningProcesses.Count -gt 0
}

# Function to start Docker Desktop
function Start-DockerDesktop {
    Write-Host "`n🚀 Attempting to start Docker Desktop..." -ForegroundColor Yellow
    
    # Common Docker Desktop installation paths
    $dockerPaths = @(
        "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
        "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
        "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
    )
    
    foreach ($path in $dockerPaths) {
        if (Test-Path $path) {
            Write-Host "📍 Found Docker Desktop at: $path" -ForegroundColor Blue
            try {
                Start-Process -FilePath $path -WindowStyle Hidden
                Write-Host "✅ Docker Desktop start command executed" -ForegroundColor Green
                Write-Host "⏳ Waiting for Docker Desktop to initialize..." -ForegroundColor Yellow
                
                # Wait for Docker to start (up to 60 seconds)
                $timeout = 60
                $elapsed = 0
                while ($elapsed -lt $timeout) {
                    Start-Sleep -Seconds 5
                    $elapsed += 5
                    
                    try {
                        docker info | Out-Null
                        if ($LASTEXITCODE -eq 0) {
                            Write-Host "✅ Docker Desktop is now running!" -ForegroundColor Green
                            return $true
                        }
                    }
                    catch {
                        # Continue waiting
                    }
                    
                    Write-Host "⏳ Still waiting... ($elapsed/$timeout seconds)" -ForegroundColor Yellow
                }
                
                Write-Host "⚠️ Docker Desktop may still be starting. Please wait a few more minutes." -ForegroundColor Yellow
                return $false
            }
            catch {
                Write-Host "❌ Failed to start Docker Desktop: $($_.Exception.Message)" -ForegroundColor Red
                return $false
            }
        }
    }
    
    Write-Host "❌ Docker Desktop executable not found in common locations" -ForegroundColor Red
    return $false
}

# Function to check container status
function Test-ContainerStatus {
    Write-Host "`n📦 Checking Container Status..." -ForegroundColor Yellow
    
    try {
        $containers = docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Container Status:" -ForegroundColor Blue
            Write-Host $containers -ForegroundColor White
            return $true
        }
        else {
            Write-Host "❌ Cannot list containers: $containers" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error checking containers: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to provide installation instructions
function Show-InstallationInstructions {
    Write-Host "`n📥 Docker Desktop Installation Instructions" -ForegroundColor Cyan
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. Download Docker Desktop for Windows from:" -ForegroundColor White
    Write-Host "   https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe" -ForegroundColor Blue
    Write-Host ""
    Write-Host "2. Run the installer as Administrator" -ForegroundColor White
    Write-Host "3. Follow the installation wizard" -ForegroundColor White
    Write-Host "4. Restart your computer when prompted" -ForegroundColor White
    Write-Host "5. Launch Docker Desktop from the Start menu" -ForegroundColor White
    Write-Host "6. Complete the initial setup" -ForegroundColor White
    Write-Host ""
    Write-Host "Alternative: Install via Chocolatey (if you have it):" -ForegroundColor White
    Write-Host "   choco install docker-desktop" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Alternative: Install via winget:" -ForegroundColor White
    Write-Host "   winget install Docker.DockerDesktop" -ForegroundColor Blue
}

# Function to provide troubleshooting steps
function Show-TroubleshootingSteps {
    Write-Host "`n🔧 Troubleshooting Steps" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "If Docker Desktop is installed but not working:" -ForegroundColor White
    Write-Host ""
    Write-Host "1. Restart Docker Desktop:" -ForegroundColor Yellow
    Write-Host "   - Right-click Docker icon in system tray" -ForegroundColor White
    Write-Host "   - Select 'Restart Docker Desktop'" -ForegroundColor White
    Write-Host ""
    Write-Host "2. Reset Docker Desktop:" -ForegroundColor Yellow
    Write-Host "   - Open Docker Desktop Settings" -ForegroundColor White
    Write-Host "   - Go to 'Troubleshoot' tab" -ForegroundColor White
    Write-Host "   - Click 'Reset to factory defaults'" -ForegroundColor White
    Write-Host ""
    Write-Host "3. Check Windows Features:" -ForegroundColor Yellow
    Write-Host "   - Enable 'Windows Subsystem for Linux'" -ForegroundColor White
    Write-Host "   - Enable 'Virtual Machine Platform'" -ForegroundColor White
    Write-Host "   - Enable 'Hyper-V' (if available)" -ForegroundColor White
    Write-Host ""
    Write-Host "4. Update WSL2:" -ForegroundColor Yellow
    Write-Host "   wsl --update" -ForegroundColor Blue
    Write-Host ""
    Write-Host "5. Restart your computer" -ForegroundColor Yellow
}

# Main execution
Write-Host "`n🔍 Starting Docker Diagnostic..." -ForegroundColor Green

# Step 1: Check if Docker is installed
$dockerInstalled = Test-DockerInstallation
if (-not $dockerInstalled) {
    Show-InstallationInstructions
    exit 1
}

# Step 2: Check if Docker service is running
$dockerRunning = Test-DockerService
if (-not $dockerRunning) {
    Write-Host "`n🔄 Docker service is not running. Checking processes..." -ForegroundColor Yellow
    
    # Step 3: Check Docker processes
    $processesRunning = Test-DockerProcesses
    if (-not $processesRunning) {
        Write-Host "`n🚀 Attempting to start Docker Desktop..." -ForegroundColor Yellow
        $started = Start-DockerDesktop
        if (-not $started) {
            Show-TroubleshootingSteps
            exit 1
        }
    }
    else {
        Write-Host "`n⚠️ Docker processes are running but service is not responding." -ForegroundColor Yellow
        Show-TroubleshootingSteps
        exit 1
    }
}

# Step 4: Check container status
Write-Host "`n✅ Docker is running! Checking containers..." -ForegroundColor Green
Test-ContainerStatus

Write-Host "`n🎉 Docker diagnostic complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run: docker-compose up -d" -ForegroundColor Blue
Write-Host "2. Or run: docker-compose -f docker-compose.yml up" -ForegroundColor Blue
Write-Host "3. Check container logs: docker-compose logs" -ForegroundColor Blue
