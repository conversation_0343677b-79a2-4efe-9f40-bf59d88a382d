/**
 * Simple WebSocket connection test
 * This script tests WebSocket connections to both the backend directly and through the frontend proxy
 */

const WebSocket = require('ws');

async function testWebSocketConnection(url, description) {
    console.log(`\n🔌 Testing ${description}: ${url}`);
    
    return new Promise((resolve) => {
        const ws = new WebSocket(url);
        let resolved = false;
        
        const timeout = setTimeout(() => {
            if (!resolved) {
                resolved = true;
                console.log(`❌ ${description}: Connection timeout`);
                ws.terminate();
                resolve(false);
            }
        }, 5000);
        
        ws.on('open', () => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeout);
                console.log(`✅ ${description}: Connection successful`);
                
                // Send a test message
                ws.send(JSON.stringify({
                    type: 'ping',
                    message: 'Test connection',
                    timestamp: new Date().toISOString()
                }));
                
                setTimeout(() => {
                    ws.close();
                    resolve(true);
                }, 1000);
            }
        });
        
        ws.on('message', (data) => {
            console.log(`📨 ${description}: Received message:`, data.toString());
        });
        
        ws.on('error', (error) => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeout);
                console.log(`❌ ${description}: Connection error:`, error.message);
                resolve(false);
            }
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔒 ${description}: Connection closed (${code}): ${reason}`);
        });
    });
}

async function runTests() {
    console.log('🚀 Starting WebSocket Connection Tests...');
    console.log('=' * 50);
    
    // Test direct backend connections
    const backendTests = [
        { url: 'ws://localhost:8000/ws/', description: 'Backend Root' },
        { url: 'ws://localhost:8000/ws/test/', description: 'Backend Test Endpoint' },
        { url: 'ws://localhost:8000/ws/app_builder/', description: 'Backend App Builder' },
        { url: 'ws://localhost:8000/ws/simple/', description: 'Backend Simple Echo' }
    ];
    
    console.log('\n📡 Testing Direct Backend Connections:');
    for (const test of backendTests) {
        await testWebSocketConnection(test.url, test.description);
    }
    
    // Test frontend proxy connections
    const proxyTests = [
        { url: 'ws://localhost:3000/ws/', description: 'Frontend Proxy Root' },
        { url: 'ws://localhost:3000/ws/test/', description: 'Frontend Proxy Test' },
        { url: 'ws://localhost:3000/ws/app_builder/', description: 'Frontend Proxy App Builder' }
    ];
    
    console.log('\n🔄 Testing Frontend Proxy Connections:');
    for (const test of proxyTests) {
        await testWebSocketConnection(test.url, test.description);
    }
    
    console.log('\n✨ WebSocket connection tests completed!');
}

// Run the tests
runTests().catch(console.error);
