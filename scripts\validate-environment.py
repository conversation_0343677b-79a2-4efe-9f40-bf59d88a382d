#!/usr/bin/env python3
"""
Environment Variable Validation Script for App Builder
Validates all environment variables across Docker Compose, .env files, and services
"""

import os
import sys
import yaml
import json
from pathlib import Path

def load_docker_compose():
    """Load docker-compose.yml configuration"""
    try:
        with open('docker-compose.yml', 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"❌ Error loading docker-compose.yml: {e}")
        return None

def load_env_file(path):
    """Load environment file"""
    env_vars = {}
    try:
        with open(path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
        return env_vars
    except FileNotFoundError:
        return {}
    except Exception as e:
        print(f"❌ Error loading {path}: {e}")
        return {}

def validate_database_config(compose_config):
    """Validate database configuration"""
    print("\n🗄️ Database Configuration:")
    
    # Check database service
    db_service = compose_config.get('services', {}).get('db', {})
    db_env = db_service.get('environment', {})
    
    required_db_vars = ['POSTGRES_DB', 'POSTGRES_USER', 'POSTGRES_PASSWORD']
    for var in required_db_vars:
        if var in db_env:
            print(f"  ✅ {var}: {db_env[var]}")
        else:
            print(f"  ❌ Missing {var} in database service")
    
    # Check backend database connection
    backend_service = compose_config.get('services', {}).get('backend', {})
    backend_env = backend_service.get('environment', [])
    
    # Convert list to dict if needed
    if isinstance(backend_env, list):
        backend_env_dict = {}
        for item in backend_env:
            if '=' in item:
                key, value = item.split('=', 1)
                backend_env_dict[key] = value
        backend_env = backend_env_dict
    
    backend_db_vars = ['POSTGRES_DB', 'POSTGRES_USER', 'POSTGRES_PASSWORD', 'POSTGRES_HOST', 'USE_POSTGRES']
    for var in backend_db_vars:
        if var in backend_env:
            print(f"  ✅ Backend {var}: {backend_env[var]}")
        else:
            print(f"  ❌ Missing Backend {var}")

def validate_frontend_config(compose_config):
    """Validate frontend configuration"""
    print("\n🌐 Frontend Configuration:")
    
    frontend_service = compose_config.get('services', {}).get('frontend', {})
    frontend_env = frontend_service.get('environment', [])
    
    # Convert list to dict if needed
    if isinstance(frontend_env, list):
        frontend_env_dict = {}
        for item in frontend_env:
            if '=' in item:
                key, value = item.split('=', 1)
                frontend_env_dict[key] = value
        frontend_env = frontend_env_dict
    
    required_frontend_vars = [
        'REACT_APP_API_URL',
        'REACT_APP_WS_URL',
        'REACT_APP_WS_ENDPOINT',
        'API_TARGET',
        'REACT_APP_WS_PROXY_TARGET'
    ]
    
    for var in required_frontend_vars:
        if var in frontend_env:
            print(f"  ✅ {var}: {frontend_env[var]}")
        else:
            print(f"  ❌ Missing {var}")

def validate_backend_config(compose_config):
    """Validate backend configuration"""
    print("\n🔧 Backend Configuration:")
    
    backend_service = compose_config.get('services', {}).get('backend', {})
    backend_env = backend_service.get('environment', [])
    
    # Convert list to dict if needed
    if isinstance(backend_env, list):
        backend_env_dict = {}
        for item in backend_env:
            if '=' in item:
                key, value = item.split('=', 1)
                backend_env_dict[key] = value
        backend_env = backend_env_dict
    
    required_backend_vars = [
        'DJANGO_SETTINGS_MODULE',
        'DJANGO_SECRET_KEY',
        'DJANGO_DEBUG',
        'DJANGO_ALLOWED_HOSTS',
        'DJANGO_CORS_ALLOWED_ORIGINS',
        'DJANGO_WEBSOCKET_ALLOWED_ORIGINS'
    ]
    
    for var in required_backend_vars:
        if var in backend_env:
            print(f"  ✅ {var}: {backend_env[var]}")
        else:
            print(f"  ❌ Missing {var}")

def validate_port_consistency(compose_config):
    """Validate port consistency"""
    print("\n🔌 Port Configuration:")
    
    services = compose_config.get('services', {})
    
    # Check backend port
    backend_ports = services.get('backend', {}).get('ports', [])
    if backend_ports:
        print(f"  ✅ Backend ports: {backend_ports}")
    else:
        print("  ❌ Backend ports not configured")
    
    # Check frontend port
    frontend_ports = services.get('frontend', {}).get('ports', [])
    if frontend_ports:
        print(f"  ✅ Frontend ports: {frontend_ports}")
    else:
        print("  ❌ Frontend ports not configured")
    
    # Check database port
    db_ports = services.get('db', {}).get('ports', [])
    if db_ports:
        print(f"  ✅ Database ports: {db_ports}")
    else:
        print("  ❌ Database ports not configured")

def validate_env_files():
    """Validate environment files"""
    print("\n📄 Environment Files:")
    
    env_files = ['.env', 'frontend/.env']
    
    for env_file in env_files:
        if os.path.exists(env_file):
            env_vars = load_env_file(env_file)
            print(f"  ✅ {env_file}: {len(env_vars)} variables")
            
            # Show key variables
            key_vars = ['REACT_APP_API_URL', 'REACT_APP_WS_URL', 'DJANGO_SECRET_KEY']
            for var in key_vars:
                if var in env_vars:
                    print(f"    ✅ {var}: {env_vars[var]}")
        else:
            print(f"  ⚠️ {env_file}: Not found")

def main():
    """Main validation function"""
    print("🔍 App Builder Environment Variable Validation")
    print("=" * 50)
    
    # Change to project root
    os.chdir(Path(__file__).parent.parent)
    
    # Load docker-compose configuration
    compose_config = load_docker_compose()
    if not compose_config:
        print("❌ Cannot proceed without docker-compose.yml")
        sys.exit(1)
    
    # Run validations
    validate_database_config(compose_config)
    validate_backend_config(compose_config)
    validate_frontend_config(compose_config)
    validate_port_consistency(compose_config)
    validate_env_files()
    
    print("\n✅ Environment validation complete!")
    print("\n💡 Next steps:")
    print("  1. Fix any missing environment variables")
    print("  2. Restart containers: docker-compose down && docker-compose up")
    print("  3. Test connections: docker-compose logs")

if __name__ == "__main__":
    main()
