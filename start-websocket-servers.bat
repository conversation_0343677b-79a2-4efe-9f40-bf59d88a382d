@echo off
echo ========================================
echo   App Builder 201 - WebSocket Servers
echo ========================================
echo.
echo Starting servers with WebSocket support...
echo.

echo 1. Starting Django ASGI server (Backend) on port 8000...
echo    - WebSocket support: ENABLED
echo    - Server: Daphne ASGI
echo.
start "Django ASGI Backend" cmd /k "cd backend && daphne -b 0.0.0.0 -p 8000 app_builder_201.asgi:application"

echo 2. Waiting 5 seconds for backend to start...
timeout /t 5 /nobreak > nul

echo 3. Starting React development server (Frontend) on port 3000...
echo    - WebSocket proxy: ENABLED (/ws -> localhost:8000)
echo    - API proxy: ENABLED (/api -> localhost:8000)
echo.
start "React Frontend" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo   Servers Starting...
echo ========================================
echo.
echo Backend (Django ASGI):  http://localhost:8000
echo Frontend (React):       http://localhost:3000
echo WebSocket Test Page:    http://localhost:3000/websocket
echo.
echo WebSocket URLs:
echo   Direct Backend:       ws://localhost:8000/ws/
echo   Through Frontend:     ws://localhost:3000/ws/
echo.
echo ========================================
echo   Important Notes:
echo ========================================
echo.
echo ✅ FIXED: "Invalid frame header" error
echo    - Issue: Using Django runserver (no WebSocket support)
echo    - Solution: Using Daphne ASGI server
echo.
echo 🔧 For WebSocket development, always use:
echo    Backend:  daphne -b 0.0.0.0 -p 8000 app_builder_201.asgi:application
echo    NOT:      python manage.py runserver
echo.
echo 📋 Test WebSocket connections:
echo    1. Open: http://localhost:3000/websocket
echo    2. Or use: test-websocket-final.html
echo.
echo Press any key to open test pages...
pause > nul

echo Opening test pages...
start http://localhost:3000
start http://localhost:3000/websocket
start file:///%~dp0test-websocket-final.html

echo.
echo All servers and test pages opened!
echo Check the terminal windows for server logs.
echo.
pause
