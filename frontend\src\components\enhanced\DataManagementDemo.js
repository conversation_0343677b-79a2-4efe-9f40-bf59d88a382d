import React, { useState } from 'react';
import { Card, Button, Input, Select, Switch, Typography, Space, Divider, Alert, Spin } from 'antd';
import { SaveOutlined, ReloadOutlined, ClearOutlined, InfoCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import useDataManager from '../../hooks/useDataManager';
import dataManager from '../../utils/dataManager';
import { setCurrentView } from '../../redux/minimal-store';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const DemoContainer = styled.div`
  padding: 20px;
`;

const DemoCard = styled(Card)`
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
  
  .label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const InfoBox = styled(Alert)`
  margin-bottom: 16px;
`;

/**
 * DataManagementDemo component
 * Demonstrates the use of data management utilities
 */
const DataManagementDemo = () => {
  // Local state
  const [inputValue, setInputValue] = useState('');
  const [cacheType, setCacheType] = useState('temporary');
  const [useCache, setUseCache] = useState(true);
  
  // Use our custom hook to manage data
  const { 
    data: currentView, 
    loading, 
    error, 
    updateData, 
    refreshData, 
    clearCache 
  } = useDataManager({
    selector: state => state.ui?.currentView,
    action: setCurrentView,
    cacheKey: 'current_view',
    cacheType,
    useCache,
    defaultValue: 'components'
  });
  
  // Handle saving data
  const handleSave = async () => {
    if (!inputValue.trim()) return;
    
    try {
      await updateData(inputValue);
      setInputValue('');
    } catch (error) {
      console.error('Error saving data:', error);
    }
  };
  
  // Handle refreshing data
  const handleRefresh = async () => {
    try {
      await refreshData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };
  
  // Handle clearing cache
  const handleClearCache = () => {
    clearCache();
  };
  
  // Handle direct cache operations
  const handleSetDirectCache = () => {
    if (!inputValue.trim()) return;
    
    dataManager.setCache('direct_cache_demo', inputValue, {
      type: cacheType,
      expiresIn: 3600000 // 1 hour
    });
    
    setInputValue('');
  };
  
  const handleGetDirectCache = () => {
    const cachedValue = dataManager.getCache('direct_cache_demo', cacheType);
    setInputValue(cachedValue || '');
  };
  
  return (
    <DemoContainer>
      <Title level={3}>Data Management Demo</Title>
      <Paragraph>
        This demo shows how to use the data management utilities to optimize Redux usage and handle data operations.
      </Paragraph>
      
      <DemoCard title="Current View Data">
        <InfoBox
          type="info"
          message="Redux + Cache Integration"
          description="This example shows how to use the useDataManager hook to manage data with Redux and caching."
          icon={<InfoCircleOutlined />}
        />
        
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '10px' }}>Loading data...</div>
          </div>
        ) : (
          <>
            {error && (
              <Alert
                type="error"
                message="Error"
                description={error.message || 'An error occurred while managing data'}
                style={{ marginBottom: '16px' }}
              />
            )}
            
            <FormGroup>
              <div className="label">Current View:</div>
              <Text strong>{currentView}</Text>
            </FormGroup>
            
            <FormGroup>
              <div className="label">New View:</div>
              <Input
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                placeholder="Enter a new view name"
              />
            </FormGroup>
            
            <FormGroup>
              <div className="label">Cache Type:</div>
              <Select
                value={cacheType}
                onChange={setCacheType}
                style={{ width: '100%' }}
              >
                <Option value="temporary">Temporary (Memory only)</Option>
                <Option value="persistent">Persistent (LocalStorage)</Option>
                <Option value="expiring">Expiring (Time-based)</Option>
              </Select>
            </FormGroup>
            
            <FormGroup>
              <div className="label">Use Cache:</div>
              <Switch checked={useCache} onChange={setUseCache} />
            </FormGroup>
            
            <ButtonGroup>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                disabled={!inputValue.trim()}
              >
                Save to Redux
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              >
                Refresh Data
              </Button>
              <Button
                danger
                icon={<ClearOutlined />}
                onClick={handleClearCache}
              >
                Clear Cache
              </Button>
            </ButtonGroup>
          </>
        )}
      </DemoCard>
      
      <DemoCard title="Direct Cache Operations">
        <InfoBox
          type="info"
          message="Direct Cache API"
          description="This example shows how to use the dataManager utility directly for caching operations."
          icon={<InfoCircleOutlined />}
        />
        
        <FormGroup>
          <div className="label">Cache Value:</div>
          <Input
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            placeholder="Enter a value to cache"
          />
        </FormGroup>
        
        <FormGroup>
          <div className="label">Cache Type:</div>
          <Select
            value={cacheType}
            onChange={setCacheType}
            style={{ width: '100%' }}
          >
            <Option value="temporary">Temporary (Memory only)</Option>
            <Option value="persistent">Persistent (LocalStorage)</Option>
            <Option value="expiring">Expiring (Time-based)</Option>
          </Select>
        </FormGroup>
        
        <ButtonGroup>
          <Button
            type="primary"
            onClick={handleSetDirectCache}
            disabled={!inputValue.trim()}
          >
            Set Cache
          </Button>
          <Button
            onClick={handleGetDirectCache}
          >
            Get Cache
          </Button>
          <Button
            danger
            onClick={() => dataManager.clearCache('direct_cache_demo', cacheType)}
          >
            Clear Cache
          </Button>
        </ButtonGroup>
      </DemoCard>
    </DemoContainer>
  );
};

export default DataManagementDemo;
