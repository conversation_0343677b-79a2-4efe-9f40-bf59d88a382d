/**
 * Error Handling Utilities
 * 
 * This module provides utilities for handling errors in the application.
 * It includes functions for handling specific types of errors and providing
 * user-friendly error messages.
 */

// Error types
export const ErrorTypes = {
  NETWORK: 'NETWORK',
  WEBSOCKET: 'WEBSOCKET',
  API: 'API',
  SERVICE_WORKER: 'SERVICE_WORKER',
  AUTHENTICATION: 'AUTHENTICATION',
  UNKNOWN: 'UNKNOWN'
};

/**
 * Determine the type of error
 * @param {Error} error - The error to analyze
 * @returns {string} The error type
 */
export function getErrorType(error) {
  if (!error) return ErrorTypes.UNKNOWN;

  // Check for network errors
  if (
    error.name === 'NetworkError' ||
    error.message.includes('network') ||
    error.message.includes('Network') ||
    error.message.includes('Failed to fetch') ||
    error.message.includes('Network request failed')
  ) {
    return ErrorTypes.NETWORK;
  }

  // Check for WebSocket errors
  if (
    error.name === 'WebSocketError' ||
    error.message.includes('WebSocket') ||
    error.message.includes('websocket') ||
    error.message.includes('socket')
  ) {
    return ErrorTypes.WEBSOCKET;
  }

  // Check for API errors
  if (
    error.name === 'ApiError' ||
    error.message.includes('API') ||
    error.message.includes('api') ||
    error.message.includes('endpoint')
  ) {
    return ErrorTypes.API;
  }

  // Check for Service Worker errors
  if (
    error.name === 'ServiceWorkerError' ||
    error.message.includes('Service Worker') ||
    error.message.includes('service worker') ||
    error.message.includes('ServiceWorker')
  ) {
    return ErrorTypes.SERVICE_WORKER;
  }

  // Check for authentication errors
  if (
    error.name === 'AuthenticationError' ||
    error.message.includes('authentication') ||
    error.message.includes('Authentication') ||
    error.message.includes('unauthorized') ||
    error.message.includes('Unauthorized') ||
    error.message.includes('forbidden') ||
    error.message.includes('Forbidden')
  ) {
    return ErrorTypes.AUTHENTICATION;
  }

  // Default to unknown
  return ErrorTypes.UNKNOWN;
}

/**
 * Get a user-friendly error message
 * @param {Error} error - The error to get a message for
 * @returns {string} A user-friendly error message
 */
export function getUserFriendlyErrorMessage(error) {
  if (!error) return 'An unknown error occurred';

  const errorType = getErrorType(error);

  switch (errorType) {
    case ErrorTypes.NETWORK:
      return 'A network error occurred. Please check your internet connection and try again.';
    case ErrorTypes.WEBSOCKET:
      return 'A WebSocket connection error occurred. Real-time updates may not be available.';
    case ErrorTypes.API:
      return 'An API error occurred. The server may be unavailable or experiencing issues.';
    case ErrorTypes.SERVICE_WORKER:
      return 'A Service Worker error occurred. Offline functionality may be limited.';
    case ErrorTypes.AUTHENTICATION:
      return 'An authentication error occurred. Please log in again.';
    case ErrorTypes.UNKNOWN:
    default:
      return `An error occurred: ${error.message}`;
  }
}

/**
 * Get troubleshooting steps for an error
 * @param {Error} error - The error to get troubleshooting steps for
 * @returns {string[]} An array of troubleshooting steps
 */
export function getTroubleshootingSteps(error) {
  if (!error) return ['Try refreshing the page'];

  const errorType = getErrorType(error);

  switch (errorType) {
    case ErrorTypes.NETWORK:
      return [
        'Check your internet connection',
        'Try refreshing the page',
        'Check if the server is running',
        'Try again later'
      ];
    case ErrorTypes.WEBSOCKET:
      return [
        'Check your internet connection',
        'Try refreshing the page',
        'Check if the WebSocket server is running',
        'Check if your firewall is blocking WebSocket connections'
      ];
    case ErrorTypes.API:
      return [
        'Check if the API server is running',
        'Try refreshing the page',
        'Check your internet connection',
        'Try again later'
      ];
    case ErrorTypes.SERVICE_WORKER:
      return [
        'Clear your browser cache',
        'Try refreshing the page',
        'Try using a different browser',
        'Check if your browser supports Service Workers'
      ];
    case ErrorTypes.AUTHENTICATION:
      return [
        'Try logging in again',
        'Check if your session has expired',
        'Clear your browser cache',
        'Contact support if the issue persists'
      ];
    case ErrorTypes.UNKNOWN:
    default:
      return [
        'Try refreshing the page',
        'Clear your browser cache',
        'Try again later',
        'Contact support if the issue persists'
      ];
  }
}

/**
 * Log an error with additional context
 * @param {Error} error - The error to log
 * @param {string} context - The context in which the error occurred
 * @param {Object} additionalData - Additional data to log
 */
export function logError(error, context = '', additionalData = {}) {
  if (!error) return;

  const errorType = getErrorType(error);
  const timestamp = new Date().toISOString();

  console.error(`[${timestamp}] [${errorType}] ${context}:`, error, additionalData);

  // You could also send the error to a logging service here
}

/**
 * Handle an error by logging it and returning user-friendly information
 * @param {Error} error - The error to handle
 * @param {string} context - The context in which the error occurred
 * @returns {Object} An object with error information
 */
export function handleError(error, context = '') {
  if (!error) return { message: 'An unknown error occurred', type: ErrorTypes.UNKNOWN };

  // Log the error
  logError(error, context);

  // Get error information
  const errorType = getErrorType(error);
  const message = getUserFriendlyErrorMessage(error);
  const troubleshootingSteps = getTroubleshootingSteps(error);

  return {
    originalError: error,
    message,
    type: errorType,
    troubleshootingSteps
  };
}

export default {
  ErrorTypes,
  getErrorType,
  getUserFriendlyErrorMessage,
  getTroubleshootingSteps,
  logError,
  handleError
};
