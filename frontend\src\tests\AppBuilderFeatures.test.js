/**
 * Comprehensive test suite for App Builder features
 * Tests all the enhanced functionality including component builder,
 * layout designer, theme manager, examples, and tutorial system.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import components to test
import IntegratedAppBuilder from '../components/builder/IntegratedAppBuilder';
import AppBuilderExamples from '../components/examples/AppBuilderExamples';
import IntegratedTutorialSystem from '../components/tutorial/IntegratedTutorialSystem';
import UXEnhancedPropertyEditor from '../components/builder/UXEnhancedPropertyEditor';

// Mock Redux store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: (state = { user: { id: 'test-user', hasCompletedTutorial: false } }) => state,
      projects: (state = { current: { name: 'Test Project', settings: {} } }) => state,
      themes: (state = { 
        themes: [], 
        activeTheme: 'default',
        userPreferences: { savedTheme: null, autoApplyTheme: true }
      }) => state,
      components: (state = { components: [] }) => state,
    },
    preloadedState: initialState,
  });
};

// Mock WebSocket
jest.mock('../hooks/useWebSocket', () => ({
  __esModule: true,
  default: () => ({
    isConnected: true,
    collaborators: [],
    sendUpdate: jest.fn(),
    sendCursor: jest.fn(),
  }),
}));

// Mock other hooks
jest.mock('../hooks/useAppBuilder', () => ({
  __esModule: true,
  default: () => ({
    components: [],
    addComponent: jest.fn(),
    updateComponent: jest.fn(),
    deleteComponent: jest.fn(),
    moveComponent: jest.fn(),
    duplicateComponent: jest.fn(),
    undoAction: jest.fn(),
    redoAction: jest.fn(),
    canUndo: false,
    canRedo: false,
    saveProject: jest.fn(),
    loadProject: jest.fn(),
    isModified: false,
  }),
}));

jest.mock('../hooks/useTutorial', () => ({
  __esModule: true,
  default: () => ({
    isActive: false,
    currentStep: 0,
    totalSteps: 8,
    nextStep: jest.fn(),
    previousStep: jest.fn(),
    skipTutorial: jest.fn(),
    startTutorial: jest.fn(),
  }),
}));

jest.mock('../hooks/useAIDesignSuggestions', () => ({
  __esModule: true,
  default: () => ({
    suggestions: [],
    loading: false,
    generateSuggestions: jest.fn(),
    applySuggestion: jest.fn(),
    dismissSuggestion: jest.fn(),
  }),
}));

jest.mock('../hooks/useTemplates', () => ({
  __esModule: true,
  default: () => ({
    templates: [],
    loading: false,
    saveAsTemplate: jest.fn(),
    loadTemplate: jest.fn(),
    deleteTemplate: jest.fn(),
  }),
}));

jest.mock('../hooks/useCodeExport', () => ({
  __esModule: true,
  default: () => ({
    exportCode: jest.fn(),
    exportFormats: ['react', 'vue', 'angular'],
    loading: false,
    downloadCode: jest.fn(),
  }),
}));

jest.mock('../hooks/useCollaboration', () => ({
  __esModule: true,
  default: () => ({
    activeUsers: [],
    comments: [],
    addComment: jest.fn(),
    resolveComment: jest.fn(),
    shareProject: jest.fn(),
  }),
}));

describe('App Builder Features', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('IntegratedAppBuilder', () => {
    test('renders main app builder interface', async () => {
      render(
        <Provider store={store}>
          <IntegratedAppBuilder projectId="test-project" />
        </Provider>
      );

      // Check if main elements are present
      expect(screen.getByText('App Builder')).toBeInTheDocument();
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });

    test('displays status indicators', async () => {
      render(
        <Provider store={store}>
          <IntegratedAppBuilder projectId="test-project" />
        </Provider>
      );

      // Check for connection status
      expect(screen.getByText('Connected')).toBeInTheDocument();
      
      // Check for component count
      expect(screen.getByText('0 Components')).toBeInTheDocument();
    });

    test('examples button opens modal', async () => {
      render(
        <Provider store={store}>
          <IntegratedAppBuilder projectId="test-project" />
        </Provider>
      );

      // Find and click examples button
      const examplesButton = screen.getByText('📚 Examples');
      fireEvent.click(examplesButton);

      // Check if modal opens
      await waitFor(() => {
        expect(screen.getByText('App Builder Examples & Tutorials')).toBeInTheDocument();
      });
    });

    test('preview mode toggle works', async () => {
      render(
        <Provider store={store}>
          <IntegratedAppBuilder projectId="test-project" />
        </Provider>
      );

      // Find preview button
      const previewButton = screen.getByText('👁️ Preview');
      fireEvent.click(previewButton);

      // Check if button text changes
      await waitFor(() => {
        expect(screen.getByText('👁️ Exit Preview')).toBeInTheDocument();
      });
    });
  });

  describe('AppBuilderExamples', () => {
    test('renders examples interface', () => {
      render(<AppBuilderExamples />);

      // Check main title
      expect(screen.getByText('App Builder Examples')).toBeInTheDocument();
      
      // Check tabs
      expect(screen.getByText('Component Builder')).toBeInTheDocument();
      expect(screen.getByText('Layout Designer')).toBeInTheDocument();
      expect(screen.getByText('Theme Manager')).toBeInTheDocument();
    });

    test('component builder example works', () => {
      render(<AppBuilderExamples />);

      // Find create component button
      const createButton = screen.getByText('Create Component');
      fireEvent.click(createButton);

      // Check if component is created
      expect(screen.getByText('Created Component:')).toBeInTheDocument();
    });

    test('layout designer example works', () => {
      render(<AppBuilderExamples />);

      // Switch to layout designer tab
      fireEvent.click(screen.getByText('Layout Designer'));

      // Find add layout item button
      const addButton = screen.getByText('Add Layout Item');
      fireEvent.click(addButton);

      // Check if item is added
      expect(screen.getByText('Item 1')).toBeInTheDocument();
    });

    test('theme manager example works', () => {
      render(<AppBuilderExamples />);

      // Switch to theme manager tab
      fireEvent.click(screen.getByText('Theme Manager'));

      // Find theme buttons
      const oceanThemeButton = screen.getByText('Ocean Blue');
      fireEvent.click(oceanThemeButton);

      // Check if theme is applied
      expect(screen.getByText('Ocean Blue')).toBeInTheDocument();
    });
  });

  describe('IntegratedTutorialSystem', () => {
    test('renders tutorial system', () => {
      const mockOnComplete = jest.fn();
      
      render(
        <IntegratedTutorialSystem onTutorialComplete={mockOnComplete}>
          <div>Test Content</div>
        </IntegratedTutorialSystem>
      );

      // Check if help button is present
      expect(screen.getByTitle('Get Help')).toBeInTheDocument();
      
      // Check if content is rendered
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    test('help drawer opens and closes', async () => {
      const mockOnComplete = jest.fn();
      
      render(
        <IntegratedTutorialSystem onTutorialComplete={mockOnComplete}>
          <div>Test Content</div>
        </IntegratedTutorialSystem>
      );

      // Click help button
      const helpButton = screen.getByTitle('Get Help');
      fireEvent.click(helpButton);

      // Check if drawer opens
      await waitFor(() => {
        expect(screen.getByText('Help & Tutorials')).toBeInTheDocument();
      });
    });
  });

  describe('UXEnhancedPropertyEditor', () => {
    test('renders property editor with component', () => {
      const mockComponent = {
        id: 'test-component',
        type: 'button',
        props: {
          text: 'Test Button',
          variant: 'primary',
          size: 'medium'
        }
      };

      const mockOnUpdate = jest.fn();

      render(
        <UXEnhancedPropertyEditor 
          component={mockComponent}
          onUpdateComponent={mockOnUpdate}
        />
      );

      // Check if component properties are displayed
      expect(screen.getByDisplayValue('Test Button')).toBeInTheDocument();
    });

    test('reset button works', () => {
      const mockComponent = {
        id: 'test-component',
        type: 'button',
        props: {
          text: 'Test Button',
          variant: 'primary',
          size: 'medium'
        }
      };

      const mockOnUpdate = jest.fn();

      render(
        <UXEnhancedPropertyEditor 
          component={mockComponent}
          onUpdateComponent={mockOnUpdate}
        />
      );

      // Find reset button and click it
      const resetButton = screen.getByText('Reset');
      fireEvent.click(resetButton);

      // Check if success message appears (mocked)
      // In a real test, you'd check if the form values are reset
    });
  });

  describe('Feature Integration', () => {
    test('all features can be enabled', () => {
      const enableFeatures = {
        websocket: true,
        tutorial: true,
        aiSuggestions: true,
        templates: true,
        codeExport: true,
        collaboration: true,
      };

      render(
        <Provider store={store}>
          <IntegratedAppBuilder 
            projectId="test-project"
            enableFeatures={enableFeatures}
          />
        </Provider>
      );

      // Check if app renders without errors
      expect(screen.getByText('App Builder')).toBeInTheDocument();
    });

    test('tutorial data attributes are present', () => {
      render(
        <Provider store={store}>
          <IntegratedAppBuilder projectId="test-project" />
        </Provider>
      );

      // Check for tutorial data attributes
      expect(document.querySelector('[data-tutorial="component-palette"]')).toBeInTheDocument();
      expect(document.querySelector('[data-tutorial="property-editor"]')).toBeInTheDocument();
      expect(document.querySelector('[data-tutorial="canvas-area"]')).toBeInTheDocument();
      expect(document.querySelector('[data-tutorial="preview-mode"]')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('handles missing component gracefully', () => {
      const mockOnUpdate = jest.fn();

      render(
        <UXEnhancedPropertyEditor 
          component={null}
          onUpdateComponent={mockOnUpdate}
        />
      );

      // Should render without crashing
      expect(screen.getByText('No component selected')).toBeInTheDocument();
    });

    test('handles network errors gracefully', () => {
      // Mock network error
      const storeWithError = createMockStore({
        auth: { user: null, error: 'Network error' }
      });

      render(
        <Provider store={storeWithError}>
          <IntegratedAppBuilder projectId="test-project" />
        </Provider>
      );

      // Should still render the main interface
      expect(screen.getByText('App Builder')).toBeInTheDocument();
    });
  });
});

// Test utilities
export const testUtils = {
  createMockStore,
  mockComponent: {
    id: 'test-component',
    type: 'button',
    props: {
      text: 'Test Button',
      variant: 'primary',
      size: 'medium'
    }
  },
  mockProject: {
    id: 'test-project',
    name: 'Test Project',
    settings: {}
  }
};
