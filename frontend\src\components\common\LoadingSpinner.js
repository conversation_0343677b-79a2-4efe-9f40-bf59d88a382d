import React from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import './LoadingSpinner.css';

/**
 * Enhanced loading spinner component with customizable options
 *
 * @param {Object} props Component props
 * @param {string} props.tip Text to display below the spinner
 * @param {string} props.size Size of the spinner ('small', 'default', 'large')
 * @param {boolean} props.fullScreen Whether to display the spinner in fullscreen mode
 * @param {string} props.backgroundColor Background color of the container
 * @param {React.ReactNode} props.icon Custom icon to use for the spinner
 * @param {string} props.className Additional CSS class names
 * @param {Object} props.style Additional inline styles
 * @returns {React.ReactElement} Loading spinner component
 */
const LoadingSpinner = ({
  tip = 'Loading...',
  size = 'large',
  fullScreen = false,
  backgroundColor = 'rgba(255, 255, 255, 0.8)',
  icon = null,
  className = '',
  style = {}
}) => {
  // Custom loading icon
  const antIcon = icon || <LoadingOutlined style={{ fontSize: size === 'large' ? 40 : 24 }} spin />;

  // If fullScreen is true, render the spinner in a fullscreen container
  if (fullScreen) {
    return (
      <div
        className={`loading-container ${className}`}
        style={{ backgroundColor, ...style }}
        aria-live="polite"
        aria-busy="true"
      >
        <div className="loading-content">
          <Spin
            className="loading-spinner"
            indicator={antIcon}
            size={size}
          />
          {tip && (
            <div
              className="loading-text"
              role="status"
            >
              {tip}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Otherwise, render the spinner inline
  return (
    <div
      style={{ textAlign: 'center', padding: '20px', ...style }}
      className={className}
      aria-live="polite"
      aria-busy="true"
    >
      <Spin indicator={antIcon} size={size} />
      {tip && (
        <div
          style={{ marginTop: '12px' }}
          role="status"
        >
          {tip}
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
