import React, { useState } from 'react';
import { Select, InputNumber, Space, Typography, Card, Button, Slider } from 'antd';
import { FontSizeOutlined, BoldOutlined, ItalicOutlined, UnderlineOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

/**
 * Typography Editor
 * 
 * Advanced typography editor with font selection, sizing, and styling options.
 */

const TypographyEditor = ({
  value = {},
  onChange,
  showPreview = true
}) => {
  const [previewText, setPreviewText] = useState('The quick brown fox jumps over the lazy dog');

  const fontFamilies = [
    'Arial, sans-serif',
    'Helvetica, sans-serif',
    'Georgia, serif',
    'Times New Roman, serif',
    'Courier New, monospace',
    'Verdana, sans-serif',
    'Trebuchet MS, sans-serif',
    'Impact, sans-serif'
  ];

  const fontWeights = [
    { label: 'Thin', value: 100 },
    { label: 'Light', value: 300 },
    { label: 'Normal', value: 400 },
    { label: 'Medium', value: 500 },
    { label: 'Semi Bold', value: 600 },
    { label: 'Bold', value: 700 },
    { label: 'Extra Bold', value: 800 },
    { label: 'Black', value: 900 }
  ];

  const handleChange = (property, newValue) => {
    const updated = { ...value, [property]: newValue };
    if (onChange) onChange(updated);
  };

  const previewStyle = {
    fontFamily: value.fontFamily || 'Arial, sans-serif',
    fontSize: `${value.fontSize || 16}px`,
    fontWeight: value.fontWeight || 400,
    fontStyle: value.fontStyle || 'normal',
    textDecoration: value.textDecoration || 'none',
    lineHeight: value.lineHeight || 1.5,
    letterSpacing: `${value.letterSpacing || 0}px`,
    textAlign: value.textAlign || 'left',
    color: value.color || '#000000'
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Text strong>Typography</Text>

      {/* Font Family */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Font Family</Text>
        <Select
          value={value.fontFamily || 'Arial, sans-serif'}
          onChange={(val) => handleChange('fontFamily', val)}
          style={{ width: '100%', marginTop: 4 }}
        >
          {fontFamilies.map(font => (
            <Option key={font} value={font} style={{ fontFamily: font }}>
              {font.split(',')[0]}
            </Option>
          ))}
        </Select>
      </div>

      {/* Font Size and Weight */}
      <Space style={{ width: '100%' }}>
        <div style={{ flex: 1 }}>
          <Text style={{ fontSize: 12, color: '#666' }}>Size</Text>
          <InputNumber
            value={value.fontSize || 16}
            onChange={(val) => handleChange('fontSize', val)}
            min={8}
            max={72}
            style={{ width: '100%', marginTop: 4 }}
            addonAfter="px"
          />
        </div>
        <div style={{ flex: 1 }}>
          <Text style={{ fontSize: 12, color: '#666' }}>Weight</Text>
          <Select
            value={value.fontWeight || 400}
            onChange={(val) => handleChange('fontWeight', val)}
            style={{ width: '100%', marginTop: 4 }}
          >
            {fontWeights.map(weight => (
              <Option key={weight.value} value={weight.value}>
                {weight.label}
              </Option>
            ))}
          </Select>
        </div>
      </Space>

      {/* Style buttons */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Style</Text>
        <div style={{ marginTop: 4 }}>
          <Space>
            <Button
              size="small"
              icon={<BoldOutlined />}
              type={value.fontWeight >= 600 ? 'primary' : 'default'}
              onClick={() => handleChange('fontWeight', value.fontWeight >= 600 ? 400 : 700)}
            />
            <Button
              size="small"
              icon={<ItalicOutlined />}
              type={value.fontStyle === 'italic' ? 'primary' : 'default'}
              onClick={() => handleChange('fontStyle', value.fontStyle === 'italic' ? 'normal' : 'italic')}
            />
            <Button
              size="small"
              icon={<UnderlineOutlined />}
              type={value.textDecoration === 'underline' ? 'primary' : 'default'}
              onClick={() => handleChange('textDecoration', value.textDecoration === 'underline' ? 'none' : 'underline')}
            />
          </Space>
        </div>
      </div>

      {/* Line Height */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Line Height: {value.lineHeight || 1.5}</Text>
        <Slider
          value={value.lineHeight || 1.5}
          onChange={(val) => handleChange('lineHeight', val)}
          min={1}
          max={3}
          step={0.1}
          style={{ marginTop: 4 }}
        />
      </div>

      {/* Letter Spacing */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Letter Spacing: {value.letterSpacing || 0}px</Text>
        <Slider
          value={value.letterSpacing || 0}
          onChange={(val) => handleChange('letterSpacing', val)}
          min={-2}
          max={10}
          step={0.5}
          style={{ marginTop: 4 }}
        />
      </div>

      {/* Text Align */}
      <div>
        <Text style={{ fontSize: 12, color: '#666' }}>Text Align</Text>
        <Select
          value={value.textAlign || 'left'}
          onChange={(val) => handleChange('textAlign', val)}
          style={{ width: '100%', marginTop: 4 }}
        >
          <Option value="left">Left</Option>
          <Option value="center">Center</Option>
          <Option value="right">Right</Option>
          <Option value="justify">Justify</Option>
        </Select>
      </div>

      {/* Preview */}
      {showPreview && (
        <Card size="small" title="Preview">
          <div style={previewStyle}>
            {previewText}
          </div>
        </Card>
      )}
    </Space>
  );
};

export default TypographyEditor;
