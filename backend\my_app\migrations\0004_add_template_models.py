# Generated by Django 5.1.6 on 2025-06-20 08:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my_app', '0003_alter_app_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AppTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('app_category', models.CharField(choices=[('business', 'Business Apps'), ('ecommerce', 'E-commerce'), ('portfolio', 'Portfolio'), ('dashboard', 'Dashboard'), ('landing', 'Landing Page'), ('blog', 'Blog'), ('social', 'Social Media'), ('education', 'Education'), ('healthcare', 'Healthcare'), ('finance', 'Finance'), ('other', 'Other')], default='other', max_length=100)),
                ('components', models.JSONField(default=dict, help_text='Complete app structure with components')),
                ('default_props', models.JSONField(default=dict, help_text='Default properties for the app')),
                ('required_components', models.JSONField(default=list, help_text='List of required component dependencies')),
                ('preview_image', models.URLField(blank=True, help_text='URL to template preview image')),
                ('is_public', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='app_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LayoutTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('layout_type', models.CharField(help_text='Type of layout (grid, flex, sidebar, etc.)', max_length=100)),
                ('components', models.JSONField(default=dict, help_text='Component references and configurations')),
                ('default_props', models.JSONField(default=dict, help_text='Default properties for the layout')),
                ('is_public', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='layout_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.DeleteModel(
            name='YourModel',
        ),
        migrations.AddIndex(
            model_name='apptemplate',
            index=models.Index(fields=['user'], name='my_app_appt_user_id_8a3a58_idx'),
        ),
        migrations.AddIndex(
            model_name='apptemplate',
            index=models.Index(fields=['is_public'], name='my_app_appt_is_publ_d003a2_idx'),
        ),
        migrations.AddIndex(
            model_name='apptemplate',
            index=models.Index(fields=['app_category'], name='my_app_appt_app_cat_76ee9e_idx'),
        ),
        migrations.AddIndex(
            model_name='apptemplate',
            index=models.Index(fields=['created_at'], name='my_app_appt_created_a1c74f_idx'),
        ),
        migrations.AddIndex(
            model_name='layouttemplate',
            index=models.Index(fields=['user'], name='my_app_layo_user_id_b51705_idx'),
        ),
        migrations.AddIndex(
            model_name='layouttemplate',
            index=models.Index(fields=['is_public'], name='my_app_layo_is_publ_9fd8d3_idx'),
        ),
        migrations.AddIndex(
            model_name='layouttemplate',
            index=models.Index(fields=['layout_type'], name='my_app_layo_layout__e53ecc_idx'),
        ),
        migrations.AddIndex(
            model_name='layouttemplate',
            index=models.Index(fields=['created_at'], name='my_app_layo_created_26234a_idx'),
        ),
    ]
