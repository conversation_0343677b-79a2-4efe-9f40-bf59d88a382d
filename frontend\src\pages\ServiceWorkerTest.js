import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Alert, Divider, List, Tag, Progress } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  SyncOutlined, 
  WifiOutlined,
  CloudOutlined,
  BellOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

const ServiceWorkerTest = () => {
  const [swStatus, setSwStatus] = useState({
    supported: false,
    registered: false,
    active: false,
    installing: false,
    waiting: false,
    error: null
  });
  
  const [cacheStatus, setCacheStatus] = useState({
    available: false,
    caches: [],
    totalSize: 0
  });
  
  const [notificationStatus, setNotificationStatus] = useState({
    supported: false,
    permission: 'default'
  });
  
  const [offlineStatus, setOfflineStatus] = useState(navigator.onLine);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    checkServiceWorkerSupport();
    checkCacheAPI();
    checkNotificationSupport();
    
    // Listen for online/offline events
    const handleOnline = () => setOfflineStatus(true);
    const handleOffline = () => setOfflineStatus(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const checkServiceWorkerSupport = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        const registration = await navigator.serviceWorker.getRegistration();
        
        setSwStatus({
          supported: true,
          registered: registrations.length > 0,
          active: registration && registration.active !== null,
          installing: registration && registration.installing !== null,
          waiting: registration && registration.waiting !== null,
          error: null
        });
        
        // Listen for service worker updates
        if (registration) {
          registration.addEventListener('updatefound', () => {
            console.log('Service Worker update found');
            checkServiceWorkerSupport(); // Refresh status
          });
        }
      } catch (error) {
        setSwStatus(prev => ({ ...prev, error: error.message }));
      }
    } else {
      setSwStatus(prev => ({ ...prev, supported: false }));
    }
  };

  const checkCacheAPI = async () => {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        let totalSize = 0;
        
        const cacheDetails = await Promise.all(
          cacheNames.map(async (name) => {
            const cache = await caches.open(name);
            const keys = await cache.keys();
            return {
              name,
              itemCount: keys.length,
              items: keys.slice(0, 5).map(req => req.url) // Show first 5 items
            };
          })
        );
        
        setCacheStatus({
          available: true,
          caches: cacheDetails,
          totalSize
        });
      } catch (error) {
        console.error('Error checking cache:', error);
      }
    }
  };

  const checkNotificationSupport = () => {
    if ('Notification' in window) {
      setNotificationStatus({
        supported: true,
        permission: Notification.permission
      });
    }
  };

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/service-worker.js');
      console.log('Service Worker registered:', registration);
      addTestResult('Service Worker Registration', 'success', 'Successfully registered service worker');
      checkServiceWorkerSupport();
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      addTestResult('Service Worker Registration', 'error', `Registration failed: ${error.message}`);
    }
  };

  const unregisterServiceWorker = async () => {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        await registration.unregister();
      }
      addTestResult('Service Worker Unregistration', 'success', 'All service workers unregistered');
      checkServiceWorkerSupport();
    } catch (error) {
      addTestResult('Service Worker Unregistration', 'error', `Unregistration failed: ${error.message}`);
    }
  };

  const clearAllCaches = async () => {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
      addTestResult('Cache Clear', 'success', `Cleared ${cacheNames.length} caches`);
      checkCacheAPI();
    } catch (error) {
      addTestResult('Cache Clear', 'error', `Failed to clear caches: ${error.message}`);
    }
  };

  const testOfflineCapability = async () => {
    try {
      // Test if cached resources are available
      const response = await fetch('/', { cache: 'only-if-cached', mode: 'same-origin' });
      if (response.ok) {
        addTestResult('Offline Test', 'success', 'App is available offline');
      } else {
        addTestResult('Offline Test', 'warning', 'App may not work offline');
      }
    } catch (error) {
      addTestResult('Offline Test', 'error', 'Offline capability test failed');
    }
  };

  const requestNotificationPermission = async () => {
    try {
      const permission = await Notification.requestPermission();
      setNotificationStatus(prev => ({ ...prev, permission }));
      addTestResult('Notification Permission', 'success', `Permission: ${permission}`);
    } catch (error) {
      addTestResult('Notification Permission', 'error', `Failed: ${error.message}`);
    }
  };

  const testPushNotification = () => {
    if (Notification.permission === 'granted') {
      new Notification('Test Notification', {
        body: 'This is a test notification from the App Builder',
        icon: '/logo192.png',
        badge: '/favicon.ico'
      });
      addTestResult('Push Notification', 'success', 'Test notification sent');
    } else {
      addTestResult('Push Notification', 'error', 'Notification permission not granted');
    }
  };

  const addTestResult = (test, status, message) => {
    const result = {
      id: Date.now(),
      test,
      status,
      message,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning': return <SyncOutlined style={{ color: '#faad14' }} />;
      default: return <SyncOutlined />;
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>Service Worker Testing Dashboard</Title>
      <Paragraph>
        Test and monitor the service worker functionality, caching, and offline capabilities.
      </Paragraph>

      {/* Connection Status */}
      <Alert
        message={`Connection Status: ${offlineStatus ? 'Online' : 'Offline'}`}
        type={offlineStatus ? 'success' : 'warning'}
        icon={offlineStatus ? <WifiOutlined /> : <CloudOutlined />}
        style={{ marginBottom: '24px' }}
      />

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '24px' }}>
        
        {/* Service Worker Status */}
        <Card title="Service Worker Status" extra={<SyncOutlined onClick={checkServiceWorkerSupport} />}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>Supported: </Text>
              {swStatus.supported ? 
                <Tag color="green"><CheckCircleOutlined /> Yes</Tag> : 
                <Tag color="red"><CloseCircleOutlined /> No</Tag>
              }
            </div>
            <div>
              <Text strong>Registered: </Text>
              {swStatus.registered ? 
                <Tag color="green"><CheckCircleOutlined /> Yes</Tag> : 
                <Tag color="orange"><CloseCircleOutlined /> No</Tag>
              }
            </div>
            <div>
              <Text strong>Active: </Text>
              {swStatus.active ? 
                <Tag color="green"><CheckCircleOutlined /> Yes</Tag> : 
                <Tag color="orange"><CloseCircleOutlined /> No</Tag>
              }
            </div>
            {swStatus.error && (
              <Alert message={swStatus.error} type="error" size="small" />
            )}
            
            <Divider />
            <Space>
              <Button 
                type="primary" 
                onClick={registerServiceWorker}
                disabled={!swStatus.supported || swStatus.registered}
              >
                Register SW
              </Button>
              <Button 
                onClick={unregisterServiceWorker}
                disabled={!swStatus.supported || !swStatus.registered}
              >
                Unregister SW
              </Button>
            </Space>
          </Space>
        </Card>

        {/* Cache Status */}
        <Card title="Cache Status" extra={<ReloadOutlined onClick={checkCacheAPI} />}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>Cache API Available: </Text>
              {cacheStatus.available ? 
                <Tag color="green"><CheckCircleOutlined /> Yes</Tag> : 
                <Tag color="red"><CloseCircleOutlined /> No</Tag>
              }
            </div>
            <div>
              <Text strong>Active Caches: </Text>
              <Tag color="blue">{cacheStatus.caches.length}</Tag>
            </div>
            
            {cacheStatus.caches.length > 0 && (
              <List
                size="small"
                dataSource={cacheStatus.caches}
                renderItem={cache => (
                  <List.Item>
                    <div style={{ width: '100%' }}>
                      <Text strong>{cache.name}</Text>
                      <br />
                      <Text type="secondary">{cache.itemCount} items</Text>
                    </div>
                  </List.Item>
                )}
              />
            )}
            
            <Divider />
            <Space>
              <Button 
                icon={<DeleteOutlined />}
                onClick={clearAllCaches}
                disabled={cacheStatus.caches.length === 0}
              >
                Clear All Caches
              </Button>
              <Button onClick={testOfflineCapability}>
                Test Offline
              </Button>
            </Space>
          </Space>
        </Card>

        {/* Notification Status */}
        <Card title="Notification Status">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>Supported: </Text>
              {notificationStatus.supported ? 
                <Tag color="green"><CheckCircleOutlined /> Yes</Tag> : 
                <Tag color="red"><CloseCircleOutlined /> No</Tag>
              }
            </div>
            <div>
              <Text strong>Permission: </Text>
              <Tag color={notificationStatus.permission === 'granted' ? 'green' : 'orange'}>
                {notificationStatus.permission}
              </Tag>
            </div>
            
            <Divider />
            <Space>
              <Button 
                icon={<BellOutlined />}
                onClick={requestNotificationPermission}
                disabled={!notificationStatus.supported || notificationStatus.permission === 'granted'}
              >
                Request Permission
              </Button>
              <Button 
                onClick={testPushNotification}
                disabled={notificationStatus.permission !== 'granted'}
              >
                Test Notification
              </Button>
            </Space>
          </Space>
        </Card>

        {/* Test Results */}
        <Card title="Test Results" style={{ gridColumn: 'span 2' }}>
          {testResults.length === 0 ? (
            <Text type="secondary">No tests run yet. Click the buttons above to start testing.</Text>
          ) : (
            <List
              dataSource={testResults}
              renderItem={result => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getStatusIcon(result.status)}
                    title={result.test}
                    description={`${result.message} - ${result.timestamp}`}
                  />
                </List.Item>
              )}
            />
          )}
        </Card>
      </div>
    </div>
  );
};

export default ServiceWorkerTest;
