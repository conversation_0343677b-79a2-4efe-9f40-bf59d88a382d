<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Performance Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .status.running {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.completed {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .metric.good { background: #d4edda; border-color: #c3e6cb; }
        .metric.warning { background: #fff3cd; border-color: #ffeaa7; }
        .metric.error { background: #f8d7da; border-color: #f5c6cb; }
        
        .metric h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }
        .metric .value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .results pre {
            background: white;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        
        .browser-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .browser-info h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Browser Performance Test</h1>
        
        <div id="browserInfo" class="browser-info">
            <h3>Browser Information</h3>
            <div id="browserDetails"></div>
        </div>
        
        <div class="controls">
            <button id="startTest" class="btn">Start Performance Test</button>
            <button id="stopTest" class="btn danger" disabled>Stop Test</button>
            <button id="clearResults" class="btn">Clear Results</button>
            
            <div style="margin-top: 10px;">
                <label for="testDuration">Test Duration (seconds):</label>
                <input type="number" id="testDuration" value="30" min="5" max="300" style="width: 80px; margin-left: 5px;">
            </div>
        </div>
        
        <div id="status" class="status"></div>
        <div class="progress" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="metrics" id="metrics">
            <div class="metric" id="fpsMetric">
                <h4>Frame Rate</h4>
                <div class="value" id="fpsValue">-</div>
            </div>
            <div class="metric" id="memoryMetric">
                <h4>Memory Usage</h4>
                <div class="value" id="memoryValue">-</div>
            </div>
            <div class="metric" id="domMetric">
                <h4>DOM Nodes</h4>
                <div class="value" id="domValue">-</div>
            </div>
            <div class="metric" id="tasksMetric">
                <h4>Long Tasks</h4>
                <div class="value" id="tasksValue">-</div>
            </div>
        </div>
        
        <div class="results" id="resultsContainer" style="display: none;">
            <h3>Test Results</h3>
            <pre id="results"></pre>
        </div>
    </div>

    <script>
        class SimplePerformanceTest {
            constructor() {
                this.isRunning = false;
                this.startTime = 0;
                this.testDuration = 30000;
                this.sampleInterval = 1000;
                this.intervalId = null;
                this.progressInterval = null;
                this.longTaskObserver = null;
                
                this.metrics = {
                    frameRates: [],
                    memoryUsage: [],
                    domNodes: [],
                    longTasks: [],
                    timestamps: []
                };
                
                this.lastFrameTime = performance.now();
                this.frameCount = 0;
                this.longTaskCount = 0;
                
                this.detectBrowser();
                this.setupLongTaskObserver();
            }
            
            detectBrowser() {
                const ua = navigator.userAgent;
                this.browserInfo = {
                    userAgent: ua,
                    name: this.getBrowserName(ua),
                    version: this.getBrowserVersion(ua),
                    platform: navigator.platform,
                    language: navigator.language,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine,
                    hardwareConcurrency: navigator.hardwareConcurrency || 'Unknown',
                    memoryInfo: !!(performance && performance.memory),
                    performanceAPI: typeof performance !== 'undefined',
                    webGL: this.detectWebGL(),
                    webWorkers: typeof Worker !== 'undefined',
                    serviceWorkers: 'serviceWorker' in navigator
                };
            }
            
            getBrowserName(ua) {
                if (ua.indexOf('Edge') > -1) return 'Edge';
                if (ua.indexOf('Chrome') > -1) return 'Chrome';
                if (ua.indexOf('Firefox') > -1) return 'Firefox';
                if (ua.indexOf('Safari') > -1) return 'Safari';
                return 'Unknown';
            }
            
            getBrowserVersion(ua) {
                const match = ua.match(/(Chrome|Firefox|Safari|Edge)\/(\d+)/);
                return match ? match[2] : 'Unknown';
            }
            
            detectWebGL() {
                try {
                    const canvas = document.createElement('canvas');
                    return !!(window.WebGLRenderingContext && 
                        (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
                } catch (e) {
                    return false;
                }
            }
            
            setupLongTaskObserver() {
                if (typeof PerformanceObserver !== 'undefined' && 
                    PerformanceObserver.supportedEntryTypes && 
                    PerformanceObserver.supportedEntryTypes.includes('longtask')) {
                    
                    this.longTaskObserver = new PerformanceObserver((entries) => {
                        entries.getEntries().forEach((entry) => {
                            this.longTaskCount++;
                            this.metrics.longTasks.push({
                                duration: entry.duration,
                                startTime: entry.startTime,
                                timestamp: performance.now()
                            });
                        });
                    });
                    
                    this.longTaskObserver.observe({ entryTypes: ['longtask'] });
                }
            }
            
            start(duration = 30000) {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.testDuration = duration;
                this.startTime = performance.now();
                this.frameCount = 0;
                this.longTaskCount = 0;
                
                // Reset metrics
                this.metrics = {
                    frameRates: [],
                    memoryUsage: [],
                    domNodes: [],
                    longTasks: [],
                    timestamps: []
                };
                
                // Start frame rate monitoring
                this.startFrameRateMonitoring();
                
                // Start collecting samples
                this.intervalId = setInterval(() => {
                    this.collectSample();
                }, this.sampleInterval);
                
                console.log('Performance test started');
            }
            
            stop() {
                if (!this.isRunning) return null;
                
                this.isRunning = false;
                
                if (this.intervalId) {
                    clearInterval(this.intervalId);
                    this.intervalId = null;
                }
                
                const endTime = performance.now();
                const actualDuration = endTime - this.startTime;
                
                const results = {
                    testName: `performance-test-${new Date().toISOString()}`,
                    timestamp: new Date().toISOString(),
                    duration: actualDuration,
                    browserInfo: this.browserInfo,
                    metrics: this.metrics,
                    summary: this.generateSummary()
                };
                
                console.log('Performance test completed', results);
                return results;
            }
            
            startFrameRateMonitoring() {
                const measureFrame = () => {
                    if (!this.isRunning) return;
                    
                    const now = performance.now();
                    const frameTime = now - this.lastFrameTime;
                    this.lastFrameTime = now;
                    this.frameCount++;
                    
                    // Calculate FPS (avoid division by zero)
                    const fps = frameTime > 0 ? 1000 / frameTime : 0;
                    
                    requestAnimationFrame(measureFrame);
                };
                
                requestAnimationFrame(measureFrame);
            }
            
            collectSample() {
                const timestamp = performance.now();
                
                // Collect frame rate (average over last second)
                const fps = this.frameCount > 0 ? (this.frameCount * 1000) / this.sampleInterval : 0;
                this.metrics.frameRates.push({ fps, timestamp });
                this.frameCount = 0; // Reset for next interval
                
                // Collect memory usage
                if (performance.memory) {
                    this.metrics.memoryUsage.push({
                        used: performance.memory.usedJSHeapSize,
                        total: performance.memory.totalJSHeapSize,
                        limit: performance.memory.jsHeapSizeLimit,
                        timestamp
                    });
                }
                
                // Collect DOM metrics
                const domNodeCount = document.querySelectorAll('*').length;
                this.metrics.domNodes.push({ count: domNodeCount, timestamp });
                
                this.metrics.timestamps.push(timestamp);
            }
            
            generateSummary() {
                const summary = {};
                
                // Frame rate summary
                if (this.metrics.frameRates.length > 0) {
                    const fps = this.metrics.frameRates.map(f => f.fps);
                    summary.frameRate = {
                        avg: fps.reduce((a, b) => a + b, 0) / fps.length,
                        min: Math.min(...fps),
                        max: Math.max(...fps)
                    };
                }
                
                // Memory summary
                if (this.metrics.memoryUsage.length > 0) {
                    const memory = this.metrics.memoryUsage;
                    const latest = memory[memory.length - 1];
                    summary.memory = {
                        final: latest.used,
                        peak: Math.max(...memory.map(m => m.used)),
                        finalMB: (latest.used / (1024 * 1024)).toFixed(1)
                    };
                }
                
                // DOM summary
                if (this.metrics.domNodes.length > 0) {
                    const nodes = this.metrics.domNodes.map(d => d.count);
                    summary.dom = {
                        final: nodes[nodes.length - 1],
                        max: Math.max(...nodes)
                    };
                }
                
                // Long tasks summary
                summary.longTasks = {
                    count: this.longTaskCount,
                    total: this.metrics.longTasks.length
                };
                
                return summary;
            }
        }
        
        // Initialize the test interface
        let performanceTest = null;
        let updateInterval = null;
        
        document.addEventListener('DOMContentLoaded', () => {
            displayBrowserInfo();
            setupEventListeners();
        });
        
        function displayBrowserInfo() {
            const test = new SimplePerformanceTest();
            const info = test.browserInfo;
            
            document.getElementById('browserDetails').innerHTML = `
                <p><strong>Browser:</strong> ${info.name} ${info.version}</p>
                <p><strong>Platform:</strong> ${info.platform}</p>
                <p><strong>CPU Cores:</strong> ${info.hardwareConcurrency}</p>
                <p><strong>Memory API:</strong> ${info.memoryInfo ? 'Available' : 'Not Available'}</p>
                <p><strong>WebGL:</strong> ${info.webGL ? 'Supported' : 'Not Supported'}</p>
                <p><strong>Web Workers:</strong> ${info.webWorkers ? 'Supported' : 'Not Supported'}</p>
            `;
        }
        
        function setupEventListeners() {
            document.getElementById('startTest').addEventListener('click', startTest);
            document.getElementById('stopTest').addEventListener('click', stopTest);
            document.getElementById('clearResults').addEventListener('click', clearResults);
        }
        
        function startTest() {
            const duration = parseInt(document.getElementById('testDuration').value) * 1000;
            
            if (duration < 5000 || duration > 300000) {
                alert('Test duration must be between 5 and 300 seconds');
                return;
            }
            
            performanceTest = new SimplePerformanceTest();
            performanceTest.start(duration);
            
            // Update UI
            document.getElementById('startTest').disabled = true;
            document.getElementById('stopTest').disabled = false;
            
            const statusEl = document.getElementById('status');
            statusEl.style.display = 'block';
            statusEl.className = 'status running';
            statusEl.textContent = `Running performance test for ${duration/1000} seconds...`;
            
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            
            // Start progress tracking
            const startTime = Date.now();
            const progressInterval = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min((elapsed / duration) * 100, 100);
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                }
            }, 100);
            
            // Start metrics updates
            updateInterval = setInterval(updateMetrics, 1000);
            
            // Auto-stop after duration
            setTimeout(() => {
                stopTest();
            }, duration);
        }
        
        function stopTest() {
            if (!performanceTest) return;
            
            const results = performanceTest.stop();
            
            // Update UI
            document.getElementById('startTest').disabled = false;
            document.getElementById('stopTest').disabled = true;
            
            const statusEl = document.getElementById('status');
            statusEl.className = 'status completed';
            statusEl.textContent = 'Performance test completed!';
            
            document.getElementById('progressBar').style.width = '100%';
            
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
            
            // Display results
            if (results) {
                displayResults(results);
            }
            
            performanceTest = null;
        }
        
        function updateMetrics() {
            if (!performanceTest || !performanceTest.isRunning) return;
            
            const metrics = performanceTest.metrics;
            
            // Update FPS
            const fpsEl = document.getElementById('fpsValue');
            const fpsMetricEl = document.getElementById('fpsMetric');
            if (metrics.frameRates.length > 0) {
                const latestFPS = metrics.frameRates[metrics.frameRates.length - 1].fps;
                fpsEl.textContent = latestFPS.toFixed(1);
                fpsMetricEl.className = 'metric ' + (latestFPS < 30 ? 'error' : latestFPS < 50 ? 'warning' : 'good');
            }
            
            // Update Memory
            const memoryEl = document.getElementById('memoryValue');
            const memoryMetricEl = document.getElementById('memoryMetric');
            if (metrics.memoryUsage.length > 0) {
                const latestMemory = metrics.memoryUsage[metrics.memoryUsage.length - 1];
                const memoryMB = (latestMemory.used / (1024 * 1024)).toFixed(1);
                memoryEl.textContent = `${memoryMB} MB`;
                memoryMetricEl.className = 'metric ' + (memoryMB > 100 ? 'warning' : 'good');
            }
            
            // Update DOM Nodes
            const domEl = document.getElementById('domValue');
            const domMetricEl = document.getElementById('domMetric');
            if (metrics.domNodes.length > 0) {
                const latestDOM = metrics.domNodes[metrics.domNodes.length - 1].count;
                domEl.textContent = latestDOM;
                domMetricEl.className = 'metric ' + (latestDOM > 1000 ? 'warning' : 'good');
            }
            
            // Update Long Tasks
            const tasksEl = document.getElementById('tasksValue');
            const tasksMetricEl = document.getElementById('tasksMetric');
            tasksEl.textContent = performanceTest.longTaskCount;
            tasksMetricEl.className = 'metric ' + (performanceTest.longTaskCount > 5 ? 'warning' : 'good');
        }
        
        function displayResults(results) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsEl = document.getElementById('results');
            
            resultsContainer.style.display = 'block';
            resultsEl.textContent = JSON.stringify(results, null, 2);
        }
        
        function clearResults() {
            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('status').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'none';
            
            // Reset metrics display
            document.getElementById('fpsValue').textContent = '-';
            document.getElementById('memoryValue').textContent = '-';
            document.getElementById('domValue').textContent = '-';
            document.getElementById('tasksValue').textContent = '-';
            
            // Reset metric colors
            document.querySelectorAll('.metric').forEach(el => {
                el.className = 'metric';
            });
        }
    </script>
</body>
</html>
