<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Theme Manager - App Builder</title>
  <link rel="stylesheet" href="/static/css/theme-manager.css">
  <style>
    body {
      font-family: var(--font-family);
      background-color: var(--background-color);
      color: var(--text-color);
      margin: 0;
      padding: 0;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      border-bottom: 1px solid #d9d9d9;
      padding-bottom: 20px;
    }

    h1 {
      color: var(--primary-color);
      margin: 0;
    }

    .content {
      display: grid;
      grid-template-columns: 1fr;
      gap: 30px;
    }

    @media (min-width: 768px) {
      .content {
        grid-template-columns: 1fr 1fr;
      }
    }

    .preview-section {
      border: 1px solid #d9d9d9;
      border-radius: var(--border-radius);
      padding: 20px;
      background-color: var(--background-color);
    }

    .preview-card {
      border: 1px solid #d9d9d9;
      border-radius: var(--border-radius);
      padding: 15px;
      margin-bottom: 15px;
    }

    .preview-form {
      display: grid;
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .preview-input {
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: var(--border-radius);
      font-family: var(--font-family);
    }

    .preview-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      border-top: 1px solid #d9d9d9;
      color: var(--text-color);
    }
  </style>
</head>

<body>
  <div class="container">
    <header>
      <h1>App Builder Theme Manager</h1>
      <div id="theme-selector"></div>
    </header>

    <div class="content">
      <div>
        <div id="theme-manager-container"></div>
      </div>

      <div class="preview-section">
        <h2 class="preview-title">Theme Preview</h2>

        <div class="preview-card">
          <h3 style="color: var(--primary-color);">UI Components</h3>
          <div style="margin-bottom: 15px;">
            <button class="preview-button">Primary Button</button>
            <button class="preview-button secondary">Secondary Button</button>
          </div>
          <p class="preview-text">This is a preview of how the theme will look. The text color, background color, and
            other properties are applied based on the selected theme.</p>
        </div>

        <div class="preview-card">
          <h3 style="color: var(--primary-color);">Form Elements</h3>
          <div class="preview-form">
            <div>
              <label for="preview-name">Name</label>
              <input type="text" id="preview-name" class="preview-input" placeholder="Enter your name">
            </div>
            <div>
              <label for="preview-email">Email</label>
              <input type="email" id="preview-email" class="preview-input" placeholder="Enter your email">
            </div>
            <div class="preview-checkbox">
              <input type="checkbox" id="preview-checkbox">
              <label for="preview-checkbox">Subscribe to newsletter</label>
            </div>
          </div>
        </div>

        <div class="preview-card">
          <h3 style="color: var(--primary-color);">Typography</h3>
          <h1 style="margin: 10px 0;">Heading 1</h1>
          <h2 style="margin: 10px 0;">Heading 2</h2>
          <h3 style="margin: 10px 0;">Heading 3</h3>
          <p><strong>Bold text</strong> and <em>italic text</em> and <a href="#"
              style="color: var(--primary-color);">link text</a>.</p>
        </div>
      </div>
    </div>

    <footer>
      <p>App Builder Theme Manager &copy; 2023</p>
    </footer>
  </div>

  <script src="/static/js/ThemeManagerComponent.js"></script>
  <script>
    // Initialize the theme manager
    document.addEventListener('DOMContentLoaded', function () {
      const themeManager = new ThemeManager();
      themeManager.init('theme-manager-container');

      // Create a simple theme selector for the header
      const themeSelector = document.getElementById('theme-selector');
      const select = document.createElement('select');
      select.className = 'theme-select';

      themeManager.themes.forEach(theme => {
        const option = document.createElement('option');
        option.value = theme.id;
        option.textContent = theme.name;
        option.selected = theme.id === themeManager.activeTheme;
        select.appendChild(option);
      });

      select.addEventListener('change', (e) => {
        themeManager.setActiveTheme(e.target.value);
      });

      themeSelector.appendChild(select);

      // Listen for theme changes
      window.addEventListener('themechange', function (event) {
        console.log('Theme changed:', event.detail);

        // Update the header selector
        const options = select.querySelectorAll('option');
        options.forEach(option => {
          option.selected = option.value === event.detail.id;
        });
      });
    });
  </script>
</body>

</html>