/**
 * Visual Onboarding Tutorial Helpers
 * 
 * This file contains validation functions, custom components, and helper utilities
 * for the visual onboarding tutorial system.
 */

import React from 'react';
import { Button, Typography, Space, Card, Confetti } from 'antd';
import { 
  RocketOutlined, 
  TrophyOutlined, 
  StarOutlined,
  CheckCircleOutlined,
  GiftOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;

// Styled Components for Custom Modals
const WelcomeContainer = styled.div`
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin: -24px -24px 24px -24px;
`;

const SuccessContainer = styled.div`
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  color: white;
  border-radius: 12px;
  margin: -24px -24px 24px -24px;
`;

const CompletionContainer = styled.div`
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border-radius: 12px;
  margin: -24px -24px 24px -24px;
`;

const FeatureList = styled.ul`
  text-align: left;
  margin: 16px 0;
  padding-left: 20px;
  
  li {
    margin: 8px 0;
    font-size: 14px;
  }
`;

// Custom Modal Components
export const WelcomeModal = ({ onNext }) => (
  <div>
    <WelcomeContainer>
      <RocketOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
      <Title level={2} style={{ color: 'white', margin: '0 0 16px 0' }}>
        🎉 Welcome to App Builder!
      </Title>
      <Paragraph style={{ color: 'white', fontSize: '16px', margin: 0 }}>
        Get ready for an amazing journey into visual app development!
      </Paragraph>
    </WelcomeContainer>
    
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <div>
        <Title level={4}>What you'll learn in this tutorial:</Title>
        <FeatureList>
          <li>🎨 How to use the Component Palette</li>
          <li>👁️ Working with the Preview Area</li>
          <li>⚙️ Customizing components with the Property Editor</li>
          <li>🖱️ Drag and drop functionality</li>
          <li>👀 Previewing your applications</li>
        </FeatureList>
      </div>
      
      <div style={{ textAlign: 'center' }}>
        <Text type="secondary">
          This tutorial takes about 8 minutes and will give you everything you need to start building!
        </Text>
      </div>
    </Space>
  </div>
);

export const SuccessModal = ({ onNext }) => (
  <div>
    <SuccessContainer>
      <CheckCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
      <Title level={3} style={{ color: 'white', margin: '0 0 16px 0' }}>
        🎉 Great Job!
      </Title>
      <Text style={{ color: 'white', fontSize: '16px' }}>
        You've successfully added your first component!
      </Text>
    </SuccessContainer>
    
    <Space direction="vertical" size="medium" style={{ width: '100%' }}>
      <div>
        <Title level={4}>What just happened?</Title>
        <FeatureList>
          <li>✅ The button component was added to your app</li>
          <li>✅ It appeared in the Preview Area</li>
          <li>✅ It was automatically selected (blue border)</li>
          <li>✅ Its properties appeared in the Property Editor</li>
        </FeatureList>
      </div>
      
      <div style={{ textAlign: 'center' }}>
        <Text type="secondary">
          This is the basic workflow for adding any component to your app!
        </Text>
      </div>
    </Space>
  </div>
);

export const CompletionModal = ({ onComplete }) => (
  <div>
    <CompletionContainer>
      <TrophyOutlined style={{ fontSize: '48px', marginBottom: '16px', color: '#faad14' }} />
      <Title level={2} style={{ margin: '0 0 16px 0', color: '#8b4513' }}>
        🎊 Congratulations!
      </Title>
      <Text style={{ fontSize: '16px', color: '#8b4513' }}>
        You've mastered the App Builder basics!
      </Text>
    </CompletionContainer>
    
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <div>
        <Title level={4}>🏆 Skills Unlocked:</Title>
        <FeatureList>
          <li>🎨 Component Palette navigation</li>
          <li>🖱️ Adding components with click and drag</li>
          <li>🎯 Selecting and editing components</li>
          <li>⚙️ Using the Property Editor</li>
          <li>👀 Previewing applications</li>
          <li>🔧 Switching between edit and preview modes</li>
        </FeatureList>
      </div>
      
      <Card style={{ background: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <Space>
          <GiftOutlined style={{ color: '#52c41a', fontSize: '20px' }} />
          <div>
            <Text strong style={{ color: '#389e0d' }}>Next Steps:</Text>
            <br />
            <Text style={{ color: '#389e0d' }}>
              Try exploring more components, experiment with layouts, and build your first real application!
            </Text>
          </div>
        </Space>
      </Card>
      
      <div style={{ textAlign: 'center' }}>
        <Text type="secondary">
          You can always restart this tutorial from the help menu if you need a refresher.
        </Text>
      </div>
    </Space>
  </div>
);

// Validation Functions
export const tutorialValidations = {
  validateComponentAdded: (context) => {
    // Check if a component was added to the components array
    const { components } = context;
    return components && components.length > 0;
  },

  validateComponentSelected: (context) => {
    // Check if a component is currently selected
    const { selectedComponent } = context;
    return selectedComponent !== null && selectedComponent !== undefined;
  },

  validateTextChanged: (context) => {
    // Check if the button text was changed from default
    const { selectedComponent } = context;
    if (!selectedComponent || !selectedComponent.props) return false;
    
    const text = selectedComponent.props.children || selectedComponent.props.text;
    return text && text !== 'Button' && text.includes('My First Button');
  },

  validateButtonTypeChanged: (context) => {
    // Check if button type was changed to primary
    const { selectedComponent } = context;
    if (!selectedComponent || !selectedComponent.props) return false;
    
    return selectedComponent.props.type === 'primary';
  },

  validateSecondComponentAdded: (context) => {
    // Check if at least 2 components exist
    const { components } = context;
    return components && components.length >= 2;
  },

  validatePreviewMode: (context) => {
    // Check if preview mode is active
    const { previewMode } = context;
    return previewMode === true;
  },

  validateEditMode: (context) => {
    // Check if edit mode is active (preview mode is false)
    const { previewMode } = context;
    return previewMode === false;
  }
};

// Tutorial Event Handlers
export const tutorialEventHandlers = {
  highlightComponentButton: () => {
    // Add special highlighting to the button component
    const buttonElement = document.querySelector('[data-tutorial-target="component-button"]');
    if (buttonElement) {
      buttonElement.style.animation = 'pulse 1s infinite';
      buttonElement.style.boxShadow = '0 0 20px rgba(24, 144, 255, 0.6)';
    }
  },

  clearHighlights: () => {
    // Remove special highlighting
    const elements = document.querySelectorAll('[data-tutorial-target]');
    elements.forEach(el => {
      el.style.animation = '';
      el.style.boxShadow = '';
    });
  },

  focusTextInput: () => {
    // Focus the text input in property editor
    setTimeout(() => {
      const textInput = document.querySelector('[data-tutorial-target="property-text-input"]');
      if (textInput) {
        textInput.focus();
        textInput.select();
      }
    }, 500);
  },

  celebrateCompletion: () => {
    // Add celebration effects
    if (window.confetti) {
      window.confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
    }
    
    // Play success sound if available
    if (window.tutorialSounds && window.tutorialSounds.success) {
      window.tutorialSounds.success.play();
    }
  }
};

// Helper function to get tutorial context from app state
export const getTutorialContext = (appState) => {
  return {
    components: appState.components || [],
    selectedComponent: appState.selectedComponent || null,
    previewMode: appState.previewMode || false,
    // Add other relevant state properties
  };
};

// Helper function to check if user is new (for auto-starting tutorial)
export const isNewUser = () => {
  const hasCompletedOnboarding = localStorage.getItem('app_builder_onboarding_completed');
  const hasCreatedComponents = localStorage.getItem('app_builder_components_created');
  
  return !hasCompletedOnboarding && !hasCreatedComponents;
};

// Helper function to mark onboarding as completed
export const markOnboardingCompleted = () => {
  localStorage.setItem('app_builder_onboarding_completed', 'true');
  localStorage.setItem('app_builder_onboarding_completed_at', new Date().toISOString());
};

export default {
  WelcomeModal,
  SuccessModal,
  CompletionModal,
  tutorialValidations,
  tutorialEventHandlers,
  getTutorialContext,
  isNewUser,
  markOnboardingCompleted
};
