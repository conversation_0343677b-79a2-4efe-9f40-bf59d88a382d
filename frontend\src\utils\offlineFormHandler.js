/**
 * Offline Form Handler
 * 
 * This utility provides functions for handling form submissions when offline.
 * It stores form data locally and syncs it when the user comes back online.
 */

/**
 * Save a form submission for later when offline
 * 
 * @param {Object} formData - The form data to save
 * @param {string} url - The URL to submit the form to
 * @param {string} method - The HTTP method to use (default: 'POST')
 * @param {Object} headers - The headers to include in the request
 * @returns {Promise<Object>} - A promise that resolves to the result of the operation
 */
export async function saveFormForLater(formData, url, method = 'POST', headers = {}) {
  try {
    // Check if we're online
    if (navigator.onLine) {
      // If we're online, try to submit the form directly
      try {
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
        }
        
        return {
          success: true,
          online: true,
          message: 'Form submitted successfully'
        };
      } catch (error) {
        console.error('Error submitting form:', error);
        // If the submission fails, save it for later
        return saveOfflineForm(formData, url, method, headers);
      }
    } else {
      // If we're offline, save the form for later
      return saveOfflineForm(formData, url, method, headers);
    }
  } catch (error) {
    console.error('Error in saveFormForLater:', error);
    return {
      success: false,
      online: navigator.onLine,
      message: 'Failed to save form data',
      error: error.message
    };
  }
}

/**
 * Save a form submission to local storage
 * 
 * @param {Object} formData - The form data to save
 * @param {string} url - The URL to submit the form to
 * @param {string} method - The HTTP method to use
 * @param {Object} headers - The headers to include in the request
 * @returns {Promise<Object>} - A promise that resolves to the result of the operation
 */
async function saveOfflineForm(formData, url, method, headers) {
  try {
    // Generate a unique ID for this submission
    const submissionId = `form_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create the submission object
    const submission = {
      id: submissionId,
      data: formData,
      url,
      method,
      headers,
      timestamp: new Date().toISOString()
    };
    
    // Get existing offline data
    let offlineData = await getOfflineData();
    
    // Add the new submission
    offlineData.formSubmissions.push(submission);
    
    // Save the updated offline data
    await saveOfflineData(offlineData);
    
    // Register for sync if service worker is available
    if ('serviceWorker' in navigator && 'SyncManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('form-submission');
        console.log('Sync registered for form submission');
      } catch (syncError) {
        console.error('Failed to register sync:', syncError);
      }
    }
    
    return {
      success: true,
      online: false,
      message: 'Form saved for later submission',
      submissionId
    };
  } catch (error) {
    console.error('Error saving offline form:', error);
    return {
      success: false,
      online: false,
      message: 'Failed to save form data offline',
      error: error.message
    };
  }
}

/**
 * Get offline data from cache
 * 
 * @returns {Promise<Object>} - A promise that resolves to the offline data
 */
async function getOfflineData() {
  try {
    // Try to get data from IndexedDB first
    if ('indexedDB' in window) {
      try {
        const db = await openDatabase();
        const transaction = db.transaction(['offlineData'], 'readonly');
        const store = transaction.objectStore('offlineData');
        const data = await new Promise((resolve, reject) => {
          const request = store.get('formSubmissions');
          request.onsuccess = () => resolve(request.result || { formSubmissions: [] });
          request.onerror = () => reject(request.error);
        });
        
        return data;
      } catch (dbError) {
        console.warn('Failed to get data from IndexedDB, falling back to localStorage:', dbError);
      }
    }
    
    // Fall back to localStorage
    const storedData = localStorage.getItem('offlineFormData');
    if (storedData) {
      return JSON.parse(storedData);
    }
    
    // Return empty data if nothing is found
    return { formSubmissions: [] };
  } catch (error) {
    console.error('Error getting offline data:', error);
    return { formSubmissions: [] };
  }
}

/**
 * Save offline data to cache
 * 
 * @param {Object} data - The data to save
 * @returns {Promise<boolean>} - A promise that resolves to true if successful
 */
async function saveOfflineData(data) {
  try {
    // Try to save to IndexedDB first
    if ('indexedDB' in window) {
      try {
        const db = await openDatabase();
        const transaction = db.transaction(['offlineData'], 'readwrite');
        const store = transaction.objectStore('offlineData');
        await new Promise((resolve, reject) => {
          const request = store.put(data, 'formSubmissions');
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
        
        return true;
      } catch (dbError) {
        console.warn('Failed to save data to IndexedDB, falling back to localStorage:', dbError);
      }
    }
    
    // Fall back to localStorage
    localStorage.setItem('offlineFormData', JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Error saving offline data:', error);
    return false;
  }
}

/**
 * Open the IndexedDB database
 * 
 * @returns {Promise<IDBDatabase>} - A promise that resolves to the database
 */
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('AppBuilderOfflineDB', 1);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('offlineData')) {
        db.createObjectStore('offlineData');
      }
    };
    
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

/**
 * Get pending form submissions
 * 
 * @returns {Promise<Array>} - A promise that resolves to an array of pending submissions
 */
export async function getPendingFormSubmissions() {
  const offlineData = await getOfflineData();
  return offlineData.formSubmissions || [];
}

/**
 * Manually trigger sync of offline forms
 * 
 * @returns {Promise<boolean>} - A promise that resolves to true if sync was triggered
 */
export async function syncOfflineForms() {
  if ('serviceWorker' in navigator && 'SyncManager' in window) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.sync.register('form-submission');
      return true;
    } catch (error) {
      console.error('Failed to trigger sync:', error);
      return false;
    }
  }
  
  return false;
}

// Listen for sync completion messages from the service worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SYNC_COMPLETED') {
      console.log('Sync completed:', event.data.results);
      // Dispatch an event that components can listen for
      window.dispatchEvent(new CustomEvent('offlineSync', {
        detail: event.data
      }));
    }
  });
}

export default {
  saveFormForLater,
  getPendingFormSubmissions,
  syncOfflineForms
};
