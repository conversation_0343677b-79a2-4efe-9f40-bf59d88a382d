/**
 * AI Design Service
 * Enhanced AI service for layout suggestions and component combinations
 */

// Import configuration
import { aiConfig, isAIEnabled, shouldShowWarnings, errorConfig } from '../config/aiConfig';

// Import WebSocket service
import aiWebSocketService from './aiWebSocketService';

class AIDesignService {
  constructor() {
    this.baseUrl = aiConfig.baseUrl;
    this.cache = new Map();
    this.cacheTimeout = aiConfig.cacheTimeout;
    this.useWebSocket = aiConfig.websocketEnabled;
    this.wsService = aiWebSocketService;
    this.enabled = isAIEnabled();

    // Setup WebSocket event listeners
    this.setupWebSocketListeners();
  }

  /**
   * Setup WebSocket event listeners
   */
  setupWebSocketListeners() {
    this.wsService.addEventListener('layoutSuggestions', (suggestions) => {
      // Cache WebSocket results
      const cacheKey = 'ws_layout_suggestions';
      this.cache.set(cacheKey, {
        data: { suggestions, status: 'success' },
        timestamp: Date.now()
      });
    });

    this.wsService.addEventListener('componentCombinations', (suggestions) => {
      // Cache WebSocket results
      const cacheKey = 'ws_component_combinations';
      this.cache.set(cacheKey, {
        data: { suggestions, status: 'success' },
        timestamp: Date.now()
      });
    });

    this.wsService.addEventListener('error', (error) => {
      if (shouldShowWarnings()) {
        console.warn('AI WebSocket error, falling back to HTTP:', error);
      }
    });
  }

  /**
   * Generate layout suggestions based on app structure
   * @param {Array} components - Current components in the app
   * @param {Array} layouts - Existing layouts (optional)
   * @param {Object} context - Additional context (optional)
   * @returns {Promise<Object>} Layout suggestions response
   */
  async generateLayoutSuggestions(components, layouts = [], context = {}) {
    // Return fallback immediately if AI is disabled
    if (!this.enabled) {
      return this._getFallbackLayoutSuggestions(components);
    }

    const cacheKey = `layout_${JSON.stringify({ components, layouts, context })}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    // Try WebSocket first if available and connected
    if (this.useWebSocket && this.wsService.getStatus().connected) {
      try {
        return await this._getLayoutSuggestionsViaWebSocket(components, layouts, context, cacheKey);
      } catch (error) {
        if (shouldShowWarnings()) {
          console.warn('WebSocket request failed, falling back to HTTP:', error);
        }
      }
    }

    // Fallback to HTTP API
    try {
      const response = await fetch(`${this.baseUrl}/layout-suggestions/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this._getAuthHeader(),
        },
        body: JSON.stringify({
          components,
          layouts,
          context
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Cache the result
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      if (shouldShowWarnings()) {
        console.warn('AI service unavailable for layout suggestions:', error.message);
      }

      // Return fallback suggestions
      return this._getFallbackLayoutSuggestions(components);
    }
  }

  /**
   * Get layout suggestions via WebSocket
   * @private
   */
  async _getLayoutSuggestionsViaWebSocket(components, layouts, context, cacheKey) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.wsService.removeEventListener('layoutSuggestions', responseHandler);
        reject(new Error('WebSocket request timeout'));
      }, 10000); // 10 second timeout

      const responseHandler = (suggestions) => {
        clearTimeout(timeout);
        this.wsService.removeEventListener('layoutSuggestions', responseHandler);

        const data = { suggestions, status: 'success' };

        // Cache the result
        this.cache.set(cacheKey, {
          data,
          timestamp: Date.now()
        });

        resolve(data);
      };

      this.wsService.addEventListener('layoutSuggestions', responseHandler);
      this.wsService.requestLayoutSuggestions(components, layouts, context);
    });
  }

  /**
   * Generate component combination suggestions
   * @param {Array} components - Current components in the app
   * @param {Object} selectedComponent - Currently selected component (optional)
   * @param {Object} context - Additional context (optional)
   * @returns {Promise<Object>} Component combination suggestions response
   */
  async generateComponentCombinations(components, selectedComponent = null, context = {}) {
    // Return fallback immediately if AI is disabled
    if (!this.enabled) {
      return this._getFallbackCombinationSuggestions(components, selectedComponent);
    }

    const cacheKey = `combinations_${JSON.stringify({ components, selectedComponent, context })}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    // Try WebSocket first if available and connected
    if (this.useWebSocket && this.wsService.getStatus().connected) {
      try {
        return await this._getComponentCombinationsViaWebSocket(components, selectedComponent, context, cacheKey);
      } catch (error) {
        if (shouldShowWarnings()) {
          console.warn('WebSocket request failed, falling back to HTTP:', error);
        }
      }
    }

    // Fallback to HTTP API
    try {
      const response = await fetch(`${this.baseUrl}/component-combinations/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this._getAuthHeader(),
        },
        body: JSON.stringify({
          components,
          selected_component: selectedComponent,
          context
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Cache the result
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      if (shouldShowWarnings()) {
        console.warn('AI service unavailable for component combinations:', error.message);
      }

      // Return fallback suggestions
      return this._getFallbackCombinationSuggestions(components, selectedComponent);
    }
  }

  /**
   * Get component combinations via WebSocket
   * @private
   */
  async _getComponentCombinationsViaWebSocket(components, selectedComponent, context, cacheKey) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.wsService.removeEventListener('componentCombinations', responseHandler);
        reject(new Error('WebSocket request timeout'));
      }, 10000); // 10 second timeout

      const responseHandler = (suggestions) => {
        clearTimeout(timeout);
        this.wsService.removeEventListener('componentCombinations', responseHandler);

        const data = { suggestions, status: 'success' };

        // Cache the result
        this.cache.set(cacheKey, {
          data,
          timestamp: Date.now()
        });

        resolve(data);
      };

      this.wsService.addEventListener('componentCombinations', responseHandler);
      this.wsService.requestComponentCombinations(components, selectedComponent, context);
    });
  }

  /**
   * Analyze app structure
   * @param {Array} components - Current components in the app
   * @param {Array} layouts - Existing layouts (optional)
   * @returns {Promise<Object>} App structure analysis response
   */
  async analyzeAppStructure(components, layouts = []) {
    // Return fallback immediately if AI is disabled
    if (!this.enabled) {
      return this._getBasicAnalysis(components);
    }

    const cacheKey = `analysis_${JSON.stringify({ components, layouts })}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const response = await fetch(`${this.baseUrl}/analyze-structure/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this._getAuthHeader(),
        },
        body: JSON.stringify({
          components,
          layouts
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Cache the result
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.warn('AI service unavailable, using basic analysis:', error.message);

      // Return basic analysis as fallback
      const fallbackData = this._getBasicAnalysis(components);

      // Cache the fallback result for a shorter time
      this.cache.set(cacheKey, {
        data: fallbackData,
        timestamp: Date.now()
      });

      return fallbackData;
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get authentication header
   * @private
   */
  _getAuthHeader() {
    const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    return token ? `Bearer ${token}` : '';
  }

  /**
   * Get fallback layout suggestions when API is unavailable
   * @private
   */
  _getFallbackLayoutSuggestions(components) {
    const componentCount = components.length;
    const suggestions = [];

    if (componentCount <= 3) {
      suggestions.push({
        id: 'simple_flex',
        name: 'Simple Flexbox Layout',
        description: 'Basic vertical layout for simple apps',
        score: 80,
        explanation: 'Perfect for apps with few components',
        structure: { display: 'flex', flexDirection: 'column' }
      });
    }

    if (componentCount > 3) {
      suggestions.push({
        id: 'grid_layout',
        name: 'Grid Layout',
        description: 'Organized grid for multiple components',
        score: 85,
        explanation: 'Grid layout works well for organizing many components',
        structure: { display: 'grid', gap: '16px' }
      });
    }

    suggestions.push({
      id: 'header_footer',
      name: 'Header-Footer Layout',
      description: 'Classic layout with header and footer',
      score: 75,
      explanation: 'Traditional layout suitable for most applications',
      structure: { header: true, footer: true }
    });

    return {
      suggestions,
      status: 'fallback',
      component_count: componentCount
    };
  }

  /**
   * Get fallback component combination suggestions
   * @private
   */
  _getFallbackCombinationSuggestions(components, selectedComponent) {
    const suggestions = [];
    const componentTypes = components.map(c => c.type);

    if (selectedComponent) {
      const type = selectedComponent.type;

      if (type === 'button' && !componentTypes.includes('form')) {
        suggestions.push({
          id: 'button_form',
          name: 'Button + Form',
          description: 'Add a form to go with your button',
          score: 70,
          components: ['button', 'form'],
          missing_components: ['form']
        });
      }

      if (type === 'text' && !componentTypes.includes('image')) {
        suggestions.push({
          id: 'text_image',
          name: 'Text + Image',
          description: 'Add an image to complement your text',
          score: 65,
          components: ['text', 'image'],
          missing_components: ['image']
        });
      }
    }

    if (!componentTypes.includes('header')) {
      suggestions.push({
        id: 'add_header',
        name: 'Add Header',
        description: 'Every app needs a header for navigation',
        score: 80,
        components: ['header'],
        missing_components: ['header']
      });
    }

    return {
      suggestions,
      status: 'fallback',
      component_count: components.length
    };
  }

  /**
   * Get basic app structure analysis
   * @private
   */
  _getBasicAnalysis(components) {
    const componentTypes = {};
    components.forEach(comp => {
      const type = comp.type || 'unknown';
      componentTypes[type] = (componentTypes[type] || 0) + 1;
    });

    return {
      analysis: {
        component_count: components.length,
        component_types: componentTypes,
        has_navigation: Object.keys(componentTypes).some(type =>
          ['header', 'nav', 'menu'].includes(type)
        ),
        has_forms: Object.keys(componentTypes).some(type =>
          ['form', 'input', 'button'].includes(type)
        ),
        has_media: Object.keys(componentTypes).some(type =>
          ['image', 'video', 'gallery'].includes(type)
        ),
        complexity_score: Object.keys(componentTypes).length * 2 + components.length,
        app_type: 'general'
      },
      status: 'basic'
    };
  }
}

// Create and export singleton instance
const aiDesignService = new AIDesignService();
export default aiDesignService;
