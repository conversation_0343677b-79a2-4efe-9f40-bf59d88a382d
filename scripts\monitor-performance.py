#!/usr/bin/env python3
"""
Real-time Performance Monitor for App Builder 201
"""

import time
import psutil
import requests
import json
from datetime import datetime

def monitor_performance(duration=60, interval=5):
    """Monitor performance for specified duration"""
    print(f"Monitoring performance for {duration} seconds...")

    start_time = time.time()
    metrics = []

    while time.time() - start_time < duration:
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()

        # Application health check
        try:
            response = requests.get("http://localhost:8000/health/", timeout=5)
            app_status = "healthy" if response.status_code == 200 else "unhealthy"
            response_time = response.elapsed.total_seconds()
        except:
            app_status = "unreachable"
            response_time = 0

        metric = {
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": memory.available / (1024**3),
            "app_status": app_status,
            "response_time": response_time
        }

        metrics.append(metric)

        print(f"[{metric['timestamp'][:19]}] CPU: {cpu_percent:5.1f}% | Memory: {memory.percent:5.1f}% | App: {app_status} ({response_time:.3f}s)")

        time.sleep(interval)

    # Save metrics to file
    with open("performance_metrics.json", "w") as f:
        json.dump(metrics, f, indent=2)

    print(f"Performance metrics saved to performance_metrics.json")

if __name__ == "__main__":
    monitor_performance()
