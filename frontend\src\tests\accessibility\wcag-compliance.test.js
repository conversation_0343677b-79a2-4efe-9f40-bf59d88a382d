/**
 * WCAG Compliance Tests
 * Comprehensive accessibility testing for WCAG 2.1 AA compliance
 */

import { test, expect } from '@playwright/test';
import { injectAxe, checkA11y, getViolations } from 'axe-playwright';

const APP_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';

// WCAG 2.1 AA compliance rules
const WCAG_AA_RULES = {
  // Level A rules
  'color-contrast': true,
  'image-alt': true,
  'label': true,
  'link-name': true,
  'button-name': true,
  'form-field-multiple-labels': true,
  'heading-order': true,
  'html-has-lang': true,
  'html-lang-valid': true,
  'input-image-alt': true,
  'keyboard': true,
  'page-has-heading-one': true,
  'region': true,
  'skip-link': true,
  'tabindex': true,
  
  // Level AA rules
  'color-contrast-enhanced': false, // This is AAA
  'focus-order-semantics': true,
  'landmark-banner-is-top-level': true,
  'landmark-contentinfo-is-top-level': true,
  'landmark-main-is-top-level': true,
  'landmark-no-duplicate-banner': true,
  'landmark-no-duplicate-contentinfo': true,
  'landmark-one-main': true,
  'meta-viewport': true,
  'scrollable-region-focusable': true,
};

// Color contrast requirements
const COLOR_CONTRAST_REQUIREMENTS = {
  normal: 4.5,    // Normal text AA
  large: 3.0,     // Large text AA
  enhanced: 7.0,  // AAA level
};

test.describe('WCAG 2.1 AA Compliance Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
    await injectAxe(page);
  });

  test('meets WCAG 2.1 AA standards', async ({ page }) => {
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true },
      tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
      rules: WCAG_AA_RULES
    });
  });

  test('has proper document structure', async ({ page }) => {
    // Check for proper HTML structure
    const html = await page.locator('html').first();
    const lang = await html.getAttribute('lang');
    expect(lang).toBeTruthy();
    expect(lang).toMatch(/^[a-z]{2}(-[A-Z]{2})?$/); // Valid language code

    // Check for proper heading hierarchy
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    
    if (headings.length > 0) {
      // Should have exactly one h1
      const h1Elements = await page.locator('h1').all();
      expect(h1Elements.length).toBeLessThanOrEqual(1);

      // Check heading order
      let previousLevel = 0;
      for (const heading of headings) {
        const tagName = await heading.evaluate(el => el.tagName);
        const level = parseInt(tagName.charAt(1));
        
        if (previousLevel > 0) {
          // Heading levels should not skip (e.g., h1 -> h3)
          expect(level - previousLevel).toBeLessThanOrEqual(1);
        }
        
        previousLevel = level;
      }
    }
  });

  test('has proper landmark structure', async ({ page }) => {
    // Check for main landmark
    const mainLandmarks = await page.locator('main, [role="main"]').all();
    expect(mainLandmarks.length).toBeGreaterThanOrEqual(1);
    expect(mainLandmarks.length).toBeLessThanOrEqual(1); // Should have exactly one main

    // Check for navigation landmark
    const navLandmarks = await page.locator('nav, [role="navigation"]').all();
    expect(navLandmarks.length).toBeGreaterThan(0);

    // Check for banner (header)
    const bannerLandmarks = await page.locator('header, [role="banner"]').all();
    if (bannerLandmarks.length > 0) {
      expect(bannerLandmarks.length).toBeLessThanOrEqual(1);
    }

    // Check for contentinfo (footer)
    const contentinfoLandmarks = await page.locator('footer, [role="contentinfo"]').all();
    if (contentinfoLandmarks.length > 0) {
      expect(contentinfoLandmarks.length).toBeLessThanOrEqual(1);
    }
  });

  test('all images have appropriate alt text', async ({ page }) => {
    const images = await page.locator('img').all();
    
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      const role = await img.getAttribute('role');
      const ariaLabel = await img.getAttribute('aria-label');
      const ariaLabelledBy = await img.getAttribute('aria-labelledby');
      
      // Decorative images should have empty alt or role="presentation"
      if (role === 'presentation' || role === 'none') {
        expect(alt).toBe('');
      } else {
        // Content images should have meaningful alt text
        expect(alt !== null || ariaLabel || ariaLabelledBy).toBeTruthy();
        
        if (alt !== null && alt !== '') {
          // Alt text should be meaningful
          expect(alt.length).toBeGreaterThan(0);
          expect(alt.toLowerCase()).not.toContain('image');
          expect(alt.toLowerCase()).not.toContain('picture');
          expect(alt.toLowerCase()).not.toContain('photo');
        }
      }
    }
  });

  test('all form controls have labels', async ({ page }) => {
    const formControls = await page.locator('input, select, textarea').all();
    
    for (const control of formControls) {
      const type = await control.getAttribute('type');
      
      // Skip hidden inputs
      if (type === 'hidden') continue;
      
      const id = await control.getAttribute('id');
      const ariaLabel = await control.getAttribute('aria-label');
      const ariaLabelledBy = await control.getAttribute('aria-labelledby');
      const title = await control.getAttribute('title');
      
      let hasLabel = false;
      
      // Check for associated label
      if (id) {
        const label = await page.locator(`label[for="${id}"]`).first();
        hasLabel = await label.count() > 0;
      }
      
      // Check for aria-label or aria-labelledby
      if (!hasLabel) {
        hasLabel = !!(ariaLabel || ariaLabelledBy);
      }
      
      // Check for title attribute (less preferred)
      if (!hasLabel) {
        hasLabel = !!title;
      }
      
      // Check for placeholder (not sufficient but better than nothing)
      if (!hasLabel && ['text', 'email', 'password', 'search'].includes(type)) {
        const placeholder = await control.getAttribute('placeholder');
        if (placeholder) {
          console.warn('Form control relies on placeholder for labeling (not ideal)');
          hasLabel = true;
        }
      }
      
      expect(hasLabel).toBeTruthy();
    }
  });

  test('all interactive elements are keyboard accessible', async ({ page }) => {
    // Get all interactive elements
    const interactiveElements = await page.locator(
      'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"]), [role="button"], [role="link"], [role="menuitem"]'
    ).all();
    
    for (const element of interactiveElements.slice(0, 10)) { // Test first 10 elements
      if (await element.isVisible()) {
        // Element should be focusable
        await element.focus();
        const isFocused = await element.evaluate(el => document.activeElement === el);
        expect(isFocused).toBeTruthy();
        
        // Test keyboard activation
        const tagName = await element.evaluate(el => el.tagName.toLowerCase());
        const role = await element.getAttribute('role');
        
        if (tagName === 'button' || role === 'button') {
          // Buttons should activate with Space and Enter
          await element.press('Space');
          await page.waitForTimeout(100);
          await element.press('Enter');
          await page.waitForTimeout(100);
        } else if (tagName === 'a' || role === 'link') {
          // Links should activate with Enter
          await element.press('Enter');
          await page.waitForTimeout(100);
        }
      }
    }
  });

  test('focus indicators are visible', async ({ page }) => {
    const focusableElements = await page.locator(
      'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ).all();
    
    for (const element of focusableElements.slice(0, 5)) { // Test first 5 elements
      if (await element.isVisible()) {
        await element.focus();
        
        // Check for focus indicator
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el, ':focus');
          return {
            outline: computed.outline,
            outlineWidth: computed.outlineWidth,
            outlineStyle: computed.outlineStyle,
            outlineColor: computed.outlineColor,
            boxShadow: computed.boxShadow,
            border: computed.border
          };
        });
        
        // Should have some form of focus indicator
        const hasFocusIndicator = 
          styles.outline !== 'none' ||
          styles.outlineWidth !== '0px' ||
          styles.boxShadow !== 'none' ||
          styles.border !== 'none';
        
        expect(hasFocusIndicator).toBeTruthy();
      }
    }
  });

  test('color contrast meets WCAG AA requirements', async ({ page }) => {
    // This is handled by axe-core, but we can add custom checks
    const textElements = await page.locator('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label').all();
    
    for (const element of textElements.slice(0, 10)) { // Test first 10 elements
      if (await element.isVisible()) {
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor,
            fontSize: computed.fontSize
          };
        });
        
        // Log color information for manual verification
        const text = await element.textContent();
        if (text && text.trim().length > 0) {
          console.log(`Text: "${text.slice(0, 50)}" - Color: ${styles.color}, Background: ${styles.backgroundColor}, Size: ${styles.fontSize}`);
        }
      }
    }
  });

  test('skip links are present and functional', async ({ page }) => {
    // Look for skip links
    const skipLinks = await page.locator('a[href^="#"]:has-text("skip"), a[href^="#"]:has-text("jump")').all();
    
    if (skipLinks.length > 0) {
      for (const skipLink of skipLinks) {
        const href = await skipLink.getAttribute('href');
        const targetId = href.substring(1);
        
        // Target should exist
        const target = await page.locator(`#${targetId}`).first();
        expect(await target.count()).toBeGreaterThan(0);
        
        // Skip link should be focusable
        await skipLink.focus();
        const isFocused = await skipLink.evaluate(el => document.activeElement === el);
        expect(isFocused).toBeTruthy();
        
        // Test skip link functionality
        await skipLink.click();
        await page.waitForTimeout(500);
        
        // Focus should move to target or near target
        const activeElement = await page.locator(':focus').first();
        const activeId = await activeElement.getAttribute('id');
        
        // Either the target itself is focused, or focus is within the target
        const focusedCorrectly = 
          activeId === targetId ||
          await target.locator(':focus').count() > 0;
        
        expect(focusedCorrectly).toBeTruthy();
      }
    } else {
      console.warn('No skip links found - consider adding for better accessibility');
    }
  });

  test('ARIA attributes are used correctly', async ({ page }) => {
    // Check for proper ARIA usage
    const ariaElements = await page.locator('[aria-label], [aria-labelledby], [aria-describedby], [role]').all();
    
    for (const element of ariaElements) {
      const ariaLabel = await element.getAttribute('aria-label');
      const ariaLabelledBy = await element.getAttribute('aria-labelledby');
      const ariaDescribedBy = await element.getAttribute('aria-describedby');
      const role = await element.getAttribute('role');
      
      // ARIA labels should not be empty
      if (ariaLabel !== null) {
        expect(ariaLabel.trim().length).toBeGreaterThan(0);
      }
      
      // Referenced elements should exist
      if (ariaLabelledBy) {
        const referencedIds = ariaLabelledBy.split(' ');
        for (const id of referencedIds) {
          const referencedElement = await page.locator(`#${id}`).first();
          expect(await referencedElement.count()).toBeGreaterThan(0);
        }
      }
      
      if (ariaDescribedBy) {
        const referencedIds = ariaDescribedBy.split(' ');
        for (const id of referencedIds) {
          const referencedElement = await page.locator(`#${id}`).first();
          expect(await referencedElement.count()).toBeGreaterThan(0);
        }
      }
      
      // Role should be valid
      if (role) {
        const validRoles = [
          'alert', 'alertdialog', 'application', 'article', 'banner', 'button',
          'cell', 'checkbox', 'columnheader', 'combobox', 'complementary',
          'contentinfo', 'definition', 'dialog', 'directory', 'document',
          'feed', 'figure', 'form', 'grid', 'gridcell', 'group', 'heading',
          'img', 'link', 'list', 'listbox', 'listitem', 'log', 'main',
          'marquee', 'math', 'menu', 'menubar', 'menuitem', 'menuitemcheckbox',
          'menuitemradio', 'navigation', 'none', 'note', 'option', 'presentation',
          'progressbar', 'radio', 'radiogroup', 'region', 'row', 'rowgroup',
          'rowheader', 'scrollbar', 'search', 'searchbox', 'separator',
          'slider', 'spinbutton', 'status', 'switch', 'tab', 'table',
          'tablist', 'tabpanel', 'term', 'textbox', 'timer', 'toolbar',
          'tooltip', 'tree', 'treegrid', 'treeitem'
        ];
        
        expect(validRoles).toContain(role);
      }
    }
  });

  test('tables have proper structure', async ({ page }) => {
    const tables = await page.locator('table').all();
    
    for (const table of tables) {
      // Tables should have headers
      const headers = await table.locator('th').all();
      const caption = await table.locator('caption').first();
      const summary = await table.getAttribute('summary');
      
      if (headers.length === 0) {
        // If no th elements, should have caption or summary
        expect(await caption.count() > 0 || summary).toBeTruthy();
      }
      
      // Check header scope
      for (const header of headers) {
        const scope = await header.getAttribute('scope');
        if (scope) {
          expect(['col', 'row', 'colgroup', 'rowgroup']).toContain(scope);
        }
      }
    }
  });

  test('error messages are accessible', async ({ page }) => {
    // Look for form validation or error scenarios
    const forms = await page.locator('form').all();
    
    for (const form of forms) {
      const submitButton = await form.locator('button[type="submit"], input[type="submit"]').first();
      
      if (await submitButton.isVisible()) {
        // Try to submit form to trigger validation
        await submitButton.click();
        await page.waitForTimeout(1000);
        
        // Look for error messages
        const errorMessages = await page.locator('[role="alert"], .error, [aria-invalid="true"]').all();
        
        for (const error of errorMessages) {
          if (await error.isVisible()) {
            // Error should have text content
            const textContent = await error.textContent();
            expect(textContent?.trim()).toBeTruthy();
            
            // Error should be associated with form field
            const id = await error.getAttribute('id');
            if (id) {
              const associatedField = await page.locator(`[aria-describedby*="${id}"]`).first();
              expect(await associatedField.count()).toBeGreaterThan(0);
            }
          }
        }
      }
    }
  });

  test('dynamic content changes are announced', async ({ page }) => {
    // Check for ARIA live regions
    const liveRegions = await page.locator('[aria-live]').all();
    expect(liveRegions.length).toBeGreaterThan(0);
    
    for (const region of liveRegions) {
      const ariaLive = await region.getAttribute('aria-live');
      expect(['polite', 'assertive', 'off']).toContain(ariaLive);
    }
    
    // Check for status regions
    const statusRegions = await page.locator('[role="status"], [role="alert"]').all();
    
    for (const region of statusRegions) {
      // Status regions should be properly labeled
      const ariaLabel = await region.getAttribute('aria-label');
      const ariaLabelledBy = await region.getAttribute('aria-labelledby');
      
      if (!ariaLabel && !ariaLabelledBy) {
        // Should at least have meaningful content
        const textContent = await region.textContent();
        expect(textContent?.trim()).toBeTruthy();
      }
    }
  });
});
