import { initErrorTracking } from './errorTracker';

// Mock fetch for testing
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  })
);

// Mock console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

describe('errorTracker', () => {
  let errorTracker;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock console methods
    console.error = jest.fn();
    console.warn = jest.fn();
    
    // Initialize error tracker with test configuration
    errorTracker = initErrorTracking({
      enabled: true,
      samplingRate: 1.0,
      errorLimit: 10,
      breadcrumbLimit: 10,
      ignoredErrors: [],
      reportingEndpoint: '/api/test-errors',
      logToConsole: false,
      captureConsoleErrors: true,
      captureNetworkErrors: true,
      captureUnhandledRejections: true,
      captureBreadcrumbs: true
    });
  });
  
  afterEach(() => {
    // Restore console methods
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  });
  
  test('should initialize with custom configuration', () => {
    const config = errorTracker.getConfig();
    expect(config.enabled).toBe(true);
    expect(config.reportingEndpoint).toBe('/api/test-errors');
  });
  
  test('should track errors manually', () => {
    // Track an error
    errorTracker.trackError(new Error('Test error'));
    
    // Get tracked errors
    const errors = errorTracker.getErrors();
    
    // Verify error was tracked
    expect(errors.length).toBe(1);
    expect(errors[0].message).toBe('Test error');
    expect(errors[0].type).toBe('manual');
  });
  
  test('should add breadcrumbs manually', () => {
    // Add a breadcrumb
    errorTracker.addBreadcrumb('Test breadcrumb', 'test', { key: 'value' });
    
    // Get breadcrumbs
    const breadcrumbs = errorTracker.getBreadcrumbs();
    
    // Verify breadcrumb was added
    expect(breadcrumbs.length).toBe(1);
    expect(breadcrumbs[0].message).toBe('Test breadcrumb');
    expect(breadcrumbs[0].category).toBe('test');
    expect(breadcrumbs[0].data.key).toBe('value');
  });
  
  test('should clear errors', () => {
    // Track some errors
    errorTracker.trackError(new Error('Error 1'));
    errorTracker.trackError(new Error('Error 2'));
    
    // Verify errors were tracked
    expect(errorTracker.getErrors().length).toBe(2);
    
    // Clear errors
    errorTracker.clearErrors();
    
    // Verify errors were cleared
    expect(errorTracker.getErrors().length).toBe(0);
  });
  
  test('should clear breadcrumbs', () => {
    // Add some breadcrumbs
    errorTracker.addBreadcrumb('Breadcrumb 1');
    errorTracker.addBreadcrumb('Breadcrumb 2');
    
    // Verify breadcrumbs were added
    expect(errorTracker.getBreadcrumbs().length).toBe(2);
    
    // Clear breadcrumbs
    errorTracker.clearBreadcrumbs();
    
    // Verify breadcrumbs were cleared
    expect(errorTracker.getBreadcrumbs().length).toBe(0);
  });
  
  test('should update configuration', () => {
    // Update configuration
    errorTracker.updateConfig({
      enabled: false,
      samplingRate: 0.5
    });
    
    // Verify configuration was updated
    const config = errorTracker.getConfig();
    expect(config.enabled).toBe(false);
    expect(config.samplingRate).toBe(0.5);
  });
  
  test('should capture console errors', () => {
    // Trigger a console error
    console.error('Test console error');
    
    // Get tracked errors
    const errors = errorTracker.getErrors();
    
    // Verify error was tracked
    expect(errors.length).toBe(1);
    expect(errors[0].type).toBe('console_error');
    expect(errors[0].message).toContain('Test console error');
  });
  
  test('should capture console warnings as breadcrumbs', () => {
    // Trigger a console warning
    console.warn('Test console warning');
    
    // Get breadcrumbs
    const breadcrumbs = errorTracker.getBreadcrumbs();
    
    // Verify breadcrumb was added
    expect(breadcrumbs.length).toBe(1);
    expect(breadcrumbs[0].type).toBe('console_warn');
    expect(breadcrumbs[0].message).toContain('Test console warning');
  });
  
  test('should respect error limit', () => {
    // Track more errors than the limit
    for (let i = 0; i < 15; i++) {
      errorTracker.trackError(new Error(`Error ${i}`));
    }
    
    // Get tracked errors
    const errors = errorTracker.getErrors();
    
    // Verify only the limit number of errors were kept
    expect(errors.length).toBe(10);
    
    // Verify the oldest errors were removed
    expect(errors[0].message).toBe('Error 5');
    expect(errors[9].message).toBe('Error 14');
  });
  
  test('should respect breadcrumb limit', () => {
    // Add more breadcrumbs than the limit
    for (let i = 0; i < 15; i++) {
      errorTracker.addBreadcrumb(`Breadcrumb ${i}`);
    }
    
    // Get breadcrumbs
    const breadcrumbs = errorTracker.getBreadcrumbs();
    
    // Verify only the limit number of breadcrumbs were kept
    expect(breadcrumbs.length).toBe(10);
    
    // Verify the oldest breadcrumbs were removed
    expect(breadcrumbs[0].message).toBe('Breadcrumb 5');
    expect(breadcrumbs[9].message).toBe('Breadcrumb 14');
  });
  
  test('should report errors to the server', () => {
    // Track an error
    errorTracker.trackError(new Error('Server-reported error'));
    
    // Verify fetch was called
    expect(fetch).toHaveBeenCalledWith(
      '/api/test-errors',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        }),
        body: expect.any(String)
      })
    );
    
    // Verify the error data was sent
    const body = JSON.parse(fetch.mock.calls[0][1].body);
    expect(body.message).toBe('Server-reported error');
    expect(body.type).toBe('manual');
  });
});
