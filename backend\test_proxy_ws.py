#!/usr/bin/env python3
"""
Test WebSocket connection through the frontend proxy
"""

import json
import time
from websocket import create_connection, WebSocketTimeoutException

def test_proxy_websocket():
    """Test WebSocket connection through the frontend proxy"""
    
    # Test the proxied WebSocket URL
    proxy_url = "ws://localhost:3000/ws"
    direct_url = "ws://localhost:8000/ws/"
    
    print("Testing WebSocket connections...")
    print("=" * 50)
    
    # Test direct connection to backend
    print(f"\n1. Testing DIRECT connection: {direct_url}")
    try:
        ws = create_connection(direct_url, timeout=5)
        print("✅ Direct connection successful!")
        
        # Send test message
        test_msg = json.dumps({"type": "ping", "message": "Direct test"})
        ws.send(test_msg)
        response = ws.recv()
        print(f"✅ Direct response: {response[:100]}...")
        ws.close()
        
    except Exception as e:
        print(f"❌ Direct connection failed: {e}")
    
    # Test proxied connection through frontend
    print(f"\n2. Testing PROXIED connection: {proxy_url}")
    try:
        ws = create_connection(proxy_url, timeout=5)
        print("✅ Proxied connection successful!")
        
        # Send test message
        test_msg = json.dumps({"type": "ping", "message": "Proxy test"})
        ws.send(test_msg)
        response = ws.recv()
        print(f"✅ Proxied response: {response[:100]}...")
        ws.close()
        
    except Exception as e:
        print(f"❌ Proxied connection failed: {e}")
    
    # Test with different endpoints
    endpoints_to_test = [
        ("ws://localhost:3000/ws/", "Frontend proxy root"),
        ("ws://localhost:3000/ws/app_builder/", "Frontend proxy app_builder"),
        ("ws://localhost:3000/ws/simple/", "Frontend proxy simple"),
    ]
    
    print(f"\n3. Testing various proxied endpoints:")
    for url, description in endpoints_to_test:
        print(f"\nTesting {description}: {url}")
        try:
            ws = create_connection(url, timeout=5)
            print(f"✅ {description} - Connection successful!")
            
            test_msg = json.dumps({"type": "ping", "message": f"Test from {description}"})
            ws.send(test_msg)
            response = ws.recv()
            print(f"✅ Response: {response[:100]}...")
            ws.close()
            
        except Exception as e:
            print(f"❌ {description} - Failed: {e}")

if __name__ == "__main__":
    test_proxy_websocket()
