import React, { useState, useEffect, useMemo } from 'react';
import { Card, Typo<PERSON>, Space, Button, Switch, Divider, Alert } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined, CodeOutlined, UndoOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';

const { Text, Title } = Typography;

const PreviewContainer = styled.div`
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  border-bottom: 1px solid #f0f0f0;
`;

const PreviewArea = styled.div`
  min-height: 120px;
  padding: 16px;
  background: ${props => props.background || '#f5f5f5'};
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
`;

const PreviewElement = styled.div`
  transition: all 0.2s ease;
  ${props => props.styles || ''}
`;

const CodePreview = styled.pre`
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  overflow-x: auto;
  max-height: 200px;
  margin: 0;
`;

const ValidationMessage = styled.div`
  margin-top: 8px;
`;

/**
 * Real-time preview component for property changes
 */
const PropertyPreview = ({
  component,
  properties = {},
  values = {},
  showPreview = true,
  showCode = false,
  showValidation = true,
  onReset,
  ...props
}) => {
  const [previewEnabled, setPreviewEnabled] = useState(showPreview);
  const [codeVisible, setCodeVisible] = useState(showCode);
  const [validationErrors, setValidationErrors] = useState({});

  // Generate styles from current property values
  const generatedStyles = useMemo(() => {
    const styles = {};
    
    Object.entries(values).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // Convert camelCase to kebab-case for CSS
        const cssProperty = key.replace(/([A-Z])/g, '-$1').toLowerCase();
        
        // Handle special cases
        if (typeof value === 'object' && value !== null) {
          // For complex objects like font settings
          if (key === 'font' || key === 'fontFamily') {
            Object.entries(value).forEach(([subKey, subValue]) => {
              const cssSubProperty = subKey.replace(/([A-Z])/g, '-$1').toLowerCase();
              styles[cssSubProperty] = subValue;
            });
          } else {
            // Convert object to string representation
            styles[cssProperty] = JSON.stringify(value);
          }
        } else {
          styles[cssProperty] = value;
        }
      }
    });
    
    return styles;
  }, [values]);

  // Generate CSS string for code preview
  const cssCode = useMemo(() => {
    const cssLines = Object.entries(generatedStyles).map(([property, value]) => {
      return `  ${property}: ${value};`;
    });
    
    return `.component {\n${cssLines.join('\n')}\n}`;
  }, [generatedStyles]);

  // Generate inline styles object
  const inlineStyles = useMemo(() => {
    const styles = {};
    
    Object.entries(generatedStyles).forEach(([property, value]) => {
      // Convert kebab-case back to camelCase for React inline styles
      const reactProperty = property.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
      styles[reactProperty] = value;
    });
    
    return styles;
  }, [generatedStyles]);

  // Validate current values
  useEffect(() => {
    const errors = {};
    
    Object.entries(values).forEach(([key, value]) => {
      const schema = properties[key];
      if (schema && value !== undefined && value !== null && value !== '') {
        // Basic validation
        if (schema.type === 'number') {
          const num = parseFloat(value);
          if (isNaN(num)) {
            errors[key] = 'Must be a valid number';
          } else if (schema.min !== undefined && num < schema.min) {
            errors[key] = `Must be at least ${schema.min}`;
          } else if (schema.max !== undefined && num > schema.max) {
            errors[key] = `Must be at most ${schema.max}`;
          }
        } else if (schema.type === 'color') {
          const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/;
          if (!colorRegex.test(value)) {
            errors[key] = 'Must be a valid color value';
          }
        }
      }
    });
    
    setValidationErrors(errors);
  }, [values, properties]);

  // Render preview element based on component type
  const renderPreviewElement = () => {
    const elementProps = {
      style: inlineStyles,
      className: 'preview-element'
    };

    switch (component?.type) {
      case 'button':
        return (
          <button {...elementProps}>
            {values.text || 'Button'}
          </button>
        );
        
      case 'text':
        const Tag = values.variant || 'p';
        return React.createElement(Tag, elementProps, values.content || 'Sample text');
        
      case 'input':
        return (
          <input
            {...elementProps}
            type={values.type || 'text'}
            placeholder={values.placeholder || 'Enter text'}
            disabled={values.disabled}
          />
        );
        
      case 'card':
        return (
          <div {...elementProps} style={{ ...inlineStyles, border: '1px solid #d9d9d9', borderRadius: '4px', padding: '16px', minWidth: '200px' }}>
            {values.title && <h4 style={{ margin: '0 0 8px 0' }}>{values.title}</h4>}
            {values.description && <p style={{ margin: 0, color: '#666' }}>{values.description}</p>}
          </div>
        );
        
      default:
        return (
          <div {...elementProps} style={{ ...inlineStyles, padding: '16px', background: '#fff', border: '1px solid #d9d9d9', borderRadius: '4px' }}>
            {component?.name || 'Component'} Preview
          </div>
        );
    }
  };

  const hasErrors = Object.keys(validationErrors).length > 0;
  const hasChanges = Object.keys(values).some(key => values[key] !== undefined && values[key] !== '' && values[key] !== null);

  return (
    <PreviewContainer>
      <Card size="small" title="Property Preview">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Space>
              <Switch
                checked={previewEnabled}
                onChange={setPreviewEnabled}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeInvisibleOutlined />}
                size="small"
              />
              <Text style={{ fontSize: '12px' }}>Live Preview</Text>
            </Space>
            
            <Space>
              <Button
                type="text"
                size="small"
                icon={<CodeOutlined />}
                onClick={() => setCodeVisible(!codeVisible)}
                style={{ fontSize: '12px' }}
              >
                {codeVisible ? 'Hide' : 'Show'} CSS
              </Button>
              
              {hasChanges && (
                <Button
                  type="text"
                  size="small"
                  icon={<UndoOutlined />}
                  onClick={onReset}
                  style={{ fontSize: '12px' }}
                >
                  Reset All
                </Button>
              )}
            </Space>
          </Space>

          {previewEnabled && (
            <PreviewArea>
              <PreviewElement styles={Object.entries(inlineStyles).map(([k, v]) => `${k}: ${v};`).join(' ')}>
                {renderPreviewElement()}
              </PreviewElement>
            </PreviewArea>
          )}

          {codeVisible && (
            <>
              <Divider style={{ margin: '8px 0' }} />
              <div>
                <Text strong style={{ fontSize: '12px' }}>Generated CSS:</Text>
                <CodePreview>{cssCode}</CodePreview>
              </div>
            </>
          )}

          {showValidation && hasErrors && (
            <ValidationMessage>
              <Alert
                message="Validation Errors"
                description={
                  <ul style={{ margin: 0, paddingLeft: '16px' }}>
                    {Object.entries(validationErrors).map(([key, error]) => (
                      <li key={key} style={{ fontSize: '12px' }}>
                        <strong>{key}:</strong> {error}
                      </li>
                    ))}
                  </ul>
                }
                type="error"
                size="small"
                showIcon
              />
            </ValidationMessage>
          )}

          {showValidation && !hasErrors && hasChanges && (
            <ValidationMessage>
              <Alert
                message="All properties are valid"
                type="success"
                size="small"
                showIcon
              />
            </ValidationMessage>
          )}
        </Space>
      </Card>
    </PreviewContainer>
  );
};

export default PropertyPreview;
