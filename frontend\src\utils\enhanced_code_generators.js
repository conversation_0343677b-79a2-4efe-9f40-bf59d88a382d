/**
 * Enhanced code generators for multiple frameworks and project structures
 * This file contains generators for Vue, Angular, Svelte, Next.js, React Native, Flutter, etc.
 */

/**
 * Generate Vue.js code
 */
export const generateVueCode = (appData, options = {}) => {
  const { components, layouts, styles, data } = appData;
  const { typescript, includeAccessibility, projectStructure } = options;
  
  if (projectStructure === 'full-project') {
    return generateVueProject(appData, options);
  }
  
  const fileExtension = typescript ? 'vue' : 'vue';
  let vueCode = `<template>
  <div class="app"${includeAccessibility ? ' role="main"' : ''}>`;
  
  // Generate template structure
  layouts.forEach(layout => {
    const layoutName = layout.name || layout.type || 'layout';
    vueCode += `
    <div class="${layoutName}-container">`;
    
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(componentId => {
        const component = components.find(c => c.id === componentId);
        if (component) {
          vueCode += `
      <${component.type.toLowerCase()}`;
          Object.entries(component.props || {}).forEach(([prop, value]) => {
            if (typeof value === 'string') {
              vueCode += ` ${prop}="${value}"`;
            } else {
              vueCode += ` :${prop}="${JSON.stringify(value)}"`;
            }
          });
          vueCode += ` />`;
        }
      });
    }
    
    vueCode += `
    </div>`;
  });
  
  vueCode += `
  </div>
</template>

<script${typescript ? ' lang="ts"' : ''}>
import { defineComponent, ref, reactive } from 'vue';

export default defineComponent({
  name: 'App',
  setup() {`;
  
  // Add reactive data
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(([key, value]) => {
      vueCode += `
    const ${key} = ref(${JSON.stringify(value)});`;
    });
  }
  
  vueCode += `
    
    return {`;
  
  if (data && Object.keys(data).length > 0) {
    Object.keys(data).forEach(key => {
      vueCode += `
      ${key},`;
    });
  }
  
  vueCode += `
    };
  }
});
</script>

<style scoped>
.app {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}

${generateVueStyles(styles, components, layouts)}
</style>`;

  return vueCode;
};

/**
 * Generate Angular code
 */
export const generateAngularCode = (appData, options = {}) => {
  const { components, layouts, styles, data } = appData;
  const { typescript = true, includeAccessibility, projectStructure } = options;
  
  if (projectStructure === 'full-project') {
    return generateAngularProject(appData, options);
  }
  
  // Component TypeScript
  let componentCode = `import { Component } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  title = 'Generated App';`;
  
  // Add component properties
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(([key, value]) => {
      componentCode += `
  ${key} = ${JSON.stringify(value)};`;
    });
  }
  
  componentCode += `
}`;

  // Template HTML
  let templateCode = `<div class="app"${includeAccessibility ? ' role="main"' : ''}>`;
  
  layouts.forEach(layout => {
    const layoutName = layout.name || layout.type || 'layout';
    templateCode += `
  <div class="${layoutName}-container">`;
    
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(componentId => {
        const component = components.find(c => c.id === componentId);
        if (component) {
          templateCode += `
    <app-${component.type.toLowerCase()}`;
          Object.entries(component.props || {}).forEach(([prop, value]) => {
            if (typeof value === 'string') {
              templateCode += ` ${prop}="${value}"`;
            } else {
              templateCode += ` [${prop}]="${JSON.stringify(value)}"`;
            }
          });
          templateCode += `></app-${component.type.toLowerCase()}>`;
        }
      });
    }
    
    templateCode += `
  </div>`;
  });
  
  templateCode += `
</div>`;

  // Styles CSS
  let stylesCode = `.app {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}

${generateAngularStyles(styles, components, layouts)}`;

  return {
    'app.component.ts': componentCode,
    'app.component.html': templateCode,
    'app.component.css': stylesCode
  };
};

/**
 * Generate Svelte code
 */
export const generateSvelteCode = (appData, options = {}) => {
  const { components, layouts, styles, data } = appData;
  const { typescript, includeAccessibility } = options;
  
  let svelteCode = `<script${typescript ? ' lang="ts"' : ''}>`;
  
  // Add reactive data
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(([key, value]) => {
      svelteCode += `
  let ${key} = ${JSON.stringify(value)};`;
    });
  }
  
  svelteCode += `
</script>

<div class="app"${includeAccessibility ? ' role="main"' : ''}>`;
  
  // Generate template structure
  layouts.forEach(layout => {
    const layoutName = layout.name || layout.type || 'layout';
    svelteCode += `
  <div class="${layoutName}-container">`;
    
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(componentId => {
        const component = components.find(c => c.id === componentId);
        if (component) {
          svelteCode += `
    <${component.type}`;
          Object.entries(component.props || {}).forEach(([prop, value]) => {
            if (typeof value === 'string') {
              svelteCode += ` ${prop}="${value}"`;
            } else {
              svelteCode += ` ${prop}={${JSON.stringify(value)}}`;
            }
          });
          svelteCode += ` />`;
        }
      });
    }
    
    svelteCode += `
  </div>`;
  });
  
  svelteCode += `
</div>

<style>
  .app {
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
  }

  ${generateSvelteStyles(styles, components, layouts)}
</style>`;

  return svelteCode;
};

/**
 * Generate Next.js code
 */
export const generateNextJSCode = (appData, options = {}) => {
  const { components, layouts, styles, data } = appData;
  const { typescript, includeAccessibility, projectStructure } = options;
  
  if (projectStructure === 'full-project') {
    return generateNextJSProject(appData, options);
  }
  
  const fileExtension = typescript ? 'tsx' : 'jsx';
  
  let nextCode = `import React from 'react';
import Head from 'next/head';
import styles from '../styles/Home.module.css';

export default function Home() {
  return (
    <div className={styles.container}>
      <Head>
        <title>Generated App</title>
        <meta name="description" content="Generated by App Builder" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className={styles.main}${includeAccessibility ? ' role="main"' : ''}>`;
  
  // Generate layout structure
  layouts.forEach(layout => {
    const layoutName = layout.name || layout.type || 'layout';
    nextCode += `
        <div className={styles.${layoutName}Container}>`;
    
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(componentId => {
        const component = components.find(c => c.id === componentId);
        if (component) {
          nextCode += `
          <${component.type}`;
          Object.entries(component.props || {}).forEach(([prop, value]) => {
            if (typeof value === 'string') {
              nextCode += ` ${prop}="${value}"`;
            } else {
              nextCode += ` ${prop}={${JSON.stringify(value)}}`;
            }
          });
          nextCode += ` />`;
        }
      });
    }
    
    nextCode += `
        </div>`;
  });
  
  nextCode += `
      </main>
    </div>
  );
}`;

  return {
    [`pages/index.${fileExtension}`]: nextCode,
    'styles/Home.module.css': generateNextJSStyles(styles, components, layouts),
    'package.json': generatePackageJson('nextjs-app', options)
  };
};

/**
 * Generate React Native code
 */
export const generateReactNativeCode = (appData, options = {}) => {
  const { components, layouts, styles, data } = appData;
  const { typescript, includeAccessibility } = options;
  
  const fileExtension = typescript ? 'tsx' : 'jsx';
  
  let rnCode = `import React${data && Object.keys(data).length > 0 ? ', { useState }' : ''} from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';

const App = () => {`;
  
  // Add state management
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(([key, value]) => {
      rnCode += `
  const [${key}, set${pascalCase(key)}] = useState(${JSON.stringify(value)});`;
    });
  }
  
  rnCode += `
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <ScrollView contentInsetAdjustmentBehavior="automatic">`;
  
  // Generate layout structure
  layouts.forEach(layout => {
    rnCode += `
        <View style={styles.${layout.name || layout.type || 'layout'}Container}>`;
    
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(componentId => {
        const component = components.find(c => c.id === componentId);
        if (component) {
          rnCode += `
          <${mapToReactNativeComponent(component.type)}`;
          Object.entries(component.props || {}).forEach(([prop, value]) => {
            const mappedProp = mapToReactNativeProp(prop, component.type);
            if (typeof value === 'string') {
              rnCode += ` ${mappedProp}="${value}"`;
            } else {
              rnCode += ` ${mappedProp}={${JSON.stringify(value)}}`;
            }
          });
          rnCode += ` />`;
        }
      });
    }
    
    rnCode += `
        </View>`;
  });
  
  rnCode += `
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  ${generateReactNativeStyles(styles, components, layouts)}
});

export default App;`;

  return rnCode;
};

// Helper functions for style generation
const generateVueStyles = (styles, components, layouts) => {
  // Implementation for Vue-specific styles
  return Object.entries(styles).map(([selector, style]) => {
    return `${selector} {
  ${Object.entries(style).map(([prop, value]) => `${prop}: ${value};`).join('\n  ')}
}`;
  }).join('\n\n');
};

const generateAngularStyles = (styles, components, layouts) => {
  // Implementation for Angular-specific styles
  return Object.entries(styles).map(([selector, style]) => {
    return `${selector} {
  ${Object.entries(style).map(([prop, value]) => `${prop}: ${value};`).join('\n  ')}
}`;
  }).join('\n\n');
};

const generateSvelteStyles = (styles, components, layouts) => {
  // Implementation for Svelte-specific styles
  return Object.entries(styles).map(([selector, style]) => {
    return `${selector} {
    ${Object.entries(style).map(([prop, value]) => `${prop}: ${value};`).join('\n    ')}
  }`;
  }).join('\n\n');
};

const generateNextJSStyles = (styles, components, layouts) => {
  // Implementation for Next.js CSS modules
  return `.container {
  padding: 0 2rem;
}

.main {
  min-height: 100vh;
  padding: 4rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

${Object.entries(styles).map(([selector, style]) => {
  return `${selector.replace(/[^a-zA-Z0-9]/g, '')} {
  ${Object.entries(style).map(([prop, value]) => `${prop}: ${value};`).join('\n  ')}
}`;
}).join('\n\n')}`;
};

const generateReactNativeStyles = (styles, components, layouts) => {
  // Implementation for React Native StyleSheet
  return layouts.map(layout => {
    const layoutName = layout.name || layout.type || 'layout';
    return `${layoutName}Container: {
    padding: 16,
    marginVertical: 8,
  },`;
  }).join('\n  ');
};

// Helper functions
const pascalCase = (str) => {
  return str.replace(/(?:^|[\s-_])(\w)/g, (match, letter) => letter.toUpperCase()).replace(/[\s-_]/g, '');
};

const mapToReactNativeComponent = (type) => {
  const componentMap = {
    'Button': 'TouchableOpacity',
    'Text': 'Text',
    'Input': 'TextInput',
    'Image': 'Image',
    'Card': 'View',
    'Section': 'View',
    'Header': 'View'
  };
  return componentMap[type] || 'View';
};

const mapToReactNativeProp = (prop, componentType) => {
  const propMap = {
    'text': 'title',
    'content': 'children',
    'src': 'source'
  };
  return propMap[prop] || prop;
};
