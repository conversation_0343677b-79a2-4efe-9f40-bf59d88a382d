/**
 * Tutorial Trigger Component
 * 
 * A floating action button and menu system for accessing tutorials
 * and help features throughout the application.
 */

import React, { useState, useEffect } from 'react';
import {
  FloatButton,
  Dropdown,
  Menu,
  Badge,
  Tooltip,
  Button,
  Space,
  Typography
} from 'antd';
import {
  QuestionCircleOutlined,
  BookOutlined,
  PlayCircleOutlined,
  TrophyOutlined,
  BulbOutlined,
  RocketOutlined,
  SettingOutlined,
  CloseOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import TutorialLauncher from './TutorialLauncher';
import TutorialProgress from './TutorialProgress';
import { getRecommendedTutorials } from './TutorialContent';
import { TUTORIAL_STATUS } from './types';

const { Text } = Typography;

// Styled Components
const TriggerContainer = styled.div`
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
`;

const QuickAccessPanel = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  width: 280px;
  max-height: 400px;
  overflow-y: auto;
`;

const QuickTutorialItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f5f5;
  }
`;

const TutorialTrigger = ({ 
  position = 'bottom-right',
  showBadge = true,
  showQuickAccess = true,
  autoHide = false,
  hideDelay = 10000
}) => {
  const {
    startTutorial,
    getAllTutorials,
    getTutorialProgress,
    getStatistics,
    isActive
  } = useTutorial();

  const [showLauncher, setShowLauncher] = useState(false);
  const [showProgress, setShowProgress] = useState(false);
  const [showQuickPanel, setShowQuickPanel] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const statistics = getStatistics();
  const allTutorials = getAllTutorials();

  // Get recommended tutorials
  const completedTutorialIds = allTutorials
    .filter(tutorial => {
      const progress = getTutorialProgress(tutorial.id);
      return progress?.status === TUTORIAL_STATUS.COMPLETED;
    })
    .map(tutorial => tutorial.id);

  const recommendedTutorials = getRecommendedTutorials(completedTutorialIds).slice(0, 3);

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && !isActive) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, hideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, hideDelay, isActive]);

  // Hide trigger when tutorial is active
  useEffect(() => {
    if (isActive) {
      setShowQuickPanel(false);
    }
  }, [isActive]);

  const handleQuickStart = (tutorialId) => {
    startTutorial(tutorialId);
    setShowQuickPanel(false);
  };

  const getPositionStyle = () => {
    switch (position) {
      case 'bottom-left':
        return { bottom: 24, left: 24 };
      case 'top-right':
        return { top: 24, right: 24 };
      case 'top-left':
        return { top: 24, left: 24 };
      default:
        return { bottom: 24, right: 24 };
    }
  };

  const menuItems = [
    {
      key: 'browse',
      icon: <BookOutlined />,
      label: 'Browse Tutorials',
      onClick: () => setShowLauncher(true)
    },
    {
      key: 'progress',
      icon: <TrophyOutlined />,
      label: 'My Progress',
      onClick: () => setShowProgress(true)
    },
    {
      key: 'recommended',
      icon: <RocketOutlined />,
      label: 'Recommended',
      children: recommendedTutorials.length > 0 ? recommendedTutorials.map(tutorial => ({
        key: tutorial.id,
        label: tutorial.title,
        onClick: () => handleQuickStart(tutorial.id)
      })) : [{
        key: 'no-recommendations',
        label: 'No recommendations available',
        disabled: true
      }]
    },
    {
      type: 'divider'
    },
    {
      key: 'getting-started',
      icon: <PlayCircleOutlined />,
      label: 'Getting Started',
      onClick: () => handleQuickStart('getting_started')
    }
  ];

  if (!isVisible || isActive) {
    return null;
  }

  return (
    <>
      <TriggerContainer style={getPositionStyle()}>
        {showQuickAccess && showQuickPanel && (
          <QuickAccessPanel>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
              <Text strong>Quick Help</Text>
              <Button
                type="text"
                size="small"
                icon={<CloseOutlined />}
                onClick={() => setShowQuickPanel(false)}
              />
            </div>

            {recommendedTutorials.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Recommended for you:
                </Text>
                {recommendedTutorials.map(tutorial => (
                  <QuickTutorialItem
                    key={tutorial.id}
                    onClick={() => handleQuickStart(tutorial.id)}
                  >
                    <div style={{ marginRight: 8, fontSize: '16px' }}>
                      {tutorial.icon || <BookOutlined />}
                    </div>
                    <div style={{ flex: 1 }}>
                      <Text strong style={{ fontSize: '13px' }}>
                        {tutorial.title}
                      </Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {tutorial.estimatedDuration}min
                      </Text>
                    </div>
                  </QuickTutorialItem>
                ))}
              </div>
            )}

            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                block
                icon={<BookOutlined />}
                onClick={() => {
                  setShowLauncher(true);
                  setShowQuickPanel(false);
                }}
              >
                Browse All Tutorials
              </Button>
              
              <Button
                block
                icon={<TrophyOutlined />}
                onClick={() => {
                  setShowProgress(true);
                  setShowQuickPanel(false);
                }}
              >
                View Progress
              </Button>
            </Space>
          </QuickAccessPanel>
        )}

        <FloatButton.Group
          trigger="click"
          type="primary"
          style={{ right: 0 }}
          icon={
            showBadge && statistics.totalTutorialsCompleted < allTutorials.length ? (
              <Badge dot>
                <QuestionCircleOutlined />
              </Badge>
            ) : (
              <QuestionCircleOutlined />
            )
          }
          onClick={() => setShowQuickPanel(!showQuickPanel)}
        >
          <Tooltip title="Browse Tutorials" placement="left">
            <FloatButton
              icon={<BookOutlined />}
              onClick={() => setShowLauncher(true)}
            />
          </Tooltip>
          
          <Tooltip title="My Progress" placement="left">
            <FloatButton
              icon={<TrophyOutlined />}
              onClick={() => setShowProgress(true)}
              badge={
                showBadge && statistics.badgesEarned > 0 ? {
                  count: statistics.badgesEarned,
                  color: '#faad14'
                } : undefined
              }
            />
          </Tooltip>
          
          <Tooltip title="Quick Help" placement="left">
            <FloatButton
              icon={<BulbOutlined />}
              onClick={() => setShowQuickPanel(!showQuickPanel)}
            />
          </Tooltip>
          
          {recommendedTutorials.length > 0 && (
            <Tooltip title="Start Recommended Tutorial" placement="left">
              <FloatButton
                icon={<RocketOutlined />}
                onClick={() => handleQuickStart(recommendedTutorials[0].id)}
                badge={showBadge ? { dot: true, color: '#52c41a' } : undefined}
              />
            </Tooltip>
          )}
        </FloatButton.Group>
      </TriggerContainer>

      {/* Tutorial Launcher Modal */}
      <TutorialLauncher
        visible={showLauncher}
        onClose={() => setShowLauncher(false)}
      />

      {/* Progress Modal */}
      {showProgress && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            background: 'white',
            borderRadius: 8,
            padding: 24,
            maxWidth: '90vw',
            maxHeight: '90vh',
            overflow: 'auto',
            position: 'relative'
          }}>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setShowProgress(false)}
              style={{
                position: 'absolute',
                top: 8,
                right: 8
              }}
            />
            <TutorialProgress />
          </div>
        </div>
      )}
    </>
  );
};

export default TutorialTrigger;
