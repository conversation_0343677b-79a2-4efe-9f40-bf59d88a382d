import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Mock Redux slices/reducers
const createMockStore = (preloadedState = {}) => {
  return configureStore({
    reducer: {
      app: (state = {
        components: [],
        selectedComponent: null,
        layouts: [],
        styles: {},
        history: [],
        currentProject: null,
        isDirty: false,
        ...preloadedState.app
      }, action) => {
        switch (action.type) {
          case 'app/addComponent':
            return {
              ...state,
              components: [...state.components, action.payload],
              history: [...state.history, { type: 'add', component: action.payload, timestamp: Date.now() }],
              isDirty: true
            };
          case 'app/updateComponent':
            return {
              ...state,
              components: state.components.map(comp =>
                comp.id === action.payload.id ? { ...comp, ...action.payload } : comp
              ),
              history: [...state.history, { type: 'update', component: action.payload, timestamp: Date.now() }],
              isDirty: true
            };
          case 'app/deleteComponent':
            return {
              ...state,
              components: state.components.filter(comp => comp.id !== action.payload),
              history: [...state.history, { type: 'delete', componentId: action.payload, timestamp: Date.now() }],
              isDirty: true
            };
          case 'app/selectComponent':
            return { ...state, selectedComponent: action.payload };
          case 'app/clearHistory':
            return { ...state, history: [] };
          case 'app/undo':
            const lastAction = state.history[state.history.length - 1];
            if (!lastAction) return state;
            
            let newComponents = [...state.components];
            if (lastAction.type === 'add') {
              newComponents = newComponents.filter(comp => comp.id !== lastAction.component.id);
            } else if (lastAction.type === 'delete') {
              // In a real implementation, you'd restore the deleted component
            }
            
            return {
              ...state,
              components: newComponents,
              history: state.history.slice(0, -1)
            };
          case 'app/saveProject':
            return { ...state, isDirty: false, currentProject: action.payload };
          default:
            return state;
        }
      },
      ui: (state = {
        sidebarOpen: true,
        currentView: 'components',
        previewMode: false,
        loading: false,
        notifications: [],
        theme: 'light',
        ...preloadedState.ui
      }, action) => {
        switch (action.type) {
          case 'ui/toggleSidebar':
            return { ...state, sidebarOpen: !state.sidebarOpen };
          case 'ui/setCurrentView':
            return { ...state, currentView: action.payload };
          case 'ui/togglePreviewMode':
            return { ...state, previewMode: !state.previewMode };
          case 'ui/setLoading':
            return { ...state, loading: action.payload };
          case 'ui/addNotification':
            return { 
              ...state, 
              notifications: [...state.notifications, { ...action.payload, id: Date.now() }] 
            };
          case 'ui/removeNotification':
            return { 
              ...state, 
              notifications: state.notifications.filter(notif => notif.id !== action.payload) 
            };
          case 'ui/setTheme':
            return { ...state, theme: action.payload };
          default:
            return state;
        }
      },
      websocket: (state = {
        connected: false,
        connecting: false,
        messages: [],
        lastMessage: null,
        connectionAttempts: 0,
        ...preloadedState.websocket
      }, action) => {
        switch (action.type) {
          case 'websocket/connecting':
            return { ...state, connecting: true, connectionAttempts: state.connectionAttempts + 1 };
          case 'websocket/connected':
            return { ...state, connected: true, connecting: false };
          case 'websocket/disconnected':
            return { ...state, connected: false, connecting: false };
          case 'websocket/messageReceived':
            return { 
              ...state, 
              messages: [...state.messages, action.payload],
              lastMessage: action.payload
            };
          case 'websocket/clearMessages':
            return { ...state, messages: [] };
          default:
            return state;
        }
      },
      ai: (state = {
        suggestions: [],
        loading: false,
        history: [],
        enabled: true,
        ...preloadedState.ai
      }, action) => {
        switch (action.type) {
          case 'ai/setSuggestions':
            return { ...state, suggestions: action.payload };
          case 'ai/setLoading':
            return { ...state, loading: action.payload };
          case 'ai/addToHistory':
            return { ...state, history: [...state.history, action.payload] };
          case 'ai/toggleEnabled':
            return { ...state, enabled: !state.enabled };
          default:
            return state;
        }
      },
      collaboration: (state = {
        collaborators: [],
        comments: [],
        activeUsers: [],
        permissions: {},
        ...preloadedState.collaboration
      }, action) => {
        switch (action.type) {
          case 'collaboration/addCollaborator':
            return { 
              ...state, 
              collaborators: [...state.collaborators, action.payload] 
            };
          case 'collaboration/removeCollaborator':
            return { 
              ...state, 
              collaborators: state.collaborators.filter(collab => collab.id !== action.payload) 
            };
          case 'collaboration/addComment':
            return { 
              ...state, 
              comments: [...state.comments, { ...action.payload, id: Date.now() }] 
            };
          case 'collaboration/updateComment':
            return { 
              ...state, 
              comments: state.comments.map(comment =>
                comment.id === action.payload.id ? { ...comment, ...action.payload } : comment
              )
            };
          case 'collaboration/deleteComment':
            return { 
              ...state, 
              comments: state.comments.filter(comment => comment.id !== action.payload) 
            };
          case 'collaboration/setActiveUsers':
            return { ...state, activeUsers: action.payload };
          default:
            return state;
        }
      },
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['websocket/messageReceived'],
          ignoredPaths: ['websocket.lastMessage'],
        },
      }),
    preloadedState,
  });
};

describe('Redux Integration Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
  });

  describe('App State Management', () => {
    test('manages component lifecycle correctly', () => {
      // Add component
      const component = { id: '1', type: 'button', props: { text: 'Test Button' } };
      store.dispatch({ type: 'app/addComponent', payload: component });

      let state = store.getState();
      expect(state.app.components).toHaveLength(1);
      expect(state.app.components[0]).toEqual(component);
      expect(state.app.history).toHaveLength(1);
      expect(state.app.isDirty).toBe(true);

      // Update component
      const updatedComponent = { id: '1', props: { text: 'Updated Button', color: 'blue' } };
      store.dispatch({ type: 'app/updateComponent', payload: updatedComponent });

      state = store.getState();
      expect(state.app.components[0].props.text).toBe('Updated Button');
      expect(state.app.components[0].props.color).toBe('blue');
      expect(state.app.history).toHaveLength(2);

      // Delete component
      store.dispatch({ type: 'app/deleteComponent', payload: '1' });

      state = store.getState();
      expect(state.app.components).toHaveLength(0);
      expect(state.app.history).toHaveLength(3);
    });

    test('handles component selection', () => {
      const component = { id: '1', type: 'button', props: { text: 'Button' } };
      store.dispatch({ type: 'app/addComponent', payload: component });
      store.dispatch({ type: 'app/selectComponent', payload: '1' });

      const state = store.getState();
      expect(state.app.selectedComponent).toBe('1');
    });

    test('implements undo functionality', () => {
      // Add component
      const component = { id: '1', type: 'button', props: { text: 'Button' } };
      store.dispatch({ type: 'app/addComponent', payload: component });

      let state = store.getState();
      expect(state.app.components).toHaveLength(1);

      // Undo
      store.dispatch({ type: 'app/undo' });

      state = store.getState();
      expect(state.app.components).toHaveLength(0);
      expect(state.app.history).toHaveLength(0);
    });

    test('tracks dirty state correctly', () => {
      let state = store.getState();
      expect(state.app.isDirty).toBe(false);

      // Make changes
      store.dispatch({ 
        type: 'app/addComponent', 
        payload: { id: '1', type: 'button', props: { text: 'Button' } } 
      });

      state = store.getState();
      expect(state.app.isDirty).toBe(true);

      // Save project
      store.dispatch({ 
        type: 'app/saveProject', 
        payload: { id: 'project-1', name: 'My Project' } 
      });

      state = store.getState();
      expect(state.app.isDirty).toBe(false);
      expect(state.app.currentProject).toEqual({ id: 'project-1', name: 'My Project' });
    });
  });

  describe('UI State Management', () => {
    test('manages sidebar state', () => {
      let state = store.getState();
      expect(state.ui.sidebarOpen).toBe(true);

      store.dispatch({ type: 'ui/toggleSidebar' });

      state = store.getState();
      expect(state.ui.sidebarOpen).toBe(false);
    });

    test('manages current view', () => {
      store.dispatch({ type: 'ui/setCurrentView', payload: 'properties' });

      const state = store.getState();
      expect(state.ui.currentView).toBe('properties');
    });

    test('manages preview mode', () => {
      let state = store.getState();
      expect(state.ui.previewMode).toBe(false);

      store.dispatch({ type: 'ui/togglePreviewMode' });

      state = store.getState();
      expect(state.ui.previewMode).toBe(true);
    });

    test('manages notifications', () => {
      const notification = { type: 'success', message: 'Component added successfully' };
      store.dispatch({ type: 'ui/addNotification', payload: notification });

      let state = store.getState();
      expect(state.ui.notifications).toHaveLength(1);
      expect(state.ui.notifications[0].message).toBe('Component added successfully');

      const notificationId = state.ui.notifications[0].id;
      store.dispatch({ type: 'ui/removeNotification', payload: notificationId });

      state = store.getState();
      expect(state.ui.notifications).toHaveLength(0);
    });

    test('manages theme', () => {
      store.dispatch({ type: 'ui/setTheme', payload: 'dark' });

      const state = store.getState();
      expect(state.ui.theme).toBe('dark');
    });
  });

  describe('WebSocket State Management', () => {
    test('manages connection state', () => {
      // Start connecting
      store.dispatch({ type: 'websocket/connecting' });

      let state = store.getState();
      expect(state.websocket.connecting).toBe(true);
      expect(state.websocket.connectionAttempts).toBe(1);

      // Connected
      store.dispatch({ type: 'websocket/connected' });

      state = store.getState();
      expect(state.websocket.connected).toBe(true);
      expect(state.websocket.connecting).toBe(false);

      // Disconnected
      store.dispatch({ type: 'websocket/disconnected' });

      state = store.getState();
      expect(state.websocket.connected).toBe(false);
    });

    test('manages messages', () => {
      const message = { type: 'component_update', data: { id: '1', props: {} } };
      store.dispatch({ type: 'websocket/messageReceived', payload: message });

      let state = store.getState();
      expect(state.websocket.messages).toHaveLength(1);
      expect(state.websocket.lastMessage).toEqual(message);

      // Clear messages
      store.dispatch({ type: 'websocket/clearMessages' });

      state = store.getState();
      expect(state.websocket.messages).toHaveLength(0);
    });
  });

  describe('AI State Management', () => {
    test('manages AI suggestions', () => {
      const suggestions = [
        { id: '1', type: 'layout', title: 'Add Container' },
        { id: '2', type: 'styling', title: 'Improve Colors' }
      ];

      store.dispatch({ type: 'ai/setSuggestions', payload: suggestions });

      const state = store.getState();
      expect(state.ai.suggestions).toHaveLength(2);
      expect(state.ai.suggestions[0].title).toBe('Add Container');
    });

    test('manages loading state', () => {
      store.dispatch({ type: 'ai/setLoading', payload: true });

      let state = store.getState();
      expect(state.ai.loading).toBe(true);

      store.dispatch({ type: 'ai/setLoading', payload: false });

      state = store.getState();
      expect(state.ai.loading).toBe(false);
    });

    test('manages AI history', () => {
      const interaction = { 
        id: '1', 
        type: 'suggestion', 
        prompt: 'Improve layout',
        response: 'Add container',
        timestamp: Date.now()
      };

      store.dispatch({ type: 'ai/addToHistory', payload: interaction });

      const state = store.getState();
      expect(state.ai.history).toHaveLength(1);
      expect(state.ai.history[0].prompt).toBe('Improve layout');
    });

    test('toggles AI enabled state', () => {
      let state = store.getState();
      expect(state.ai.enabled).toBe(true);

      store.dispatch({ type: 'ai/toggleEnabled' });

      state = store.getState();
      expect(state.ai.enabled).toBe(false);
    });
  });

  describe('Collaboration State Management', () => {
    test('manages collaborators', () => {
      const collaborator = { id: '1', name: 'John Doe', role: 'editor' };
      store.dispatch({ type: 'collaboration/addCollaborator', payload: collaborator });

      let state = store.getState();
      expect(state.collaboration.collaborators).toHaveLength(1);
      expect(state.collaboration.collaborators[0].name).toBe('John Doe');

      // Remove collaborator
      store.dispatch({ type: 'collaboration/removeCollaborator', payload: '1' });

      state = store.getState();
      expect(state.collaboration.collaborators).toHaveLength(0);
    });

    test('manages comments', () => {
      const comment = { 
        text: 'This needs improvement', 
        author: 'John Doe',
        componentId: 'button-1',
        position: { x: 100, y: 200 }
      };

      store.dispatch({ type: 'collaboration/addComment', payload: comment });

      let state = store.getState();
      expect(state.collaboration.comments).toHaveLength(1);
      expect(state.collaboration.comments[0].text).toBe('This needs improvement');

      // Update comment
      const commentId = state.collaboration.comments[0].id;
      store.dispatch({ 
        type: 'collaboration/updateComment', 
        payload: { id: commentId, text: 'Updated comment' } 
      });

      state = store.getState();
      expect(state.collaboration.comments[0].text).toBe('Updated comment');

      // Delete comment
      store.dispatch({ type: 'collaboration/deleteComment', payload: commentId });

      state = store.getState();
      expect(state.collaboration.comments).toHaveLength(0);
    });

    test('manages active users', () => {
      const activeUsers = [
        { id: '1', name: 'John Doe', cursor: { x: 100, y: 200 } },
        { id: '2', name: 'Jane Smith', cursor: { x: 300, y: 150 } }
      ];

      store.dispatch({ type: 'collaboration/setActiveUsers', payload: activeUsers });

      const state = store.getState();
      expect(state.collaboration.activeUsers).toHaveLength(2);
      expect(state.collaboration.activeUsers[0].name).toBe('John Doe');
    });
  });

  describe('Cross-Slice Interactions', () => {
    test('handles complex state updates across multiple slices', () => {
      // Add component and show notification
      const component = { id: '1', type: 'button', props: { text: 'Button' } };
      store.dispatch({ type: 'app/addComponent', payload: component });
      store.dispatch({ 
        type: 'ui/addNotification', 
        payload: { type: 'success', message: 'Component added' } 
      });

      // Select component and change view
      store.dispatch({ type: 'app/selectComponent', payload: '1' });
      store.dispatch({ type: 'ui/setCurrentView', payload: 'properties' });

      // Add AI suggestion
      store.dispatch({ 
        type: 'ai/setSuggestions', 
        payload: [{ id: '1', type: 'styling', title: 'Improve button style' }] 
      });

      const state = store.getState();
      expect(state.app.components).toHaveLength(1);
      expect(state.app.selectedComponent).toBe('1');
      expect(state.ui.currentView).toBe('properties');
      expect(state.ui.notifications).toHaveLength(1);
      expect(state.ai.suggestions).toHaveLength(1);
    });

    test('maintains state consistency during rapid updates', () => {
      // Perform rapid state updates
      for (let i = 0; i < 10; i++) {
        store.dispatch({ 
          type: 'app/addComponent', 
          payload: { id: `comp-${i}`, type: 'button', props: { text: `Button ${i}` } } 
        });
        store.dispatch({ type: 'app/selectComponent', payload: `comp-${i}` });
      }

      const state = store.getState();
      expect(state.app.components).toHaveLength(10);
      expect(state.app.selectedComponent).toBe('comp-9');
      expect(state.app.history).toHaveLength(10);
    });
  });

  describe('State Persistence and Hydration', () => {
    test('handles state serialization', () => {
      // Add some state
      store.dispatch({ 
        type: 'app/addComponent', 
        payload: { id: '1', type: 'button', props: { text: 'Button' } } 
      });
      store.dispatch({ type: 'ui/setTheme', payload: 'dark' });

      const state = store.getState();
      
      // Serialize state
      const serializedState = JSON.stringify(state);
      expect(serializedState).toContain('Button');
      expect(serializedState).toContain('dark');

      // Deserialize and create new store
      const deserializedState = JSON.parse(serializedState);
      const newStore = createMockStore(deserializedState);

      const newState = newStore.getState();
      expect(newState.app.components).toHaveLength(1);
      expect(newState.ui.theme).toBe('dark');
    });
  });
});
