import React from 'react';
import { Layout, BackTop } from 'antd';
import { UpOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useEnhancedTheme } from '../../contexts/EnhancedThemeContext';
import EnhancedHeader from './EnhancedHeader';

const { Content, Footer } = Layout;

// Styled components
const StyledLayout = styled(Layout)`
  min-height: 100vh;
  background-color: var(--color-background);
  transition: background-color 0.3s ease;
`;

const StyledContent = styled(Content)`
  padding: 24px;
  background-color: var(--color-background);
  min-height: calc(100vh - 64px - 70px);
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    padding: 16px;
  }

  @media (max-width: 480px) {
    padding: 12px;
  }
`;

const StyledFooter = styled(Footer)`
  text-align: center;
  padding: 24px;
  background-color: var(--color-surface);
  border-top: 1px solid var(--color-border-light);
  color: var(--color-text-secondary);
  font-size: 14px;
  transition: all 0.3s ease;

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
    }
  }

  .footer-links {
    display: flex;
    gap: 24px;
    align-items: center;

    @media (max-width: 768px) {
      gap: 16px;
    }

    a {
      color: var(--color-text-secondary);
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: var(--color-primary);
      }
    }
  }

  .footer-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--color-text-tertiary);
    font-size: 12px;
  }
`;

const BackToTopButton = styled(BackTop)`
  .ant-back-top-content {
    background-color: var(--color-primary);
    color: white;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--color-primary-hover);
      transform: scale(1.1);
    }

    .anticon {
      font-size: 16px;
    }
  }
`;

const MainContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
`;

const ContentWrapper = styled.div`
  background-color: var(--color-background);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    border-radius: var(--border-radius-md);
  }
`;

const EnhancedLayout = ({ 
  children, 
  headerTitle,
  showStatus = true,
  onLogoClick,
  headerActions,
  footerLinks = [],
  showBackToTop = true 
}) => {
  const { isDarkMode } = useEnhancedTheme();

  const defaultFooterLinks = [
    { href: '/about', label: 'About' },
    { href: '/docs', label: 'Documentation' },
    { href: '/support', label: 'Support' },
    { href: '/privacy', label: 'Privacy' },
  ];

  const links = footerLinks.length > 0 ? footerLinks : defaultFooterLinks;

  return (
    <StyledLayout>
      <EnhancedHeader
        title={headerTitle}
        showStatus={showStatus}
        onLogoClick={onLogoClick}
      >
        {headerActions}
      </EnhancedHeader>

      <StyledContent id="main-content">
        <MainContainer>
          <ContentWrapper>
            {children}
          </ContentWrapper>
        </MainContainer>
      </StyledContent>

      <StyledFooter>
        <div className="footer-content">
          <div>
            App Builder ©{new Date().getFullYear()} - Build with ease
          </div>
          
          <div className="footer-links">
            {links.map((link, index) => (
              <a 
                key={index} 
                href={link.href} 
                target={link.external ? '_blank' : undefined}
                rel={link.external ? 'noopener noreferrer' : undefined}
              >
                {link.label}
              </a>
            ))}
          </div>

          <div className="footer-info">
            <span>Theme: {isDarkMode ? 'Dark' : 'Light'}</span>
          </div>
        </div>
      </StyledFooter>

      {showBackToTop && (
        <BackToTopButton>
          <div className="ant-back-top-content">
            <UpOutlined />
          </div>
        </BackToTopButton>
      )}
    </StyledLayout>
  );
};

export default EnhancedLayout;
