import { test, expect, devices } from '@playwright/test';

const APP_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';

// Browser configurations to test
const BROWSER_CONFIGS = [
  {
    name: 'Chrome Desktop',
    ...devices['Desktop Chrome'],
    browserName: 'chromium'
  },
  {
    name: 'Firefox Desktop',
    ...devices['Desktop Firefox'],
    browserName: 'firefox'
  },
  {
    name: 'Safari Desktop',
    ...devices['Desktop Safari'],
    browserName: 'webkit'
  },
  {
    name: 'Chrome Mobile',
    ...devices['Pixel 5'],
    browserName: 'chromium'
  },
  {
    name: 'Safari Mobile',
    ...devices['iPhone 12'],
    browserName: 'webkit'
  },
  {
    name: 'Edge Desktop',
    ...devices['Desktop Edge'],
    browserName: 'chromium'
  }
];

// Core features to test across browsers
const CORE_FEATURES = [
  'page-load',
  'navigation',
  'drag-and-drop',
  'form-interaction',
  'modal-dialogs',
  'responsive-design',
  'javascript-features',
  'css-features'
];

BROWSER_CONFIGS.forEach(config => {
  test.describe(`${config.name} Compatibility`, () => {
    test.use(config);

    test.beforeEach(async ({ page }) => {
      // Set up browser-specific configurations
      await page.goto(APP_URL);
      await page.waitForLoadState('networkidle');
    });

    test('page loads successfully', async ({ page }) => {
      // Check that the page loads without errors
      await expect(page).toHaveTitle(/App Builder/);
      
      // Check for JavaScript errors
      const errors = [];
      page.on('pageerror', error => errors.push(error.message));
      
      await page.waitForTimeout(2000);
      
      if (errors.length > 0) {
        console.warn(`${config.name} JavaScript errors:`, errors);
        // Allow some errors but fail if too many
        expect(errors.length).toBeLessThan(5);
      }
    });

    test('basic navigation works', async ({ page }) => {
      // Test navigation elements
      const navElements = [
        '[data-testid="nav-home"]',
        '[data-testid="nav-builder"]',
        '[data-testid="nav-templates"]',
        'nav a',
        '.navigation a'
      ];

      let workingNavigation = false;
      
      for (const selector of navElements) {
        const elements = await page.locator(selector).all();
        if (elements.length > 0) {
          const firstNav = elements[0];
          if (await firstNav.isVisible()) {
            await firstNav.click();
            await page.waitForTimeout(1000);
            workingNavigation = true;
            break;
          }
        }
      }

      // At least one navigation method should work
      expect(workingNavigation).toBeTruthy();
    });

    test('responsive design works correctly', async ({ page }) => {
      const viewport = page.viewportSize();
      
      if (viewport.width < 768) {
        // Mobile-specific tests
        await testMobileLayout(page);
      } else if (viewport.width < 1024) {
        // Tablet-specific tests
        await testTabletLayout(page);
      } else {
        // Desktop-specific tests
        await testDesktopLayout(page);
      }
    });

    test('CSS features are supported', async ({ page }) => {
      // Test CSS Grid support
      const gridSupport = await page.evaluate(() => {
        return CSS.supports('display', 'grid');
      });
      
      // Test Flexbox support
      const flexSupport = await page.evaluate(() => {
        return CSS.supports('display', 'flex');
      });
      
      // Test CSS Custom Properties
      const customPropsSupport = await page.evaluate(() => {
        return CSS.supports('color', 'var(--test)');
      });

      expect(gridSupport).toBeTruthy();
      expect(flexSupport).toBeTruthy();
      expect(customPropsSupport).toBeTruthy();
    });

    test('JavaScript features are supported', async ({ page }) => {
      const jsFeatures = await page.evaluate(() => {
        const features = {
          es6Classes: typeof class {} === 'function',
          arrowFunctions: (() => true)(),
          promises: typeof Promise !== 'undefined',
          asyncAwait: (async () => true)() instanceof Promise,
          destructuring: (() => { try { const [a] = [1]; return true; } catch { return false; } })(),
          templateLiterals: (() => { try { return `test` === 'test'; } catch { return false; } })(),
          modules: typeof import !== 'undefined',
          fetch: typeof fetch !== 'undefined',
          localStorage: typeof localStorage !== 'undefined',
          sessionStorage: typeof sessionStorage !== 'undefined',
          webSockets: typeof WebSocket !== 'undefined',
          intersectionObserver: typeof IntersectionObserver !== 'undefined',
          resizeObserver: typeof ResizeObserver !== 'undefined'
        };
        
        return features;
      });

      // Core features that should be supported
      expect(jsFeatures.es6Classes).toBeTruthy();
      expect(jsFeatures.arrowFunctions).toBeTruthy();
      expect(jsFeatures.promises).toBeTruthy();
      expect(jsFeatures.fetch).toBeTruthy();
      expect(jsFeatures.localStorage).toBeTruthy();

      // Log feature support for debugging
      console.log(`${config.name} JS Feature Support:`, jsFeatures);
    });

    test('form interactions work correctly', async ({ page }) => {
      // Look for form elements
      const formElements = await page.locator('input, select, textarea, button').all();
      
      if (formElements.length > 0) {
        // Test input interaction
        const inputs = await page.locator('input[type="text"], input[type="email"], textarea').all();
        
        for (const input of inputs.slice(0, 3)) { // Test first 3 inputs
          if (await input.isVisible() && await input.isEnabled()) {
            await input.click();
            await input.fill('Test input');
            
            const value = await input.inputValue();
            expect(value).toBe('Test input');
            
            await input.clear();
          }
        }

        // Test button interaction
        const buttons = await page.locator('button:not([disabled])').all();
        
        for (const button of buttons.slice(0, 2)) { // Test first 2 buttons
          if (await button.isVisible()) {
            await button.click();
            await page.waitForTimeout(500);
          }
        }
      }
    });

    test('modal dialogs work correctly', async ({ page }) => {
      // Look for modal triggers
      const modalTriggers = await page.locator(
        'button:has-text("open"), button:has-text("show"), [data-testid*="modal"], [data-testid*="dialog"]'
      ).all();

      for (const trigger of modalTriggers.slice(0, 2)) { // Test first 2 modals
        if (await trigger.isVisible()) {
          await trigger.click();
          await page.waitForTimeout(1000);

          // Check if modal appeared
          const modal = await page.locator('.modal, .dialog, [role="dialog"]').first();
          
          if (await modal.isVisible()) {
            // Test modal close
            const closeButtons = await page.locator(
              '.modal button:has-text("close"), .modal button:has-text("×"), [aria-label="close"]'
            ).all();
            
            if (closeButtons.length > 0) {
              await closeButtons[0].click();
              await page.waitForTimeout(500);
            } else {
              // Try Escape key
              await page.keyboard.press('Escape');
              await page.waitForTimeout(500);
            }
          }
        }
      }
    });

    test('drag and drop functionality', async ({ page }) => {
      // Skip drag and drop tests on mobile devices
      if (config.name.includes('Mobile')) {
        test.skip();
        return;
      }

      // Look for draggable elements
      const draggableElements = await page.locator('[draggable="true"], [data-testid*="draggable"]').all();
      const dropZones = await page.locator('[data-testid*="drop"], .drop-zone').all();

      if (draggableElements.length > 0 && dropZones.length > 0) {
        const draggable = draggableElements[0];
        const dropZone = dropZones[0];

        if (await draggable.isVisible() && await dropZone.isVisible()) {
          // Test drag and drop
          await draggable.dragTo(dropZone);
          await page.waitForTimeout(1000);

          // Verify drop was successful (implementation specific)
          // This would depend on your actual drag and drop implementation
        }
      }
    });

    test('WebSocket connectivity', async ({ page }) => {
      // Test WebSocket support and connectivity
      const wsSupport = await page.evaluate(() => {
        return typeof WebSocket !== 'undefined';
      });

      expect(wsSupport).toBeTruthy();

      // Test actual WebSocket connection if enabled
      const wsEnabled = process.env.REACT_APP_ENABLE_WEBSOCKET === 'true';
      
      if (wsEnabled) {
        const wsStatus = await page.evaluate(async () => {
          try {
            const ws = new WebSocket('ws://localhost:8000/ws/test/');
            
            return new Promise((resolve) => {
              const timeout = setTimeout(() => {
                ws.close();
                resolve({ connected: false, error: 'timeout' });
              }, 5000);

              ws.onopen = () => {
                clearTimeout(timeout);
                ws.close();
                resolve({ connected: true });
              };

              ws.onerror = (error) => {
                clearTimeout(timeout);
                resolve({ connected: false, error: error.message });
              };
            });
          } catch (error) {
            return { connected: false, error: error.message };
          }
        });

        // WebSocket connection is optional, so just log the result
        console.log(`${config.name} WebSocket status:`, wsStatus);
      }
    });

    test('local storage functionality', async ({ page }) => {
      const storageTest = await page.evaluate(() => {
        try {
          // Test localStorage
          localStorage.setItem('test', 'value');
          const retrieved = localStorage.getItem('test');
          localStorage.removeItem('test');
          
          // Test sessionStorage
          sessionStorage.setItem('test', 'value');
          const sessionRetrieved = sessionStorage.getItem('test');
          sessionStorage.removeItem('test');
          
          return {
            localStorage: retrieved === 'value',
            sessionStorage: sessionRetrieved === 'value'
          };
        } catch (error) {
          return {
            localStorage: false,
            sessionStorage: false,
            error: error.message
          };
        }
      });

      expect(storageTest.localStorage).toBeTruthy();
      expect(storageTest.sessionStorage).toBeTruthy();
    });

    test('performance is acceptable', async ({ page }) => {
      // Measure basic performance metrics
      const startTime = Date.now();
      
      await page.goto(APP_URL);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Performance thresholds by device type
      const maxLoadTime = config.name.includes('Mobile') ? 8000 : 5000; // 8s mobile, 5s desktop
      
      expect(loadTime).toBeLessThan(maxLoadTime);
      
      console.log(`${config.name} load time: ${loadTime}ms`);
    });

    test('accessibility features work', async ({ page }) => {
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      const focusedElement = await page.locator(':focus').first();
      
      if (await focusedElement.count() > 0) {
        expect(await focusedElement.isVisible()).toBeTruthy();
      }

      // Test screen reader attributes
      const ariaElements = await page.locator('[aria-label], [aria-labelledby], [role]').all();
      expect(ariaElements.length).toBeGreaterThan(0);
    });
  });
});

// Helper functions for responsive design tests
async function testMobileLayout(page) {
  // Test mobile-specific features
  const mobileMenu = await page.locator('.mobile-menu, .hamburger, [data-testid="mobile-nav"]').first();
  
  if (await mobileMenu.isVisible()) {
    await mobileMenu.click();
    await page.waitForTimeout(500);
  }

  // Check that content is properly sized for mobile
  const body = await page.locator('body').first();
  const bodyWidth = await body.evaluate(el => el.scrollWidth);
  const viewportWidth = page.viewportSize().width;
  
  // Content should not overflow horizontally
  expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 50); // Allow small tolerance
}

async function testTabletLayout(page) {
  // Test tablet-specific features
  const sidebarToggle = await page.locator('[data-testid="sidebar-toggle"], .sidebar-toggle').first();
  
  if (await sidebarToggle.isVisible()) {
    await sidebarToggle.click();
    await page.waitForTimeout(500);
  }
}

async function testDesktopLayout(page) {
  // Test desktop-specific features
  const sidebar = await page.locator('.sidebar, [data-testid="sidebar"]').first();
  
  if (await sidebar.isVisible()) {
    const sidebarWidth = await sidebar.evaluate(el => el.offsetWidth);
    expect(sidebarWidth).toBeGreaterThan(200); // Desktop sidebar should be substantial
  }
}
