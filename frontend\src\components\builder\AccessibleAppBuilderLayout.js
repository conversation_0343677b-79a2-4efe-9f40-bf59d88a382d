/**
 * Accessible App Builder Layout
 * 
 * A comprehensive layout component that integrates all UI/UX improvements
 * with full accessibility support, ensuring WCAG 2.1 AA compliance.
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Layout, Button, Tooltip, Alert, Space, Badge } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  EyeOutlined,
  KeyboardOutlined,
  AccessibilityOutlined,
  BulbOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme, a11yUtils } from '../../design-system';
import AccessibleComponent from '../common/AccessibleComponent';
import UXEnhancedComponentPalette from './UXEnhancedComponentPalette';
import UXEnhancedPropertyEditor from './UXEnhancedPropertyEditor';
import UXEnhancedPreviewArea from './UXEnhancedPreviewArea';
import a11yUtilsLib from '../../utils/accessibility';

const { Header, Sider, Content } = Layout;

// Enhanced styled components with accessibility
const AccessibleLayout = styled(Layout)`
  height: 100vh;
  background: ${theme.colors.background.default};
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    border: 2px solid;
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
`;

const AccessibleHeader = styled(Header)`
  background: ${theme.colors.background.paper};
  border-bottom: 1px solid ${theme.colors.border.light};
  padding: 0 ${theme.spacing[4]};
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: ${theme.shadows.sm};
  z-index: ${theme.zIndex.appHeader};
  
  /* Ensure header is accessible landmark */
  &[role="banner"] {
    position: relative;
  }
`;

const AccessibleSider = styled(Sider)`
  background: ${theme.colors.background.paper};
  border-right: 1px solid ${theme.colors.border.light};
  box-shadow: ${theme.shadows.sm};
  z-index: ${theme.zIndex.appSidebar};
  
  /* Ensure proper focus management */
  &:focus-within {
    box-shadow: ${theme.shadows.focus};
  }
  
  .ant-layout-sider-trigger {
    background: ${theme.colors.primary.main};
    color: ${theme.colors.primary.contrastText};
    border: none;
    
    &:hover {
      background: ${theme.colors.primary.dark};
    }
    
    &:focus {
      ${a11yUtils.focusRing()};
    }
  }
`;

const AccessibleContent = styled(Content)`
  background: ${theme.colors.background.secondary};
  overflow: hidden;
  position: relative;
  
  /* Ensure content is accessible landmark */
  &[role="main"] {
    outline: none;
  }
`;

const ToolbarSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
`;

const AccessibilityPanel = styled.div`
  position: fixed;
  top: 60px;
  right: ${theme.spacing[4]};
  background: ${theme.colors.background.paper};
  border: 1px solid ${theme.colors.border.light};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.lg};
  padding: ${theme.spacing[4]};
  z-index: ${theme.zIndex.modal};
  max-width: 300px;
  
  ${props => !props.visible && `
    display: none;
  `}
`;

const SkipNavigation = styled.nav`
  position: absolute;
  top: -100px;
  left: 0;
  z-index: ${theme.zIndex.skipLink};
  
  &:focus-within {
    top: 0;
  }
`;

const SkipLink = styled.a`
  position: absolute;
  top: ${theme.spacing[2]};
  left: ${theme.spacing[2]};
  background: ${theme.colors.primary.main};
  color: ${theme.colors.primary.contrastText};
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  border-radius: ${theme.borderRadius.md};
  text-decoration: none;
  font-weight: ${theme.typography.fontWeight.medium};
  
  &:focus {
    ${a11yUtils.focusRing()};
  }
`;

const LiveRegion = styled.div`
  ${theme.accessibility.srOnly};
`;

export default function AccessibleAppBuilderLayout({
  components = [],
  selectedComponent = null,
  onSelectComponent,
  onAddComponent,
  onUpdateComponent,
  onDeleteComponent,
  onMoveComponent,
  previewMode = false,
  websocketConnected = false,
  loading = false
}) {
  // State management
  const [leftSiderCollapsed, setLeftSiderCollapsed] = useState(false);
  const [rightSiderCollapsed, setRightSiderCollapsed] = useState(false);
  const [showAccessibilityPanel, setShowAccessibilityPanel] = useState(false);
  const [announcements, setAnnouncements] = useState([]);
  const [keyboardShortcutsEnabled, setKeyboardShortcutsEnabled] = useState(true);
  const [highContrastMode, setHighContrastMode] = useState(false);
  const [reducedMotionMode, setReducedMotionMode] = useState(false);

  // Refs for focus management
  const mainContentRef = useRef(null);
  const componentPaletteRef = useRef(null);
  const propertyEditorRef = useRef(null);
  const previewAreaRef = useRef(null);

  // Accessibility monitoring
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const cleanup1 = a11yUtilsLib.monitoring.monitorFocus(true);
      const cleanup2 = a11yUtilsLib.monitoring.monitorLiveRegions(true);
      
      return () => {
        cleanup1?.();
        cleanup2?.();
      };
    }
  }, []);

  // Detect user preferences
  useEffect(() => {
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    setHighContrastMode(highContrastQuery.matches);
    setReducedMotionMode(reducedMotionQuery.matches);
    
    const handleHighContrastChange = (e) => setHighContrastMode(e.matches);
    const handleReducedMotionChange = (e) => setReducedMotionMode(e.matches);
    
    highContrastQuery.addEventListener('change', handleHighContrastChange);
    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
    
    return () => {
      highContrastQuery.removeEventListener('change', handleHighContrastChange);
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
    };
  }, []);

  // Keyboard shortcuts
  const handleKeyboardShortcuts = useCallback((e) => {
    if (!keyboardShortcutsEnabled) return;
    
    // Alt + 1: Focus component palette
    if (e.altKey && e.key === '1') {
      e.preventDefault();
      componentPaletteRef.current?.focus();
      announce('Focused on component palette');
    }
    
    // Alt + 2: Focus preview area
    if (e.altKey && e.key === '2') {
      e.preventDefault();
      previewAreaRef.current?.focus();
      announce('Focused on preview area');
    }
    
    // Alt + 3: Focus property editor
    if (e.altKey && e.key === '3') {
      e.preventDefault();
      propertyEditorRef.current?.focus();
      announce('Focused on property editor');
    }
    
    // Alt + A: Toggle accessibility panel
    if (e.altKey && e.key.toLowerCase() === 'a') {
      e.preventDefault();
      setShowAccessibilityPanel(prev => !prev);
      announce(showAccessibilityPanel ? 'Closed accessibility panel' : 'Opened accessibility panel');
    }
    
    // Escape: Close panels
    if (e.key === 'Escape') {
      setShowAccessibilityPanel(false);
    }
  }, [keyboardShortcutsEnabled, showAccessibilityPanel]);

  // Announce function for screen readers
  const announce = useCallback((message, priority = 'polite') => {
    setAnnouncements(prev => [...prev, { message, priority, id: Date.now() }]);
    
    // Clear announcement after 3 seconds
    setTimeout(() => {
      setAnnouncements(prev => prev.filter(a => a.message !== message));
    }, 3000);
  }, []);

  // Handle component selection with accessibility
  const handleSelectComponent = useCallback((component) => {
    onSelectComponent(component);
    
    if (component) {
      announce(`Selected ${component.type} component`);
    } else {
      announce('Deselected component');
    }
  }, [onSelectComponent, announce]);

  // Handle component addition with accessibility
  const handleAddComponent = useCallback((componentType) => {
    onAddComponent(componentType);
    announce(`Added ${componentType} component to canvas`);
  }, [onAddComponent, announce]);

  // Handle component deletion with accessibility
  const handleDeleteComponent = useCallback((componentId) => {
    const component = components.find(c => c.id === componentId);
    onDeleteComponent(componentId);
    
    if (component) {
      announce(`Deleted ${component.type} component`);
    }
  }, [onDeleteComponent, components, announce]);

  // Set up keyboard event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyboardShortcuts);
    return () => document.removeEventListener('keydown', handleKeyboardShortcuts);
  }, [handleKeyboardShortcuts]);

  // Toggle sidebar functions
  const toggleLeftSider = useCallback(() => {
    setLeftSiderCollapsed(prev => !prev);
    announce(leftSiderCollapsed ? 'Expanded component palette' : 'Collapsed component palette');
  }, [leftSiderCollapsed, announce]);

  const toggleRightSider = useCallback(() => {
    setRightSiderCollapsed(prev => !prev);
    announce(rightSiderCollapsed ? 'Expanded property editor' : 'Collapsed property editor');
  }, [rightSiderCollapsed, announce]);

  return (
    <AccessibleLayout>
      {/* Skip Navigation */}
      <SkipNavigation>
        <SkipLink href="#main-content">Skip to main content</SkipLink>
        <SkipLink href="#component-palette">Skip to component palette</SkipLink>
        <SkipLink href="#property-editor">Skip to property editor</SkipLink>
      </SkipNavigation>

      {/* Header */}
      <AccessibleComponent
        role="banner"
        ariaLabel="App Builder header with navigation and tools"
      >
        <AccessibleHeader>
          <ToolbarSection>
            <AccessibleComponent
              interactive
              onClick={toggleLeftSider}
              ariaLabel={leftSiderCollapsed ? 'Expand component palette' : 'Collapse component palette'}
              ariaExpanded={!leftSiderCollapsed}
              ariaControls="component-palette"
            >
              <Button
                type="text"
                icon={leftSiderCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                size="large"
              />
            </AccessibleComponent>
            
            <h1 style={{ margin: 0, fontSize: theme.typography.fontSize.xl, fontWeight: theme.typography.fontWeight.semibold }}>
              App Builder
            </h1>
          </ToolbarSection>

          <ToolbarSection>
            <Tooltip title="Keyboard shortcuts (Alt+A)">
              <AccessibleComponent
                interactive
                onClick={() => setShowAccessibilityPanel(prev => !prev)}
                ariaLabel="Toggle accessibility panel"
                ariaExpanded={showAccessibilityPanel}
              >
                <Button
                  type="text"
                  icon={<AccessibilityOutlined />}
                  size="large"
                />
              </AccessibleComponent>
            </Tooltip>

            <Badge dot={websocketConnected} color="green">
              <Tooltip title="Preview mode">
                <AccessibleComponent
                  interactive
                  ariaLabel="Toggle preview mode"
                  ariaPressed={previewMode}
                >
                  <Button
                    type={previewMode ? 'primary' : 'text'}
                    icon={<EyeOutlined />}
                    size="large"
                  />
                </AccessibleComponent>
              </Tooltip>
            </Badge>
          </ToolbarSection>
        </AccessibleHeader>
      </AccessibleComponent>

      <Layout>
        {/* Left Sidebar - Component Palette */}
        <AccessibleSider
          id="component-palette"
          width={320}
          collapsed={leftSiderCollapsed}
          collapsible
          trigger={null}
          theme="light"
        >
          <AccessibleComponent
            ref={componentPaletteRef}
            role="complementary"
            ariaLabel="Component palette for adding components to the canvas"
            tabIndex={-1}
          >
            <UXEnhancedComponentPalette
              onAddComponent={handleAddComponent}
              selectedComponent={selectedComponent}
              loading={loading}
            />
          </AccessibleComponent>
        </AccessibleSider>

        {/* Main Content Area */}
        <AccessibleContent
          id="main-content"
          role="main"
          tabIndex={-1}
          ref={mainContentRef}
        >
          <AccessibleComponent
            ref={previewAreaRef}
            ariaLabel="Canvas area for building your application"
            tabIndex={-1}
          >
            <UXEnhancedPreviewArea
              components={components}
              selectedComponentId={selectedComponent?.id}
              onSelectComponent={handleSelectComponent}
              onDeleteComponent={handleDeleteComponent}
              onUpdateComponent={onUpdateComponent}
              onMoveComponent={onMoveComponent}
              previewMode={previewMode}
              websocketConnected={websocketConnected}
              loading={loading}
            />
          </AccessibleComponent>
        </AccessibleContent>

        {/* Right Sidebar - Property Editor */}
        <AccessibleSider
          id="property-editor"
          width={320}
          collapsed={rightSiderCollapsed}
          collapsible
          trigger={null}
          theme="light"
          reverseArrow
        >
          <AccessibleComponent
            ref={propertyEditorRef}
            role="complementary"
            ariaLabel="Property editor for configuring selected component"
            tabIndex={-1}
          >
            <UXEnhancedPropertyEditor
              component={selectedComponent}
              onUpdateComponent={onUpdateComponent}
              loading={loading}
            />
          </AccessibleComponent>
        </AccessibleSider>
      </Layout>

      {/* Accessibility Panel */}
      {showAccessibilityPanel && (
        <AccessibleComponent
          role="dialog"
          ariaLabel="Accessibility settings and information"
          ariaModal={true}
          focusTrap={true}
        >
          <AccessibilityPanel visible={showAccessibilityPanel}>
            <h3 style={{ marginTop: 0 }}>Accessibility Settings</h3>
            
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <strong>Keyboard Shortcuts:</strong>
                <ul style={{ fontSize: theme.typography.fontSize.sm, marginTop: theme.spacing[1] }}>
                  <li>Alt + 1: Focus component palette</li>
                  <li>Alt + 2: Focus preview area</li>
                  <li>Alt + 3: Focus property editor</li>
                  <li>Alt + A: Toggle this panel</li>
                  <li>Escape: Close panels</li>
                </ul>
              </div>
              
              <div>
                <strong>Current Settings:</strong>
                <ul style={{ fontSize: theme.typography.fontSize.sm, marginTop: theme.spacing[1] }}>
                  <li>High Contrast: {highContrastMode ? 'Enabled' : 'Disabled'}</li>
                  <li>Reduced Motion: {reducedMotionMode ? 'Enabled' : 'Disabled'}</li>
                  <li>Screen Reader: {navigator.userAgent.includes('NVDA') || navigator.userAgent.includes('JAWS') ? 'Detected' : 'Not detected'}</li>
                </ul>
              </div>
              
              <Button
                onClick={() => setShowAccessibilityPanel(false)}
                type="primary"
                block
              >
                Close Panel
              </Button>
            </Space>
          </AccessibilityPanel>
        </AccessibleComponent>
      )}

      {/* Live Region for Announcements */}
      <LiveRegion aria-live="polite" aria-atomic="true">
        {announcements.map(announcement => (
          <div key={announcement.id}>{announcement.message}</div>
        ))}
      </LiveRegion>

      {/* Development Accessibility Alerts */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ position: 'fixed', bottom: theme.spacing[4], left: theme.spacing[4], zIndex: theme.zIndex.toast }}>
          {highContrastMode && (
            <Alert
              message="High Contrast Mode Detected"
              type="info"
              showIcon
              style={{ marginBottom: theme.spacing[2] }}
            />
          )}
          {reducedMotionMode && (
            <Alert
              message="Reduced Motion Mode Detected"
              type="info"
              showIcon
            />
          )}
        </div>
      )}
    </AccessibleLayout>
  );
}
