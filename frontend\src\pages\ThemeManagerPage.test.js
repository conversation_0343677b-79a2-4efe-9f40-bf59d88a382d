import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import ThemeManagerPage from './ThemeManagerPage';

// Mock the ThemeManagerV2 component
jest.mock('../components/ThemeManagerV2', () => {
  return function MockThemeManagerV2() {
    return <div data-testid="theme-manager-v2">Theme Manager V2 Component</div>;
  };
});

// Create mock store
const mockStore = configureStore([]);

describe('ThemeManagerPage', () => {
  let store;

  beforeEach(() => {
    store = mockStore({
      themes: {
        themes: [],
        activeTheme: 'default'
      }
    });
  });

  it('renders the page title', () => {
    render(
      <Provider store={store}>
        <ThemeManagerPage />
      </Provider>
    );

    expect(screen.getByText('Theme Manager')).toBeInTheDocument();
  });

  it('renders the ThemeManagerV2 component', () => {
    render(
      <Provider store={store}>
        <ThemeManagerPage />
      </Provider>
    );

    expect(screen.getByTestId('theme-manager-v2')).toBeInTheDocument();
  });

  it('renders the breadcrumb navigation', () => {
    render(
      <Provider store={store}>
        <ThemeManagerPage />
      </Provider>
    );

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Theme Manager')).toBeInTheDocument();
  });

  it('renders the description text', () => {
    render(
      <Provider store={store}>
        <ThemeManagerPage />
      </Provider>
    );

    expect(screen.getByText(/Customize the look and feel/i)).toBeInTheDocument();
  });

  it('renders the alert message', () => {
    render(
      <Provider store={store}>
        <ThemeManagerPage />
      </Provider>
    );

    expect(screen.getByText(/Theme Management/i)).toBeInTheDocument();
    expect(screen.getByText(/Changes made here will affect/i)).toBeInTheDocument();
  });
});
