/**
 * Tutorial Progress Tracker
 * 
 * Enhanced progress tracking and completion rewards system for tutorials.
 * Provides detailed analytics, achievements, and motivational feedback.
 */

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Card, 
  Progress, 
  Typography, 
  Space, 
  Button, 
  Badge, 
  Timeline,
  Statistic,
  Row,
  Col,
  Divider,
  Tag,
  Avatar,
  notification
} from 'antd';
import {
  TrophyOutlined,
  StarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  FireOutlined,
  GiftOutlined,
  RocketOutlined,
  BulbOutlined,
  HeartOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import styled, { keyframes } from 'styled-components';
import confetti from 'canvas-confetti';

const { Title, Text, Paragraph } = Typography;

// Animations
const celebrationPulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
`;

const sparkle = keyframes`
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
`;

// Styled Components
const CelebrationCard = styled(Card)`
  background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  border: none;
  border-radius: 16px;
  overflow: hidden;
  
  .ant-card-body {
    padding: 32px;
    text-align: center;
    color: white;
  }
  
  .celebration-icon {
    font-size: 64px;
    animation: ${celebrationPulse} 2s infinite;
    margin-bottom: 16px;
  }
`;

const AchievementBadge = styled.div`
  position: relative;
  display: inline-block;
  
  .sparkle {
    position: absolute;
    top: -5px;
    right: -5px;
    color: #faad14;
    animation: ${sparkle} 2s infinite;
  }
`;

const ProgressCard = styled(Card)`
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
`;

// Achievement definitions
const ACHIEVEMENTS = {
  first_steps: {
    id: 'first_steps',
    title: 'First Steps',
    description: 'Started your first tutorial',
    icon: <RocketOutlined />,
    color: '#1890ff',
    points: 10
  },
  component_master: {
    id: 'component_master',
    title: 'Component Master',
    description: 'Added your first component',
    icon: <BulbOutlined />,
    color: '#52c41a',
    points: 25
  },
  property_editor: {
    id: 'property_editor',
    title: 'Property Editor',
    description: 'Customized component properties',
    icon: <SettingOutlined />,
    color: '#722ed1',
    points: 20
  },
  preview_pro: {
    id: 'preview_pro',
    title: 'Preview Pro',
    description: 'Used preview mode',
    icon: <EyeOutlined />,
    color: '#fa8c16',
    points: 15
  },
  tutorial_complete: {
    id: 'tutorial_complete',
    title: 'Tutorial Graduate',
    description: 'Completed the onboarding tutorial',
    icon: <TrophyOutlined />,
    color: '#faad14',
    points: 100
  },
  speed_learner: {
    id: 'speed_learner',
    title: 'Speed Learner',
    description: 'Completed tutorial in under 5 minutes',
    icon: <ThunderboltOutlined />,
    color: '#eb2f96',
    points: 50
  }
};

// Progress Tracking Component
export const TutorialProgressTracker = ({ 
  visible, 
  onClose, 
  tutorialId, 
  progress,
  achievements = [],
  timeSpent = 0
}) => {
  const [showCelebration, setShowCelebration] = useState(false);
  const [newAchievements, setNewAchievements] = useState([]);

  useEffect(() => {
    if (visible && achievements.length > 0) {
      // Check for new achievements
      const stored = JSON.parse(localStorage.getItem('tutorial_achievements') || '[]');
      const newOnes = achievements.filter(id => !stored.includes(id));
      
      if (newOnes.length > 0) {
        setNewAchievements(newOnes);
        localStorage.setItem('tutorial_achievements', JSON.stringify(achievements));
      }
    }
  }, [visible, achievements]);

  const totalPoints = achievements.reduce((sum, id) => {
    return sum + (ACHIEVEMENTS[id]?.points || 0);
  }, 0);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  return (
    <Modal
      title={
        <Space>
          <TrophyOutlined style={{ color: '#faad14' }} />
          Tutorial Progress
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Overall Progress */}
        <ProgressCard>
          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title="Progress"
                value={progress?.percentage || 0}
                suffix="%"
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="Time Spent"
                value={formatTime(timeSpent)}
                prefix={<ClockCircleOutlined />}
              />
            </Col>
          </Row>
          
          <Divider />
          
          <Progress
            percent={progress?.percentage || 0}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            showInfo={false}
          />
          
          <Text type="secondary">
            Step {progress?.currentStep || 0} of {progress?.totalSteps || 0}
          </Text>
        </ProgressCard>

        {/* Achievements */}
        <Card title="Achievements" extra={<Badge count={achievements.length} />}>
          <Row gutter={[16, 16]}>
            {Object.values(ACHIEVEMENTS).map(achievement => {
              const earned = achievements.includes(achievement.id);
              const isNew = newAchievements.includes(achievement.id);
              
              return (
                <Col span={12} key={achievement.id}>
                  <Card
                    size="small"
                    style={{
                      opacity: earned ? 1 : 0.5,
                      border: earned ? `2px solid ${achievement.color}` : '1px solid #d9d9d9'
                    }}
                  >
                    <Space>
                      <AchievementBadge>
                        <Avatar
                          icon={achievement.icon}
                          style={{ 
                            backgroundColor: earned ? achievement.color : '#d9d9d9',
                            color: 'white'
                          }}
                        />
                        {isNew && <StarOutlined className="sparkle" />}
                      </AchievementBadge>
                      
                      <div>
                        <Text strong style={{ color: earned ? achievement.color : '#999' }}>
                          {achievement.title}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {achievement.description}
                        </Text>
                        <br />
                        <Tag color={earned ? achievement.color : 'default'} size="small">
                          {achievement.points} pts
                        </Tag>
                      </div>
                    </Space>
                  </Card>
                </Col>
              );
            })}
          </Row>
          
          <Divider />
          
          <div style={{ textAlign: 'center' }}>
            <Statistic
              title="Total Points Earned"
              value={totalPoints}
              prefix={<StarOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </div>
        </Card>

        {/* Tutorial Timeline */}
        {progress?.steps && (
          <Card title="Tutorial Timeline">
            <Timeline>
              {progress.steps.map((step, index) => (
                <Timeline.Item
                  key={index}
                  color={step.completed ? 'green' : index === progress.currentStep ? 'blue' : 'gray'}
                  dot={step.completed ? <CheckCircleOutlined /> : undefined}
                >
                  <Text strong={index === progress.currentStep}>
                    {step.title}
                  </Text>
                  {step.completed && (
                    <Text type="secondary" style={{ marginLeft: '8px' }}>
                      ✓ Completed
                    </Text>
                  )}
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        )}
      </Space>
    </Modal>
  );
};

// Completion Celebration Component
export const TutorialCompletionCelebration = ({ 
  visible, 
  onClose, 
  tutorialTitle,
  achievements = [],
  timeSpent = 0,
  pointsEarned = 0
}) => {
  useEffect(() => {
    if (visible) {
      // Trigger confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });

      // Show achievement notifications
      achievements.forEach((achievementId, index) => {
        const achievement = ACHIEVEMENTS[achievementId];
        if (achievement) {
          setTimeout(() => {
            notification.success({
              message: 'Achievement Unlocked!',
              description: `${achievement.title}: ${achievement.description}`,
              icon: <TrophyOutlined style={{ color: achievement.color }} />,
              duration: 4,
              placement: 'topRight'
            });
          }, index * 1000);
        }
      });
    }
  }, [visible, achievements]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      centered
      closable={false}
    >
      <CelebrationCard>
        <div className="celebration-icon">
          🎉
        </div>
        
        <Title level={2} style={{ color: 'white', margin: '0 0 16px 0' }}>
          Congratulations!
        </Title>
        
        <Paragraph style={{ color: 'white', fontSize: '16px' }}>
          You've successfully completed the <strong>{tutorialTitle}</strong>!
        </Paragraph>
        
        <Row gutter={16} style={{ margin: '24px 0' }}>
          <Col span={8}>
            <Statistic
              title="Time"
              value={formatTime(timeSpent)}
              valueStyle={{ color: 'white' }}
              prefix={<ClockCircleOutlined style={{ color: 'white' }} />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Points"
              value={pointsEarned}
              valueStyle={{ color: 'white' }}
              prefix={<StarOutlined style={{ color: 'white' }} />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Achievements"
              value={achievements.length}
              valueStyle={{ color: 'white' }}
              prefix={<TrophyOutlined style={{ color: 'white' }} />}
            />
          </Col>
        </Row>
        
        <Space>
          <Button
            type="primary"
            size="large"
            icon={<RocketOutlined />}
            onClick={onClose}
            style={{
              background: 'white',
              borderColor: 'white',
              color: '#19547b'
            }}
          >
            Start Building!
          </Button>
          
          <Button
            type="text"
            size="large"
            icon={<BookOutlined />}
            style={{ color: 'white', borderColor: 'white' }}
          >
            More Tutorials
          </Button>
        </Space>
      </CelebrationCard>
    </Modal>
  );
};

export default {
  TutorialProgressTracker,
  TutorialCompletionCelebration,
  ACHIEVEMENTS
};
