import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { register } from '../../utils/auth';

/**
 * Register Component
 * 
 * This component provides a registration form for new users.
 */
const Register = ({ onSuccess }) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [agreeTerms, setAgreeTerms] = useState(false);
  
  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Validate form
  const validateForm = () => {
    // Check if all fields are filled
    if (!formData.username || !formData.email || !formData.password || !formData.confirmPassword) {
      setError('Please fill in all fields');
      return false;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }
    
    // Check if passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    
    // Check password strength
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }
    
    // Check if terms are agreed
    if (!agreeTerms) {
      setError('You must agree to the terms and conditions');
      return false;
    }
    
    return true;
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!validateForm()) {
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Attempt registration
      const result = await register({
        username: formData.username,
        email: formData.email,
        password: formData.password
      });
      
      if (result.success) {
        // Call success callback if provided
        if (onSuccess) {
          onSuccess(result.user);
        }
        
        // Redirect to login page
        navigate('/login', { state: { registered: true } });
      } else {
        setError(result.error || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="register-container">
      <div className="register-card">
        <div className="register-header">
          <h2>Create an Account</h2>
          <p>Join us today and start building amazing apps!</p>
        </div>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="register-form">
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              placeholder="Choose a username"
              disabled={loading}
              autoComplete="username"
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              disabled={loading}
              autoComplete="email"
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Create a password"
              disabled={loading}
              autoComplete="new-password"
              required
            />
            <div className="password-requirements">
              Password must be at least 8 characters long
            </div>
          </div>
          
          <div className="form-group">
            <label htmlFor="confirmPassword">Confirm Password</label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="Confirm your password"
              disabled={loading}
              autoComplete="new-password"
              required
            />
          </div>
          
          <div className="form-options">
            <div className="agree-terms">
              <input
                type="checkbox"
                id="agree-terms"
                checked={agreeTerms}
                onChange={(e) => setAgreeTerms(e.target.checked)}
                disabled={loading}
                required
              />
              <label htmlFor="agree-terms">
                I agree to the{' '}
                <Link to="/terms" target="_blank">Terms and Conditions</Link>
              </label>
            </div>
          </div>
          
          <button
            type="submit"
            className="register-button"
            disabled={loading}
          >
            {loading ? 'Creating Account...' : 'Create Account'}
          </button>
        </form>
        
        <div className="register-footer">
          <p>
            Already have an account?{' '}
            <Link to="/login">Login</Link>
          </p>
        </div>
      </div>
      
      <style jsx>{`
        .register-container {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          padding: var(--spacing-md);
          background-color: var(--color-background);
        }
        
        .register-card {
          width: 100%;
          max-width: 500px;
          padding: var(--spacing-lg);
          background-color: var(--color-surface);
          border-radius: var(--border-radius-lg);
          box-shadow: var(--shadow-md);
        }
        
        .register-header {
          margin-bottom: var(--spacing-lg);
          text-align: center;
        }
        
        .register-header h2 {
          margin-bottom: var(--spacing-xs);
          color: var(--color-text);
        }
        
        .register-header p {
          color: var(--color-textSecondary);
        }
        
        .error-message {
          padding: var(--spacing-sm);
          margin-bottom: var(--spacing-md);
          background-color: rgba(var(--color-error-rgb), 0.1);
          border: 1px solid var(--color-error);
          border-radius: var(--border-radius-md);
          color: var(--color-error);
          font-size: var(--font-size-sm);
        }
        
        .register-form {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);
        }
        
        .form-group {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-xs);
        }
        
        .form-group label {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .form-group input {
          padding: var(--spacing-sm) var(--spacing-md);
          border: 1px solid var(--color-border);
          border-radius: var(--border-radius-md);
          background-color: var(--color-background);
          color: var(--color-text);
          font-size: var(--font-size-md);
          transition: border-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .form-group input:focus {
          outline: none;
          border-color: var(--color-primary);
        }
        
        .password-requirements {
          font-size: var(--font-size-xs);
          color: var(--color-textSecondary);
          margin-top: var(--spacing-xs);
        }
        
        .form-options {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);
          font-size: var(--font-size-sm);
        }
        
        .agree-terms {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
        }
        
        .agree-terms a {
          color: var(--color-primary);
          text-decoration: none;
        }
        
        .agree-terms a:hover {
          text-decoration: underline;
        }
        
        .register-button {
          padding: var(--spacing-sm) var(--spacing-md);
          background-color: var(--color-primary);
          color: white;
          border: none;
          border-radius: var(--border-radius-md);
          font-size: var(--font-size-md);
          font-weight: var(--font-weight-medium);
          cursor: pointer;
          transition: background-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .register-button:hover {
          background-color: color-mix(in srgb, var(--color-primary) 80%, black);
        }
        
        .register-button:disabled {
          background-color: var(--color-border);
          color: var(--color-textSecondary);
          cursor: not-allowed;
        }
        
        .register-footer {
          margin-top: var(--spacing-lg);
          text-align: center;
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .register-footer a {
          color: var(--color-primary);
          text-decoration: none;
        }
        
        .register-footer a:hover {
          text-decoration: underline;
        }
      `}</style>
    </div>
  );
};

export default Register;
