/**
 * Tutorial Localization System
 * 
 * Provides internationalization (i18n) support for the tutorial system
 * including multi-language content, RTL support, and locale-specific formatting.
 */

import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { ConfigProvider } from 'antd';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import esES from 'antd/locale/es_ES';
import frFR from 'antd/locale/fr_FR';
import deDE from 'antd/locale/de_DE';
import jaJP from 'antd/locale/ja_JP';
import arEG from 'antd/locale/ar_EG';
import heIL from 'antd/locale/he_IL';

// Supported languages
export const SUPPORTED_LANGUAGES = {
  'en-US': { name: 'English', nativeName: 'English', rtl: false, antdLocale: enUS },
  'zh-CN': { name: 'Chinese (Simplified)', nativeName: '简体中文', rtl: false, antdLocale: zhCN },
  'es-ES': { name: 'Spanish', nativeName: 'Español', rtl: false, antdLocale: esES },
  'fr-FR': { name: 'French', nativeName: 'Français', rtl: false, antdLocale: frFR },
  'de-DE': { name: 'German', nativeName: 'Deutsch', rtl: false, antdLocale: deDE },
  'ja-JP': { name: 'Japanese', nativeName: '日本語', rtl: false, antdLocale: jaJP },
  'ar-EG': { name: 'Arabic', nativeName: 'العربية', rtl: true, antdLocale: arEG },
  'he-IL': { name: 'Hebrew', nativeName: 'עברית', rtl: true, antdLocale: heIL }
};

// Default translations
const DEFAULT_TRANSLATIONS = {
  'en-US': {
    // Tutorial System
    'tutorial.start': 'Start Tutorial',
    'tutorial.next': 'Next',
    'tutorial.previous': 'Previous',
    'tutorial.skip': 'Skip',
    'tutorial.complete': 'Complete',
    'tutorial.pause': 'Pause',
    'tutorial.resume': 'Resume',
    'tutorial.exit': 'Exit Tutorial',
    'tutorial.progress': 'Progress',
    'tutorial.step': 'Step {current} of {total}',
    
    // Tutorial Launcher
    'launcher.title': 'Tutorial Center',
    'launcher.browse': 'Browse Tutorials',
    'launcher.recommended': 'Recommended',
    'launcher.search': 'Search tutorials...',
    'launcher.category': 'Category',
    'launcher.difficulty': 'Difficulty',
    'launcher.duration': 'Duration',
    'launcher.prerequisites': 'Prerequisites',
    
    // Tutorial Progress
    'progress.title': 'Your Learning Progress',
    'progress.completed': 'Completed',
    'progress.timeSpent': 'Time Spent',
    'progress.badges': 'Badges Earned',
    'progress.overall': 'Overall Progress',
    
    // Tutorial Categories
    'category.beginner': 'Beginner',
    'category.intermediate': 'Intermediate',
    'category.advanced': 'Advanced',
    'category.feature_specific': 'Feature Specific',
    
    // Help Context
    'help.componentPalette': 'Component Palette',
    'help.previewArea': 'Preview Area',
    'help.propertyEditor': 'Property Editor',
    'help.dragDrop': 'Drag & Drop',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.close': 'Close'
  },
  
  'zh-CN': {
    'tutorial.start': '开始教程',
    'tutorial.next': '下一步',
    'tutorial.previous': '上一步',
    'tutorial.skip': '跳过',
    'tutorial.complete': '完成',
    'tutorial.pause': '暂停',
    'tutorial.resume': '继续',
    'tutorial.exit': '退出教程',
    'tutorial.progress': '进度',
    'tutorial.step': '第 {current} 步，共 {total} 步',
    
    'launcher.title': '教程中心',
    'launcher.browse': '浏览教程',
    'launcher.recommended': '推荐',
    'launcher.search': '搜索教程...',
    'launcher.category': '类别',
    'launcher.difficulty': '难度',
    'launcher.duration': '时长',
    'launcher.prerequisites': '前置要求',
    
    'progress.title': '您的学习进度',
    'progress.completed': '已完成',
    'progress.timeSpent': '花费时间',
    'progress.badges': '获得徽章',
    'progress.overall': '总体进度',
    
    'category.beginner': '初级',
    'category.intermediate': '中级',
    'category.advanced': '高级',
    'category.feature_specific': '功能特定',
    
    'help.componentPalette': '组件面板',
    'help.previewArea': '预览区域',
    'help.propertyEditor': '属性编辑器',
    'help.dragDrop': '拖拽',
    
    'common.loading': '加载中...',
    'common.error': '错误',
    'common.success': '成功',
    'common.warning': '警告',
    'common.cancel': '取消',
    'common.save': '保存',
    'common.delete': '删除',
    'common.edit': '编辑',
    'common.close': '关闭'
  },
  
  'es-ES': {
    'tutorial.start': 'Iniciar Tutorial',
    'tutorial.next': 'Siguiente',
    'tutorial.previous': 'Anterior',
    'tutorial.skip': 'Omitir',
    'tutorial.complete': 'Completar',
    'tutorial.pause': 'Pausar',
    'tutorial.resume': 'Reanudar',
    'tutorial.exit': 'Salir del Tutorial',
    'tutorial.progress': 'Progreso',
    'tutorial.step': 'Paso {current} de {total}',
    
    'launcher.title': 'Centro de Tutoriales',
    'launcher.browse': 'Explorar Tutoriales',
    'launcher.recommended': 'Recomendado',
    'launcher.search': 'Buscar tutoriales...',
    'launcher.category': 'Categoría',
    'launcher.difficulty': 'Dificultad',
    'launcher.duration': 'Duración',
    'launcher.prerequisites': 'Prerrequisitos',
    
    'progress.title': 'Tu Progreso de Aprendizaje',
    'progress.completed': 'Completado',
    'progress.timeSpent': 'Tiempo Empleado',
    'progress.badges': 'Insignias Obtenidas',
    'progress.overall': 'Progreso General',
    
    'category.beginner': 'Principiante',
    'category.intermediate': 'Intermedio',
    'category.advanced': 'Avanzado',
    'category.feature_specific': 'Específico de Función',
    
    'help.componentPalette': 'Paleta de Componentes',
    'help.previewArea': 'Área de Vista Previa',
    'help.propertyEditor': 'Editor de Propiedades',
    'help.dragDrop': 'Arrastrar y Soltar',
    
    'common.loading': 'Cargando...',
    'common.error': 'Error',
    'common.success': 'Éxito',
    'common.warning': 'Advertencia',
    'common.cancel': 'Cancelar',
    'common.save': 'Guardar',
    'common.delete': 'Eliminar',
    'common.edit': 'Editar',
    'common.close': 'Cerrar'
  },
  
  'ar-EG': {
    'tutorial.start': 'بدء الدرس',
    'tutorial.next': 'التالي',
    'tutorial.previous': 'السابق',
    'tutorial.skip': 'تخطي',
    'tutorial.complete': 'إكمال',
    'tutorial.pause': 'إيقاف مؤقت',
    'tutorial.resume': 'استئناف',
    'tutorial.exit': 'الخروج من الدرس',
    'tutorial.progress': 'التقدم',
    'tutorial.step': 'الخطوة {current} من {total}',
    
    'launcher.title': 'مركز الدروس',
    'launcher.browse': 'تصفح الدروس',
    'launcher.recommended': 'موصى به',
    'launcher.search': 'البحث في الدروس...',
    'launcher.category': 'الفئة',
    'launcher.difficulty': 'الصعوبة',
    'launcher.duration': 'المدة',
    'launcher.prerequisites': 'المتطلبات المسبقة',
    
    'progress.title': 'تقدم التعلم الخاص بك',
    'progress.completed': 'مكتمل',
    'progress.timeSpent': 'الوقت المستغرق',
    'progress.badges': 'الشارات المكتسبة',
    'progress.overall': 'التقدم العام',
    
    'category.beginner': 'مبتدئ',
    'category.intermediate': 'متوسط',
    'category.advanced': 'متقدم',
    'category.feature_specific': 'خاص بالميزة',
    
    'help.componentPalette': 'لوحة المكونات',
    'help.previewArea': 'منطقة المعاينة',
    'help.propertyEditor': 'محرر الخصائص',
    'help.dragDrop': 'السحب والإفلات',
    
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.warning': 'تحذير',
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.delete': 'حذف',
    'common.edit': 'تحرير',
    'common.close': 'إغلاق'
  }
};

// Localization Context
const LocalizationContext = createContext({
  currentLanguage: 'en-US',
  translations: DEFAULT_TRANSLATIONS['en-US'],
  isRTL: false,
  setLanguage: () => {},
  t: (key, params) => key,
  formatNumber: (num) => num,
  formatDate: (date) => date,
  formatCurrency: (amount) => amount
});

// Translation function
const createTranslationFunction = (translations, fallbackTranslations) => {
  return (key, params = {}) => {
    let translation = translations[key] || fallbackTranslations[key] || key;
    
    // Replace parameters in translation
    Object.entries(params).forEach(([param, value]) => {
      translation = translation.replace(new RegExp(`{${param}}`, 'g'), value);
    });
    
    return translation;
  };
};

// Number formatting
const formatNumber = (number, locale) => {
  try {
    return new Intl.NumberFormat(locale).format(number);
  } catch (error) {
    return number.toString();
  }
};

// Date formatting
const formatDate = (date, locale, options = {}) => {
  try {
    return new Intl.DateTimeFormat(locale, options).format(new Date(date));
  } catch (error) {
    return date.toString();
  }
};

// Currency formatting
const formatCurrency = (amount, locale, currency = 'USD') => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  } catch (error) {
    return `${currency} ${amount}`;
  }
};

// Localization Provider
export const LocalizationProvider = ({ children, defaultLanguage = 'en-US' }) => {
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    // Try to get language from localStorage or browser
    const savedLanguage = localStorage.getItem('tutorial_language');
    if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage]) {
      return savedLanguage;
    }
    
    // Detect browser language
    const browserLanguage = navigator.language || navigator.languages[0];
    const supportedLanguage = Object.keys(SUPPORTED_LANGUAGES).find(lang => 
      lang.startsWith(browserLanguage.split('-')[0])
    );
    
    return supportedLanguage || defaultLanguage;
  });

  const languageConfig = SUPPORTED_LANGUAGES[currentLanguage];
  const translations = DEFAULT_TRANSLATIONS[currentLanguage] || DEFAULT_TRANSLATIONS['en-US'];
  const fallbackTranslations = DEFAULT_TRANSLATIONS['en-US'];

  const setLanguage = (language) => {
    if (SUPPORTED_LANGUAGES[language]) {
      setCurrentLanguage(language);
      localStorage.setItem('tutorial_language', language);
      
      // Update document direction for RTL languages
      document.documentElement.dir = SUPPORTED_LANGUAGES[language].rtl ? 'rtl' : 'ltr';
      document.documentElement.lang = language;
    }
  };

  const t = useMemo(() => 
    createTranslationFunction(translations, fallbackTranslations),
    [translations, fallbackTranslations]
  );

  const contextValue = useMemo(() => ({
    currentLanguage,
    translations,
    isRTL: languageConfig.rtl,
    setLanguage,
    t,
    formatNumber: (num) => formatNumber(num, currentLanguage),
    formatDate: (date, options) => formatDate(date, currentLanguage, options),
    formatCurrency: (amount, currency) => formatCurrency(amount, currentLanguage, currency),
    supportedLanguages: SUPPORTED_LANGUAGES
  }), [currentLanguage, translations, languageConfig, t]);

  // Set initial document direction
  useEffect(() => {
    document.documentElement.dir = languageConfig.rtl ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage, languageConfig.rtl]);

  return (
    <LocalizationContext.Provider value={contextValue}>
      <ConfigProvider locale={languageConfig.antdLocale} direction={languageConfig.rtl ? 'rtl' : 'ltr'}>
        {children}
      </ConfigProvider>
    </LocalizationContext.Provider>
  );
};

// Hook to use localization
export const useLocalization = () => {
  const context = useContext(LocalizationContext);
  if (!context) {
    throw new Error('useLocalization must be used within a LocalizationProvider');
  }
  return context;
};

// Language selector component
export const LanguageSelector = ({ style, className }) => {
  const { currentLanguage, setLanguage, supportedLanguages } = useLocalization();

  return (
    <select
      value={currentLanguage}
      onChange={(e) => setLanguage(e.target.value)}
      style={style}
      className={className}
    >
      {Object.entries(supportedLanguages).map(([code, config]) => (
        <option key={code} value={code}>
          {config.nativeName}
        </option>
      ))}
    </select>
  );
};

// Localized tutorial content creator
export const createLocalizedTutorial = (tutorialBase, localizedContent) => {
  return {
    ...tutorialBase,
    getLocalizedContent: (language) => {
      const content = localizedContent[language] || localizedContent['en-US'];
      return {
        ...tutorialBase,
        title: content.title || tutorialBase.title,
        description: content.description || tutorialBase.description,
        steps: tutorialBase.steps.map((step, index) => ({
          ...step,
          title: content.steps?.[index]?.title || step.title,
          content: content.steps?.[index]?.content || step.content
        }))
      };
    }
  };
};

// RTL-aware styling hook
export const useRTLStyles = () => {
  const { isRTL } = useLocalization();
  
  return useMemo(() => ({
    marginLeft: isRTL ? 'marginRight' : 'marginLeft',
    marginRight: isRTL ? 'marginLeft' : 'marginRight',
    paddingLeft: isRTL ? 'paddingRight' : 'paddingLeft',
    paddingRight: isRTL ? 'paddingLeft' : 'paddingRight',
    left: isRTL ? 'right' : 'left',
    right: isRTL ? 'left' : 'right',
    textAlign: isRTL ? 'right' : 'left',
    direction: isRTL ? 'rtl' : 'ltr',
    transform: (value) => isRTL ? value.replace(/translateX\(([^)]+)\)/, (match, p1) => {
      const num = parseFloat(p1);
      return `translateX(${-num}${p1.replace(/[0-9.-]/g, '')})`;
    }) : value
  }), [isRTL]);
};

// Localized tutorial step component
export const LocalizedTutorialStep = ({ step, ...props }) => {
  const { t, isRTL } = useLocalization();
  const rtlStyles = useRTLStyles();
  
  return (
    <div 
      className={`tutorial-step ${isRTL ? 'rtl' : 'ltr'}`}
      style={{ direction: rtlStyles.direction }}
    >
      <h3 style={{ textAlign: rtlStyles.textAlign }}>{step.title}</h3>
      <p style={{ textAlign: rtlStyles.textAlign }}>{step.content}</p>
      <div className="tutorial-controls" style={{ textAlign: rtlStyles.textAlign }}>
        <button>{t('tutorial.previous')}</button>
        <button>{t('tutorial.next')}</button>
        <button>{t('tutorial.skip')}</button>
      </div>
    </div>
  );
};

// Translation loader for dynamic content
export class TranslationLoader {
  constructor() {
    this.loadedTranslations = new Map();
    this.loadingPromises = new Map();
  }

  async loadTranslations(language, namespace = 'default') {
    const key = `${language}-${namespace}`;
    
    if (this.loadedTranslations.has(key)) {
      return this.loadedTranslations.get(key);
    }
    
    if (this.loadingPromises.has(key)) {
      return this.loadingPromises.get(key);
    }
    
    const loadingPromise = this.fetchTranslations(language, namespace);
    this.loadingPromises.set(key, loadingPromise);
    
    try {
      const translations = await loadingPromise;
      this.loadedTranslations.set(key, translations);
      this.loadingPromises.delete(key);
      return translations;
    } catch (error) {
      this.loadingPromises.delete(key);
      throw error;
    }
  }

  async fetchTranslations(language, namespace) {
    try {
      // In a real implementation, this would fetch from an API or load from files
      const response = await fetch(`/locales/${language}/${namespace}.json`);
      if (!response.ok) {
        throw new Error(`Failed to load translations for ${language}/${namespace}`);
      }
      return await response.json();
    } catch (error) {
      console.warn(`Failed to load translations for ${language}/${namespace}:`, error);
      return {};
    }
  }
}

// Export utilities
export const localizationUtils = {
  detectUserLanguage: () => {
    const browserLanguage = navigator.language || navigator.languages[0];
    return Object.keys(SUPPORTED_LANGUAGES).find(lang => 
      lang.startsWith(browserLanguage.split('-')[0])
    ) || 'en-US';
  },
  
  isRTLLanguage: (language) => {
    return SUPPORTED_LANGUAGES[language]?.rtl || false;
  },
  
  getLanguageName: (language) => {
    return SUPPORTED_LANGUAGES[language]?.name || language;
  },
  
  getNativeLanguageName: (language) => {
    return SUPPORTED_LANGUAGES[language]?.nativeName || language;
  }
};

export default {
  LocalizationProvider,
  useLocalization,
  LanguageSelector,
  createLocalizedTutorial,
  useRTLStyles,
  LocalizedTutorialStep,
  TranslationLoader,
  localizationUtils,
  SUPPORTED_LANGUAGES,
  DEFAULT_TRANSLATIONS
};
