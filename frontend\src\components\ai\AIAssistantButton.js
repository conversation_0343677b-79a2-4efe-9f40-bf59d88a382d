import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, Badge, Tooltip, Popover, Typography } from 'antd';
import { 
  RobotOutlined, 
  BulbOutlined, 
  ThunderboltOutlined,
  CloseOutlined 
} from '@ant-design/icons';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';

const { Text } = Typography;

/**
 * Floating AI Assistant Button
 * Shows notification badges and quick preview of suggestions
 */
const AIAssistantButton = ({
  onClick,
  position = 'bottom-right',
  showBadge = true,
  showPreview = true,
  components = [],
  selectedComponent = null,
  style = {},
  className = ''
}) => {
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [hasNewSuggestions, setHasNewSuggestions] = useState(false);

  // AI suggestions hook
  const {
    suggestions,
    loading,
    hasLayoutSuggestions,
    hasCombinationSuggestions,
    componentCount,
    lastRefresh
  } = useAIDesignSuggestions({
    autoRefresh: true,
    refreshInterval: 60000, // Check every minute
    enableCache: true,
    context: { selectedComponent }
  });

  // Track new suggestions
  useEffect(() => {
    if (hasLayoutSuggestions || hasCombinationSuggestions) {
      setHasNewSuggestions(true);
    }
  }, [hasLayoutSuggestions, hasCombinationSuggestions, lastRefresh]);

  // Reset new suggestions flag when button is clicked
  const handleClick = () => {
    setHasNewSuggestions(false);
    setPopoverVisible(false);
    if (onClick) {
      onClick();
    }
  };

  // Calculate total suggestions count
  const totalSuggestions = suggestions.layout.length + suggestions.combinations.length;

  // Get position styles
  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed',
      zIndex: 1000,
      ...style
    };

    switch (position) {
      case 'bottom-right':
        return { ...baseStyles, bottom: '24px', right: '24px' };
      case 'bottom-left':
        return { ...baseStyles, bottom: '24px', left: '24px' };
      case 'top-right':
        return { ...baseStyles, top: '24px', right: '24px' };
      case 'top-left':
        return { ...baseStyles, top: '24px', left: '24px' };
      default:
        return { ...baseStyles, bottom: '24px', right: '24px' };
    }
  };

  // Render preview content for popover
  const renderPreviewContent = () => (
    <div style={{ maxWidth: '300px' }}>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        marginBottom: '12px'
      }}>
        <Text strong>AI Suggestions Available</Text>
        <Button 
          type="text" 
          size="small" 
          icon={<CloseOutlined />}
          onClick={() => setPopoverVisible(false)}
        />
      </div>

      {suggestions.layout.length > 0 && (
        <div style={{ marginBottom: '8px' }}>
          <Text type="secondary">
            <BulbOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
            {suggestions.layout.length} layout suggestion{suggestions.layout.length !== 1 ? 's' : ''}
          </Text>
          <div style={{ marginLeft: '20px', marginTop: '4px' }}>
            {suggestions.layout.slice(0, 2).map((suggestion, index) => (
              <div key={index} style={{ fontSize: '12px', color: '#666' }}>
                • {suggestion.name}
              </div>
            ))}
            {suggestions.layout.length > 2 && (
              <div style={{ fontSize: '12px', color: '#999' }}>
                +{suggestions.layout.length - 2} more...
              </div>
            )}
          </div>
        </div>
      )}

      {suggestions.combinations.length > 0 && (
        <div style={{ marginBottom: '8px' }}>
          <Text type="secondary">
            <ThunderboltOutlined style={{ marginRight: '4px', color: '#52c41a' }} />
            {suggestions.combinations.length} component combination{suggestions.combinations.length !== 1 ? 's' : ''}
          </Text>
          <div style={{ marginLeft: '20px', marginTop: '4px' }}>
            {suggestions.combinations.slice(0, 2).map((suggestion, index) => (
              <div key={index} style={{ fontSize: '12px', color: '#666' }}>
                • {suggestion.name}
              </div>
            ))}
            {suggestions.combinations.length > 2 && (
              <div style={{ fontSize: '12px', color: '#999' }}>
                +{suggestions.combinations.length - 2} more...
              </div>
            )}
          </div>
        </div>
      )}

      <div style={{ 
        marginTop: '12px', 
        paddingTop: '8px', 
        borderTop: '1px solid #f0f0f0',
        textAlign: 'center'
      }}>
        <Button 
          type="primary" 
          size="small" 
          onClick={handleClick}
          style={{ width: '100%' }}
        >
          Open AI Assistant
        </Button>
      </div>
    </div>
  );

  // Don't show button if no components
  if (componentCount === 0) {
    return null;
  }

  const buttonElement = (
    <Badge 
      count={showBadge && totalSuggestions > 0 ? totalSuggestions : 0}
      dot={hasNewSuggestions && !showBadge}
      offset={[-8, 8]}
    >
      <Button
        type="primary"
        shape="circle"
        size="large"
        icon={<RobotOutlined />}
        onClick={handleClick}
        loading={loading.layout || loading.combinations}
        className={`ai-assistant-button ${className}`}
        style={{
          width: '56px',
          height: '56px',
          fontSize: '24px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          border: 'none',
          background: hasNewSuggestions 
            ? 'linear-gradient(45deg, #1890ff, #52c41a)' 
            : undefined
        }}
      />
    </Badge>
  );

  return (
    <div style={getPositionStyles()}>
      {showPreview && totalSuggestions > 0 ? (
        <Popover
          content={renderPreviewContent()}
          title={null}
          trigger="hover"
          placement={position.includes('right') ? 'leftBottom' : 'rightBottom'}
          open={popoverVisible}
          onOpenChange={setPopoverVisible}
          overlayStyle={{ zIndex: 1001 }}
        >
          <Tooltip 
            title={
              totalSuggestions > 0 
                ? `${totalSuggestions} AI suggestions available`
                : 'AI Assistant'
            }
            placement={position.includes('right') ? 'left' : 'right'}
          >
            {buttonElement}
          </Tooltip>
        </Popover>
      ) : (
        <Tooltip 
          title={
            totalSuggestions > 0 
              ? `${totalSuggestions} AI suggestions available`
              : 'AI Assistant'
          }
          placement={position.includes('right') ? 'left' : 'right'}
        >
          {buttonElement}
        </Tooltip>
      )}
    </div>
  );
};

AIAssistantButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  position: PropTypes.oneOf(['bottom-right', 'bottom-left', 'top-right', 'top-left']),
  showBadge: PropTypes.bool,
  showPreview: PropTypes.bool,
  components: PropTypes.array,
  selectedComponent: PropTypes.object,
  style: PropTypes.object,
  className: PropTypes.string,
};

export default AIAssistantButton;
