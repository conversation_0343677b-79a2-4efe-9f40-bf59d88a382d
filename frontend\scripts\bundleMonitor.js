const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const { analyzeJSFiles, trackBundleSize, generateSizeReport, formatBytes } = require('./analyzeBundleStructure');

/**
 * Bundle Size Monitor
 * 
 * Continuous monitoring system for bundle size with alerts and regression detection.
 */

const TARGET_SIZE = 244 * 1024; // 244 KB
const WARNING_THRESHOLD = 0.9; // 90% of target
const CRITICAL_THRESHOLD = 1.1; // 110% of target

class BundleSizeMonitor {
  constructor() {
    this.historyFile = path.join(__dirname, '../build/bundle-history.json');
    this.alertsFile = path.join(__dirname, '../build/bundle-alerts.json');
    this.configFile = path.join(__dirname, '../build/bundle-config.json');
    
    this.loadConfig();
  }

  loadConfig() {
    const defaultConfig = {
      targetSize: TARGET_SIZE,
      warningThreshold: WARNING_THRESHOLD,
      criticalThreshold: CRITICAL_THRESHOLD,
      alertsEnabled: true,
      regressionThreshold: 0.05, // 5% increase triggers alert
      maxHistoryEntries: 100
    };

    try {
      if (fs.existsSync(this.configFile)) {
        this.config = { ...defaultConfig, ...JSON.parse(fs.readFileSync(this.configFile, 'utf8')) };
      } else {
        this.config = defaultConfig;
        this.saveConfig();
      }
    } catch (error) {
      console.warn('Could not load config, using defaults:', error.message);
      this.config = defaultConfig;
    }
  }

  saveConfig() {
    try {
      fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.warn('Could not save config:', error.message);
    }
  }

  loadHistory() {
    try {
      if (fs.existsSync(this.historyFile)) {
        return JSON.parse(fs.readFileSync(this.historyFile, 'utf8'));
      }
    } catch (error) {
      console.warn('Could not load history:', error.message);
    }
    return [];
  }

  saveAlert(alert) {
    try {
      let alerts = [];
      if (fs.existsSync(this.alertsFile)) {
        alerts = JSON.parse(fs.readFileSync(this.alertsFile, 'utf8'));
      }
      
      alerts.push({
        ...alert,
        timestamp: new Date().toISOString()
      });
      
      // Keep only last 50 alerts
      if (alerts.length > 50) {
        alerts = alerts.slice(-50);
      }
      
      fs.writeFileSync(this.alertsFile, JSON.stringify(alerts, null, 2));
    } catch (error) {
      console.warn('Could not save alert:', error.message);
    }
  }

  checkForRegression(currentSize, history) {
    if (history.length < 2) return null;
    
    const previousSize = history[history.length - 2].totalSize;
    const increase = currentSize - previousSize;
    const percentIncrease = increase / previousSize;
    
    if (percentIncrease > this.config.regressionThreshold) {
      return {
        type: 'regression',
        severity: 'warning',
        message: `Bundle size increased by ${formatBytes(increase)} (${(percentIncrease * 100).toFixed(1)}%)`,
        previousSize,
        currentSize,
        increase
      };
    }
    
    return null;
  }

  checkThresholds(currentSize) {
    const alerts = [];
    const targetRatio = currentSize / this.config.targetSize;
    
    if (targetRatio > this.config.criticalThreshold) {
      alerts.push({
        type: 'threshold',
        severity: 'critical',
        message: `Bundle size is ${(targetRatio * 100).toFixed(1)}% of target (${formatBytes(currentSize)} > ${formatBytes(this.config.targetSize)})`,
        currentSize,
        targetSize: this.config.targetSize,
        ratio: targetRatio
      });
    } else if (targetRatio > this.config.warningThreshold) {
      alerts.push({
        type: 'threshold',
        severity: 'warning',
        message: `Bundle size is approaching target: ${(targetRatio * 100).toFixed(1)}% (${formatBytes(currentSize)})`,
        currentSize,
        targetSize: this.config.targetSize,
        ratio: targetRatio
      });
    }
    
    return alerts;
  }

  displayAlerts(alerts) {
    if (alerts.length === 0) return;
    
    console.log(chalk.yellow.bold('\n⚠️  Bundle Size Alerts:\n'));
    
    alerts.forEach(alert => {
      const icon = alert.severity === 'critical' ? '🚨' : '⚠️';
      const color = alert.severity === 'critical' ? chalk.red : chalk.yellow;
      
      console.log(color(`${icon} ${alert.message}`));
    });
    
    console.log('');
  }

  generateTrend(history) {
    if (history.length < 5) return null;
    
    const recent = history.slice(-5);
    const sizes = recent.map(entry => entry.totalSize);
    
    // Simple linear regression to detect trend
    const n = sizes.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = sizes.reduce((sum, size) => sum + size, 0);
    const sumXY = sizes.reduce((sum, size, index) => sum + (index * size), 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    
    return {
      trend: slope > 1000 ? 'increasing' : slope < -1000 ? 'decreasing' : 'stable',
      slope,
      prediction: sizes[sizes.length - 1] + slope
    };
  }

  monitor() {
    console.log(chalk.blue.bold('🔍 Bundle Size Monitor\n'));
    
    // Analyze current bundle
    const analysis = analyzeJSFiles();
    if (!analysis) {
      console.error(chalk.red('❌ Could not analyze bundle'));
      return false;
    }
    
    // Load history and track current size
    const history = this.loadHistory();
    const tracking = trackBundleSize(analysis);
    
    // Check for issues
    const alerts = [];
    
    // Check for regression
    const regression = this.checkForRegression(analysis.totalSize, history);
    if (regression) alerts.push(regression);
    
    // Check thresholds
    const thresholdAlerts = this.checkThresholds(analysis.totalSize);
    alerts.push(...thresholdAlerts);
    
    // Display alerts
    this.displayAlerts(alerts);
    
    // Save alerts
    alerts.forEach(alert => this.saveAlert(alert));
    
    // Generate trend analysis
    const trend = this.generateTrend([...history, tracking]);
    if (trend) {
      console.log(chalk.blue.bold('📈 Trend Analysis:\n'));
      const trendColor = trend.trend === 'increasing' ? chalk.red : 
                        trend.trend === 'decreasing' ? chalk.green : chalk.yellow;
      console.log(trendColor(`Trend: ${trend.trend}`));
      
      if (trend.trend !== 'stable') {
        console.log(`Predicted next size: ${formatBytes(trend.prediction)}`);
      }
      console.log('');
    }
    
    // Generate recommendations
    const report = generateSizeReport(analysis);
    if (report.recommendations.length > 0) {
      console.log(chalk.blue.bold('💡 Recommendations:\n'));
      report.recommendations.forEach(rec => {
        console.log(chalk.cyan(`• ${rec}`));
      });
      console.log('');
    }
    
    // Success/failure status
    const isUnderTarget = analysis.totalSize <= this.config.targetSize;
    if (isUnderTarget) {
      console.log(chalk.green.bold('✅ Bundle size is within target!'));
    } else {
      console.log(chalk.red.bold('❌ Bundle size exceeds target'));
    }
    
    return isUnderTarget;
  }

  watch() {
    console.log(chalk.blue('👀 Starting bundle size watcher...\n'));
    
    const buildDir = path.join(__dirname, '../build');
    
    if (!fs.existsSync(buildDir)) {
      console.error(chalk.red('❌ Build directory not found. Run "npm run build" first.'));
      return;
    }
    
    // Initial check
    this.monitor();
    
    // Watch for changes
    fs.watch(buildDir, { recursive: true }, (eventType, filename) => {
      if (filename && filename.endsWith('.js')) {
        console.log(chalk.blue(`\n🔄 Detected change in ${filename}, re-analyzing...\n`));
        setTimeout(() => this.monitor(), 1000); // Debounce
      }
    });
    
    console.log(chalk.blue('👀 Watching for bundle changes... (Press Ctrl+C to stop)'));
  }
}

// CLI interface
if (require.main === module) {
  const monitor = new BundleSizeMonitor();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'watch':
      monitor.watch();
      break;
    case 'check':
    default:
      const success = monitor.monitor();
      process.exit(success ? 0 : 1);
  }
}

module.exports = BundleSizeMonitor;
