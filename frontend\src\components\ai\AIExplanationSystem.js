import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { 
  Modal, 
  Card, 
  Typography, 
  Space, 
  Tag, 
  Progress, 
  Collapse, 
  List,
  Tooltip,
  But<PERSON>,
  Divider
} from 'antd';
import { 
  InfoCircleOutlined, 
  BulbOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  StarOutlined,
  TrophyOutlined,
  TargetOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

/**
 * AI Explanation System Component
 * Provides detailed explanations for AI recommendations
 */
const AIExplanationSystem = ({
  visible,
  onClose,
  suggestion,
  type = 'layout', // 'layout' or 'combination'
  showAdvanced = true
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (!suggestion) return null;

  // Generate detailed explanation based on suggestion data
  const generateDetailedExplanation = () => {
    const explanations = [];
    
    // Score-based explanation
    if (suggestion.score >= 80) {
      explanations.push({
        type: 'success',
        icon: <TrophyOutlined />,
        title: 'Excellent Match',
        content: `This ${type} has a high compatibility score of ${suggestion.score}/100, indicating it's an excellent fit for your current app structure.`
      });
    } else if (suggestion.score >= 60) {
      explanations.push({
        type: 'info',
        icon: <StarOutlined />,
        title: 'Good Match',
        content: `This ${type} has a good compatibility score of ${suggestion.score}/100, making it a solid choice for your app.`
      });
    } else {
      explanations.push({
        type: 'warning',
        icon: <ExclamationCircleOutlined />,
        title: 'Moderate Match',
        content: `This ${type} has a moderate compatibility score of ${suggestion.score}/100. Consider if it aligns with your specific needs.`
      });
    }

    // Type-specific explanations
    if (type === 'layout') {
      if (suggestion.id.includes('grid')) {
        explanations.push({
          type: 'info',
          icon: <TargetOutlined />,
          title: 'Grid Layout Benefits',
          content: 'Grid layouts provide excellent organization for multiple components, responsive design capabilities, and consistent spacing. They work particularly well for dashboards, galleries, and content-heavy applications.'
        });
      }
      
      if (suggestion.id.includes('header')) {
        explanations.push({
          type: 'info',
          icon: <CheckCircleOutlined />,
          title: 'Header-Footer Structure',
          content: 'This classic layout pattern provides clear navigation hierarchy and is familiar to users. It\'s excellent for content-focused applications and provides good SEO structure.'
        });
      }
      
      if (suggestion.id.includes('sidebar')) {
        explanations.push({
          type: 'info',
          icon: <BulbOutlined />,
          title: 'Sidebar Navigation',
          content: 'Sidebar layouts offer persistent navigation and efficient use of screen space. They\'re ideal for applications with complex navigation structures or admin interfaces.'
        });
      }
    }

    if (type === 'combination') {
      if (suggestion.missing_components && suggestion.missing_components.length > 0) {
        explanations.push({
          type: 'info',
          icon: <InfoCircleOutlined />,
          title: 'Component Synergy',
          content: `Adding ${suggestion.missing_components.join(', ')} will create a cohesive user experience. These components work together to provide complete functionality.`
        });
      }
    }

    return explanations;
  };

  // Get reasoning factors
  const getReasoningFactors = () => {
    const factors = [];
    
    if (suggestion.applicable_components && suggestion.applicable_components.length > 0) {
      factors.push({
        factor: 'Component Compatibility',
        score: Math.min(100, (suggestion.applicable_components.length / 5) * 100),
        description: `${suggestion.applicable_components.length} of your components are compatible with this ${type}`
      });
    }
    
    if (suggestion.use_cases) {
      factors.push({
        factor: 'Use Case Alignment',
        score: suggestion.score,
        description: `Matches ${suggestion.use_cases.length} common use cases: ${suggestion.use_cases.join(', ')}`
      });
    }
    
    factors.push({
      factor: 'Best Practices',
      score: Math.max(60, suggestion.score - 10),
      description: 'Follows modern UI/UX design principles and accessibility guidelines'
    });
    
    factors.push({
      factor: 'Implementation Ease',
      score: Math.max(70, suggestion.score - 5),
      description: 'Can be implemented with minimal changes to your existing structure'
    });
    
    return factors;
  };

  // Render overview tab
  const renderOverview = () => (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      <Card size="small">
        <div style={{ textAlign: 'center' }}>
          <Title level={4}>{suggestion.name}</Title>
          <Progress 
            type="circle" 
            percent={suggestion.score} 
            format={percent => `${percent}%`}
            strokeColor={suggestion.score >= 80 ? '#52c41a' : suggestion.score >= 60 ? '#1890ff' : '#faad14'}
          />
          <Paragraph style={{ marginTop: '16px' }}>
            {suggestion.description}
          </Paragraph>
        </div>
      </Card>

      <Card size="small" title="Why This Recommendation?">
        <Paragraph>
          {suggestion.explanation}
        </Paragraph>
        
        {suggestion.use_cases && (
          <div style={{ marginTop: '16px' }}>
            <Text strong>Best suited for:</Text>
            <div style={{ marginTop: '8px' }}>
              {suggestion.use_cases.map((useCase, index) => (
                <Tag key={index} color="blue" style={{ marginBottom: '4px' }}>
                  {useCase.replace('_', ' ')}
                </Tag>
              ))}
            </div>
          </div>
        )}
      </Card>

      {type === 'combination' && suggestion.missing_components && (
        <Card size="small" title="Components to Add">
          <List
            size="small"
            dataSource={suggestion.missing_components}
            renderItem={component => (
              <List.Item>
                <Space>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  <Text>{component}</Text>
                </Space>
              </List.Item>
            )}
          />
        </Card>
      )}
    </Space>
  );

  // Render detailed analysis tab
  const renderDetailedAnalysis = () => (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      <Card size="small" title="Reasoning Factors">
        {getReasoningFactors().map((factor, index) => (
          <div key={index} style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
              <Text strong>{factor.factor}</Text>
              <Text>{factor.score}%</Text>
            </div>
            <Progress percent={factor.score} showInfo={false} size="small" />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {factor.description}
            </Text>
          </div>
        ))}
      </Card>

      <Card size="small" title="Detailed Explanations">
        <Collapse ghost>
          {generateDetailedExplanation().map((explanation, index) => (
            <Panel 
              key={index}
              header={
                <Space>
                  {explanation.icon}
                  <Text strong>{explanation.title}</Text>
                </Space>
              }
            >
              <Paragraph>{explanation.content}</Paragraph>
            </Panel>
          ))}
        </Collapse>
      </Card>

      {suggestion.structure && (
        <Card size="small" title="Technical Details">
          <Paragraph>
            <Text strong>Structure:</Text>
          </Paragraph>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify(suggestion.structure, null, 2)}
          </pre>
        </Card>
      )}
    </Space>
  );

  // Render pros and cons
  const renderProsAndCons = () => {
    const pros = [];
    const cons = [];

    // Generate pros based on suggestion data
    if (suggestion.score >= 80) {
      pros.push('High compatibility with your current app structure');
    }
    if (suggestion.use_cases && suggestion.use_cases.length > 2) {
      pros.push('Versatile - works for multiple use cases');
    }
    if (type === 'layout' && suggestion.id.includes('responsive')) {
      pros.push('Responsive design that works on all devices');
    }
    if (type === 'combination' && suggestion.missing_components && suggestion.missing_components.length <= 2) {
      pros.push('Requires minimal additional components');
    }

    // Generate cons based on suggestion data
    if (suggestion.score < 60) {
      cons.push('May require adjustments to fit your specific needs');
    }
    if (type === 'combination' && suggestion.missing_components && suggestion.missing_components.length > 3) {
      cons.push('Requires adding several new components');
    }
    if (suggestion.id.includes('complex')) {
      cons.push('May be more complex to implement initially');
    }

    // Default pros/cons if none generated
    if (pros.length === 0) {
      pros.push('Follows modern design principles');
      pros.push('Improves user experience');
    }
    if (cons.length === 0) {
      cons.push('May require some customization');
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card size="small" title="Advantages">
          <List
            size="small"
            dataSource={pros}
            renderItem={pro => (
              <List.Item>
                <Space>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  <Text>{pro}</Text>
                </Space>
              </List.Item>
            )}
          />
        </Card>

        <Card size="small" title="Considerations">
          <List
            size="small"
            dataSource={cons}
            renderItem={con => (
              <List.Item>
                <Space>
                  <ExclamationCircleOutlined style={{ color: '#faad14' }} />
                  <Text>{con}</Text>
                </Space>
              </List.Item>
            )}
          />
        </Card>
      </Space>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <InfoCircleOutlined />
          AI Recommendation Explanation
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>
      ]}
      width={700}
    >
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button 
            type={activeTab === 'overview' ? 'primary' : 'default'}
            size="small"
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </Button>
          {showAdvanced && (
            <>
              <Button 
                type={activeTab === 'analysis' ? 'primary' : 'default'}
                size="small"
                onClick={() => setActiveTab('analysis')}
              >
                Detailed Analysis
              </Button>
              <Button 
                type={activeTab === 'proscons' ? 'primary' : 'default'}
                size="small"
                onClick={() => setActiveTab('proscons')}
              >
                Pros & Cons
              </Button>
            </>
          )}
        </Space>
      </div>

      <Divider style={{ margin: '16px 0' }} />

      <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'analysis' && renderDetailedAnalysis()}
        {activeTab === 'proscons' && renderProsAndCons()}
      </div>
    </Modal>
  );
};

AIExplanationSystem.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  suggestion: PropTypes.object,
  type: PropTypes.oneOf(['layout', 'combination']),
  showAdvanced: PropTypes.bool
};

export default AIExplanationSystem;
