# WebSocket Exponential Backoff with Jitter Implementation

## Overview

This document describes the enhanced WebSocket reconnection strategy implemented in the App Builder application. The implementation uses exponential backoff with jitter to provide robust and efficient reconnection handling.

## Key Features

### 1. Exponential Backoff
- **Base Delay**: 1 second (1000ms)
- **Max Delay**: 30 seconds (30000ms)
- **Growth Factor**: 2x per attempt
- **Formula**: `delay = min(maxDelay, baseDelay * 2^attempt)`

### 2. Jitter Implementation
- **Jitter Factor**: 30% (±30% of calculated delay)
- **Purpose**: Prevents thundering herd problem
- **Algorithm**: `jitter = (random() - 0.5) * 2 * (delay * 0.3)`
- **Final Delay**: `max(baseDelay, exponentialDelay + jitter)`

### 3. Connection Management
- **Max Attempts**: 10 reconnection attempts
- **State Tracking**: CONNECTING, CONNECTED, DISCONNECTED, RECONNECTING, FAILED
- **Auto-reconnect**: Configurable automatic reconnection
- **Manual Control**: Connect/disconnect methods available

## Implementation Details

### Delay Calculation Example

```
Attempt 1: 1s + jitter (±0.3s) = 0.7s - 1.3s
Attempt 2: 2s + jitter (±0.6s) = 1.4s - 2.6s
Attempt 3: 4s + jitter (±1.2s) = 2.8s - 5.2s
Attempt 4: 8s + jitter (±2.4s) = 5.6s - 10.4s
Attempt 5: 16s + jitter (±4.8s) = 11.2s - 20.8s
Attempt 6+: 30s + jitter (±9s) = 21s - 39s (capped at maxDelay)
```

### Benefits of Jitter

1. **Prevents Thundering Herd**: Multiple clients don't reconnect simultaneously
2. **Reduces Server Load**: Spreads reconnection attempts over time
3. **Improves Success Rate**: Reduces connection conflicts
4. **Better User Experience**: More natural reconnection patterns

## Usage Examples

### Basic Usage with useWebSocketWithBackoff Hook

```javascript
import { useWebSocketWithBackoff } from '../services/websocket';

const MyComponent = () => {
  const {
    connect,
    disconnect,
    send,
    isConnected,
    getConnectionState
  } = useWebSocketWithBackoff({
    url: 'ws://localhost:8000/ws/app_builder/',
    autoConnect: true,
    autoReconnect: true,
    maxReconnectAttempts: 10,
    baseDelay: 1000,
    maxDelay: 30000,
    jitterFactor: 0.3,
    debug: true,
    onReconnecting: (info) => {
      console.log(`Reconnecting... attempt ${info.attempt}`);
    }
  });

  return (
    <div>
      <p>Status: {getConnectionState()}</p>
      <button onClick={connect} disabled={isConnected()}>
        Connect
      </button>
      <button onClick={disconnect} disabled={!isConnected()}>
        Disconnect
      </button>
    </div>
  );
};
```

### Using WebSocketService Class

```javascript
import WebSocketService from '../services/WebSocketService';

// Initialize with enhanced backoff
const wsService = WebSocketService.getInstance()
  .setReconnectOptions({
    maxAttempts: 10,
    initialDelay: 1000,
    maxDelay: 30000,
    useExponentialBackoff: true,
    jitter: 0.3
  });

// Connect
wsService.connect('ws://localhost:8000/ws/app_builder/')
  .then(() => console.log('Connected'))
  .catch(error => console.error('Connection failed:', error));
```

## Configuration Options

### WebSocket Hook Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `url` | string | required | WebSocket server URL |
| `autoConnect` | boolean | true | Auto-connect on mount |
| `autoReconnect` | boolean | true | Enable automatic reconnection |
| `maxReconnectAttempts` | number | 10 | Maximum reconnection attempts |
| `baseDelay` | number | 1000 | Base delay in milliseconds |
| `maxDelay` | number | 30000 | Maximum delay in milliseconds |
| `jitterFactor` | number | 0.3 | Jitter factor (0.0 - 1.0) |
| `debug` | boolean | false | Enable debug logging |

### Event Callbacks

| Callback | Parameters | Description |
|----------|------------|-------------|
| `onOpen` | event | Called when connection opens |
| `onClose` | event | Called when connection closes |
| `onMessage` | data | Called when message received |
| `onError` | error | Called on connection error |
| `onReconnecting` | info | Called during reconnection attempts |

## Best Practices

### 1. Jitter Configuration
- Use 20-30% jitter for most applications
- Higher jitter (up to 50%) for high-traffic scenarios
- Lower jitter (10-20%) for real-time applications

### 2. Delay Limits
- Set reasonable max delays (30-60 seconds)
- Consider user experience vs. server load
- Adjust based on application requirements

### 3. Attempt Limits
- 5-10 attempts for user-facing applications
- Higher limits for background services
- Consider exponential backoff growth rate

### 4. Error Handling
- Implement proper error callbacks
- Log reconnection attempts for debugging
- Provide user feedback for connection issues

## Monitoring and Debugging

### Debug Output Example

```
Reconnecting in 2s (attempt 2/10) {
  exponentialDelay: 2000,
  jitter: -341,
  finalDelay: 1659,
  jitterFactor: 0.3
}
```

### Connection State Events

- `connecting`: Connection attempt started
- `reconnecting`: Reconnection attempt with delay info
- `max_retries_reached`: All attempts exhausted
- `network_status_change`: Network connectivity changed

## Testing

### Manual Testing
1. Start the application
2. Disconnect network/server
3. Observe reconnection attempts in console
4. Verify exponential backoff timing
5. Check jitter variation between attempts

### Automated Testing
- Unit tests for delay calculation
- Integration tests for reconnection flow
- Load tests for multiple concurrent connections
- Network simulation tests

## Troubleshooting

### Common Issues

1. **Too Aggressive Reconnection**
   - Increase base delay
   - Add more jitter
   - Reduce max attempts

2. **Slow Reconnection**
   - Decrease base delay
   - Reduce jitter factor
   - Check network conditions

3. **Server Overload**
   - Increase jitter factor
   - Implement connection pooling
   - Add rate limiting

### Performance Considerations

- Monitor server connection metrics
- Track reconnection success rates
- Analyze delay distribution patterns
- Optimize based on usage patterns

## Future Enhancements

1. **Adaptive Backoff**: Adjust delays based on success rates
2. **Circuit Breaker**: Temporary connection suspension
3. **Health Checks**: Periodic connection validation
4. **Metrics Collection**: Detailed reconnection analytics
5. **Smart Jitter**: Context-aware jitter algorithms
