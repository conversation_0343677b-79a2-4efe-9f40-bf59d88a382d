import aiDesignService from '../../../services/aiDesignService';
import { aiConfig } from '../../../config/aiConfig';

// Mock fetch globally
global.fetch = jest.fn();

describe('AI Design Service Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset fetch mock
    fetch.mockClear();
  });

  afterEach(() => {
    // Clear any cached data
    aiDesignService.clearCache();
  });

  describe('Service Unavailable Scenarios', () => {
    test('should handle 404 errors gracefully for analyzeAppStructure', async () => {
      // Mock a 404 response
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Not found' })
      });

      const components = [
        { type: 'button', id: '1' },
        { type: 'text', id: '2' }
      ];

      const result = await aiDesignService.analyzeAppStructure(components);

      // Should return fallback analysis
      expect(result).toBeDefined();
      expect(result.status).toBe('basic');
      expect(result.analysis).toBeDefined();
      expect(result.analysis.component_count).toBe(2);
    });

    test('should handle network errors gracefully for layoutSuggestions', async () => {
      // Mock a network error
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const components = [
        { type: 'button', id: '1' },
        { type: 'text', id: '2' }
      ];

      const result = await aiDesignService.generateLayoutSuggestions(components);

      // Should return fallback suggestions
      expect(result).toBeDefined();
      expect(result.status).toBe('fallback');
      expect(result.suggestions).toBeDefined();
      expect(Array.isArray(result.suggestions)).toBe(true);
    });

    test('should handle timeout errors gracefully for componentCombinations', async () => {
      // Mock a timeout error
      fetch.mockRejectedValueOnce(new Error('Request timeout'));

      const components = [
        { type: 'button', id: '1' }
      ];

      const result = await aiDesignService.generateComponentCombinations(components);

      // Should return fallback suggestions
      expect(result).toBeDefined();
      expect(result.status).toBe('fallback');
      expect(result.suggestions).toBeDefined();
      expect(Array.isArray(result.suggestions)).toBe(true);
    });
  });

  describe('Fallback Behavior', () => {
    test('should provide meaningful fallback layout suggestions', async () => {
      // Mock service failure
      fetch.mockRejectedValueOnce(new Error('Service unavailable'));

      const components = [
        { type: 'button', id: '1' },
        { type: 'text', id: '2' },
        { type: 'image', id: '3' },
        { type: 'form', id: '4' }
      ];

      const result = await aiDesignService.generateLayoutSuggestions(components);

      expect(result.suggestions).toBeDefined();
      expect(result.suggestions.length).toBeGreaterThan(0);

      // Should suggest grid layout for multiple components
      const gridSuggestion = result.suggestions.find(s => s.id === 'grid_layout');
      expect(gridSuggestion).toBeDefined();
      expect(gridSuggestion.score).toBeGreaterThan(0);
    });

    test('should provide meaningful fallback component combinations', async () => {
      // Mock service failure
      fetch.mockRejectedValueOnce(new Error('Service unavailable'));

      const components = [
        { type: 'button', id: '1' }
      ];
      const selectedComponent = { type: 'button', id: '1' };

      const result = await aiDesignService.generateComponentCombinations(components, selectedComponent);

      expect(result.suggestions).toBeDefined();
      expect(result.suggestions.length).toBeGreaterThan(0);

      // Should suggest adding a form with button
      const formSuggestion = result.suggestions.find(s => s.id === 'button_form');
      expect(formSuggestion).toBeDefined();
      expect(formSuggestion.missing_components).toContain('form');
    });

    test('should provide basic app analysis when service is unavailable', async () => {
      // Mock service failure
      fetch.mockRejectedValueOnce(new Error('Service unavailable'));

      const components = [
        { type: 'header', id: '1' },
        { type: 'button', id: '2' },
        { type: 'form', id: '3' },
        { type: 'image', id: '4' }
      ];

      const result = await aiDesignService.analyzeAppStructure(components);

      expect(result.analysis).toBeDefined();
      expect(result.analysis.component_count).toBe(4);
      expect(result.analysis.has_navigation).toBe(true);
      expect(result.analysis.has_forms).toBe(true);
      expect(result.analysis.has_media).toBe(true);
      expect(result.analysis.complexity_score).toBeGreaterThan(0);
    });
  });

  describe('Caching Behavior', () => {
    test('should return consistent fallback results', async () => {
      // Mock service failure for both potential calls
      fetch.mockRejectedValue(new Error('Service unavailable'));

      const components = [{ type: 'button', id: '1' }];

      // First call
      const result1 = await aiDesignService.generateLayoutSuggestions(components);

      // Second call should return the same fallback data
      const result2 = await aiDesignService.generateLayoutSuggestions(components);

      // Results should be consistent
      expect(result1).toEqual(result2);
      expect(result1.status).toBe('fallback');
      expect(result2.status).toBe('fallback');
    });

    test('should clear cache when requested', async () => {
      // Mock service failure
      fetch.mockRejectedValueOnce(new Error('Service unavailable'));

      const components = [{ type: 'button', id: '1' }];

      // First call
      await aiDesignService.generateLayoutSuggestions(components);

      // Clear cache
      aiDesignService.clearCache();

      // Mock another failure for second call
      fetch.mockRejectedValueOnce(new Error('Service unavailable'));

      // Second call should make new request
      await aiDesignService.generateLayoutSuggestions(components);

      expect(fetch).toHaveBeenCalledTimes(2); // Called twice
    });
  });

  describe('Configuration Handling', () => {
    test('should respect AI disabled configuration', async () => {
      // Temporarily disable AI
      const originalEnabled = aiDesignService.enabled;
      aiDesignService.enabled = false;

      const components = [{ type: 'button', id: '1' }];

      const result = await aiDesignService.generateLayoutSuggestions(components);

      // Should return fallback without making any API calls
      expect(fetch).not.toHaveBeenCalled();
      expect(result.status).toBe('fallback');

      // Restore original state
      aiDesignService.enabled = originalEnabled;
    });
  });

  describe('Error Message Handling', () => {
    test('should not spam console with warnings when configured', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => { });

      // Mock service failure
      fetch.mockRejectedValueOnce(new Error('Service unavailable'));

      const components = [{ type: 'button', id: '1' }];
      await aiDesignService.generateLayoutSuggestions(components);

      // Should handle error gracefully without excessive logging
      expect(consoleSpy).toHaveBeenCalledTimes(0); // No warnings when showWarnings is false

      consoleSpy.mockRestore();
    });
  });
});
