/**
 * Structured Data Utility
 * 
 * This utility provides functions for generating structured data (JSON-LD)
 * to improve SEO and provide better search engine results.
 */

/**
 * Generate website structured data
 * @param {Object} options - Website options
 * @returns {Object} - JSON-LD structured data
 */
export const generateWebsiteData = (options = {}) => {
  const {
    name = 'App Builder 201',
    url = window.location.origin,
    description = 'Build your application with minimal setup',
    logo = `${window.location.origin}/logo512.png`,
    sameAs = []
  } = options;

  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name,
    url,
    description,
    potentialAction: {
      '@type': 'SearchAction',
      'target': {
        '@type': 'EntryPoint',
        'urlTemplate': `${url}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    ...(logo && { logo }),
    ...(sameAs.length > 0 && { sameAs })
  };
};

/**
 * Generate organization structured data
 * @param {Object} options - Organization options
 * @returns {Object} - JSON-LD structured data
 */
export const generateOrganizationData = (options = {}) => {
  const {
    name = 'App Builder Team',
    url = window.location.origin,
    logo = `${window.location.origin}/logo512.png`,
    sameAs = []
  } = options;

  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    url,
    logo,
    ...(sameAs.length > 0 && { sameAs })
  };
};

/**
 * Generate breadcrumb structured data
 * @param {Array} items - Breadcrumb items
 * @returns {Object} - JSON-LD structured data
 */
export const generateBreadcrumbData = (items = []) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };
};

/**
 * Generate software application structured data
 * @param {Object} options - Software application options
 * @returns {Object} - JSON-LD structured data
 */
export const generateSoftwareApplicationData = (options = {}) => {
  const {
    name = 'App Builder 201',
    description = 'Build your application with minimal setup',
    operatingSystem = 'Web',
    applicationCategory = 'DeveloperApplication',
    offers = {
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock'
    }
  } = options;

  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name,
    description,
    operatingSystem,
    applicationCategory,
    offers: {
      '@type': 'Offer',
      ...offers
    }
  };
};

/**
 * Generate structured data script tag
 * @param {Object} data - Structured data object
 * @returns {string} - Script tag with JSON-LD
 */
export const generateStructuredDataScript = (data) => {
  return `<script type="application/ld+json">${JSON.stringify(data)}</script>`;
};

export default {
  generateWebsiteData,
  generateOrganizationData,
  generateBreadcrumbData,
  generateSoftwareApplicationData,
  generateStructuredDataScript
};
