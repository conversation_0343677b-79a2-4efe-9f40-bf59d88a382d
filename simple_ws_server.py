import asyncio
import websockets
import json
import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('simple_ws_server')

# Set of connected clients
connected = set()
client_counter = 0

async def echo(websocket):
    """
    Handle a WebSocket connection.
    
    Args:
        websocket: The WebSocket connection
    """
    global client_counter
    client_counter += 1
    client_id = client_counter
    
    # Get client info
    client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
    logger.info(f"New client connected: {client_info} (ID: {client_id})")
    
    # Register the client
    connected.add(websocket)
    
    try:
        # Send welcome message
        await websocket.send(json.dumps({
            'type': 'welcome',
            'message': 'Welcome to the simple WebSocket server!',
            'client_id': client_id,
            'timestamp': datetime.now().isoformat()
        }))
        
        # Handle messages
        async for message in websocket:
            logger.info(f"Received message from client {client_id}: {message[:100]}...")
            
            try:
                # Try to parse as JSON
                data = json.loads(message)
                message_type = data.get('type', 'unknown')
                
                # Handle ping messages
                if message_type == 'ping':
                    await websocket.send(json.dumps({
                        'type': 'pong',
                        'timestamp': datetime.now().isoformat(),
                        'client_id': client_id,
                        'received_timestamp': data.get('timestamp')
                    }))
                    logger.info(f"Sent pong to client {client_id}")
                else:
                    # Echo the message back
                    await websocket.send(json.dumps({
                        'type': 'echo',
                        'message': f"Echo: {data.get('message', 'No message')}",
                        'original_message': data,
                        'client_id': client_id,
                        'timestamp': datetime.now().isoformat()
                    }))
                    logger.info(f"Echoed message to client {client_id}")
            except json.JSONDecodeError:
                # If not valid JSON, echo as plain text
                await websocket.send(json.dumps({
                    'type': 'echo',
                    'message': f"Echo: {message}",
                    'client_id': client_id,
                    'timestamp': datetime.now().isoformat()
                }))
                logger.info(f"Echoed plain text to client {client_id}")
    except websockets.exceptions.ConnectionClosed as e:
        logger.info(f"Connection closed with client {client_info}: code={e.code}, reason={e.reason}")
    except Exception as e:
        logger.error(f"Error handling client {client_info}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # Unregister the client
        connected.remove(websocket)
        logger.info(f"Client disconnected: {client_info} (ID: {client_id})")

async def main():
    """
    Start the WebSocket server.
    """
    host = "0.0.0.0"  # Listen on all interfaces
    port = 8765
    
    logger.info(f"Starting WebSocket server on {host}:{port}")
    
    async with websockets.serve(echo, host, port):
        logger.info(f"WebSocket server running at ws://{host}:{port}")
        await asyncio.Future()  # Run forever

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
