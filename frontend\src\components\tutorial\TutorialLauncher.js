/**
 * Tutorial Launcher Component
 * 
 * Provides an interface for users to discover, select, and launch tutorials.
 * Includes filtering, search, and recommendation features.
 */

import React, { useState, useMemo } from 'react';
import {
  Modal,
  Card,
  Row,
  Col,
  Button,
  Input,
  Select,
  Tag,
  Typography,
  Space,
  Badge,
  Progress,
  Empty,
  Tabs,
  Alert,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  StarOutlined,
  BookOutlined,
  FilterOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  RocketOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import {
  TUTORIAL_DEFINITIONS,
  TUTORIAL_CATEGORIES_CONFIG,
  LEARNING_PATHS,
  getRecommendedTutorials
} from './TutorialContent';
import { TUTORIAL_STATUS, TUTORIAL_CATEGORIES } from './types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// Styled Components
const TutorialCard = styled(Card)`
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #1890ff;
  }
  
  .ant-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
`;

const TutorialMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
`;

const CategoryBadge = styled(Badge)`
  .ant-badge-count {
    background-color: ${props => props.color || '#1890ff'};
  }
`;

const DifficultyStars = styled.div`
  display: flex;
  gap: 2px;
`;

const FilterContainer = styled.div`
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
`;

const TutorialLauncher = ({ visible, onClose, initialCategory = null }) => {
  const {
    startTutorial,
    getTutorialProgress,
    getAllTutorials,
    getStatistics
  } = useTutorial();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [selectedDifficulty, setSelectedDifficulty] = useState(null);
  const [showCompleted, setShowCompleted] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  const statistics = getStatistics();
  const allTutorials = Object.values(TUTORIAL_DEFINITIONS);

  // Filter tutorials based on current filters
  const filteredTutorials = useMemo(() => {
    return allTutorials.filter(tutorial => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          tutorial.title.toLowerCase().includes(searchLower) ||
          tutorial.description.toLowerCase().includes(searchLower) ||
          tutorial.tags?.some(tag => tag.toLowerCase().includes(searchLower));

        if (!matchesSearch) return false;
      }

      // Category filter
      if (selectedCategory && tutorial.category !== selectedCategory) {
        return false;
      }

      // Difficulty filter
      if (selectedDifficulty && tutorial.difficulty !== selectedDifficulty) {
        return false;
      }

      // Completion filter
      if (!showCompleted) {
        const progress = getTutorialProgress(tutorial.id);
        if (progress?.status === TUTORIAL_STATUS.COMPLETED) {
          return false;
        }
      }

      return true;
    });
  }, [allTutorials, searchTerm, selectedCategory, selectedDifficulty, showCompleted, getTutorialProgress]);

  // Get recommended tutorials
  const recommendedTutorials = useMemo(() => {
    const completedTutorialIds = allTutorials
      .filter(tutorial => {
        const progress = getTutorialProgress(tutorial.id);
        return progress?.status === TUTORIAL_STATUS.COMPLETED;
      })
      .map(tutorial => tutorial.id);

    return getRecommendedTutorials(completedTutorialIds).slice(0, 6);
  }, [allTutorials, getTutorialProgress]);

  const handleStartTutorial = (tutorialId) => {
    startTutorial(tutorialId);
    onClose();
  };

  const getDifficultyStars = (difficulty) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarOutlined
        key={i}
        style={{
          color: i < difficulty ? '#faad14' : '#d9d9d9',
          fontSize: '12px'
        }}
      />
    ));
  };

  const getTutorialStatus = (tutorial) => {
    const progress = getTutorialProgress(tutorial.id);
    return progress?.status || TUTORIAL_STATUS.NOT_STARTED;
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case TUTORIAL_STATUS.COMPLETED:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case TUTORIAL_STATUS.IN_PROGRESS:
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case TUTORIAL_STATUS.SKIPPED:
        return <Text type="secondary">Skipped</Text>;
      default:
        return null;
    }
  };

  const renderTutorialCard = (tutorial) => {
    const status = getTutorialStatus(tutorial);
    const progress = getTutorialProgress(tutorial.id);
    const categoryConfig = TUTORIAL_CATEGORIES_CONFIG[tutorial.category];

    return (
      <Col xs={24} sm={12} lg={8} key={tutorial.id}>
        <TutorialCard
          onClick={() => handleStartTutorial(tutorial.id)}
          actions={[
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleStartTutorial(tutorial.id);
              }}
            >
              {status === TUTORIAL_STATUS.IN_PROGRESS ? 'Continue' : 'Start'}
            </Button>
          ]}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <div style={{ fontSize: '24px', color: categoryConfig.color }}>
                {tutorial.icon || categoryConfig.icon}
              </div>
              {getStatusIcon(status)}
            </div>

            <Title level={5} style={{ margin: 0 }}>
              {tutorial.title}
            </Title>

            <Paragraph
              ellipsis={{ rows: 2 }}
              style={{ margin: 0, color: '#666' }}
            >
              {tutorial.description}
            </Paragraph>

            {tutorial.prerequisites.length > 0 && (
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Prerequisites:
                </Text>
                <div style={{ marginTop: '4px' }}>
                  {tutorial.prerequisites.map(prereqId => {
                    const prereq = TUTORIAL_DEFINITIONS[prereqId];
                    const prereqStatus = getTutorialStatus({ id: prereqId });
                    return (
                      <Tag
                        key={prereqId}
                        size="small"
                        color={prereqStatus === TUTORIAL_STATUS.COMPLETED ? 'green' : 'default'}
                      >
                        {prereq?.title || prereqId}
                      </Tag>
                    );
                  })}
                </div>
              </div>
            )}

            {status === TUTORIAL_STATUS.IN_PROGRESS && progress && (
              <Progress
                percent={(progress.completedSteps.length / tutorial.steps.length) * 100}
                size="small"
                showInfo={false}
              />
            )}
          </Space>

          <TutorialMeta>
            <Space size="small">
              <CategoryBadge
                count={tutorial.category.replace('_', ' ')}
                color={categoryConfig.color}
              />
              <Tooltip title="Difficulty">
                <DifficultyStars>
                  {getDifficultyStars(tutorial.difficulty)}
                </DifficultyStars>
              </Tooltip>
            </Space>

            <Space size="small">
              <ClockCircleOutlined />
              <Text type="secondary">{tutorial.estimatedDuration}min</Text>
            </Space>
          </TutorialMeta>
        </TutorialCard>
      </Col>
    );
  };

  const renderLearningPath = (pathKey, path) => (
    <Card key={pathKey} style={{ marginBottom: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5} style={{ margin: 0 }}>
            {path.title}
          </Title>
          <Space>
            <ClockCircleOutlined />
            <Text type="secondary">{path.estimatedDuration}min</Text>
          </Space>
        </div>

        <Text type="secondary">{path.description}</Text>

        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
          {path.tutorials.map((tutorialId, index) => {
            const tutorial = TUTORIAL_DEFINITIONS[tutorialId];
            const status = getTutorialStatus({ id: tutorialId });

            return (
              <Tag
                key={tutorialId}
                color={status === TUTORIAL_STATUS.COMPLETED ? 'green' : 'blue'}
                style={{ cursor: 'pointer' }}
                onClick={() => handleStartTutorial(tutorialId)}
              >
                {index + 1}. {tutorial?.title || tutorialId}
              </Tag>
            );
          })}
        </div>

        <Button
          type="primary"
          icon={<RocketOutlined />}
          onClick={() => handleStartTutorial(path.tutorials[0])}
        >
          Start Learning Path
        </Button>
      </Space>
    </Card>
  );

  return (
    <Modal
      title={
        <Space>
          <BookOutlined />
          Tutorial Center
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      style={{ top: 20 }}
    >
      <div style={{ maxHeight: '80vh', overflowY: 'auto' }}>
        {/* Statistics Overview */}
        <Alert
          message={`Welcome back! You've completed ${statistics.totalTutorialsCompleted} tutorials and earned ${statistics.badgesEarned} badges.`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* All Tutorials Tab */}
          <TabPane tab="All Tutorials" key="all">
            {/* Filters */}
            <FilterContainer>
              <Row gutter={16}>
                <Col span={8}>
                  <Input
                    placeholder="Search tutorials..."
                    prefix={<SearchOutlined />}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </Col>
                <Col span={6}>
                  <Select
                    placeholder="Category"
                    value={selectedCategory}
                    onChange={setSelectedCategory}
                    allowClear
                    style={{ width: '100%' }}
                  >
                    {Object.entries(TUTORIAL_CATEGORIES_CONFIG).map(([key, config]) => (
                      <Option key={key} value={key}>
                        {config.title}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={6}>
                  <Select
                    placeholder="Difficulty"
                    value={selectedDifficulty}
                    onChange={setSelectedDifficulty}
                    allowClear
                    style={{ width: '100%' }}
                  >
                    <Option value={1}>Beginner (⭐)</Option>
                    <Option value={2}>Easy (⭐⭐)</Option>
                    <Option value={3}>Intermediate (⭐⭐⭐)</Option>
                    <Option value={4}>Advanced (⭐⭐⭐⭐)</Option>
                    <Option value={5}>Expert (⭐⭐⭐⭐⭐)</Option>
                  </Select>
                </Col>
                <Col span={4}>
                  <Button
                    icon={<FilterOutlined />}
                    onClick={() => setShowCompleted(!showCompleted)}
                    type={showCompleted ? 'default' : 'primary'}
                  >
                    {showCompleted ? 'Hide Completed' : 'Show All'}
                  </Button>
                </Col>
              </Row>
            </FilterContainer>

            {/* Tutorial Grid */}
            {filteredTutorials.length > 0 ? (
              <Row gutter={[16, 16]}>
                {filteredTutorials.map(renderTutorialCard)}
              </Row>
            ) : (
              <Empty
                description="No tutorials match your filters"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </TabPane>

          {/* Recommended Tab */}
          <TabPane tab="Recommended" key="recommended">
            <Title level={4}>Recommended for You</Title>
            <Paragraph type="secondary">
              Based on your progress, here are tutorials we recommend you try next:
            </Paragraph>

            {recommendedTutorials.length > 0 ? (
              <Row gutter={[16, 16]}>
                {recommendedTutorials.map(renderTutorialCard)}
              </Row>
            ) : (
              <Empty
                description="You've completed all available tutorials! Check back for new content."
                image={<TrophyOutlined style={{ fontSize: 64, color: '#faad14' }} />}
              />
            )}
          </TabPane>

          {/* Learning Paths Tab */}
          <TabPane tab="Learning Paths" key="paths">
            <Title level={4}>Structured Learning Paths</Title>
            <Paragraph type="secondary">
              Follow these curated paths to master specific aspects of App Builder:
            </Paragraph>

            {Object.entries(LEARNING_PATHS).map(([pathKey, path]) =>
              renderLearningPath(pathKey, path)
            )}
          </TabPane>
        </Tabs>
      </div>
    </Modal>
  );
};

export default TutorialLauncher;
