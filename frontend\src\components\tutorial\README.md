# Tutorial System Documentation

## Overview

The Tutorial System is a comprehensive guided learning solution for the App Builder application. It provides interactive tutorials, contextual help, progress tracking, and gamification features to help users learn and master the App Builder interface.

## Features

### 🎯 Interactive Tutorial Assistant
- **Element Highlighting**: Automatically highlights UI elements during tutorials
- **Step-by-step Guidance**: Progressive walkthroughs with clear instructions
- **Progress Tracking**: Tracks completion status and allows pause/resume
- **Keyboard Navigation**: Full keyboard accessibility support

### 🧠 Context-Aware Help System
- **Smart Tooltips**: Context-sensitive help based on user actions
- **Hover Assistance**: Helpful hints when users hover over elements
- **Struggle Detection**: Identifies when users need help and offers assistance
- **Related Tutorials**: Suggests relevant tutorials based on current context

### 📊 Progress & Gamification
- **Achievement Badges**: Earn badges for completing tutorials and milestones
- **Progress Dashboard**: Visual progress tracking across all tutorials
- **Learning Paths**: Structured sequences of tutorials for different user types
- **Completion Certificates**: Downloadable certificates for completed learning paths

### ♿ Accessibility Features
- **Screen Reader Support**: Full ARIA compliance and screen reader announcements
- **Keyboard Navigation**: Complete keyboard-only navigation support
- **High Contrast Mode**: Automatic detection and adaptation to high contrast themes
- **Focus Management**: Proper focus trapping and restoration

## Architecture

### Core Components

```
tutorial/
├── types.js                 # Type definitions and constants
├── TutorialStorage.js       # LocalStorage management
├── TutorialManager.js       # Central state management (React Context)
├── TutorialOverlay.js       # Main tutorial overlay component
├── TutorialProgress.js      # Progress tracking and display
├── ContextualHelp.js        # Context-aware help system
├── TutorialTrigger.js       # Floating action button for tutorial access
├── TutorialContent.js       # Predefined tutorial definitions
├── TutorialLauncher.js      # Tutorial selection and launch interface
├── TutorialDashboard.js     # Comprehensive analytics dashboard
├── TutorialBadges.js        # Achievement and badge system
├── TutorialAdmin.js         # Administrative interface for creating tutorials
├── TutorialAccessibility.js # Accessibility enhancements
├── withTutorialSupport.js   # HOC for adding tutorial support to components
└── index.js                 # Main exports
```

### Data Flow

1. **Tutorial Registration**: Tutorials are registered with the TutorialManager
2. **User Interaction**: Users trigger tutorials through various entry points
3. **State Management**: TutorialManager handles all tutorial state
4. **Storage**: Progress and preferences are persisted to localStorage
5. **Rendering**: Components react to state changes and render accordingly

## Quick Start

### Basic Integration

```jsx
import React from 'react';
import {
  TutorialProvider,
  TutorialOverlay,
  ContextualHelp,
  TutorialTrigger,
  TutorialRegistration
} from './components/tutorial';

function App() {
  return (
    <TutorialProvider userId="current-user">
      {/* Your app content */}
      <YourAppContent />
      
      {/* Tutorial System Components */}
      <TutorialRegistration />
      <TutorialOverlay />
      <ContextualHelp />
      <TutorialTrigger />
    </TutorialProvider>
  );
}
```

### Adding Tutorial Support to Components

```jsx
import { withComponentPaletteHelp } from './components/tutorial';

// Enhance existing component with tutorial support
const EnhancedComponentPalette = withComponentPaletteHelp(ComponentPalette);

// Or use custom tutorial support
const CustomComponent = withTutorialSupport(MyComponent, {
  helpContext: 'custom_context',
  tutorialId: 'custom_tutorial',
  helpTriggers: ['hover', 'focus']
});
```

### Using Tutorial Hooks

```jsx
import { useTutorial } from './components/tutorial';

function MyComponent() {
  const {
    startTutorial,
    isActive,
    currentStep,
    nextStep,
    previousStep
  } = useTutorial();

  return (
    <div>
      <button onClick={() => startTutorial('getting_started')}>
        Start Tutorial
      </button>
      {isActive && (
        <div>
          Current Step: {currentStep?.title}
          <button onClick={nextStep}>Next</button>
          <button onClick={previousStep}>Previous</button>
        </div>
      )}
    </div>
  );
}
```

## Creating Custom Tutorials

### Tutorial Definition

```jsx
import { createTutorial, createTutorialStep, TUTORIAL_CATEGORIES } from './types';

const myTutorial = createTutorial({
  id: 'my_custom_tutorial',
  title: 'My Custom Tutorial',
  description: 'Learn how to use custom features',
  category: TUTORIAL_CATEGORIES.INTERMEDIATE,
  difficulty: 3,
  estimatedDuration: 10,
  steps: [
    createTutorialStep({
      id: 'step1',
      title: 'Welcome',
      content: 'Welcome to the custom tutorial!',
      type: 'modal'
    }),
    createTutorialStep({
      id: 'step2',
      title: 'Find the Button',
      content: 'Click on the highlighted button',
      type: 'highlight',
      targetSelector: '[data-tutorial-target="my-button"]',
      position: 'bottom'
    })
  ]
});
```

### Registering Tutorials

```jsx
import { useTutorial } from './components/tutorial';

function TutorialRegistration() {
  const { registerTutorial } = useTutorial();

  useEffect(() => {
    registerTutorial(myTutorial);
  }, [registerTutorial]);

  return null;
}
```

## Tutorial Step Types

### Available Step Types

- **`highlight`**: Highlights a specific element with overlay
- **`modal`**: Shows a modal dialog with content
- **`tooltip`**: Shows a tooltip near the target element
- **`overlay`**: Full-screen overlay with content
- **`interactive`**: Requires user interaction to proceed
- **`wait`**: Waits for a condition or user action

### Step Configuration Options

```jsx
createTutorialStep({
  id: 'unique_step_id',
  type: 'highlight',
  title: 'Step Title',
  content: 'Step description and instructions',
  targetSelector: '[data-help-context="component-palette"]',
  position: 'bottom', // 'top', 'bottom', 'left', 'right', 'auto'
  showSkip: true,
  showPrevious: true,
  showNext: true,
  autoAdvance: false,
  autoAdvanceDelay: 3000,
  requiredAction: 'click', // Action required to proceed
  validationFn: () => document.querySelectorAll('.component').length > 0,
  onEnter: (step, tutorial) => console.log('Step entered'),
  onExit: (step, tutorial) => console.log('Step exited'),
  highlightPadding: 8,
  highlightBorderRadius: 4
})
```

## Contextual Help

### Adding Help Context to Elements

```jsx
// Add data attributes to enable contextual help
<div data-help-context="component-palette">
  Component Palette Content
</div>

<div data-help-context="preview-area">
  Preview Area Content
</div>

<div data-help-context="property-editor">
  Property Editor Content
</div>
```

### Available Help Contexts

- `component_palette`: Component selection area
- `preview_area`: Main canvas/preview area
- `property_editor`: Component property editing panel
- `drag_drop`: Drag and drop interactions
- `theme_manager`: Theme customization interface
- `layout_designer`: Layout design tools
- `code_export`: Code export functionality
- `websocket`: WebSocket/real-time features

## Accessibility

### Screen Reader Support

The tutorial system provides comprehensive screen reader support:

- **Announcements**: Tutorial state changes are announced
- **ARIA Labels**: All interactive elements have proper ARIA labels
- **Live Regions**: Dynamic content updates are announced
- **Focus Management**: Focus is properly managed during tutorials

### Keyboard Navigation

- **Arrow Keys**: Navigate between tutorial steps
- **Space**: Pause/resume tutorial
- **Escape**: Exit tutorial
- **Tab**: Navigate within tutorial interface
- **Enter**: Activate buttons and proceed

### High Contrast Mode

The system automatically detects and adapts to:
- Windows High Contrast mode
- Forced colors preference
- High contrast preference

## Storage and Persistence

### Data Stored

- **Tutorial Progress**: Completion status, current step, time spent
- **User Preferences**: Settings like auto-start, help visibility
- **Achievement Badges**: Earned badges and completion dates
- **Help Context History**: Previously shown contextual help

### Storage Keys

```javascript
const STORAGE_KEYS = {
  TUTORIAL_PROGRESS: 'app_builder_tutorial_progress',
  TUTORIAL_PREFERENCES: 'app_builder_tutorial_preferences',
  HELP_CONTEXT_SHOWN: 'app_builder_help_context_shown',
  TUTORIAL_COMPLETION_BADGES: 'app_builder_tutorial_badges'
};
```

### Data Export/Import

```jsx
import tutorialStorage from './TutorialStorage';

// Export user data
const userData = tutorialStorage.exportTutorialData('user-id');

// Import user data
const success = tutorialStorage.importTutorialData(userData, 'user-id');
```

## Customization

### Theming

The tutorial system respects the application's theme:

```jsx
// Tutorial components automatically adapt to:
// - Light/dark themes
// - Custom color schemes
// - High contrast modes
// - Reduced motion preferences
```

### Animation Control

```jsx
// Disable animations for users who prefer reduced motion
const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

// Animation durations are automatically adjusted
const ANIMATION_DURATIONS = {
  HIGHLIGHT_FADE_IN: prefersReducedMotion ? 0 : 300,
  TOOLTIP_SHOW: prefersReducedMotion ? 0 : 200,
  // ...
};
```

## Testing

### Running Tests

```bash
# Run all tutorial system tests
npm test -- --testPathPattern=tutorial

# Run specific test suites
npm test TutorialSystem.test.js
npm test TutorialIntegration.test.js

# Run with coverage
npm test -- --coverage --testPathPattern=tutorial
```

### Test Structure

- **Unit Tests**: Individual component functionality
- **Integration Tests**: Component interaction and workflows
- **Accessibility Tests**: Screen reader and keyboard navigation
- **Performance Tests**: Rendering and interaction performance

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Tutorial components are loaded only when needed
2. **Event Delegation**: Efficient event handling for contextual help
3. **Debounced Updates**: Progress updates are debounced to reduce storage writes
4. **Virtual Scrolling**: Large tutorial lists use virtual scrolling
5. **Memory Management**: Proper cleanup of event listeners and timers

### Performance Monitoring

```jsx
// Built-in performance monitoring
const { renderTime, frameRate, memoryUsage } = usePreviewPerformance({
  enablePerformanceMonitoring: true
});
```

## Troubleshooting

### Common Issues

1. **Tutorial Not Starting**
   - Check if tutorial is registered
   - Verify user permissions
   - Check browser console for errors

2. **Elements Not Highlighting**
   - Verify target selector is correct
   - Check if element exists in DOM
   - Ensure element is visible

3. **Progress Not Saving**
   - Check localStorage availability
   - Verify user ID is set correctly
   - Check for storage quota limits

4. **Accessibility Issues**
   - Test with screen reader
   - Verify keyboard navigation
   - Check ARIA attributes

### Debug Mode

```jsx
// Enable debug mode for detailed logging
localStorage.setItem('tutorial_debug', 'true');

// Check tutorial system status
console.log('Registered tutorials:', getAllTutorials());
console.log('Current progress:', getTutorialProgress());
```

## API Reference

### TutorialManager Context

```jsx
const {
  // State
  tutorials,
  activeTutorial,
  currentStep,
  currentStepIndex,
  isActive,
  isPaused,
  progress,
  preferences,
  
  // Tutorial Management
  registerTutorial,
  unregisterTutorial,
  startTutorial,
  pauseTutorial,
  resumeTutorial,
  completeTutorial,
  skipTutorial,
  
  // Step Navigation
  nextStep,
  previousStep,
  goToStep,
  
  // Utilities
  getTutorial,
  getTutorialProgress,
  getAllTutorials,
  getAvailableTutorials,
  getStatistics,
  getBadges
} = useTutorial();
```

### Storage API

```jsx
import tutorialStorage from './TutorialStorage';

// Progress Management
tutorialStorage.getTutorialProgress(tutorialId, userId);
tutorialStorage.saveTutorialProgress(progress);
tutorialStorage.clearTutorialProgress(tutorialId, userId);

// Preferences
tutorialStorage.getTutorialPreferences();
tutorialStorage.saveTutorialPreferences(preferences);

// Badges
tutorialStorage.getTutorialBadges(userId);
tutorialStorage.addTutorialBadge(badge, userId);

// Statistics
tutorialStorage.getTutorialStatistics(userId);
```

## Contributing

### Adding New Tutorials

1. Create tutorial definition in `TutorialContent.js`
2. Add to appropriate category
3. Test tutorial flow
4. Update documentation

### Extending Functionality

1. Follow existing patterns and conventions
2. Add comprehensive tests
3. Update TypeScript definitions
4. Document new features

### Code Style

- Use TypeScript for type safety
- Follow React best practices
- Implement proper error handling
- Add accessibility features
- Write comprehensive tests

## Examples

### Complete Integration Example

```jsx
import React from 'react';
import {
  TutorialProvider,
  TutorialOverlay,
  ContextualHelp,
  TutorialTrigger,
  TutorialRegistration,
  withComponentPaletteHelp,
  withPreviewAreaHelp,
  withPropertyEditorHelp
} from './components/tutorial';

// Enhanced components
const EnhancedComponentPalette = withComponentPaletteHelp(ComponentPalette);
const EnhancedPreviewArea = withPreviewAreaHelp(PreviewArea);
const EnhancedPropertyEditor = withPropertyEditorHelp(PropertyEditor);

function AppBuilderWithTutorials() {
  return (
    <TutorialProvider userId="current-user">
      <div className="app-builder">
        <EnhancedComponentPalette />
        <EnhancedPreviewArea />
        <EnhancedPropertyEditor />

        {/* Tutorial System */}
        <TutorialRegistration />
        <TutorialOverlay />
        <ContextualHelp />
        <TutorialTrigger />
      </div>
    </TutorialProvider>
  );
}
```

### Custom Tutorial Example

```jsx
import { createTutorial, createTutorialStep } from './types';

const customTutorial = createTutorial({
  id: 'advanced_features',
  title: 'Advanced Features',
  description: 'Learn advanced App Builder features',
  category: 'advanced',
  difficulty: 4,
  estimatedDuration: 15,
  steps: [
    createTutorialStep({
      id: 'intro',
      type: 'modal',
      title: 'Advanced Features',
      content: 'This tutorial covers advanced features like custom components and API integration.'
    }),
    createTutorialStep({
      id: 'custom_component',
      type: 'highlight',
      title: 'Custom Components',
      content: 'Learn how to create custom components',
      targetSelector: '[data-feature="custom-components"]',
      position: 'right'
    }),
    createTutorialStep({
      id: 'api_integration',
      type: 'interactive',
      title: 'API Integration',
      content: 'Connect your app to external APIs',
      targetSelector: '[data-feature="api-panel"]',
      requiredAction: 'click',
      validationFn: () => document.querySelector('.api-connected')
    })
  ]
});
```

## License

This tutorial system is part of the App Builder project and follows the same license terms.
