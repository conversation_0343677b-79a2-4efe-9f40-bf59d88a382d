<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .log {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        button:hover {
            background-color: #0069d9;
        }
        .error-item {
            border-bottom: 1px solid #eee;
            padding: 8px 0;
        }
        .error-item .timestamp {
            color: #6c757d;
            font-size: 0.85em;
        }
        .error-item .message {
            color: #dc3545;
            font-weight: bold;
        }
        .error-item .stack {
            color: #6c757d;
            font-size: 0.9em;
            white-space: pre-wrap;
            margin-top: 5px;
        }
        .error-item .url {
            color: #007bff;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .stats {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .stat-item {
            flex: 1;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            background-color: #f8f9fa;
        }
        .stat-item .value {
            font-size: 1.5em;
            font-weight: bold;
        }
        .stat-item .label {
            font-size: 0.9em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Error Monitor</h1>
        
        <div class="card">
            <h2>Error Statistics</h2>
            <div class="stats">
                <div class="stat-item">
                    <div class="value" id="totalErrors">0</div>
                    <div class="label">Total Errors</div>
                </div>
                <div class="stat-item">
                    <div class="value" id="uniqueErrors">0</div>
                    <div class="label">Unique Errors</div>
                </div>
                <div class="stat-item">
                    <div class="value" id="lastErrorTime">-</div>
                    <div class="label">Last Error</div>
                </div>
            </div>
            <div style="margin-top: 10px;">
                <button id="startMonitoringBtn">Start Monitoring</button>
                <button id="stopMonitoringBtn" disabled>Stop Monitoring</button>
                <button id="clearErrorsBtn">Clear Errors</button>
            </div>
        </div>
        
        <div class="card">
            <h2>Error Log</h2>
            <div id="errorLog" class="log">
                <div class="error-item">
                    <div class="timestamp">Monitoring not started</div>
                    <div class="message">Click "Start Monitoring" to begin capturing errors</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>Test Errors</h2>
            <div style="margin-top: 10px;">
                <button id="generateErrorBtn">Generate Error</button>
                <button id="generatePromiseErrorBtn">Generate Promise Error</button>
                <button id="generateFetchErrorBtn">Generate Fetch Error</button>
            </div>
        </div>
        
        <div class="card">
            <h2>Network Test</h2>
            <div style="margin-top: 10px;">
                <button id="testBackendBtn">Test Backend Connection</button>
                <button id="testWebSocketBtn">Test WebSocket Connection</button>
            </div>
            <div id="networkTestResult" class="status warning" style="margin-top: 10px;">
                Click a button to test network connectivity
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const totalErrorsEl = document.getElementById('totalErrors');
        const uniqueErrorsEl = document.getElementById('uniqueErrors');
        const lastErrorTimeEl = document.getElementById('lastErrorTime');
        const errorLogEl = document.getElementById('errorLog');
        const networkTestResultEl = document.getElementById('networkTestResult');
        
        // Buttons
        const startMonitoringBtn = document.getElementById('startMonitoringBtn');
        const stopMonitoringBtn = document.getElementById('stopMonitoringBtn');
        const clearErrorsBtn = document.getElementById('clearErrorsBtn');
        const generateErrorBtn = document.getElementById('generateErrorBtn');
        const generatePromiseErrorBtn = document.getElementById('generatePromiseErrorBtn');
        const generateFetchErrorBtn = document.getElementById('generateFetchErrorBtn');
        const testBackendBtn = document.getElementById('testBackendBtn');
        const testWebSocketBtn = document.getElementById('testWebSocketBtn');
        
        // Error tracking
        let isMonitoring = false;
        let originalErrorHandler = window.onerror;
        let originalUnhandledRejectionHandler = window.onunhandledrejection;
        let errors = [];
        let uniqueErrorMessages = new Set();
        
        // Start error monitoring
        function startMonitoring() {
            if (isMonitoring) return;
            
            // Save original handlers
            originalErrorHandler = window.onerror;
            originalUnhandledRejectionHandler = window.onunhandledrejection;
            
            // Set up error handler
            window.onerror = function(message, source, lineno, colno, error) {
                handleError({
                    type: 'error',
                    message: message,
                    source: source,
                    lineno: lineno,
                    colno: colno,
                    error: error,
                    timestamp: new Date()
                });
                
                // Call original handler if it exists
                if (typeof originalErrorHandler === 'function') {
                    return originalErrorHandler(message, source, lineno, colno, error);
                }
                
                return false;
            };
            
            // Set up unhandled promise rejection handler
            window.onunhandledrejection = function(event) {
                const error = event.reason;
                handleError({
                    type: 'unhandledrejection',
                    message: error instanceof Error ? error.message : String(error),
                    error: error,
                    timestamp: new Date()
                });
                
                // Call original handler if it exists
                if (typeof originalUnhandledRejectionHandler === 'function') {
                    return originalUnhandledRejectionHandler(event);
                }
            };
            
            isMonitoring = true;
            startMonitoringBtn.disabled = true;
            stopMonitoringBtn.disabled = false;
            
            addErrorLogEntry({
                type: 'info',
                message: 'Error monitoring started',
                timestamp: new Date()
            });
        }
        
        // Stop error monitoring
        function stopMonitoring() {
            if (!isMonitoring) return;
            
            // Restore original handlers
            window.onerror = originalErrorHandler;
            window.onunhandledrejection = originalUnhandledRejectionHandler;
            
            isMonitoring = false;
            startMonitoringBtn.disabled = false;
            stopMonitoringBtn.disabled = true;
            
            addErrorLogEntry({
                type: 'info',
                message: 'Error monitoring stopped',
                timestamp: new Date()
            });
        }
        
        // Handle captured error
        function handleError(errorData) {
            errors.push(errorData);
            uniqueErrorMessages.add(errorData.message);
            
            updateErrorStats();
            addErrorLogEntry(errorData);
        }
        
        // Update error statistics
        function updateErrorStats() {
            totalErrorsEl.textContent = errors.length;
            uniqueErrorsEl.textContent = uniqueErrorMessages.size;
            
            if (errors.length > 0) {
                const lastError = errors[errors.length - 1];
                lastErrorTimeEl.textContent = lastError.timestamp.toLocaleTimeString();
            } else {
                lastErrorTimeEl.textContent = '-';
            }
        }
        
        // Add entry to error log
        function addErrorLogEntry(errorData) {
            const errorItem = document.createElement('div');
            errorItem.className = 'error-item';
            
            const timestamp = document.createElement('div');
            timestamp.className = 'timestamp';
            timestamp.textContent = errorData.timestamp.toLocaleTimeString();
            
            const message = document.createElement('div');
            message.className = 'message';
            
            if (errorData.type === 'info') {
                message.style.color = '#28a745';
            }
            
            message.textContent = errorData.message;
            
            errorItem.appendChild(timestamp);
            errorItem.appendChild(message);
            
            if (errorData.error && errorData.error.stack) {
                const stack = document.createElement('div');
                stack.className = 'stack';
                stack.textContent = errorData.error.stack;
                errorItem.appendChild(stack);
            }
            
            if (errorData.source) {
                const url = document.createElement('div');
                url.className = 'url';
                url.textContent = `${errorData.source} (${errorData.lineno}:${errorData.colno})`;
                errorItem.appendChild(url);
            }
            
            errorLogEl.appendChild(errorItem);
            errorLogEl.scrollTop = errorLogEl.scrollHeight;
        }
        
        // Clear errors
        function clearErrors() {
            errors = [];
            uniqueErrorMessages.clear();
            updateErrorStats();
            
            errorLogEl.innerHTML = '';
            addErrorLogEntry({
                type: 'info',
                message: 'Error log cleared',
                timestamp: new Date()
            });
        }
        
        // Generate test error
        function generateError() {
            try {
                // Generate a reference error
                const nonExistentVariable = undefinedVariable;
            } catch (error) {
                if (isMonitoring) {
                    // Error will be caught by the global handler
                } else {
                    addErrorLogEntry({
                        type: 'error',
                        message: 'Test error: ' + error.message,
                        error: error,
                        timestamp: new Date()
                    });
                }
            }
        }
        
        // Generate promise error
        function generatePromiseError() {
            new Promise((resolve, reject) => {
                reject(new Error('Test promise rejection'));
            }).catch(error => {
                if (!isMonitoring) {
                    addErrorLogEntry({
                        type: 'error',
                        message: 'Caught promise error: ' + error.message,
                        error: error,
                        timestamp: new Date()
                    });
                }
            });
            
            // This one will be uncaught
            new Promise((resolve, reject) => {
                setTimeout(() => {
                    reject(new Error('Test uncaught promise rejection'));
                }, 100);
            });
        }
        
        // Generate fetch error
        function generateFetchError() {
            fetch('http://non-existent-domain.example/api')
                .then(response => response.json())
                .catch(error => {
                    if (!isMonitoring) {
                        addErrorLogEntry({
                            type: 'error',
                            message: 'Caught fetch error: ' + error.message,
                            error: error,
                            timestamp: new Date()
                        });
                    }
                });
        }
        
        // Test backend connection
        function testBackendConnection() {
            networkTestResultEl.className = 'status warning';
            networkTestResultEl.textContent = 'Testing backend connection...';
            
            fetch('/api/health-check')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    networkTestResultEl.className = 'status success';
                    networkTestResultEl.textContent = `Backend connection successful: ${JSON.stringify(data)}`;
                })
                .catch(error => {
                    networkTestResultEl.className = 'status error';
                    networkTestResultEl.textContent = `Backend connection failed: ${error.message}`;
                });
        }
        
        // Test WebSocket connection
        function testWebSocketConnection() {
            networkTestResultEl.className = 'status warning';
            networkTestResultEl.textContent = 'Testing WebSocket connection...';
            
            try {
                const socket = new WebSocket('ws://localhost:8000/ws/app_builder/');
                
                socket.onopen = function() {
                    networkTestResultEl.className = 'status success';
                    networkTestResultEl.textContent = 'WebSocket connection successful';
                    
                    // Close the socket after successful test
                    setTimeout(() => {
                        socket.close(1000, 'Test completed');
                    }, 1000);
                };
                
                socket.onerror = function(error) {
                    networkTestResultEl.className = 'status error';
                    networkTestResultEl.textContent = 'WebSocket connection failed';
                    console.error('WebSocket error:', error);
                };
                
                socket.onclose = function(event) {
                    if (event.code !== 1000) {
                        networkTestResultEl.className = 'status error';
                        networkTestResultEl.textContent = `WebSocket connection closed with code ${event.code}`;
                    }
                };
            } catch (error) {
                networkTestResultEl.className = 'status error';
                networkTestResultEl.textContent = `WebSocket connection error: ${error.message}`;
            }
        }
        
        // Event Listeners
        startMonitoringBtn.addEventListener('click', startMonitoring);
        stopMonitoringBtn.addEventListener('click', stopMonitoring);
        clearErrorsBtn.addEventListener('click', clearErrors);
        generateErrorBtn.addEventListener('click', generateError);
        generatePromiseErrorBtn.addEventListener('click', generatePromiseError);
        generateFetchErrorBtn.addEventListener('click', generateFetchError);
        testBackendBtn.addEventListener('click', testBackendConnection);
        testWebSocketBtn.addEventListener('click', testWebSocketConnection);
        
        // Initialize
        addErrorLogEntry({
            type: 'info',
            message: 'Error Monitor initialized',
            timestamp: new Date()
        });
    </script>
</body>
</html>
