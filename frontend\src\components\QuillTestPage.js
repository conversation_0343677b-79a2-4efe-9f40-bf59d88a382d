import React, { useState } from 'react';
import { <PERSON>, Typography, Space, Button, message, Divider } from 'antd';
import { FileTextOutlined, SaveOutlined, ClearOutlined } from '@ant-design/icons';
import SharedEditor from './SharedEditor';

const { Title, Text } = Typography;

/**
 * QuillTestPage component
 * A test page to verify Quill.js integration and functionality
 */
const QuillTestPage = () => {
  const [content1, setContent1] = useState('<p>Welcome to the Quill.js test page!</p><p>This editor demonstrates rich text editing capabilities.</p>');
  const [content2, setContent2] = useState('');
  const [savedContent, setSavedContent] = useState('');

  const handleSaveContent = () => {
    setSavedContent(content1);
    message.success('Content saved successfully!');
  };

  const handleClearContent = () => {
    setContent1('');
    setContent2('');
    message.info('Content cleared');
  };

  const handleLoadSavedContent = () => {
    if (savedContent) {
      setContent2(savedContent);
      message.success('Saved content loaded!');
    } else {
      message.warning('No saved content available');
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>
          <FileTextOutlined style={{ marginRight: '8px' }} />
          Quill.js Integration Test
        </Title>
        <Text type="secondary">
          This page demonstrates the integration and functionality of Quill.js rich text editor 
          in the App Builder application.
        </Text>
      </Card>

      <Divider />

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Primary Editor */}
        <Card title="Primary Rich Text Editor" size="small">
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            Test basic rich text editing features: bold, italic, lists, headers, etc.
          </Text>
          <SharedEditor
            documentId="test-doc-1"
            userId="test-user-1"
            username="Test User"
            title="Rich Text Editor"
            height={300}
            onContentChange={setContent1}
          />
          <div style={{ marginTop: '16px' }}>
            <Space>
              <Button 
                type="primary" 
                icon={<SaveOutlined />} 
                onClick={handleSaveContent}
              >
                Save Content
              </Button>
              <Button 
                icon={<ClearOutlined />} 
                onClick={handleClearContent}
              >
                Clear All
              </Button>
            </Space>
          </div>
        </Card>

        {/* Secondary Editor */}
        <Card title="Secondary Editor (Read-Only Mode)" size="small">
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            This editor demonstrates read-only mode and content loading.
          </Text>
          <SharedEditor
            documentId="test-doc-2"
            userId="test-user-2"
            username="Test User 2"
            title="Read-Only Editor"
            height={200}
            readOnly={true}
            onContentChange={setContent2}
          />
          <div style={{ marginTop: '16px' }}>
            <Button onClick={handleLoadSavedContent}>
              Load Saved Content
            </Button>
          </div>
        </Card>

        {/* Content Preview */}
        <Card title="Content Preview" size="small">
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            Raw HTML content from the editor:
          </Text>
          <div style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '12px',
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            {content1 || '<em>No content</em>'}
          </div>
        </Card>

        {/* Feature Checklist */}
        <Card title="Feature Verification Checklist" size="small">
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
            <div>
              <Title level={5}>Basic Formatting</Title>
              <ul style={{ fontSize: '14px' }}>
                <li>Bold text (Ctrl+B)</li>
                <li>Italic text (Ctrl+I)</li>
                <li>Underline text (Ctrl+U)</li>
                <li>Strikethrough text</li>
              </ul>
            </div>
            <div>
              <Title level={5}>Structure</Title>
              <ul style={{ fontSize: '14px' }}>
                <li>Headers (H1, H2)</li>
                <li>Bullet lists</li>
                <li>Numbered lists</li>
                <li>Blockquotes</li>
              </ul>
            </div>
            <div>
              <Title level={5}>Advanced</Title>
              <ul style={{ fontSize: '14px' }}>
                <li>Text colors</li>
                <li>Background colors</li>
                <li>Code blocks</li>
                <li>Subscript/Superscript</li>
              </ul>
            </div>
            <div>
              <Title level={5}>Integration</Title>
              <ul style={{ fontSize: '14px' }}>
                <li>Ant Design styling</li>
                <li>Event handling</li>
                <li>Content persistence</li>
                <li>Read-only mode</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Performance Info */}
        <Card title="Performance & Bundle Information" size="small">
          <Text type="secondary">
            Quill.js adds approximately 400KB to the bundle size but provides comprehensive 
            rich text editing capabilities. The integration uses react-quill wrapper for 
            better React compatibility and includes CSS styling that integrates well with 
            the Ant Design theme.
          </Text>
        </Card>
      </Space>
    </div>
  );
};

export default QuillTestPage;
