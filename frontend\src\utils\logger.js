/**
 * Enhanced logging utility for the application
 * Provides consistent logging with additional features like:
 * - Log levels (debug, info, warn, error)
 * - Module/component tagging
 * - Timestamp
 * - Log persistence (optional)
 * - Log filtering
 * - Performance tracking
 */

// Log levels with numeric values for filtering
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
};

// Default configuration
const DEFAULT_CONFIG = {
  level: process.env.NODE_ENV === 'production' ? LOG_LEVELS.INFO : LOG_LEVELS.DEBUG,
  persist: false,
  persistLevel: LOG_LEVELS.ERROR,
  maxPersistedLogs: 100,
  showTimestamp: true,
  enableConsole: true
};

// Current configuration
let config = { ...DEFAULT_CONFIG };

// In-memory log storage
const logHistory = [];

// Performance measurements
const performanceMarks = {};

/**
 * Configure the logger
 * @param {Object} newConfig - Configuration options
 */
export function configureLogger(newConfig = {}) {
  config = { ...config, ...newConfig };
  
  // Log the configuration change
  debug('Logger', 'Logger configured', config);
}

/**
 * Log a debug message
 * @param {string} module - Module/component name
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
export function debug(module, message, data) {
  logWithLevel(LOG_LEVELS.DEBUG, module, message, data);
}

/**
 * Log an info message
 * @param {string} module - Module/component name
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
export function info(module, message, data) {
  logWithLevel(LOG_LEVELS.INFO, module, message, data);
}

/**
 * Log a warning message
 * @param {string} module - Module/component name
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
export function warn(module, message, data) {
  logWithLevel(LOG_LEVELS.WARN, module, message, data);
}

/**
 * Log an error message
 * @param {string} module - Module/component name
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
export function error(module, message, data) {
  logWithLevel(LOG_LEVELS.ERROR, module, message, data);
}

/**
 * Internal function to log with a specific level
 * @param {number} level - Log level
 * @param {string} module - Module/component name
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 * @private
 */
function logWithLevel(level, module, message, data) {
  if (level < config.level) return;
  
  const timestamp = config.showTimestamp ? new Date().toISOString() : null;
  const logEntry = {
    level,
    levelName: getLevelName(level),
    module,
    message,
    data,
    timestamp
  };
  
  // Add to history if persistence is enabled
  if (config.persist && level >= config.persistLevel) {
    logHistory.push(logEntry);
    
    // Trim history if it exceeds the maximum size
    if (logHistory.length > config.maxPersistedLogs) {
      logHistory.shift();
    }
  }
  
  // Output to console if enabled
  if (config.enableConsole) {
    const prefix = timestamp ? `[${timestamp}] ` : '';
    const modulePrefix = module ? `[${module}] ` : '';
    
    switch (level) {
      case LOG_LEVELS.DEBUG:
        console.debug(`${prefix}${modulePrefix}${message}`, data !== undefined ? data : '');
        break;
      case LOG_LEVELS.INFO:
        console.info(`${prefix}${modulePrefix}${message}`, data !== undefined ? data : '');
        break;
      case LOG_LEVELS.WARN:
        console.warn(`${prefix}${modulePrefix}${message}`, data !== undefined ? data : '');
        break;
      case LOG_LEVELS.ERROR:
        console.error(`${prefix}${modulePrefix}${message}`, data !== undefined ? data : '');
        break;
    }
  }
}

/**
 * Get the name of a log level
 * @param {number} level - Log level
 * @returns {string} Level name
 * @private
 */
function getLevelName(level) {
  switch (level) {
    case LOG_LEVELS.DEBUG: return 'DEBUG';
    case LOG_LEVELS.INFO: return 'INFO';
    case LOG_LEVELS.WARN: return 'WARN';
    case LOG_LEVELS.ERROR: return 'ERROR';
    default: return 'UNKNOWN';
  }
}

/**
 * Get all persisted logs
 * @param {number} [level] - Minimum log level to retrieve
 * @returns {Array} Array of log entries
 */
export function getLogs(level = LOG_LEVELS.DEBUG) {
  return logHistory.filter(entry => entry.level >= level);
}

/**
 * Clear all persisted logs
 */
export function clearLogs() {
  logHistory.length = 0;
  info('Logger', 'Logs cleared');
}

/**
 * Start a performance measurement
 * @param {string} label - Measurement label
 */
export function startPerformanceMeasurement(label) {
  performanceMarks[label] = {
    start: performance.now(),
    end: null,
    duration: null
  };
  debug('Performance', `Started measurement: ${label}`);
}

/**
 * End a performance measurement and log the result
 * @param {string} label - Measurement label
 * @param {boolean} [logResult=true] - Whether to log the result
 * @returns {number} Duration in milliseconds
 */
export function endPerformanceMeasurement(label, logResult = true) {
  if (!performanceMarks[label] || performanceMarks[label].end !== null) {
    warn('Performance', `Measurement not started or already ended: ${label}`);
    return -1;
  }
  
  performanceMarks[label].end = performance.now();
  performanceMarks[label].duration = performanceMarks[label].end - performanceMarks[label].start;
  
  if (logResult) {
    info('Performance', `${label}: ${performanceMarks[label].duration.toFixed(2)}ms`);
  }
  
  return performanceMarks[label].duration;
}

// Export constants and the logger itself as default
export const Logger = {
  debug,
  info,
  warn,
  error,
  configure: configureLogger,
  getLogs,
  clearLogs,
  startMeasurement: startPerformanceMeasurement,
  endMeasurement: endPerformanceMeasurement,
  LEVELS: LOG_LEVELS
};

export default Logger;
