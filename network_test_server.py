import http.server
import socketserver
import json
import logging
import sys
import os
import socket
import threading
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('network_test_server')

# HTML content for the test page
HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Connectivity Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .test-panel {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
        }
        .info {
            background-color: #d9edf7;
            color: #31708f;
        }
        .test-log {
            border: 1px solid #ddd;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            margin-top: 10px;
            font-family: monospace;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>Network Connectivity Test</h1>
    
    <div class="container">
        <div class="test-panel">
            <div class="test-title">HTTP Connectivity Test</div>
            <button id="httpTestBtn" class="test-button">Run HTTP Test</button>
            <div id="httpResult" class="test-result"></div>
        </div>
        
        <div class="test-panel">
            <div class="test-title">WebSocket Connectivity Test</div>
            <button id="wsTestBtn" class="test-button">Run WebSocket Test</button>
            <div id="wsResult" class="test-result"></div>
        </div>
        
        <div class="test-panel">
            <div class="test-title">TCP Port Scan</div>
            <div>
                <input type="text" id="hostInput" value="localhost" placeholder="Host">
                <input type="text" id="portsInput" value="8000,8765,3000" placeholder="Ports (comma-separated)">
                <button id="portScanBtn" class="test-button">Scan Ports</button>
            </div>
            <div id="portScanResult" class="test-result"></div>
        </div>
        
        <div class="test-panel">
            <div class="test-title">Test Log</div>
            <div id="testLog" class="test-log"></div>
        </div>
    </div>

    <script>
        // DOM Elements
        const httpTestBtn = document.getElementById('httpTestBtn');
        const wsTestBtn = document.getElementById('wsTestBtn');
        const portScanBtn = document.getElementById('portScanBtn');
        const httpResult = document.getElementById('httpResult');
        const wsResult = document.getElementById('wsResult');
        const portScanResult = document.getElementById('portScanResult');
        const testLog = document.getElementById('testLog');
        const hostInput = document.getElementById('hostInput');
        const portsInput = document.getElementById('portsInput');
        
        // Add log entry
        function addLog(message) {
            const logEntry = document.createElement('div');
            logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            testLog.appendChild(logEntry);
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        // HTTP Test
        httpTestBtn.addEventListener('click', async () => {
            httpResult.className = 'test-result info';
            httpResult.textContent = 'Running HTTP test...';
            addLog('Starting HTTP test');
            
            try {
                const response = await fetch('/api/test');
                const data = await response.json();
                
                httpResult.className = 'test-result success';
                httpResult.textContent = `HTTP test successful: ${JSON.stringify(data)}`;
                addLog(`HTTP test successful: ${JSON.stringify(data)}`);
            } catch (error) {
                httpResult.className = 'test-result error';
                httpResult.textContent = `HTTP test failed: ${error.message}`;
                addLog(`HTTP test failed: ${error.message}`);
            }
        });
        
        // WebSocket Test
        wsTestBtn.addEventListener('click', () => {
            wsResult.className = 'test-result info';
            wsResult.textContent = 'Running WebSocket test...';
            addLog('Starting WebSocket test');
            
            try {
                const ws = new WebSocket('ws://localhost:8765');
                
                ws.onopen = () => {
                    wsResult.className = 'test-result success';
                    wsResult.textContent = 'WebSocket connection established successfully';
                    addLog('WebSocket connection established successfully');
                    
                    // Send a test message
                    ws.send(JSON.stringify({
                        type: 'ping',
                        timestamp: new Date().toISOString()
                    }));
                    addLog('Sent ping message to WebSocket server');
                };
                
                ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`Received WebSocket message: ${JSON.stringify(data)}`);
                    } catch (e) {
                        addLog(`Received WebSocket message: ${event.data}`);
                    }
                };
                
                ws.onerror = (error) => {
                    wsResult.className = 'test-result error';
                    wsResult.textContent = 'WebSocket error occurred';
                    addLog(`WebSocket error: ${error}`);
                };
                
                ws.onclose = (event) => {
                    addLog(`WebSocket connection closed: Code ${event.code}, Reason: ${event.reason || 'No reason provided'}`);
                };
            } catch (error) {
                wsResult.className = 'test-result error';
                wsResult.textContent = `WebSocket test failed: ${error.message}`;
                addLog(`WebSocket test failed: ${error.message}`);
            }
        });
        
        // Port Scan
        portScanBtn.addEventListener('click', async () => {
            const host = hostInput.value.trim();
            const ports = portsInput.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p));
            
            portScanResult.className = 'test-result info';
            portScanResult.textContent = `Scanning ports on ${host}: ${ports.join(', ')}...`;
            addLog(`Starting port scan on ${host}: ${ports.join(', ')}`);
            
            try {
                const response = await fetch('/api/port-scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        host,
                        ports
                    })
                });
                
                const data = await response.json();
                
                portScanResult.className = 'test-result success';
                portScanResult.innerHTML = `Port scan results:<br>`;
                
                for (const [port, status] of Object.entries(data.results)) {
                    portScanResult.innerHTML += `Port ${port}: <span class="${status ? 'success' : 'error'}">${status ? 'OPEN' : 'CLOSED'}</span><br>`;
                    addLog(`Port ${port}: ${status ? 'OPEN' : 'CLOSED'}`);
                }
            } catch (error) {
                portScanResult.className = 'test-result error';
                portScanResult.textContent = `Port scan failed: ${error.message}`;
                addLog(`Port scan failed: ${error.message}`);
            }
        });
        
        // Initial log
        addLog('Network test page loaded');
    </script>
</body>
</html>
"""

class NetworkTestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(HTML.encode())
        elif self.path == '/api/test':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'status': 'success',
                'message': 'HTTP test successful',
                'timestamp': datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response).encode())
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def do_POST(self):
        if self.path == '/api/port-scan':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode())
            
            host = data.get('host', 'localhost')
            ports = data.get('ports', [8000, 8765, 3000])
            
            logger.info(f"Port scan request for {host}: {ports}")
            
            results = {}
            for port in ports:
                results[port] = is_port_open(host, port)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'status': 'success',
                'host': host,
                'results': results,
                'timestamp': datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response).encode())
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Not Found')

def is_port_open(host, port, timeout=1):
    """
    Check if a port is open on a host.
    
    Args:
        host: The host to check
        port: The port to check
        timeout: The timeout in seconds
        
    Returns:
        bool: True if the port is open, False otherwise
    """
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        logger.error(f"Error checking port {port} on {host}: {str(e)}")
        return False

def main():
    """
    Start the HTTP server.
    """
    port = 8080
    handler = NetworkTestHandler
    
    logger.info(f"Starting HTTP server on port {port}")
    
    with socketserver.TCPServer(("", port), handler) as httpd:
        logger.info(f"Server running at http://localhost:{port}")
        httpd.serve_forever()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
