"""
Integration tests for the complete export pipeline
Tests the entire flow from app data to validated exported code
"""

import json
import tempfile
import os
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from core.enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat, StyleFramework
from core.code_validator import CodeValidator, ValidationLevel
from my_app.models import App, LayoutTemplate, AppTemplate
from my_app.services.export_template_service import ExportTemplateService


class ExportPipelineIntegrationTests(TestCase):
    """Integration tests for the complete export pipeline"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.generator = EnhancedCodeGenerator()
        self.validator = CodeValidator()
        self.export_service = ExportTemplateService()
        
        # Create comprehensive test app data
        self.complex_app_data = {
            'components': [
                {
                    'id': 'header-comp',
                    'type': 'Header',
                    'props': {
                        'title': 'My Application',
                        'subtitle': 'Built with App Builder',
                        'className': 'app-header'
                    }
                },
                {
                    'id': 'nav-comp',
                    'type': 'Navigation',
                    'props': {
                        'items': ['Home', 'About', 'Contact'],
                        'className': 'main-nav'
                    }
                },
                {
                    'id': 'form-comp',
                    'type': 'Form',
                    'props': {
                        'fields': [
                            {'name': 'email', 'type': 'email', 'required': True},
                            {'name': 'message', 'type': 'textarea', 'required': True}
                        ],
                        'submitText': 'Send Message'
                    }
                },
                {
                    'id': 'footer-comp',
                    'type': 'Footer',
                    'props': {
                        'copyright': '2024 My App',
                        'links': ['Privacy', 'Terms']
                    }
                }
            ],
            'layouts': [
                {
                    'id': 'main-layout',
                    'name': 'MainLayout',
                    'type': 'container',
                    'components': ['header-comp', 'nav-comp', 'form-comp', 'footer-comp'],
                    'styles': {
                        'display': 'flex',
                        'flexDirection': 'column',
                        'minHeight': '100vh'
                    }
                }
            ],
            'styles': {
                '.app-header': {
                    'background-color': '#2c3e50',
                    'color': 'white',
                    'padding': '1rem',
                    'text-align': 'center'
                },
                '.main-nav': {
                    'background-color': '#34495e',
                    'padding': '0.5rem'
                },
                '.main-nav ul': {
                    'list-style': 'none',
                    'display': 'flex',
                    'gap': '1rem',
                    'margin': '0',
                    'padding': '0'
                }
            },
            'data': {
                'appName': 'My Application',
                'version': '1.0.0',
                'author': 'App Builder User',
                'features': ['responsive', 'accessible', 'modern']
            }
        }
        
        # Create test app in database
        self.test_app = App.objects.create(
            name='Integration Test App',
            user=self.user,
            app_data=json.dumps(self.complex_app_data)
        )
    
    def test_complete_react_export_pipeline(self):
        """Test complete React export pipeline with validation"""
        # Step 1: Generate React code
        options = ExportOptions(
            format=ExportFormat.REACT,
            typescript=True,
            include_accessibility=True,
            include_tests=True,
            style_framework=StyleFramework.STYLED_COMPONENTS,
            project_structure='full-project',
            include_docker=True,
            include_ci_cd=True
        )
        
        generated_code = self.generator.generate_code(self.complex_app_data, options)
        
        # Verify generation succeeded
        self.assertIsInstance(generated_code, dict)
        self.assertIn('package.json', generated_code)
        self.assertIn('src/App.tsx', generated_code)
        self.assertIn('README.md', generated_code)
        self.assertIn('Dockerfile', generated_code)
        
        # Step 2: Validate generated code
        app_code = generated_code['src/App.tsx']
        validation_results = self.validator.validate_code(app_code, 'typescript', 'react')
        
        # Check for critical errors
        critical_errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
        self.assertEqual(len(critical_errors), 0, f"Critical errors found: {critical_errors}")
        
        # Step 3: Validate project structure
        structure_results = self.validator.validate_project_structure(generated_code, 'react')
        structure_errors = [r for r in structure_results if r.level == ValidationLevel.ERROR]
        self.assertEqual(len(structure_errors), 0, f"Structure errors found: {structure_errors}")
        
        # Step 4: Verify specific React patterns
        self.assertIn('import React', app_code)
        self.assertIn('export default App', app_code)
        self.assertIn('styled-components', app_code)
        self.assertIn('role=', app_code)  # Accessibility
        
        # Step 5: Validate package.json
        package_data = json.loads(generated_code['package.json'])
        self.assertIn('react', package_data['dependencies'])
        self.assertIn('typescript', package_data['devDependencies'])
        self.assertIn('styled-components', package_data['dependencies'])
    
    def test_vue_export_with_typescript(self):
        """Test Vue.js export with TypeScript validation"""
        options = ExportOptions(
            format=ExportFormat.VUE_TS,
            typescript=True,
            include_accessibility=True,
            project_structure='multi-file'
        )
        
        generated_code = self.generator.generate_code(self.complex_app_data, options)
        
        # Validate Vue structure
        self.assertIsInstance(generated_code, dict)
        self.assertIn('App.vue', generated_code)
        
        vue_code = generated_code['App.vue']
        validation_results = self.validator.validate_code(vue_code, 'javascript', 'vue')
        
        # Check Vue-specific requirements
        self.assertIn('<template>', vue_code)
        self.assertIn('<script lang="ts">', vue_code)
        self.assertIn('<style scoped>', vue_code)
        self.assertIn('defineComponent', vue_code)
        
        # Validate no critical errors
        critical_errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
        self.assertEqual(len(critical_errors), 0)
    
    def test_angular_export_validation(self):
        """Test Angular export with comprehensive validation"""
        options = ExportOptions(
            format=ExportFormat.ANGULAR,
            typescript=True,
            include_accessibility=True,
            project_structure='multi-file'
        )
        
        generated_code = self.generator.generate_code(self.complex_app_data, options)
        
        # Validate Angular structure
        self.assertIn('app.component.ts', generated_code)
        self.assertIn('app.component.html', generated_code)
        self.assertIn('app.component.css', generated_code)
        
        # Validate TypeScript component
        ts_code = generated_code['app.component.ts']
        validation_results = self.validator.validate_code(ts_code, 'typescript', 'angular')
        
        self.assertIn('@Component', ts_code)
        self.assertIn('export class AppComponent', ts_code)
        
        # Validate HTML template
        html_code = generated_code['app.component.html']
        html_validation = self.validator.validate_code(html_code, 'html')
        
        # Check for accessibility attributes
        self.assertIn('role=', html_code)
    
    def test_react_native_export_validation(self):
        """Test React Native export with mobile-specific validation"""
        options = ExportOptions(
            format=ExportFormat.REACT_NATIVE,
            typescript=True,
            project_structure='full-project'
        )
        
        generated_code = self.generator.generate_code(self.complex_app_data, options)
        
        if isinstance(generated_code, str):
            rn_code = generated_code
        else:
            rn_code = generated_code['App.tsx']
        
        # Validate React Native specific imports
        self.assertIn('react-native', rn_code)
        self.assertIn('SafeAreaView', rn_code)
        self.assertIn('StyleSheet', rn_code)
        
        # Validate mobile component mappings
        self.assertIn('TouchableOpacity', rn_code)  # Button mapping
        self.assertIn('TextInput', rn_code)  # Input mapping
        
        validation_results = self.validator.validate_code(rn_code, 'typescript', 'react')
        critical_errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
        self.assertEqual(len(critical_errors), 0)
    
    def test_flutter_export_validation(self):
        """Test Flutter export with Dart validation"""
        options = ExportOptions(
            format=ExportFormat.FLUTTER,
            project_structure='full-project'
        )
        
        generated_code = self.generator.generate_code(self.complex_app_data, options)
        
        if isinstance(generated_code, str):
            dart_code = generated_code
        else:
            dart_code = generated_code['lib/main.dart']
        
        # Validate Flutter/Dart structure
        self.assertIn('import \'package:flutter/material.dart\'', dart_code)
        self.assertIn('void main()', dart_code)
        self.assertIn('runApp(', dart_code)
        self.assertIn('StatefulWidget', dart_code)
        
        validation_results = self.validator.validate_code(dart_code, 'dart')
        critical_errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
        self.assertEqual(len(critical_errors), 0)
    
    def test_html_export_accessibility_validation(self):
        """Test HTML export with comprehensive accessibility validation"""
        options = ExportOptions(
            format=ExportFormat.HTML,
            include_accessibility=True,
            project_structure='single-file'
        )
        
        generated_code = self.generator.generate_code(self.complex_app_data, options)
        
        # Validate HTML structure
        self.assertIn('<!DOCTYPE html>', generated_code)
        self.assertIn('<html lang="en">', generated_code)
        self.assertIn('<head>', generated_code)
        self.assertIn('<body>', generated_code)
        
        # Validate accessibility features
        validation_results = self.validator.validate_code(generated_code, 'html')
        
        # Check for accessibility warnings (should be minimal with our generator)
        accessibility_warnings = [
            r for r in validation_results 
            if r.rule == 'accessibility' and r.level == ValidationLevel.WARNING
        ]
        
        # Our generator should produce accessible HTML, so warnings should be minimal
        self.assertLessEqual(len(accessibility_warnings), 2)
    
    def test_api_export_validation(self):
        """Test API export formats with validation"""
        formats_to_test = [
            (ExportFormat.EXPRESS_API, 'javascript'),
            (ExportFormat.FASTAPI, 'python'),
            (ExportFormat.DJANGO_API, 'python')
        ]
        
        for export_format, language in formats_to_test:
            with self.subTest(format=export_format):
                options = ExportOptions(
                    format=export_format,
                    project_structure='multi-file'
                )
                
                generated_code = self.generator.generate_code(self.complex_app_data, options)
                
                self.assertIsInstance(generated_code, dict)
                
                # Validate main file based on format
                if export_format == ExportFormat.EXPRESS_API:
                    self.assertIn('server.js', generated_code)
                    main_code = generated_code['server.js']
                    self.assertIn('express', main_code)
                    self.assertIn('app.listen', main_code)
                elif export_format in [ExportFormat.FASTAPI, ExportFormat.DJANGO_API]:
                    main_file = 'main.py' if export_format == ExportFormat.FASTAPI else 'views.py'
                    self.assertIn(main_file, generated_code)
                    main_code = generated_code[main_file]
                    
                    if export_format == ExportFormat.FASTAPI:
                        self.assertIn('FastAPI', main_code)
                    else:
                        self.assertIn('django', main_code)
                
                # Validate syntax
                validation_results = self.validator.validate_code(main_code, language)
                critical_errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
                self.assertEqual(len(critical_errors), 0)
    
    def test_template_integration_export(self):
        """Test export with template system integration"""
        # Create a layout template
        layout_template = LayoutTemplate.objects.create(
            name='Header Layout',
            user=self.user,
            layout_type='header',
            components=json.dumps({
                'components': [
                    {'id': 'logo', 'type': 'Image', 'props': {'src': '/logo.png', 'alt': 'Logo'}},
                    {'id': 'nav', 'type': 'Navigation', 'props': {'items': ['Home', 'About']}}
                ]
            })
        )
        
        # Export template as project
        result = self.export_service.export_template_as_project(
            layout_template.id,
            'layout',
            'react',
            {'typescript': True, 'project_structure': 'full-project'},
            self.user
        )
        
        self.assertEqual(result['type'], 'project')
        self.assertIn('files', result)
        self.assertIn('metadata', result)
        
        # Validate generated code
        files = result['files']
        self.assertIn('package.json', files)
        
        # Find the main component file
        main_file = None
        for filename in files.keys():
            if filename.endswith(('.jsx', '.tsx')) and 'App' in filename:
                main_file = filename
                break
        
        self.assertIsNotNone(main_file)
        
        # Validate the generated component
        component_code = files[main_file]
        validation_results = self.validator.validate_code(component_code, 'typescript', 'react')
        
        critical_errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
        self.assertEqual(len(critical_errors), 0)
    
    def test_export_api_endpoint_integration(self):
        """Test the complete API endpoint integration"""
        self.client.force_login(self.user)
        
        # Test enhanced export endpoint
        response = self.client.post('/api/enhanced-export/', {
            'app_id': self.test_app.id,
            'format': 'react',
            'options': {
                'typescript': True,
                'include_accessibility': True,
                'project_structure': 'full-project',
                'style_framework': 'styled-components'
            }
        }, content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertIn('files', data)
        
        # Validate the returned code
        files = data['files']
        self.assertIn('package.json', files)
        
        # Validate package.json structure
        package_data = json.loads(files['package.json'])
        self.assertIn('react', package_data['dependencies'])
        self.assertIn('typescript', package_data['devDependencies'])
    
    def test_performance_with_large_app(self):
        """Test export performance with large application data"""
        # Create large app data
        large_app_data = {
            'components': [],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        # Generate 100 components
        for i in range(100):
            large_app_data['components'].append({
                'id': f'comp-{i}',
                'type': 'Button' if i % 2 == 0 else 'Text',
                'props': {
                    'text': f'Component {i}',
                    'className': f'comp-{i}-class'
                }
            })
        
        # Generate 10 layouts
        for i in range(10):
            component_refs = [f'comp-{j}' for j in range(i*10, (i+1)*10)]
            large_app_data['layouts'].append({
                'id': f'layout-{i}',
                'name': f'Layout{i}',
                'type': 'container',
                'components': component_refs
            })
        
        # Test export performance
        import time
        
        options = ExportOptions(
            format=ExportFormat.REACT,
            typescript=True,
            project_structure='full-project'
        )
        
        start_time = time.time()
        generated_code = self.generator.generate_code(large_app_data, options)
        end_time = time.time()
        
        # Should complete within reasonable time (5 seconds)
        self.assertLess(end_time - start_time, 5.0)
        
        # Validate the result
        self.assertIsInstance(generated_code, dict)
        self.assertIn('package.json', generated_code)
        
        # Validate generated code quality
        main_file = None
        for filename in generated_code.keys():
            if filename.endswith(('.jsx', '.tsx')) and 'App' in filename:
                main_file = filename
                break
        
        if main_file:
            validation_results = self.validator.validate_code(
                generated_code[main_file], 'typescript', 'react'
            )
            critical_errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
            self.assertEqual(len(critical_errors), 0)
