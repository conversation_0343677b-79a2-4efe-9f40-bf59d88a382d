import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import components to test
import EnhancedAIPlugin from '../../plugins/EnhancedAIPlugin';
import AISuggestionsPanel from '../../components/ai/AISuggestionsPanel';
import AIAssistantButton from '../../components/ai/AIAssistantButton';
import EnhancedComponentPalette from '../../components/builder/EnhancedComponentPalette';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';

// Mock the AI services
jest.mock('../../services/aiDesignService', () => ({
  generateLayoutSuggestions: jest.fn(),
  generateComponentCombinations: jest.fn(),
  analyzeAppStructure: jest.fn(),
  clearCache: jest.fn()
}));

jest.mock('../../services/aiWebSocketService', () => ({
  connect: jest.fn(),
  disconnect: jest.fn(),
  requestLayoutSuggestions: jest.fn(),
  requestComponentCombinations: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  getStatus: jest.fn(() => ({ connected: false }))
}));

// Mock Redux store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: (state = {
        components: [],
        layouts: [],
        styles: {}
      }, action) => state,
      ui: (state = {
        selectedComponent: null
      }, action) => state
    },
    preloadedState: initialState
  });
};

// Mock data
const mockComponents = [
  { id: '1', type: 'button', props: {} },
  { id: '2', type: 'text', props: {} },
  { id: '3', type: 'card', props: {} }
];

const mockLayoutSuggestions = [
  {
    id: 'grid_layout',
    name: 'Grid Layout',
    description: 'Responsive grid system for organizing content',
    score: 85,
    explanation: 'Grid layout works well for organizing many components',
    structure: { display: 'grid', gap: '16px' },
    use_cases: ['portfolio', 'gallery', 'product_catalog']
  },
  {
    id: 'header_footer',
    name: 'Header-Footer Layout',
    description: 'Classic layout with header, main content, and footer',
    score: 75,
    explanation: 'Traditional layout suitable for most applications',
    structure: { header: true, footer: true },
    use_cases: ['landing_page', 'blog', 'corporate_site']
  }
];

const mockCombinationSuggestions = [
  {
    id: 'form_pattern',
    name: 'Form with Validation',
    description: 'Complete form setup with validation and submit button',
    score: 90,
    explanation: 'Add input and button to complete this form pattern',
    components: ['form', 'input', 'button'],
    missing_components: ['input'],
    use_cases: ['contact_form', 'registration', 'survey']
  }
];

describe('AI Integration Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore({
      app: {
        components: mockComponents,
        layouts: [],
        styles: {}
      },
      ui: {
        selectedComponent: mockComponents[0]
      }
    });
  });

  describe('EnhancedAIPlugin', () => {
    test('renders AI plugin with layout and component suggestions', async () => {
      const mockOnApplyLayout = jest.fn();
      const mockOnApplyComponent = jest.fn();

      render(
        <Provider store={store}>
          <EnhancedAIPlugin
            components={mockComponents}
            onApplyLayoutSuggestion={mockOnApplyLayout}
            onApplyComponentCombination={mockOnApplyComponent}
          />
        </Provider>
      );

      expect(screen.getByText('AI Design Assistant')).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    test('switches between tabs correctly', async () => {
      render(
        <Provider store={store}>
          <EnhancedAIPlugin
            components={mockComponents}
          />
        </Provider>
      );

      // Check initial tab
      expect(screen.getByRole('tab', { name: /layouts/i })).toBeInTheDocument();
      
      // Switch to components tab
      fireEvent.click(screen.getByRole('tab', { name: /components/i }));
      
      // Switch to analysis tab
      fireEvent.click(screen.getByRole('tab', { name: /analysis/i }));
    });
  });

  describe('AISuggestionsPanel', () => {
    test('renders suggestions panel when visible', () => {
      const mockOnClose = jest.fn();

      render(
        <Provider store={store}>
          <AISuggestionsPanel
            visible={true}
            onClose={mockOnClose}
            components={mockComponents}
          />
        </Provider>
      );

      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument();
    });

    test('calls onClose when close button is clicked', () => {
      const mockOnClose = jest.fn();

      render(
        <Provider store={store}>
          <AISuggestionsPanel
            visible={true}
            onClose={mockOnClose}
            components={mockComponents}
          />
        </Provider>
      );

      fireEvent.click(screen.getByRole('button', { name: /close/i }));
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('AIAssistantButton', () => {
    test('renders assistant button with correct props', () => {
      const mockOnClick = jest.fn();

      render(
        <Provider store={store}>
          <AIAssistantButton
            onClick={mockOnClick}
            components={mockComponents}
          />
        </Provider>
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      
      fireEvent.click(button);
      expect(mockOnClick).toHaveBeenCalled();
    });

    test('shows badge when suggestions are available', () => {
      render(
        <Provider store={store}>
          <AIAssistantButton
            onClick={jest.fn()}
            components={mockComponents}
            showBadge={true}
          />
        </Provider>
      );

      // Badge should be present when there are suggestions
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('EnhancedComponentPalette', () => {
    test('renders component palette with AI suggestions', () => {
      const mockOnAddComponent = jest.fn();

      render(
        <Provider store={store}>
          <EnhancedComponentPalette
            onAddComponent={mockOnAddComponent}
            components={mockComponents}
            selectedComponent={mockComponents[0]}
            showAISuggestions={true}
          />
        </Provider>
      );

      expect(screen.getByText('Component Palette')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search components...')).toBeInTheDocument();
    });

    test('adds component when clicked', () => {
      const mockOnAddComponent = jest.fn();

      render(
        <Provider store={store}>
          <EnhancedComponentPalette
            onAddComponent={mockOnAddComponent}
            components={mockComponents}
          />
        </Provider>
      );

      // Find and click a component button
      const buttonComponent = screen.getByText('Button');
      fireEvent.click(buttonComponent);
      
      expect(mockOnAddComponent).toHaveBeenCalledWith('button');
    });

    test('filters components based on search term', () => {
      render(
        <Provider store={store}>
          <EnhancedComponentPalette
            onAddComponent={jest.fn()}
            components={mockComponents}
          />
        </Provider>
      );

      const searchInput = screen.getByPlaceholderText('Search components...');
      fireEvent.change(searchInput, { target: { value: 'button' } });

      expect(screen.getByText('Button')).toBeInTheDocument();
    });
  });

  describe('useAIDesignSuggestions Hook', () => {
    test('provides correct initial state', () => {
      let hookResult;
      
      function TestComponent() {
        hookResult = useAIDesignSuggestions();
        return <div>Test</div>;
      }

      render(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );

      expect(hookResult.suggestions).toBeDefined();
      expect(hookResult.loading).toBeDefined();
      expect(hookResult.error).toBe(null);
    });
  });

  describe('AI Service Integration', () => {
    test('handles API errors gracefully', async () => {
      const aiDesignService = require('../../services/aiDesignService').default;
      aiDesignService.generateLayoutSuggestions.mockRejectedValue(new Error('API Error'));

      render(
        <Provider store={store}>
          <EnhancedAIPlugin
            components={mockComponents}
          />
        </Provider>
      );

      // Should not crash and should show fallback content
      await waitFor(() => {
        expect(screen.getByText('AI Design Assistant')).toBeInTheDocument();
      });
    });

    test('caches suggestions correctly', async () => {
      const aiDesignService = require('../../services/aiDesignService').default;
      aiDesignService.generateLayoutSuggestions.mockResolvedValue({
        suggestions: mockLayoutSuggestions,
        status: 'success'
      });

      render(
        <Provider store={store}>
          <EnhancedAIPlugin
            components={mockComponents}
          />
        </Provider>
      );

      await waitFor(() => {
        expect(aiDesignService.generateLayoutSuggestions).toHaveBeenCalled();
      });
    });
  });

  describe('WebSocket Integration', () => {
    test('connects to WebSocket service', () => {
      const aiWebSocketService = require('../../services/aiWebSocketService').default;
      
      render(
        <Provider store={store}>
          <EnhancedAIPlugin
            components={mockComponents}
          />
        </Provider>
      );

      // WebSocket service should be available
      expect(aiWebSocketService.connect).toBeDefined();
    });
  });

  describe('Accessibility', () => {
    test('AI components have proper ARIA labels', () => {
      render(
        <Provider store={store}>
          <AISuggestionsPanel
            visible={true}
            onClose={jest.fn()}
            components={mockComponents}
          />
        </Provider>
      );

      // Check for proper accessibility attributes
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    test('keyboard navigation works correctly', () => {
      render(
        <Provider store={store}>
          <EnhancedComponentPalette
            onAddComponent={jest.fn()}
            components={mockComponents}
          />
        </Provider>
      );

      const searchInput = screen.getByPlaceholderText('Search components...');
      expect(searchInput).toBeInTheDocument();
      
      // Test keyboard interaction
      fireEvent.keyDown(searchInput, { key: 'Enter' });
    });
  });
});

describe('Integration with Existing App Builder', () => {
  test('AI suggestions integrate with drag and drop', () => {
    const mockOnAddComponent = jest.fn();
    const mockOnDragStart = jest.fn();

    render(
      <Provider store={createMockStore()}>
        <EnhancedComponentPalette
          onAddComponent={mockOnAddComponent}
          onDragStart={mockOnDragStart}
          components={mockComponents}
          showAISuggestions={true}
        />
      </Provider>
    );

    expect(screen.getByText('Component Palette')).toBeInTheDocument();
  });

  test('AI suggestions work with existing Redux state', () => {
    const storeWithData = createMockStore({
      app: {
        components: mockComponents,
        layouts: [],
        styles: {}
      }
    });

    render(
      <Provider store={storeWithData}>
        <EnhancedAIPlugin
          components={mockComponents}
        />
      </Provider>
    );

    expect(screen.getByText('AI Design Assistant')).toBeInTheDocument();
  });
});
