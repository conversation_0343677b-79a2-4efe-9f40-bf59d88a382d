import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';

/**
 * Custom hook for managing code export functionality
 */
export const useCodeExport = (options = {}) => {
  const {
    enabled = true,
    components = [],
    projectSettings = {}
  } = options;

  // Local state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [exportHistory, setExportHistory] = useState([]);

  // Available export formats
  const exportFormats = [
    {
      id: 'react',
      name: 'React',
      description: 'Export as React components with JSX',
      extension: 'jsx',
      icon: '⚛️'
    },
    {
      id: 'vue',
      name: 'Vue.js',
      description: 'Export as Vue.js single file components',
      extension: 'vue',
      icon: '🟢'
    },
    {
      id: 'angular',
      name: 'Angular',
      description: 'Export as Angular components with TypeScript',
      extension: 'ts',
      icon: '🔺'
    },
    {
      id: 'html',
      name: 'HTML/CSS',
      description: 'Export as static HTML with CSS',
      extension: 'html',
      icon: '🌐'
    },
    {
      id: 'svelte',
      name: 'Svelte',
      description: 'Export as Svelte components',
      extension: 'svelte',
      icon: '🧡'
    },
    {
      id: 'nextjs',
      name: 'Next.js',
      description: 'Export as Next.js pages and components',
      extension: 'jsx',
      icon: '▲'
    }
  ];

  // Generate code for components
  const generateComponentCode = useCallback((component, format) => {
    switch (format) {
      case 'react':
        return `import React from 'react';

const ${component.type}Component = (props) => {
  return (
    <div className="${component.type.toLowerCase()}-component">
      {/* ${component.type} component content */}
      <h2>{props.title || '${component.type}'}</h2>
      {props.children}
    </div>
  );
};

export default ${component.type}Component;`;

      case 'vue':
        return `<template>
  <div class="${component.type.toLowerCase()}-component">
    <!-- ${component.type} component content -->
    <h2>{{ title || '${component.type}' }}</h2>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: '${component.type}Component',
  props: {
    title: {
      type: String,
      default: '${component.type}'
    }
  }
}
</script>

<style scoped>
.${component.type.toLowerCase()}-component {
  /* Component styles */
}
</style>`;

      case 'angular':
        return `import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-${component.type.toLowerCase()}',
  template: \`
    <div class="${component.type.toLowerCase()}-component">
      <!-- ${component.type} component content -->
      <h2>{{ title || '${component.type}' }}</h2>
      <ng-content></ng-content>
    </div>
  \`,
  styleUrls: ['./${component.type.toLowerCase()}.component.css']
})
export class ${component.type}Component {
  @Input() title: string = '${component.type}';
}`;

      case 'html':
        return `<div class="${component.type.toLowerCase()}-component">
  <!-- ${component.type} component content -->
  <h2>${component.type}</h2>
</div>`;

      case 'svelte':
        return `<script>
  export let title = '${component.type}';
</script>

<div class="${component.type.toLowerCase()}-component">
  <!-- ${component.type} component content -->
  <h2>{title}</h2>
  <slot></slot>
</div>

<style>
  .${component.type.toLowerCase()}-component {
    /* Component styles */
  }
</style>`;

      case 'nextjs':
        return `import React from 'react';

const ${component.type}Component = ({ title = '${component.type}', children }) => {
  return (
    <div className="${component.type.toLowerCase()}-component">
      {/* ${component.type} component content */}
      <h2>{title}</h2>
      {children}
    </div>
  );
};

export default ${component.type}Component;`;

      default:
        return `// ${component.type} component code`;
    }
  }, []);

  // Export code
  const exportCode = useCallback(async (format, options = {}) => {
    if (!enabled) return null;

    try {
      setLoading(true);
      setError(null);

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));

      const exportData = {
        format,
        components: {},
        styles: {},
        metadata: {
          generatedAt: new Date().toISOString(),
          format,
          componentCount: components.length,
          projectSettings
        }
      };

      // Generate code for each component
      components.forEach(component => {
        exportData.components[component.id] = {
          name: component.type,
          code: generateComponentCode(component, format),
          props: component.props || {},
          type: component.type
        };
      });

      // Generate main app file
      if (format === 'react' || format === 'nextjs') {
        exportData.mainApp = `import React from 'react';
${components.map(comp => `import ${comp.type}Component from './${comp.type}Component';`).join('\n')}

const App = () => {
  return (
    <div className="app">
      ${components.map(comp => `<${comp.type}Component />`).join('\n      ')}
    </div>
  );
};

export default App;`;
      }

      // Add to export history
      const exportRecord = {
        id: Date.now(),
        format,
        timestamp: new Date().toISOString(),
        componentCount: components.length,
        options
      };
      
      setExportHistory(prev => [exportRecord, ...prev.slice(0, 9)]); // Keep last 10 exports

      message.success(`Code exported successfully as ${format.toUpperCase()}`);
      
      return exportData;
    } catch (err) {
      console.error('Error exporting code:', err);
      setError(err);
      message.error('Failed to export code');
      return null;
    } finally {
      setLoading(false);
    }
  }, [enabled, components, generateComponentCode, projectSettings]);

  // Download code
  const downloadCode = useCallback(async (format, options = {}) => {
    try {
      const exportData = await exportCode(format, options);
      if (!exportData) return;

      const formatInfo = exportFormats.find(f => f.id === format);
      const extension = formatInfo?.extension || 'txt';

      if (exportData.mainApp) {
        // Download main app file
        const blob = new Blob([exportData.mainApp], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `App.${extension}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }

      // Download component files
      Object.entries(exportData.components).forEach(([id, component]) => {
        const blob = new Blob([component.code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${component.name}Component.${extension}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      });

      message.success('Files downloaded successfully');
    } catch (err) {
      console.error('Error downloading code:', err);
      message.error('Failed to download code');
    }
  }, [exportCode, exportFormats]);

  // Get export format by id
  const getExportFormat = useCallback((formatId) => {
    return exportFormats.find(format => format.id === formatId);
  }, [exportFormats]);

  // Clear export history
  const clearExportHistory = useCallback(() => {
    setExportHistory([]);
    message.success('Export history cleared');
  }, []);

  return {
    // State
    loading,
    error,
    exportHistory,
    exportFormats,

    // Actions
    exportCode,
    downloadCode,
    clearExportHistory,

    // Utility functions
    getExportFormat,

    // Computed values
    hasComponents: components.length > 0,
    canExport: enabled && components.length > 0,
    formatCount: exportFormats.length,
    historyCount: exportHistory.length
  };
};

export default useCodeExport;
