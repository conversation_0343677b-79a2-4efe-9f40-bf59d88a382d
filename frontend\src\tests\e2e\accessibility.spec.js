import { test, expect } from '@playwright/test';
import { injectAxe, checkA11y } from 'axe-playwright';

const APP_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
    
    // Inject axe-core for accessibility testing
    await injectAxe(page);
  });

  test('homepage meets WCAG AA standards', async ({ page }) => {
    // Run axe accessibility checks
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true },
      tags: ['wcag2a', 'wcag2aa', 'wcag21aa']
    });
  });

  test('keyboard navigation works correctly', async ({ page }) => {
    // Test tab navigation through interactive elements
    const focusableElements = [
      'button',
      'input',
      'select',
      'textarea',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ];

    // Start from the beginning
    await page.keyboard.press('Tab');
    
    let focusedElement = await page.locator(':focus').first();
    let tabCount = 0;
    const maxTabs = 20; // Prevent infinite loop

    while (tabCount < maxTabs) {
      // Check if focused element is visible and interactive
      if (await focusedElement.isVisible()) {
        const tagName = await focusedElement.evaluate(el => el.tagName.toLowerCase());
        const tabIndex = await focusedElement.getAttribute('tabindex');
        
        // Verify element is focusable
        expect(focusableElements.some(selector => 
          selector.includes(tagName) || 
          (tabIndex && parseInt(tabIndex) >= 0)
        )).toBeTruthy();

        // Test Enter/Space activation for buttons
        if (tagName === 'button') {
          const isDisabled = await focusedElement.isDisabled();
          if (!isDisabled) {
            // Test space key activation
            await page.keyboard.press('Space');
            await page.waitForTimeout(100);
          }
        }
      }

      // Move to next focusable element
      await page.keyboard.press('Tab');
      focusedElement = await page.locator(':focus').first();
      tabCount++;
    }

    expect(tabCount).toBeGreaterThan(0);
  });

  test('screen reader announcements work correctly', async ({ page }) => {
    // Check for ARIA live regions
    const liveRegions = await page.locator('[aria-live]').all();
    expect(liveRegions.length).toBeGreaterThan(0);

    // Check for proper heading structure
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    if (headings.length > 0) {
      // Verify h1 exists and is unique
      const h1Elements = await page.locator('h1').all();
      expect(h1Elements.length).toBeLessThanOrEqual(1);

      // Check heading hierarchy
      for (let i = 0; i < headings.length; i++) {
        const heading = headings[i];
        const tagName = await heading.evaluate(el => el.tagName);
        const level = parseInt(tagName.charAt(1));
        
        // Verify heading has text content
        const textContent = await heading.textContent();
        expect(textContent?.trim()).toBeTruthy();
      }
    }

    // Check for proper landmark roles
    const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"], main, nav, header, footer').all();
    expect(landmarks.length).toBeGreaterThan(0);
  });

  test('color contrast meets WCAG standards', async ({ page }) => {
    // Run axe color contrast checks specifically
    await checkA11y(page, null, {
      rules: {
        'color-contrast': { enabled: true },
        'color-contrast-enhanced': { enabled: true }
      },
      tags: ['wcag2aa']
    });
  });

  test('form accessibility', async ({ page }) => {
    // Find all form elements
    const forms = await page.locator('form').all();
    
    for (const form of forms) {
      // Check form inputs have labels
      const inputs = await form.locator('input, select, textarea').all();
      
      for (const input of inputs) {
        const inputId = await input.getAttribute('id');
        const ariaLabel = await input.getAttribute('aria-label');
        const ariaLabelledBy = await input.getAttribute('aria-labelledby');
        
        if (inputId) {
          // Check for associated label
          const label = await page.locator(`label[for="${inputId}"]`).first();
          const hasLabel = await label.isVisible().catch(() => false);
          
          // Input should have either a label, aria-label, or aria-labelledby
          expect(hasLabel || ariaLabel || ariaLabelledBy).toBeTruthy();
        }

        // Check for required field indicators
        const isRequired = await input.getAttribute('required');
        const ariaRequired = await input.getAttribute('aria-required');
        
        if (isRequired !== null || ariaRequired === 'true') {
          // Required fields should be clearly indicated
          const parentText = await input.locator('..').textContent();
          expect(parentText).toMatch(/\*|required|mandatory/i);
        }
      }
    }
  });

  test('modal and dialog accessibility', async ({ page }) => {
    // Look for modal triggers
    const modalTriggers = await page.locator('[data-testid*="modal"], [data-testid*="dialog"], button:has-text("open"), button:has-text("show")').all();
    
    for (const trigger of modalTriggers) {
      if (await trigger.isVisible()) {
        await trigger.click();
        await page.waitForTimeout(500);

        // Check for modal/dialog
        const modal = await page.locator('[role="dialog"], [role="alertdialog"], .modal, .dialog').first();
        
        if (await modal.isVisible()) {
          // Check modal has proper ARIA attributes
          const ariaLabel = await modal.getAttribute('aria-label');
          const ariaLabelledBy = await modal.getAttribute('aria-labelledby');
          expect(ariaLabel || ariaLabelledBy).toBeTruthy();

          // Check focus management
          const focusedElement = await page.locator(':focus').first();
          const isInsideModal = await modal.locator(':focus').count() > 0;
          expect(isInsideModal).toBeTruthy();

          // Test Escape key closes modal
          await page.keyboard.press('Escape');
          await page.waitForTimeout(500);
          
          const isModalClosed = !(await modal.isVisible().catch(() => false));
          expect(isModalClosed).toBeTruthy();
        }
      }
    }
  });

  test('drag and drop accessibility', async ({ page }) => {
    // Check for drag and drop elements
    const draggableElements = await page.locator('[draggable="true"], [data-testid*="draggable"]').all();
    
    for (const element of draggableElements) {
      if (await element.isVisible()) {
        // Check for keyboard alternatives
        const ariaLabel = await element.getAttribute('aria-label');
        const role = await element.getAttribute('role');
        
        // Draggable elements should have proper labeling
        expect(ariaLabel || role).toBeTruthy();

        // Check for keyboard interaction instructions
        const instructions = await element.locator('..').textContent();
        // Should provide keyboard alternatives or instructions
        expect(instructions).toMatch(/keyboard|arrow|enter|space|move/i);
      }
    }
  });

  test('high contrast mode support', async ({ page }) => {
    // Test with forced colors (high contrast mode)
    await page.emulateMedia({ forcedColors: 'active' });
    await page.waitForTimeout(1000);

    // Run accessibility checks in high contrast mode
    await checkA11y(page, null, {
      rules: {
        'color-contrast': { enabled: true }
      }
    });

    // Check that interactive elements are still visible
    const buttons = await page.locator('button:visible').all();
    for (const button of buttons) {
      const boundingBox = await button.boundingBox();
      expect(boundingBox).toBeTruthy();
      expect(boundingBox.width).toBeGreaterThan(0);
      expect(boundingBox.height).toBeGreaterThan(0);
    }
  });

  test('reduced motion support', async ({ page }) => {
    // Test with reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' });
    await page.waitForTimeout(1000);

    // Check that animations are reduced or disabled
    const animatedElements = await page.locator('[style*="animation"], [style*="transition"], .animate, .transition').all();
    
    for (const element of animatedElements) {
      const computedStyle = await element.evaluate(el => {
        const style = window.getComputedStyle(el);
        return {
          animationDuration: style.animationDuration,
          transitionDuration: style.transitionDuration
        };
      });

      // Animations should be disabled or very short
      expect(
        computedStyle.animationDuration === '0s' || 
        computedStyle.transitionDuration === '0s' ||
        computedStyle.animationDuration === 'none'
      ).toBeTruthy();
    }
  });

  test('zoom and magnification support', async ({ page }) => {
    // Test at 200% zoom
    await page.setViewportSize({ width: 640, height: 480 }); // Simulate 200% zoom
    await page.waitForTimeout(1000);

    // Check that content is still accessible
    const mainContent = await page.locator('main, [role="main"], #main').first();
    if (await mainContent.isVisible()) {
      const boundingBox = await mainContent.boundingBox();
      expect(boundingBox.width).toBeGreaterThan(0);
    }

    // Check that interactive elements are still clickable
    const buttons = await page.locator('button:visible').all();
    for (const button of buttons.slice(0, 3)) { // Test first 3 buttons
      const boundingBox = await button.boundingBox();
      expect(boundingBox.width).toBeGreaterThan(20); // Minimum touch target
      expect(boundingBox.height).toBeGreaterThan(20);
    }
  });

  test('error messages are accessible', async ({ page }) => {
    // Look for form validation or error scenarios
    const forms = await page.locator('form').all();
    
    for (const form of forms) {
      const submitButton = await form.locator('button[type="submit"], input[type="submit"]').first();
      
      if (await submitButton.isVisible()) {
        // Try to submit empty form to trigger validation
        await submitButton.click();
        await page.waitForTimeout(1000);

        // Check for error messages
        const errorMessages = await page.locator('[role="alert"], .error, .invalid, [aria-invalid="true"]').all();
        
        for (const error of errorMessages) {
          if (await error.isVisible()) {
            // Error should have text content
            const textContent = await error.textContent();
            expect(textContent?.trim()).toBeTruthy();

            // Error should be associated with the relevant field
            const ariaDescribedBy = await error.getAttribute('aria-describedby');
            const id = await error.getAttribute('id');
            
            if (id) {
              const associatedField = await page.locator(`[aria-describedby*="${id}"]`).first();
              const hasAssociation = await associatedField.isVisible().catch(() => false);
              expect(hasAssociation).toBeTruthy();
            }
          }
        }
      }
    }
  });

  test('skip links functionality', async ({ page }) => {
    // Check for skip links
    const skipLinks = await page.locator('a[href^="#"]:has-text("skip"), a[href^="#"]:has-text("jump")').all();
    
    for (const skipLink of skipLinks) {
      const href = await skipLink.getAttribute('href');
      if (href && href.startsWith('#')) {
        const targetId = href.substring(1);
        const target = await page.locator(`#${targetId}`).first();
        
        // Target should exist
        expect(await target.count()).toBeGreaterThan(0);
        
        // Test skip link functionality
        await skipLink.click();
        await page.waitForTimeout(500);
        
        // Focus should move to target
        const focusedElement = await page.locator(':focus').first();
        const targetElement = await target.first();
        
        // Check if focus moved to target or near target
        const focusedId = await focusedElement.getAttribute('id');
        expect(focusedId === targetId || await targetElement.isVisible()).toBeTruthy();
      }
    }
  });
});
