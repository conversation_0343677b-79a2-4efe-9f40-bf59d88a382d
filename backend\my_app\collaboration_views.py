"""
Collaboration API views for the App Builder
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.shortcuts import get_object_or_404
from django.db.models import Q
from .models import CollaborationSession, SessionParticipant, Comment, EditOperation, UserActivity
from .error_handling import error_response, handle_exception
from .security import add_security_headers, sanitize_request, get_sanitized_data
import logging
import re

logger = logging.getLogger(__name__)


@api_view(['GET', 'POST'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
@add_security_headers
@sanitize_request
def collaboration_sessions(request):
    """
    List collaboration sessions or create a new one
    """
    if request.method == 'GET':
        # Get sessions where user is a participant
        sessions = CollaborationSession.objects.filter(
            participants__user=request.user,
            is_active=True
        ).distinct()
        
        return Response([
            {
                'id': str(session.id),
                'name': session.name,
                'app_id': session.app.id,
                'app_name': session.app.name,
                'created_at': session.created_at.isoformat(),
                'participants_count': session.active_participants_count,
                'role': session.participants.get(user=request.user).role,
            }
            for session in sessions
        ])
    
    elif request.method == 'POST':
        app_id = get_sanitized_data(request, 'app_id')
        name = get_sanitized_data(request, 'name', f'Collaboration Session')
        
        if not app_id:
            return error_response(
                'MISSING_FIELD',
                'app_id is required',
                status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from .models import App
            app = App.objects.get(id=app_id, user=request.user)
            
            # Create collaboration session
            session = CollaborationSession.objects.create(
                app=app,
                name=name,
                created_by=request.user
            )
            
            # Add creator as owner
            SessionParticipant.objects.create(
                session=session,
                user=request.user,
                role='owner'
            )
            
            return Response({
                'id': str(session.id),
                'name': session.name,
                'app_id': session.app.id,
                'created_at': session.created_at.isoformat(),
            }, status=status.HTTP_201_CREATED)
            
        except App.DoesNotExist:
            return error_response(
                'NOT_FOUND',
                'App not found or access denied',
                status.HTTP_404_NOT_FOUND
            )


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
@add_security_headers
@sanitize_request
def collaboration_session_detail(request, session_id):
    """
    Get, update, or delete a collaboration session
    """
    session = get_object_or_404(CollaborationSession, id=session_id)
    
    # Check if user is a participant
    try:
        participant = SessionParticipant.objects.get(session=session, user=request.user)
    except SessionParticipant.DoesNotExist:
        return error_response(
            'ACCESS_DENIED',
            'You are not a participant in this session',
            status.HTTP_403_FORBIDDEN
        )
    
    if request.method == 'GET':
        return Response({
            'id': str(session.id),
            'name': session.name,
            'app_id': session.app.id,
            'app_name': session.app.name,
            'created_at': session.created_at.isoformat(),
            'updated_at': session.updated_at.isoformat(),
            'is_active': session.is_active,
            'participants': [
                {
                    'user_id': p.user.id,
                    'username': p.user.username,
                    'role': p.role,
                    'joined_at': p.joined_at.isoformat(),
                    'last_seen': p.last_seen.isoformat(),
                    'is_active': p.is_active,
                }
                for p in session.participants.all()
            ]
        })
    
    elif request.method == 'PUT':
        # Only owner can update session
        if participant.role != 'owner':
            return error_response(
                'ACCESS_DENIED',
                'Only session owner can update session',
                status.HTTP_403_FORBIDDEN
            )
        
        name = get_sanitized_data(request, 'name')
        if name:
            session.name = name
            session.save(update_fields=['name'])
        
        return Response({'message': 'Session updated successfully'})
    
    elif request.method == 'DELETE':
        # Only owner can delete session
        if participant.role != 'owner':
            return error_response(
                'ACCESS_DENIED',
                'Only session owner can delete session',
                status.HTTP_403_FORBIDDEN
            )
        
        session.is_active = False
        session.save(update_fields=['is_active'])
        
        return Response({'message': 'Session deleted successfully'})


@api_view(['GET', 'POST'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
@add_security_headers
@sanitize_request
def session_comments(request, session_id):
    """
    List comments for a session or create a new comment
    """
    session = get_object_or_404(CollaborationSession, id=session_id)
    
    # Check if user is a participant
    try:
        SessionParticipant.objects.get(session=session, user=request.user)
    except SessionParticipant.DoesNotExist:
        return error_response(
            'ACCESS_DENIED',
            'You are not a participant in this session',
            status.HTTP_403_FORBIDDEN
        )
    
    if request.method == 'GET':
        # Get comments for the session
        comments = Comment.objects.filter(session=session, parent=None).order_by('-created_at')
        
        def serialize_comment(comment):
            return {
                'id': str(comment.id),
                'author': {
                    'id': comment.author.id,
                    'username': comment.author.username,
                },
                'content': comment.content,
                'component_id': comment.component_id,
                'canvas_position': comment.canvas_position,
                'status': comment.status,
                'created_at': comment.created_at.isoformat(),
                'updated_at': comment.updated_at.isoformat(),
                'reply_count': comment.reply_count,
                'replies': [
                    serialize_comment(reply) for reply in comment.replies.all()[:5]  # Limit replies
                ] if comment.replies.exists() else []
            }
        
        return Response([serialize_comment(comment) for comment in comments])
    
    elif request.method == 'POST':
        content = get_sanitized_data(request, 'content')
        component_id = get_sanitized_data(request, 'component_id')
        canvas_position = get_sanitized_data(request, 'canvas_position', {})
        parent_id = get_sanitized_data(request, 'parent_id')
        
        if not content:
            return error_response(
                'MISSING_FIELD',
                'content is required',
                status.HTTP_400_BAD_REQUEST
            )
        
        # Create comment
        comment_data = {
            'session': session,
            'author': request.user,
            'content': content,
            'component_id': component_id,
            'canvas_position': canvas_position,
        }
        
        if parent_id:
            try:
                parent_comment = Comment.objects.get(id=parent_id, session=session)
                comment_data['parent'] = parent_comment
            except Comment.DoesNotExist:
                return error_response(
                    'NOT_FOUND',
                    'Parent comment not found',
                    status.HTTP_404_NOT_FOUND
                )
        
        comment = Comment.objects.create(**comment_data)
        
        # Extract mentions from content
        mentions = extract_mentions(content)
        if mentions:
            # Add mentioned users
            mentioned_users = User.objects.filter(username__in=mentions)
            comment.mentioned_users.set(mentioned_users)
        
        return Response({
            'id': str(comment.id),
            'author': {
                'id': comment.author.id,
                'username': comment.author.username,
            },
            'content': comment.content,
            'component_id': comment.component_id,
            'canvas_position': comment.canvas_position,
            'status': comment.status,
            'created_at': comment.created_at.isoformat(),
            'mentions': mentions,
        }, status=status.HTTP_201_CREATED)


def extract_mentions(content):
    """
    Extract @mentions from comment content
    """
    mention_pattern = r'@(\w+)'
    mentions = re.findall(mention_pattern, content)
    return list(set(mentions))  # Remove duplicates


@api_view(['PUT', 'DELETE'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
@add_security_headers
@sanitize_request
def comment_detail(request, comment_id):
    """
    Update or delete a comment
    """
    comment = get_object_or_404(Comment, id=comment_id)
    
    # Check if user is the author or has permission
    if comment.author != request.user:
        return error_response(
            'ACCESS_DENIED',
            'You can only modify your own comments',
            status.HTTP_403_FORBIDDEN
        )
    
    if request.method == 'PUT':
        content = get_sanitized_data(request, 'content')
        if content:
            comment.content = content
            comment.save(update_fields=['content', 'updated_at'])
            
            # Update mentions
            mentions = extract_mentions(content)
            if mentions:
                mentioned_users = User.objects.filter(username__in=mentions)
                comment.mentioned_users.set(mentioned_users)
        
        return Response({
            'id': str(comment.id),
            'content': comment.content,
            'updated_at': comment.updated_at.isoformat(),
        })
    
    elif request.method == 'DELETE':
        comment.status = 'archived'
        comment.save(update_fields=['status'])
        
        return Response({'message': 'Comment deleted successfully'})


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
@add_security_headers
@sanitize_request
def resolve_comment(request, comment_id):
    """
    Resolve or reopen a comment
    """
    comment = get_object_or_404(Comment, id=comment_id)
    action = get_sanitized_data(request, 'action', 'resolve')  # 'resolve' or 'reopen'
    
    if action == 'resolve':
        comment.resolve(request.user)
    elif action == 'reopen':
        comment.reopen()
    else:
        return error_response(
            'INVALID_FIELD',
            'action must be "resolve" or "reopen"',
            status.HTTP_400_BAD_REQUEST
        )
    
    return Response({
        'id': str(comment.id),
        'status': comment.status,
        'resolved_at': comment.resolved_at.isoformat() if comment.resolved_at else None,
        'resolved_by': comment.resolved_by.username if comment.resolved_by else None,
    })
