# Wait for Dock<PERSON> to be ready
param(
    [int]$TimeoutSeconds = 120
)

Write-Host "Waiting for Docker Desktop to be ready..." -ForegroundColor Cyan
Write-Host "Timeout: $TimeoutSeconds seconds" -ForegroundColor Yellow

$elapsed = 0
$interval = 5

while ($elapsed -lt $TimeoutSeconds) {
    try {
        docker info | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "`nSUCCESS: Docker is now ready!" -ForegroundColor Green
            
            # Show Docker info
            Write-Host "`nDocker Status:" -ForegroundColor Blue
            docker version --format "Client: {{.Client.Version}}, Server: {{.Server.Version}}"
            
            # Check if containers exist
            Write-Host "`nChecking existing containers..." -ForegroundColor Blue
            $containers = docker ps -a --format "table {{.Names}}\t{{.Status}}"
            if ($containers -match "app-builder") {
                Write-Host $containers
            } else {
                Write-Host "No app-builder containers found"
            }
            
            Write-Host "`nDocker is ready! You can now run:" -ForegroundColor Green
            Write-Host "  docker-compose up -d" -ForegroundColor Blue
            Write-Host "  or" -ForegroundColor White
            Write-Host "  powershell scripts/start-containers.ps1" -ForegroundColor Blue
            exit 0
        }
    } catch {
        # Continue waiting
    }
    
    Write-Host "Waiting... ($elapsed/$TimeoutSeconds seconds)" -ForegroundColor Yellow
    Start-Sleep -Seconds $interval
    $elapsed += $interval
}

Write-Host "`nTIMEOUT: Docker did not start within $TimeoutSeconds seconds" -ForegroundColor Red
Write-Host "Please try:" -ForegroundColor Yellow
Write-Host "1. Check if Docker Desktop is running in system tray" -ForegroundColor White
Write-Host "2. Restart Docker Desktop manually" -ForegroundColor White
Write-Host "3. Check Windows Event Viewer for Docker errors" -ForegroundColor White
Write-Host "4. Try running: wsl --update" -ForegroundColor White
exit 1
