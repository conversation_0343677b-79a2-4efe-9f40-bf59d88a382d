import React, { useState, useCallback } from 'react';
import { InputNumber, Space, Button, Typography, Card, Slider, Select, Tooltip } from 'antd';
import { 
  BorderOutlined, 
  ColumnWidthOutlined, 
  ColumnHeightOutlined,
  LinkOutlined,
  UnlinkOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Text } = Typography;
const { Option } = Select;

/**
 * Advanced Spacing Editor
 * 
 * Visual editor for margin, padding, and other spacing properties.
 */

const SpacingContainer = styled.div`
  .spacing-visual {
    position: relative;
    width: 200px;
    height: 150px;
    margin: 16px auto;
    background: #f5f5f5;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
  }
  
  .margin-area {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid #ffc107;
    border-radius: 6px;
  }
  
  .padding-area {
    position: absolute;
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid #28a745;
    border-radius: 4px;
  }
  
  .content-area {
    position: absolute;
    background: rgba(0, 123, 255, 0.2);
    border: 1px solid #007bff;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #007bff;
  }
`;

const SpacingInput = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  
  .spacing-label {
    min-width: 60px;
    font-size: 12px;
    color: #666;
  }
  
  .ant-input-number {
    width: 60px;
  }
`;

const SpacingGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  grid-template-rows: 1fr auto 1fr;
  gap: 4px;
  width: 120px;
  margin: 0 auto;
  
  .spacing-input {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .center-element {
    grid-column: 2;
    grid-row: 2;
    background: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    font-size: 11px;
    color: #666;
  }
`;

const SpacingEditor = ({
  value = { margin: 0, padding: 0 },
  onChange,
  properties = ['margin', 'padding'],
  units = ['px', 'rem', 'em', '%'],
  showVisual = true,
  showPresets = true
}) => {
  const [linkedMargin, setLinkedMargin] = useState(true);
  const [linkedPadding, setLinkedPadding] = useState(true);
  const [unit, setUnit] = useState('px');

  // Parse spacing value (can be number, string, or object)
  const parseSpacing = (spacing) => {
    if (typeof spacing === 'number') {
      return { top: spacing, right: spacing, bottom: spacing, left: spacing };
    }
    if (typeof spacing === 'string') {
      const values = spacing.split(' ').map(v => parseInt(v) || 0);
      if (values.length === 1) return { top: values[0], right: values[0], bottom: values[0], left: values[0] };
      if (values.length === 2) return { top: values[0], right: values[1], bottom: values[0], left: values[1] };
      if (values.length === 4) return { top: values[0], right: values[1], bottom: values[2], left: values[3] };
    }
    if (typeof spacing === 'object') {
      return {
        top: spacing.top || 0,
        right: spacing.right || 0,
        bottom: spacing.bottom || 0,
        left: spacing.left || 0
      };
    }
    return { top: 0, right: 0, bottom: 0, left: 0 };
  };

  const margin = parseSpacing(value.margin);
  const padding = parseSpacing(value.padding);

  const handleSpacingChange = useCallback((property, side, newValue) => {
    const currentSpacing = parseSpacing(value[property]);
    const updatedSpacing = { ...currentSpacing, [side]: newValue };
    
    // If linked, update all sides
    if ((property === 'margin' && linkedMargin) || (property === 'padding' && linkedPadding)) {
      updatedSpacing.top = newValue;
      updatedSpacing.right = newValue;
      updatedSpacing.bottom = newValue;
      updatedSpacing.left = newValue;
    }
    
    const newValue_obj = {
      ...value,
      [property]: updatedSpacing
    };
    
    if (onChange) {
      onChange(newValue_obj);
    }
  }, [value, onChange, linkedMargin, linkedPadding]);

  const handlePresetClick = useCallback((preset) => {
    if (onChange) {
      onChange(preset);
    }
  }, [onChange]);

  const formatSpacingValue = (spacing) => {
    const { top, right, bottom, left } = spacing;
    if (top === right && right === bottom && bottom === left) {
      return `${top}${unit}`;
    }
    return `${top}${unit} ${right}${unit} ${bottom}${unit} ${left}${unit}`;
  };

  const presets = [
    { label: 'None', value: { margin: 0, padding: 0 } },
    { label: 'Small', value: { margin: 8, padding: 8 } },
    { label: 'Medium', value: { margin: 16, padding: 16 } },
    { label: 'Large', value: { margin: 24, padding: 24 } },
    { label: 'Card', value: { margin: 16, padding: 24 } },
    { label: 'Button', value: { margin: 4, padding: { top: 8, right: 16, bottom: 8, left: 16 } } }
  ];

  const renderVisualEditor = () => (
    <SpacingContainer>
      <div className="spacing-visual">
        {/* Margin area */}
        <div 
          className="margin-area"
          style={{
            top: `${margin.top}px`,
            right: `${margin.right}px`,
            bottom: `${margin.bottom}px`,
            left: `${margin.left}px`
          }}
        >
          {/* Padding area */}
          <div 
            className="padding-area"
            style={{
              top: `${padding.top}px`,
              right: `${padding.right}px`,
              bottom: `${padding.bottom}px`,
              left: `${padding.left}px`
            }}
          >
            {/* Content area */}
            <div className="content-area">
              Content
            </div>
          </div>
        </div>
      </div>
      
      <div style={{ textAlign: 'center', marginTop: 8 }}>
        <Space size="small">
          <Text style={{ fontSize: 11, color: '#ffc107' }}>■ Margin</Text>
          <Text style={{ fontSize: 11, color: '#28a745' }}>■ Padding</Text>
          <Text style={{ fontSize: 11, color: '#007bff' }}>■ Content</Text>
        </Space>
      </div>
    </SpacingContainer>
  );

  const renderSpacingInputs = (property, spacing, linked, setLinked) => (
    <Card size="small" title={
      <Space>
        <span style={{ textTransform: 'capitalize' }}>{property}</span>
        <Button
          type="text"
          size="small"
          icon={linked ? <LinkOutlined /> : <UnlinkOutlined />}
          onClick={() => setLinked(!linked)}
          title={linked ? 'Unlink sides' : 'Link all sides'}
        />
      </Space>
    }>
      <SpacingGrid>
        {/* Top */}
        <div className="spacing-input" style={{ gridColumn: 2, gridRow: 1 }}>
          <InputNumber
            size="small"
            value={spacing.top}
            onChange={(val) => handleSpacingChange(property, 'top', val || 0)}
            min={0}
            placeholder="0"
          />
        </div>
        
        {/* Left */}
        <div className="spacing-input" style={{ gridColumn: 1, gridRow: 2 }}>
          <InputNumber
            size="small"
            value={spacing.left}
            onChange={(val) => handleSpacingChange(property, 'left', val || 0)}
            min={0}
            placeholder="0"
          />
        </div>
        
        {/* Center */}
        <div className="center-element">
          {property}
        </div>
        
        {/* Right */}
        <div className="spacing-input" style={{ gridColumn: 3, gridRow: 2 }}>
          <InputNumber
            size="small"
            value={spacing.right}
            onChange={(val) => handleSpacingChange(property, 'right', val || 0)}
            min={0}
            placeholder="0"
          />
        </div>
        
        {/* Bottom */}
        <div className="spacing-input" style={{ gridColumn: 2, gridRow: 3 }}>
          <InputNumber
            size="small"
            value={spacing.bottom}
            onChange={(val) => handleSpacingChange(property, 'bottom', val || 0)}
            min={0}
            placeholder="0"
          />
        </div>
      </SpacingGrid>
      
      <div style={{ marginTop: 12, textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: 11 }}>
          {formatSpacingValue(spacing)}
        </Text>
      </div>
    </Card>
  );

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {/* Unit selector */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Text strong>Spacing</Text>
        <Select
          value={unit}
          onChange={setUnit}
          size="small"
          style={{ width: 60 }}
        >
          {units.map(u => (
            <Option key={u} value={u}>{u}</Option>
          ))}
        </Select>
      </div>

      {/* Visual editor */}
      {showVisual && renderVisualEditor()}

      {/* Spacing inputs */}
      <Space direction="vertical" style={{ width: '100%' }}>
        {properties.includes('margin') && 
          renderSpacingInputs('margin', margin, linkedMargin, setLinkedMargin)
        }
        
        {properties.includes('padding') && 
          renderSpacingInputs('padding', padding, linkedPadding, setLinkedPadding)
        }
      </Space>

      {/* Presets */}
      {showPresets && (
        <Card size="small" title="Presets">
          <Space wrap>
            {presets.map((preset, index) => (
              <Button
                key={index}
                size="small"
                onClick={() => handlePresetClick(preset.value)}
              >
                {preset.label}
              </Button>
            ))}
          </Space>
        </Card>
      )}
    </Space>
  );
};

export default SpacingEditor;
