import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import { useAuth } from './AuthContext';

// Create the collaboration context
const CollaborationContext = createContext({
  // Session state
  currentSession: null,
  isConnected: false,
  isJoining: false,
  
  // Participants
  participants: [],
  activeParticipants: [],
  
  // Comments
  comments: [],
  
  // Operations
  pendingOperations: [],
  
  // Actions
  joinSession: () => {},
  leaveSession: () => {},
  createComment: () => {},
  updateComment: () => {},
  resolveComment: () => {},
  sendOperation: () => {},
  updatePresence: () => {},
});

// User colors for visual identification
const USER_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
];

export const CollaborationProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [currentSession, setCurrentSession] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [comments, setComments] = useState([]);
  const [pendingOperations, setPendingOperations] = useState([]);
  
  // WebSocket connection
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);
  
  // User presence tracking
  const [userPresence, setUserPresence] = useState({});
  const presenceUpdateTimeoutRef = useRef(null);
  
  // Get active participants (those who have been active recently)
  const activeParticipants = participants.filter(p => {
    const lastSeen = new Date(p.last_seen);
    const now = new Date();
    const timeDiff = now - lastSeen;
    return timeDiff < 5 * 60 * 1000; // Active if seen within 5 minutes
  });
  
  // Assign colors to participants
  const getParticipantColor = useCallback((userId) => {
    const index = participants.findIndex(p => p.user_id === userId);
    return USER_COLORS[index % USER_COLORS.length];
  }, [participants]);
  
  // WebSocket connection management
  const connectWebSocket = useCallback((sessionId) => {
    if (!isAuthenticated || !user) {
      console.warn('Cannot connect to collaboration WebSocket: user not authenticated');
      return;
    }
    
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/collaboration/${sessionId}/`;
      
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        console.log('Collaboration WebSocket connected');
        setIsConnected(true);
        
        // Start heartbeat
        heartbeatIntervalRef.current = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));
          }
        }, 30000);
        
        // Send join session message
        wsRef.current.send(JSON.stringify({
          type: 'join_session',
          session_id: sessionId
        }));
      };
      
      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      wsRef.current.onclose = (event) => {
        console.log('Collaboration WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        
        // Clear heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }
        
        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && currentSession) {
          scheduleReconnect(sessionId);
        }
      };
      
      wsRef.current.onerror = (error) => {
        console.error('Collaboration WebSocket error:', error);
        message.error('Connection error occurred');
      };
      
    } catch (error) {
      console.error('Error connecting to collaboration WebSocket:', error);
      message.error('Failed to connect to collaboration service');
    }
  }, [isAuthenticated, user, currentSession]);
  
  const scheduleReconnect = useCallback((sessionId) => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    reconnectTimeoutRef.current = setTimeout(() => {
      console.log('Attempting to reconnect to collaboration WebSocket...');
      connectWebSocket(sessionId);
    }, 3000);
  }, [connectWebSocket]);
  
  const disconnectWebSocket = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected');
      wsRef.current = null;
    }
    
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsConnected(false);
  }, []);
  
  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((data) => {
    switch (data.type) {
      case 'session_joined':
        setCurrentSession(data.data);
        setParticipants(data.data.participants || []);
        setComments(data.data.comments || []);
        setIsJoining(false);
        message.success('Joined collaboration session');
        break;
        
      case 'user_joined':
        setParticipants(prev => {
          const existing = prev.find(p => p.user_id === data.user_id);
          if (existing) {
            return prev.map(p => 
              p.user_id === data.user_id 
                ? { ...p, is_active: true, last_seen: data.timestamp }
                : p
            );
          } else {
            return [...prev, {
              user_id: data.user_id,
              username: data.username,
              is_active: true,
              last_seen: data.timestamp,
              role: 'editor'
            }];
          }
        });
        
        if (data.user_id !== user?.id) {
          message.info(`${data.username} joined the session`);
        }
        break;
        
      case 'user_left':
        setParticipants(prev => 
          prev.map(p => 
            p.user_id === data.user_id 
              ? { ...p, is_active: false }
              : p
          )
        );
        
        if (data.user_id !== user?.id) {
          message.info(`${data.username} left the session`);
        }
        break;
        
      case 'cursor_position':
        if (data.user_id !== user?.id) {
          setUserPresence(prev => ({
            ...prev,
            [data.user_id]: {
              ...prev[data.user_id],
              cursor_position: data.position,
              username: data.username,
              timestamp: data.timestamp
            }
          }));
        }
        break;
        
      case 'selection_change':
        if (data.user_id !== user?.id) {
          setUserPresence(prev => ({
            ...prev,
            [data.user_id]: {
              ...prev[data.user_id],
              selection: data.selection,
              username: data.username,
              timestamp: data.timestamp
            }
          }));
        }
        break;
        
      case 'comment_created':
        setComments(prev => [data.comment, ...prev]);
        if (data.comment.author !== user?.username) {
          message.info('New comment added');
        }
        break;
        
      case 'comment_updated':
        setComments(prev => 
          prev.map(c => 
            c.id === data.comment.id ? data.comment : c
          )
        );
        break;
        
      case 'comment_resolved':
        setComments(prev => 
          prev.map(c => 
            c.id === data.comment.id 
              ? { ...c, status: data.comment.status, resolved_at: data.comment.resolved_at }
              : c
          )
        );
        break;
        
      case 'component_operation':
        // Handle real-time component operations
        if (data.operation.user_id !== user?.id) {
          // This would trigger a callback to update the app state
          // The actual implementation would depend on the app's state management
          console.log('Received component operation:', data.operation);
        }
        break;
        
      case 'error':
        console.error('Collaboration error:', data.message);
        message.error(data.message);
        break;
        
      default:
        console.log('Unknown collaboration message type:', data.type);
    }
  }, [user]);
  
  // Join a collaboration session
  const joinSession = useCallback(async (sessionId) => {
    if (!isAuthenticated) {
      message.error('You must be logged in to join a collaboration session');
      return false;
    }
    
    setIsJoining(true);
    
    try {
      // Connect to WebSocket
      connectWebSocket(sessionId);
      return true;
    } catch (error) {
      console.error('Error joining session:', error);
      message.error('Failed to join collaboration session');
      setIsJoining(false);
      return false;
    }
  }, [isAuthenticated, connectWebSocket]);
  
  // Leave the current session
  const leaveSession = useCallback(() => {
    if (wsRef.current && currentSession) {
      wsRef.current.send(JSON.stringify({
        type: 'leave_session'
      }));
    }
    
    disconnectWebSocket();
    setCurrentSession(null);
    setParticipants([]);
    setComments([]);
    setUserPresence({});
    setPendingOperations([]);
    setIsJoining(false);
  }, [currentSession, disconnectWebSocket]);
  
  // Create a comment
  const createComment = useCallback((content, componentId = null, canvasPosition = null, parentId = null) => {
    if (!wsRef.current || !isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }
    
    wsRef.current.send(JSON.stringify({
      type: 'create_comment',
      content,
      component_id: componentId,
      canvas_position: canvasPosition,
      parent_id: parentId
    }));
  }, [isConnected]);
  
  // Update a comment
  const updateComment = useCallback((commentId, content) => {
    if (!wsRef.current || !isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }
    
    wsRef.current.send(JSON.stringify({
      type: 'update_comment',
      comment_id: commentId,
      content
    }));
  }, [isConnected]);
  
  // Resolve a comment
  const resolveComment = useCallback((commentId) => {
    if (!wsRef.current || !isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }
    
    wsRef.current.send(JSON.stringify({
      type: 'resolve_comment',
      comment_id: commentId
    }));
  }, [isConnected]);
  
  // Send a component operation
  const sendOperation = useCallback((operationType, targetId, operationData) => {
    if (!wsRef.current || !isConnected) {
      console.warn('Cannot send operation: not connected to collaboration session');
      return;
    }
    
    const operation = {
      type: 'component_operation',
      operation_type: operationType,
      target_id: targetId,
      operation_data: operationData,
      timestamp: Date.now()
    };
    
    wsRef.current.send(JSON.stringify(operation));
    
    // Add to pending operations
    setPendingOperations(prev => [...prev, operation]);
  }, [isConnected]);
  
  // Update user presence
  const updatePresence = useCallback((presenceData) => {
    if (!wsRef.current || !isConnected) {
      return;
    }
    
    // Debounce presence updates
    if (presenceUpdateTimeoutRef.current) {
      clearTimeout(presenceUpdateTimeoutRef.current);
    }
    
    presenceUpdateTimeoutRef.current = setTimeout(() => {
      if (presenceData.cursor_position) {
        wsRef.current.send(JSON.stringify({
          type: 'cursor_position',
          position: presenceData.cursor_position
        }));
      }
      
      if (presenceData.selection) {
        wsRef.current.send(JSON.stringify({
          type: 'selection_change',
          selection: presenceData.selection
        }));
      }
    }, 100); // Debounce by 100ms
  }, [isConnected]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnectWebSocket();
      if (presenceUpdateTimeoutRef.current) {
        clearTimeout(presenceUpdateTimeoutRef.current);
      }
    };
  }, [disconnectWebSocket]);
  
  const value = {
    // Session state
    currentSession,
    isConnected,
    isJoining,
    
    // Participants
    participants,
    activeParticipants,
    userPresence,
    getParticipantColor,
    
    // Comments
    comments,
    
    // Operations
    pendingOperations,
    
    // Actions
    joinSession,
    leaveSession,
    createComment,
    updateComment,
    resolveComment,
    sendOperation,
    updatePresence,
  };
  
  return (
    <CollaborationContext.Provider value={value}>
      {children}
    </CollaborationContext.Provider>
  );
};

// Hook to use collaboration context
export const useCollaboration = () => {
  const context = useContext(CollaborationContext);
  if (!context) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
};

export default CollaborationContext;
