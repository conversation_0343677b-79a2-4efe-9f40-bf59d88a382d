/**
 * API Endpoint Checker Utility
 * 
 * This utility helps diagnose API connectivity issues by checking multiple
 * possible endpoints and reporting which ones are available.
 */

// Get API base URL from environment or use default
const API_BASE_URL = process.env.REACT_APP_API_URL || '';

// List of endpoints to check
const ENDPOINTS_TO_CHECK = [
  '/api/app-data/',
  '/get_app_data/',
  '/api/v1/apps/',
  '/api/apps/',
  '/api/status/',
  '/api/health/',
  '/health/',
  '/api/health-check',
];

/**
 * Check if an endpoint is available
 * @param {string} endpoint - The endpoint to check
 * @returns {Promise<Object>} - Result object with status and response info
 */
const checkEndpoint = async (endpoint) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  try {
    console.log(`Checking endpoint: ${url}`);
    const startTime = Date.now();
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      },
      // Short timeout to avoid hanging
      signal: AbortSignal.timeout(5000)
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    let responseData = null;
    let responseError = null;
    
    try {
      // Try to parse as JSON
      responseData = await response.json();
    } catch (error) {
      responseError = 'Invalid JSON response';
    }
    
    return {
      endpoint,
      url,
      available: response.ok,
      status: response.status,
      statusText: response.statusText,
      responseTime,
      responseData,
      responseError,
      headers: Object.fromEntries([...response.headers.entries()])
    };
  } catch (error) {
    return {
      endpoint,
      url,
      available: false,
      error: error.message || 'Unknown error',
      errorName: error.name,
      errorStack: error.stack
    };
  }
};

/**
 * Check all endpoints and return results
 * @returns {Promise<Object>} - Results for all endpoints
 */
export const checkAllEndpoints = async () => {
  const results = await Promise.all(
    ENDPOINTS_TO_CHECK.map(endpoint => checkEndpoint(endpoint))
  );
  
  // Group by availability
  const available = results.filter(result => result.available);
  const unavailable = results.filter(result => !result.available);
  
  // Log results
  console.group('API Endpoint Check Results');
  console.log(`Available endpoints: ${available.length}`);
  console.log(`Unavailable endpoints: ${unavailable.length}`);
  
  if (available.length > 0) {
    console.group('Available Endpoints');
    available.forEach(result => {
      console.log(`${result.endpoint} (${result.status}) - ${result.responseTime}ms`);
    });
    console.groupEnd();
  }
  
  if (unavailable.length > 0) {
    console.group('Unavailable Endpoints');
    unavailable.forEach(result => {
      console.log(`${result.endpoint} - ${result.error || result.status}`);
    });
    console.groupEnd();
  }
  console.groupEnd();
  
  return {
    results,
    available,
    unavailable,
    allAvailable: unavailable.length === 0,
    anyAvailable: available.length > 0,
    timestamp: new Date().toISOString()
  };
};

/**
 * Run the endpoint check and display results in the UI
 * @param {Function} onComplete - Callback with results
 */
export const runApiEndpointCheck = async (onComplete) => {
  console.log('Starting API endpoint check...');
  const results = await checkAllEndpoints();
  
  if (onComplete && typeof onComplete === 'function') {
    onComplete(results);
  }
  
  return results;
};

export default {
  checkAllEndpoints,
  runApiEndpointCheck
};
