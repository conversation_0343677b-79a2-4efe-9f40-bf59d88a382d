import React from 'react';
import { useSelector } from 'react-redux';

const LoadingStateManager = ({ children }) => {
  const { loading, error, usingCachedData } = useSelector(state => ({
    loading: state.app.loading,
    error: state.app.error,
    usingCachedData: state.app.usingCachedData
  }));

  // Show a minimal loading indicator
  if (loading && !usingCachedData) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading application data...</p>
      </div>
    );
  }

  // Show error with retry option
  if (error && !usingCachedData) {
    return (
      <div className="error-container">
        <h3>Unable to load latest data</h3>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>
          Retry
        </button>
      </div>
    );
  }

  // Show cached data indicator if using cached data
  return (
    <>
      {usingCachedData && (
        <div className="cached-data-indicator">
          Using cached data. <button onClick={() => window.location.reload()}>Refresh</button>
        </div>
      )}
      {children}
    </>
  );
};

export default LoadingStateManager;