import React, { useEffect, useRef } from 'react';
import styled, { keyframes, css } from 'styled-components';
import { CheckCircleOutlined, CloseCircleOutlined, DragOutlined } from '@ant-design/icons';

// Keyframe animations
const pulse = keyframes`
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
`;

const shake = keyframes`
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
`;

const bounce = keyframes`
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const glow = keyframes`
  0%, 100% {
    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);
  }
`;

// Styled components
const DropIndicator = styled.div`
  position: absolute;
  width: 100%;
  height: 4px;
  background: ${props => props.isValid ? '#52c41a' : '#ff4d4f'};
  border-radius: 2px;
  z-index: 1000;
  animation: ${pulse} 1.5s ease-in-out infinite;
  
  &::before {
    content: '';
    position: absolute;
    left: -6px;
    top: -2px;
    width: 8px;
    height: 8px;
    background: ${props => props.isValid ? '#52c41a' : '#ff4d4f'};
    border-radius: 50%;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  }
  
  &::after {
    content: '';
    position: absolute;
    right: -6px;
    top: -2px;
    width: 8px;
    height: 8px;
    background: ${props => props.isValid ? '#52c41a' : '#ff4d4f'};
    border-radius: 50%;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  }
`;

const DropZoneOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 3px dashed ${props => props.isValid ? '#52c41a' : '#ff4d4f'};
  border-radius: 12px;
  background: ${props => props.isValid
    ? 'linear-gradient(135deg, rgba(82, 196, 26, 0.08) 0%, rgba(82, 196, 26, 0.15) 100%)'
    : 'linear-gradient(135deg, rgba(255, 77, 79, 0.08) 0%, rgba(255, 77, 79, 0.15) 100%)'
  };
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
  animation: ${fadeIn} 0.3s ease-out;
  backdrop-filter: blur(4px);
  box-shadow: ${props => props.isValid
    ? '0 8px 32px rgba(82, 196, 26, 0.2)'
    : '0 8px 32px rgba(255, 77, 79, 0.2)'
  };

  ${props => props.isValid && css`
    animation: ${pulse} 1.5s ease-in-out infinite;

    &::before {
      content: '';
      position: absolute;
      top: -6px;
      left: -6px;
      right: -6px;
      bottom: -6px;
      border: 2px solid rgba(82, 196, 26, 0.3);
      border-radius: 16px;
      animation: ${pulse} 2s ease-in-out infinite reverse;
    }
  `}

  ${props => !props.isValid && css`
    animation: ${shake} 0.5s ease-in-out;

    &::before {
      content: '';
      position: absolute;
      top: -6px;
      left: -6px;
      right: -6px;
      bottom: -6px;
      border: 2px solid rgba(255, 77, 79, 0.3);
      border-radius: 16px;
    }
  `}
`;

const DropZoneMessage = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  font-weight: 600;
  font-size: 16px;
  color: ${props => props.isValid ? '#52c41a' : '#ff4d4f'};
  animation: ${fadeIn} 0.3s ease-out 0.1s both;
  backdrop-filter: blur(8px);
  border: 1px solid ${props => props.isValid ? 'rgba(82, 196, 26, 0.2)' : 'rgba(255, 77, 79, 0.2)'};

  svg {
    font-size: 20px;
  }

  /* Add subtle hint text */
  &::after {
    content: '${props => props.isValid ? 'Release to add' : 'Choose valid location'}';
    display: block;
    font-size: 12px;
    font-weight: 400;
    color: ${props => props.isValid ? 'rgba(82, 196, 26, 0.7)' : 'rgba(255, 77, 79, 0.7)'};
    margin-top: 4px;
  }
`;

const GhostElement = styled.div`
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  opacity: 0.8;
  transform: rotate(3deg) scale(0.95);
  filter: blur(0.5px);
  transition: all 0.15s ease-out;
  border: 2px solid #1890ff;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  box-shadow: 0 12px 40px rgba(24, 144, 255, 0.4);
  backdrop-filter: blur(4px);

  /* Add floating animation */
  animation: ${glow} 2s ease-in-out infinite;

  /* Enhanced visual depth */
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 1px solid rgba(24, 144, 255, 0.3);
    border-radius: 10px;
    animation: ${pulse} 1.5s ease-in-out infinite;
  }
`;

const DragPreview = styled.div`
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 2px solid #1890ff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  font-size: 14px;
  color: #1890ff;
  animation: ${glow} 2s ease-in-out infinite;
  backdrop-filter: blur(8px);
  min-width: 120px;

  /* Add component type indicator */
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #1890ff;
    border-radius: 50%;
    animation: ${pulse} 1s ease-in-out infinite;
  }

  svg {
    font-size: 16px;
  }
`;

const HoverIndicator = styled.div`
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #1890ff;
  border-radius: 6px;
  background: rgba(24, 144, 255, 0.05);
  pointer-events: none;
  z-index: 10;
  animation: ${fadeIn} 0.2s ease-out;
  
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 1px solid rgba(24, 144, 255, 0.3);
    border-radius: 8px;
    animation: ${pulse} 2s ease-in-out infinite;
  }
`;

const SuccessIndicator = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #52c41a;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  z-index: 1001;
  animation: ${bounce} 0.6s ease-out;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
`;

// Component for drop indicator line
export const DropIndicatorLine = ({ position, isValid = true, visible = false }) => {
  if (!visible || !position) return null;

  return (
    <DropIndicator
      isValid={isValid}
      style={{
        top: position.y,
        left: position.x,
        width: position.width || '100%'
      }}
    />
  );
};

// Component for drop zone overlay
export const DropZoneOverlayComponent = ({ isValid = true, visible = false, message }) => {
  if (!visible) return null;

  return (
    <DropZoneOverlay isValid={isValid}>
      <DropZoneMessage isValid={isValid}>
        {isValid ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
        {message || (isValid ? 'Drop here to add component' : 'Invalid drop target')}
      </DropZoneMessage>
    </DropZoneOverlay>
  );
};

// Component for ghost element during drag
export const DragGhost = ({ visible = false, position, children, componentType }) => {
  const ghostRef = useRef(null);

  useEffect(() => {
    if (ghostRef.current && visible && position) {
      ghostRef.current.style.left = `${position.x}px`;
      ghostRef.current.style.top = `${position.y}px`;
    }
  }, [visible, position]);

  if (!visible) return null;

  return (
    <GhostElement ref={ghostRef}>
      {children || (
        <DragPreview>
          <DragOutlined />
          {componentType || 'Component'}
        </DragPreview>
      )}
    </GhostElement>
  );
};

// Component for hover indicator
export const HoverIndicatorComponent = ({ visible = false, targetRef }) => {
  const indicatorRef = useRef(null);

  useEffect(() => {
    if (indicatorRef.current && targetRef?.current && visible) {
      const rect = targetRef.current.getBoundingClientRect();
      const indicator = indicatorRef.current;

      indicator.style.position = 'fixed';
      indicator.style.top = `${rect.top}px`;
      indicator.style.left = `${rect.left}px`;
      indicator.style.width = `${rect.width}px`;
      indicator.style.height = `${rect.height}px`;
    }
  }, [visible, targetRef]);

  if (!visible) return null;

  return <HoverIndicator ref={indicatorRef} />;
};

// Component for success indicator
export const SuccessIndicatorComponent = ({ visible = false, message = 'Component added!' }) => {
  if (!visible) return null;

  return (
    <SuccessIndicator>
      <CheckCircleOutlined />
      {message}
    </SuccessIndicator>
  );
};

// Main visual feedback component
const DragVisualFeedback = ({
  isDragging = false,
  isOver = false,
  isValid = true,
  dropPosition,
  ghostPosition,
  hoveredElement,
  draggedComponent,
  showSuccess = false,
  successMessage,
  dropMessage,
  children
}) => {
  return (
    <>
      {/* Drop indicator line */}
      <DropIndicatorLine
        position={dropPosition}
        isValid={isValid}
        visible={isDragging && isOver && dropPosition}
      />

      {/* Drop zone overlay */}
      <DropZoneOverlayComponent
        isValid={isValid}
        visible={isDragging && isOver}
        message={dropMessage}
      />

      {/* Drag ghost */}
      <DragGhost
        visible={isDragging}
        position={ghostPosition}
        componentType={draggedComponent?.type}
      />

      {/* Hover indicator */}
      <HoverIndicatorComponent
        visible={!isDragging && hoveredElement}
        targetRef={hoveredElement}
      />

      {/* Success indicator */}
      <SuccessIndicatorComponent
        visible={showSuccess}
        message={successMessage}
      />

      {children}
    </>
  );
};

export default DragVisualFeedback;
