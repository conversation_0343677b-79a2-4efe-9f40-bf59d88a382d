import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { debounce, throttle } from 'lodash';

/**
 * Custom hook for real-time preview functionality
 * Handles instant updates, performance optimization, and WebSocket synchronization
 */
const useRealTimePreview = ({
  components = [],
  onUpdateComponent,
  onAddComponent,
  onDeleteComponent,
  websocketService,
  updateDelay = 300,
  throttleDelay = 100,
  enableWebSocket = true
}) => {
  // State for tracking updates
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [pendingUpdates, setPendingUpdates] = useState(new Map());
  const [updateQueue, setUpdateQueue] = useState([]);
  
  // Refs for cleanup and performance
  const updateTimeoutRef = useRef(null);
  const websocketRef = useRef(websocketService);
  const componentCacheRef = useRef(new Map());
  
  // Redux state
  const dispatch = useDispatch();
  const websocketConnected = useSelector(state => state.websocket?.connected || false);
  
  // Update WebSocket reference when service changes
  useEffect(() => {
    websocketRef.current = websocketService;
  }, [websocketService]);

  // Debounced update function for batching changes
  const debouncedUpdate = useCallback(
    debounce((updates) => {
      if (updates.length === 0) return;
      
      setIsUpdating(true);
      
      // Process all pending updates
      updates.forEach(({ type, componentId, data }) => {
        switch (type) {
          case 'update':
            if (onUpdateComponent) {
              onUpdateComponent(componentId, data);
            }
            break;
          case 'add':
            if (onAddComponent) {
              onAddComponent(data);
            }
            break;
          case 'delete':
            if (onDeleteComponent) {
              onDeleteComponent(componentId);
            }
            break;
          default:
            console.warn('Unknown update type:', type);
        }
      });
      
      // Send WebSocket updates if enabled and connected
      if (enableWebSocket && websocketConnected && websocketRef.current) {
        updates.forEach(({ type, componentId, data }) => {
          websocketRef.current.send({
            type: `component_${type}`,
            component_id: componentId,
            component_data: data,
            timestamp: new Date().toISOString()
          });
        });
      }
      
      setLastUpdateTime(new Date());
      setUpdateQueue([]);
      setPendingUpdates(new Map());
      
      // Clear updating state after a short delay
      setTimeout(() => setIsUpdating(false), 500);
    }, updateDelay),
    [onUpdateComponent, onAddComponent, onDeleteComponent, enableWebSocket, websocketConnected, updateDelay]
  );

  // Throttled function for immediate visual feedback
  const throttledVisualUpdate = useCallback(
    throttle((componentId, updates) => {
      // Update component cache for immediate visual feedback
      const currentCache = componentCacheRef.current.get(componentId) || {};
      componentCacheRef.current.set(componentId, { ...currentCache, ...updates });
      
      // Force re-render by updating a timestamp
      setLastUpdateTime(new Date());
    }, throttleDelay),
    [throttleDelay]
  );

  // Main update function
  const updateComponent = useCallback((componentId, updates, immediate = false) => {
    if (!componentId) return;
    
    // Add to pending updates
    const currentPending = pendingUpdates.get(componentId) || {};
    const newPending = { ...currentPending, ...updates };
    setPendingUpdates(prev => new Map(prev.set(componentId, newPending)));
    
    // Add to update queue
    setUpdateQueue(prev => [
      ...prev.filter(item => !(item.type === 'update' && item.componentId === componentId)),
      { type: 'update', componentId, data: newPending }
    ]);
    
    // Immediate visual feedback
    if (immediate) {
      throttledVisualUpdate(componentId, updates);
    }
    
    // Trigger debounced update
    debouncedUpdate(updateQueue);
  }, [pendingUpdates, updateQueue, debouncedUpdate, throttledVisualUpdate]);

  // Add component function
  const addComponent = useCallback((componentData, immediate = false) => {
    const componentId = componentData.id || Date.now().toString();
    const newComponent = { ...componentData, id: componentId };
    
    setUpdateQueue(prev => [...prev, { type: 'add', componentId, data: newComponent }]);
    
    if (immediate) {
      componentCacheRef.current.set(componentId, newComponent);
      setLastUpdateTime(new Date());
    }
    
    debouncedUpdate(updateQueue);
    return componentId;
  }, [updateQueue, debouncedUpdate]);

  // Delete component function
  const deleteComponent = useCallback((componentId, immediate = false) => {
    if (!componentId) return;
    
    setUpdateQueue(prev => [...prev, { type: 'delete', componentId }]);
    
    if (immediate) {
      componentCacheRef.current.delete(componentId);
      setLastUpdateTime(new Date());
    }
    
    debouncedUpdate(updateQueue);
  }, [updateQueue, debouncedUpdate]);

  // Get component with cached updates
  const getComponent = useCallback((componentId) => {
    const originalComponent = components.find(c => c.id === componentId);
    const cachedUpdates = componentCacheRef.current.get(componentId);
    const pendingUpdate = pendingUpdates.get(componentId);
    
    return {
      ...originalComponent,
      ...cachedUpdates,
      ...pendingUpdate
    };
  }, [components, pendingUpdates]);

  // Get all components with cached updates
  const getAllComponents = useCallback(() => {
    return components.map(component => getComponent(component.id));
  }, [components, getComponent]);

  // Force update function for manual refresh
  const forceUpdate = useCallback(() => {
    debouncedUpdate.flush();
    componentCacheRef.current.clear();
    setPendingUpdates(new Map());
    setUpdateQueue([]);
  }, [debouncedUpdate]);

  // WebSocket message handler
  useEffect(() => {
    if (!enableWebSocket || !websocketRef.current) return;
    
    const handleWebSocketMessage = (message) => {
      if (message.type?.startsWith('component_')) {
        const { component_id, component_data, timestamp } = message;
        
        // Update component cache with remote changes
        if (component_data && component_id) {
          componentCacheRef.current.set(component_id, component_data);
          setLastUpdateTime(new Date(timestamp));
        }
      }
    };
    
    websocketRef.current.addEventListener('message', handleWebSocketMessage);
    
    return () => {
      if (websocketRef.current) {
        websocketRef.current.removeEventListener('message', handleWebSocketMessage);
      }
    };
  }, [enableWebSocket]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      debouncedUpdate.cancel();
      throttledVisualUpdate.cancel();
    };
  }, [debouncedUpdate, throttledVisualUpdate]);

  return {
    // State
    isUpdating,
    lastUpdateTime,
    websocketConnected,
    hasPendingUpdates: pendingUpdates.size > 0,
    
    // Functions
    updateComponent,
    addComponent,
    deleteComponent,
    getComponent,
    getAllComponents,
    forceUpdate,
    
    // Utilities
    clearCache: () => componentCacheRef.current.clear(),
    getPendingUpdates: () => Array.from(pendingUpdates.entries()),
    getUpdateQueueSize: () => updateQueue.length
  };
};

export default useRealTimePreview;
