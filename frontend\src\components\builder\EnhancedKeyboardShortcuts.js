/**
 * Enhanced Keyboard Shortcuts System
 * 
 * Comprehensive keyboard shortcuts with visual feedback, customization,
 * and quick action toolbars for improved productivity.
 */

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  Modal,
  Table,
  Input,
  Button,
  Space,
  Tag,
  Tooltip,
  Typography,
  Divider,
  Card,
  Badge,
  Alert,
  Switch,
  Tabs,
  FloatButton,
  message,
  Popover
} from 'antd';
import {
  KeyboardOutlined,
  SettingOutlined,
  CopyOutlined,
  ScissorOutlined,
  FileAddOutlined,
  DeleteOutlined,
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  SearchOutlined,
  FullscreenOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  QuestionCircleOutlined,
  ThunderboltOutlined,
  StarOutlined,
  CloseOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme } from '../../design-system';
import useKeyboardShortcuts from '../../hooks/useKeyboardShortcuts';

const { Text, Title } = Typography;
const { TabPane } = Tabs;

// Enhanced styled components
const ShortcutContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
`;

const ShortcutPanel = styled.div`
  background: ${theme.colors.background.paper};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.xl};
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  padding: ${theme.spacing[6]};
  margin: ${theme.spacing[4]};
`;

const QuickActionBar = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  background: ${theme.colors.background.paper};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.lg};
  padding: ${theme.spacing[2]};
  display: flex;
  gap: ${theme.spacing[1]};
  z-index: 1000;
  border: 1px solid ${theme.colors.border.light};
  backdrop-filter: blur(8px);
  
  &.hidden {
    transform: translateY(-100%);
    opacity: 0;
    pointer-events: none;
  }
  
  transition: all 0.3s ease;
`;

const ShortcutKey = styled.span`
  background: ${theme.colors.neutral[100]};
  border: 1px solid ${theme.colors.border.medium};
  border-radius: ${theme.borderRadius.sm};
  padding: 2px 6px;
  font-family: ${theme.typography.fontFamily.mono};
  font-size: ${theme.typography.fontSize.xs};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.text.primary};
  margin: 0 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
`;

const ShortcutFeedback = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: ${theme.colors.primary.main};
  color: white;
  padding: ${theme.spacing[3]} ${theme.spacing[4]};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.xl};
  z-index: 10000;
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  font-weight: ${theme.typography.fontWeight.semibold};
  animation: shortcutFeedback 0.8s ease-out;
  
  @keyframes shortcutFeedback {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
    20% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.05);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(1);
    }
  }
`;

// Default keyboard shortcuts configuration
const DEFAULT_SHORTCUTS = {
  // File operations
  'ctrl+s': { action: 'save', label: 'Save Project', category: 'File', icon: <SaveOutlined /> },
  'ctrl+n': { action: 'new', label: 'New Component', category: 'File', icon: <FileAddOutlined /> },
  
  // Edit operations
  'ctrl+c': { action: 'copy', label: 'Copy Component', category: 'Edit', icon: <CopyOutlined /> },
  'ctrl+v': { action: 'paste', label: 'Paste Component', category: 'Edit', icon: <ScissorOutlined /> },
  'ctrl+x': { action: 'cut', label: 'Cut Component', category: 'Edit', icon: <ScissorOutlined /> },
  'ctrl+z': { action: 'undo', label: 'Undo', category: 'Edit', icon: <UndoOutlined /> },
  'ctrl+y': { action: 'redo', label: 'Redo', category: 'Edit', icon: <RedoOutlined /> },
  'delete': { action: 'delete', label: 'Delete Component', category: 'Edit', icon: <DeleteOutlined /> },
  
  // View operations
  'f11': { action: 'fullscreen', label: 'Toggle Fullscreen', category: 'View', icon: <FullscreenOutlined /> },
  'ctrl+shift+p': { action: 'preview', label: 'Toggle Preview', category: 'View', icon: <EyeOutlined /> },
  'ctrl+f': { action: 'search', label: 'Search Components', category: 'View', icon: <SearchOutlined /> },
  
  // Navigation
  'alt+1': { action: 'focusPalette', label: 'Focus Component Palette', category: 'Navigation', icon: <ThunderboltOutlined /> },
  'alt+2': { action: 'focusCanvas', label: 'Focus Canvas', category: 'Navigation', icon: <ThunderboltOutlined /> },
  'alt+3': { action: 'focusProperties', label: 'Focus Properties', category: 'Navigation', icon: <ThunderboltOutlined /> },
  
  // Help
  'f1': { action: 'help', label: 'Show Help', category: 'Help', icon: <QuestionCircleOutlined /> },
  'ctrl+shift+k': { action: 'shortcuts', label: 'Show Shortcuts', category: 'Help', icon: <KeyboardOutlined /> },
  
  // Quick actions
  'ctrl+space': { action: 'quickActions', label: 'Quick Actions', category: 'Quick', icon: <ThunderboltOutlined /> },
  'escape': { action: 'escape', label: 'Cancel/Close', category: 'Quick', icon: <CloseOutlined /> }
};

export default function EnhancedKeyboardShortcuts({
  onAction,
  showQuickActions = true,
  enableCustomization = true,
  showFeedback = true
}) {
  const [showShortcutsPanel, setShowShortcutsPanel] = useState(false);
  const [showQuickActionBar, setShowQuickActionBar] = useState(showQuickActions);
  const [shortcuts, setShortcuts] = useState(DEFAULT_SHORTCUTS);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('All');
  const feedbackTimeoutRef = useRef(null);

  // Get categories for filtering
  const categories = useMemo(() => {
    const cats = ['All', ...new Set(Object.values(shortcuts).map(s => s.category))];
    return cats;
  }, [shortcuts]);

  // Filter shortcuts based on search and category
  const filteredShortcuts = useMemo(() => {
    return Object.entries(shortcuts).filter(([key, shortcut]) => {
      const matchesSearch = !searchTerm || 
        shortcut.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        key.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = activeCategory === 'All' || shortcut.category === activeCategory;
      
      return matchesSearch && matchesCategory;
    });
  }, [shortcuts, searchTerm, activeCategory]);

  // Show feedback message
  const showFeedback = useCallback((message, icon) => {
    if (!showFeedback) return;
    
    setFeedbackMessage({ text: message, icon });
    
    if (feedbackTimeoutRef.current) {
      clearTimeout(feedbackTimeoutRef.current);
    }
    
    feedbackTimeoutRef.current = setTimeout(() => {
      setFeedbackMessage('');
    }, 800);
  }, [showFeedback]);

  // Handle shortcut action
  const handleShortcutAction = useCallback((action, shortcutKey) => {
    const shortcut = shortcuts[shortcutKey];
    
    if (onAction) {
      onAction(action, shortcutKey);
    }
    
    // Show feedback
    if (shortcut) {
      showFeedback(shortcut.label, shortcut.icon);
    }
    
    // Handle built-in actions
    switch (action) {
      case 'shortcuts':
        setShowShortcutsPanel(true);
        break;
      case 'escape':
        setShowShortcutsPanel(false);
        break;
      default:
        break;
    }
  }, [shortcuts, onAction, showFeedback]);

  // Create shortcuts object for the hook
  const shortcutHandlers = useMemo(() => {
    const handlers = {};
    Object.entries(shortcuts).forEach(([key, shortcut]) => {
      handlers[key] = () => handleShortcutAction(shortcut.action, key);
    });
    return handlers;
  }, [shortcuts, handleShortcutAction]);

  // Register keyboard shortcuts
  useKeyboardShortcuts(shortcutHandlers);

  // Quick action buttons
  const quickActions = [
    { action: 'save', icon: <SaveOutlined />, tooltip: 'Save (Ctrl+S)', color: '#52c41a' },
    { action: 'undo', icon: <UndoOutlined />, tooltip: 'Undo (Ctrl+Z)', color: '#1890ff' },
    { action: 'redo', icon: <RedoOutlined />, tooltip: 'Redo (Ctrl+Y)', color: '#1890ff' },
    { action: 'copy', icon: <CopyOutlined />, tooltip: 'Copy (Ctrl+C)', color: '#722ed1' },
    { action: 'paste', icon: <ScissorOutlined />, tooltip: 'Paste (Ctrl+V)', color: '#722ed1' },
    { action: 'preview', icon: <EyeOutlined />, tooltip: 'Preview (Ctrl+Shift+P)', color: '#fa8c16' }
  ];

  return (
    <>
      {/* Quick Action Bar */}
      {showQuickActionBar && (
        <QuickActionBar className={showQuickActionBar ? '' : 'hidden'}>
          {quickActions.map((action, index) => (
            <Tooltip key={index} title={action.tooltip} placement="bottom">
              <Button
                type="text"
                icon={action.icon}
                size="small"
                onClick={() => handleShortcutAction(action.action)}
                style={{ 
                  color: action.color,
                  border: `1px solid ${action.color}20`,
                  background: `${action.color}10`
                }}
              />
            </Tooltip>
          ))}
          
          <Divider type="vertical" style={{ margin: '0 4px' }} />
          
          <Tooltip title="Keyboard Shortcuts (Ctrl+Shift+K)" placement="bottom">
            <Button
              type="text"
              icon={<KeyboardOutlined />}
              size="small"
              onClick={() => setShowShortcutsPanel(true)}
            />
          </Tooltip>
          
          <Tooltip title="Hide Quick Actions" placement="bottom">
            <Button
              type="text"
              icon={<CloseOutlined />}
              size="small"
              onClick={() => setShowQuickActionBar(false)}
            />
          </Tooltip>
        </QuickActionBar>
      )}

      {/* Floating Action Button to show quick actions */}
      {!showQuickActionBar && (
        <FloatButton
          icon={<ThunderboltOutlined />}
          tooltip="Show Quick Actions"
          onClick={() => setShowQuickActionBar(true)}
          style={{ right: 24, top: 24 }}
        />
      )}

      {/* Keyboard Shortcuts Panel */}
      {showShortcutsPanel && (
        <ShortcutContainer onClick={() => setShowShortcutsPanel(false)}>
          <ShortcutPanel onClick={(e) => e.stopPropagation()}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: theme.spacing[4] }}>
              <Title level={3} style={{ margin: 0 }}>
                <KeyboardOutlined /> Keyboard Shortcuts
              </Title>
              <Button
                type="text"
                icon={<CloseOutlined />}
                onClick={() => setShowShortcutsPanel(false)}
              />
            </div>

            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* Search and Filter */}
              <div style={{ display: 'flex', gap: theme.spacing[2] }}>
                <Input
                  placeholder="Search shortcuts..."
                  prefix={<SearchOutlined />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  allowClear
                  style={{ flex: 1 }}
                />
              </div>

              {/* Category Tabs */}
              <Tabs activeKey={activeCategory} onChange={setActiveCategory}>
                {categories.map(category => (
                  <TabPane tab={category} key={category}>
                    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                      {filteredShortcuts
                        .filter(([, shortcut]) => activeCategory === 'All' || shortcut.category === activeCategory)
                        .map(([key, shortcut]) => (
                          <Card key={key} size="small" style={{ marginBottom: theme.spacing[2] }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Space>
                                {shortcut.icon}
                                <Text strong>{shortcut.label}</Text>
                              </Space>
                              <div>
                                {key.split('+').map((k, i) => (
                                  <ShortcutKey key={i}>{k.toUpperCase()}</ShortcutKey>
                                ))}
                              </div>
                            </div>
                          </Card>
                        ))}
                    </div>
                  </TabPane>
                ))}
              </Tabs>

              <Alert
                message="Pro Tip"
                description="Press Ctrl+Shift+K anytime to view this shortcuts panel. Most shortcuts work globally throughout the App Builder."
                type="info"
                showIcon
              />
            </Space>
          </ShortcutPanel>
        </ShortcutContainer>
      )}

      {/* Feedback Message */}
      {feedbackMessage && (
        <ShortcutFeedback>
          {feedbackMessage.icon}
          {feedbackMessage.text}
        </ShortcutFeedback>
      )}
    </>
  );
}
