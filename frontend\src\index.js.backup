import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import store from './redux/store';
import { BrowserRouter as Router } from 'react-router-dom';
import Routes from './Routes';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './components/theme/ThemeManager';
import EnhancedErrorBoundary from './components/common/EnhancedErrorBoundary';
window.fetch = (function (originalFetch) {
  return function (url, options) {
    try {
      // Your mock API logic here
      // ...

      // Example:
      if (url === '/api/some-endpoint') {
        return Promise.resolve({
          json: () => Promise.resolve({ data: 'mock data' }),
          ok: true,
          status: 200,
        });
      }

      // If no mock is found, call the original fetch
      return originalFetch.call(window, url, options);
    } catch (error) {
      console.error('Error in mockApiServer.js:', error);
      // Return a rejected promise or a default response
      return Promise.reject(error); // Or return a default response
    }
  };
})(window.fetch);
// Import global theme styles
import './styles/globalTheme.css';
import './styles/theme.css';

// Expose React to global scope for debugging and verification
window.React = React;
window.ReactDOM = { createRoot };

// Add debugging information
console.log('🚀 React loaded successfully:', React.version);
console.log('🔧 React available globally:', typeof window.React);
console.log('🏗️ ReactDOM createRoot available:', typeof createRoot);

// Add global styles directly to avoid CSS import issues
const style = document.createElement('style');
style.textContent = `
  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(style);

// Import error tracker
import errorTracker from './utils/errorTracker';

// Import chunk error handler
import { setupChunkErrorHandler } from './utils/chunkErrorHandler';

// Initialize error tracker with custom configuration
errorTracker.updateConfig({
  enabled: true,
  samplingRate: 1.0,
  reportingEndpoint: '/api/errors/',  // Add trailing slash for Django
  logToConsole: true
});

// Setup chunk error handler
setupChunkErrorHandler();

// Add global error handler to log detailed information
window.addEventListener('error', (event) => {
  console.error('Global error caught:', {
    message: event.message,
    source: event.filename,
    lineNo: event.lineno,
    colNo: event.colno,
    error: event.error
  });

  // Error is already tracked by the error tracker
});

// Add unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason);

  // Error is already tracked by the error tracker
});

// Initialize service worker cleanup
initServiceWorkerCleanup();

// Configure API mode - use real API instead of mocks
// Check if we should use real API (default to true for real backend testing)
const useRealApi = process.env.REACT_APP_USE_REAL_API !== 'false';
const isDev = process.env.NODE_ENV === 'development';

if (useRealApi) {
  console.log('🌐 Using real backend API at http://localhost:8000');
  console.log('🔌 WebSocket will connect to ws://localhost:8765');

  // Set global flag for real API usage
  window.USE_REAL_API = true;
  window.MOCK_SERVERS_ENABLED = false;
} else {
  // Initialize mock API server (only if explicitly disabled real API)
  initMockApiServer({
    enabled: isDev,
    delay: 300,
    logRequests: true
  });
  console.log(`Mock API server ${isDev ? 'initialized' : 'disabled'} for testing`);

  // Initialize mock WebSocket server
  initMockWebSocketServer({
    enabled: isDev,
    logMessages: true
  });
  console.log(`Mock WebSocket server ${isDev ? 'initialized' : 'disabled'} for testing`);

  // Add a global flag to indicate mock servers are enabled
  window.MOCK_SERVERS_ENABLED = isDev;
  window.USE_REAL_API = false;
}

// Enhanced Service Worker registration with better error handling
// TEMPORARILY DISABLED FOR DEBUGGING
if (false && 'serviceWorker' in navigator) {
  console.log('🔧 Service Worker API is available');

  window.addEventListener('load', () => {
    console.log('🚀 Registering Service Worker...');

    navigator.serviceWorker.register('/service-worker.js')
      .then((registration) => {
        console.log('✅ Service Worker registered successfully:', {
          scope: registration.scope,
          updateViaCache: registration.updateViaCache,
          installing: !!registration.installing,
          waiting: !!registration.waiting,
          active: !!registration.active
        });

        // Check for updates
        registration.addEventListener('updatefound', () => {
          console.log('🔄 Service Worker update found');
          const newWorker = registration.installing;

          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              console.log(`🔄 Service Worker state changed to: ${newWorker.state}`);

              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                console.log('✅ New Service Worker installed and ready');

                // Show update notification
                const updateNotification = document.createElement('div');
                updateNotification.innerHTML = `
                  <div style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #1976d2;
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10000;
                    max-width: 300px;
                  ">
                    <h4 style="margin: 0 0 0.5rem;">Update Available</h4>
                    <p style="margin: 0 0 1rem; font-size: 0.9rem;">A new version of the app is available.</p>
                    <button onclick="window.location.reload()" style="
                      background: white;
                      color: #1976d2;
                      border: none;
                      padding: 0.5rem 1rem;
                      border-radius: 4px;
                      cursor: pointer;
                      margin-right: 0.5rem;
                    ">Update Now</button>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                      background: transparent;
                      color: white;
                      border: 1px solid white;
                      padding: 0.5rem 1rem;
                      border-radius: 4px;
                      cursor: pointer;
                    ">Later</button>
                  </div>
                `;
                document.body.appendChild(updateNotification);
              }
            });
          }
        });

        // Handle service worker messages
        navigator.serviceWorker.addEventListener('message', (event) => {
          console.log('📨 Message from Service Worker:', event.data);
        });

        // Check if there's a waiting service worker
        if (registration.waiting) {
          console.log('⏳ Service Worker is waiting to activate');
        }

        // Check if service worker is controlling the page
        if (navigator.serviceWorker.controller) {
          console.log('🎮 Service Worker is controlling this page');
        } else {
          console.log('🔄 Service Worker will control this page after next reload');
        }
      })
      .catch((error) => {
        console.error('❌ Service Worker registration failed:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });

        // Show error notification in development
        if (process.env.NODE_ENV === 'development') {
          const errorNotification = document.createElement('div');
          errorNotification.innerHTML = `
            <div style="
              position: fixed;
              bottom: 20px;
              right: 20px;
              background: #d32f2f;
              color: white;
              padding: 1rem;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              z-index: 10000;
              max-width: 300px;
            ">
              <h4 style="margin: 0 0 0.5rem;">Service Worker Error</h4>
              <p style="margin: 0; font-size: 0.9rem;">Failed to register service worker: ${error.message}</p>
              <button onclick="this.parentElement.parentElement.remove()" style="
                background: white;
                color: #d32f2f;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 0.5rem;
              ">Dismiss</button>
            </div>
          `;
          document.body.appendChild(errorNotification);

          // Auto-remove after 10 seconds
          setTimeout(() => {
            if (errorNotification.parentElement) {
              errorNotification.parentElement.removeChild(errorNotification);
            }
          }, 10000);
        }
      });
  });
} else {
  console.warn('⚠️ Service Worker API is not available in this browser');
}

// Initialize performance optimizer
initPerformanceOptimizer({
  lazyLoadImages: true,
  resourceHints: true
});
console.log('Performance optimizer initialized');

// Initialize connection manager
connectionManager.init();
console.log('Connection manager initialized');

// Initialize API client
apiClient.initServices().catch(error => {
  console.warn('API client initialization error:', error);
});

// Note: WebSocket connections are managed by ConnectionManager
// Individual WebSocketClient instances are created as needed

// Initialize all services
console.log('Initializing all services...');
initializeAllServices()
  .then(() => {
    console.log('All services initialized successfully');

    // Show success message
    const message = document.createElement('div');
    message.textContent = 'Services initialized successfully!';
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.right = '20px';
    message.style.padding = '10px 20px';
    message.style.backgroundColor = 'var(--color-success, #52c41a)';
    message.style.color = 'white';
    message.style.borderRadius = '4px';
    message.style.boxShadow = 'var(--shadow-md, 0 2px 8px rgba(0, 0, 0, 0.15))';
    message.style.zIndex = '1000';
    document.body.appendChild(message);

    // Remove message after 3 seconds
    setTimeout(() => {
      document.body.removeChild(message);
    }, 3000);
  })
  .catch(error => {
    console.error('Error initializing services:', error);

    // Show error message
    const message = document.createElement('div');
    message.textContent = 'Error initializing services. Check the console for details.';
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.right = '20px';
    message.style.padding = '10px 20px';
    message.style.backgroundColor = 'var(--color-error, #f5222d)';
    message.style.color = 'white';
    message.style.borderRadius = '4px';
    message.style.boxShadow = 'var(--shadow-md, 0 2px 8px rgba(0, 0, 0, 0.15))';
    message.style.zIndex = '1000';
    document.body.appendChild(message);

    // Remove message after 5 seconds
    setTimeout(() => {
      document.body.removeChild(message);
    }, 5000);
  });

// Import the enhanced error boundary
import EnhancedErrorBoundary from './components/common/EnhancedErrorBoundary';

// Import AppLayout
import AppLayout from './components/layout/AppLayout';

// Import Routes and Router
import Routes from './Routes';
import { BrowserRouter as Router } from 'react-router-dom';

// Import required providers
import { AuthProvider } from './contexts/AuthContext';
import { AnalyticsProvider } from './components/analytics';

// Add debugging for React mounting
console.log('🔍 Starting React app initialization...');

// Check if root element exists
const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('❌ Root element not found! Cannot mount React app.');
  throw new Error('Root element not found');
}

console.log('✅ Root element found:', rootElement);

// Update loading indicator to show React is initializing
rootElement.innerHTML = `
  <div style="
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #f8fafc;
  ">
    <div style="
      background: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    ">
      <div style="
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      "></div>
      <h2 style="margin: 0 0 0.5rem; color: #1f2937;">Initializing React App</h2>
      <p style="margin: 0; color: #6b7280;">Loading components and services...</p>
    </div>
  </div>
`;

// Use createRoot API for React 18
console.log('🏗️ Creating React root...');
const root = createRoot(rootElement);

console.log('🚀 Rendering React app...');

// Create a simplified fallback app in case of provider issues
const SimpleFallbackApp = () => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      textAlign: 'center',
      padding: '2rem'
    }}>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        padding: '2rem',
        borderRadius: '12px',
        backdropFilter: 'blur(10px)',
        maxWidth: '500px'
      }}>
        <h1 style={{ margin: '0 0 1rem', fontSize: '2rem' }}>App Builder 201</h1>
        <p style={{ margin: '0 0 1.5rem', fontSize: '1.1rem', opacity: 0.9 }}>
          Welcome to App Builder 201! The application is starting up...
        </p>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid rgba(255,255,255,0.3)',
          borderTop: '4px solid white',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          margin: '0 auto'
        }}></div>
        <p style={{ margin: '1rem 0 0', fontSize: '0.9rem', opacity: 0.7 }}>
          If this takes too long, please refresh the page.
        </p>
      </div>
    </div>
  );
};

try {
  console.log('🔍 Starting React app render...');
  console.log('📦 Available components:', {
    React: !!React,
    Provider: !!Provider,
    AuthProvider: !!AuthProvider,
    EnhancedErrorBoundary: !!EnhancedErrorBoundary,
    ThemeProvider: !!ThemeProvider,
    AppLayout: !!AppLayout,
    App: !!App,
    store: !!store,
    root: !!root
  });

  // Restore the proper App structure but bypass initialization issues
  root.render(
    <React.StrictMode>
      <Provider store={store}>
        <AuthProvider>
          <EnhancedErrorBoundary>
            <ThemeProvider initialTheme="system">
              <Router>
                <Routes />
              </Router>
            </ThemeProvider>
          </EnhancedErrorBoundary>
        </AuthProvider>
      </Provider>
    </React.StrictMode>
  );
  console.log('✅ React app rendered successfully!');
} catch (error) {
  console.error('❌ Error rendering React app:', error);

  // Try to render a simplified fallback
  try {
    console.log('🔄 Attempting to render fallback app...');
    root.render(<SimpleFallbackApp />);
    console.log('✅ Fallback app rendered successfully!');

    // Show error notification after a delay
    setTimeout(() => {
      const errorNotification = document.createElement('div');
      errorNotification.innerHTML = `
        <div style="
          position: fixed;
          top: 20px;
          right: 20px;
          background: #dc2626;
          color: white;
          padding: 1rem;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          z-index: 10000;
          max-width: 300px;
        ">
          <h4 style="margin: 0 0 0.5rem;">App Loading Issue</h4>
          <p style="margin: 0 0 1rem; font-size: 0.9rem;">
            The main app failed to load. Running in fallback mode.
          </p>
          <button onclick="window.location.reload()" style="
            background: white;
            color: #dc2626;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 0.5rem;
          ">Reload</button>
          <button onclick="this.parentElement.parentElement.remove()" style="
            background: transparent;
            color: white;
            border: 1px solid white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
          ">Dismiss</button>
        </div>
      `;
      document.body.appendChild(errorNotification);
    }, 3000);

  } catch (fallbackError) {
    console.error('❌ Fallback app also failed:', fallbackError);

    // Last resort: show static HTML error message
    rootElement.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #fef2f2;
        color: #dc2626;
      ">
        <div style="
          background: white;
          padding: 2rem;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          text-align: center;
          max-width: 500px;
          border: 2px solid #fecaca;
        ">
          <h2 style="margin: 0 0 1rem; color: #dc2626;">React App Failed to Load</h2>
          <p style="margin: 0 0 1rem; color: #7f1d1d;">
            There was a critical error initializing the React application. Please check the browser console for details.
          </p>
          <button onclick="window.location.reload()" style="
            background: #dc2626;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
          ">
            Reload Page
          </button>
        </div>
      </div>
    `;
  }

  throw error;
}
