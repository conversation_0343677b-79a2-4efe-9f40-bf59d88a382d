<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #cccccc; cursor: not-allowed; }
        input, select {
            padding: 8px;
            margin-bottom: 10px;
            width: 100%;
            box-sizing: border-box;
        }
        #log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            font-family: monospace;
            background-color: #f5f5f5;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .timestamp {
            color: #666;
            font-size: 0.8em;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .controls button {
            flex-grow: 1;
        }
        .header-info {
            display: grid;
            grid-template-columns: 150px 1fr;
            gap: 10px;
        }
        .header-info div {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .header-info div:nth-child(odd) {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>WebSocket Debug Tool</h1>
    
    <div class="card">
        <h2>Connection Settings</h2>
        <div>
            <label for="ws-url">WebSocket URL:</label>
            <input type="text" id="ws-url" value="ws://localhost:8000/ws/test/" />
        </div>
        <div>
            <label for="protocol">WebSocket Protocol (optional):</label>
            <input type="text" id="protocol" placeholder="e.g., graphql-ws" />
        </div>
        <div class="controls">
            <button id="connect-btn">Connect</button>
            <button id="disconnect-btn" disabled>Disconnect</button>
            <button id="clear-log-btn">Clear Log</button>
        </div>
        <div id="connection-status" class="warning">Not connected</div>
    </div>
    
    <div class="card">
        <h2>WebSocket Information</h2>
        <div class="header-info" id="ws-info">
            <div>Status:</div><div>Not connected</div>
            <div>URL:</div><div>-</div>
            <div>Protocol:</div><div>-</div>
            <div>Extensions:</div><div>-</div>
            <div>ReadyState:</div><div>-</div>
            <div>BufferedAmount:</div><div>-</div>
        </div>
    </div>
    
    <div class="card">
        <h2>Debug Log</h2>
        <div id="log"></div>
        <div class="controls">
            <button id="ping-btn" disabled>Send Ping</button>
            <button id="binary-btn" disabled>Send Binary</button>
            <button id="invalid-btn" disabled>Send Invalid Frame</button>
            <button id="large-btn" disabled>Send Large Message</button>
        </div>
    </div>
    
    <div class="card">
        <h2>Custom Message</h2>
        <div>
            <label for="message-type">Message Type:</label>
            <select id="message-type">
                <option value="text">Text</option>
                <option value="json">JSON</option>
                <option value="binary">Binary</option>
            </select>
        </div>
        <div>
            <label for="message-content">Message Content:</label>
            <textarea id="message-content" rows="4" style="width: 100%;" placeholder="Enter message content"></textarea>
        </div>
        <button id="send-custom-btn" disabled>Send Custom Message</button>
    </div>
    
    <script>
        // DOM Elements
        const wsUrlInput = document.getElementById('ws-url');
        const protocolInput = document.getElementById('protocol');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const clearLogBtn = document.getElementById('clear-log-btn');
        const connectionStatus = document.getElementById('connection-status');
        const wsInfo = document.getElementById('ws-info');
        const logDiv = document.getElementById('log');
        const pingBtn = document.getElementById('ping-btn');
        const binaryBtn = document.getElementById('binary-btn');
        const invalidBtn = document.getElementById('invalid-btn');
        const largeBtn = document.getElementById('large-btn');
        const messageTypeSelect = document.getElementById('message-type');
        const messageContentTextarea = document.getElementById('message-content');
        const sendCustomBtn = document.getElementById('send-custom-btn');
        
        // WebSocket instance
        let socket = null;
        
        // Log a message to the debug log
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toISOString();
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Update WebSocket info display
        function updateWsInfo() {
            if (!socket) {
                wsInfo.innerHTML = `
                    <div>Status:</div><div>Not connected</div>
                    <div>URL:</div><div>-</div>
                    <div>Protocol:</div><div>-</div>
                    <div>Extensions:</div><div>-</div>
                    <div>ReadyState:</div><div>-</div>
                    <div>BufferedAmount:</div><div>-</div>
                `;
                return;
            }
            
            const readyStateMap = {
                0: 'CONNECTING',
                1: 'OPEN',
                2: 'CLOSING',
                3: 'CLOSED'
            };
            
            wsInfo.innerHTML = `
                <div>Status:</div><div>${socket.readyState === 1 ? '<span class="success">Connected</span>' : '<span class="error">Not connected</span>'}</div>
                <div>URL:</div><div>${socket.url}</div>
                <div>Protocol:</div><div>${socket.protocol || 'none'}</div>
                <div>Extensions:</div><div>${socket.extensions || 'none'}</div>
                <div>ReadyState:</div><div>${readyStateMap[socket.readyState]} (${socket.readyState})</div>
                <div>BufferedAmount:</div><div>${socket.bufferedAmount} bytes</div>
            `;
        }
        
        // Connect to WebSocket
        function connect() {
            try {
                const url = wsUrlInput.value.trim();
                if (!url) {
                    log('Please enter a WebSocket URL', 'error');
                    return;
                }
                
                log(`Connecting to ${url}...`);
                
                // Get protocol if specified
                const protocol = protocolInput.value.trim() || undefined;
                
                // Create WebSocket connection
                socket = protocol ? new WebSocket(url, protocol) : new WebSocket(url);
                
                // Set binary type to arraybuffer for better compatibility
                socket.binaryType = 'arraybuffer';
                
                // Connection opened
                socket.addEventListener('open', (event) => {
                    log('Connection established', 'success');
                    connectionStatus.textContent = 'Connected';
                    connectionStatus.className = 'success';
                    
                    // Update button states
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    pingBtn.disabled = false;
                    binaryBtn.disabled = false;
                    invalidBtn.disabled = false;
                    largeBtn.disabled = false;
                    sendCustomBtn.disabled = false;
                    
                    updateWsInfo();
                });
                
                // Listen for messages
                socket.addEventListener('message', (event) => {
                    try {
                        if (event.data instanceof ArrayBuffer) {
                            // Binary data
                            const bytes = new Uint8Array(event.data);
                            let hexString = '';
                            for (let i = 0; i < bytes.length; i++) {
                                hexString += bytes[i].toString(16).padStart(2, '0') + ' ';
                                if ((i + 1) % 16 === 0) hexString += '\n';
                            }
                            log(`Received binary data (${bytes.length} bytes):<pre>${hexString}</pre>`, 'info');
                        } else {
                            // Try to parse as JSON
                            try {
                                const data = JSON.parse(event.data);
                                log(`Received JSON:<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                            } catch (e) {
                                // Not JSON, display as text
                                log(`Received text: ${event.data}`, 'info');
                            }
                        }
                    } catch (e) {
                        log(`Error processing message: ${e.message}`, 'error');
                    }
                    
                    updateWsInfo();
                });
                
                // Connection closed
                socket.addEventListener('close', (event) => {
                    const reason = event.reason ? ` (${event.reason})` : '';
                    log(`Connection closed with code ${event.code}${reason}`, 'warning');
                    connectionStatus.textContent = 'Disconnected';
                    connectionStatus.className = 'error';
                    
                    // Update button states
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    binaryBtn.disabled = true;
                    invalidBtn.disabled = true;
                    largeBtn.disabled = true;
                    sendCustomBtn.disabled = true;
                    
                    socket = null;
                    updateWsInfo();
                });
                
                // Connection error
                socket.addEventListener('error', (event) => {
                    log('WebSocket error occurred', 'error');
                    console.error('WebSocket error:', event);
                    updateWsInfo();
                });
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }
        
        // Disconnect from WebSocket
        function disconnect() {
            if (socket) {
                socket.close(1000, 'User initiated disconnect');
            }
        }
        
        // Send a ping message
        function sendPing() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const pingData = JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
                socket.send(pingData);
                log(`Sent ping: ${pingData}`, 'success');
                updateWsInfo();
            } else {
                log('Cannot send ping: WebSocket is not connected', 'error');
            }
        }
        
        // Send a binary message
        function sendBinary() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const buffer = new ArrayBuffer(8);
                const view = new DataView(buffer);
                view.setInt32(0, 1234567890, false); // Big-endian
                view.setInt32(4, 987654321, false);
                socket.send(buffer);
                log('Sent binary data: 8 bytes (1234567890, 987654321)', 'success');
                updateWsInfo();
            } else {
                log('Cannot send binary: WebSocket is not connected', 'error');
            }
        }
        
        // Send an invalid frame (for testing error handling)
        function sendInvalidFrame() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                try {
                    // This is a hack to try to send an invalid frame
                    // It may not work in all browsers due to security restrictions
                    const hackSocket = socket;
                    const originalSend = hackSocket.send;
                    hackSocket.send = function(data) {
                        log('Attempting to send invalid frame (this may not work in all browsers)', 'warning');
                        // Try to access the internal WebSocket to send raw data
                        // This will likely fail in modern browsers
                        try {
                            const invalidFrame = new Uint8Array([0x88, 0x02, 0x03, 0xe8]); // Invalid close frame
                            originalSend.call(this, invalidFrame.buffer);
                        } catch (e) {
                            log(`Failed to send invalid frame: ${e.message}`, 'error');
                        }
                        hackSocket.send = originalSend;
                    };
                    hackSocket.send('test');
                } catch (e) {
                    log(`Error sending invalid frame: ${e.message}`, 'error');
                }
                updateWsInfo();
            } else {
                log('Cannot send invalid frame: WebSocket is not connected', 'error');
            }
        }
        
        // Send a large message
        function sendLargeMessage() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                // Create a large JSON message (about 100KB)
                const largeArray = [];
                for (let i = 0; i < 1000; i++) {
                    largeArray.push({
                        index: i,
                        data: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '.repeat(10),
                        timestamp: new Date().toISOString()
                    });
                }
                const largeMessage = JSON.stringify({
                    type: 'large_message',
                    data: largeArray
                });
                
                log(`Sending large message (${Math.round(largeMessage.length / 1024)} KB)...`, 'info');
                socket.send(largeMessage);
                log('Large message sent', 'success');
                updateWsInfo();
            } else {
                log('Cannot send large message: WebSocket is not connected', 'error');
            }
        }
        
        // Send a custom message
        function sendCustomMessage() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const type = messageTypeSelect.value;
                const content = messageContentTextarea.value;
                
                if (!content) {
                    log('Please enter message content', 'error');
                    return;
                }
                
                try {
                    if (type === 'text') {
                        socket.send(content);
                        log(`Sent text message: ${content}`, 'success');
                    } else if (type === 'json') {
                        // Validate JSON
                        const jsonObj = JSON.parse(content);
                        socket.send(JSON.stringify(jsonObj));
                        log(`Sent JSON message: ${JSON.stringify(jsonObj)}`, 'success');
                    } else if (type === 'binary') {
                        // Convert hex string to binary
                        const hexString = content.replace(/\s+/g, '');
                        if (!/^[0-9A-Fa-f]+$/.test(hexString)) {
                            throw new Error('Invalid hex string');
                        }
                        const bytes = new Uint8Array(hexString.length / 2);
                        for (let i = 0; i < hexString.length; i += 2) {
                            bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
                        }
                        socket.send(bytes.buffer);
                        log(`Sent binary message: ${bytes.length} bytes`, 'success');
                    }
                    updateWsInfo();
                } catch (e) {
                    log(`Error sending message: ${e.message}`, 'error');
                }
            } else {
                log('Cannot send message: WebSocket is not connected', 'error');
            }
        }
        
        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearLogBtn.addEventListener('click', () => {
            logDiv.innerHTML = '';
            log('Log cleared', 'info');
        });
        pingBtn.addEventListener('click', sendPing);
        binaryBtn.addEventListener('click', sendBinary);
        invalidBtn.addEventListener('click', sendInvalidFrame);
        largeBtn.addEventListener('click', sendLargeMessage);
        sendCustomBtn.addEventListener('click', sendCustomMessage);
        
        // Initial log
        log('WebSocket Debug Tool loaded. Click "Connect" to start.', 'info');
        updateWsInfo();
    </script>
</body>
</html>
