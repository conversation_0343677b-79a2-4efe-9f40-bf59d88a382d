# Comprehensive Testing Guide

This document provides a complete guide to the testing infrastructure for the App Builder application, covering all types of tests, tools, and best practices.

## Table of Contents

1. [Overview](#overview)
2. [Test Types](#test-types)
3. [Frontend Testing](#frontend-testing)
4. [Backend Testing](#backend-testing)
5. [End-to-End Testing](#end-to-end-testing)
6. [Performance Testing](#performance-testing)
7. [Security Testing](#security-testing)
8. [Accessibility Testing](#accessibility-testing)
9. [Cross-Browser Testing](#cross-browser-testing)
10. [CI/CD Integration](#cicd-integration)
11. [Coverage Reports](#coverage-reports)
12. [Best Practices](#best-practices)

## Overview

The App Builder application uses a comprehensive testing strategy that includes:

- **Unit Tests**: Testing individual components and functions
- **Integration Tests**: Testing component interactions and API integrations
- **End-to-End Tests**: Testing complete user workflows
- **Performance Tests**: Bundle size analysis and Lighthouse audits
- **Security Tests**: XSS, CSRF, and vulnerability testing
- **Accessibility Tests**: WCAG 2.1 AA compliance testing
- **Cross-Browser Tests**: Compatibility across different browsers

## Test Types

### Frontend Tests

#### Unit Tests
- **Location**: `frontend/src/tests/unit/`
- **Framework**: Jest + React Testing Library
- **Coverage**: Components, hooks, utilities, services
- **Command**: `npm run test:unit`

#### Integration Tests
- **Location**: `frontend/src/tests/integration/`
- **Framework**: Jest + MSW (Mock Service Worker)
- **Coverage**: API integration, Redux state management, WebSocket connections
- **Command**: `npm run test:integration`

#### Component Tests
- **Location**: `frontend/src/tests/components/`
- **Framework**: Jest + React Testing Library
- **Coverage**: Component rendering, user interactions, props validation
- **Command**: `npm run test:components`

### Backend Tests

#### Unit Tests
- **Location**: `backend/tests/test_models.py`, `backend/tests/test_views.py`
- **Framework**: Django TestCase + pytest
- **Coverage**: Models, views, serializers, utilities
- **Command**: `python manage.py test tests.test_models tests.test_views`

#### Integration Tests
- **Location**: `backend/tests/test_integration_*.py`
- **Framework**: Django TransactionTestCase + pytest
- **Coverage**: API endpoints, database operations, WebSocket functionality
- **Command**: `python manage.py test tests.test_integration_api`

#### Security Tests
- **Location**: `backend/tests/test_security.py`
- **Framework**: Django TestCase + pytest
- **Coverage**: Authentication, authorization, input validation, security headers
- **Command**: `python manage.py test tests.test_security`

### End-to-End Tests

#### Playwright Tests
- **Location**: `frontend/src/tests/e2e/`
- **Framework**: Playwright
- **Coverage**: Complete user workflows, cross-browser compatibility
- **Command**: `npx playwright test`

#### Accessibility Tests
- **Location**: `frontend/src/tests/e2e/accessibility.spec.js`
- **Framework**: Playwright + axe-core
- **Coverage**: WCAG 2.1 AA compliance, keyboard navigation, screen reader support
- **Command**: `npx playwright test --project=accessibility`

## Frontend Testing

### Running Tests

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# All tests
npm run test:all

# With coverage
npm run test:coverage
```

### Test Structure

```javascript
// Example unit test
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../store';
import MyComponent from './MyComponent';

describe('MyComponent', () => {
  test('renders correctly', () => {
    render(
      <Provider store={store}>
        <MyComponent />
      </Provider>
    );
    
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Mock Service Worker Setup

```javascript
// src/tests/mocks/handlers.js
import { rest } from 'msw';

export const handlers = [
  rest.get('/api/apps', (req, res, ctx) => {
    return res(
      ctx.json({
        results: [
          { id: 1, name: 'Test App', description: 'Test Description' }
        ]
      })
    );
  }),
];
```

## Backend Testing

### Running Tests

```bash
# Unit tests
python manage.py test tests.test_models tests.test_views

# Integration tests
python manage.py test tests.test_integration_api

# Security tests
python manage.py test tests.test_security

# All tests with coverage
coverage run --source='.' manage.py test
coverage report
coverage html
```

### Test Structure

```python
# Example Django test
from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status

class AppAPITestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_create_app(self):
        data = {
            'name': 'Test App',
            'description': 'Test Description',
            'app_data': '{}'
        }
        response = self.client.post('/api/apps/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
```

## End-to-End Testing

### Playwright Configuration

The E2E tests use Playwright with multiple browser configurations:

- **Chromium**: Desktop Chrome
- **Firefox**: Desktop Firefox  
- **WebKit**: Desktop Safari
- **Mobile Chrome**: Pixel 5
- **Mobile Safari**: iPhone 12

### Running E2E Tests

```bash
# All browsers
npx playwright test

# Specific browser
npx playwright test --project=chromium

# Headed mode (visible browser)
npx playwright test --headed

# Debug mode
npx playwright test --debug

# Accessibility tests
npx playwright test --project=accessibility
```

### Test Structure

```javascript
// Example E2E test
import { test, expect } from '@playwright/test';

test.describe('App Builder Workflow', () => {
  test('user can create and save an app', async ({ page }) => {
    await page.goto('/');
    
    // Create new app
    await page.click('[data-testid="new-app-button"]');
    await page.fill('[data-testid="app-name-input"]', 'My Test App');
    
    // Add components
    await page.dragAndDrop('[data-testid="button-component"]', '[data-testid="canvas"]');
    
    // Save app
    await page.click('[data-testid="save-button"]');
    
    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});
```

## Performance Testing

### Bundle Size Analysis

```bash
# Run bundle size tests
npm run test:bundle-size

# Analyze bundle composition
npm run analyze

# Check bundle size limits
npm run check-size
```

### Lighthouse Audits

```bash
# Run Lighthouse performance tests
npm run test:performance:lighthouse

# Run all performance tests
npm run test:performance
```

### Performance Thresholds

- **Main Bundle**: < 244KB (as per user preference)
- **Vendor Bundle**: < 500KB
- **Total Bundle**: < 800KB
- **Lighthouse Performance**: > 90
- **First Contentful Paint**: < 1.8s
- **Largest Contentful Paint**: < 2.5s

## Security Testing

### Frontend Security Tests

```bash
# Run security tests
npm run test:security

# Run security tests in CI
npm run test:security:ci
```

### Backend Security Tests

```bash
# Run security tests
python manage.py test tests.test_security

# Run with security tools
bandit -r . -f json -o bandit-report.json
```

### Security Test Coverage

- **XSS Protection**: Input sanitization, output encoding
- **CSRF Protection**: Token validation, SameSite cookies
- **Authentication**: Password requirements, session security
- **Authorization**: Access control, privilege escalation
- **Input Validation**: SQL injection, command injection
- **Security Headers**: CSP, HSTS, X-Frame-Options

## Accessibility Testing

### Running Accessibility Tests

```bash
# Playwright accessibility tests
npx playwright test --project=accessibility

# Jest accessibility tests
npm run test:accessibility

# Axe-core tests
npm run test:accessibility:axe
```

### WCAG 2.1 AA Compliance

The accessibility tests ensure compliance with:

- **Perceivable**: Color contrast, alt text, captions
- **Operable**: Keyboard navigation, focus management
- **Understandable**: Clear language, consistent navigation
- **Robust**: Valid markup, assistive technology support

## Cross-Browser Testing

### Supported Browsers

- **Desktop**: Chrome, Firefox, Safari, Edge
- **Mobile**: Chrome Mobile, Safari Mobile
- **Tablets**: iPad Pro

### Running Cross-Browser Tests

```bash
# All browsers
npx playwright test

# Specific browsers
npx playwright test --project=chromium,firefox,webkit

# Mobile browsers
npx playwright test --project="Mobile Chrome","Mobile Safari"
```

## CI/CD Integration

### GitHub Actions Workflow

The comprehensive testing pipeline includes:

1. **Code Quality**: ESLint, Prettier, security scanning
2. **Frontend Tests**: Unit, integration, E2E tests
3. **Backend Tests**: Unit, integration, security tests
4. **Cross-Browser Tests**: Multiple browser configurations
5. **Performance Tests**: Bundle size, Lighthouse audits
6. **Accessibility Tests**: WCAG compliance
7. **Coverage Reports**: Combined frontend/backend coverage

### Quality Gates

Tests must pass these quality gates:

- **Code Quality**: No ESLint errors, proper formatting
- **Unit Tests**: > 80% coverage, all tests passing
- **Integration Tests**: All API integrations working
- **Security Tests**: No critical vulnerabilities
- **Performance**: Bundle size within limits
- **Accessibility**: WCAG 2.1 AA compliance

## Coverage Reports

### Generating Coverage Reports

```bash
# Frontend coverage
npm run test:coverage

# Backend coverage
coverage run --source='.' manage.py test
coverage html

# Combined coverage report
npm run test:coverage:report
```

### Coverage Thresholds

- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

### Coverage Report Features

- **HTML Reports**: Interactive coverage visualization
- **JSON Reports**: Machine-readable coverage data
- **Badges**: SVG badges for README
- **Quality Gates**: Automated pass/fail based on thresholds

## Best Practices

### Test Organization

1. **Follow AAA Pattern**: Arrange, Act, Assert
2. **Use Descriptive Names**: Test names should explain what is being tested
3. **Keep Tests Independent**: Each test should be able to run in isolation
4. **Mock External Dependencies**: Use mocks for API calls, external services
5. **Test User Behavior**: Focus on what users do, not implementation details

### Test Data Management

1. **Use Factories**: Create test data with libraries like model-bakery
2. **Clean Up**: Ensure tests clean up after themselves
3. **Realistic Data**: Use data that resembles production data
4. **Edge Cases**: Test boundary conditions and error scenarios

### Performance Considerations

1. **Parallel Execution**: Run tests in parallel when possible
2. **Selective Testing**: Use test tags to run specific test suites
3. **Fast Feedback**: Prioritize fast-running tests in development
4. **Resource Management**: Clean up resources to prevent memory leaks

### Maintenance

1. **Regular Updates**: Keep testing frameworks and tools updated
2. **Review Test Results**: Regularly review and analyze test failures
3. **Refactor Tests**: Keep test code clean and maintainable
4. **Documentation**: Document complex test scenarios and setup

## Troubleshooting

### Common Issues

1. **Flaky Tests**: Use proper waits, avoid hardcoded delays
2. **Slow Tests**: Optimize test data setup, use mocks appropriately
3. **Browser Issues**: Update browser drivers, check compatibility
4. **Coverage Issues**: Ensure all code paths are tested

### Debug Commands

```bash
# Debug E2E tests
npx playwright test --debug

# Run specific test file
npx playwright test tests/specific-test.spec.js

# Run with verbose output
npm run test:unit -- --verbose

# Generate test report
npx playwright show-report
```

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/)
- [Django Testing](https://docs.djangoproject.com/en/stable/topics/testing/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [axe-core Documentation](https://github.com/dequelabs/axe-core)
