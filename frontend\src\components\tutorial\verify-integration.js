/**
 * Tutorial System Integration Verification
 * 
 * This script verifies that the tutorial system is properly integrated
 * with the App Builder application and all features are working correctly.
 */

// Verification functions
const verifyTutorialSystem = () => {
  const results = {
    passed: 0,
    failed: 0,
    warnings: 0,
    details: []
  };

  const addResult = (test, status, message, details = null) => {
    results[status]++;
    results.details.push({
      test,
      status,
      message,
      details,
      timestamp: new Date().toISOString()
    });
  };

  console.log('🔍 Verifying Tutorial System Integration...\n');

  // 1. Check if tutorial components are available
  try {
    const tutorialModules = [
      'TutorialProvider',
      'TutorialOverlay',
      'ContextualHelp',
      'TutorialTrigger',
      'TutorialProgress',
      'TutorialLauncher',
      'TutorialDashboard',
      'TutorialBadges',
      'withTutorialSupport'
    ];

    tutorialModules.forEach(moduleName => {
      try {
        const module = require('./index.js')[moduleName];
        if (module) {
          addResult(
            `Module Import: ${moduleName}`,
            'passed',
            `✅ ${moduleName} imported successfully`
          );
        } else {
          addResult(
            `Module Import: ${moduleName}`,
            'failed',
            `❌ ${moduleName} not found in exports`
          );
        }
      } catch (error) {
        addResult(
          `Module Import: ${moduleName}`,
          'failed',
          `❌ Failed to import ${moduleName}: ${error.message}`
        );
      }
    });
  } catch (error) {
    addResult(
      'Module Imports',
      'failed',
      `❌ Failed to load tutorial modules: ${error.message}`
    );
  }

  // 2. Check tutorial content definitions
  try {
    const { TUTORIAL_DEFINITIONS } = require('./TutorialContent.js');
    const tutorialCount = Object.keys(TUTORIAL_DEFINITIONS).length;
    
    if (tutorialCount > 0) {
      addResult(
        'Tutorial Content',
        'passed',
        `✅ ${tutorialCount} tutorials defined`,
        { tutorialIds: Object.keys(TUTORIAL_DEFINITIONS) }
      );
    } else {
      addResult(
        'Tutorial Content',
        'failed',
        '❌ No tutorials defined'
      );
    }

    // Verify tutorial structure
    Object.entries(TUTORIAL_DEFINITIONS).forEach(([id, tutorial]) => {
      const issues = [];
      
      if (!tutorial.title) issues.push('Missing title');
      if (!tutorial.description) issues.push('Missing description');
      if (!tutorial.steps || tutorial.steps.length === 0) issues.push('No steps defined');
      if (!tutorial.category) issues.push('Missing category');
      
      if (issues.length === 0) {
        addResult(
          `Tutorial Structure: ${id}`,
          'passed',
          `✅ Tutorial ${id} structure is valid`
        );
      } else {
        addResult(
          `Tutorial Structure: ${id}`,
          'failed',
          `❌ Tutorial ${id} has issues: ${issues.join(', ')}`
        );
      }
    });
  } catch (error) {
    addResult(
      'Tutorial Content',
      'failed',
      `❌ Failed to load tutorial content: ${error.message}`
    );
  }

  // 3. Check storage functionality
  try {
    const tutorialStorage = require('./TutorialStorage.js').default;
    
    // Test basic storage operations
    const testProgress = {
      tutorialId: 'test_tutorial',
      userId: 'test_user',
      status: 'completed',
      completedSteps: [0, 1, 2]
    };

    tutorialStorage.saveTutorialProgress(testProgress);
    const retrievedProgress = tutorialStorage.getTutorialProgress('test_tutorial', 'test_user');
    
    if (retrievedProgress && retrievedProgress.status === 'completed') {
      addResult(
        'Storage Functionality',
        'passed',
        '✅ Tutorial storage working correctly'
      );
    } else {
      addResult(
        'Storage Functionality',
        'failed',
        '❌ Tutorial storage not working correctly'
      );
    }

    // Test preferences
    const testPreferences = { autoStartTutorials: true, showContextualHelp: false };
    tutorialStorage.saveTutorialPreferences(testPreferences);
    const retrievedPreferences = tutorialStorage.getTutorialPreferences();
    
    if (retrievedPreferences.autoStartTutorials === true) {
      addResult(
        'Preferences Storage',
        'passed',
        '✅ Preferences storage working correctly'
      );
    } else {
      addResult(
        'Preferences Storage',
        'failed',
        '❌ Preferences storage not working correctly'
      );
    }
  } catch (error) {
    addResult(
      'Storage Functionality',
      'failed',
      `❌ Storage functionality error: ${error.message}`
    );
  }

  // 4. Check type definitions
  try {
    const types = require('./types.js');
    const requiredTypes = [
      'TUTORIAL_STEP_TYPES',
      'TUTORIAL_CATEGORIES',
      'TUTORIAL_STATUS',
      'HELP_CONTEXT_TYPES',
      'createTutorial',
      'createTutorialStep'
    ];

    requiredTypes.forEach(typeName => {
      if (types[typeName]) {
        addResult(
          `Type Definition: ${typeName}`,
          'passed',
          `✅ ${typeName} defined correctly`
        );
      } else {
        addResult(
          `Type Definition: ${typeName}`,
          'failed',
          `❌ ${typeName} not found`
        );
      }
    });
  } catch (error) {
    addResult(
      'Type Definitions',
      'failed',
      `❌ Type definitions error: ${error.message}`
    );
  }

  // 5. Check DOM integration requirements
  if (typeof window !== 'undefined') {
    // Check for required DOM features
    const domFeatures = [
      { name: 'localStorage', check: () => window.localStorage },
      { name: 'ResizeObserver', check: () => window.ResizeObserver },
      { name: 'MutationObserver', check: () => window.MutationObserver },
      { name: 'matchMedia', check: () => window.matchMedia }
    ];

    domFeatures.forEach(({ name, check }) => {
      try {
        if (check()) {
          addResult(
            `DOM Feature: ${name}`,
            'passed',
            `✅ ${name} available`
          );
        } else {
          addResult(
            `DOM Feature: ${name}`,
            'warnings',
            `⚠️ ${name} not available (may impact functionality)`
          );
        }
      } catch (error) {
        addResult(
          `DOM Feature: ${name}`,
          'failed',
          `❌ Error checking ${name}: ${error.message}`
        );
      }
    });

    // Check for tutorial-specific DOM elements
    const tutorialElements = document.querySelectorAll('[data-help-context]');
    if (tutorialElements.length > 0) {
      addResult(
        'DOM Integration',
        'passed',
        `✅ Found ${tutorialElements.length} elements with tutorial context`
      );
    } else {
      addResult(
        'DOM Integration',
        'warnings',
        '⚠️ No elements with tutorial context found (may be expected if not in App Builder)'
      );
    }
  }

  // 6. Check accessibility features
  try {
    const accessibility = require('./TutorialAccessibility.js');
    if (accessibility.announceToScreenReader && accessibility.manageFocus) {
      addResult(
        'Accessibility Features',
        'passed',
        '✅ Accessibility utilities available'
      );
    } else {
      addResult(
        'Accessibility Features',
        'failed',
        '❌ Accessibility utilities missing'
      );
    }
  } catch (error) {
    addResult(
      'Accessibility Features',
      'failed',
      `❌ Accessibility features error: ${error.message}`
    );
  }

  // 7. Performance check
  const performanceStart = performance.now();
  
  // Simulate loading tutorial system
  try {
    const tutorialSystem = require('./index.js');
    const loadTime = performance.now() - performanceStart;
    
    if (loadTime < 100) {
      addResult(
        'Performance',
        'passed',
        `✅ Tutorial system loads quickly (${loadTime.toFixed(2)}ms)`
      );
    } else if (loadTime < 500) {
      addResult(
        'Performance',
        'warnings',
        `⚠️ Tutorial system load time acceptable (${loadTime.toFixed(2)}ms)`
      );
    } else {
      addResult(
        'Performance',
        'failed',
        `❌ Tutorial system loads slowly (${loadTime.toFixed(2)}ms)`
      );
    }
  } catch (error) {
    addResult(
      'Performance',
      'failed',
      `❌ Performance test failed: ${error.message}`
    );
  }

  return results;
};

// Report generation
const generateReport = (results) => {
  console.log('\n📊 Tutorial System Integration Report');
  console.log('=====================================\n');
  
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️ Warnings: ${results.warnings}`);
  console.log(`📝 Total Tests: ${results.passed + results.failed + results.warnings}\n`);

  if (results.failed > 0) {
    console.log('❌ FAILED TESTS:');
    results.details
      .filter(detail => detail.status === 'failed')
      .forEach(detail => {
        console.log(`   ${detail.message}`);
        if (detail.details) {
          console.log(`      Details: ${JSON.stringify(detail.details, null, 2)}`);
        }
      });
    console.log('');
  }

  if (results.warnings > 0) {
    console.log('⚠️ WARNINGS:');
    results.details
      .filter(detail => detail.status === 'warnings')
      .forEach(detail => {
        console.log(`   ${detail.message}`);
      });
    console.log('');
  }

  const successRate = (results.passed / (results.passed + results.failed + results.warnings)) * 100;
  
  if (successRate === 100) {
    console.log('🎉 All tests passed! Tutorial system is ready for use.');
  } else if (successRate >= 80) {
    console.log('✅ Tutorial system is mostly ready. Address warnings if needed.');
  } else if (successRate >= 60) {
    console.log('⚠️ Tutorial system has some issues. Review failed tests.');
  } else {
    console.log('❌ Tutorial system has significant issues. Fix failed tests before use.');
  }

  return {
    success: results.failed === 0,
    successRate,
    summary: {
      passed: results.passed,
      failed: results.failed,
      warnings: results.warnings
    }
  };
};

// Main verification function
const runVerification = () => {
  console.log('🚀 Starting Tutorial System Integration Verification...\n');
  
  try {
    const results = verifyTutorialSystem();
    const report = generateReport(results);
    
    // Save detailed results for debugging
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem('tutorial_verification_results', JSON.stringify({
        timestamp: new Date().toISOString(),
        results,
        report
      }));
    }
    
    return report;
  } catch (error) {
    console.error('❌ Verification failed with error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    verifyTutorialSystem,
    generateReport,
    runVerification
  };
}

// Auto-run if called directly
if (typeof window !== 'undefined' && window.location) {
  // Browser environment - can be called manually
  window.verifyTutorialSystem = runVerification;
  console.log('💡 Tutorial verification available. Run verifyTutorialSystem() in console.');
} else if (require.main === module) {
  // Node environment - run immediately
  runVerification();
}
