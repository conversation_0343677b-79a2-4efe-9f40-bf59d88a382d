#!/bin/bash
# SSL Certificate Setup Script using Let's Encrypt

set -e

echo "Setting up SSL certificates..."

# Configuration
DOMAIN="your-domain.com"
EMAIL="<EMAIL>"

# Create directories
mkdir -p nginx/ssl
mkdir -p nginx/certbot-webroot

# Initial certificate request
docker-compose -f docker-compose.prod.yml run --rm certbot \
    certonly --webroot \
    --webroot-path=/var/www/certbot \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    -d $DOMAIN \
    -d www.$DOMAIN

# Set up automatic renewal
echo "0 12 * * * /usr/bin/docker-compose -f /path/to/docker-compose.prod.yml run --rm certbot renew --quiet" | crontab -

echo "SSL certificates configured successfully!"
