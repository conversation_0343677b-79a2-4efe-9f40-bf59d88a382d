import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Hook for managing undo/redo functionality
 * @param {*} initialState - Initial state value
 * @param {number} maxHistorySize - Maximum number of history entries to keep
 * @returns {Object} State and undo/redo functions
 */
export const useUndoRedo = (initialState, maxHistorySize = 50) => {
  const [history, setHistory] = useState([initialState]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const isUndoRedoAction = useRef(false);

  // Get current state
  const currentState = history[currentIndex];

  // Push new state to history
  const pushState = useCallback((newState) => {
    if (isUndoRedoAction.current) {
      isUndoRedoAction.current = false;
      return;
    }

    setHistory(prev => {
      const newHistory = prev.slice(0, currentIndex + 1);
      newHistory.push(newState);
      
      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        return newHistory;
      }
      
      return newHistory;
    });
    
    setCurrentIndex(prev => {
      const newIndex = Math.min(prev + 1, maxHistorySize - 1);
      return newIndex;
    });
  }, [currentIndex, maxHistorySize]);

  // Undo function
  const undo = useCallback(() => {
    if (currentIndex > 0) {
      isUndoRedoAction.current = true;
      setCurrentIndex(prev => prev - 1);
      return history[currentIndex - 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Redo function
  const redo = useCallback(() => {
    if (currentIndex < history.length - 1) {
      isUndoRedoAction.current = true;
      setCurrentIndex(prev => prev + 1);
      return history[currentIndex + 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Check if undo is available
  const canUndo = currentIndex > 0;

  // Check if redo is available
  const canRedo = currentIndex < history.length - 1;

  // Clear history
  const clearHistory = useCallback(() => {
    setHistory([currentState]);
    setCurrentIndex(0);
  }, [currentState]);

  // Get history info
  const getHistoryInfo = useCallback(() => ({
    totalStates: history.length,
    currentIndex,
    canUndo,
    canRedo
  }), [history.length, currentIndex, canUndo, canRedo]);

  return {
    state: currentState,
    pushState,
    undo,
    redo,
    canUndo,
    canRedo,
    clearHistory,
    getHistoryInfo
  };
};

/**
 * Hook for keyboard shortcuts
 * @param {Object} shortcuts - Object mapping key combinations to functions
 * @param {Array} dependencies - Dependencies for the effect
 */
export const useKeyboardShortcuts = (shortcuts, dependencies = []) => {
  useEffect(() => {
    const handleKeyDown = (event) => {
      const { ctrlKey, metaKey, shiftKey, altKey, key } = event;
      
      // Create key combination string
      const modifiers = [];
      if (ctrlKey || metaKey) modifiers.push('ctrl');
      if (shiftKey) modifiers.push('shift');
      if (altKey) modifiers.push('alt');
      
      const combination = [...modifiers, key.toLowerCase()].join('+');
      
      // Check if combination exists in shortcuts
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination](event);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, dependencies);
};

/**
 * Hook for managing contextual menus
 * @returns {Object} Context menu state and functions
 */
export const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    items: []
  });

  const showContextMenu = useCallback((event, items) => {
    event.preventDefault();
    
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      items: items || []
    });
  }, []);

  const hideContextMenu = useCallback(() => {
    setContextMenu(prev => ({
      ...prev,
      visible: false
    }));
  }, []);

  // Hide context menu when clicking outside
  useEffect(() => {
    const handleClick = () => {
      if (contextMenu.visible) {
        hideContextMenu();
      }
    };

    document.addEventListener('click', handleClick);
    
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [contextMenu.visible, hideContextMenu]);

  return {
    contextMenu,
    showContextMenu,
    hideContextMenu
  };
};

/**
 * Hook for managing loading states with debouncing
 * @param {number} delay - Delay before showing loading state
 * @returns {Object} Loading state and functions
 */
export const useLoadingState = (delay = 200) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const timeoutRef = useRef(null);

  const startLoading = useCallback((message = 'Loading...') => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setIsLoading(true);
      setLoadingMessage(message);
    }, delay);
  }, [delay]);

  const stopLoading = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsLoading(false);
    setLoadingMessage('');
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isLoading,
    loadingMessage,
    startLoading,
    stopLoading
  };
};

/**
 * Hook for managing component selection with multi-select support
 * @param {Array} items - Array of selectable items
 * @returns {Object} Selection state and functions
 */
export const useSelection = (items = []) => {
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [lastSelectedIndex, setLastSelectedIndex] = useState(-1);

  const selectItem = useCallback((item, multiSelect = false) => {
    const itemIndex = items.findIndex(i => i.id === item.id);
    
    setSelectedItems(prev => {
      const newSelection = new Set(multiSelect ? prev : []);
      
      if (newSelection.has(item.id)) {
        newSelection.delete(item.id);
      } else {
        newSelection.add(item.id);
      }
      
      return newSelection;
    });
    
    setLastSelectedIndex(itemIndex);
  }, [items]);

  const selectRange = useCallback((item) => {
    const itemIndex = items.findIndex(i => i.id === item.id);
    
    if (lastSelectedIndex !== -1) {
      const start = Math.min(lastSelectedIndex, itemIndex);
      const end = Math.max(lastSelectedIndex, itemIndex);
      
      setSelectedItems(prev => {
        const newSelection = new Set(prev);
        
        for (let i = start; i <= end; i++) {
          if (items[i]) {
            newSelection.add(items[i].id);
          }
        }
        
        return newSelection;
      });
    } else {
      selectItem(item);
    }
  }, [items, lastSelectedIndex, selectItem]);

  const selectAll = useCallback(() => {
    setSelectedItems(new Set(items.map(item => item.id)));
  }, [items]);

  const clearSelection = useCallback(() => {
    setSelectedItems(new Set());
    setLastSelectedIndex(-1);
  }, []);

  const isSelected = useCallback((itemId) => {
    return selectedItems.has(itemId);
  }, [selectedItems]);

  const getSelectedItems = useCallback(() => {
    return items.filter(item => selectedItems.has(item.id));
  }, [items, selectedItems]);

  return {
    selectedItems: Array.from(selectedItems),
    selectItem,
    selectRange,
    selectAll,
    clearSelection,
    isSelected,
    getSelectedItems,
    selectedCount: selectedItems.size
  };
};

/**
 * Hook for managing clipboard operations
 * @returns {Object} Clipboard functions
 */
export const useClipboard = () => {
  const [clipboardData, setClipboardData] = useState(null);

  const copy = useCallback((data) => {
    setClipboardData(data);
    
    // Also copy to system clipboard if possible
    if (navigator.clipboard && typeof data === 'string') {
      navigator.clipboard.writeText(data).catch(console.error);
    }
  }, []);

  const paste = useCallback(() => {
    return clipboardData;
  }, [clipboardData]);

  const clear = useCallback(() => {
    setClipboardData(null);
  }, []);

  const hasData = clipboardData !== null;

  return {
    copy,
    paste,
    clear,
    hasData,
    data: clipboardData
  };
};

export default useUndoRedo;
