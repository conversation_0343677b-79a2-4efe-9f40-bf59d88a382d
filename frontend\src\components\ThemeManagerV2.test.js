import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import ThemeManagerV2 from './ThemeManagerV2';
import { addTheme, updateTheme, removeTheme, setActiveTheme } from '../redux/actions';

// Mock redux store
const mockStore = configureStore([]);

// Mock the Modal.confirm from Ant Design
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    Modal: {
      ...antd.Modal,
      confirm: ({ onOk }) => {
        onOk();
      }
    }
  };
});

describe('ThemeManagerV2', () => {
  let store;
  
  beforeEach(() => {
    // Create a fresh store for each test
    store = mockStore({
      themes: [
        {
          id: 'theme1',
          name: 'Test Theme',
          primaryColor: '#FF0000',
          secondaryColor: '#00FF00',
          backgroundColor: '#FFFFFF',
          textColor: '#000000',
          fontFamily: 'Arial, sans-serif',
          createdAt: '2023-01-01T00:00:00.000Z'
        }
      ],
      activeTheme: 'default'
    });
    
    // Mock dispatch
    store.dispatch = jest.fn();
  });
  
  it('renders without crashing', () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    expect(screen.getByText('Theme Manager')).toBeInTheDocument();
  });
  
  it('adds default theme if none exists', () => {
    const emptyStore = mockStore({
      themes: [],
      activeTheme: null
    });
    emptyStore.dispatch = jest.fn();
    
    render(
      <Provider store={emptyStore}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    expect(emptyStore.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('ADD_THEME')
      })
    );
  });
  
  it('creates a new theme', async () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    // Fill out the form
    fireEvent.change(screen.getByPlaceholderText('Theme Name'), {
      target: { value: 'New Test Theme' }
    });
    
    // Submit the form
    fireEvent.click(screen.getByText('Create Theme'));
    
    // Check that the correct action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('ADD_THEME'),
        payload: expect.objectContaining({
          name: 'New Test Theme'
        })
      })
    );
  });
  
  it('validates theme name is required', async () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    // Submit without filling name
    fireEvent.click(screen.getByText('Create Theme'));
    
    // Check for validation error
    expect(screen.getByText('Theme name is required')).toBeInTheDocument();
    
    // Dispatch should not have been called
    expect(store.dispatch).not.toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('ADD_THEME')
      })
    );
  });
  
  it('edits an existing theme', async () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    // Switch to Theme Gallery tab
    fireEvent.click(screen.getByText('Theme Gallery'));
    
    // Find and click edit button (using the tooltip title)
    const editButtons = screen.getAllByRole('button', { name: 'edit' });
    fireEvent.click(editButtons[0]);
    
    // Change the theme name
    fireEvent.change(screen.getByPlaceholderText('Theme Name'), {
      target: { value: 'Updated Theme Name' }
    });
    
    // Submit the form
    fireEvent.click(screen.getByText('Update Theme'));
    
    // Check that the correct action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('UPDATE_THEME'),
        payload: expect.objectContaining({
          name: 'Updated Theme Name'
        })
      })
    );
  });
  
  it('deletes a theme', async () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    // Switch to Theme Gallery tab
    fireEvent.click(screen.getByText('Theme Gallery'));
    
    // Find and click delete button
    const deleteButtons = screen.getAllByRole('button', { name: 'delete' });
    fireEvent.click(deleteButtons[0]);
    
    // Check that the correct action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('REMOVE_THEME'),
        payload: expect.objectContaining({
          id: 'theme1'
        })
      })
    );
  });
  
  it('activates a theme', async () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    // Switch to Theme Gallery tab
    fireEvent.click(screen.getByText('Theme Gallery'));
    
    // Find and click activate button
    const activateButton = screen.getByText('Activate');
    fireEvent.click(activateButton);
    
    // Check that the correct action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('SET_ACTIVE_THEME'),
        payload: 'theme1'
      })
    );
  });
  
  it('duplicates a theme', async () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    // Switch to Theme Gallery tab
    fireEvent.click(screen.getByText('Theme Gallery'));
    
    // Find and click duplicate button
    const duplicateButtons = screen.getAllByRole('button', { name: 'copy' });
    fireEvent.click(duplicateButtons[0]);
    
    // Check that the correct action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining('ADD_THEME'),
        payload: expect.objectContaining({
          name: 'Test Theme (Copy)'
        })
      })
    );
  });
  
  it('applies a color palette', async () => {
    render(
      <Provider store={store}>
        <ThemeManagerV2 />
      </Provider>
    );
    
    // Find and click a palette button (first one is 'Blue')
    const paletteButtons = screen.getAllByRole('button');
    const blueButton = paletteButtons.find(button => 
      button.closest('[title="Blue"]')
    );
    
    if (blueButton) {
      fireEvent.click(blueButton);
      
      // Check that the primary color input has been updated
      const primaryColorInput = screen.getAllByDisplayValue('#2563EB')[0];
      expect(primaryColorInput).toBeInTheDocument();
    }
  });
});
