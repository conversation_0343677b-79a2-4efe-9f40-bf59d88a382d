<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #cccccc; cursor: not-allowed; }
        input {
            padding: 8px;
            margin-bottom: 10px;
            width: 100%;
            box-sizing: border-box;
        }
        #log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>WebSocket Client</h1>
    
    <div class="card">
        <h2>Connection</h2>
        <div>
            <label for="ws-url">WebSocket URL:</label>
            <input type="text" id="ws-url" value="ws://localhost:8000/ws/test/" />
        </div>
        <div>
            <button id="connect-btn">Connect</button>
            <button id="disconnect-btn" disabled>Disconnect</button>
        </div>
        <div id="status">Not connected</div>
    </div>
    
    <div class="card">
        <h2>Messages</h2>
        <div id="log"></div>
        <div>
            <input type="text" id="message" placeholder="Type a message..." disabled />
            <button id="send-btn" disabled>Send</button>
            <button id="ping-btn" disabled>Send Ping</button>
            <button id="clear-btn">Clear Log</button>
        </div>
    </div>
    
    <script>
        // DOM Elements
        const wsUrlInput = document.getElementById('ws-url');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        const messageInput = document.getElementById('message');
        const sendBtn = document.getElementById('send-btn');
        const pingBtn = document.getElementById('ping-btn');
        const clearBtn = document.getElementById('clear-btn');
        
        // WebSocket instance
        let socket = null;
        
        // Log a message to the log div
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = type;
            
            const timestamp = new Date().toISOString();
            entry.innerHTML = `<span>[${timestamp}]</span> ${message}`;
            
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Connect to WebSocket
        function connect() {
            try {
                const url = wsUrlInput.value.trim();
                if (!url) {
                    log('Please enter a WebSocket URL', 'error');
                    return;
                }
                
                log(`Connecting to ${url}...`);
                
                // Create WebSocket connection
                socket = new WebSocket(url);
                
                // Connection opened
                socket.onopen = function(event) {
                    log('Connection established', 'success');
                    statusDiv.textContent = 'Connected';
                    statusDiv.className = 'success';
                    
                    // Update button states
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    messageInput.disabled = false;
                    sendBtn.disabled = false;
                    pingBtn.disabled = false;
                };
                
                // Listen for messages
                socket.onmessage = function(event) {
                    try {
                        // Try to parse as JSON
                        const data = JSON.parse(event.data);
                        log(`Received: <pre>${JSON.stringify(data, null, 2)}</pre>`);
                    } catch (e) {
                        // Not JSON, display as text
                        log(`Received: ${event.data}`);
                    }
                };
                
                // Connection closed
                socket.onclose = function(event) {
                    const reason = event.reason ? ` (${event.reason})` : '';
                    log(`Connection closed with code ${event.code}${reason}`, 'warning');
                    statusDiv.textContent = 'Disconnected';
                    statusDiv.className = 'error';
                    
                    // Update button states
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    messageInput.disabled = true;
                    sendBtn.disabled = true;
                    pingBtn.disabled = true;
                    
                    socket = null;
                };
                
                // Connection error
                socket.onerror = function(event) {
                    log('WebSocket error occurred', 'error');
                    console.error('WebSocket error:', event);
                };
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }
        
        // Disconnect from WebSocket
        function disconnect() {
            if (socket) {
                socket.close(1000, 'User initiated disconnect');
            }
        }
        
        // Send a message
        function sendMessage() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const message = messageInput.value.trim();
                if (!message) return;
                
                try {
                    socket.send(message);
                    log(`Sent: ${message}`, 'success');
                    messageInput.value = '';
                } catch (error) {
                    log(`Error sending message: ${error.message}`, 'error');
                }
            } else {
                log('Cannot send message: WebSocket is not connected', 'error');
            }
        }
        
        // Send a ping message
        function sendPing() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const ping = JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
                
                try {
                    socket.send(ping);
                    log(`Sent ping: ${ping}`, 'success');
                } catch (error) {
                    log(`Error sending ping: ${error.message}`, 'error');
                }
            } else {
                log('Cannot send ping: WebSocket is not connected', 'error');
            }
        }
        
        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        sendBtn.addEventListener('click', sendMessage);
        pingBtn.addEventListener('click', sendPing);
        clearBtn.addEventListener('click', function() {
            logDiv.innerHTML = '';
            log('Log cleared');
        });
        
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initial log
        log('WebSocket Client loaded. Click "Connect" to start.');
    </script>
</body>
</html>
