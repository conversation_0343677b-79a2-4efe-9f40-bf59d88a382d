import React from 'react';
import styled from 'styled-components';
import SkipLink from '../accessibility/SkipLink';

const TestContainer = styled.div`
  padding: 20px;
  background-color: ${props => {
    if (props.theme?.colorPalette?.background) return props.theme.colorPalette.background;
    if (props.theme?.colors?.background?.default) return props.theme.colors.background.default;
    if (props.theme?.backgroundColor) return props.theme.backgroundColor;
    return '#FFFFFF';
  }};
  color: ${props => {
    if (props.theme?.colorPalette?.textPrimary) return props.theme.colorPalette.textPrimary;
    if (props.theme?.colors?.text?.primary) return props.theme.colors.text.primary;
    if (props.theme?.textColor) return props.theme.textColor;
    return '#111827';
  }};
`;

const TestButton = styled.button`
  background-color: ${props => {
    if (props.theme?.colorPalette?.primary) return props.theme.colorPalette.primary;
    if (props.theme?.colors?.primary?.main) return props.theme.colors.primary.main;
    if (props.theme?.primaryColor) return props.theme.primaryColor;
    return '#2563EB';
  }};
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 10px;
  
  &:hover {
    opacity: 0.8;
  }
`;

/**
 * ThemeTest component
 * A simple test component to verify theme functionality
 */
const ThemeTest = () => {
  return (
    <TestContainer>
      <h2>Theme Test Component</h2>
      <p>This component tests if the theme is working properly.</p>
      
      {/* Test SkipLink component */}
      <SkipLink targetId="test-content" />
      
      <TestButton onClick={() => alert('Theme test button clicked!')}>
        Test Button
      </TestButton>
      
      <div id="test-content">
        <p>This is the test content that the skip link should navigate to.</p>
        <p>If you can see this page without errors, the theme fixes are working!</p>
      </div>
    </TestContainer>
  );
};

export default ThemeTest;
