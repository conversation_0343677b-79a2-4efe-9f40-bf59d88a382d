import React from 'react';
import { Input, Select, Switch, Typography, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { PropertyTypes, detectPropertyType, validatePropertyValue } from './PropertyTypeDetector';
import {
  NumberInput,
  ColorInput,
  SpacingEditor,
  BorderEditor,
  ShadowEditor,
  FontSelector
} from './index';

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

/**
 * Renders the appropriate input component based on property type
 */
const PropertyRenderer = ({
  propertyName,
  value,
  onChange,
  componentType,
  schema,
  showValidation = true,
  ...props
}) => {
  // Get property schema (either provided or detected)
  const propertySchema = schema || detectPropertyType(propertyName, value, componentType);
  
  // Validate current value
  const validation = showValidation ? validatePropertyValue(value, propertySchema) : { valid: true };
  
  // Handle value change with validation
  const handleChange = (newValue) => {
    if (onChange) {
      onChange(newValue, propertyName, propertySchema);
    }
  };

  // Render validation error
  const renderValidationError = () => {
    if (!showValidation || validation.valid) return null;
    
    return (
      <Text type="danger" style={{ fontSize: '12px', display: 'block', marginTop: '4px' }}>
        {validation.error}
      </Text>
    );
  };

  // Render tooltip if description is provided
  const renderTooltip = () => {
    if (!propertySchema.description && !propertySchema.tooltip) return null;
    
    return (
      <Tooltip title={propertySchema.description || propertySchema.tooltip}>
        <InfoCircleOutlined style={{ color: '#8c8c8c', marginLeft: '4px' }} />
      </Tooltip>
    );
  };

  // Render based on property type
  switch (propertySchema.type) {
    case PropertyTypes.TEXT:
      if (propertySchema.multiline) {
        return (
          <div>
            <TextArea
              value={value}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={propertySchema.placeholder}
              rows={propertySchema.rows || 3}
              status={!validation.valid ? 'error' : ''}
              {...props}
            />
            {renderTooltip()}
            {renderValidationError()}
          </div>
        );
      }
      
      return (
        <div>
          <Input
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={propertySchema.placeholder}
            status={!validation.valid ? 'error' : ''}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.NUMBER:
      return (
        <div>
          <NumberInput
            value={value}
            onChange={handleChange}
            min={propertySchema.min}
            max={propertySchema.max}
            step={propertySchema.step}
            precision={propertySchema.precision}
            unit={propertySchema.unit}
            units={propertySchema.units}
            showSlider={propertySchema.showSlider}
            showUnit={propertySchema.showUnit}
            placeholder={propertySchema.placeholder}
            tooltip={propertySchema.tooltip}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.BOOLEAN:
      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Switch
            checked={value}
            onChange={handleChange}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.COLOR:
      return (
        <div>
          <ColorInput
            value={value}
            onChange={handleChange}
            showPresets={propertySchema.showPresets}
            showModeToggle={propertySchema.showModeToggle}
            presets={propertySchema.presets}
            placeholder={propertySchema.placeholder}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.SELECT:
      return (
        <div>
          <Select
            value={value}
            onChange={handleChange}
            placeholder={propertySchema.placeholder}
            style={{ width: '100%' }}
            status={!validation.valid ? 'error' : ''}
            {...props}
          >
            {propertySchema.options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.SPACING:
      return (
        <div>
          <SpacingEditor
            value={value}
            onChange={handleChange}
            type={propertyName.includes('margin') ? 'margin' : 'padding'}
            showVisual={propertySchema.showVisual}
            showPresets={propertySchema.showPresets}
            unit={propertySchema.unit}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.BORDER:
      return (
        <div>
          <BorderEditor
            value={value}
            onChange={handleChange}
            showPreview={propertySchema.showPreview}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.SHADOW:
      return (
        <div>
          <ShadowEditor
            value={value}
            onChange={handleChange}
            showPreview={propertySchema.showPreview}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.FONT:
      return (
        <div>
          <FontSelector
            value={value}
            onChange={handleChange}
            showPreview={propertySchema.showPreview}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.ARRAY:
      return (
        <div>
          <TextArea
            value={Array.isArray(value) ? JSON.stringify(value, null, 2) : value}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleChange(parsed);
              } catch (error) {
                // Keep the raw string value for now
                handleChange(e.target.value);
              }
            }}
            placeholder={propertySchema.placeholder || 'Enter array as JSON'}
            rows={4}
            status={!validation.valid ? 'error' : ''}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.OBJECT:
      return (
        <div>
          <TextArea
            value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleChange(parsed);
              } catch (error) {
                // Keep the raw string value for now
                handleChange(e.target.value);
              }
            }}
            placeholder={propertySchema.placeholder || 'Enter object as JSON'}
            rows={6}
            status={!validation.valid ? 'error' : ''}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    case PropertyTypes.JSON:
      return (
        <div>
          <TextArea
            value={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={propertySchema.placeholder || 'Enter JSON'}
            rows={8}
            status={!validation.valid ? 'error' : ''}
            style={{ fontFamily: 'monospace' }}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );

    default:
      // Fallback to text input
      return (
        <div>
          <Input
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={propertySchema.placeholder || 'Enter value'}
            status={!validation.valid ? 'error' : ''}
            {...props}
          />
          {renderTooltip()}
          {renderValidationError()}
        </div>
      );
  }
};

export default PropertyRenderer;
