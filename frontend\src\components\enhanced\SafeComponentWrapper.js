import React from 'react';
import { Al<PERSON>, Button } from 'antd';

/**
 * Safe wrapper component that catches errors and provides fallback UI
 */
class SafeComponentWrapper extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Component error caught by SafeComponentWrapper:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px' }}>
          <Alert
            message="Component Loading Error"
            description={
              <div>
                <p>This component failed to load properly. This might be due to missing dependencies or configuration issues.</p>
                <p><strong>Error:</strong> {this.state.error?.message || 'Unknown error'}</p>
                {this.props.fallback && (
                  <div style={{ marginTop: '16px' }}>
                    <p>Using fallback component:</p>
                    {this.props.fallback}
                  </div>
                )}
              </div>
            }
            type="warning"
            showIcon
            action={
              <Button 
                size="small" 
                onClick={() => this.setState({ hasError: false, error: null })}
              >
                Retry
              </Button>
            }
          />
        </div>
      );
    }

    return this.props.children;
  }
}

export default SafeComponentWrapper;
