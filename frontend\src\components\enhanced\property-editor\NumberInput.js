import React, { useState, useEffect } from 'react';
import { InputNumber, Slider, Space, Typography, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';

const { Text } = Typography;

const NumberInputContainer = styled.div`
  width: 100%;
`;

const InputGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
`;

const SliderContainer = styled.div`
  flex: 1;
  margin-left: 8px;
`;

const UnitSelector = styled.select`
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  min-width: 50px;
`;

/**
 * Enhanced number input with constraints, step controls, and optional slider
 */
const NumberInput = ({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  unit = 'px',
  units = ['px', '%', 'rem', 'em', 'vh', 'vw'],
  showSlider = false,
  showUnit = true,
  placeholder = 'Enter value',
  tooltip,
  precision = 0,
  ...props
}) => {
  const [numericValue, setNumericValue] = useState(0);
  const [selectedUnit, setSelectedUnit] = useState(unit);

  // Parse value on mount and when value changes
  useEffect(() => {
    if (value) {
      const parsed = parseValue(value);
      setNumericValue(parsed.number);
      setSelectedUnit(parsed.unit);
    }
  }, [value]);

  // Parse a value string like "10px" into number and unit
  const parseValue = (val) => {
    if (typeof val === 'number') {
      return { number: val, unit: selectedUnit };
    }
    
    if (typeof val === 'string') {
      const match = val.match(/^(-?\d*\.?\d+)(.*)$/);
      if (match) {
        return {
          number: parseFloat(match[1]),
          unit: match[2] || selectedUnit
        };
      }
    }
    
    return { number: 0, unit: selectedUnit };
  };

  // Format value for output
  const formatValue = (num, unitStr) => {
    if (showUnit && unitStr) {
      return `${num}${unitStr}`;
    }
    return num;
  };

  // Handle numeric value change
  const handleNumberChange = (newValue) => {
    if (newValue !== null && newValue !== undefined) {
      setNumericValue(newValue);
      const formattedValue = formatValue(newValue, selectedUnit);
      onChange?.(formattedValue);
    }
  };

  // Handle unit change
  const handleUnitChange = (e) => {
    const newUnit = e.target.value;
    setSelectedUnit(newUnit);
    const formattedValue = formatValue(numericValue, newUnit);
    onChange?.(formattedValue);
  };

  return (
    <NumberInputContainer>
      <InputGroup>
        <InputNumber
          value={numericValue}
          onChange={handleNumberChange}
          min={min}
          max={max}
          step={step}
          precision={precision}
          placeholder={placeholder}
          style={{ flex: 1 }}
          {...props}
        />
        
        {showUnit && (
          <UnitSelector value={selectedUnit} onChange={handleUnitChange}>
            {units.map(u => (
              <option key={u} value={u}>{u}</option>
            ))}
          </UnitSelector>
        )}
        
        {tooltip && (
          <Tooltip title={tooltip}>
            <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
          </Tooltip>
        )}
      </InputGroup>
      
      {showSlider && (
        <SliderContainer>
          <Slider
            value={numericValue}
            onChange={handleNumberChange}
            min={min}
            max={max}
            step={step}
            tooltip={{ formatter: (val) => `${val}${selectedUnit}` }}
          />
        </SliderContainer>
      )}
      
      {(min !== undefined || max !== undefined) && (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Range: {min} - {max}
        </Text>
      )}
    </NumberInputContainer>
  );
};

export default NumberInput;
