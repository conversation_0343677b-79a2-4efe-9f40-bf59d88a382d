/**
 * WebSocket Exponential Backoff with Jitter Tests
 * 
 * Tests for the enhanced WebSocket reconnection logic with exponential backoff and jitter
 */

import { renderHook, act } from '@testing-library/react';
import { useWebSocketWithBackoff, ConnectionState } from '../../services/websocket';

// Mock WebSocket
class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = WebSocket.CONNECTING;
    this.CONNECTING = 0;
    this.OPEN = 1;
    this.CLOSING = 2;
    this.CLOSED = 3;
    
    // Store instance for testing
    MockWebSocket.instances.push(this);
    
    // Simulate connection after a short delay
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 10);
  }
  
  send(data) {
    this.lastSentData = data;
  }
  
  close(code = 1000, reason = '') {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) {
      this.onclose({ code, reason, wasClean: code === 1000 });
    }
  }
  
  // Simulate error
  simulateError() {
    if (this.onerror) {
      this.onerror(new Error('Connection failed'));
    }
  }
  
  // Simulate close
  simulateClose(code = 1006, reason = 'Abnormal closure') {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) {
      this.onclose({ code, reason, wasClean: false });
    }
  }
}

MockWebSocket.instances = [];

// Mock global WebSocket
global.WebSocket = MockWebSocket;

// Mock timers
jest.useFakeTimers();

describe('WebSocket Exponential Backoff with Jitter', () => {
  beforeEach(() => {
    MockWebSocket.instances = [];
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
  });

  describe('useWebSocketWithBackoff Hook', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false
        })
      );

      expect(result.current.isConnected()).toBe(false);
      expect(result.current.getConnectionState()).toBe(ConnectionState.DISCONNECTED);
      expect(result.current.connectionAttempts).toBe(0);
      expect(result.current.maxAttempts).toBe(10);
    });

    it('should connect successfully', async () => {
      const onOpen = jest.fn();
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false,
          onOpen
        })
      );

      act(() => {
        result.current.connect();
      });

      // Fast-forward timers to simulate connection
      act(() => {
        jest.advanceTimersByTime(20);
      });

      expect(MockWebSocket.instances).toHaveLength(1);
      expect(onOpen).toHaveBeenCalled();
      expect(result.current.isConnected()).toBe(true);
      expect(result.current.getConnectionState()).toBe(ConnectionState.CONNECTED);
    });

    it('should handle disconnection', () => {
      const onClose = jest.fn();
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false,
          onClose
        })
      );

      act(() => {
        result.current.connect();
      });

      act(() => {
        jest.advanceTimersByTime(20);
      });

      act(() => {
        result.current.disconnect();
      });

      expect(onClose).toHaveBeenCalled();
      expect(result.current.isConnected()).toBe(false);
      expect(result.current.getConnectionState()).toBe(ConnectionState.DISCONNECTED);
    });

    it('should send messages when connected', () => {
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false
        })
      );

      act(() => {
        result.current.connect();
      });

      act(() => {
        jest.advanceTimersByTime(20);
      });

      const testMessage = { type: 'test', data: 'hello' };
      const success = result.current.send(testMessage);

      expect(success).toBe(true);
      expect(MockWebSocket.instances[0].lastSentData).toBe(JSON.stringify(testMessage));
    });

    it('should not send messages when disconnected', () => {
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false
        })
      );

      const testMessage = { type: 'test', data: 'hello' };
      const success = result.current.send(testMessage);

      expect(success).toBe(false);
    });
  });

  describe('Exponential Backoff Logic', () => {
    it('should implement exponential backoff delays', () => {
      const onReconnecting = jest.fn();
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false,
          autoReconnect: true,
          baseDelay: 1000,
          maxDelay: 30000,
          jitterFactor: 0,
          onReconnecting
        })
      );

      // Connect first
      act(() => {
        result.current.connect();
      });

      act(() => {
        jest.advanceTimersByTime(20);
      });

      // Simulate connection failures and reconnection attempts
      for (let attempt = 1; attempt <= 5; attempt++) {
        // Simulate connection failure
        act(() => {
          MockWebSocket.instances[MockWebSocket.instances.length - 1].simulateClose();
        });

        // Check that reconnection was scheduled
        expect(onReconnecting).toHaveBeenCalledWith(
          expect.objectContaining({
            attempt,
            maxAttempts: 10,
            delay: expect.any(Number)
          })
        );

        // Get the delay from the last call
        const lastCall = onReconnecting.mock.calls[onReconnecting.mock.calls.length - 1];
        const delay = lastCall[0].delay;

        // Verify exponential backoff (without jitter, should be exactly 2^(attempt-1) * 1000)
        const expectedDelay = Math.min(30000, 1000 * Math.pow(2, attempt - 1));
        expect(delay).toBe(expectedDelay);

        // Fast-forward to trigger reconnection
        act(() => {
          jest.advanceTimersByTime(delay + 20);
        });
      }
    });

    it('should apply jitter to delays', () => {
      const onReconnecting = jest.fn();
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false,
          autoReconnect: true,
          baseDelay: 1000,
          maxDelay: 30000,
          jitterFactor: 0.3,
          onReconnecting
        })
      );

      // Connect first
      act(() => {
        result.current.connect();
      });

      act(() => {
        jest.advanceTimersByTime(20);
      });

      // Simulate multiple connection failures to test jitter
      const delays = [];
      for (let attempt = 1; attempt <= 3; attempt++) {
        // Simulate connection failure
        act(() => {
          MockWebSocket.instances[MockWebSocket.instances.length - 1].simulateClose();
        });

        // Get the delay from the reconnection callback
        const lastCall = onReconnecting.mock.calls[onReconnecting.mock.calls.length - 1];
        const delay = lastCall[0].delay;
        delays.push(delay);

        // Calculate expected range with jitter
        const baseDelay = Math.min(30000, 1000 * Math.pow(2, attempt - 1));
        const jitterRange = baseDelay * 0.3;
        const minDelay = Math.max(1000, baseDelay - jitterRange);
        const maxDelay = baseDelay + jitterRange;

        // Verify delay is within jitter range
        expect(delay).toBeGreaterThanOrEqual(minDelay);
        expect(delay).toBeLessThanOrEqual(maxDelay);

        // Fast-forward to trigger reconnection
        act(() => {
          jest.advanceTimersByTime(delay + 20);
        });
      }

      // Verify that delays are different (jitter effect)
      // With 30% jitter, it's very unlikely all delays would be identical
      const uniqueDelays = new Set(delays);
      expect(uniqueDelays.size).toBeGreaterThan(1);
    });

    it('should respect maximum delay cap', () => {
      const onReconnecting = jest.fn();
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false,
          autoReconnect: true,
          baseDelay: 1000,
          maxDelay: 5000, // Low max delay for testing
          jitterFactor: 0,
          onReconnecting
        })
      );

      // Connect first
      act(() => {
        result.current.connect();
      });

      act(() => {
        jest.advanceTimersByTime(20);
      });

      // Simulate many connection failures
      for (let attempt = 1; attempt <= 10; attempt++) {
        // Simulate connection failure
        act(() => {
          MockWebSocket.instances[MockWebSocket.instances.length - 1].simulateClose();
        });

        // Get the delay
        const lastCall = onReconnecting.mock.calls[onReconnecting.mock.calls.length - 1];
        const delay = lastCall[0].delay;

        // Verify delay never exceeds maxDelay
        expect(delay).toBeLessThanOrEqual(5000);

        // Fast-forward to trigger reconnection
        act(() => {
          jest.advanceTimersByTime(delay + 20);
        });
      }
    });

    it('should stop reconnecting after max attempts', () => {
      const onReconnecting = jest.fn();
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false,
          autoReconnect: true,
          maxReconnectAttempts: 3,
          baseDelay: 100,
          onReconnecting
        })
      );

      // Connect first
      act(() => {
        result.current.connect();
      });

      act(() => {
        jest.advanceTimersByTime(20);
      });

      // Simulate connection failures up to max attempts
      for (let attempt = 1; attempt <= 4; attempt++) {
        // Simulate connection failure
        act(() => {
          MockWebSocket.instances[MockWebSocket.instances.length - 1].simulateClose();
        });

        if (attempt <= 3) {
          // Should attempt reconnection
          expect(onReconnecting).toHaveBeenCalledWith(
            expect.objectContaining({ attempt })
          );

          // Fast-forward to trigger reconnection
          const lastCall = onReconnecting.mock.calls[onReconnecting.mock.calls.length - 1];
          const delay = lastCall[0].delay;
          act(() => {
            jest.advanceTimersByTime(delay + 20);
          });
        } else {
          // Should not attempt reconnection after max attempts
          expect(result.current.getConnectionState()).toBe(ConnectionState.FAILED);
        }
      }

      // Verify exactly 3 reconnection attempts were made
      expect(onReconnecting).toHaveBeenCalledTimes(3);
    });

    it('should reset connection attempts on successful connection', () => {
      const onReconnecting = jest.fn();
      const { result } = renderHook(() => 
        useWebSocketWithBackoff({
          url: 'ws://localhost:8000/test',
          autoConnect: false,
          autoReconnect: true,
          baseDelay: 100,
          onReconnecting
        })
      );

      // Connect first
      act(() => {
        result.current.connect();
      });

      act(() => {
        jest.advanceTimersByTime(20);
      });

      // Simulate a connection failure and reconnection
      act(() => {
        MockWebSocket.instances[0].simulateClose();
      });

      // Fast-forward to trigger reconnection
      const lastCall = onReconnecting.mock.calls[onReconnecting.mock.calls.length - 1];
      const delay = lastCall[0].delay;
      act(() => {
        jest.advanceTimersByTime(delay + 20);
      });

      // Verify connection attempts were reset after successful reconnection
      expect(result.current.connectionAttempts).toBe(0);
    });
  });
});
