import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Checkbox,
  Divider,
  Typography,
  Space,
  Alert,
  Tabs,
  Card,
  Spin,
  Modal,
  Steps,
  Result,
  message
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  MailOutlined,
  MobileOutlined,
  KeyOutlined,
  SafetyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  GoogleOutlined,
  Gith<PERSON>Outlined,
  FacebookOutlined,
  TwitterOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import axios from 'axios';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { login, register } from '../../redux/actions/authActions';
import { LiveRegion } from '../a11y/LiveRegion';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Step } = Steps;

// Styled components
const AuthContainer = styled.div`
  max-width: 480px;
  margin: 0 auto;
  padding: 24px;
`;

const SocialButton = styled(Button)`
  width: 100%;
  margin-bottom: 16px;
`;

const OrDivider = styled(Divider)`
  &::before, &::after {
    border-top: 1px solid ${props => props.theme === 'dark' ? '#374151' : '#e5e7eb'};
  }
  
  .ant-divider-inner-text {
    color: ${props => props.theme === 'dark' ? '#9ca3af' : '#6b7280'};
  }
`;

const PasswordStrengthMeter = styled.div`
  height: 4px;
  border-radius: 2px;
  margin-top: 8px;
  background-color: #e5e7eb;
  overflow: hidden;
`;

const PasswordStrengthIndicator = styled.div`
  height: 100%;
  width: ${props => props.strength * 25}%;
  background-color: ${props => {
    if (props.strength === 0) return '#e5e7eb';
    if (props.strength === 1) return '#ef4444';
    if (props.strength === 2) return '#f59e0b';
    if (props.strength === 3) return '#10b981';
    if (props.strength === 4) return '#10b981';
    return '#e5e7eb';
  }};
  transition: width 0.3s ease;
`;

const PasswordRequirements = styled.ul`
  padding-left: 20px;
  margin-top: 8px;
  
  li {
    color: ${props => props.theme === 'dark' ? '#9ca3af' : '#6b7280'};
    font-size: 12px;
    margin-bottom: 4px;
    
    &.met {
      color: #10b981;
    }
    
    &.not-met {
      color: #ef4444;
    }
  }
`;

/**
 * EnhancedAuthForm component
 * A comprehensive authentication form with login, registration, and MFA support
 */
const EnhancedAuthForm = ({
  mode = 'login',
  redirectTo = '/',
  onSuccess = () => { },
  theme = 'light'
}) => {
  // State
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(mode);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordRequirements, setPasswordRequirements] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false
  });
  const [showMfaModal, setShowMfaModal] = useState(false);
  const [mfaStep, setMfaStep] = useState(0);
  const [mfaCode, setMfaCode] = useState('');
  const [mfaError, setMfaError] = useState(null);
  const [mfaLoading, setMfaLoading] = useState(false);
  const [announceMessage, setAnnounceMessage] = useState('');

  // Hooks
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  // Get redirect path from location state
  const from = location.state?.from?.pathname || redirectTo;

  // Check password strength
  const checkPasswordStrength = (password) => {
    if (!password) {
      setPasswordStrength(0);
      setPasswordRequirements({
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false
      });
      return;
    }

    // Check requirements
    const requirements = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[^A-Za-z0-9]/.test(password)
    };

    setPasswordRequirements(requirements);

    // Calculate strength
    const metRequirements = Object.values(requirements).filter(Boolean).length;
    setPasswordStrength(metRequirements);
  };

  // Handle form submission
  const handleSubmit = async (values) => {
    setLoading(true);
    setError(null);

    try {
      if (activeTab === 'login') {
        // Login with CSRF protection
        const response = await axios.post('/api/auth/login', {
          username: values.username,
          password: values.password
        }, {
          withCredentials: true, // Include CSRF cookie
          headers: {
            'Content-Type': 'application/json',
          }
        });

        // Check if MFA is required
        if (response.data.mfa_required) {
          setShowMfaModal(true);
          setAnnounceMessage('Multi-factor authentication required');
        } else {
          // Dispatch login action
          dispatch(login(response.data));

          // Navigate to redirect path
          navigate(from, { replace: true });

          // Call success callback
          onSuccess(response.data);

          setAnnounceMessage('Login successful');
        }
      } else {
        // Register with CSRF protection
        const response = await axios.post('/api/auth/register', {
          username: values.username,
          email: values.email,
          password: values.password,
          first_name: values.firstName,
          last_name: values.lastName
        }, {
          withCredentials: true, // Include CSRF cookie
          headers: {
            'Content-Type': 'application/json',
          }
        });

        // Dispatch register action
        dispatch(register(response.data));

        // Navigate to redirect path
        navigate(from, { replace: true });

        // Call success callback
        onSuccess(response.data);

        setAnnounceMessage('Registration successful');
      }
    } catch (err) {
      console.error('Authentication error:', err);

      // Handle different error types
      if (err.response?.status === 401) {
        setError('Invalid credentials. Please try again.');
      } else if (err.response?.status === 403) {
        // Handle CSRF errors
        if (err.response.data?.message?.includes('CSRF') ||
          err.response.data?.detail?.includes('CSRF')) {
          setError('Security token expired. Please refresh the page and try again.');
        } else {
          setError('Access denied. Please check your permissions.');
        }
      } else if (err.response?.status === 429) {
        setError('Too many attempts. Please try again later.');
      } else if (err.response?.data?.message) {
        setError(err.response.data.message);
      } else {
        setError('Authentication failed. Please try again.');
      }

      setAnnounceMessage('Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  // Handle MFA verification
  const handleMfaVerification = async () => {
    setMfaLoading(true);
    setMfaError(null);

    try {
      const response = await axios.post('/api/auth/verify-mfa', {
        code: mfaCode,
        username: form.getFieldValue('username')
      }, {
        withCredentials: true, // Include CSRF cookie
        headers: {
          'Content-Type': 'application/json',
        }
      });

      // Dispatch login action
      dispatch(login(response.data));

      // Close MFA modal
      setShowMfaModal(false);

      // Navigate to redirect path
      navigate(from, { replace: true });

      // Call success callback
      onSuccess(response.data);

      setAnnounceMessage('Multi-factor authentication successful');
    } catch (err) {
      console.error('MFA verification error:', err);
      setMfaError(err.response?.data?.message || 'MFA verification failed. Please try again.');
      setAnnounceMessage('Multi-factor authentication failed');
    } finally {
      setMfaLoading(false);
    }
  };

  // Handle MFA setup
  const handleMfaSetup = () => {
    setMfaStep(1);
  };

  // Render login form
  const renderLoginForm = () => {
    return (
      <Form
        form={form}
        name="login"
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="username"
          label="Username"
          rules={[{ required: true, message: 'Please enter your username' }]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="Username"
            size="large"
            autoComplete="username"
            aria-label="Username"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="Password"
          rules={[{ required: true, message: 'Please enter your password' }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Password"
            size="large"
            autoComplete="current-password"
            aria-label="Password"
            iconRender={visible => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Checkbox>Remember me</Checkbox>
            <a href="/forgot-password">Forgot password?</a>
          </div>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            block
            loading={loading}
          >
            Log In
          </Button>
        </Form.Item>

        <OrDivider theme={theme}>Or</OrDivider>

        <SocialButton
          icon={<GoogleOutlined />}
          size="large"
        >
          Continue with Google
        </SocialButton>

        <SocialButton
          icon={<GithubOutlined />}
          size="large"
        >
          Continue with GitHub
        </SocialButton>
      </Form>
    );
  };

  // Render registration form
  const renderRegistrationForm = () => {
    return (
      <Form
        form={form}
        name="register"
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="username"
          label="Username"
          rules={[
            { required: true, message: 'Please enter a username' },
            { min: 3, message: 'Username must be at least 3 characters' }
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="Username"
            size="large"
            autoComplete="username"
            aria-label="Username"
          />
        </Form.Item>

        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: 'Please enter your email' },
            { type: 'email', message: 'Please enter a valid email' }
          ]}
        >
          <Input
            prefix={<MailOutlined />}
            placeholder="Email"
            size="large"
            autoComplete="email"
            aria-label="Email"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="Password"
          rules={[
            { required: true, message: 'Please enter a password' },
            { min: 8, message: 'Password must be at least 8 characters' }
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Password"
            size="large"
            autoComplete="new-password"
            aria-label="Password"
            iconRender={visible => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
            onChange={e => checkPasswordStrength(e.target.value)}
          />
        </Form.Item>

        <PasswordStrengthMeter>
          <PasswordStrengthIndicator strength={passwordStrength} />
        </PasswordStrengthMeter>

        <PasswordRequirements theme={theme}>
          <li className={passwordRequirements.length ? 'met' : 'not-met'}>
            At least 8 characters
          </li>
          <li className={passwordRequirements.uppercase ? 'met' : 'not-met'}>
            At least one uppercase letter
          </li>
          <li className={passwordRequirements.lowercase ? 'met' : 'not-met'}>
            At least one lowercase letter
          </li>
          <li className={passwordRequirements.number ? 'met' : 'not-met'}>
            At least one number
          </li>
          <li className={passwordRequirements.special ? 'met' : 'not-met'}>
            At least one special character
          </li>
        </PasswordRequirements>

        <Form.Item
          name="confirmPassword"
          label="Confirm Password"
          dependencies={['password']}
          rules={[
            { required: true, message: 'Please confirm your password' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('The two passwords do not match'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Confirm Password"
            size="large"
            autoComplete="new-password"
            aria-label="Confirm Password"
            iconRender={visible => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item
          name="firstName"
          label="First Name"
        >
          <Input
            placeholder="First Name"
            size="large"
            autoComplete="given-name"
            aria-label="First Name"
          />
        </Form.Item>

        <Form.Item
          name="lastName"
          label="Last Name"
        >
          <Input
            placeholder="Last Name"
            size="large"
            autoComplete="family-name"
            aria-label="Last Name"
          />
        </Form.Item>

        <Form.Item
          name="agreement"
          valuePropName="checked"
          rules={[
            {
              validator: (_, value) =>
                value ? Promise.resolve() : Promise.reject(new Error('You must accept the terms and conditions')),
            },
          ]}
        >
          <Checkbox>
            I agree to the <a href="/terms">Terms of Service</a> and <a href="/privacy">Privacy Policy</a>
          </Checkbox>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            block
            loading={loading}
          >
            Register
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // Render MFA modal
  const renderMfaModal = () => {
    return (
      <Modal
        title="Multi-Factor Authentication"
        open={showMfaModal}
        onCancel={() => setShowMfaModal(false)}
        footer={null}
        width={400}
      >
        <Steps current={mfaStep} style={{ marginBottom: 24 }}>
          <Step title="Verify" />
          <Step title="Setup" />
          <Step title="Complete" />
        </Steps>

        {mfaStep === 0 && (
          <>
            <Paragraph>
              Please enter the verification code from your authenticator app.
            </Paragraph>

            {mfaError && (
              <Alert
                message={mfaError}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Input
              prefix={<KeyOutlined />}
              placeholder="Verification Code"
              size="large"
              value={mfaCode}
              onChange={e => setMfaCode(e.target.value)}
              style={{ marginBottom: 16 }}
              maxLength={6}
              autoFocus
            />

            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button onClick={() => setShowMfaModal(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                onClick={handleMfaVerification}
                loading={mfaLoading}
                disabled={mfaCode.length !== 6}
              >
                Verify
              </Button>
            </div>

            <Divider />

            <Paragraph>
              <a onClick={handleMfaSetup}>
                Don't have an authenticator app set up?
              </a>
            </Paragraph>
          </>
        )}

        {mfaStep === 1 && (
          <>
            <Result
              status="success"
              title="MFA Setup Complete"
              subTitle="You have successfully set up multi-factor authentication."
              extra={[
                <Button
                  type="primary"
                  key="done"
                  onClick={() => setMfaStep(0)}
                >
                  Done
                </Button>
              ]}
            />
          </>
        )}
      </Modal>
    );
  };

  return (
    <AuthContainer>
      {/* Accessibility announcement */}
      <LiveRegion id="auth-announcer" ariaLive="polite">
        {announceMessage}
      </LiveRegion>

      <Card>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 24 }}>
          {activeTab === 'login' ? 'Log In' : 'Create Account'}
        </Title>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          centered
        >
          <TabPane tab="Log In" key="login">
            {renderLoginForm()}
          </TabPane>

          <TabPane tab="Register" key="register">
            {renderRegistrationForm()}
          </TabPane>
        </Tabs>
      </Card>

      {renderMfaModal()}
    </AuthContainer>
  );
};

export default EnhancedAuthForm;
