/**
 * Data Synchronization Service
 * 
 * Handles real-time data synchronization between frontend and backend
 * with conflict resolution and offline support
 */

import { apiClient } from './api';
import { realTimeNotifications } from './NotificationService';

class DataSyncService {
  constructor() {
    this.syncQueue = [];
    this.isSyncing = false;
    this.lastSyncTime = null;
    this.conflictResolver = null;
    this.syncInterval = null;
    this.retryAttempts = new Map();
    this.maxRetryAttempts = 3;
    this.syncIntervalMs = 30000; // 30 seconds
    this.debug = process.env.NODE_ENV === 'development';
    
    // Local storage keys
    this.STORAGE_KEYS = {
      PENDING_SYNC: 'app_builder_pending_sync',
      LAST_SYNC: 'app_builder_last_sync',
      OFFLINE_DATA: 'app_builder_offline_data'
    };
  }

  /**
   * Initialize data sync service
   * @param {Object} options - Configuration options
   */
  initialize(options = {}) {
    const {
      syncInterval = this.syncIntervalMs,
      conflictResolver = this._defaultConflictResolver,
      autoSync = true
    } = options;

    this.syncIntervalMs = syncInterval;
    this.conflictResolver = conflictResolver;

    // Load pending sync operations from storage
    this._loadPendingSyncOperations();

    // Start auto-sync if enabled
    if (autoSync) {
      this.startAutoSync();
    }

    // Listen for online/offline events
    this._setupNetworkListeners();

    this._log('Data sync service initialized');
  }

  /**
   * Start automatic synchronization
   */
  startAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.syncPendingOperations();
    }, this.syncIntervalMs);

    this._log('Auto-sync started');
  }

  /**
   * Stop automatic synchronization
   */
  stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    this._log('Auto-sync stopped');
  }

  /**
   * Queue a data operation for synchronization
   * @param {Object} operation - Data operation
   */
  queueOperation(operation) {
    const syncOperation = {
      id: this._generateOperationId(),
      timestamp: Date.now(),
      retryCount: 0,
      ...operation
    };

    this.syncQueue.push(syncOperation);
    this._savePendingSyncOperations();

    // Try to sync immediately if online
    if (navigator.onLine) {
      this.syncPendingOperations();
    }

    this._log('Operation queued for sync:', syncOperation);
    return syncOperation.id;
  }

  /**
   * Sync all pending operations
   */
  async syncPendingOperations() {
    if (this.isSyncing || this.syncQueue.length === 0 || !navigator.onLine) {
      return;
    }

    this.isSyncing = true;
    this._log('Starting sync of', this.syncQueue.length, 'operations');

    try {
      const operations = [...this.syncQueue];
      const results = [];

      for (const operation of operations) {
        try {
          const result = await this._syncOperation(operation);
          results.push({ operation, result, success: true });
          
          // Remove successful operation from queue
          this._removeOperationFromQueue(operation.id);
          
        } catch (error) {
          this._log('Sync failed for operation:', operation.id, error);
          
          // Increment retry count
          operation.retryCount = (operation.retryCount || 0) + 1;
          
          // Remove operation if max retries exceeded
          if (operation.retryCount >= this.maxRetryAttempts) {
            this._log('Max retries exceeded for operation:', operation.id);
            this._removeOperationFromQueue(operation.id);
            
            // Notify user of failed sync
            realTimeNotifications.showMessage(
              `Failed to sync ${operation.type} after ${this.maxRetryAttempts} attempts`,
              'error'
            );
          }
          
          results.push({ operation, error, success: false });
        }
      }

      this.lastSyncTime = Date.now();
      this._saveLastSyncTime();
      this._savePendingSyncOperations();

      this._log('Sync completed. Results:', results);
      
      // Notify successful sync if there were operations
      if (results.some(r => r.success)) {
        realTimeNotifications.showMessage('Data synchronized successfully', 'success');
      }

    } catch (error) {
      this._error('Sync process failed:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Sync a single operation
   * @param {Object} operation - Operation to sync
   */
  async _syncOperation(operation) {
    const { type, method, endpoint, data, id } = operation;

    switch (type) {
      case 'create':
        return await apiClient.post(endpoint, data);
      
      case 'update':
        return await apiClient.put(endpoint, data);
      
      case 'delete':
        return await apiClient.delete(endpoint);
      
      case 'custom':
        return await apiClient[method.toLowerCase()](endpoint, data);
      
      default:
        throw new Error(`Unknown operation type: ${type}`);
    }
  }

  /**
   * Create app data operation
   * @param {Object} appData - App data to create
   */
  createApp(appData) {
    return this.queueOperation({
      type: 'create',
      endpoint: '/api/apps/',
      data: appData,
      description: `Create app: ${appData.name}`
    });
  }

  /**
   * Update app data operation
   * @param {string} appId - App ID
   * @param {Object} appData - App data to update
   */
  updateApp(appId, appData) {
    return this.queueOperation({
      type: 'update',
      endpoint: `/api/apps/${appId}/`,
      data: appData,
      description: `Update app: ${appId}`
    });
  }

  /**
   * Delete app operation
   * @param {string} appId - App ID to delete
   */
  deleteApp(appId) {
    return this.queueOperation({
      type: 'delete',
      endpoint: `/api/apps/${appId}/`,
      description: `Delete app: ${appId}`
    });
  }

  /**
   * Save app configuration with auto-sync
   * @param {string} appId - App ID
   * @param {Object} config - App configuration
   */
  saveAppConfig(appId, config) {
    // Save to local storage immediately
    this._saveToLocalStorage(`app_config_${appId}`, config);
    
    // Queue for server sync
    return this.updateApp(appId, { configuration: config });
  }

  /**
   * Load app configuration with fallback to local storage
   * @param {string} appId - App ID
   */
  async loadAppConfig(appId) {
    try {
      // Try to load from server first
      const response = await apiClient.get(`/api/apps/${appId}/`);
      return response.configuration;
    } catch (error) {
      this._log('Failed to load from server, using local storage:', error);
      
      // Fallback to local storage
      return this._loadFromLocalStorage(`app_config_${appId}`);
    }
  }

  /**
   * Default conflict resolver
   * @param {Object} localData - Local data
   * @param {Object} serverData - Server data
   * @returns {Object} Resolved data
   */
  _defaultConflictResolver(localData, serverData) {
    // Simple last-write-wins strategy
    const localTimestamp = localData.lastModified || 0;
    const serverTimestamp = serverData.lastModified || 0;
    
    return serverTimestamp > localTimestamp ? serverData : localData;
  }

  /**
   * Setup network event listeners
   */
  _setupNetworkListeners() {
    window.addEventListener('online', () => {
      this._log('Network online - starting sync');
      realTimeNotifications.showMessage('Connection restored - syncing data', 'info');
      this.syncPendingOperations();
    });

    window.addEventListener('offline', () => {
      this._log('Network offline - queuing operations');
      realTimeNotifications.showMessage('Working offline - changes will sync when online', 'warning');
    });
  }

  /**
   * Load pending sync operations from storage
   */
  _loadPendingSyncOperations() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.PENDING_SYNC);
      if (stored) {
        this.syncQueue = JSON.parse(stored);
        this._log('Loaded', this.syncQueue.length, 'pending operations from storage');
      }
    } catch (error) {
      this._error('Failed to load pending sync operations:', error);
      this.syncQueue = [];
    }
  }

  /**
   * Save pending sync operations to storage
   */
  _savePendingSyncOperations() {
    try {
      localStorage.setItem(this.STORAGE_KEYS.PENDING_SYNC, JSON.stringify(this.syncQueue));
    } catch (error) {
      this._error('Failed to save pending sync operations:', error);
    }
  }

  /**
   * Save last sync time
   */
  _saveLastSyncTime() {
    try {
      localStorage.setItem(this.STORAGE_KEYS.LAST_SYNC, this.lastSyncTime.toString());
    } catch (error) {
      this._error('Failed to save last sync time:', error);
    }
  }

  /**
   * Save data to local storage
   * @param {string} key - Storage key
   * @param {*} data - Data to save
   */
  _saveToLocalStorage(key, data) {
    try {
      localStorage.setItem(key, JSON.stringify({
        data,
        timestamp: Date.now()
      }));
    } catch (error) {
      this._error('Failed to save to local storage:', error);
    }
  }

  /**
   * Load data from local storage
   * @param {string} key - Storage key
   * @returns {*} Loaded data
   */
  _loadFromLocalStorage(key) {
    try {
      const stored = localStorage.getItem(key);
      if (stored) {
        const parsed = JSON.parse(stored);
        return parsed.data;
      }
    } catch (error) {
      this._error('Failed to load from local storage:', error);
    }
    return null;
  }

  /**
   * Remove operation from queue
   * @param {string} operationId - Operation ID
   */
  _removeOperationFromQueue(operationId) {
    this.syncQueue = this.syncQueue.filter(op => op.id !== operationId);
  }

  /**
   * Generate unique operation ID
   * @returns {string} Operation ID
   */
  _generateOperationId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get sync status
   * @returns {Object} Sync status
   */
  getSyncStatus() {
    return {
      isSyncing: this.isSyncing,
      pendingOperations: this.syncQueue.length,
      lastSyncTime: this.lastSyncTime,
      isOnline: navigator.onLine
    };
  }

  /**
   * Clear all pending operations
   */
  clearPendingOperations() {
    this.syncQueue = [];
    this._savePendingSyncOperations();
    this._log('Cleared all pending operations');
  }

  /**
   * Destroy the service
   */
  destroy() {
    this.stopAutoSync();
    this.clearPendingOperations();
    this._log('Data sync service destroyed');
  }

  /**
   * Log debug message
   * @param {...any} args - Arguments to log
   */
  _log(...args) {
    if (this.debug) {
      console.log('[DataSyncService]', ...args);
    }
  }

  /**
   * Log error message
   * @param {...any} args - Arguments to log
   */
  _error(...args) {
    console.error('[DataSyncService]', ...args);
  }
}

// Export singleton instance
export default new DataSyncService();
