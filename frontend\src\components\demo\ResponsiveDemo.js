import React from 'react';
import { <PERSON>, Row, Col, Button, Table, Space, Typography, Tag, Grid } from 'antd';
import {
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  BarChartOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import ResponsiveContainer, { useResponsive, ResponsiveGrid } from '../common/ResponsiveContainer';

const { Title, Text } = Typography;

/**
 * Demo component to showcase responsive design improvements
 */
const ResponsiveDemo = () => {
  const { isMobile, isTablet, isDesktop, screens } = useResponsive();

  // Sample data for demonstration
  const sampleData = [
    { key: '1', name: 'Component A', type: 'Button', status: 'Active' },
    { key: '2', name: 'Component B', type: 'Input', status: 'Inactive' },
    { key: '3', name: 'Component C', type: 'Card', status: 'Active' },
    { key: '4', name: 'Component D', type: 'Table', status: 'Active' },
  ];

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'Active' ? 'green' : 'red'}>
          {status}
        </Tag>
      ),
    },
  ];

  const getCurrentBreakpointIcon = () => {
    if (isMobile) return <MobileOutlined style={{ color: '#1890ff' }} />;
    if (isTablet) return <TabletOutlined style={{ color: '#52c41a' }} />;
    return <DesktopOutlined style={{ color: '#722ed1' }} />;
  };

  const getCurrentBreakpointText = () => {
    if (isMobile) return 'Mobile';
    if (isTablet) return 'Tablet';
    return 'Desktop';
  };

  return (
    <ResponsiveContainer className="responsive-demo">
      <div className="dashboard-container">
        {/* Header Section */}
        <Card className="dashboard-card" style={{ marginBottom: 16 }}>
          <div className="card-header">
            <Title level={2}>
              <DashboardOutlined /> Responsive Design Demo
            </Title>
            <Space>
              {getCurrentBreakpointIcon()}
              <Text strong>Current: {getCurrentBreakpointText()}</Text>
            </Space>
          </div>
        </Card>

        {/* Breakpoint Information */}
        <Card className="dashboard-card" style={{ marginBottom: 16 }}>
          <div className="card-header">
            <Title level={4}>Breakpoint Information</Title>
          </div>
          <div className="card-content">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Card size="small">
                  <Space direction="vertical" align="center">
                    <MobileOutlined style={{ fontSize: 24, color: isMobile ? '#1890ff' : '#d9d9d9' }} />
                    <Text strong={isMobile}>Mobile</Text>
                    <Text type="secondary">≤ 768px</Text>
                  </Space>
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card size="small">
                  <Space direction="vertical" align="center">
                    <TabletOutlined style={{ fontSize: 24, color: isTablet ? '#52c41a' : '#d9d9d9' }} />
                    <Text strong={isTablet}>Tablet</Text>
                    <Text type="secondary">769px - 1024px</Text>
                  </Space>
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card size="small">
                  <Space direction="vertical" align="center">
                    <DesktopOutlined style={{ fontSize: 24, color: isDesktop ? '#722ed1' : '#d9d9d9' }} />
                    <Text strong={isDesktop}>Desktop</Text>
                    <Text type="secondary">≥ 1025px</Text>
                  </Space>
                </Card>
              </Col>
            </Row>
          </div>
        </Card>

        {/* Responsive Grid Demo */}
        <Card className="dashboard-card" style={{ marginBottom: 16 }}>
          <div className="card-header">
            <Title level={4}>Responsive Grid Layout</Title>
          </div>
          <div className="card-content">
            <ResponsiveGrid
              columns={{ mobile: 1, tablet: 2, desktop: 3 }}
              gap={{ mobile: 8, tablet: 12, desktop: 16 }}
            >
              {[1, 2, 3, 4, 5, 6].map(num => (
                <Card key={num} size="small" className="grid-item">
                  <Space direction="vertical" align="center">
                    <BarChartOutlined style={{ fontSize: 20 }} />
                    <Text>Item {num}</Text>
                  </Space>
                </Card>
              ))}
            </ResponsiveGrid>
          </div>
        </Card>

        {/* Data Visualization Demo */}
        <Card className="dashboard-card" style={{ marginBottom: 16 }}>
          <div className="card-header">
            <Title level={4}>Data Visualization</Title>
          </div>
          <div className="card-content">
            <div className="data-visualization">
              <div className="chart-container" style={{
                height: isMobile ? 200 : isTablet ? 250 : 300,
                backgroundColor: '#f5f5f5',
                border: '1px dashed #d9d9d9',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 4
              }}>
                <Space direction="vertical" align="center">
                  <BarChartOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <Text type="secondary">Chart Placeholder</Text>
                  <Text type="secondary">
                    Height: {isMobile ? '200px' : isTablet ? '250px' : '300px'}
                  </Text>
                </Space>
              </div>
            </div>
          </div>
        </Card>

        {/* Responsive Table Demo */}
        <Card className="dashboard-card" style={{ marginBottom: 16 }}>
          <div className="card-header">
            <Title level={4}>Responsive Table</Title>
          </div>
          <div className="card-content">
            <Table
              dataSource={sampleData}
              columns={columns}
              pagination={false}
              scroll={{ x: 400 }}
              size={isMobile ? 'small' : 'middle'}
            />
          </div>
        </Card>

        {/* Button Group Demo */}
        <Card className="dashboard-card">
          <div className="card-header">
            <Title level={4}>Responsive Button Group</Title>
          </div>
          <div className="card-content">
            <div className="button-group">
              <Button type="primary">Primary Action</Button>
              <Button>Secondary Action</Button>
              <Button>Tertiary Action</Button>
            </div>
          </div>
        </Card>
      </div>
    </ResponsiveContainer>
  );
};

export default ResponsiveDemo;
