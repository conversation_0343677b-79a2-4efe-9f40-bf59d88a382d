import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Button, Badge, Tooltip, Space, Typography, Popover, List } from 'antd';
import { 
  BulbOutlined, 
  RobotOutlined, 
  ThunderboltOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';
import AISuggestionsPanel from './AISuggestionsPanel';

const { Text } = Typography;

/**
 * AI Design Suggestions
 * 
 * Main AI suggestions component that provides intelligent design recommendations
 * based on current app state and user interactions.
 */

const SuggestionsContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SuggestionCard = styled(Card)`
  .ant-card-body {
    padding: 12px;
  }
  
  .suggestion-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: #262626;
  }
  
  .suggestion-description {
    color: #595959;
    font-size: 12px;
    line-height: 1.4;
  }
  
  .suggestion-actions {
    margin-top: 8px;
    display: flex;
    gap: 4px;
  }
`;

const CompactSuggestion = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: #f0f9ff;
    border-color: #91d5ff;
  }
  
  .suggestion-icon {
    color: #52c41a;
    font-size: 16px;
  }
  
  .suggestion-text {
    flex: 1;
    font-size: 13px;
    color: #262626;
  }
  
  .suggestion-count {
    background: #52c41a;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 600;
  }
`;

const AIDesignSuggestions = ({
  suggestions = [],
  loading = false,
  onApply,
  onDismiss,
  compact = false,
  showPanel = false,
  components = [],
  selectedComponent = null
}) => {
  const [panelVisible, setPanelVisible] = useState(showPanel);
  const [dismissedSuggestions, setDismissedSuggestions] = useState(new Set());

  // AI suggestions hook
  const {
    suggestions: hookSuggestions,
    loading: hookLoading,
    hasLayoutSuggestions,
    hasCombinationSuggestions,
    applyLayoutSuggestion,
    applyComponentCombination,
    refresh
  } = useAIDesignSuggestions({
    autoRefresh: true,
    context: { selectedComponent }
  });

  // Use hook data if no props provided
  const activeSuggestions = suggestions.length > 0 ? suggestions : [
    ...hookSuggestions.layout.map(s => ({ ...s, type: 'layout' })),
    ...hookSuggestions.combinations.map(s => ({ ...s, type: 'combination' }))
  ];
  const isLoading = loading || hookLoading;
  const totalSuggestions = activeSuggestions.filter(s => !dismissedSuggestions.has(s.id)).length;

  // Handle applying suggestions
  const handleApply = useCallback((suggestion) => {
    if (onApply) {
      onApply(suggestion);
    } else {
      // Use hook methods based on suggestion type
      if (suggestion.type === 'layout') {
        applyLayoutSuggestion(suggestion);
      } else if (suggestion.type === 'combination') {
        applyComponentCombination(suggestion);
      }
    }
  }, [onApply, applyLayoutSuggestion, applyComponentCombination]);

  // Handle dismissing suggestions
  const handleDismiss = useCallback((suggestion) => {
    setDismissedSuggestions(prev => new Set([...prev, suggestion.id]));
    if (onDismiss) {
      onDismiss(suggestion);
    }
  }, [onDismiss]);

  // Auto-show panel when suggestions are available
  useEffect(() => {
    if (totalSuggestions > 0 && !panelVisible && components.length > 2) {
      // Auto-show after a delay to not interrupt user workflow
      const timer = setTimeout(() => {
        setPanelVisible(true);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [totalSuggestions, panelVisible, components.length]);

  // Don't render if no suggestions
  if (totalSuggestions === 0 && !isLoading) {
    return null;
  }

  // Compact mode for header/toolbar
  if (compact) {
    const topSuggestion = activeSuggestions.find(s => !dismissedSuggestions.has(s.id));
    
    return (
      <SuggestionsContainer>
        {isLoading ? (
          <Badge status="processing" text="AI analyzing..." />
        ) : topSuggestion ? (
          <Popover
            content={
              <div style={{ maxWidth: 300 }}>
                <div className="suggestion-title">{topSuggestion.title}</div>
                <div className="suggestion-description">{topSuggestion.description}</div>
                <div className="suggestion-actions">
                  <Button
                    type="primary"
                    size="small"
                    icon={<CheckOutlined />}
                    onClick={() => handleApply(topSuggestion)}
                  >
                    Apply
                  </Button>
                  <Button
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={() => handleDismiss(topSuggestion)}
                  >
                    Dismiss
                  </Button>
                  <Button
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => setPanelVisible(true)}
                  >
                    View All
                  </Button>
                </div>
              </div>
            }
            title={
              <Space>
                <RobotOutlined style={{ color: '#1890ff' }} />
                AI Suggestion
              </Space>
            }
            trigger="hover"
          >
            <CompactSuggestion onClick={() => setPanelVisible(true)}>
              <BulbOutlined className="suggestion-icon" />
              <span className="suggestion-text">{topSuggestion.title}</span>
              {totalSuggestions > 1 && (
                <span className="suggestion-count">+{totalSuggestions - 1}</span>
              )}
            </CompactSuggestion>
          </Popover>
        ) : null}

        <Tooltip title="Open AI Suggestions Panel">
          <Button
            type="text"
            icon={<RobotOutlined />}
            onClick={() => setPanelVisible(true)}
            style={{ color: totalSuggestions > 0 ? '#1890ff' : '#8c8c8c' }}
          />
        </Tooltip>

        {/* AI Suggestions Panel */}
        <AISuggestionsPanel
          visible={panelVisible}
          onClose={() => setPanelVisible(false)}
          components={components}
          selectedComponent={selectedComponent}
          onApplyLayoutSuggestion={handleApply}
          onApplyComponentCombination={handleApply}
        />
      </SuggestionsContainer>
    );
  }

  // Full mode for dedicated suggestions area
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <Text strong>AI Suggestions</Text>
            {totalSuggestions > 0 && (
              <Badge count={totalSuggestions} style={{ backgroundColor: '#52c41a' }} />
            )}
          </Space>
          
          <Space>
            <Tooltip title="Refresh suggestions">
              <Button
                type="text"
                size="small"
                icon={<ThunderboltOutlined />}
                onClick={refresh}
                loading={isLoading}
              />
            </Tooltip>
            <Button
              size="small"
              onClick={() => setPanelVisible(true)}
            >
              View All
            </Button>
          </Space>
        </div>

        {isLoading ? (
          <Card loading={true} />
        ) : (
          <List
            dataSource={activeSuggestions.filter(s => !dismissedSuggestions.has(s.id)).slice(0, 3)}
            renderItem={(suggestion) => (
              <List.Item key={suggestion.id}>
                <SuggestionCard
                  size="small"
                  style={{ width: '100%' }}
                  actions={[
                    <Button
                      key="apply"
                      type="primary"
                      size="small"
                      icon={<CheckOutlined />}
                      onClick={() => handleApply(suggestion)}
                    >
                      Apply
                    </Button>,
                    <Button
                      key="dismiss"
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={() => handleDismiss(suggestion)}
                    >
                      Dismiss
                    </Button>
                  ]}
                >
                  <div className="suggestion-title">{suggestion.title}</div>
                  <div className="suggestion-description">{suggestion.description}</div>
                  {suggestion.confidence && (
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary" style={{ fontSize: 11 }}>
                        Confidence: {Math.round(suggestion.confidence * 100)}%
                      </Text>
                    </div>
                  )}
                </SuggestionCard>
              </List.Item>
            )}
          />
        )}
      </Space>

      {/* AI Suggestions Panel */}
      <AISuggestionsPanel
        visible={panelVisible}
        onClose={() => setPanelVisible(false)}
        components={components}
        selectedComponent={selectedComponent}
        onApplyLayoutSuggestion={handleApply}
        onApplyComponentCombination={handleApply}
      />
    </div>
  );
};

export default AIDesignSuggestions;
