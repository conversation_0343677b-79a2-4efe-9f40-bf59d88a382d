import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, Button, Space, Typography, Card, Upload, message } from 'antd';
import { SaveOutlined, UploadOutlined, EyeOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

/**
 * Template Editor
 * 
 * Form component for creating and editing templates with metadata and preview.
 */

const TemplateEditor = ({
  template = null,
  components = [],
  onSave,
  onCancel,
  mode = 'create' // 'create', 'edit', 'save'
}) => {
  const [form] = Form.useForm();
  const [loading, setSaving] = useState(false);
  const [thumbnailUrl, setThumbnailUrl] = useState(template?.thumbnail || '');

  useEffect(() => {
    if (template) {
      form.setFieldsValue({
        name: template.name,
        description: template.description,
        category: template.category,
        type: template.type || 'layout',
        isPublic: template.isPublic || false,
        tags: template.tags || []
      });
      setThumbnailUrl(template.thumbnail || '');
    } else if (mode === 'save') {
      // Auto-generate name for save mode
      const timestamp = new Date().toLocaleDateString();
      form.setFieldsValue({
        name: `My Template - ${timestamp}`,
        description: 'Template created from current app design',
        category: 'custom',
        type: 'layout',
        isPublic: false,
        tags: []
      });
    }
  }, [template, mode, form]);

  const handleSave = async (values) => {
    setSaving(true);
    try {
      const templateData = {
        ...values,
        components: components || [],
        componentCount: components?.length || 0,
        thumbnail: thumbnailUrl,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (template) {
        templateData.id = template.id;
      }

      await onSave(templateData);
      message.success(`Template ${mode === 'edit' ? 'updated' : 'saved'} successfully!`);
      form.resetFields();
    } catch (error) {
      message.error(`Failed to ${mode === 'edit' ? 'update' : 'save'} template`);
    } finally {
      setSaving(false);
    }
  };

  const handleThumbnailUpload = (info) => {
    if (info.file.status === 'done') {
      setThumbnailUrl(info.file.response.url);
      message.success('Thumbnail uploaded successfully');
    } else if (info.file.status === 'error') {
      message.error('Thumbnail upload failed');
    }
  };

  const categories = [
    'basic',
    'business',
    'portfolio',
    'blog',
    'ecommerce',
    'dashboard',
    'landing',
    'custom'
  ];

  const types = [
    { value: 'layout', label: 'Layout Template' },
    { value: 'app', label: 'App Template' },
    { value: 'component', label: 'Component Template' },
    { value: 'mobile', label: 'Mobile Template' }
  ];

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={{
          type: 'layout',
          isPublic: false,
          category: 'custom'
        }}
      >
        <Form.Item
          name="name"
          label="Template Name"
          rules={[
            { required: true, message: 'Please enter a template name' },
            { min: 3, message: 'Name must be at least 3 characters' }
          ]}
        >
          <Input placeholder="Enter template name" />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
          rules={[
            { required: true, message: 'Please enter a description' },
            { min: 10, message: 'Description must be at least 10 characters' }
          ]}
        >
          <TextArea
            rows={3}
            placeholder="Describe what this template is for and what it includes"
          />
        </Form.Item>

        <Space style={{ width: '100%' }} size="large">
          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select a category' }]}
            style={{ flex: 1 }}
          >
            <Select placeholder="Select category">
              {categories.map(category => (
                <Option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="type"
            label="Type"
            rules={[{ required: true, message: 'Please select a type' }]}
            style={{ flex: 1 }}
          >
            <Select placeholder="Select type">
              {types.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Space>

        <Form.Item
          name="tags"
          label="Tags"
          help="Add tags to help others find your template"
        >
          <Select
            mode="tags"
            placeholder="Add tags (press Enter to add)"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="isPublic"
          label="Visibility"
          valuePropName="checked"
        >
          <Switch
            checkedChildren="Public"
            unCheckedChildren="Private"
          />
        </Form.Item>

        {/* Thumbnail upload */}
        <Form.Item label="Thumbnail">
          <Upload
            name="thumbnail"
            listType="picture-card"
            className="template-thumbnail-uploader"
            showUploadList={false}
            action="/api/upload/thumbnail"
            onChange={handleThumbnailUpload}
          >
            {thumbnailUrl ? (
              <img
                src={thumbnailUrl}
                alt="thumbnail"
                style={{ width: '100%', height: '100%', objectFit: 'cover' }}
              />
            ) : (
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            )}
          </Upload>
          <Text type="secondary" style={{ fontSize: 12 }}>
            Recommended size: 400x300px
          </Text>
        </Form.Item>

        {/* Component preview */}
        {components && components.length > 0 && (
          <Card
            title="Template Preview"
            size="small"
            style={{ marginBottom: 16 }}
          >
            <div style={{ marginBottom: 8 }}>
              <Text strong>Components included: </Text>
              <Text>{components.length}</Text>
            </div>
            <div style={{ maxHeight: 120, overflowY: 'auto' }}>
              {components.map((component, index) => (
                <div key={index} style={{ fontSize: 12, color: '#666' }}>
                  • {component.type} {component.props?.title && `- ${component.props.title}`}
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Form actions */}
        <Form.Item style={{ marginBottom: 0 }}>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
            >
              {mode === 'edit' ? 'Update Template' : 'Save Template'}
            </Button>
            
            {onCancel && (
              <Button onClick={onCancel}>
                Cancel
              </Button>
            )}
            
            <Button
              type="dashed"
              icon={<EyeOutlined />}
              onClick={() => {
                // Preview functionality could be added here
                message.info('Preview functionality coming soon');
              }}
            >
              Preview
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
};

export default TemplateEditor;
