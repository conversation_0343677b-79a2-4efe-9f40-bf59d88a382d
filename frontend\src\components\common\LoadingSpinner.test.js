import React from 'react';
import { render, screen } from '@testing-library/react';
import LoadingSpinner from './LoadingSpinner';
import { LoadingOutlined } from '@ant-design/icons';

describe('LoadingSpinner', () => {
  test('renders with default props', () => {
    render(<LoadingSpinner />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('renders with custom tip', () => {
    render(<LoadingSpinner tip="Custom loading message" />);
    expect(screen.getByText('Custom loading message')).toBeInTheDocument();
  });

  test('renders in fullscreen mode', () => {
    const { container } = render(<LoadingSpinner fullScreen={true} />);
    expect(container.querySelector('.loading-container')).toBeInTheDocument();
  });

  test('renders with custom background color', () => {
    const { container } = render(
      <LoadingSpinner fullScreen={true} backgroundColor="rgba(0, 0, 0, 0.5)" />
    );
    const loadingContainer = container.querySelector('.loading-container');
    expect(loadingContainer).toHaveStyle('background-color: rgba(0, 0, 0, 0.5)');
  });

  test('renders with different sizes', () => {
    const { rerender } = render(<LoadingSpinner size="small" />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();

    rerender(<LoadingSpinner size="large" />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('renders without tip when tip is not provided', () => {
    const { container } = render(<LoadingSpinner tip="" />);
    expect(container.querySelector('.loading-text')).toBeNull();
  });

  test('renders with custom icon', () => {
    const customIcon = <LoadingOutlined style={{ color: 'red' }} />;
    render(<LoadingSpinner icon={customIcon} />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('applies correct styling in non-fullscreen mode', () => {
    const { container } = render(<LoadingSpinner fullScreen={false} />);
    expect(container.querySelector('.loading-container')).toBeNull();
    expect(container.firstChild).toHaveStyle('text-align: center');
  });

  test('renders with accessibility attributes', () => {
    render(<LoadingSpinner tip="Loading content" />);
    const loadingElement = screen.getByText('Loading content');
    expect(loadingElement).toBeInTheDocument();
    // The parent should have role="status" for screen readers
    expect(loadingElement.parentElement).toHaveAttribute('role', 'status');
  });
});
