/**
 * Tutorial Customization Interface
 * 
 * Admin interface for customizing tutorial content, steps, and behavior
 * without requiring code changes. Provides visual editor for tutorials.
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Card,
  Tabs,
  Space,
  Table,
  Popconfirm,
  message,
  Drawer,
  ColorPicker,
  Slider,
  Typography,
  Divider,
  Upload,
  Tag
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  EyeOutlined,
  CopyOutlined,
  SettingOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { TUTORIAL_STEP_TYPES, TUTORIAL_CATEGORIES } from './types';
import { TUTORIAL_DEFINITIONS } from './TutorialContent';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

// Tutorial Customizer Component
const TutorialCustomizer = ({ visible, onClose, tutorialId = null }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [tutorials, setTutorials] = useState({});
  const [selectedTutorial, setSelectedTutorial] = useState(null);
  const [steps, setSteps] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);

  // Load tutorials on mount
  useEffect(() => {
    loadTutorials();
  }, []);

  // Load selected tutorial
  useEffect(() => {
    if (tutorialId && tutorials[tutorialId]) {
      setSelectedTutorial(tutorials[tutorialId]);
      setSteps(tutorials[tutorialId].steps || []);
      form.setFieldsValue(tutorials[tutorialId]);
    }
  }, [tutorialId, tutorials, form]);

  const loadTutorials = () => {
    // Load from localStorage or API
    const customTutorials = JSON.parse(
      localStorage.getItem('custom_tutorials') || '{}'
    );
    
    // Merge with default tutorials
    const allTutorials = { ...TUTORIAL_DEFINITIONS, ...customTutorials };
    setTutorials(allTutorials);
  };

  const saveTutorial = async (values) => {
    try {
      const tutorialData = {
        ...values,
        steps,
        id: values.id || `custom_${Date.now()}`,
        lastModified: new Date().toISOString(),
        isCustom: true
      };

      // Save to localStorage (in production, this would be an API call)
      const customTutorials = JSON.parse(
        localStorage.getItem('custom_tutorials') || '{}'
      );
      customTutorials[tutorialData.id] = tutorialData;
      localStorage.setItem('custom_tutorials', JSON.stringify(customTutorials));

      setTutorials(prev => ({ ...prev, [tutorialData.id]: tutorialData }));
      message.success('Tutorial saved successfully');
      
      return tutorialData;
    } catch (error) {
      message.error('Failed to save tutorial');
      throw error;
    }
  };

  const deleteTutorial = async (id) => {
    try {
      const customTutorials = JSON.parse(
        localStorage.getItem('custom_tutorials') || '{}'
      );
      delete customTutorials[id];
      localStorage.setItem('custom_tutorials', JSON.stringify(customTutorials));

      setTutorials(prev => {
        const newTutorials = { ...prev };
        delete newTutorials[id];
        return newTutorials;
      });

      message.success('Tutorial deleted successfully');
    } catch (error) {
      message.error('Failed to delete tutorial');
    }
  };

  const duplicateTutorial = (tutorial) => {
    const newTutorial = {
      ...tutorial,
      id: `${tutorial.id}_copy_${Date.now()}`,
      title: `${tutorial.title} (Copy)`,
      isCustom: true,
      lastModified: new Date().toISOString()
    };

    setTutorials(prev => ({ ...prev, [newTutorial.id]: newTutorial }));
    setSelectedTutorial(newTutorial);
    setSteps(newTutorial.steps || []);
    form.setFieldsValue(newTutorial);
    message.success('Tutorial duplicated successfully');
  };

  const addStep = () => {
    const newStep = {
      id: `step_${Date.now()}`,
      type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
      title: 'New Step',
      content: 'Step description',
      targetSelector: '',
      position: 'bottom',
      showPrevious: true,
      showNext: true,
      showSkip: true
    };

    setSteps(prev => [...prev, newStep]);
  };

  const updateStep = (index, updatedStep) => {
    setSteps(prev => prev.map((step, i) => i === index ? updatedStep : step));
  };

  const deleteStep = (index) => {
    setSteps(prev => prev.filter((_, i) => i !== index));
  };

  const reorderSteps = (result) => {
    if (!result.destination) return;

    const items = Array.from(steps);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSteps(items);
  };

  const exportTutorial = (tutorial) => {
    const dataStr = JSON.stringify(tutorial, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `tutorial_${tutorial.id}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const importTutorial = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const tutorial = JSON.parse(e.target.result);
        tutorial.id = `imported_${Date.now()}`;
        tutorial.isCustom = true;
        tutorial.lastModified = new Date().toISOString();
        
        setTutorials(prev => ({ ...prev, [tutorial.id]: tutorial }));
        message.success('Tutorial imported successfully');
      } catch (error) {
        message.error('Invalid tutorial file');
      }
    };
    reader.readAsText(file);
    return false; // Prevent upload
  };

  // Tutorial List Table
  const tutorialColumns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <Space>
          <Text strong>{text}</Text>
          {record.isCustom && <Tag color="blue">Custom</Tag>}
          {record.isRequired && <Tag color="red">Required</Tag>}
        </Space>
      )
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (category) => <Tag>{category}</Tag>
    },
    {
      title: 'Steps',
      dataIndex: 'steps',
      key: 'steps',
      render: (steps) => steps?.length || 0
    },
    {
      title: 'Duration',
      dataIndex: 'estimatedDuration',
      key: 'duration',
      render: (duration) => `${duration} min`
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            size="small"
            onClick={() => {
              setSelectedTutorial(record);
              setSteps(record.steps || []);
              form.setFieldsValue(record);
            }}
          >
            Edit
          </Button>
          <Button
            icon={<CopyOutlined />}
            size="small"
            onClick={() => duplicateTutorial(record)}
          >
            Duplicate
          </Button>
          <Button
            icon={<EyeOutlined />}
            size="small"
            onClick={() => setPreviewVisible(true)}
          >
            Preview
          </Button>
          <Button
            icon={<ExportOutlined />}
            size="small"
            onClick={() => exportTutorial(record)}
          >
            Export
          </Button>
          {record.isCustom && (
            <Popconfirm
              title="Are you sure you want to delete this tutorial?"
              onConfirm={() => deleteTutorial(record.id)}
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              >
                Delete
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  // Step Editor Component
  const StepEditor = ({ step, index, onChange, onDelete }) => (
    <Card
      size="small"
      title={`Step ${index + 1}: ${step.title}`}
      extra={
        <Button
          icon={<DeleteOutlined />}
          size="small"
          danger
          onClick={() => onDelete(index)}
        />
      }
      style={{ marginBottom: 16 }}
    >
      <Form layout="vertical">
        <Form.Item label="Title">
          <Input
            value={step.title}
            onChange={(e) => onChange(index, { ...step, title: e.target.value })}
          />
        </Form.Item>
        
        <Form.Item label="Content">
          <TextArea
            rows={3}
            value={step.content}
            onChange={(e) => onChange(index, { ...step, content: e.target.value })}
          />
        </Form.Item>
        
        <Form.Item label="Type">
          <Select
            value={step.type}
            onChange={(value) => onChange(index, { ...step, type: value })}
          >
            {Object.values(TUTORIAL_STEP_TYPES).map(type => (
              <Option key={type} value={type}>{type}</Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item label="Target Selector">
          <Input
            value={step.targetSelector}
            onChange={(e) => onChange(index, { ...step, targetSelector: e.target.value })}
            placeholder="[data-tutorial-target='element']"
          />
        </Form.Item>
        
        <Form.Item label="Position">
          <Select
            value={step.position}
            onChange={(value) => onChange(index, { ...step, position: value })}
          >
            <Option value="top">Top</Option>
            <Option value="bottom">Bottom</Option>
            <Option value="left">Left</Option>
            <Option value="right">Right</Option>
          </Select>
        </Form.Item>
        
        <Space>
          <Form.Item label="Show Previous" valuePropName="checked">
            <Switch
              checked={step.showPrevious}
              onChange={(checked) => onChange(index, { ...step, showPrevious: checked })}
            />
          </Form.Item>
          
          <Form.Item label="Show Next" valuePropName="checked">
            <Switch
              checked={step.showNext}
              onChange={(checked) => onChange(index, { ...step, showNext: checked })}
            />
          </Form.Item>
          
          <Form.Item label="Show Skip" valuePropName="checked">
            <Switch
              checked={step.showSkip}
              onChange={(checked) => onChange(index, { ...step, showSkip: checked })}
            />
          </Form.Item>
        </Space>
      </Form>
    </Card>
  );

  return (
    <>
      <Modal
        title="Tutorial Customizer"
        open={visible}
        onCancel={onClose}
        width={1200}
        footer={null}
        destroyOnClose
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Tutorial List" key="list">
            <Space style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  const newTutorial = {
                    id: `custom_${Date.now()}`,
                    title: 'New Tutorial',
                    description: 'Tutorial description',
                    category: TUTORIAL_CATEGORIES.BEGINNER,
                    estimatedDuration: 5,
                    difficulty: 1,
                    steps: [],
                    isCustom: true
                  };
                  setSelectedTutorial(newTutorial);
                  setSteps([]);
                  form.setFieldsValue(newTutorial);
                  setActiveTab('basic');
                }}
              >
                New Tutorial
              </Button>
              
              <Upload
                accept=".json"
                beforeUpload={importTutorial}
                showUploadList={false}
              >
                <Button icon={<ImportOutlined />}>
                  Import Tutorial
                </Button>
              </Upload>
              
              <Button
                icon={<SettingOutlined />}
                onClick={() => setSettingsVisible(true)}
              >
                Settings
              </Button>
            </Space>
            
            <Table
              columns={tutorialColumns}
              dataSource={Object.values(tutorials)}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          {selectedTutorial && (
            <>
              <TabPane tab="Basic Info" key="basic">
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={saveTutorial}
                >
                  <Form.Item
                    name="title"
                    label="Title"
                    rules={[{ required: true, message: 'Please enter tutorial title' }]}
                  >
                    <Input />
                  </Form.Item>
                  
                  <Form.Item
                    name="description"
                    label="Description"
                    rules={[{ required: true, message: 'Please enter description' }]}
                  >
                    <TextArea rows={3} />
                  </Form.Item>
                  
                  <Form.Item
                    name="category"
                    label="Category"
                    rules={[{ required: true, message: 'Please select category' }]}
                  >
                    <Select>
                      {Object.values(TUTORIAL_CATEGORIES).map(category => (
                        <Option key={category} value={category}>{category}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="estimatedDuration"
                    label="Estimated Duration (minutes)"
                  >
                    <Slider min={1} max={60} />
                  </Form.Item>
                  
                  <Form.Item
                    name="difficulty"
                    label="Difficulty (1-5)"
                  >
                    <Slider min={1} max={5} />
                  </Form.Item>
                  
                  <Space>
                    <Form.Item name="isRequired" valuePropName="checked">
                      <Switch /> Required Tutorial
                    </Form.Item>
                    
                    <Form.Item name="isOnboarding" valuePropName="checked">
                      <Switch /> Onboarding Tutorial
                    </Form.Item>
                  </Space>
                  
                  <Form.Item>
                    <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                      Save Tutorial
                    </Button>
                  </Form.Item>
                </Form>
              </TabPane>

              <TabPane tab="Steps" key="steps">
                <Space style={{ marginBottom: 16 }}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={addStep}
                  >
                    Add Step
                  </Button>
                  
                  <Text type="secondary">
                    Drag and drop to reorder steps
                  </Text>
                </Space>
                
                <DragDropContext onDragEnd={reorderSteps}>
                  <Droppable droppableId="steps">
                    {(provided) => (
                      <div {...provided.droppableProps} ref={provided.innerRef}>
                        {steps.map((step, index) => (
                          <Draggable key={step.id} draggableId={step.id} index={index}>
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                              >
                                <StepEditor
                                  step={step}
                                  index={index}
                                  onChange={updateStep}
                                  onDelete={deleteStep}
                                />
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </TabPane>
            </>
          )}
        </Tabs>
      </Modal>

      {/* Settings Drawer */}
      <Drawer
        title="Tutorial Settings"
        placement="right"
        onClose={() => setSettingsVisible(false)}
        open={settingsVisible}
        width={400}
      >
        <Form layout="vertical">
          <Title level={4}>Visual Settings</Title>
          
          <Form.Item label="Highlight Color">
            <ColorPicker />
          </Form.Item>
          
          <Form.Item label="Animation Speed">
            <Select defaultValue="normal">
              <Option value="slow">Slow</Option>
              <Option value="normal">Normal</Option>
              <Option value="fast">Fast</Option>
            </Select>
          </Form.Item>
          
          <Divider />
          
          <Title level={4}>Behavior Settings</Title>
          
          <Form.Item label="Auto-start for new users" valuePropName="checked">
            <Switch />
          </Form.Item>
          
          <Form.Item label="Show progress indicator" valuePropName="checked">
            <Switch defaultChecked />
          </Form.Item>
          
          <Form.Item label="Enable keyboard shortcuts" valuePropName="checked">
            <Switch defaultChecked />
          </Form.Item>
          
          <Form.Item label="Show contextual help" valuePropName="checked">
            <Switch defaultChecked />
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
};

export default TutorialCustomizer;
