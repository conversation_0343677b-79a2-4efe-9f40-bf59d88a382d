<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Direct Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .connection-panel {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 20px;
        }
        .url-input {
            flex-grow: 1;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
        }
        button {
            padding: 8px 16px;
            cursor: pointer;
        }
        .connect-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
        }
        .disconnect-btn {
            background-color: #f44336;
            color: white;
            border: none;
        }
        .status {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .disconnected {
            background-color: #f2dede;
            color: #a94442;
        }
        .connecting {
            background-color: #fcf8e3;
            color: #8a6d3b;
        }
        .message-list {
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 10px;
        }
        .message {
            padding: 5px;
            margin-bottom: 5px;
            border-radius: 4px;
        }
        .system {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .sent {
            background-color: #e8f4f8;
            color: #0275d8;
            text-align: right;
        }
        .received {
            background-color: #f0f4e8;
            color: #5cb85c;
        }
        .error {
            background-color: #f8d7da;
            color: #dc3545;
        }
        .message-input {
            display: flex;
            gap: 10px;
        }
        .message-input input {
            flex-grow: 1;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .url-presets {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .preset-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 4px 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebSocket Direct Test</h1>
    
    <div class="container">
        <div class="connection-panel">
            <div class="url-input">
                <input type="text" id="wsUrl" value="ws://localhost:8000/ws/simple_echo/" placeholder="WebSocket URL">
            </div>
            <button id="connectBtn" class="connect-btn">Connect</button>
            <button id="disconnectBtn" class="disconnect-btn" disabled>Disconnect</button>
        </div>
        
        <div class="url-presets">
            <button class="preset-btn" data-url="ws://localhost:8000/ws/simple_echo/">Simple Echo</button>
            <button class="preset-btn" data-url="ws://localhost:8000/ws/test/">Test</button>
            <button class="preset-btn" data-url="ws://localhost:8000/ws/app_builder/">App Builder</button>
            <button class="preset-btn" data-url="ws://localhost:8000/ws/">Root</button>
            <button class="preset-btn" data-url="ws://localhost:8000/ws/health/">Health</button>
        </div>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="test-buttons">
            <button id="pingBtn" disabled>Send Ping</button>
            <button id="echoBtn" disabled>Send Echo</button>
            <button id="clearBtn">Clear Messages</button>
        </div>
        
        <div id="messageList" class="message-list">
            <div class="message system">WebSocket test initialized. Click Connect to start.</div>
        </div>
        
        <div class="message-input">
            <input type="text" id="messageInput" placeholder="Type a message..." disabled>
            <button id="sendBtn" disabled>Send</button>
        </div>
    </div>

    <script>
        // DOM Elements
        const wsUrlInput = document.getElementById('wsUrl');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const statusDiv = document.getElementById('status');
        const messageList = document.getElementById('messageList');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const pingBtn = document.getElementById('pingBtn');
        const echoBtn = document.getElementById('echoBtn');
        const clearBtn = document.getElementById('clearBtn');
        const presetBtns = document.querySelectorAll('.preset-btn');
        
        // WebSocket instance
        let socket = null;
        
        // URL presets
        presetBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                wsUrlInput.value = btn.dataset.url;
            });
        });
        
        // Connect to WebSocket
        connectBtn.addEventListener('click', () => {
            const url = wsUrlInput.value.trim();
            if (!url) {
                addMessage('error', 'Please enter a WebSocket URL');
                return;
            }
            
            try {
                // Update UI
                statusDiv.className = 'status connecting';
                statusDiv.textContent = 'Connecting...';
                addMessage('system', `Attempting to connect to ${url}...`);
                
                // Create WebSocket
                socket = new WebSocket(url);
                
                // Connection opened
                socket.addEventListener('open', (event) => {
                    statusDiv.className = 'status connected';
                    statusDiv.textContent = 'Connected';
                    addMessage('system', 'Connection established');
                    
                    // Enable UI elements
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    messageInput.disabled = false;
                    sendBtn.disabled = false;
                    pingBtn.disabled = false;
                    echoBtn.disabled = false;
                    wsUrlInput.disabled = true;
                });
                
                // Listen for messages
                socket.addEventListener('message', (event) => {
                    try {
                        // Try to parse as JSON
                        const data = JSON.parse(event.data);
                        addMessage('received', `Received: ${JSON.stringify(data, null, 2)}`);
                    } catch (e) {
                        // Not JSON, treat as plain text
                        addMessage('received', `Received: ${event.data}`);
                    }
                });
                
                // Connection closed
                socket.addEventListener('close', (event) => {
                    statusDiv.className = 'status disconnected';
                    statusDiv.textContent = `Disconnected (Code: ${event.code})`;
                    addMessage('system', `Connection closed: ${event.reason || 'No reason provided'} (Code: ${event.code})`);
                    
                    // Disable UI elements
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    messageInput.disabled = true;
                    sendBtn.disabled = true;
                    pingBtn.disabled = true;
                    echoBtn.disabled = true;
                    wsUrlInput.disabled = false;
                    
                    // Clear socket
                    socket = null;
                });
                
                // Connection error
                socket.addEventListener('error', (event) => {
                    statusDiv.className = 'status disconnected';
                    statusDiv.textContent = 'Error';
                    addMessage('error', 'WebSocket error occurred. Check browser console for details.');
                    console.error('WebSocket error:', event);
                });
            } catch (err) {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = 'Error';
                addMessage('error', `Failed to create WebSocket: ${err.message}`);
                console.error('WebSocket creation error:', err);
            }
        });
        
        // Disconnect from WebSocket
        disconnectBtn.addEventListener('click', () => {
            if (socket) {
                socket.close(1000, 'User initiated disconnect');
            }
        });
        
        // Send message
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        function sendMessage() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage('error', 'WebSocket is not connected');
                return;
            }
            
            const message = messageInput.value.trim();
            if (!message) return;
            
            try {
                // Try to parse as JSON
                let data;
                try {
                    data = JSON.parse(message);
                    socket.send(JSON.stringify(data));
                    addMessage('sent', `Sent: ${JSON.stringify(data, null, 2)}`);
                } catch (e) {
                    // Not valid JSON, send as plain text
                    socket.send(message);
                    addMessage('sent', `Sent: ${message}`);
                }
                
                // Clear input
                messageInput.value = '';
            } catch (err) {
                addMessage('error', `Failed to send message: ${err.message}`);
                console.error('Send error:', err);
            }
        }
        
        // Send ping
        pingBtn.addEventListener('click', () => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage('error', 'WebSocket is not connected');
                return;
            }
            
            try {
                const pingMessage = {
                    type: 'ping',
                    timestamp: new Date().toISOString()
                };
                socket.send(JSON.stringify(pingMessage));
                addMessage('sent', `Sent: ${JSON.stringify(pingMessage, null, 2)}`);
            } catch (err) {
                addMessage('error', `Failed to send ping: ${err.message}`);
                console.error('Ping error:', err);
            }
        });
        
        // Send echo
        echoBtn.addEventListener('click', () => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage('error', 'WebSocket is not connected');
                return;
            }
            
            try {
                const echoMessage = {
                    type: 'echo',
                    message: 'Hello, WebSocket!',
                    timestamp: new Date().toISOString()
                };
                socket.send(JSON.stringify(echoMessage));
                addMessage('sent', `Sent: ${JSON.stringify(echoMessage, null, 2)}`);
            } catch (err) {
                addMessage('error', `Failed to send echo: ${err.message}`);
                console.error('Echo error:', err);
            }
        });
        
        // Clear messages
        clearBtn.addEventListener('click', () => {
            messageList.innerHTML = '';
            addMessage('system', 'Messages cleared');
        });
        
        // Add message to list
        function addMessage(type, text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            messageList.appendChild(messageDiv);
            messageList.scrollTop = messageList.scrollHeight;
        }
    </script>
</body>
</html>
