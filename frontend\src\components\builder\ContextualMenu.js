import React, { useEffect, useRef } from 'react';
import { Menu, Divider } from 'antd';
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  GroupOutlined,
  UngroupOutlined,
  FormatPainterOutlined,
  SettingOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const ContextMenuContainer = styled.div`
  position: fixed;
  z-index: 10000;
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  min-width: 180px;
  overflow: hidden;
  
  .ant-menu {
    border: none;
    box-shadow: none;
  }
  
  .ant-menu-item {
    margin: 0;
    padding: 8px 16px;
    height: auto;
    line-height: 1.4;
    
    &:hover {
      background: #f0f2f5;
    }
    
    &.ant-menu-item-disabled {
      color: #bfbfbf;
      cursor: not-allowed;
      
      &:hover {
        background: transparent;
      }
    }
  }
  
  .ant-menu-item-icon {
    margin-right: 8px;
    font-size: 14px;
  }
`;

const MenuSection = styled.div`
  padding: 4px 0;
  
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
`;

const MenuItemContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

const MenuItemShortcut = styled.span`
  font-size: 11px;
  color: #999;
  margin-left: 16px;
`;

const ContextualMenu = ({
  visible,
  x,
  y,
  onClose,
  selectedComponent,
  selectedComponents = [],
  onCopy,
  onPaste,
  onDelete,
  onEdit,
  onDuplicate,
  onMoveUp,
  onMoveDown,
  onToggleVisibility,
  onToggleLock,
  onGroup,
  onUngroup,
  onCopyStyle,
  onPasteStyle,
  onProperties,
  clipboardHasData = false,
  canMoveUp = true,
  canMoveDown = true,
  canGroup = false,
  canUngroup = false
}) => {
  const menuRef = useRef(null);

  // Position menu and handle viewport boundaries
  useEffect(() => {
    if (visible && menuRef.current) {
      const menu = menuRef.current;
      const rect = menu.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let adjustedX = x;
      let adjustedY = y;

      // Adjust horizontal position if menu would overflow
      if (x + rect.width > viewportWidth) {
        adjustedX = viewportWidth - rect.width - 10;
      }

      // Adjust vertical position if menu would overflow
      if (y + rect.height > viewportHeight) {
        adjustedY = viewportHeight - rect.height - 10;
      }

      menu.style.left = `${Math.max(10, adjustedX)}px`;
      menu.style.top = `${Math.max(10, adjustedY)}px`;
    }
  }, [visible, x, y]);

  // Close menu on escape key
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && visible) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [visible, onClose]);

  if (!visible) return null;

  const isMultipleSelection = selectedComponents.length > 1;
  const hasSelection = selectedComponent || selectedComponents.length > 0;

  const menuItems = [
    // Edit section
    {
      section: 'edit',
      items: [
        {
          key: 'edit',
          icon: <EditOutlined />,
          label: 'Edit Properties',
          shortcut: 'Enter',
          disabled: !hasSelection || isMultipleSelection,
          onClick: () => {
            onEdit?.(selectedComponent);
            onClose();
          }
        },
        {
          key: 'copy',
          icon: <CopyOutlined />,
          label: isMultipleSelection ? `Copy ${selectedComponents.length} Components` : 'Copy',
          shortcut: 'Ctrl+C',
          disabled: !hasSelection,
          onClick: () => {
            onCopy?.(isMultipleSelection ? selectedComponents : selectedComponent);
            onClose();
          }
        },
        {
          key: 'paste',
          icon: <CopyOutlined style={{ transform: 'scaleX(-1)' }} />,
          label: 'Paste',
          shortcut: 'Ctrl+V',
          disabled: !clipboardHasData,
          onClick: () => {
            onPaste?.();
            onClose();
          }
        },
        {
          key: 'duplicate',
          icon: <CopyOutlined />,
          label: isMultipleSelection ? 'Duplicate Selection' : 'Duplicate',
          shortcut: 'Ctrl+D',
          disabled: !hasSelection,
          onClick: () => {
            onDuplicate?.(isMultipleSelection ? selectedComponents : selectedComponent);
            onClose();
          }
        }
      ]
    },
    // Arrange section
    {
      section: 'arrange',
      items: [
        {
          key: 'move-up',
          icon: <ArrowUpOutlined />,
          label: 'Move Up',
          shortcut: 'Ctrl+↑',
          disabled: !hasSelection || !canMoveUp,
          onClick: () => {
            if (isMultipleSelection) {
              selectedComponents.forEach(comp => onMoveUp?.(comp));
            } else {
              onMoveUp?.(selectedComponent);
            }
            onClose();
          }
        },
        {
          key: 'move-down',
          icon: <ArrowDownOutlined />,
          label: 'Move Down',
          shortcut: 'Ctrl+↓',
          disabled: !hasSelection || !canMoveDown,
          onClick: () => {
            if (isMultipleSelection) {
              selectedComponents.forEach(comp => onMoveDown?.(comp));
            } else {
              onMoveDown?.(selectedComponent);
            }
            onClose();
          }
        }
      ]
    },
    // Visibility section
    {
      section: 'visibility',
      items: [
        {
          key: 'toggle-visibility',
          icon: selectedComponent?.visible !== false ? <EyeInvisibleOutlined /> : <EyeOutlined />,
          label: selectedComponent?.visible !== false ? 'Hide' : 'Show',
          shortcut: 'Ctrl+H',
          disabled: !hasSelection,
          onClick: () => {
            if (isMultipleSelection) {
              selectedComponents.forEach(comp => onToggleVisibility?.(comp));
            } else {
              onToggleVisibility?.(selectedComponent);
            }
            onClose();
          }
        },
        {
          key: 'toggle-lock',
          icon: selectedComponent?.locked ? <UnlockOutlined /> : <LockOutlined />,
          label: selectedComponent?.locked ? 'Unlock' : 'Lock',
          shortcut: 'Ctrl+L',
          disabled: !hasSelection,
          onClick: () => {
            if (isMultipleSelection) {
              selectedComponents.forEach(comp => onToggleLock?.(comp));
            } else {
              onToggleLock?.(selectedComponent);
            }
            onClose();
          }
        }
      ]
    },
    // Group section
    {
      section: 'group',
      items: [
        {
          key: 'group',
          icon: <GroupOutlined />,
          label: 'Group',
          shortcut: 'Ctrl+G',
          disabled: !canGroup || selectedComponents.length < 2,
          onClick: () => {
            onGroup?.(selectedComponents);
            onClose();
          }
        },
        {
          key: 'ungroup',
          icon: <UngroupOutlined />,
          label: 'Ungroup',
          shortcut: 'Ctrl+Shift+G',
          disabled: !canUngroup,
          onClick: () => {
            onUngroup?.(selectedComponent);
            onClose();
          }
        }
      ]
    },
    // Style section
    {
      section: 'style',
      items: [
        {
          key: 'copy-style',
          icon: <FormatPainterOutlined />,
          label: 'Copy Style',
          disabled: !hasSelection || isMultipleSelection,
          onClick: () => {
            onCopyStyle?.(selectedComponent);
            onClose();
          }
        },
        {
          key: 'paste-style',
          icon: <FormatPainterOutlined style={{ transform: 'scaleX(-1)' }} />,
          label: 'Paste Style',
          disabled: !hasSelection,
          onClick: () => {
            if (isMultipleSelection) {
              selectedComponents.forEach(comp => onPasteStyle?.(comp));
            } else {
              onPasteStyle?.(selectedComponent);
            }
            onClose();
          }
        }
      ]
    },
    // Actions section
    {
      section: 'actions',
      items: [
        {
          key: 'properties',
          icon: <SettingOutlined />,
          label: 'Properties',
          shortcut: 'F4',
          disabled: !hasSelection || isMultipleSelection,
          onClick: () => {
            onProperties?.(selectedComponent);
            onClose();
          }
        },
        {
          key: 'delete',
          icon: <DeleteOutlined />,
          label: isMultipleSelection ? `Delete ${selectedComponents.length} Components` : 'Delete',
          shortcut: 'Delete',
          disabled: !hasSelection,
          danger: true,
          onClick: () => {
            if (isMultipleSelection) {
              selectedComponents.forEach(comp => onDelete?.(comp));
            } else {
              onDelete?.(selectedComponent);
            }
            onClose();
          }
        }
      ]
    }
  ];

  return (
    <ContextMenuContainer
      ref={menuRef}
      style={{ left: x, top: y }}
      onClick={(e) => e.stopPropagation()}
    >
      <Menu mode="vertical" selectable={false}>
        {menuItems.map((section, sectionIndex) => (
          <MenuSection key={section.section}>
            {section.items.map((item) => (
              <Menu.Item
                key={item.key}
                icon={item.icon}
                disabled={item.disabled}
                danger={item.danger}
                onClick={item.onClick}
              >
                <MenuItemContent>
                  <span>{item.label}</span>
                  {item.shortcut && (
                    <MenuItemShortcut>{item.shortcut}</MenuItemShortcut>
                  )}
                </MenuItemContent>
              </Menu.Item>
            ))}
          </MenuSection>
        ))}
      </Menu>
    </ContextMenuContainer>
  );
};

export default ContextualMenu;
