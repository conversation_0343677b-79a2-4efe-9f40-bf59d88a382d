import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import AppBuilderEnhanced from '../pages/AppBuilderEnhanced';

// Mock the components to avoid rendering them
jest.mock('../components/enhanced/ComponentBuilder', () => () => <div data-testid="component-builder">Component Builder</div>);
jest.mock('../components/enhanced/LayoutDesigner', () => () => <div data-testid="layout-designer">Layout Designer</div>);
jest.mock('../components/enhanced/ThemeManager', () => () => <div data-testid="theme-manager">Theme Manager</div>);
jest.mock('../components/enhanced/FixedWebSocketManager', () => () => <div data-testid="websocket-manager">WebSocket Manager</div>);
jest.mock('../components/enhanced/ProjectManager', () => () => <div data-testid="project-manager">Project Manager</div>);
jest.mock('../components/enhanced/CodeExporter', () => () => <div data-testid="code-exporter">Code Exporter</div>);
jest.mock('../components/enhanced/PerformanceMonitor', () => () => <div data-testid="performance-monitor">Performance Monitor</div>);
jest.mock('../components/enhanced/DataManagementDemo', () => () => <div data-testid="data-management">Data Management</div>);
jest.mock('../components/enhanced/TestingTools', () => () => <div data-testid="testing-tools">Testing Tools</div>);

// Mock the hooks
jest.mock('../hooks/useWebSocket', () => () => ({
  connected: true,
  connecting: false,
  messages: [],
  sendMessage: jest.fn(),
  lastMessage: null,
  readyState: 1,
}));

// Create a mock store
const middlewares = [thunk];
const mockStore = configureStore(middlewares);

describe('AppBuilderEnhanced', () => {
  let store;

  beforeEach(() => {
    // Initialize the store with a known state
    store = mockStore({
      ui: {
        currentView: 'components',
        theme: 'light',
        sidebarCollapsed: false,
      },
    });

    // Mock fetch
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ status: 'ok' }),
      })
    );

    // Mock WebSocket
    global.WebSocket = jest.fn(() => ({
      onopen: null,
      onclose: null,
      onmessage: null,
      onerror: null,
      close: jest.fn(),
      send: jest.fn(),
      readyState: 1,
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', async () => {
    render(
      <Provider store={store}>
        <AppBuilderEnhanced />
      </Provider>
    );

    // Check if loading state is rendered
    expect(screen.getByText('Loading App Builder...')).toBeInTheDocument();

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading App Builder...')).not.toBeInTheDocument();
    }, { timeout: 6000 });
  });

  test('renders the component builder tab by default', async () => {
    render(
      <Provider store={store}>
        <AppBuilderEnhanced />
      </Provider>
    );

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading App Builder...')).not.toBeInTheDocument();
    }, { timeout: 6000 });

    // Check if the component builder tab is rendered
    expect(screen.getByTestId('component-builder')).toBeInTheDocument();
  });

  test('switches tabs when clicked', async () => {
    render(
      <Provider store={store}>
        <AppBuilderEnhanced />
      </Provider>
    );

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading App Builder...')).not.toBeInTheDocument();
    }, { timeout: 6000 });

    // Click on the layout designer tab
    fireEvent.click(screen.getByText('Layout Designer'));

    // Check if the layout designer tab is rendered
    expect(screen.getByTestId('layout-designer')).toBeInTheDocument();

    // Click on the theme manager tab
    fireEvent.click(screen.getByText('Theme Manager'));

    // Check if the theme manager tab is rendered
    expect(screen.getByTestId('theme-manager')).toBeInTheDocument();

    // Click on the websocket manager tab
    fireEvent.click(screen.getByText('WebSocket Manager'));

    // Check if the websocket manager tab is rendered
    expect(screen.getByTestId('websocket-manager')).toBeInTheDocument();

    // Click on the project manager tab
    fireEvent.click(screen.getByText('Project Manager'));

    // Check if the project manager tab is rendered
    expect(screen.getByTestId('project-manager')).toBeInTheDocument();

    // Click on the code exporter tab
    fireEvent.click(screen.getByText('Code Exporter'));

    // Check if the code exporter tab is rendered
    expect(screen.getByTestId('code-exporter')).toBeInTheDocument();

    // Click on the performance monitor tab
    fireEvent.click(screen.getByText('Performance'));

    // Check if the performance monitor tab is rendered
    expect(screen.getByTestId('performance-monitor')).toBeInTheDocument();

    // Click on the data management tab
    fireEvent.click(screen.getByText('Data Management'));

    // Check if the data management tab is rendered
    expect(screen.getByTestId('data-management')).toBeInTheDocument();

    // Click on the testing tools tab
    fireEvent.click(screen.getByText('Testing Tools'));

    // Check if the testing tools tab is rendered
    expect(screen.getByTestId('testing-tools')).toBeInTheDocument();
  });

  test('shows connection status indicators', async () => {
    render(
      <Provider store={store}>
        <AppBuilderEnhanced />
      </Provider>
    );

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading App Builder...')).not.toBeInTheDocument();
    }, { timeout: 6000 });

    // Check if the API connection status indicator is rendered
    expect(screen.getByText('API')).toBeInTheDocument();

    // Check if the WebSocket connection status indicator is rendered
    expect(screen.getByText('WebSocket')).toBeInTheDocument();
  });

  test('shows help button', async () => {
    render(
      <Provider store={store}>
        <AppBuilderEnhanced />
      </Provider>
    );

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading App Builder...')).not.toBeInTheDocument();
    }, { timeout: 6000 });

    // Check if the help button is rendered
    expect(screen.getByText('Help')).toBeInTheDocument();
  });

  test('shows refresh button', async () => {
    render(
      <Provider store={store}>
        <AppBuilderEnhanced />
      </Provider>
    );

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading App Builder...')).not.toBeInTheDocument();
    }, { timeout: 6000 });

    // Check if the refresh button is rendered
    expect(screen.getByText('Refresh')).toBeInTheDocument();
  });
});
