// React Application Verification Script
// This script checks if the React application is loading correctly

console.log('🔍 Starting React application verification...');

// Function to check if React is available
function checkReactAvailability() {
    console.log('📦 Checking React availability...');
    
    if (typeof window.React !== 'undefined') {
        console.log('✅ React is available globally');
        console.log(`📋 React version: ${window.React.version || 'Unknown'}`);
        return true;
    } else {
        console.log('❌ React is not available globally');
        return false;
    }
}

// Function to check if ReactDOM is available
function checkReactDOMAvailability() {
    console.log('📦 Checking ReactDOM availability...');
    
    if (typeof window.ReactDOM !== 'undefined') {
        console.log('✅ ReactDOM is available globally');
        return true;
    } else {
        console.log('❌ ReactDOM is not available globally');
        return false;
    }
}

// Function to check root element
function checkRootElement() {
    console.log('🎯 Checking root element...');
    
    const rootElement = document.getElementById('root');
    if (rootElement) {
        console.log('✅ Root element found');
        
        if (rootElement.children.length > 0) {
            console.log(`✅ Root element has ${rootElement.children.length} child element(s)`);
            console.log(`📋 Root content preview: ${rootElement.innerHTML.substring(0, 200)}...`);
            return true;
        } else {
            console.log('⚠️ Root element is empty');
            return false;
        }
    } else {
        console.log('❌ Root element not found');
        return false;
    }
}

// Function to check if main bundle is loaded
function checkMainBundle() {
    console.log('📦 Checking main bundle...');
    
    const scripts = document.querySelectorAll('script[src*="main"]');
    if (scripts.length > 0) {
        console.log(`✅ Found ${scripts.length} main script(s)`);
        scripts.forEach((script, index) => {
            console.log(`  Script ${index + 1}: ${script.src}`);
        });
        return true;
    } else {
        console.log('❌ No main scripts found');
        return false;
    }
}

// Function to check CSS loading
function checkCSSLoading() {
    console.log('🎨 Checking CSS loading...');
    
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    if (stylesheets.length > 0) {
        console.log(`✅ Found ${stylesheets.length} stylesheet(s)`);
        stylesheets.forEach((link, index) => {
            console.log(`  Stylesheet ${index + 1}: ${link.href}`);
        });
        return true;
    } else {
        console.log('❌ No stylesheets found');
        return false;
    }
}

// Function to check for React components in DOM
function checkReactComponents() {
    console.log('⚛️ Checking for React components...');
    
    // Look for common React component indicators
    const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
    const antdElements = document.querySelectorAll('[class*="ant-"]');
    
    if (reactElements.length > 0) {
        console.log(`✅ Found ${reactElements.length} React element(s)`);
        return true;
    } else if (antdElements.length > 0) {
        console.log(`✅ Found ${antdElements.length} Ant Design element(s) (React components)`);
        return true;
    } else {
        console.log('⚠️ No obvious React components found in DOM');
        return false;
    }
}

// Function to check console for React-related messages
function checkConsoleMessages() {
    console.log('📋 Checking for React-related console messages...');
    
    // This is a simplified check - in a real scenario, you'd capture console messages
    console.log('ℹ️ Check browser console for React initialization messages');
    return true;
}

// Main verification function
function verifyReactApplication() {
    console.log('🚀 Starting comprehensive React application verification...');
    console.log('=' .repeat(60));
    
    const checks = [
        { name: 'React Availability', fn: checkReactAvailability },
        { name: 'ReactDOM Availability', fn: checkReactDOMAvailability },
        { name: 'Root Element', fn: checkRootElement },
        { name: 'Main Bundle', fn: checkMainBundle },
        { name: 'CSS Loading', fn: checkCSSLoading },
        { name: 'React Components', fn: checkReactComponents },
        { name: 'Console Messages', fn: checkConsoleMessages }
    ];
    
    let passedChecks = 0;
    const totalChecks = checks.length;
    
    checks.forEach((check, index) => {
        console.log(`\n${index + 1}. ${check.name}`);
        console.log('-'.repeat(30));
        
        try {
            const result = check.fn();
            if (result) {
                passedChecks++;
            }
        } catch (error) {
            console.log(`❌ Error during ${check.name}: ${error.message}`);
        }
    });
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 VERIFICATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${passedChecks}/${totalChecks} checks`);
    console.log(`❌ Failed: ${totalChecks - passedChecks}/${totalChecks} checks`);
    
    if (passedChecks === totalChecks) {
        console.log('🎉 All checks passed! React application is working correctly.');
    } else if (passedChecks >= totalChecks * 0.7) {
        console.log('⚠️ Most checks passed. React application is mostly working.');
    } else {
        console.log('❌ Many checks failed. React application may have issues.');
    }
    
    return passedChecks / totalChecks;
}

// Run verification when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', verifyReactApplication);
} else {
    // DOM is already ready
    setTimeout(verifyReactApplication, 1000); // Wait a bit for React to initialize
}

// Also expose the verification function globally for manual testing
window.verifyReactApplication = verifyReactApplication;
