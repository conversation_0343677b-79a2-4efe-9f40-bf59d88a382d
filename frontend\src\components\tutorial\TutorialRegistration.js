/**
 * Tutorial Registration Component
 * 
 * Automatically registers all tutorials when the app loads and
 * handles tutorial system initialization.
 */

import { useEffect } from 'react';
import { useTutorial } from './TutorialManager';
import { TUTORIAL_DEFINITIONS } from './TutorialContent';

const TutorialRegistration = () => {
  const { registerTutorial } = useTutorial();

  useEffect(() => {
    // Register all tutorials
    Object.values(TUTORIAL_DEFINITIONS).forEach(tutorial => {
      registerTutorial(tutorial);
    });

    console.log('Tutorial system initialized with', Object.keys(TUTORIAL_DEFINITIONS).length, 'tutorials');
  }, [registerTutorial]);

  // This component doesn't render anything
  return null;
};

export default TutorialRegistration;
