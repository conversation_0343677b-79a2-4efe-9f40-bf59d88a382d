<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Service Worker Test - App Builder 201</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #10b981;
            background: #f0fdf4;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }

        .info {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }

        .code {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }

        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #1d4ed8;
        }

        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #10b981;
            transition: width 0.3s ease;
        }

        .cache-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            font-size: 12px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background: #10b981;
        }

        .status-error {
            background: #ef4444;
        }

        .status-warning {
            background: #f59e0b;
        }

        .status-info {
            background: #3b82f6;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🚀 Advanced Service Worker Test Suite - App Builder 201</h1>

        <div class="test-section">
            <h3>Test Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
            <div id="progress-text">Ready to start testing...</div>
        </div>

        <div class="test-grid">
            <div class="test-section" id="registration-test">
                <h3>🔧 Registration Test</h3>
                <div id="registration-results"></div>
                <button onclick="testRegistration()" id="test-registration-btn">Test Registration</button>
            </div>

            <div class="test-section" id="cache-test">
                <h3>💾 Cache Functionality Test</h3>
                <div id="cache-results"></div>
                <button onclick="testCaching()" id="test-cache-btn">Test Caching</button>
            </div>

            <div class="test-section" id="offline-test">
                <h3>📡 Offline Functionality Test</h3>
                <div id="offline-results"></div>
                <button onclick="testOffline()" id="test-offline-btn">Test Offline</button>
            </div>

            <div class="test-section" id="pwa-test">
                <h3>📱 PWA Installation Test</h3>
                <div id="pwa-results"></div>
                <button onclick="testPWA()" id="test-pwa-btn">Test PWA</button>
            </div>

            <div class="test-section" id="performance-test">
                <h3>⚡ Performance Test</h3>
                <div id="performance-results"></div>
                <button onclick="testPerformance()" id="test-performance-btn">Test Performance</button>
            </div>

            <div class="test-section" id="update-test">
                <h3>🔄 Update Mechanism Test</h3>
                <div id="update-results"></div>
                <button onclick="testUpdates()" id="test-update-btn">Test Updates</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Run All Tests</h3>
            <button onclick="runAllTests()" id="run-all-btn">Run Complete Test Suite</button>
            <button onclick="clearResults()" id="clear-btn">Clear Results</button>
            <button onclick="exportResults()" id="export-btn">Export Results</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="results-summary"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Detailed Cache Inspection</h3>
            <div id="cache-details"></div>
            <button onclick="inspectCaches()" id="inspect-cache-btn">Inspect All Caches</button>
        </div>

        <div id="console-output" class="test-section">
            <h3>📝 Console Output</h3>
            <div class="code" id="console-log"></div>
        </div>
    </div>

    <script>
        let testResults = {
            registration: null,
            cache: null,
            offline: null,
            pwa: null,
            performance: null,
            updates: null
        };

        let consoleOutput = [];
        let currentTestIndex = 0;
        const totalTests = 6;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            consoleOutput.push(logEntry);

            // Update console output display
            const consoleDiv = document.getElementById('console-log');
            consoleDiv.innerHTML = consoleOutput.slice(-30).join('<br>');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;

            // Also log to browser console
            console.log(logEntry);
        }

        function updateProgress(testName, status) {
            if (status === 'complete') {
                currentTestIndex++;
            }

            const progress = (currentTestIndex / totalTests) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
            document.getElementById('progress-text').textContent =
                `${testName} - ${status} (${currentTestIndex}/${totalTests} tests completed)`;
        }

        function setTestResult(testType, status, message, details = '') {
            testResults[testType] = { status, message, details, timestamp: new Date() };

            const resultDiv = document.getElementById(`${testType}-results`);
            const statusClass = status === 'success' ? 'status-success' :
                status === 'error' ? 'status-error' :
                    status === 'warning' ? 'status-warning' : 'status-info';

            resultDiv.innerHTML = `
                <div>
                    <span class="status-indicator ${statusClass}"></span>
                    <strong>${message}</strong>
                </div>
                ${details ? `<div class="code">${details}</div>` : ''}
            `;

            updateResultsSummary();
        }

        function updateResultsSummary() {
            const summary = document.getElementById('results-summary');
            const completed = Object.values(testResults).filter(r => r !== null).length;
            const successful = Object.values(testResults).filter(r => r && r.status === 'success').length;
            const failed = Object.values(testResults).filter(r => r && r.status === 'error').length;

            summary.innerHTML = `
                <div class="info">
                    <strong>Tests Completed:</strong> ${completed}/${totalTests}<br>
                    <strong>Successful:</strong> ${successful}<br>
                    <strong>Failed:</strong> ${failed}<br>
                    <strong>Success Rate:</strong> ${completed > 0 ? Math.round((successful / completed) * 100) : 0}%
                </div>
            `;
        }

        // Test Registration
        async function testRegistration() {
            log('Starting registration test...');
            updateProgress('Registration Test', 'running');

            try {
                if (!('serviceWorker' in navigator)) {
                    throw new Error('Service Worker not supported');
                }

                const registration = await navigator.serviceWorker.register('/service-worker.js');
                const registrations = await navigator.serviceWorker.getRegistrations();

                const details = `
                    Scope: ${registration.scope}<br>
                    Active: ${registration.active ? 'Yes' : 'No'}<br>
                    Total Registrations: ${registrations.length}
                `;

                setTestResult('registration', 'success', 'Service Worker registered successfully', details);
                log('Registration test completed successfully');
                updateProgress('Registration Test', 'complete');

            } catch (error) {
                setTestResult('registration', 'error', 'Registration failed', error.message);
                log(`Registration test failed: ${error.message}`, 'error');
                updateProgress('Registration Test', 'complete');
            }
        }

        // Test Caching
        async function testCaching() {
            log('Starting cache test...');
            updateProgress('Cache Test', 'running');

            try {
                if (!('caches' in window)) {
                    throw new Error('Cache API not supported');
                }

                // Test cache creation and storage
                const testCache = await caches.open('test-cache-' + Date.now());
                const testUrl = '/test-cache-item-' + Date.now();
                const testResponse = new Response('Test cache content', {
                    headers: { 'Content-Type': 'text/plain' }
                });

                await testCache.put(testUrl, testResponse);
                const cachedResponse = await testCache.match(testUrl);

                if (!cachedResponse) {
                    throw new Error('Failed to retrieve cached item');
                }

                const cacheNames = await caches.keys();
                const appCache = cacheNames.find(name => name.includes('app-builder-cache'));

                let appCacheSize = 0;
                if (appCache) {
                    const cache = await caches.open(appCache);
                    const keys = await cache.keys();
                    appCacheSize = keys.length;
                }

                const details = `
                    Cache API: Supported<br>
                    Test Cache: Created and verified<br>
                    App Cache: ${appCache || 'Not found'}<br>
                    App Cache Size: ${appCacheSize} items<br>
                    Total Caches: ${cacheNames.length}
                `;

                setTestResult('cache', 'success', 'Cache functionality working', details);
                log('Cache test completed successfully');
                updateProgress('Cache Test', 'complete');

            } catch (error) {
                setTestResult('cache', 'error', 'Cache test failed', error.message);
                log(`Cache test failed: ${error.message}`, 'error');
                updateProgress('Cache Test', 'complete');
            }
        }

        // Test Offline Functionality
        async function testOffline() {
            log('Starting offline test...');
            updateProgress('Offline Test', 'running');

            try {
                // Test offline page availability
                const offlineResponse = await fetch('/offline.html');
                if (!offlineResponse.ok) {
                    throw new Error('Offline page not accessible');
                }

                // Test API offline fallback
                const apiOfflineResponse = await fetch('/api-offline.json');
                if (!apiOfflineResponse.ok) {
                    throw new Error('API offline fallback not accessible');
                }

                const apiData = await apiOfflineResponse.json();

                // Test service worker fetch interception
                const testResponse = await fetch('/non-existent-resource-' + Date.now());

                const details = `
                    Offline Page: Available<br>
                    API Offline Data: Available (${Object.keys(apiData.data || {}).length} sections)<br>
                    Fetch Interception: ${testResponse.status === 503 ? 'Working' : 'May not be working'}<br>
                    Network Status: ${navigator.onLine ? 'Online' : 'Offline'}
                `;

                setTestResult('offline', 'success', 'Offline functionality ready', details);
                log('Offline test completed successfully');
                updateProgress('Offline Test', 'complete');

            } catch (error) {
                setTestResult('offline', 'error', 'Offline test failed', error.message);
                log(`Offline test failed: ${error.message}`, 'error');
                updateProgress('Offline Test', 'complete');
            }
        }

        // Test PWA Installation
        async function testPWA() {
            log('Starting PWA test...');
            updateProgress('PWA Test', 'running');

            try {
                // Check if app is already installed
                const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                    window.navigator.standalone === true;

                // Check manifest
                const manifestResponse = await fetch('/manifest.json');
                if (!manifestResponse.ok) {
                    throw new Error('Manifest not accessible');
                }

                const manifest = await manifestResponse.json();

                // Check for beforeinstallprompt event support
                let installPromptAvailable = false;
                window.addEventListener('beforeinstallprompt', (e) => {
                    installPromptAvailable = true;
                });

                const details = `
                    Manifest: Available<br>
                    App Name: ${manifest.name || 'Not specified'}<br>
                    Display Mode: ${manifest.display || 'Not specified'}<br>
                    Currently Installed: ${isInstalled ? 'Yes' : 'No'}<br>
                    Install Prompt: ${installPromptAvailable ? 'Available' : 'Not triggered yet'}<br>
                    Icons: ${manifest.icons ? manifest.icons.length : 0} defined
                `;

                setTestResult('pwa', 'success', 'PWA configuration ready', details);
                log('PWA test completed successfully');
                updateProgress('PWA Test', 'complete');

            } catch (error) {
                setTestResult('pwa', 'error', 'PWA test failed', error.message);
                log(`PWA test failed: ${error.message}`, 'error');
                updateProgress('PWA Test', 'complete');
            }
        }

        // Test Performance
        async function testPerformance() {
            log('Starting performance test...');
            updateProgress('Performance Test', 'running');

            try {
                const startTime = performance.now();

                // Test cache hit performance
                const cacheTestStart = performance.now();
                await fetch('/offline.html');
                const cacheTestEnd = performance.now();
                const cacheTime = cacheTestEnd - cacheTestStart;

                // Test service worker response time
                const swTestStart = performance.now();
                await fetch('/manifest.json');
                const swTestEnd = performance.now();
                const swTime = swTestEnd - swTestStart;

                // Check cache sizes
                const cacheNames = await caches.keys();
                let totalCacheSize = 0;
                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const keys = await cache.keys();
                    totalCacheSize += keys.length;
                }

                const endTime = performance.now();
                const totalTime = endTime - startTime;

                const details = `
                    Cache Response Time: ${cacheTime.toFixed(2)}ms<br>
                    Service Worker Response: ${swTime.toFixed(2)}ms<br>
                    Total Cache Items: ${totalCacheSize}<br>
                    Test Duration: ${totalTime.toFixed(2)}ms<br>
                    Performance Rating: ${totalTime < 100 ? 'Excellent' : totalTime < 500 ? 'Good' : 'Needs Improvement'}
                `;

                setTestResult('performance', 'success', 'Performance test completed', details);
                log('Performance test completed successfully');
                updateProgress('Performance Test', 'complete');

            } catch (error) {
                setTestResult('performance', 'error', 'Performance test failed', error.message);
                log(`Performance test failed: ${error.message}`, 'error');
                updateProgress('Performance Test', 'complete');
            }
        }

        // Test Updates
        async function testUpdates() {
            log('Starting update test...');
            updateProgress('Update Test', 'running');

            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (!registration) {
                    throw new Error('No service worker registration found');
                }

                // Trigger update check
                await registration.update();

                // Check for waiting worker
                const hasWaitingWorker = registration.waiting !== null;
                const hasInstallingWorker = registration.installing !== null;

                const details = `
                    Update Check: Triggered<br>
                    Active Worker: ${registration.active ? 'Present' : 'None'}<br>
                    Waiting Worker: ${hasWaitingWorker ? 'Present' : 'None'}<br>
                    Installing Worker: ${hasInstallingWorker ? 'Present' : 'None'}<br>
                    Update Available: ${hasWaitingWorker || hasInstallingWorker ? 'Yes' : 'No'}
                `;

                setTestResult('updates', 'success', 'Update mechanism working', details);
                log('Update test completed successfully');
                updateProgress('Update Test', 'complete');

            } catch (error) {
                setTestResult('updates', 'error', 'Update test failed', error.message);
                log(`Update test failed: ${error.message}`, 'error');
                updateProgress('Update Test', 'complete');
            }
        }

        // Run All Tests
        async function runAllTests() {
            log('Starting complete test suite...');
            currentTestIndex = 0;

            const tests = [
                testRegistration,
                testCaching,
                testOffline,
                testPWA,
                testPerformance,
                testUpdates
            ];

            for (const test of tests) {
                await test();
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
            }

            log('Complete test suite finished');
        }

        // Utility Functions
        function clearResults() {
            testResults = {
                registration: null,
                cache: null,
                offline: null,
                pwa: null,
                performance: null,
                updates: null
            };

            currentTestIndex = 0;
            consoleOutput = [];

            // Clear all result displays
            Object.keys(testResults).forEach(key => {
                document.getElementById(`${key}-results`).innerHTML = '';
            });

            document.getElementById('console-log').innerHTML = '';
            document.getElementById('progress-fill').style.width = '0%';
            document.getElementById('progress-text').textContent = 'Ready to start testing...';

            updateResultsSummary();
            log('Results cleared');
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                tests: testResults
            };

            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `service-worker-test-results-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            log('Results exported');
        }

        async function inspectCaches() {
            log('Inspecting all caches...');

            try {
                const cacheNames = await caches.keys();
                const cacheDetails = document.getElementById('cache-details');

                if (cacheNames.length === 0) {
                    cacheDetails.innerHTML = '<div class="warning">No caches found</div>';
                    return;
                }

                let html = '<div class="info">Found ' + cacheNames.length + ' cache(s):</div>';

                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const keys = await cache.keys();

                    html += `<div class="cache-item">
                        <strong>${cacheName}</strong> (${keys.length} items)<br>
                        <small>Items: ${keys.slice(0, 5).map(req => req.url.split('/').pop()).join(', ')}${keys.length > 5 ? '...' : ''}</small>
                    </div>`;
                }

                cacheDetails.innerHTML = html;
                log('Cache inspection completed');

            } catch (error) {
                document.getElementById('cache-details').innerHTML =
                    `<div class="error">Cache inspection failed: ${error.message}</div>`;
                log(`Cache inspection failed: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('Advanced Service Worker Test Suite loaded');
            updateResultsSummary();
        });
    </script>
</body>

</html>