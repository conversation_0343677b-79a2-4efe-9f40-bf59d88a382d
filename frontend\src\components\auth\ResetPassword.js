import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { resetPassword } from '../../utils/auth';

/**
 * Reset Password Component
 * 
 * This component provides a form for resetting a password.
 */
const ResetPassword = () => {
  const navigate = useNavigate();
  const { token } = useParams();
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  
  // Validate token
  useEffect(() => {
    if (!token) {
      setError('Invalid or missing reset token');
    }
  }, [token]);
  
  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Validate form
  const validateForm = () => {
    // Check if all fields are filled
    if (!formData.password || !formData.confirmPassword) {
      setError('Please fill in all fields');
      return false;
    }
    
    // Check if passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    
    // Check password strength
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }
    
    return true;
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!validateForm()) {
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Reset password
      const result = await resetPassword(token, formData.password);
      
      if (result.success) {
        setSuccess(true);
        
        // Redirect to login page after a delay
        setTimeout(() => {
          navigate('/login', { state: { passwordReset: true } });
        }, 3000);
      } else {
        setError(result.error || 'Password reset failed');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="reset-password-container">
      <div className="reset-password-card">
        <div className="reset-password-header">
          <h2>Reset Password</h2>
          <p>Create a new password for your account.</p>
        </div>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        {success ? (
          <div className="success-message">
            <h3>Password Reset Successful</h3>
            <p>
              Your password has been reset successfully. You will be redirected to the login page shortly.
            </p>
            <div className="success-actions">
              <Link to="/login" className="login-link">
                Go to Login
              </Link>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="reset-password-form">
            <div className="form-group">
              <label htmlFor="password">New Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Enter new password"
                disabled={loading || !token}
                autoComplete="new-password"
                required
              />
              <div className="password-requirements">
                Password must be at least 8 characters long
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="confirmPassword">Confirm Password</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Confirm new password"
                disabled={loading || !token}
                autoComplete="new-password"
                required
              />
            </div>
            
            <button
              type="submit"
              className="reset-button"
              disabled={loading || !token}
            >
              {loading ? 'Resetting Password...' : 'Reset Password'}
            </button>
            
            <div className="form-footer">
              <Link to="/login" className="back-to-login">
                Back to Login
              </Link>
            </div>
          </form>
        )}
      </div>
      
      <style jsx>{`
        .reset-password-container {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          padding: var(--spacing-md);
          background-color: var(--color-background);
        }
        
        .reset-password-card {
          width: 100%;
          max-width: 400px;
          padding: var(--spacing-lg);
          background-color: var(--color-surface);
          border-radius: var(--border-radius-lg);
          box-shadow: var(--shadow-md);
        }
        
        .reset-password-header {
          margin-bottom: var(--spacing-lg);
          text-align: center;
        }
        
        .reset-password-header h2 {
          margin-bottom: var(--spacing-xs);
          color: var(--color-text);
        }
        
        .reset-password-header p {
          color: var(--color-textSecondary);
        }
        
        .error-message {
          padding: var(--spacing-sm);
          margin-bottom: var(--spacing-md);
          background-color: rgba(var(--color-error-rgb), 0.1);
          border: 1px solid var(--color-error);
          border-radius: var(--border-radius-md);
          color: var(--color-error);
          font-size: var(--font-size-sm);
        }
        
        .success-message {
          padding: var(--spacing-md);
          margin-bottom: var(--spacing-md);
          background-color: rgba(var(--color-success-rgb), 0.1);
          border: 1px solid var(--color-success);
          border-radius: var(--border-radius-md);
          color: var(--color-text);
        }
        
        .success-message h3 {
          margin-bottom: var(--spacing-sm);
          color: var(--color-success);
        }
        
        .success-actions {
          margin-top: var(--spacing-md);
          text-align: center;
        }
        
        .reset-password-form {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);
        }
        
        .form-group {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-xs);
        }
        
        .form-group label {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .form-group input {
          padding: var(--spacing-sm) var(--spacing-md);
          border: 1px solid var(--color-border);
          border-radius: var(--border-radius-md);
          background-color: var(--color-background);
          color: var(--color-text);
          font-size: var(--font-size-md);
          transition: border-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .form-group input:focus {
          outline: none;
          border-color: var(--color-primary);
        }
        
        .password-requirements {
          font-size: var(--font-size-xs);
          color: var(--color-textSecondary);
          margin-top: var(--spacing-xs);
        }
        
        .reset-button {
          padding: var(--spacing-sm) var(--spacing-md);
          background-color: var(--color-primary);
          color: white;
          border: none;
          border-radius: var(--border-radius-md);
          font-size: var(--font-size-md);
          font-weight: var(--font-weight-medium);
          cursor: pointer;
          transition: background-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .reset-button:hover {
          background-color: color-mix(in srgb, var(--color-primary) 80%, black);
        }
        
        .reset-button:disabled {
          background-color: var(--color-border);
          color: var(--color-textSecondary);
          cursor: not-allowed;
        }
        
        .form-footer {
          margin-top: var(--spacing-md);
          text-align: center;
        }
        
        .back-to-login, .login-link {
          color: var(--color-primary);
          text-decoration: none;
          font-size: var(--font-size-sm);
        }
        
        .back-to-login:hover, .login-link:hover {
          text-decoration: underline;
        }
      `}</style>
    </div>
  );
};

export default ResetPassword;
