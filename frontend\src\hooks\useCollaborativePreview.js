import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import PreviewWebSocketService from '../services/PreviewWebSocketService';

/**
 * Custom hook for collaborative preview functionality
 * Handles real-time synchronization, cursor tracking, and collaborative editing
 */
const useCollaborativePreview = ({
  sessionId,
  userId,
  username = 'Anonymous',
  avatar = null,
  enableCollaboration = true,
  enableCursorTracking = true,
  enableDeviceSync = true
}) => {
  // State management
  const [collaborators, setCollaborators] = useState(new Map());
  const [cursors, setCursors] = useState(new Map());
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [syncedComponents, setSyncedComponents] = useState(new Map());
  const [deviceState, setDeviceState] = useState({});
  const [conflictResolution, setConflictResolution] = useState(new Map());

  // Refs
  const wsServiceRef = useRef(null);
  const cursorTimeoutRef = useRef(new Map());
  const lastSyncTime = useRef(new Date());

  // Redux state
  const websocketConfig = useSelector(state => state.websocket?.config || {});

  // Initialize WebSocket service
  useEffect(() => {
    if (!enableCollaboration || !sessionId) return;

    const wsService = new PreviewWebSocketService({
      url: websocketConfig.url || 'ws://localhost:8000/ws/collaboration/',
      autoConnect: true,
      reconnectOptions: {
        maxAttempts: 10,
        initialDelay: 1000,
        maxDelay: 30000
      }
    });

    wsServiceRef.current = wsService;

    // Connection event handlers
    wsService.on('connect', () => {
      setIsConnected(true);
      setConnectionStatus('connected');
      
      // Join the collaborative session
      wsService.joinSession(sessionId, { username, avatar });
    });

    wsService.on('disconnect', () => {
      setIsConnected(false);
      setConnectionStatus('disconnected');
    });

    wsService.on('error', (error) => {
      setConnectionStatus('error');
      console.error('Collaborative WebSocket error:', error);
    });

    // Preview event handlers
    setupPreviewEventHandlers(wsService);

    return () => {
      if (wsService) {
        wsService.leaveSession(sessionId);
        wsService.disconnect();
      }
      
      // Clear cursor timeouts
      cursorTimeoutRef.current.forEach(timeout => clearTimeout(timeout));
      cursorTimeoutRef.current.clear();
    };
  }, [sessionId, enableCollaboration, username, avatar, websocketConfig.url]);

  // Setup preview event handlers
  const setupPreviewEventHandlers = useCallback((wsService) => {
    // Collaborator events
    wsService.onPreviewEvent('collaborator_joined', (data) => {
      const { user_id, username: collaboratorName, avatar: collaboratorAvatar } = data;
      setCollaborators(prev => new Map(prev.set(user_id, {
        id: user_id,
        username: collaboratorName,
        avatar: collaboratorAvatar,
        joinedAt: new Date(),
        isActive: true
      })));
    });

    wsService.onPreviewEvent('collaborator_left', (data) => {
      const { user_id } = data;
      setCollaborators(prev => {
        const newMap = new Map(prev);
        newMap.delete(user_id);
        return newMap;
      });
      
      setCursors(prev => {
        const newMap = new Map(prev);
        newMap.delete(user_id);
        return newMap;
      });
    });

    // Cursor tracking events
    if (enableCursorTracking) {
      wsService.onPreviewEvent('cursor_moved', (data) => {
        const { user_id, position } = data;
        if (user_id !== userId) {
          setCursors(prev => new Map(prev.set(user_id, {
            position,
            timestamp: new Date(),
            userId: user_id
          })));

          // Auto-hide cursor after inactivity
          const existingTimeout = cursorTimeoutRef.current.get(user_id);
          if (existingTimeout) {
            clearTimeout(existingTimeout);
          }

          const timeout = setTimeout(() => {
            setCursors(prev => {
              const newMap = new Map(prev);
              newMap.delete(user_id);
              return newMap;
            });
            cursorTimeoutRef.current.delete(user_id);
          }, 5000);

          cursorTimeoutRef.current.set(user_id, timeout);
        }
      });
    }

    // Component synchronization events
    wsService.onPreviewEvent('component_updated', (data) => {
      const { component_id, component_data, user_id: updatedBy } = data;
      if (updatedBy !== userId) {
        setSyncedComponents(prev => new Map(prev.set(component_id, {
          ...component_data,
          lastUpdatedBy: updatedBy,
          lastUpdated: new Date(),
          synced: true
        })));
      }
    });

    wsService.onPreviewEvent('component_added', (data) => {
      const { component, user_id: addedBy } = data;
      if (addedBy !== userId && component?.id) {
        setSyncedComponents(prev => new Map(prev.set(component.id, {
          ...component,
          addedBy,
          synced: true
        })));
      }
    });

    wsService.onPreviewEvent('component_deleted', (data) => {
      const { component_id, user_id: deletedBy } = data;
      if (deletedBy !== userId) {
        setSyncedComponents(prev => {
          const newMap = new Map(prev);
          newMap.delete(component_id);
          return newMap;
        });
      }
    });

    // Device synchronization events
    if (enableDeviceSync) {
      wsService.onPreviewEvent('device_changed', (data) => {
        const { device_type, device_config, user_id: changedBy } = data;
        if (changedBy !== userId) {
          setDeviceState({
            type: device_type,
            config: device_config,
            changedBy,
            timestamp: new Date()
          });
        }
      });
    }

    // Preview state synchronization
    wsService.onPreviewEvent('preview_state_synced', (data) => {
      const { state } = data;
      
      if (state.components) {
        const componentsMap = new Map(state.components);
        setSyncedComponents(componentsMap);
      }
      
      if (state.deviceSettings && enableDeviceSync) {
        setDeviceState(state.deviceSettings);
      }
      
      lastSyncTime.current = new Date();
    });
  }, [userId, enableCursorTracking, enableDeviceSync]);

  // Send component update
  const sendComponentUpdate = useCallback(async (componentId, componentData, immediate = false) => {
    if (!wsServiceRef.current || !isConnected) return false;

    try {
      await wsServiceRef.current.sendComponentUpdate(componentId, componentData, {
        userId,
        sessionId,
        immediate
      });
      return true;
    } catch (error) {
      console.error('Failed to send component update:', error);
      return false;
    }
  }, [isConnected, userId, sessionId]);

  // Send cursor position
  const sendCursorPosition = useCallback(async (position) => {
    if (!wsServiceRef.current || !isConnected || !enableCursorTracking) return;

    try {
      await wsServiceRef.current.sendCursorPosition(position, {
        userId,
        sessionId
      });
    } catch (error) {
      console.error('Failed to send cursor position:', error);
    }
  }, [isConnected, userId, sessionId, enableCursorTracking]);

  // Send device change
  const sendDeviceChange = useCallback(async (deviceType, deviceConfig) => {
    if (!wsServiceRef.current || !isConnected || !enableDeviceSync) return false;

    try {
      await wsServiceRef.current.sendDeviceChange(deviceType, deviceConfig, {
        userId,
        sessionId
      });
      return true;
    } catch (error) {
      console.error('Failed to send device change:', error);
      return false;
    }
  }, [isConnected, userId, sessionId, enableDeviceSync]);

  // Request preview state sync
  const requestSync = useCallback(async () => {
    if (!wsServiceRef.current || !isConnected) return false;

    try {
      await wsServiceRef.current.requestPreviewState(sessionId);
      return true;
    } catch (error) {
      console.error('Failed to request preview state:', error);
      return false;
    }
  }, [isConnected, sessionId]);

  // Resolve component conflicts
  const resolveConflict = useCallback((componentId, resolution) => {
    setConflictResolution(prev => new Map(prev.set(componentId, {
      resolution,
      timestamp: new Date(),
      resolvedBy: userId
    })));
  }, [userId]);

  // Get component with conflict resolution
  const getResolvedComponent = useCallback((componentId, localComponent) => {
    const syncedComponent = syncedComponents.get(componentId);
    const conflict = conflictResolution.get(componentId);

    if (!syncedComponent) return localComponent;
    if (!conflict) {
      // Check for conflicts based on timestamps
      const localTime = new Date(localComponent?.lastUpdated || 0);
      const syncedTime = new Date(syncedComponent?.lastUpdated || 0);
      
      if (Math.abs(localTime - syncedTime) < 1000) {
        // Recent conflict, use last-writer-wins
        return syncedTime > localTime ? syncedComponent : localComponent;
      }
    }

    // Apply conflict resolution
    switch (conflict?.resolution) {
      case 'use_local':
        return localComponent;
      case 'use_remote':
        return syncedComponent;
      case 'merge':
        return { ...localComponent, ...syncedComponent };
      default:
        return syncedComponent; // Default to remote
    }
  }, [syncedComponents, conflictResolution]);

  // Get collaboration status
  const getCollaborationStatus = useCallback(() => {
    return {
      isConnected,
      connectionStatus,
      collaboratorCount: collaborators.size,
      hasActiveCollaborators: collaborators.size > 0,
      lastSyncTime: lastSyncTime.current,
      syncedComponentCount: syncedComponents.size
    };
  }, [isConnected, connectionStatus, collaborators.size, syncedComponents.size]);

  return {
    // Connection state
    isConnected,
    connectionStatus,
    
    // Collaboration data
    collaborators: Array.from(collaborators.values()),
    cursors: Array.from(cursors.values()),
    syncedComponents: Array.from(syncedComponents.entries()),
    deviceState,
    
    // Actions
    sendComponentUpdate,
    sendCursorPosition,
    sendDeviceChange,
    requestSync,
    resolveConflict,
    
    // Utilities
    getResolvedComponent,
    getCollaborationStatus,
    
    // WebSocket service reference
    wsService: wsServiceRef.current
  };
};

export default useCollaborativePreview;
