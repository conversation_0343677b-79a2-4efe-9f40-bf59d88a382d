#!/usr/bin/env python3
"""
Environment Status Summary
Provides a comprehensive status report of the App Builder environment configuration
"""

import subprocess
import json
import sys
import requests
from datetime import datetime

def run_command(command):
    """Run a command and return success status and output"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_container_status():
    """Check Docker container status"""
    print("🐳 Container Status:")
    success, stdout, stderr = run_command('docker-compose ps --format json')
    
    if success:
        try:
            containers = []
            for line in stdout.strip().split('\n'):
                if line.strip():
                    containers.append(json.loads(line))
            
            for container in containers:
                name = container.get('Name', 'Unknown')
                state = container.get('State', 'Unknown')
                health = container.get('Health', 'Unknown')
                
                if state == 'running':
                    if health in ['healthy', 'Unknown']:
                        print(f"  ✅ {name}: {state} ({health})")
                    else:
                        print(f"  ⚠️ {name}: {state} ({health})")
                else:
                    print(f"  ❌ {name}: {state}")
                    
        except json.JSONDecodeError:
            print("  ❌ Could not parse container status")
    else:
        print("  ❌ Could not get container status")

def check_api_endpoints():
    """Check API endpoint accessibility"""
    print("\n🔗 API Endpoint Status:")
    
    endpoints = [
        ("Backend Health", "http://localhost:8000/health/"),
        ("Frontend", "http://localhost:3000/"),
    ]
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {name}: {response.status_code} OK")
            else:
                print(f"  ⚠️ {name}: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"  ❌ {name}: Connection refused")
        except requests.exceptions.Timeout:
            print(f"  ❌ {name}: Timeout")
        except Exception as e:
            print(f"  ❌ {name}: {str(e)}")

def check_environment_variables():
    """Check key environment variables in containers"""
    print("\n🔧 Environment Variables:")
    
    # Backend environment variables
    backend_vars = [
        'USE_POSTGRES',
        'POSTGRES_DB',
        'DJANGO_DEBUG',
        'DJANGO_CORS_ALLOWED_ORIGINS'
    ]
    
    print("  Backend:")
    for var in backend_vars:
        success, stdout, stderr = run_command(
            f'docker-compose exec -T backend printenv {var}'
        )
        if success and stdout.strip():
            print(f"    ✅ {var}: {stdout.strip()}")
        else:
            print(f"    ❌ {var}: Not set")
    
    # Frontend environment variables
    frontend_vars = [
        'REACT_APP_API_URL',
        'REACT_APP_WS_URL',
        'NODE_ENV'
    ]
    
    print("  Frontend:")
    for var in frontend_vars:
        success, stdout, stderr = run_command(
            f'docker-compose exec -T frontend printenv {var}'
        )
        if success and stdout.strip():
            print(f"    ✅ {var}: {stdout.strip()}")
        else:
            print(f"    ❌ {var}: Not set")

def check_database_connection():
    """Check database connectivity"""
    print("\n🗄️ Database Connection:")
    
    success, stdout, stderr = run_command(
        'docker-compose exec -T backend python -c "import os; import socket; sock = socket.socket(); sock.settimeout(5); result = sock.connect_ex((os.environ.get(\'POSTGRES_HOST\', \'db\'), 5432)); sock.close(); print(\'Connected\' if result == 0 else \'Failed\')"'
    )
    
    if success and 'Connected' in stdout:
        print("  ✅ PostgreSQL: Connected")
    else:
        print("  ❌ PostgreSQL: Connection failed")

def check_websocket_config():
    """Check WebSocket configuration"""
    print("\n🔌 WebSocket Configuration:")
    
    # Check WebSocket allowed origins
    success, stdout, stderr = run_command(
        'docker-compose exec -T backend python -c "from django.conf import settings; print(\',\'.join(getattr(settings, \'WEBSOCKET_ALLOWED_ORIGINS\', [])))"'
    )
    
    if success and stdout.strip():
        origins = stdout.strip().split(',')
        print(f"  ✅ Allowed Origins: {len(origins)} configured")
        for origin in origins:
            print(f"    - {origin}")
    else:
        print("  ❌ WebSocket origins: Not configured")

def check_ports():
    """Check port accessibility"""
    print("\n🔌 Port Status:")
    
    ports = [
        ("Backend", 8000),
        ("Frontend", 3000),
        ("Database", 5432)
    ]
    
    for name, port in ports:
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"  ✅ {name} (:{port}): Accessible")
            else:
                print(f"  ❌ {name} (:{port}): Not accessible")
        except Exception as e:
            print(f"  ❌ {name} (:{port}): Error - {e}")

def main():
    """Main status check function"""
    print("🔍 App Builder Environment Status Report")
    print("=" * 50)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all checks
    check_container_status()
    check_ports()
    check_api_endpoints()
    check_environment_variables()
    check_database_connection()
    check_websocket_config()
    
    print("\n✅ Environment status check complete!")
    print("\n💡 Next steps:")
    print("  - If any issues found, check docker-compose.yml configuration")
    print("  - Review logs: docker-compose logs [service]")
    print("  - Restart if needed: docker-compose restart")
    print("  - Full documentation: docs/ENVIRONMENT_CONFIGURATION.md")

if __name__ == "__main__":
    try:
        import requests
    except ImportError:
        print("Installing requests library...")
        subprocess.run([sys.executable, "-m", "pip", "install", "requests"], check=True)
        import requests
    
    main()
