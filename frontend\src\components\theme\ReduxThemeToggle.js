import React from 'react';
import { <PERSON><PERSON>, Tooltip, Switch } from 'antd';
import { BulbOutlined, BulbFilled } from '@ant-design/icons';
import useReduxTheme from '../../hooks/useReduxTheme';

/**
 * A simple theme toggle component that uses Redux for state management
 * @param {Object} props - Component props
 * @param {boolean} props.compact - Whether to show a compact version of the toggle
 * @param {string} props.placement - Tooltip placement
 * @returns {JSX.Element} Theme toggle component
 */
const ReduxThemeToggle = ({ compact = false, placement = 'bottom' }) => {
  const { isDarkMode, toggleDarkMode, activeTheme } = useReduxTheme();
  
  if (compact) {
    return (
      <Tooltip title={`Switch to ${isDarkMode ? 'Light' : 'Dark'} Mode`} placement={placement}>
        <Button
          type="text"
          icon={isDarkMode ? <BulbOutlined /> : <BulbFilled />}
          onClick={toggleDarkMode}
          aria-label={`Switch to ${isDarkMode ? 'Light' : 'Dark'} Mode`}
          style={{
            color: isDarkMode ? '#f0f0f0' : '#1f1f1f',
          }}
        />
      </Tooltip>
    );
  }
  
  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <BulbOutlined style={{ fontSize: '16px' }} />
      <Switch
        checked={isDarkMode}
        onChange={toggleDarkMode}
        checkedChildren="Dark"
        unCheckedChildren="Light"
      />
      <BulbFilled style={{ fontSize: '16px' }} />
      <span style={{ marginLeft: '8px', fontSize: '14px' }}>
        {activeTheme?.name || (isDarkMode ? 'Dark Mode' : 'Light Mode')}
      </span>
    </div>
  );
};

export default ReduxThemeToggle;
