/**
 * Tutorial Accessibility Enhancements
 * 
 * Provides accessibility features for the tutorial system including
 * screen reader support, keyboard navigation, and ARIA attributes.
 */

import React, { useEffect, useRef } from 'react';
import { useTutorial } from './TutorialManager';

// Accessibility utilities
export const announceToScreenReader = (message, priority = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.style.cssText = `
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  `;
  
  document.body.appendChild(announcement);
  announcement.textContent = message;
  
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

// Focus management utilities
export const manageFocus = {
  store: () => {
    const activeElement = document.activeElement;
    return activeElement;
  },
  
  restore: (element) => {
    if (element && element.focus) {
      element.focus();
    }
  },
  
  trapInContainer: (container) => {
    if (!container) return;
    
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    const handleTabKey = (e) => {
      if (e.key !== 'Tab') return;
      
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }
};

// ARIA utilities
export const setAriaAttributes = (element, attributes) => {
  if (!element) return;
  
  Object.entries(attributes).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      element.setAttribute(`aria-${key}`, value);
    } else {
      element.removeAttribute(`aria-${key}`);
    }
  });
};

// Tutorial Accessibility Provider Component
const TutorialAccessibility = () => {
  const {
    isActive,
    activeTutorial,
    currentStep,
    currentStepIndex,
    nextStep,
    previousStep,
    skipTutorial,
    pauseTutorial,
    resumeTutorial,
    isPaused
  } = useTutorial();

  const previousFocusRef = useRef(null);
  const overlayRef = useRef(null);

  // Announce tutorial state changes
  useEffect(() => {
    if (isActive && activeTutorial) {
      announceToScreenReader(
        `Tutorial started: ${activeTutorial.title}. Use arrow keys to navigate, Escape to exit.`,
        'assertive'
      );
    } else if (!isActive && previousFocusRef.current) {
      announceToScreenReader('Tutorial ended.', 'polite');
    }
  }, [isActive, activeTutorial]);

  // Announce step changes
  useEffect(() => {
    if (currentStep && isActive) {
      const stepNumber = currentStepIndex + 1;
      const totalSteps = activeTutorial?.steps.length || 0;
      
      announceToScreenReader(
        `Step ${stepNumber} of ${totalSteps}: ${currentStep.title}. ${currentStep.content}`,
        'polite'
      );
    }
  }, [currentStep, currentStepIndex, activeTutorial, isActive]);

  // Announce pause/resume
  useEffect(() => {
    if (isActive) {
      if (isPaused) {
        announceToScreenReader('Tutorial paused. Press Space to resume.', 'polite');
      } else {
        announceToScreenReader('Tutorial resumed.', 'polite');
      }
    }
  }, [isPaused, isActive]);

  // Focus management
  useEffect(() => {
    if (isActive) {
      // Store current focus
      previousFocusRef.current = document.activeElement;
      
      // Set focus to tutorial overlay
      const overlay = document.querySelector('[data-tutorial-overlay]');
      if (overlay) {
        overlay.focus();
      }
    } else if (previousFocusRef.current) {
      // Restore focus when tutorial ends
      manageFocus.restore(previousFocusRef.current);
      previousFocusRef.current = null;
    }
  }, [isActive]);

  // Keyboard navigation
  useEffect(() => {
    if (!isActive) return;

    const handleKeyDown = (e) => {
      // Prevent default browser behavior for tutorial navigation
      const tutorialKeys = ['ArrowLeft', 'ArrowRight', 'Escape', 'Space', 'Enter'];
      if (tutorialKeys.includes(e.key)) {
        e.preventDefault();
      }

      switch (e.key) {
        case 'ArrowRight':
        case 'Enter':
          nextStep();
          break;
        case 'ArrowLeft':
          previousStep();
          break;
        case 'Escape':
          skipTutorial();
          break;
        case ' ': // Space
          if (isPaused) {
            resumeTutorial();
          } else {
            pauseTutorial();
          }
          break;
        case 'h':
        case 'H':
          // Show help
          announceToScreenReader(
            'Tutorial help: Use arrow keys to navigate steps, Space to pause/resume, Escape to exit.',
            'assertive'
          );
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isActive, isPaused, nextStep, previousStep, skipTutorial, pauseTutorial, resumeTutorial]);

  // Focus trap for tutorial overlay
  useEffect(() => {
    if (!isActive) return;

    const overlay = document.querySelector('[data-tutorial-overlay]');
    if (overlay) {
      const cleanup = manageFocus.trapInContainer(overlay);
      return cleanup;
    }
  }, [isActive, currentStep]);

  // Set ARIA attributes on tutorial elements
  useEffect(() => {
    if (!isActive) return;

    const overlay = document.querySelector('[data-tutorial-overlay]');
    const tooltip = document.querySelector('[data-tutorial-tooltip]');
    const highlight = document.querySelector('[data-tutorial-highlight]');

    if (overlay) {
      setAriaAttributes(overlay, {
        modal: 'true',
        labelledby: 'tutorial-title',
        describedby: 'tutorial-content',
        live: 'polite'
      });
    }

    if (tooltip) {
      setAriaAttributes(tooltip, {
        role: 'dialog',
        labelledby: 'tutorial-step-title',
        describedby: 'tutorial-step-content'
      });
    }

    if (highlight && currentStep?.targetSelector) {
      const targetElement = document.querySelector(currentStep.targetSelector);
      if (targetElement) {
        setAriaAttributes(targetElement, {
          describedby: 'tutorial-step-content',
          expanded: 'true'
        });
      }
    }

    return () => {
      // Cleanup ARIA attributes
      if (currentStep?.targetSelector) {
        const targetElement = document.querySelector(currentStep.targetSelector);
        if (targetElement) {
          setAriaAttributes(targetElement, {
            describedby: null,
            expanded: null
          });
        }
      }
    };
  }, [isActive, currentStep]);

  // Add skip link for screen readers
  useEffect(() => {
    if (!isActive) return;

    const skipLink = document.createElement('a');
    skipLink.href = '#';
    skipLink.textContent = 'Skip tutorial';
    skipLink.className = 'tutorial-skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 100000;
      border-radius: 4px;
    `;
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });
    
    skipLink.addEventListener('click', (e) => {
      e.preventDefault();
      skipTutorial();
    });

    document.body.appendChild(skipLink);

    return () => {
      if (document.body.contains(skipLink)) {
        document.body.removeChild(skipLink);
      }
    };
  }, [isActive, skipTutorial]);

  return null; // This component doesn't render anything visible
};

// High contrast mode detection
export const useHighContrastMode = () => {
  const [isHighContrast, setIsHighContrast] = React.useState(false);

  React.useEffect(() => {
    const checkHighContrast = () => {
      // Check for Windows high contrast mode
      const isWindowsHighContrast = window.matchMedia('(-ms-high-contrast: active)').matches;
      
      // Check for forced colors (modern browsers)
      const isForcedColors = window.matchMedia('(forced-colors: active)').matches;
      
      // Check for prefers-contrast
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      
      setIsHighContrast(isWindowsHighContrast || isForcedColors || prefersHighContrast);
    };

    checkHighContrast();

    // Listen for changes
    const mediaQueries = [
      window.matchMedia('(-ms-high-contrast: active)'),
      window.matchMedia('(forced-colors: active)'),
      window.matchMedia('(prefers-contrast: high)')
    ];

    mediaQueries.forEach(mq => {
      mq.addEventListener('change', checkHighContrast);
    });

    return () => {
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', checkHighContrast);
      });
    };
  }, []);

  return isHighContrast;
};

// Reduced motion detection
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

export default TutorialAccessibility;
