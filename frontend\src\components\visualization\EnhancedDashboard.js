import React, { useState, useEffect, useCallback } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  Select, 
  Spin, 
  Alert, 
  Divider,
  Badge,
  Tooltip,
  Switch
} from 'antd';
import { 
  ReloadOutlined, 
  SettingOutlined, 
  FullscreenOutlined, 
  FullscreenExitOutlined,
  DashboardOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  Bar<PERSON><PERSON>Outlined,
  Pie<PERSON>hartOutlined,
  ApiOutlined,
  CloudOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import DashboardCharts from './DashboardCharts';
import PerformanceChart from './PerformanceChart';
import WebSocketService from '../../services/WebSocketService';
import { generateRandomChartData, calculateStatistics } from '../../utils/chartExport';
import usePreferences from '../../hooks/usePreferences';
import useAccessibility from '../../hooks/useAccessibility';
import { LiveRegion } from '../a11y/LiveRegion';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// Styled components
const DashboardContainer = styled.div`
  padding: 16px;
  background-color: ${props => props.theme === 'dark' ? '#111827' : '#f9fafb'};
  min-height: 100vh;
`;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const StatisticCard = styled(Card)`
  margin-bottom: 24px;
  height: 100%;
  
  .ant-card-body {
    padding: 16px;
  }
  
  .ant-statistic-title {
    font-size: 14px;
    color: ${props => props.theme === 'dark' ? '#9ca3af' : '#6b7280'};
  }
  
  .ant-statistic-content {
    font-size: 24px;
    color: ${props => props.theme === 'dark' ? '#f9fafb' : '#1f2937'};
  }
`;

const ChartCard = styled(Card)`
  margin-bottom: 24px;
  height: 100%;
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => props.status === 'online' ? '#10b981' : '#ef4444'};
  margin-right: 8px;
`;

/**
 * EnhancedDashboard component
 * A comprehensive dashboard with real-time data visualization
 */
const EnhancedDashboard = () => {
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [performanceData, setPerformanceData] = useState([]);
  const [usageData, setUsageData] = useState([]);
  const [errorData, setErrorData] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshInterval, setRefreshInterval] = useState(30);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [connectionStatus, setConnectionStatus] = useState('offline');
  const [announceMessage, setAnnounceMessage] = useState('');
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  
  // Get theme preferences
  const { theme } = usePreferences();
  
  // Get accessibility preferences
  const { highContrast, reducedMotion } = useAccessibility();
  
  // WebSocket service
  const webSocketService = WebSocketService.getInstance();
  
  // Fetch data
  const fetchData = useCallback(() => {
    setLoading(true);
    setError(null);
    
    try {
      // Generate random data for demonstration
      const perfData = generateRandomChartData(30, 10, 200);
      const usageData = generateRandomChartData(30, 20, 100);
      const errData = generateRandomChartData(30, 0, 10);
      
      setPerformanceData(perfData);
      setUsageData(usageData);
      setErrorData(errData);
      setLastUpdated(new Date());
      setAnnounceMessage('Dashboard data updated');
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to fetch dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Initialize data and set up WebSocket connection
  useEffect(() => {
    // Fetch initial data
    fetchData();
    
    // Set up WebSocket connection for real-time updates
    const handleWebSocketMessage = (message) => {
      if (!realTimeEnabled) return;
      
      try {
        const data = JSON.parse(message.data);
        
        if (data.type === 'performance_update') {
          setPerformanceData(prevData => {
            const newData = [...prevData, data.data];
            // Keep only the last 30 data points
            if (newData.length > 30) {
              return newData.slice(newData.length - 30);
            }
            return newData;
          });
        } else if (data.type === 'usage_update') {
          setUsageData(prevData => {
            const newData = [...prevData, data.data];
            if (newData.length > 30) {
              return newData.slice(newData.length - 30);
            }
            return newData;
          });
        } else if (data.type === 'error_update') {
          setErrorData(prevData => {
            const newData = [...prevData, data.data];
            if (newData.length > 30) {
              return newData.slice(newData.length - 30);
            }
            return newData;
          });
        }
        
        setLastUpdated(new Date());
      } catch (err) {
        console.error('Error processing WebSocket message:', err);
      }
    };
    
    const handleWebSocketOpen = () => {
      setConnectionStatus('online');
      setAnnounceMessage('WebSocket connection established');
    };
    
    const handleWebSocketClose = () => {
      setConnectionStatus('offline');
      setAnnounceMessage('WebSocket connection closed');
    };
    
    // Add event listeners
    webSocketService.addEventListener('message', handleWebSocketMessage);
    webSocketService.addEventListener('open', handleWebSocketOpen);
    webSocketService.addEventListener('close', handleWebSocketClose);
    
    // Set connection status
    setConnectionStatus(webSocketService.isConnected() ? 'online' : 'offline');
    
    // Set up refresh interval
    let intervalId;
    if (refreshInterval > 0) {
      intervalId = setInterval(fetchData, refreshInterval * 1000);
    }
    
    // Clean up
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      
      webSocketService.removeEventListener('message', handleWebSocketMessage);
      webSocketService.removeEventListener('open', handleWebSocketOpen);
      webSocketService.removeEventListener('close', handleWebSocketClose);
    };
  }, [fetchData, refreshInterval, webSocketService, realTimeEnabled]);
  
  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };
  
  // Handle fullscreen change
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);
  
  // Calculate statistics
  const perfStats = calculateStatistics(performanceData, 'value');
  const usageStats = calculateStatistics(usageData, 'value');
  const errorStats = calculateStatistics(errorData, 'value');
  
  // Render overview tab
  const renderOverviewTab = () => {
    return (
      <>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <StatisticCard theme={theme}>
              <Statistic
                title="Average Response Time"
                value={perfStats.avg}
                precision={2}
                suffix="ms"
                valueStyle={{ color: perfStats.avg > 100 ? '#ef4444' : '#10b981' }}
                prefix={perfStats.avg > 100 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              />
              <Paragraph type="secondary" style={{ marginTop: 8 }}>
                {perfStats.avg > 100 ? '5% higher than last period' : '10% lower than last period'}
              </Paragraph>
            </StatisticCard>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <StatisticCard theme={theme}>
              <Statistic
                title="CPU Usage"
                value={usageStats.avg}
                precision={2}
                suffix="%"
                valueStyle={{ color: usageStats.avg > 80 ? '#ef4444' : '#10b981' }}
                prefix={usageStats.avg > 80 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              />
              <Paragraph type="secondary" style={{ marginTop: 8 }}>
                {usageStats.avg > 80 ? 'High load detected' : 'Normal load'}
              </Paragraph>
            </StatisticCard>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <StatisticCard theme={theme}>
              <Statistic
                title="Error Rate"
                value={errorStats.avg}
                precision={2}
                suffix="%"
                valueStyle={{ color: errorStats.avg > 5 ? '#ef4444' : '#10b981' }}
                prefix={errorStats.avg > 5 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              />
              <Paragraph type="secondary" style={{ marginTop: 8 }}>
                {errorStats.avg > 5 ? 'Above threshold' : 'Below threshold'}
              </Paragraph>
            </StatisticCard>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <StatisticCard theme={theme}>
              <Statistic
                title="WebSocket Status"
                value={connectionStatus === 'online' ? 'Connected' : 'Disconnected'}
                valueStyle={{ color: connectionStatus === 'online' ? '#10b981' : '#ef4444' }}
                prefix={<CloudOutlined />}
              />
              <StatusIndicator>
                <StatusDot status={connectionStatus} />
                <Text type="secondary">
                  {connectionStatus === 'online' ? 'Online' : 'Offline'}
                </Text>
              </StatusIndicator>
            </StatisticCard>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <DashboardCharts
              data={performanceData}
              loading={loading}
              title="Performance Metrics"
              onRefresh={fetchData}
              chartTypes={['line', 'area']}
            />
          </Col>
          
          <Col xs={24} lg={12}>
            <DashboardCharts
              data={usageData}
              loading={loading}
              title="Resource Usage"
              onRefresh={fetchData}
              chartTypes={['bar', 'pie']}
            />
          </Col>
        </Row>
        
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <DashboardCharts
              data={errorData}
              loading={loading}
              title="Error Metrics"
              onRefresh={fetchData}
              chartTypes={['line', 'scatter']}
            />
          </Col>
        </Row>
      </>
    );
  };
  
  // Render performance tab
  const renderPerformanceTab = () => {
    return (
      <>
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <PerformanceChart
              data={performanceData}
              loading={loading}
              title="Response Time"
              metrics={['latency']}
              thresholds={{ latency: 100 }}
            />
          </Col>
        </Row>
        
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <PerformanceChart
              data={performanceData}
              loading={loading}
              title="Throughput"
              metrics={['throughput']}
              thresholds={{ throughput: 50 }}
            />
          </Col>
          
          <Col xs={24} md={12}>
            <PerformanceChart
              data={errorData}
              loading={loading}
              title="Error Rate"
              metrics={['errors']}
              thresholds={{ errors: 5 }}
            />
          </Col>
        </Row>
      </>
    );
  };
  
  // Render WebSocket tab
  const renderWebSocketTab = () => {
    return (
      <>
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card>
              <StatusIndicator>
                <StatusDot status={connectionStatus} />
                <Text style={{ marginRight: 16 }}>
                  WebSocket Status: <strong>{connectionStatus === 'online' ? 'Connected' : 'Disconnected'}</strong>
                </Text>
                
                <Button
                  type={connectionStatus === 'online' ? 'default' : 'primary'}
                  onClick={() => {
                    if (connectionStatus === 'online') {
                      webSocketService.disconnect();
                    } else {
                      webSocketService.connect();
                    }
                  }}
                >
                  {connectionStatus === 'online' ? 'Disconnect' : 'Connect'}
                </Button>
              </StatusIndicator>
              
              <Divider />
              
              <Paragraph>
                <Text strong>URL:</Text> {webSocketService.getUrl()}
              </Paragraph>
              
              <Paragraph>
                <Text strong>Last Message:</Text> {lastUpdated.toLocaleString()}
              </Paragraph>
              
              <Paragraph>
                <Text strong>Real-time Updates:</Text>{' '}
                <Switch
                  checked={realTimeEnabled}
                  onChange={setRealTimeEnabled}
                />
              </Paragraph>
            </Card>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <DashboardCharts
              data={performanceData}
              loading={loading}
              title="WebSocket Performance"
              onRefresh={fetchData}
              chartTypes={['line', 'area']}
            />
          </Col>
        </Row>
      </>
    );
  };
  
  return (
    <DashboardContainer theme={theme}>
      {/* Accessibility announcement */}
      <LiveRegion id="dashboard-announcer" ariaLive="polite">
        {announceMessage}
      </LiveRegion>
      
      <DashboardHeader>
        <Title level={2}>
          <DashboardOutlined /> Enhanced Dashboard
        </Title>
        
        <Space>
          <Select
            value={refreshInterval}
            onChange={setRefreshInterval}
            style={{ width: 120 }}
          >
            <Option value={0}>No Refresh</Option>
            <Option value={5}>5 Seconds</Option>
            <Option value={10}>10 Seconds</Option>
            <Option value={30}>30 Seconds</Option>
            <Option value={60}>1 Minute</Option>
          </Select>
          
          <Tooltip title="Refresh Data">
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchData}
              loading={loading}
            />
          </Tooltip>
          
          <Tooltip title="Dashboard Settings">
            <Button icon={<SettingOutlined />} />
          </Tooltip>
          
          <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}>
            <Button 
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
              onClick={toggleFullscreen}
            />
          </Tooltip>
        </Space>
      </DashboardHeader>
      
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={<span><DashboardOutlined /> Overview</span>} 
          key="overview"
        >
          {renderOverviewTab()}
        </TabPane>
        
        <TabPane 
          tab={<span><LineChartOutlined /> Performance</span>} 
          key="performance"
        >
          {renderPerformanceTab()}
        </TabPane>
        
        <TabPane 
          tab={<span><ApiOutlined /> WebSocket</span>} 
          key="websocket"
        >
          {renderWebSocketTab()}
        </TabPane>
      </Tabs>
      
      <div style={{ textAlign: 'center', marginTop: 24 }}>
        <Text type="secondary">
          Last updated: {lastUpdated.toLocaleString()}
        </Text>
      </div>
    </DashboardContainer>
  );
};

export default EnhancedDashboard;
