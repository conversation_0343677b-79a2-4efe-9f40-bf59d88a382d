/**
 * Advanced Tutorial Features
 * 
 * Provides advanced tutorial functionality including branching paths,
 * conditional steps, user customization, and adaptive learning.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useTutorial } from './TutorialManager';
import tutorialStorage from './TutorialStorage';
import { TUTORIAL_STATUS } from './types';

// Advanced Tutorial Types
export const ADVANCED_STEP_TYPES = {
  CONDITIONAL: 'conditional',
  BRANCH: 'branch',
  ADAPTIVE: 'adaptive',
  PERSONALIZED: 'personalized',
  ASSESSMENT: 'assessment',
  CHECKPOINT: 'checkpoint'
};

export const CONDITION_TYPES = {
  USER_SKILL_LEVEL: 'user_skill_level',
  PREVIOUS_COMPLETION: 'previous_completion',
  USER_PREFERENCE: 'user_preference',
  PERFORMANCE_METRIC: 'performance_metric',
  TIME_SPENT: 'time_spent',
  ERROR_COUNT: 'error_count',
  CUSTOM_FUNCTION: 'custom_function'
};

// Advanced Tutorial Step Creator
export const createAdvancedTutorialStep = ({
  id,
  type,
  title,
  content,
  conditions = [],
  branches = [],
  adaptiveContent = null,
  personalizationRules = [],
  assessmentQuestions = [],
  checkpointData = null,
  onConditionMet = null,
  onConditionFailed = null,
  ...baseStepProps
}) => ({
  id,
  type,
  title,
  content,
  conditions,
  branches,
  adaptiveContent,
  personalizationRules,
  assessmentQuestions,
  checkpointData,
  onConditionMet,
  onConditionFailed,
  ...baseStepProps,
  isAdvanced: true
});

// Condition Evaluator
export class ConditionEvaluator {
  constructor(userProfile, tutorialProgress, performanceMetrics) {
    this.userProfile = userProfile;
    this.tutorialProgress = tutorialProgress;
    this.performanceMetrics = performanceMetrics;
  }

  evaluateCondition(condition) {
    switch (condition.type) {
      case CONDITION_TYPES.USER_SKILL_LEVEL:
        return this.evaluateSkillLevel(condition);
      
      case CONDITION_TYPES.PREVIOUS_COMPLETION:
        return this.evaluatePreviousCompletion(condition);
      
      case CONDITION_TYPES.USER_PREFERENCE:
        return this.evaluateUserPreference(condition);
      
      case CONDITION_TYPES.PERFORMANCE_METRIC:
        return this.evaluatePerformanceMetric(condition);
      
      case CONDITION_TYPES.TIME_SPENT:
        return this.evaluateTimeSpent(condition);
      
      case CONDITION_TYPES.ERROR_COUNT:
        return this.evaluateErrorCount(condition);
      
      case CONDITION_TYPES.CUSTOM_FUNCTION:
        return this.evaluateCustomFunction(condition);
      
      default:
        return false;
    }
  }

  evaluateSkillLevel(condition) {
    const userSkillLevel = this.userProfile.skillLevel || 'beginner';
    return condition.expectedLevel === userSkillLevel ||
           (condition.minLevel && this.getSkillLevelValue(userSkillLevel) >= this.getSkillLevelValue(condition.minLevel));
  }

  evaluatePreviousCompletion(condition) {
    const tutorialId = condition.tutorialId;
    const progress = this.tutorialProgress[tutorialId];
    return progress && progress.status === TUTORIAL_STATUS.COMPLETED;
  }

  evaluateUserPreference(condition) {
    const preferences = this.userProfile.preferences || {};
    return preferences[condition.preferenceKey] === condition.expectedValue;
  }

  evaluatePerformanceMetric(condition) {
    const metric = this.performanceMetrics[condition.metricName];
    if (!metric) return false;
    
    switch (condition.operator) {
      case 'gt': return metric > condition.value;
      case 'gte': return metric >= condition.value;
      case 'lt': return metric < condition.value;
      case 'lte': return metric <= condition.value;
      case 'eq': return metric === condition.value;
      default: return false;
    }
  }

  evaluateTimeSpent(condition) {
    const timeSpent = this.performanceMetrics.timeSpent || 0;
    return timeSpent >= condition.minTime && timeSpent <= condition.maxTime;
  }

  evaluateErrorCount(condition) {
    const errorCount = this.performanceMetrics.errorCount || 0;
    return errorCount <= condition.maxErrors;
  }

  evaluateCustomFunction(condition) {
    if (typeof condition.evaluator === 'function') {
      return condition.evaluator(this.userProfile, this.tutorialProgress, this.performanceMetrics);
    }
    return false;
  }

  getSkillLevelValue(level) {
    const levels = { beginner: 1, intermediate: 2, advanced: 3, expert: 4 };
    return levels[level] || 1;
  }
}

// Branching Logic Manager
export class BranchingManager {
  constructor(conditionEvaluator) {
    this.conditionEvaluator = conditionEvaluator;
  }

  evaluateBranches(branches) {
    for (const branch of branches) {
      if (this.evaluateBranchConditions(branch.conditions)) {
        return branch;
      }
    }
    return null; // No branch matched
  }

  evaluateBranchConditions(conditions) {
    if (!conditions || conditions.length === 0) return true;
    
    return conditions.every(condition => 
      this.conditionEvaluator.evaluateCondition(condition)
    );
  }

  getNextStepFromBranch(branch, currentStepIndex) {
    if (branch.nextStepId) {
      return branch.nextStepId;
    }
    
    if (branch.skipSteps) {
      return currentStepIndex + branch.skipSteps + 1;
    }
    
    return currentStepIndex + 1;
  }
}

// Adaptive Content Manager
export class AdaptiveContentManager {
  constructor(userProfile, performanceMetrics) {
    this.userProfile = userProfile;
    this.performanceMetrics = performanceMetrics;
  }

  getAdaptiveContent(step) {
    if (!step.adaptiveContent) return step.content;
    
    const adaptiveRules = step.adaptiveContent.rules || [];
    
    for (const rule of adaptiveRules) {
      if (this.evaluateAdaptiveRule(rule)) {
        return rule.content;
      }
    }
    
    return step.adaptiveContent.defaultContent || step.content;
  }

  evaluateAdaptiveRule(rule) {
    switch (rule.type) {
      case 'skill_based':
        return this.userProfile.skillLevel === rule.skillLevel;
      
      case 'performance_based':
        const metric = this.performanceMetrics[rule.metricName];
        return metric && this.compareMetric(metric, rule.operator, rule.value);
      
      case 'preference_based':
        const preference = this.userProfile.preferences?.[rule.preferenceKey];
        return preference === rule.expectedValue;
      
      case 'time_based':
        const timeSpent = this.performanceMetrics.timeSpent || 0;
        return timeSpent >= rule.minTime;
      
      default:
        return false;
    }
  }

  compareMetric(metric, operator, value) {
    switch (operator) {
      case 'gt': return metric > value;
      case 'gte': return metric >= value;
      case 'lt': return metric < value;
      case 'lte': return metric <= value;
      case 'eq': return metric === value;
      default: return false;
    }
  }
}

// Personalization Engine
export class PersonalizationEngine {
  constructor(userId) {
    this.userId = userId;
    this.userProfile = this.loadUserProfile();
    this.learningStyle = this.detectLearningStyle();
  }

  loadUserProfile() {
    const profile = tutorialStorage.getItem(`user_profile_${this.userId}`, {
      skillLevel: 'beginner',
      preferences: {},
      learningHistory: [],
      completedTutorials: [],
      strugglingAreas: [],
      strengths: []
    });
    
    return profile;
  }

  saveUserProfile() {
    tutorialStorage.setItem(`user_profile_${this.userId}`, this.userProfile);
  }

  detectLearningStyle() {
    const history = this.userProfile.learningHistory || [];
    
    // Analyze learning patterns
    const visualSteps = history.filter(h => h.stepType === 'visual').length;
    const textSteps = history.filter(h => h.stepType === 'text').length;
    const interactiveSteps = history.filter(h => h.stepType === 'interactive').length;
    
    if (visualSteps > textSteps && visualSteps > interactiveSteps) {
      return 'visual';
    } else if (interactiveSteps > textSteps && interactiveSteps > visualSteps) {
      return 'kinesthetic';
    } else {
      return 'textual';
    }
  }

  personalizeStep(step) {
    const personalizedStep = { ...step };
    
    // Adapt content based on learning style
    if (this.learningStyle === 'visual' && step.visualContent) {
      personalizedStep.content = step.visualContent;
    } else if (this.learningStyle === 'kinesthetic' && step.interactiveContent) {
      personalizedStep.content = step.interactiveContent;
    }
    
    // Adjust difficulty based on skill level
    if (this.userProfile.skillLevel === 'beginner' && step.beginnerContent) {
      personalizedStep.content = step.beginnerContent;
    } else if (this.userProfile.skillLevel === 'advanced' && step.advancedContent) {
      personalizedStep.content = step.advancedContent;
    }
    
    // Add personalized hints based on struggling areas
    if (this.userProfile.strugglingAreas.includes(step.topic)) {
      personalizedStep.hints = step.extraHints || [];
    }
    
    return personalizedStep;
  }

  updateLearningHistory(step, performance) {
    this.userProfile.learningHistory.push({
      stepId: step.id,
      stepType: step.type,
      topic: step.topic,
      timeSpent: performance.timeSpent,
      errorCount: performance.errorCount,
      completed: performance.completed,
      timestamp: new Date().toISOString()
    });
    
    // Update struggling areas and strengths
    if (performance.errorCount > 2 || performance.timeSpent > step.expectedTime * 2) {
      if (!this.userProfile.strugglingAreas.includes(step.topic)) {
        this.userProfile.strugglingAreas.push(step.topic);
      }
    } else if (performance.errorCount === 0 && performance.timeSpent < step.expectedTime) {
      if (!this.userProfile.strengths.includes(step.topic)) {
        this.userProfile.strengths.push(step.topic);
      }
    }
    
    this.saveUserProfile();
  }
}

// Assessment System
export class AssessmentSystem {
  constructor() {
    this.assessmentResults = new Map();
  }

  createAssessmentStep(questions, passingScore = 70) {
    return createAdvancedTutorialStep({
      id: `assessment_${Date.now()}`,
      type: ADVANCED_STEP_TYPES.ASSESSMENT,
      title: 'Knowledge Check',
      content: 'Test your understanding with these questions',
      assessmentQuestions: questions,
      passingScore,
      onComplete: (results) => this.handleAssessmentComplete(results)
    });
  }

  handleAssessmentComplete(results) {
    const score = this.calculateScore(results);
    this.assessmentResults.set(results.stepId, {
      score,
      answers: results.answers,
      passed: score >= results.passingScore,
      timestamp: new Date().toISOString()
    });
    
    return {
      score,
      passed: score >= results.passingScore,
      feedback: this.generateFeedback(score, results.passingScore)
    };
  }

  calculateScore(results) {
    const correctAnswers = results.answers.filter(answer => answer.correct).length;
    return (correctAnswers / results.answers.length) * 100;
  }

  generateFeedback(score, passingScore) {
    if (score >= passingScore) {
      return {
        type: 'success',
        message: 'Great job! You have a solid understanding of the material.',
        suggestions: []
      };
    } else {
      return {
        type: 'improvement',
        message: 'You might want to review some concepts before continuing.',
        suggestions: [
          'Review the previous tutorial steps',
          'Practice with the interactive examples',
          'Ask for help if you need clarification'
        ]
      };
    }
  }
}

// Advanced Tutorial Hook
export const useAdvancedTutorial = (userId) => {
  const tutorial = useTutorial();
  const [personalizationEngine] = useState(() => new PersonalizationEngine(userId));
  const [assessmentSystem] = useState(() => new AssessmentSystem());
  
  const [userProfile, setUserProfile] = useState(personalizationEngine.userProfile);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    timeSpent: 0,
    errorCount: 0,
    completionRate: 0
  });

  const conditionEvaluator = new ConditionEvaluator(
    userProfile,
    tutorial.progress,
    performanceMetrics
  );
  
  const branchingManager = new BranchingManager(conditionEvaluator);
  const adaptiveContentManager = new AdaptiveContentManager(userProfile, performanceMetrics);

  const processAdvancedStep = useCallback((step) => {
    if (!step.isAdvanced) return step;
    
    let processedStep = { ...step };
    
    // Apply personalization
    processedStep = personalizationEngine.personalizeStep(processedStep);
    
    // Apply adaptive content
    processedStep.content = adaptiveContentManager.getAdaptiveContent(processedStep);
    
    // Handle branching
    if (step.type === ADVANCED_STEP_TYPES.BRANCH && step.branches) {
      const selectedBranch = branchingManager.evaluateBranches(step.branches);
      if (selectedBranch) {
        processedStep.nextStepOverride = branchingManager.getNextStepFromBranch(
          selectedBranch,
          tutorial.currentStepIndex
        );
      }
    }
    
    return processedStep;
  }, [personalizationEngine, adaptiveContentManager, branchingManager, tutorial.currentStepIndex]);

  const updatePerformanceMetrics = useCallback((metrics) => {
    setPerformanceMetrics(prev => ({ ...prev, ...metrics }));
  }, []);

  const updateUserProfile = useCallback((updates) => {
    const updatedProfile = { ...userProfile, ...updates };
    setUserProfile(updatedProfile);
    personalizationEngine.userProfile = updatedProfile;
    personalizationEngine.saveUserProfile();
  }, [userProfile, personalizationEngine]);

  return {
    ...tutorial,
    processAdvancedStep,
    updatePerformanceMetrics,
    updateUserProfile,
    userProfile,
    performanceMetrics,
    personalizationEngine,
    assessmentSystem,
    conditionEvaluator,
    branchingManager,
    adaptiveContentManager
  };
};

export default {
  ADVANCED_STEP_TYPES,
  CONDITION_TYPES,
  createAdvancedTutorialStep,
  ConditionEvaluator,
  BranchingManager,
  AdaptiveContentManager,
  PersonalizationEngine,
  AssessmentSystem,
  useAdvancedTutorial
};
