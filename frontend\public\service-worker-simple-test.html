<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .cache-item {
            background: #e9ecef;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Service Worker Simple Test</h1>
        <p>This page tests basic service worker functionality without complex frameworks.</p>
        
        <div id="status"></div>
        
        <div>
            <h3>Actions</h3>
            <button onclick="registerSW()">Register Service Worker</button>
            <button onclick="unregisterSW()">Unregister Service Worker</button>
            <button onclick="checkCaches()">Check Caches</button>
            <button onclick="clearCaches()">Clear All Caches</button>
            <button onclick="testOffline()">Test Offline</button>
            <button onclick="testNotification()">Test Notification</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div>
            <h3>Cache Contents</h3>
            <div id="caches"></div>
        </div>
        
        <div>
            <h3>Test Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        let cachesElement = document.getElementById('caches');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#6c757d';
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[SW Test] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            logElement.innerHTML = '';
        }

        async function registerSW() {
            if ('serviceWorker' in navigator) {
                try {
                    log('Attempting to register service worker...');
                    const registration = await navigator.serviceWorker.register('/service-worker.js');
                    log(`Service worker registered successfully: ${registration.scope}`, 'success');
                    updateStatus('✅ Service Worker registered successfully', 'success');
                    
                    // Listen for updates
                    registration.addEventListener('updatefound', () => {
                        log('Service worker update found');
                    });
                    
                    checkSWStatus();
                } catch (error) {
                    log(`Service worker registration failed: ${error.message}`, 'error');
                    updateStatus('❌ Service Worker registration failed', 'error');
                }
            } else {
                log('Service workers not supported', 'error');
                updateStatus('❌ Service Workers not supported in this browser', 'error');
            }
        }

        async function unregisterSW() {
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (const registration of registrations) {
                        await registration.unregister();
                        log(`Unregistered service worker: ${registration.scope}`, 'success');
                    }
                    updateStatus('✅ All service workers unregistered', 'success');
                    checkSWStatus();
                } catch (error) {
                    log(`Failed to unregister service workers: ${error.message}`, 'error');
                    updateStatus('❌ Failed to unregister service workers', 'error');
                }
            }
        }

        async function checkSWStatus() {
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    const registration = await navigator.serviceWorker.getRegistration();
                    
                    log(`Found ${registrations.length} service worker registrations`);
                    
                    if (registration) {
                        log(`Active: ${registration.active ? 'Yes' : 'No'}`);
                        log(`Installing: ${registration.installing ? 'Yes' : 'No'}`);
                        log(`Waiting: ${registration.waiting ? 'Yes' : 'No'}`);
                        log(`Scope: ${registration.scope}`);
                    }
                } catch (error) {
                    log(`Error checking service worker status: ${error.message}`, 'error');
                }
            }
        }

        async function checkCaches() {
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    log(`Found ${cacheNames.length} caches`);
                    
                    cachesElement.innerHTML = '';
                    
                    for (const cacheName of cacheNames) {
                        const cache = await caches.open(cacheName);
                        const keys = await cache.keys();
                        
                        const cacheDiv = document.createElement('div');
                        cacheDiv.className = 'cache-item';
                        cacheDiv.innerHTML = `
                            <strong>${cacheName}</strong> (${keys.length} items)<br>
                            ${keys.slice(0, 3).map(req => `• ${req.url}`).join('<br>')}
                            ${keys.length > 3 ? `<br>... and ${keys.length - 3} more` : ''}
                        `;
                        cachesElement.appendChild(cacheDiv);
                        
                        log(`Cache "${cacheName}": ${keys.length} items`);
                    }
                    
                    if (cacheNames.length === 0) {
                        cachesElement.innerHTML = '<div class="cache-item">No caches found</div>';
                    }
                } catch (error) {
                    log(`Error checking caches: ${error.message}`, 'error');
                }
            } else {
                log('Cache API not supported', 'error');
            }
        }

        async function clearCaches() {
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    for (const cacheName of cacheNames) {
                        await caches.delete(cacheName);
                        log(`Deleted cache: ${cacheName}`, 'success');
                    }
                    updateStatus('✅ All caches cleared', 'success');
                    checkCaches();
                } catch (error) {
                    log(`Error clearing caches: ${error.message}`, 'error');
                    updateStatus('❌ Failed to clear caches', 'error');
                }
            }
        }

        async function testOffline() {
            try {
                log('Testing offline capability...');
                
                // Test if the current page is cached
                const response = await fetch(window.location.href, { 
                    cache: 'only-if-cached', 
                    mode: 'same-origin' 
                });
                
                if (response.ok) {
                    log('✅ Current page is available offline', 'success');
                    updateStatus('✅ App appears to work offline', 'success');
                } else {
                    log('⚠️ Current page may not be available offline', 'warning');
                    updateStatus('⚠️ App may not work offline', 'warning');
                }
            } catch (error) {
                log(`Offline test failed: ${error.message}`, 'error');
                updateStatus('❌ Offline test failed', 'error');
            }
        }

        async function testNotification() {
            if ('Notification' in window) {
                try {
                    const permission = await Notification.requestPermission();
                    log(`Notification permission: ${permission}`);
                    
                    if (permission === 'granted') {
                        new Notification('Test Notification', {
                            body: 'This is a test notification from the service worker test page',
                            icon: '/favicon.ico'
                        });
                        log('✅ Test notification sent', 'success');
                        updateStatus('✅ Notification test successful', 'success');
                    } else {
                        log('⚠️ Notification permission denied', 'warning');
                        updateStatus('⚠️ Notification permission denied', 'warning');
                    }
                } catch (error) {
                    log(`Notification test failed: ${error.message}`, 'error');
                    updateStatus('❌ Notification test failed', 'error');
                }
            } else {
                log('Notifications not supported', 'error');
                updateStatus('❌ Notifications not supported', 'error');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('Page loaded, checking service worker status...');
            checkSWStatus();
            checkCaches();
            
            // Check if we're online/offline
            log(`Network status: ${navigator.onLine ? 'Online' : 'Offline'}`);
            
            // Listen for online/offline events
            window.addEventListener('online', () => {
                log('Network status changed: Online', 'success');
                updateStatus('🌐 Back online', 'success');
            });
            
            window.addEventListener('offline', () => {
                log('Network status changed: Offline', 'warning');
                updateStatus('📴 Gone offline', 'warning');
            });
        });
    </script>
</body>
</html>
