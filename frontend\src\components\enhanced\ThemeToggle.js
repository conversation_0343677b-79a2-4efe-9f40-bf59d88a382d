import React, { useState } from 'react';
import { Button, Dropdown, <PERSON>u, Tooltip, Badge } from 'antd';
import { 
  BgColorsOutlined, 
  CheckOutlined, 
  DownOutlined,
  BulbOutlined,
  BulbFilled
} from '@ant-design/icons';
import { useEnhancedTheme } from './EnhancedThemeProvider';
import { styled } from '../../design-system';

const ThemeButtonContainer = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 8px;
`;

const ColorSwatch = styled.div`
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
`;

/**
 * ThemeToggle Component
 * 
 * A component that allows users to toggle between different themes
 * and theme modes (light, dark, blue, high-contrast).
 */
const ThemeToggle = ({ showModeToggle = true, showThemeSelector = true, compact = false }) => {
  const { 
    currentTheme, 
    setThemeById, 
    availableThemes, 
    themeMode, 
    toggleThemeMode, 
    setThemeMode,
    availableThemeModes
  } = useEnhancedTheme();
  
  const [dropdownVisible, setDropdownVisible] = useState(false);
  
  // Handle theme selection
  const handleThemeSelect = ({ key }) => {
    setThemeById(key);
    setDropdownVisible(false);
  };
  
  // Handle theme mode selection
  const handleThemeModeSelect = ({ key }) => {
    setThemeMode(key);
    setDropdownVisible(false);
  };
  
  // Get icon for current theme mode
  const getThemeModeIcon = () => {
    switch (themeMode) {
      case 'dark':
        return <BulbFilled style={{ color: '#177ddc' }} />;
      case 'blue':
        return <BulbOutlined style={{ color: '#0050b3' }} />;
      case 'high-contrast':
        return <BulbFilled style={{ color: '#000000' }} />;
      case 'light':
      default:
        return <BulbOutlined style={{ color: '#faad14' }} />;
    }
  };
  
  // Create theme menu items
  const themeMenuItems = availableThemes.map(theme => ({
    key: theme.id,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ColorSwatch style={{ backgroundColor: theme.primaryColor }} />
          <span>{theme.name}</span>
        </div>
        {currentTheme && currentTheme.id === theme.id && (
          <CheckOutlined style={{ color: theme.primaryColor }} />
        )}
      </div>
    )
  }));
  
  // Create theme mode menu items
  const themeModeMenuItems = availableThemeModes.map(mode => ({
    key: mode,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <span style={{ textTransform: 'capitalize' }}>{mode.replace('-', ' ')}</span>
        {themeMode === mode && <CheckOutlined />}
      </div>
    )
  }));
  
  // Create theme menu
  const themeMenu = {
    items: themeMenuItems,
    onClick: handleThemeSelect
  };
  
  // Create theme mode menu
  const themeModeMenu = {
    items: themeModeMenuItems,
    onClick: handleThemeModeSelect
  };
  
  // Combined menu for compact mode
  const combinedMenu = {
    items: [
      {
        key: 'theme-modes',
        type: 'group',
        label: 'Theme Modes',
        children: themeModeMenuItems
      },
      {
        type: 'divider'
      },
      {
        key: 'themes',
        type: 'group',
        label: 'Custom Themes',
        children: themeMenuItems
      }
    ],
    onClick: (info) => {
      // Check if this is a theme mode or theme ID
      if (availableThemeModes.includes(info.key)) {
        handleThemeModeSelect(info);
      } else {
        handleThemeSelect(info);
      }
    }
  };
  
  if (compact) {
    return (
      <Dropdown 
        menu={combinedMenu} 
        trigger={['click']}
        onOpenChange={setDropdownVisible}
        open={dropdownVisible}
      >
        <Badge dot={currentTheme && currentTheme.id !== 'default'}>
          <Button 
            icon={<BgColorsOutlined />} 
            type="text"
            aria-label="Theme settings"
          />
        </Badge>
      </Dropdown>
    );
  }
  
  return (
    <ThemeButtonContainer>
      {showModeToggle && (
        <Tooltip title={`Current mode: ${themeMode}. Click to toggle.`}>
          <Button 
            icon={getThemeModeIcon()} 
            onClick={toggleThemeMode}
            type="text"
            aria-label={`Toggle theme mode. Current mode: ${themeMode}`}
          />
        </Tooltip>
      )}
      
      {showThemeSelector && (
        <Dropdown 
          menu={themeMenu} 
          trigger={['click']}
          onOpenChange={setDropdownVisible}
          open={dropdownVisible}
        >
          <Button type="default">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {currentTheme && (
                <ColorSwatch style={{ backgroundColor: currentTheme.primaryColor }} />
              )}
              <span>{currentTheme ? currentTheme.name : 'Select Theme'}</span>
              <DownOutlined style={{ fontSize: '12px', marginLeft: '5px' }} />
            </div>
          </Button>
        </Dropdown>
      )}
    </ThemeButtonContainer>
  );
};

export default ThemeToggle;
