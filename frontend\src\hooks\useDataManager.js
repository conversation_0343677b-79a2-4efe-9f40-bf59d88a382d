import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import dataManager from '../utils/dataManager';

/**
 * Custom hook for managing data with optimized Redux usage
 * 
 * @param {Object} options - Configuration options
 * @param {Function} options.selector - Redux selector function
 * @param {Function} options.action - Action creator function
 * @param {string} options.cacheKey - Key for caching data
 * @param {string} options.cacheType - Type of cache ('temporary', 'persistent', or 'expiring')
 * @param {number} options.expiresIn - Time in ms after which cache expires (for 'expiring' type)
 * @param {boolean} options.useCache - Whether to use cache
 * @param {any} options.defaultValue - Default value if data is not found
 * @returns {Object} Data management utilities
 */
const useDataManager = ({
  selector,
  action,
  cacheKey,
  cacheType = 'temporary',
  expiresIn = 3600000, // 1 hour
  useCache = true,
  defaultValue = null
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Get data from Redux store if selector is provided
  const storeData = selector ? useSelector(selector) : undefined;
  
  // Get cached data
  const getCachedData = useCallback(() => {
    if (!useCache || !cacheKey) return null;
    return dataManager.getCache(cacheKey, cacheType);
  }, [useCache, cacheKey, cacheType]);
  
  // Set data in cache
  const setCachedData = useCallback((data) => {
    if (!useCache || !cacheKey) return;
    dataManager.setCache(cacheKey, data, { type: cacheType, expiresIn });
  }, [useCache, cacheKey, cacheType, expiresIn]);
  
  // Clear cached data
  const clearCachedData = useCallback(() => {
    if (!cacheKey) return;
    dataManager.clearCache(cacheKey, cacheType);
  }, [cacheKey, cacheType]);
  
  // Update data in Redux store
  const updateData = useCallback(async (payload) => {
    if (!action) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await dispatch(action(payload));
      
      // Update cache with new data if caching is enabled
      if (useCache && cacheKey) {
        setCachedData(result?.payload || result);
      }
      
      setLoading(false);
      return result;
    } catch (err) {
      setError(err);
      setLoading(false);
      throw err;
    }
  }, [action, dispatch, useCache, cacheKey, setCachedData]);
  
  // Get data with cache fallback
  const getData = useCallback(() => {
    // First try Redux store
    if (storeData !== undefined) {
      return storeData;
    }
    
    // Then try cache
    const cachedData = getCachedData();
    if (cachedData !== null) {
      return cachedData;
    }
    
    // Fall back to default value
    return defaultValue;
  }, [storeData, getCachedData, defaultValue]);
  
  // Refresh data by forcing a Redux update
  const refreshData = useCallback(async () => {
    if (!action) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await dispatch(action());
      
      // Update cache with new data
      if (useCache && cacheKey) {
        setCachedData(result?.payload || result);
      }
      
      setLoading(false);
      return result;
    } catch (err) {
      setError(err);
      setLoading(false);
      throw err;
    }
  }, [action, dispatch, useCache, cacheKey, setCachedData]);
  
  // Initialize cache on mount if needed
  useEffect(() => {
    if (useCache && cacheKey && storeData !== undefined && !getCachedData()) {
      setCachedData(storeData);
    }
  }, [useCache, cacheKey, storeData, getCachedData, setCachedData]);
  
  return {
    data: getData(),
    loading,
    error,
    updateData,
    refreshData,
    clearCache: clearCachedData
  };
};

export default useDataManager;
