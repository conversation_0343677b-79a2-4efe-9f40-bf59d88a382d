import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, Drawer, Switch, Slider, Radio, Space, Typography } from 'antd';
import { SettingOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

/**
 * AccessibilityManager Component
 *
 * This component provides accessibility controls for the application.
 * It allows users to adjust text size, contrast, motion, and color modes.
 *
 * @param {Object} props - Component props
 * @returns {React.Component} - Accessibility manager component
 */
const AccessibilityManager = ({
  position = 'right',
  defaultOpen = false
}) => {
  // State for drawer visibility
  const [visible, setVisible] = useState(defaultOpen);

  // State for accessibility settings
  const [textSize, setTextSize] = useState(100);
  const [highContrast, setHighContrast] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [colorBlindMode, setColorBlindMode] = useState('none');

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedTextSize = localStorage.getItem('accessibility_textSize');
    const savedHighContrast = localStorage.getItem('accessibility_highContrast');
    const savedReducedMotion = localStorage.getItem('accessibility_reducedMotion');
    const savedColorBlindMode = localStorage.getItem('accessibility_colorBlindMode');

    if (savedTextSize) setTextSize(parseInt(savedTextSize, 10));
    if (savedHighContrast) setHighContrast(savedHighContrast === 'true');
    if (savedReducedMotion) setReducedMotion(savedReducedMotion === 'true');
    if (savedColorBlindMode) setColorBlindMode(savedColorBlindMode);

    // Check for system preferences
    if (window.matchMedia) {
      // Check for prefers-reduced-motion
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
      if (prefersReducedMotion.matches && !savedReducedMotion) {
        setReducedMotion(true);
      }

      // Check for prefers-contrast
      const prefersContrast = window.matchMedia('(prefers-contrast: more)');
      if (prefersContrast.matches && !savedHighContrast) {
        setHighContrast(true);
      }
    }
  }, []);

  // Apply text size
  useEffect(() => {
    document.documentElement.style.fontSize = `${textSize}%`;
    localStorage.setItem('accessibility_textSize', textSize.toString());
  }, [textSize]);

  // Apply high contrast
  useEffect(() => {
    if (highContrast) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }
    localStorage.setItem('accessibility_highContrast', highContrast.toString());
  }, [highContrast]);

  // Apply reduced motion
  useEffect(() => {
    if (reducedMotion) {
      document.body.classList.add('reduced-motion');
    } else {
      document.body.classList.remove('reduced-motion');
    }
    localStorage.setItem('accessibility_reducedMotion', reducedMotion.toString());
  }, [reducedMotion]);

  // Apply color blind mode
  useEffect(() => {
    // Remove all color blind mode classes
    document.body.classList.remove('protanopia', 'deuteranopia', 'tritanopia');

    // Add the selected mode class
    if (colorBlindMode !== 'none') {
      document.body.classList.add(colorBlindMode);
    }

    localStorage.setItem('accessibility_colorBlindMode', colorBlindMode);
  }, [colorBlindMode]);

  // Toggle drawer visibility
  const toggleDrawer = () => {
    setVisible(!visible);
  };

  return (
    <>
      {/* Accessibility button */}
      <Button
        type="primary"
        icon={<SettingOutlined />}
        onClick={toggleDrawer}
        style={{
          position: 'fixed',
          [position]: '20px',
          bottom: '20px',
          zIndex: 1000,
        }}
        aria-label="Accessibility Settings"
      />

      {/* Accessibility drawer */}
      <Drawer
        title="Accessibility Settings"
        placement={position}
        onClose={toggleDrawer}
        open={visible}
        width={320}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* Text Size */}
          <Title level={5}>Text Size</Title>
          <Paragraph>Adjust the size of text throughout the application.</Paragraph>
          <Slider
            min={75}
            max={200}
            step={5}
            value={textSize}
            onChange={setTextSize}
            marks={{
              75: 'Small',
              100: 'Normal',
              150: 'Large',
              200: 'X-Large'
            }}
          />

          {/* High Contrast */}
          <Title level={5}>High Contrast</Title>
          <Paragraph>Increase contrast for better readability.</Paragraph>
          <Switch
            checked={highContrast}
            onChange={setHighContrast}
          />

          {/* Reduced Motion */}
          <Title level={5}>Reduced Motion</Title>
          <Paragraph>Minimize animations and transitions.</Paragraph>
          <Switch
            checked={reducedMotion}
            onChange={setReducedMotion}
          />

          {/* Color Blind Mode */}
          <Title level={5}>Color Blind Mode</Title>
          <Paragraph>Adjust colors for different types of color blindness.</Paragraph>
          <Radio.Group
            value={colorBlindMode}
            onChange={(e) => setColorBlindMode(e.target.value)}
          >
            <Space direction="vertical">
              <Radio value="none">None</Radio>
              <Radio value="protanopia">Protanopia (Red-Blind)</Radio>
              <Radio value="deuteranopia">Deuteranopia (Green-Blind)</Radio>
              <Radio value="tritanopia">Tritanopia (Blue-Blind)</Radio>
            </Space>
          </Radio.Group>
        </Space>
      </Drawer>
    </>
  );
};

AccessibilityManager.propTypes = {
  position: PropTypes.oneOf(['left', 'right']),
  defaultOpen: PropTypes.bool
};

export default AccessibilityManager;
