/**
 * Project structure generators and configuration file generators
 * This file contains functions to generate complete project structures with build configs, package.json, etc.
 */

/**
 * Generate package.json for different project types
 */
export const generatePackageJson = (projectType, options = {}) => {
  const { packageManager = 'npm', typescript = false, styleFramework = 'styled-components' } = options;
  
  const basePackage = {
    name: 'app-builder-generated-app',
    version: '0.1.0',
    private: true,
    description: 'Generated by App Builder',
    author: 'App Builder',
    license: 'MIT'
  };
  
  switch (projectType) {
    case 'react-app':
      return JSON.stringify({
        ...basePackage,
        scripts: {
          start: 'react-scripts start',
          build: 'react-scripts build',
          test: 'react-scripts test',
          eject: 'react-scripts eject',
          lint: 'eslint src --ext .js,.jsx,.ts,.tsx',
          'lint:fix': 'eslint src --ext .js,.jsx,.ts,.tsx --fix'
        },
        dependencies: {
          react: '^18.2.0',
          'react-dom': '^18.2.0',
          'react-scripts': '5.0.1',
          'web-vitals': '^2.1.4',
          ...(styleFramework === 'styled-components' && { 'styled-components': '^5.3.9' }),
          ...(styleFramework === 'emotion' && { '@emotion/react': '^11.10.6', '@emotion/styled': '^11.10.6' }),
          ...(styleFramework === 'tailwind' && { 'tailwindcss': '^3.2.7' }),
          ...(!typescript && { 'prop-types': '^15.8.1' })
        },
        devDependencies: {
          '@testing-library/jest-dom': '^5.16.4',
          '@testing-library/react': '^13.4.0',
          '@testing-library/user-event': '^13.5.0',
          ...(typescript && {
            '@types/react': '^18.0.28',
            '@types/react-dom': '^18.0.11',
            '@types/node': '^16.18.23',
            typescript: '^4.9.5'
          }),
          eslint: '^8.36.0',
          'eslint-plugin-react': '^7.32.2',
          'eslint-plugin-react-hooks': '^4.6.0'
        },
        browserslist: {
          production: ['>0.2%', 'not dead', 'not op_mini all'],
          development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
        }
      }, null, 2);
      
    case 'nextjs-app':
      return JSON.stringify({
        ...basePackage,
        scripts: {
          dev: 'next dev',
          build: 'next build',
          start: 'next start',
          lint: 'next lint',
          export: 'next export'
        },
        dependencies: {
          next: '^13.2.4',
          react: '^18.2.0',
          'react-dom': '^18.2.0',
          ...(styleFramework === 'styled-components' && { 'styled-components': '^5.3.9' }),
          ...(styleFramework === 'tailwind' && { 'tailwindcss': '^3.2.7', 'autoprefixer': '^10.4.14', 'postcss': '^8.4.21' })
        },
        devDependencies: {
          ...(typescript && {
            '@types/node': '^18.15.3',
            '@types/react': '^18.0.28',
            '@types/react-dom': '^18.0.11',
            typescript: '^5.0.2'
          }),
          eslint: '^8.36.0',
          'eslint-config-next': '^13.2.4'
        }
      }, null, 2);
      
    case 'vue-app':
      return JSON.stringify({
        ...basePackage,
        scripts: {
          serve: 'vue-cli-service serve',
          build: 'vue-cli-service build',
          test: 'vue-cli-service test:unit',
          lint: 'vue-cli-service lint'
        },
        dependencies: {
          'core-js': '^3.8.3',
          vue: '^3.2.13',
          ...(typescript && { 'vue-tsc': '^1.2.0' })
        },
        devDependencies: {
          '@babel/core': '^7.12.16',
          '@babel/eslint-parser': '^7.12.16',
          '@vue/cli-plugin-babel': '~5.0.0',
          '@vue/cli-plugin-eslint': '~5.0.0',
          '@vue/cli-service': '~5.0.0',
          eslint: '^7.32.0',
          'eslint-plugin-vue': '^8.0.3',
          ...(typescript && {
            '@vue/cli-plugin-typescript': '~5.0.0',
            '@vue/eslint-config-typescript': '^9.1.0',
            typescript: '~4.5.5'
          })
        }
      }, null, 2);
      
    case 'angular-app':
      return JSON.stringify({
        ...basePackage,
        scripts: {
          ng: 'ng',
          start: 'ng serve',
          build: 'ng build',
          watch: 'ng build --watch --configuration development',
          test: 'ng test'
        },
        dependencies: {
          '@angular/animations': '^15.2.0',
          '@angular/common': '^15.2.0',
          '@angular/compiler': '^15.2.0',
          '@angular/core': '^15.2.0',
          '@angular/forms': '^15.2.0',
          '@angular/platform-browser': '^15.2.0',
          '@angular/platform-browser-dynamic': '^15.2.0',
          '@angular/router': '^15.2.0',
          rxjs: '~7.8.0',
          tslib: '^2.3.0',
          'zone.js': '~0.12.0'
        },
        devDependencies: {
          '@angular-devkit/build-angular': '^15.2.4',
          '@angular/cli': '~15.2.4',
          '@angular/compiler-cli': '^15.2.0',
          '@types/jasmine': '~4.3.0',
          '@types/node': '^12.11.1',
          jasmine: '~4.5.0',
          'karma': '~6.4.0',
          'karma-chrome-launcher': '~3.1.0',
          'karma-coverage': '~2.2.0',
          'karma-jasmine': '~5.1.0',
          'karma-jasmine-html-reporter': '~2.0.0',
          typescript: '~4.9.4'
        }
      }, null, 2);
      
    default:
      return JSON.stringify(basePackage, null, 2);
  }
};

/**
 * Generate README.md file
 */
export const generateReadme = (framework, options = {}) => {
  const { projectName = 'Generated App', typescript = false } = options;
  
  return `# ${projectName}

This project was generated using App Builder.

## Framework: ${framework}${typescript ? ' with TypeScript' : ''}

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm, yarn, or pnpm

### Installation

\`\`\`bash
# Install dependencies
npm install

# Or with yarn
yarn install

# Or with pnpm
pnpm install
\`\`\`

### Development

\`\`\`bash
# Start development server
npm start

# Or with yarn
yarn start

# Or with pnpm
pnpm start
\`\`\`

### Building for Production

\`\`\`bash
# Build for production
npm run build

# Or with yarn
yarn build

# Or with pnpm
pnpm build
\`\`\`

## Project Structure

\`\`\`
src/
├── components/     # Reusable components
├── pages/         # Page components
├── styles/        # Stylesheets
├── utils/         # Utility functions
└── App.${typescript ? 'tsx' : 'jsx'}        # Main application component
\`\`\`

## Features

- ✅ Modern ${framework} application
- ✅ Responsive design
- ✅ Accessibility features
${typescript ? '- ✅ TypeScript support' : ''}
- ✅ ESLint configuration
- ✅ Production-ready build

## Generated by App Builder

This application was automatically generated by App Builder. You can customize and extend it according to your needs.

## Learn More

- [${framework} Documentation](${getFrameworkDocsUrl(framework)})
- [App Builder Documentation](https://app-builder.example.com/docs)

## License

MIT
`;
};

/**
 * Generate TypeScript configuration
 */
export const generateTSConfig = () => {
  return JSON.stringify({
    compilerOptions: {
      target: 'es5',
      lib: ['dom', 'dom.iterable', 'es6'],
      allowJs: true,
      skipLibCheck: true,
      esModuleInterop: true,
      allowSyntheticDefaultImports: true,
      strict: true,
      forceConsistentCasingInFileNames: true,
      noFallthroughCasesInSwitch: true,
      module: 'esnext',
      moduleResolution: 'node',
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: 'react-jsx'
    },
    include: ['src'],
    exclude: ['node_modules']
  }, null, 2);
};

/**
 * Generate ESLint configuration
 */
export const generateESLintConfig = (framework, typescript = false) => {
  const baseConfig = {
    env: {
      browser: true,
      es2021: true,
      node: true
    },
    extends: [
      'eslint:recommended'
    ],
    parserOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module'
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-console': 'warn',
      'prefer-const': 'error'
    }
  };
  
  if (framework === 'react') {
    baseConfig.extends.push('plugin:react/recommended', 'plugin:react-hooks/recommended');
    baseConfig.plugins = ['react', 'react-hooks'];
    baseConfig.parserOptions.ecmaFeatures = { jsx: true };
    baseConfig.settings = { react: { version: 'detect' } };
  }
  
  if (typescript) {
    baseConfig.extends.push('@typescript-eslint/recommended');
    baseConfig.parser = '@typescript-eslint/parser';
    baseConfig.plugins = [...(baseConfig.plugins || []), '@typescript-eslint'];
  }
  
  return JSON.stringify(baseConfig, null, 2);
};

/**
 * Generate Dockerfile
 */
export const generateDockerfile = (framework) => {
  return `# Use official Node.js runtime as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
`;
};

/**
 * Generate Docker Compose file
 */
export const generateDockerCompose = () => {
  return `version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - .:/app
      - /app/node_modules
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    restart: unless-stopped
`;
};

/**
 * Generate GitHub Actions workflow
 */
export const generateGitHubActions = (framework) => {
  return `name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js \${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: \${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to production
      run: echo "Deploy to your hosting platform"
`;
};

/**
 * Generate Vite configuration
 */
export const generateViteConfig = (framework, typescript = false) => {
  const ext = typescript ? 'ts' : 'js';
  
  return `import { defineConfig } from 'vite'
${framework === 'react' ? "import react from '@vitejs/plugin-react'" : ''}
${framework === 'vue' ? "import vue from '@vitejs/plugin-vue'" : ''}

export default defineConfig({
  plugins: [${framework === 'react' ? 'react()' : framework === 'vue' ? 'vue()' : ''}],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
`;
};

// Helper functions
const getFrameworkDocsUrl = (framework) => {
  const urls = {
    'React': 'https://reactjs.org/docs',
    'Vue': 'https://vuejs.org/guide/',
    'Angular': 'https://angular.io/docs',
    'Svelte': 'https://svelte.dev/docs',
    'Next.js': 'https://nextjs.org/docs'
  };
  return urls[framework] || 'https://developer.mozilla.org/';
};
