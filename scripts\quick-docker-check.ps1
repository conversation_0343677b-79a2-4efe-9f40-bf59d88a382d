# Quick Docker Status Check
Write-Host "🐳 Quick Docker Status Check" -ForegroundColor Cyan

# Check if Docker command exists
try {
    $dockerVersion = docker --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker CLI: $dockerVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker CLI not found" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Docker CLI not available" -ForegroundColor Red
    exit 1
}

# Check if Docker service is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker service is running" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker service is not running" -ForegroundColor Red
        Write-Host "💡 Try running: scripts/fix-docker-connection.ps1" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Cannot connect to Docker service" -ForegroundColor Red
    Write-Host "💡 Try running: scripts/fix-docker-connection.ps1" -ForegroundColor Yellow
    exit 1
}

# Check Docker Compose
try {
    $composeVersion = docker-compose --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker Compose: $composeVersion" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Docker Compose not found (using docker compose instead)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Docker Compose not available" -ForegroundColor Yellow
}

Write-Host "`n🎉 Docker is ready!" -ForegroundColor Green
Write-Host "You can now run: docker-compose up -d" -ForegroundColor Blue
