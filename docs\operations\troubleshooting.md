# Troubleshooting Guide - App Builder 201

This guide helps you diagnose and resolve common issues with App Builder 201.

## Quick Diagnostics

### Health Check Commands
```bash
# Check application health
curl http://localhost:8000/health/

# Check container status
docker-compose ps

# View application logs
docker-compose logs

# Check system resources
python scripts/monitor-performance.py
```

### Service Status
```bash
# Check all services
docker-compose ps

# Check specific service
docker-compose logs [service-name]

# Restart service
docker-compose restart [service-name]
```

## Common Issues

### 1. Application Won't Start

#### Symptoms
- Containers fail to start
- Error messages during startup
- Services not responding

#### Diagnosis
```bash
# Check container status
docker-compose ps

# View startup logs
docker-compose logs

# Check for port conflicts
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000
```

#### Solutions

**Port Conflicts**
```bash
# Stop conflicting services
sudo lsof -ti:3000 | xargs kill -9
sudo lsof -ti:8000 | xargs kill -9

# Or change ports in docker-compose.yml
```

**Docker Issues**
```bash
# Restart Docker service
sudo systemctl restart docker

# Clean Docker system
docker system prune -a

# Rebuild containers
docker-compose down
docker-compose up --build
```

**Permission Issues**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .

# Fix Docker permissions
sudo usermod -aG docker $USER
```

### 2. Database Connection Issues

#### Symptoms
- Database connection errors
- Migration failures
- Data not persisting

#### Diagnosis
```bash
# Check database container
docker-compose logs db

# Test database connection
docker-compose exec backend python manage.py dbshell

# Check database status
docker-compose exec db pg_isready -U app_builder_user
```

#### Solutions

**Database Not Ready**
```bash
# Wait for database to start
sleep 30

# Check database logs
docker-compose logs db

# Restart database
docker-compose restart db
```

**Migration Issues**
```bash
# Run migrations manually
docker-compose exec backend python manage.py migrate

# Reset migrations (development only)
docker-compose exec backend python manage.py migrate --fake-initial

# Check migration status
docker-compose exec backend python manage.py showmigrations
```

**Connection Pool Issues**
```bash
# Restart backend service
docker-compose restart backend

# Check connection settings in settings.py
# Increase connection pool size if needed
```

### 3. Frontend Issues

#### Symptoms
- White screen or blank page
- JavaScript errors in console
- Components not loading

#### Diagnosis
```bash
# Check frontend logs
docker-compose logs frontend

# Check browser console for errors
# Open Developer Tools > Console

# Check network requests
# Open Developer Tools > Network
```

#### Solutions

**Build Issues**
```bash
# Clear build cache
docker-compose exec frontend npm run build

# Reinstall dependencies
docker-compose exec frontend npm install

# Check for dependency conflicts
docker-compose exec frontend npm audit
```

**Memory Issues**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Or modify package.json scripts
"build": "NODE_OPTIONS='--max-old-space-size=4096' react-scripts build"
```

**Browser Cache Issues**
```bash
# Clear browser cache
# Hard refresh: Ctrl+Shift+R (Cmd+Shift+R on Mac)

# Disable cache in DevTools
# Open DevTools > Network > Disable cache
```

### 4. WebSocket Connection Issues

#### Symptoms
- Real-time features not working
- Connection timeouts
- Frequent disconnections

#### Diagnosis
```bash
# Test WebSocket connection
python scripts/websocket-test.py

# Check WebSocket logs
docker-compose logs backend | grep -i websocket

# Check network connectivity
curl -I http://localhost:8000/ws/test/
```

#### Solutions

**Connection Issues**
```bash
# Check firewall settings
sudo ufw status

# Check proxy configuration
# Ensure WebSocket upgrade headers are passed

# Restart backend service
docker-compose restart backend
```

**Performance Issues**
```bash
# Increase WebSocket timeout
# Modify settings.py CHANNEL_LAYERS configuration

# Check Redis connection
docker-compose exec redis redis-cli ping

# Monitor WebSocket connections
docker-compose exec backend python manage.py shell
```

### 5. Performance Issues

#### Symptoms
- Slow page loading
- High CPU/memory usage
- Timeouts

#### Diagnosis
```bash
# Run performance tests
python scripts/performance-test-suite.py

# Monitor system resources
htop
docker stats

# Check application metrics
curl http://localhost:8000/metrics/
```

#### Solutions

**Database Performance**
```bash
# Optimize database queries
# Add database indexes
# Use query optimization

# Check slow queries
docker-compose exec db psql -U app_builder_user -d app_builder_201 -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"
```

**Memory Issues**
```bash
# Increase container memory limits
# Modify docker-compose.yml

# Check for memory leaks
docker stats --no-stream

# Restart services periodically
```

**Network Performance**
```bash
# Enable gzip compression in Nginx
# Optimize static file serving
# Use CDN for static assets

# Check network latency
ping your-domain.com
traceroute your-domain.com
```

### 6. SSL/HTTPS Issues

#### Symptoms
- SSL certificate errors
- HTTPS redirects not working
- Mixed content warnings

#### Diagnosis
```bash
# Check SSL certificate
openssl s_client -connect your-domain.com:443

# Test SSL configuration
curl -I https://your-domain.com

# Check certificate expiration
echo | openssl s_client -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates
```

#### Solutions

**Certificate Issues**
```bash
# Renew Let's Encrypt certificates
docker-compose run --rm certbot renew

# Check certificate files
ls -la nginx/ssl/live/your-domain.com/

# Restart Nginx
docker-compose restart nginx
```

**Configuration Issues**
```bash
# Check Nginx configuration
docker-compose exec nginx nginx -t

# Verify SSL settings in nginx.conf
# Ensure proper SSL protocols and ciphers
```

## Monitoring and Logging

### Log Locations
```bash
# Application logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs nginx

# System logs
/var/log/syslog
/var/log/docker.log

# Custom logs
./logs/application.log
./logs/security.log
```

### Monitoring Commands
```bash
# Real-time monitoring
docker-compose logs -f

# Resource monitoring
docker stats

# Performance monitoring
python scripts/monitor-performance.py

# Security monitoring
python scripts/security-test-suite.py
```

## Recovery Procedures

### Database Recovery
```bash
# Restore from backup
docker-compose exec -T db psql -U app_builder_user -d app_builder_201 < backup.sql

# Reset database (development only)
docker-compose down -v
docker-compose up -d
docker-compose exec backend python manage.py migrate
```

### Application Recovery
```bash
# Full restart
docker-compose down
docker-compose up -d

# Rebuild and restart
docker-compose down
docker-compose up --build

# Reset to clean state
docker-compose down -v
docker system prune -a
docker-compose up -d
```

### Data Recovery
```bash
# Restore from backup
cp -r backup/data/* ./data/

# Restore database
docker-compose exec -T db psql -U app_builder_user -d app_builder_201 < backup/database.sql

# Restore static files
cp -r backup/static/* ./static/
```

## Prevention

### Regular Maintenance
```bash
# Update dependencies
npm audit fix
pip-audit --fix

# Clean Docker system
docker system prune -f

# Backup data
./scripts/backup.sh

# Monitor logs
tail -f logs/application.log
```

### Monitoring Setup
```bash
# Set up monitoring
docker-compose -f monitoring/docker-compose.monitoring.yml up -d

# Configure alerts
# Set up Grafana dashboards
# Configure Prometheus alerts
```

### Security Maintenance
```bash
# Run security audits
python scripts/security-audit-and-fix.py

# Update security configurations
# Review access logs
# Update SSL certificates
```

## Getting Help

### Self-Service Resources
1. **Documentation**: Check the comprehensive documentation
2. **FAQ**: Review frequently asked questions
3. **Community Forums**: Search existing discussions
4. **GitHub Issues**: Check for known issues

### Support Channels
1. **GitHub Issues**: Report bugs and feature requests
2. **Community Discord**: Get help from other users
3. **Email Support**: Contact technical support team
4. **Emergency Hotline**: For critical production issues

### Information to Provide
When seeking help, include:
- Error messages and stack traces
- Steps to reproduce the issue
- System information (OS, Docker version, etc.)
- Log files and screenshots
- Configuration files (remove sensitive data)

---

For additional help, visit our [support page](../support.md) or contact our technical support team.
