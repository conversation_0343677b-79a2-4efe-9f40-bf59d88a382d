/**
 * Tutorial System Type Definitions and Constants
 * 
 * This file defines the data structures and constants used throughout
 * the tutorial system for type safety and consistency.
 */

// Tutorial Step Types
export const TUTORIAL_STEP_TYPES = {
  HIGHLIGHT: 'highlight',           // Highlight a specific element
  MODAL: 'modal',                  // Show modal with information
  TOOLTIP: 'tooltip',              // Show tooltip near element
  OVERLAY: 'overlay',              // Full screen overlay with content
  INTERACTIVE: 'interactive',      // Require user interaction
  WAIT: 'wait'                     // Wait for user action or condition
};

// Tutorial Categories
export const TUTORIAL_CATEGORIES = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
  FEATURE_SPECIFIC: 'feature-specific'
};

// Tutorial Status
export const TUTORIAL_STATUS = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  SKIPPED: 'skipped'
};

// Help Context Types
export const HELP_CONTEXT_TYPES = {
  COMPONENT_PALETTE: 'component_palette',
  PREVIEW_AREA: 'preview_area',
  PROPERTY_EDITOR: 'property_editor',
  DRAG_DROP: 'drag_drop',
  THEME_MANAGER: 'theme_manager',
  LAYOUT_DESIGNER: 'layout_designer',
  CODE_EXPORT: 'code_export',
  WEBSOCKET: 'websocket'
};

// Tutorial Step Schema
export const createTutorialStep = ({
  id,
  type = TUTORIAL_STEP_TYPES.HIGHLIGHT,
  title,
  content,
  targetSelector = null,
  position = 'bottom',
  showSkip = true,
  showPrevious = true,
  showNext = true,
  autoAdvance = false,
  autoAdvanceDelay = 0,
  requiredAction = null,
  validationFn = null,
  onEnter = null,
  onExit = null,
  customComponent = null,
  highlightPadding = 8,
  highlightBorderRadius = 4,
  zIndex = 10000
}) => ({
  id,
  type,
  title,
  content,
  targetSelector,
  position,
  showSkip,
  showPrevious,
  showNext,
  autoAdvance,
  autoAdvanceDelay,
  requiredAction,
  validationFn,
  onEnter,
  onExit,
  customComponent,
  highlightPadding,
  highlightBorderRadius,
  zIndex
});

// Tutorial Schema
export const createTutorial = ({
  id,
  title,
  description,
  category = TUTORIAL_CATEGORIES.BEGINNER,
  estimatedDuration = 5,
  prerequisites = [],
  tags = [],
  steps = [],
  onStart = null,
  onComplete = null,
  onSkip = null,
  icon = null,
  difficulty = 1,
  isRequired = false
}) => ({
  id,
  title,
  description,
  category,
  estimatedDuration,
  prerequisites,
  tags,
  steps,
  onStart,
  onComplete,
  onSkip,
  icon,
  difficulty,
  isRequired,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

// Tutorial Progress Schema
export const createTutorialProgress = ({
  tutorialId,
  userId = 'anonymous',
  status = TUTORIAL_STATUS.NOT_STARTED,
  currentStepIndex = 0,
  completedSteps = [],
  startedAt = null,
  completedAt = null,
  pausedAt = null,
  timeSpent = 0,
  skippedSteps = [],
  metadata = {}
}) => ({
  tutorialId,
  userId,
  status,
  currentStepIndex,
  completedSteps,
  startedAt,
  completedAt,
  pausedAt,
  timeSpent,
  skippedSteps,
  metadata,
  lastUpdated: new Date().toISOString()
});

// Help Context Schema
export const createHelpContext = ({
  id,
  type,
  title,
  content,
  triggers = [],
  conditions = [],
  priority = 1,
  showOnce = false,
  relatedTutorials = [],
  customComponent = null
}) => ({
  id,
  type,
  title,
  content,
  triggers,
  conditions,
  priority,
  showOnce,
  relatedTutorials,
  customComponent,
  createdAt: new Date().toISOString()
});

// Tutorial Event Types for analytics and tracking
export const TUTORIAL_EVENTS = {
  TUTORIAL_STARTED: 'tutorial_started',
  TUTORIAL_COMPLETED: 'tutorial_completed',
  TUTORIAL_SKIPPED: 'tutorial_skipped',
  TUTORIAL_PAUSED: 'tutorial_paused',
  TUTORIAL_RESUMED: 'tutorial_resumed',
  STEP_STARTED: 'step_started',
  STEP_COMPLETED: 'step_completed',
  STEP_SKIPPED: 'step_skipped',
  HELP_REQUESTED: 'help_requested',
  HELP_DISMISSED: 'help_dismissed',
  CONTEXT_HELP_SHOWN: 'context_help_shown'
};

// Storage Keys
export const STORAGE_KEYS = {
  TUTORIAL_PROGRESS: 'app_builder_tutorial_progress',
  TUTORIAL_PREFERENCES: 'app_builder_tutorial_preferences',
  HELP_CONTEXT_SHOWN: 'app_builder_help_context_shown',
  TUTORIAL_COMPLETION_BADGES: 'app_builder_tutorial_badges'
};

// Default Tutorial Preferences
export const DEFAULT_TUTORIAL_PREFERENCES = {
  autoStartTutorials: false,
  showContextualHelp: true,
  showTooltips: true,
  enableSoundEffects: false,
  animationSpeed: 'normal', // 'slow', 'normal', 'fast'
  skipCompletedTutorials: true,
  showProgressIndicator: true,
  enableKeyboardShortcuts: true
};

// Keyboard Shortcuts
export const TUTORIAL_SHORTCUTS = {
  NEXT_STEP: 'ArrowRight',
  PREVIOUS_STEP: 'ArrowLeft',
  SKIP_TUTORIAL: 'Escape',
  PAUSE_TUTORIAL: 'Space',
  SHOW_HELP: 'F1'
};

// Animation Durations (in milliseconds)
export const ANIMATION_DURATIONS = {
  HIGHLIGHT_FADE_IN: 300,
  HIGHLIGHT_FADE_OUT: 200,
  TOOLTIP_SHOW: 200,
  TOOLTIP_HIDE: 150,
  MODAL_SHOW: 300,
  MODAL_HIDE: 200,
  OVERLAY_FADE: 400
};

// Z-Index Layers
export const Z_INDEX = {
  TUTORIAL_OVERLAY: 10000,
  TUTORIAL_HIGHLIGHT: 10001,
  TUTORIAL_TOOLTIP: 10002,
  TUTORIAL_MODAL: 10003,
  TUTORIAL_CONTROLS: 10004
};

export default {
  TUTORIAL_STEP_TYPES,
  TUTORIAL_CATEGORIES,
  TUTORIAL_STATUS,
  HELP_CONTEXT_TYPES,
  TUTORIAL_EVENTS,
  STORAGE_KEYS,
  DEFAULT_TUTORIAL_PREFERENCES,
  TUTORIAL_SHORTCUTS,
  ANIMATION_DURATIONS,
  Z_INDEX,
  createTutorialStep,
  createTutorial,
  createTutorialProgress,
  createHelpContext
};
