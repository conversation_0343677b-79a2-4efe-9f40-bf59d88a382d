import { useEffect, useState, useRef } from 'react';
import { FeatureComponents, LoadingPriority } from '../config/lazyComponents';

/**
 * Progressive Component Loading System
 * 
 * Implements intelligent loading of components based on priority, user interaction,
 * and viewport visibility to optimize initial bundle size and user experience.
 */

// Global state for tracking loaded components
const loadedComponents = new Set();
const loadingComponents = new Set();
const preloadQueue = [];

/**
 * Progressive loading manager
 */
class ProgressiveLoadingManager {
  constructor() {
    this.intersectionObserver = null;
    this.idleCallback = null;
    this.loadingStrategy = 'priority'; // 'priority', 'interaction', 'viewport'
    this.maxConcurrentLoads = 3;
    this.currentLoads = 0;
    
    this.initializeObservers();
  }

  initializeObservers() {
    // Intersection Observer for viewport-based loading
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const componentName = entry.target.dataset.componentName;
              if (componentName) {
                this.loadComponent(componentName, 'viewport');
              }
            }
          });
        },
        {
          rootMargin: '50px',
          threshold: 0.1
        }
      );
    }

    // Idle callback for background loading
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      this.scheduleIdleLoading();
    }
  }

  scheduleIdleLoading() {
    this.idleCallback = requestIdleCallback(() => {
      this.loadNextInQueue();
      this.scheduleIdleLoading(); // Schedule next idle period
    }, { timeout: 5000 });
  }

  async loadComponent(componentName, trigger = 'manual') {
    if (loadedComponents.has(componentName) || loadingComponents.has(componentName)) {
      return;
    }

    if (this.currentLoads >= this.maxConcurrentLoads) {
      preloadQueue.push({ componentName, trigger });
      return;
    }

    loadingComponents.add(componentName);
    this.currentLoads++;

    try {
      // Find component in feature groups
      let component = null;
      for (const [featureName, components] of Object.entries(FeatureComponents)) {
        component = components.find(comp => comp.displayName === componentName);
        if (component) break;
      }

      if (component && component.preload) {
        console.log(`🔄 Loading component: ${componentName} (trigger: ${trigger})`);
        await component.preload();
        loadedComponents.add(componentName);
        console.log(`✅ Loaded component: ${componentName}`);
      }
    } catch (error) {
      console.warn(`❌ Failed to load component: ${componentName}`, error);
    } finally {
      loadingComponents.delete(componentName);
      this.currentLoads--;
      this.loadNextInQueue();
    }
  }

  loadNextInQueue() {
    if (preloadQueue.length > 0 && this.currentLoads < this.maxConcurrentLoads) {
      const { componentName, trigger } = preloadQueue.shift();
      this.loadComponent(componentName, trigger);
    }
  }

  observeElement(element, componentName) {
    if (this.intersectionObserver && element) {
      element.dataset.componentName = componentName;
      this.intersectionObserver.observe(element);
    }
  }

  unobserveElement(element) {
    if (this.intersectionObserver && element) {
      this.intersectionObserver.unobserve(element);
    }
  }

  preloadByPriority() {
    // Load high priority components first
    LoadingPriority.high.forEach(component => {
      this.loadComponent(component.displayName, 'priority-high');
    });

    // Schedule medium priority for idle time
    setTimeout(() => {
      LoadingPriority.medium.forEach(component => {
        this.loadComponent(component.displayName, 'priority-medium');
      });
    }, 1000);

    // Schedule low priority for later
    setTimeout(() => {
      LoadingPriority.low.forEach(component => {
        this.loadComponent(component.displayName, 'priority-low');
      });
    }, 3000);
  }

  preloadByFeature(featureName) {
    const components = FeatureComponents[featureName];
    if (components) {
      components.forEach(component => {
        this.loadComponent(component.displayName, `feature-${featureName}`);
      });
    }
  }

  destroy() {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    if (this.idleCallback) {
      cancelIdleCallback(this.idleCallback);
    }
  }
}

// Global instance
const progressiveLoader = new ProgressiveLoadingManager();

/**
 * Hook for progressive component loading
 */
export const useProgressiveLoading = (options = {}) => {
  const {
    strategy = 'priority',
    features = [],
    components = [],
    autoStart = true,
    delay = 0
  } = options;

  const [loadingState, setLoadingState] = useState({
    loaded: Array.from(loadedComponents),
    loading: Array.from(loadingComponents),
    total: 0
  });

  useEffect(() => {
    const updateLoadingState = () => {
      setLoadingState({
        loaded: Array.from(loadedComponents),
        loading: Array.from(loadingComponents),
        total: Object.values(FeatureComponents).flat().length
      });
    };

    // Update state periodically
    const interval = setInterval(updateLoadingState, 1000);

    if (autoStart) {
      const timer = setTimeout(() => {
        switch (strategy) {
          case 'priority':
            progressiveLoader.preloadByPriority();
            break;
          case 'features':
            features.forEach(feature => {
              progressiveLoader.preloadByFeature(feature);
            });
            break;
          case 'components':
            components.forEach(componentName => {
              progressiveLoader.loadComponent(componentName, 'manual');
            });
            break;
        }
      }, delay);

      return () => {
        clearTimeout(timer);
        clearInterval(interval);
      };
    }

    return () => clearInterval(interval);
  }, [strategy, features, components, autoStart, delay]);

  return {
    ...loadingState,
    loadComponent: (componentName) => progressiveLoader.loadComponent(componentName, 'manual'),
    loadFeature: (featureName) => progressiveLoader.preloadByFeature(featureName),
    isLoaded: (componentName) => loadedComponents.has(componentName),
    isLoading: (componentName) => loadingComponents.has(componentName)
  };
};

/**
 * Hook for viewport-based component loading
 */
export const useViewportLoading = (componentName, options = {}) => {
  const elementRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(loadedComponents.has(componentName));

  useEffect(() => {
    const element = elementRef.current;
    if (element && componentName) {
      progressiveLoader.observeElement(element, componentName);

      // Check if component gets loaded
      const checkLoaded = () => {
        if (loadedComponents.has(componentName)) {
          setIsLoaded(true);
        }
      };

      const interval = setInterval(checkLoaded, 500);

      return () => {
        progressiveLoader.unobserveElement(element);
        clearInterval(interval);
      };
    }
  }, [componentName]);

  return {
    ref: elementRef,
    isVisible,
    isLoaded,
    loadComponent: () => progressiveLoader.loadComponent(componentName, 'manual')
  };
};

/**
 * Hook for interaction-based component loading
 */
export const useInteractionLoading = (componentName, events = ['mouseenter', 'focus']) => {
  const elementRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(loadedComponents.has(componentName));
  const hasTriggered = useRef(false);

  useEffect(() => {
    const element = elementRef.current;
    if (element && componentName && !hasTriggered.current) {
      const handleInteraction = () => {
        if (!hasTriggered.current) {
          hasTriggered.current = true;
          progressiveLoader.loadComponent(componentName, 'interaction');
        }
      };

      events.forEach(event => {
        element.addEventListener(event, handleInteraction, { passive: true });
      });

      return () => {
        events.forEach(event => {
          element.removeEventListener(event, handleInteraction);
        });
      };
    }
  }, [componentName, events]);

  useEffect(() => {
    const checkLoaded = () => {
      if (loadedComponents.has(componentName)) {
        setIsLoaded(true);
      }
    };

    const interval = setInterval(checkLoaded, 500);
    return () => clearInterval(interval);
  }, [componentName]);

  return {
    ref: elementRef,
    isLoaded,
    loadComponent: () => progressiveLoader.loadComponent(componentName, 'manual')
  };
};

/**
 * Component wrapper for progressive loading
 */
export const ProgressiveWrapper = ({ 
  children, 
  componentName, 
  strategy = 'viewport',
  fallback = null,
  ...props 
}) => {
  const viewportHook = useViewportLoading(componentName);
  const interactionHook = useInteractionLoading(componentName);
  
  const hook = strategy === 'viewport' ? viewportHook : interactionHook;
  
  return (
    <div ref={hook.ref} {...props}>
      {hook.isLoaded ? children : fallback}
    </div>
  );
};

// Export the manager instance for direct use
export { progressiveLoader };

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    progressiveLoader.destroy();
  });
}
