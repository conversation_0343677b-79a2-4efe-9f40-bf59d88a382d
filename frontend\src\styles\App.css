/* App specific styles */
.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.App-header {
  background-color: #001529;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: rgb(242, 243, 248);
}

.App-logo {
  height: 32px;
  margin: 16px;
}

.App-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.App-nav {
  background-color: #f0f2f5;
  padding: 8px 16px;
}

.App-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 16px;
}

.App-nav a {
  color: #1890ff;
  text-decoration: none;
  font-weight: 500;
}

.App-nav a:hover {
  color: #40a9ff;
}

.App-footer {
  text-align: center;
  padding: 12px;
  background: #f0f2f5;
  color: rgba(0, 0, 0, 0.65);
}

/* Main content area */
.main-content {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px - 48px);
}

/* Card styles */
.app-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.app-card-title {
  font-size: 16px;
  font-weight: 500;
}

/* Form styles */
.app-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

/* Button styles */
.action-button {
  margin-left: 8px;
}

/* Offline mode banner */
.offline-mode-banner {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  padding: 8px 16px;
  margin: 16px 0;
  border-radius: 4px;
  text-align: center;
  color: #ad8b00;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .App-nav ul {
    flex-direction: column;
    gap: 8px;
  }

  .main-content {
    padding: 12px;
  }
}

/* Tutorial styles */
.tutorial-start-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.3s;
}

.tutorial-start-button:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateY(-2px);
}

/* Tutorial highlight animation enhancement */
@keyframes tutorial-pulse {
  0% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(24, 144, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0); }
}

.tutorial-highlight {
  animation: tutorial-pulse 2s infinite;
}

/* First-time user indicator */
.new-user-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f5222d;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
