/**
 * Data Manager Utility
 * 
 * This utility provides functions for managing data flow between components,
 * optimizing Redux store usage, and handling data operations.
 */

import store from '../redux/store';
import { message } from 'antd';

/**
 * Cache for storing temporary data that doesn't need to be in Redux
 */
const dataCache = {
  temporary: new Map(),
  persistent: new Map(),
  expiring: new Map(),
  timestamps: new Map()
};

/**
 * Set data in the cache
 * @param {string} key - The key to store the data under
 * @param {any} data - The data to store
 * @param {Object} options - Options for storing the data
 * @param {string} options.type - The type of cache to use ('temporary', 'persistent', or 'expiring')
 * @param {number} options.expiresIn - Time in milliseconds after which the data expires (for 'expiring' type)
 */
export const setCache = (key, data, options = { type: 'temporary', expiresIn: 3600000 }) => {
  const { type, expiresIn } = options;
  
  switch (type) {
    case 'persistent':
      dataCache.persistent.set(key, data);
      try {
        localStorage.setItem(`cache_${key}`, JSON.stringify(data));
      } catch (error) {
        console.warn('Failed to save to localStorage:', error);
      }
      break;
      
    case 'expiring':
      dataCache.expiring.set(key, data);
      dataCache.timestamps.set(key, Date.now() + expiresIn);
      break;
      
    case 'temporary':
    default:
      dataCache.temporary.set(key, data);
      break;
  }
};

/**
 * Get data from the cache
 * @param {string} key - The key to retrieve data for
 * @param {string} type - The type of cache to use ('temporary', 'persistent', or 'expiring')
 * @returns {any} The cached data or null if not found
 */
export const getCache = (key, type = 'temporary') => {
  switch (type) {
    case 'persistent': {
      // Try memory cache first
      if (dataCache.persistent.has(key)) {
        return dataCache.persistent.get(key);
      }
      
      // Try localStorage
      try {
        const data = localStorage.getItem(`cache_${key}`);
        if (data) {
          const parsed = JSON.parse(data);
          // Update memory cache
          dataCache.persistent.set(key, parsed);
          return parsed;
        }
      } catch (error) {
        console.warn('Failed to retrieve from localStorage:', error);
      }
      return null;
    }
      
    case 'expiring': {
      if (dataCache.expiring.has(key)) {
        const timestamp = dataCache.timestamps.get(key);
        if (timestamp && timestamp > Date.now()) {
          return dataCache.expiring.get(key);
        } else {
          // Expired, clean up
          dataCache.expiring.delete(key);
          dataCache.timestamps.delete(key);
        }
      }
      return null;
    }
      
    case 'temporary':
    default:
      return dataCache.temporary.get(key) || null;
  }
};

/**
 * Clear data from the cache
 * @param {string} key - The key to clear (if not provided, clears all data of the specified type)
 * @param {string} type - The type of cache to clear ('temporary', 'persistent', 'expiring', or 'all')
 */
export const clearCache = (key, type = 'temporary') => {
  if (key) {
    // Clear specific key
    switch (type) {
      case 'persistent':
        dataCache.persistent.delete(key);
        try {
          localStorage.removeItem(`cache_${key}`);
        } catch (error) {
          console.warn('Failed to remove from localStorage:', error);
        }
        break;
        
      case 'expiring':
        dataCache.expiring.delete(key);
        dataCache.timestamps.delete(key);
        break;
        
      case 'all':
        dataCache.temporary.delete(key);
        dataCache.persistent.delete(key);
        dataCache.expiring.delete(key);
        dataCache.timestamps.delete(key);
        try {
          localStorage.removeItem(`cache_${key}`);
        } catch (error) {
          console.warn('Failed to remove from localStorage:', error);
        }
        break;
        
      case 'temporary':
      default:
        dataCache.temporary.delete(key);
        break;
    }
  } else {
    // Clear all data of specified type
    switch (type) {
      case 'persistent':
        dataCache.persistent.clear();
        try {
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('cache_')) {
              localStorage.removeItem(key);
            }
          });
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
        break;
        
      case 'expiring':
        dataCache.expiring.clear();
        dataCache.timestamps.clear();
        break;
        
      case 'all':
        dataCache.temporary.clear();
        dataCache.persistent.clear();
        dataCache.expiring.clear();
        dataCache.timestamps.clear();
        try {
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('cache_')) {
              localStorage.removeItem(key);
            }
          });
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
        break;
        
      case 'temporary':
      default:
        dataCache.temporary.clear();
        break;
    }
  }
};

/**
 * Get data from Redux store with error handling
 * @param {Function} selector - Redux selector function
 * @param {any} defaultValue - Default value to return if selector fails
 * @returns {any} The selected data or default value
 */
export const getStoreData = (selector, defaultValue = null) => {
  try {
    return selector(store.getState());
  } catch (error) {
    console.error('Error accessing Redux store:', error);
    return defaultValue;
  }
};

/**
 * Dispatch an action to Redux store with error handling
 * @param {Object|Function} action - Redux action or thunk
 * @returns {Promise<any>} Promise that resolves with the result of the dispatch
 */
export const dispatchAction = async (action) => {
  try {
    return await store.dispatch(action);
  } catch (error) {
    console.error('Error dispatching action:', error);
    message.error('An error occurred while updating data');
    throw error;
  }
};

/**
 * Batch multiple Redux actions into a single update
 * @param {Array<Object|Function>} actions - Array of Redux actions or thunks
 * @returns {Promise<Array<any>>} Promise that resolves with results of all dispatches
 */
export const batchActions = async (actions) => {
  try {
    const results = [];
    // Start all dispatches
    const promises = actions.map(action => store.dispatch(action));
    // Wait for all to complete
    return await Promise.all(promises);
  } catch (error) {
    console.error('Error in batch actions:', error);
    message.error('An error occurred while updating multiple items');
    throw error;
  }
};

export default {
  setCache,
  getCache,
  clearCache,
  getStoreData,
  dispatchAction,
  batchActions
};
