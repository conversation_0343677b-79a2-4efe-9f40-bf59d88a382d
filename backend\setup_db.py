#!/usr/bin/env python
"""
Database Setup Script

This script helps set up the database for the Django application.
It can be used as an alternative to Django's built-in database commands
when PostgreSQL client tools are not available.
"""

import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app_builder_201.settings')
    django.setup()

def check_database_connection():
    """Check if database connection is working"""
    from django.db import connection
    from django.core.exceptions import OperationalError
    
    try:
        # Try to connect to the database
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("✅ Database connection successful")
                return True
    except OperationalError as e:
        print(f"❌ Database connection failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def run_migrations():
    """Run Django migrations"""
    try:
        print("Running migrations...")
        execute_from_command_line(['manage.py', 'migrate'])
        print("✅ Migrations completed successfully")
        return True
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def create_superuser():
    """Create a superuser if it doesn't exist"""
    try:
        from django.contrib.auth.models import User
        
        username = os.environ.get('DJANGO_SUPERUSER_USERNAME', 'admin')
        email = os.environ.get('DJANGO_SUPERUSER_EMAIL', '<EMAIL>')
        password = os.environ.get('DJANGO_SUPERUSER_PASSWORD', 'admin123')
        
        if not User.objects.filter(username=username).exists():
            User.objects.create_superuser(username, email, password)
            print(f"✅ Superuser '{username}' created successfully")
        else:
            print(f"ℹ️ Superuser '{username}' already exists")
        return True
    except Exception as e:
        print(f"❌ Failed to create superuser: {e}")
        return False

def setup_initial_data():
    """Setup initial data for the application"""
    try:
        from my_app.models import App
        
        # Create a default app if none exists
        if not App.objects.exists():
            default_app_data = {
                "components": [],
                "layouts": [],
                "styles": {},
                "data": {}
            }
            
            App.objects.create(
                name="Default App",
                app_data=str(default_app_data)
            )
            print("✅ Default app data created")
        else:
            print("ℹ️ App data already exists")
        return True
    except Exception as e:
        print(f"❌ Failed to setup initial data: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Starting database setup...")
    
    # Setup Django
    setup_django()
    
    # Check database connection
    if not check_database_connection():
        print("❌ Cannot proceed without database connection")
        
        # Suggest using SQLite as fallback
        print("\n💡 Suggestion: Try using SQLite instead of PostgreSQL")
        print("   Set USE_POSTGRES=false in your environment or docker-compose.yml")
        print("   This will use SQLite which doesn't require PostgreSQL client tools")
        
        return False
    
    # Run migrations
    if not run_migrations():
        print("❌ Cannot proceed without successful migrations")
        return False
    
    # Create superuser
    create_superuser()
    
    # Setup initial data
    setup_initial_data()
    
    print("\n✅ Database setup completed successfully!")
    print("\nYou can now:")
    print("  - Access the admin panel at http://localhost:8000/admin/")
    print("  - Use the API endpoints")
    print("  - Start developing your application")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
