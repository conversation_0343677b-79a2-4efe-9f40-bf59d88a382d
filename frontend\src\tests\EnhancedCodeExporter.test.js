/**
 * Tests for Enhanced Code Exporter component
 * Comprehensive testing of export functionality, UI interactions, and validation
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';
import EnhancedCodeExporter from '../components/enhanced/EnhancedCodeExporter';
import { generateCode } from '../utils/code_generator';

// Mock the code generator
jest.mock('../utils/code_generator', () => ({
  generateCode: jest.fn()
}));

// Mock fetch
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Create a mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: (state = { components: [], layouts: [] }, action) => state,
      themes: (state = { activeTheme: 'default', themes: [] }, action) => state,
      websocket: (state = { connected: false }, action) => state,
      templates: (state = {}, action) => state,
      ui: (state = { currentView: 'export' }, action) => state
    },
    preloadedState: {
      app: { components: [], layouts: [] },
      themes: { activeTheme: 'default', themes: [] },
      websocket: { connected: false },
      templates: {},
      ui: { currentView: 'export' },
      ...initialState
    }
  });
};

const renderWithProvider = (component, initialState = {}) => {
  const store = createMockStore(initialState);
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('EnhancedCodeExporter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    generateCode.mockReturnValue('// Generated code');
    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        status: 'success',
        type: 'single-file',
        code: '// Generated code'
      })
    });
  });

  test('renders export configuration interface', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    expect(screen.getByText('Enhanced Code Exporter')).toBeInTheDocument();
    expect(screen.getByText('Configure')).toBeInTheDocument();
    expect(screen.getByText('Preview')).toBeInTheDocument();
    expect(screen.getByText('History')).toBeInTheDocument();
  });

  test('displays export format options', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    // Check for export format selection
    expect(screen.getByText('Export Format')).toBeInTheDocument();
    
    // Check for some specific formats
    const formatSelect = screen.getByRole('combobox');
    fireEvent.click(formatSelect);
    
    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('Vue.js')).toBeInTheDocument();
    expect(screen.getByText('Angular')).toBeInTheDocument();
  });

  test('shows project structure options', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    expect(screen.getByText('Project Structure')).toBeInTheDocument();
    expect(screen.getByText('Single File')).toBeInTheDocument();
    expect(screen.getByText('Multiple Files')).toBeInTheDocument();
    expect(screen.getByText('Full Project')).toBeInTheDocument();
  });

  test('displays style framework options', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    expect(screen.getByText('Style Framework')).toBeInTheDocument();
  });

  test('shows basic configuration options', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    expect(screen.getByText('TypeScript Support')).toBeInTheDocument();
    expect(screen.getByText('Accessibility Features')).toBeInTheDocument();
    expect(screen.getByText('Include Tests')).toBeInTheDocument();
    expect(screen.getByText('Storybook Stories')).toBeInTheDocument();
  });

  test('handles export format change', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    const formatSelect = screen.getByRole('combobox');
    fireEvent.change(formatSelect, { target: { value: 'vue' } });
    
    // The component should update its internal state
    // This is tested indirectly through other interactions
  });

  test('handles option toggles', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    const typescriptCheckbox = screen.getByRole('checkbox', { name: /TypeScript Support/i });
    fireEvent.click(typescriptCheckbox);
    
    expect(typescriptCheckbox).toBeChecked();
  });

  test('generates code preview', async () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    // Switch to preview tab
    const previewTab = screen.getByText('Preview');
    fireEvent.click(previewTab);
    
    await waitFor(() => {
      expect(generateCode).toHaveBeenCalled();
    });
  });

  test('handles export button click', async () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    const exportButton = screen.getByText('Export Application');
    
    await act(async () => {
      fireEvent.click(exportButton);
    });
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/enhanced-export/', expect.any(Object));
    });
  });

  test('displays export history', () => {
    const mockHistory = [
      {
        id: 1,
        format: 'react',
        timestamp: new Date().toISOString(),
        status: 'success',
        size: 1000
      }
    ];
    
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockHistory));
    
    renderWithProvider(<EnhancedCodeExporter />);
    
    // Switch to history tab
    const historyTab = screen.getByText('History');
    fireEvent.click(historyTab);
    
    expect(screen.getByText('Export History')).toBeInTheDocument();
  });

  test('handles WebSocket integration', () => {
    const storeWithWebSocket = {
      websocket: { connected: true }
    };
    
    renderWithProvider(<EnhancedCodeExporter />, storeWithWebSocket);
    
    // Component should recognize WebSocket connection
    // This is tested indirectly through the component's behavior
  });

  test('shows loading state during export', async () => {
    // Mock a delayed response
    fetch.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ status: 'success', code: '// Code' })
        }), 100)
      )
    );
    
    renderWithProvider(<EnhancedCodeExporter />);
    
    const exportButton = screen.getByText('Export Application');
    
    await act(async () => {
      fireEvent.click(exportButton);
    });
    
    // Should show loading state
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles export errors gracefully', async () => {
    fetch.mockRejectedValue(new Error('Export failed'));
    
    renderWithProvider(<EnhancedCodeExporter />);
    
    const exportButton = screen.getByText('Export Application');
    
    await act(async () => {
      fireEvent.click(exportButton);
    });
    
    // Should handle error without crashing
    // Error handling is typically done through message notifications
  });

  test('validates export options', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    // Test that incompatible options are handled
    // For example, TypeScript with HTML format
    const formatSelect = screen.getByRole('combobox');
    fireEvent.change(formatSelect, { target: { value: 'html' } });
    
    const typescriptCheckbox = screen.getByRole('checkbox', { name: /TypeScript Support/i });
    fireEvent.click(typescriptCheckbox);
    
    // Component should handle this validation
  });

  test('supports batch export functionality', async () => {
    const storeWithMultipleApps = {
      app: {
        components: [
          { id: 'comp1', type: 'Button' },
          { id: 'comp2', type: 'Text' }
        ],
        layouts: [
          { id: 'layout1', components: ['comp1', 'comp2'] }
        ]
      }
    };
    
    renderWithProvider(<EnhancedCodeExporter />, storeWithMultipleApps);
    
    // Test batch export functionality if available
    // This would depend on the specific UI implementation
  });

  test('integrates with template system', () => {
    const storeWithTemplates = {
      templates: {
        layoutTemplates: [
          { id: 'template1', name: 'Header Layout' }
        ],
        appTemplates: [
          { id: 'template2', name: 'Dashboard App' }
        ]
      }
    };
    
    renderWithProvider(<EnhancedCodeExporter />, storeWithTemplates);
    
    // Component should recognize available templates
    // This is tested through the component's behavior with template data
  });

  test('preserves export history in localStorage', async () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    const exportButton = screen.getByText('Export Application');
    
    await act(async () => {
      fireEvent.click(exportButton);
    });
    
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'exportHistory',
        expect.any(String)
      );
    });
  });

  test('handles different project structures', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    // Test single file structure
    const singleFileRadio = screen.getByRole('radio', { name: /Single File/i });
    fireEvent.click(singleFileRadio);
    
    // Test multi-file structure
    const multiFileRadio = screen.getByRole('radio', { name: /Multiple Files/i });
    fireEvent.click(multiFileRadio);
    
    // Test full project structure
    const fullProjectRadio = screen.getByRole('radio', { name: /Full Project/i });
    fireEvent.click(fullProjectRadio);
    
    // Each should update the component's internal state
  });

  test('supports advanced export options', () => {
    renderWithProvider(<EnhancedCodeExporter />);
    
    // Check for advanced options panel
    const advancedPanel = screen.getByText('Advanced Options');
    fireEvent.click(advancedPanel);
    
    // Should show package manager options
    expect(screen.getByText('Package Manager:')).toBeInTheDocument();
    expect(screen.getByText('Bundler:')).toBeInTheDocument();
  });

  test('validates generated code quality', async () => {
    generateCode.mockReturnValue(`
      import React from 'react';
      
      const App = () => {
        return (
          <div className="app" role="main">
            <button>Click me</button>
          </div>
        );
      };
      
      export default App;
    `);
    
    renderWithProvider(<EnhancedCodeExporter />);
    
    // Switch to preview tab
    const previewTab = screen.getByText('Preview');
    fireEvent.click(previewTab);
    
    await waitFor(() => {
      const codePreview = screen.getByText(/import React/);
      expect(codePreview).toBeInTheDocument();
    });
    
    // Validate that the generated code contains expected elements
    expect(screen.getByText(/export default App/)).toBeInTheDocument();
    expect(screen.getByText(/role="main"/)).toBeInTheDocument();
  });
});

describe('Export Service Integration', () => {
  test('handles API responses correctly', async () => {
    const mockResponse = {
      status: 'success',
      type: 'multi-file',
      files: {
        'App.jsx': '// React component',
        'package.json': '{"name": "test-app"}'
      }
    };
    
    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });
    
    renderWithProvider(<EnhancedCodeExporter />);
    
    const exportButton = screen.getByText('Export Application');
    
    await act(async () => {
      fireEvent.click(exportButton);
    });
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        '/api/enhanced-export/',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
    });
  });

  test('handles network errors', async () => {
    fetch.mockRejectedValue(new Error('Network error'));
    
    renderWithProvider(<EnhancedCodeExporter />);
    
    const exportButton = screen.getByText('Export Application');
    
    await act(async () => {
      fireEvent.click(exportButton);
    });
    
    // Component should handle network errors gracefully
    // This is typically done through error boundaries or try-catch blocks
  });
});
