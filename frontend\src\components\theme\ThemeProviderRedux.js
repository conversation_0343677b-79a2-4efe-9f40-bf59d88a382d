import React, { createContext, useContext, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { ThemeProvider as StyledThemeProvider, createGlobalStyle } from 'styled-components';
import { message } from 'antd';
import { 
  setActiveTheme, 
  selectThemes, 
  selectActiveTheme, 
  selectActiveThemeData,
  selectUserPreferences
} from '../../redux/slices/themeSlice';

// Create a context for theme switching
export const ThemeContext = createContext({
  currentTheme: null,
  setThemeById: () => { },
  availableThemes: [],
  themeMode: 'light',
  toggleThemeMode: () => { },
  setThemeMode: () => { },
});

// Create a global style component
const GlobalStyle = createGlobalStyle`
  :root {
    --primary-color: ${props => props.theme.primaryColor || '#2563EB'};
    --primary-light: ${props => props.theme.primaryColor ? adjustColor(props.theme.primaryColor, 20) : '#4F85F6'};
    --primary-dark: ${props => props.theme.primaryColor ? adjustColor(props.theme.primaryColor, -20) : '#1A47B8'};
    --secondary-color: ${props => props.theme.secondaryColor || '#10B981'};
    --background-color: ${props => props.theme.backgroundColor || '#FFFFFF'};
    --text-color: ${props => props.theme.textColor || '#111827'};
    --font-family: ${props => props.theme.fontFamily || 'Inter, sans-serif'};
  }

  body {
    margin: 0;
    padding: 0;
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family);
    color: var(--text-color);
  }

  a {
    color: var(--primary-color);
    text-decoration: none;
  }

  a:hover {
    color: var(--primary-light);
  }

  button {
    font-family: var(--font-family);
  }

  .ant-btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .ant-btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
  }
`;

// Helper function to adjust color brightness
function adjustColor(color, amount) {
  // Remove the # if it exists
  color = color.replace('#', '');
  
  // Parse the color
  let r = parseInt(color.substring(0, 2), 16);
  let g = parseInt(color.substring(2, 4), 16);
  let b = parseInt(color.substring(4, 6), 16);
  
  // Adjust the color
  r = Math.max(0, Math.min(255, r + amount));
  g = Math.max(0, Math.min(255, g + amount));
  b = Math.max(0, Math.min(255, b + amount));
  
  // Convert back to hex
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

// Custom hook to use the theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

const ThemeProviderRedux = ({ children }) => {
  const dispatch = useDispatch();
  const themes = useSelector(selectThemes);
  const activeThemeId = useSelector(selectActiveTheme);
  const activeTheme = useSelector(selectActiveThemeData);
  const userPreferences = useSelector(selectUserPreferences);

  // Set theme by ID
  const setThemeById = (themeId) => {
    if (themes.some(theme => theme.id === themeId)) {
      dispatch(setActiveTheme(themeId));
      message.success(`Theme "${themes.find(theme => theme.id === themeId)?.name}" applied`);
    } else {
      message.error(`Theme with ID "${themeId}" not found`);
    }
  };

  // Toggle between light and dark themes
  const toggleThemeMode = () => {
    const currentMode = activeTheme.backgroundColor === '#FFFFFF' ? 'light' : 'dark';
    const newMode = currentMode === 'light' ? 'dark' : 'light';
    
    // Find a theme that matches the new mode
    const themeForMode = themes.find(theme => {
      const themeMode = theme.backgroundColor === '#FFFFFF' ? 'light' : 'dark';
      return themeMode === newMode;
    });
    
    if (themeForMode) {
      dispatch(setActiveTheme(themeForMode.id));
      message.success(`Switched to ${newMode} mode`);
    }
  };

  // Set theme mode (light or dark)
  const handleSetThemeMode = (mode) => {
    // Find a theme that matches the requested mode
    const themeForMode = themes.find(theme => {
      const themeMode = theme.backgroundColor === '#FFFFFF' ? 'light' : 'dark';
      return themeMode === mode;
    });
    
    if (themeForMode) {
      dispatch(setActiveTheme(themeForMode.id));
    }
  };

  // Determine current theme mode
  const themeMode = activeTheme.backgroundColor === '#FFFFFF' ? 'light' : 'dark';

  // Apply theme to document body
  useEffect(() => {
    document.body.dataset.theme = themeMode;
  }, [themeMode]);

  // Load user preference on mount
  useEffect(() => {
    if (userPreferences.savedTheme && userPreferences.autoApplyTheme) {
      dispatch(setActiveTheme(userPreferences.savedTheme));
    }
  }, [dispatch, userPreferences.savedTheme, userPreferences.autoApplyTheme]);

  return (
    <ThemeContext.Provider value={{
      currentTheme: activeTheme,
      setThemeById,
      availableThemes: themes,
      themeMode,
      toggleThemeMode,
      setThemeMode: handleSetThemeMode,
    }}>
      <StyledThemeProvider theme={activeTheme}>
        <GlobalStyle />
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};

export default ThemeProviderRedux;
