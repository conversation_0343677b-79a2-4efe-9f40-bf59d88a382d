import React, { useEffect, useRef, useCallback } from 'react';
import { message } from 'antd';
import { useCollaboration } from '../../contexts/CollaborationContext';
import { useAuth } from '../../contexts/AuthContext';

// Hook for collaborative drag and drop functionality
export const useCollaborativeDragDrop = (components, setComponents) => {
  const { 
    sendOperation, 
    isConnected, 
    updatePresence 
  } = useCollaboration();
  const { user } = useAuth();
  
  const pendingOperationsRef = useRef(new Map());
  const dragStateRef = useRef({
    isDragging: false,
    draggedComponent: null,
    dragStartPosition: null
  });

  // Send component operation to other users
  const broadcastOperation = useCallback((operationType, targetId, operationData) => {
    if (!isConnected) return;

    try {
      sendOperation(operationType, targetId, {
        ...operationData,
        userId: user?.id,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Failed to broadcast operation:', error);
      message.error('Failed to sync changes with collaborators');
    }
  }, [sendOperation, isConnected, user]);

  // Handle component addition
  const addComponent = useCallback((componentData, position = null) => {
    const newComponent = {
      ...componentData,
      id: `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      position: position || { x: 100, y: 100 },
      createdBy: user?.id,
      createdAt: new Date().toISOString()
    };

    // Update local state immediately for responsiveness
    setComponents(prev => [...prev, newComponent]);

    // Broadcast to other users
    broadcastOperation('component_add', newComponent.id, {
      component: newComponent,
      position: newComponent.position
    });

    return newComponent;
  }, [setComponents, broadcastOperation, user]);

  // Handle component update
  const updateComponent = useCallback((componentId, updates) => {
    // Update local state immediately
    setComponents(prev => 
      prev.map(comp => 
        comp.id === componentId 
          ? { ...comp, ...updates, updatedAt: new Date().toISOString() }
          : comp
      )
    );

    // Broadcast to other users
    broadcastOperation('component_update', componentId, {
      updates: {
        ...updates,
        updatedAt: new Date().toISOString()
      }
    });
  }, [setComponents, broadcastOperation]);

  // Handle component deletion
  const deleteComponent = useCallback((componentId) => {
    // Update local state immediately
    setComponents(prev => prev.filter(comp => comp.id !== componentId));

    // Broadcast to other users
    broadcastOperation('component_delete', componentId, {
      deletedAt: new Date().toISOString()
    });
  }, [setComponents, broadcastOperation]);

  // Handle component move
  const moveComponent = useCallback((componentId, newPosition) => {
    // Update local state immediately
    setComponents(prev => 
      prev.map(comp => 
        comp.id === componentId 
          ? { ...comp, position: newPosition, updatedAt: new Date().toISOString() }
          : comp
      )
    );

    // Broadcast to other users
    broadcastOperation('component_move', componentId, {
      position: newPosition,
      updatedAt: new Date().toISOString()
    });
  }, [setComponents, broadcastOperation]);

  // Enhanced drag handlers with collaboration support
  const handleDragStart = useCallback((event, component) => {
    dragStateRef.current = {
      isDragging: true,
      draggedComponent: component,
      dragStartPosition: component.position
    };

    // Update presence to show what user is dragging
    updatePresence({
      selection: {
        component_id: component.id,
        action: 'dragging'
      }
    });

    // Set drag data
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/json', JSON.stringify(component));
      event.dataTransfer.effectAllowed = 'move';
    }
  }, [updatePresence]);

  const handleDragEnd = useCallback((event) => {
    const dragState = dragStateRef.current;
    
    if (dragState.isDragging && dragState.draggedComponent) {
      // Clear presence
      updatePresence({
        selection: null
      });

      // Reset drag state
      dragStateRef.current = {
        isDragging: false,
        draggedComponent: null,
        dragStartPosition: null
      };
    }
  }, [updatePresence]);

  const handleDrop = useCallback((event, dropZone) => {
    event.preventDefault();
    
    const dragState = dragStateRef.current;
    
    try {
      // Get drop position
      const rect = event.currentTarget.getBoundingClientRect();
      const dropPosition = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };

      // Handle component from palette
      const paletteData = event.dataTransfer.getData('application/json');
      if (paletteData) {
        const componentData = JSON.parse(paletteData);
        
        if (componentData.fromPalette) {
          // Adding new component from palette
          addComponent(componentData, dropPosition);
        } else if (dragState.draggedComponent) {
          // Moving existing component
          moveComponent(dragState.draggedComponent.id, dropPosition);
        }
      }
    } catch (error) {
      console.error('Error handling drop:', error);
      message.error('Failed to drop component');
    }

    handleDragEnd(event);
  }, [addComponent, moveComponent, handleDragEnd]);

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle incoming operations from other users
  useEffect(() => {
    // This would be connected to the collaboration service
    // to receive real-time updates from other users
    
    const handleRemoteOperation = (operation) => {
      // Skip operations from current user
      if (operation.userId === user?.id) return;

      const { operation_type, target_id, operation_data } = operation;

      switch (operation_type) {
        case 'component_add':
          setComponents(prev => {
            // Check if component already exists
            if (prev.find(c => c.id === target_id)) return prev;
            return [...prev, operation_data.component];
          });
          break;

        case 'component_update':
          setComponents(prev => 
            prev.map(comp => 
              comp.id === target_id 
                ? { ...comp, ...operation_data.updates }
                : comp
            )
          );
          break;

        case 'component_delete':
          setComponents(prev => prev.filter(comp => comp.id !== target_id));
          break;

        case 'component_move':
          setComponents(prev => 
            prev.map(comp => 
              comp.id === target_id 
                ? { ...comp, position: operation_data.position }
                : comp
            )
          );
          break;

        default:
          console.warn('Unknown operation type:', operation_type);
      }
    };

    // In a real implementation, this would subscribe to collaboration events
    // For now, we'll just set up the handler structure
    
    return () => {
      // Cleanup subscription
    };
  }, [setComponents, user]);

  return {
    // Component management
    addComponent,
    updateComponent,
    deleteComponent,
    moveComponent,
    
    // Drag and drop handlers
    handleDragStart,
    handleDragEnd,
    handleDrop,
    handleDragOver,
    
    // State
    isDragging: dragStateRef.current.isDragging,
    draggedComponent: dragStateRef.current.draggedComponent
  };
};

// Component wrapper that adds collaborative drag and drop support
export const CollaborativeComponent = ({ 
  component, 
  children, 
  onUpdate, 
  onDelete, 
  onSelect,
  isSelected = false,
  collaborativeDragDrop 
}) => {
  const { 
    handleDragStart, 
    handleDragEnd, 
    updateComponent, 
    deleteComponent 
  } = collaborativeDragDrop;
  
  const { updatePresence } = useCollaboration();

  const handleClick = useCallback((event) => {
    event.stopPropagation();
    onSelect?.(component);
    
    // Update presence to show selection
    updatePresence({
      selection: {
        component_id: component.id,
        bounds: event.currentTarget.getBoundingClientRect()
      }
    });
  }, [component, onSelect, updatePresence]);

  const handleDoubleClick = useCallback((event) => {
    event.stopPropagation();
    // Enter edit mode or show properties panel
    onUpdate?.(component);
  }, [component, onUpdate]);

  const handleContextMenu = useCallback((event) => {
    event.preventDefault();
    // Show context menu with collaborative options
  }, []);

  return (
    <div
      draggable
      onDragStart={(e) => handleDragStart(e, component)}
      onDragEnd={handleDragEnd}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onContextMenu={handleContextMenu}
      style={{
        position: 'absolute',
        left: component.position?.x || 0,
        top: component.position?.y || 0,
        cursor: 'move',
        border: isSelected ? '2px solid #1890ff' : '1px solid transparent',
        borderRadius: '4px',
        padding: '4px',
        background: isSelected ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
        transition: 'all 0.2s ease'
      }}
    >
      {children}
      
      {/* Show component info for debugging */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'absolute',
          top: '-20px',
          left: '0',
          fontSize: '10px',
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '2px 4px',
          borderRadius: '2px',
          whiteSpace: 'nowrap'
        }}>
          {component.id}
        </div>
      )}
    </div>
  );
};

// Drop zone component for collaborative canvas
export const CollaborativeDropZone = ({ 
  children, 
  onDrop, 
  collaborativeDragDrop,
  style = {} 
}) => {
  const { handleDrop, handleDragOver } = collaborativeDragDrop;

  return (
    <div
      onDrop={(e) => {
        handleDrop(e);
        onDrop?.(e);
      }}
      onDragOver={handleDragOver}
      style={{
        minHeight: '400px',
        position: 'relative',
        border: '2px dashed #d9d9d9',
        borderRadius: '8px',
        padding: '16px',
        ...style
      }}
    >
      {children}
    </div>
  );
};

export default {
  useCollaborativeDragDrop,
  CollaborativeComponent,
  CollaborativeDropZone
};
