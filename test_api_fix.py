#!/usr/bin/env python3
"""
Test script to verify the API key fix works correctly
"""

import os
import sys

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_api_config():
    """Test that API config loads without errors"""
    print("🧪 Testing API Configuration...")
    
    try:
        from core.config import get_api_config
        config = get_api_config()
        print("✅ API config loaded successfully")
        
        # Check if keys are properly handled
        print(f"   OpenAI Key: {'Present' if config.openai_api_key else 'None (development mode)'}")
        print(f"   AI Studio Key: {'Present' if config.ai_studio_api_key else 'None (development mode)'}")
        print(f"   Stability Key: {'Present' if config.stability_api_key else 'None (development mode)'}")
        print(f"   ElevenLabs Key: {'Present' if config.elevenlabs_api_key else 'None (development mode)'}")
        
        return True
    except Exception as e:
        print(f"❌ API config failed: {e}")
        return False

def test_ai_engines():
    """Test that AI engines can be created without errors"""
    print("\n🤖 Testing AI Engines...")
    
    try:
        from core.app_logic import AILayoutSuggestionsEngine, AIComponentCombinationEngine, TutorialAIPlugin
        
        # Test Layout Engine
        layout_engine = AILayoutSuggestionsEngine()
        print("✅ AILayoutSuggestionsEngine created successfully")
        
        # Test Component Engine
        component_engine = AIComponentCombinationEngine()
        print("✅ AIComponentCombinationEngine created successfully")
        
        # Test Tutorial Plugin
        tutorial_plugin = TutorialAIPlugin()
        print("✅ TutorialAIPlugin created successfully")
        
        return True
    except Exception as e:
        print(f"❌ AI engines failed: {e}")
        return False

def test_ai_functionality():
    """Test that AI functionality works with fallback"""
    print("\n🎯 Testing AI Functionality...")
    
    try:
        from core.app_logic import AILayoutSuggestionsEngine
        
        engine = AILayoutSuggestionsEngine()
        
        # Test with sample components
        sample_components = [
            {'type': 'button', 'id': 'btn1'},
            {'type': 'text', 'id': 'txt1'},
            {'type': 'card', 'id': 'card1'}
        ]
        
        suggestions = engine.suggest_layouts(sample_components)
        print(f"✅ Layout suggestions generated: {len(suggestions)} suggestions")
        
        # Test analysis
        analysis = engine.analyze_app_structure(sample_components)
        print(f"✅ App analysis completed: {analysis['app_type']} type detected")
        
        return True
    except Exception as e:
        print(f"❌ AI functionality failed: {e}")
        return False

def test_websocket_consumer():
    """Test that WebSocket consumer can be imported"""
    print("\n🔌 Testing WebSocket Consumer...")
    
    try:
        from websockets.consumers.ai_suggestions import AISuggestionsConsumer
        print("✅ AISuggestionsConsumer imported successfully")
        return True
    except Exception as e:
        print(f"❌ WebSocket consumer failed: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 API Key Fix Verification Test")
    print("=" * 60)
    
    tests = [
        ("API Configuration", test_api_config),
        ("AI Engines", test_ai_engines),
        ("AI Functionality", test_ai_functionality),
        ("WebSocket Consumer", test_websocket_consumer)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests passed! The API key fix is working correctly.")
        print("   You can now run the application without API key validation errors.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    print("=" * 60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
