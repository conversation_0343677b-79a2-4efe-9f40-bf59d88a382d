/**
 * WebSocket Proxy Test Script
 * Tests the fixed webpack proxy configuration for WebSocket error handling
 *
 * This script creates a simple HTTP server to test the proxy configuration
 * Run this in a browser console or use the HTML test file instead
 */

// This script is designed to run in a browser environment
// For Node.js testing, install 'ws' package first: npm install ws
if (typeof window === 'undefined') {
    console.log('❌ This script is designed to run in a browser environment.');
    console.log('📝 Please use the test-websocket-proxy-fix.html file instead.');
    console.log('🌐 Open http://localhost:3000/test-websocket-proxy-fix.html in your browser');
    process.exit(1);
}

// Test configuration
const TEST_CONFIG = {
    frontendUrl: 'ws://localhost:3000',
    backendUrl: 'ws://localhost:8000',
    testEndpoints: [
        '/ws/app_builder/',
        '/ws/test/',
        '/ws/invalid_endpoint/'
    ],
    timeout: 5000
};

/**
 * Test WebSocket connection through proxy
 */
async function testWebSocketProxy(endpoint) {
    return new Promise((resolve) => {
        console.log(`\n🔌 Testing WebSocket proxy: ${TEST_CONFIG.frontendUrl}${endpoint}`);

        const ws = new WebSocket(`${TEST_CONFIG.frontendUrl}${endpoint}`);
        const startTime = Date.now();
        let resolved = false;

        const resolveTest = (result) => {
            if (!resolved) {
                resolved = true;
                resolve(result);
            }
        };

        ws.on('open', () => {
            const duration = Date.now() - startTime;
            console.log(`✅ Connection opened successfully in ${duration}ms`);
            ws.close();
            resolveTest({ success: true, duration, endpoint });
        });

        ws.on('close', (code, reason) => {
            const duration = Date.now() - startTime;
            console.log(`🔒 Connection closed: code=${code}, reason=${reason || 'No reason'}, duration=${duration}ms`);
            if (!resolved) {
                resolveTest({ success: false, code, reason: reason.toString(), duration, endpoint });
            }
        });

        ws.on('error', (error) => {
            const duration = Date.now() - startTime;
            console.log(`❌ WebSocket error: ${error.message}, duration=${duration}ms`);
            resolveTest({ success: false, error: error.message, duration, endpoint });
        });

        // Timeout handling
        setTimeout(() => {
            if (!resolved) {
                const duration = Date.now() - startTime;
                console.log(`⏰ Connection timeout after ${duration}ms`);
                ws.close();
                resolveTest({ success: false, error: 'Timeout', duration, endpoint });
            }
        }, TEST_CONFIG.timeout);
    });
}

/**
 * Test direct backend connection
 */
async function testDirectBackend(endpoint) {
    return new Promise((resolve) => {
        console.log(`\n🎯 Testing direct backend: ${TEST_CONFIG.backendUrl}${endpoint}`);

        const ws = new WebSocket(`${TEST_CONFIG.backendUrl}${endpoint}`);
        const startTime = Date.now();
        let resolved = false;

        const resolveTest = (result) => {
            if (!resolved) {
                resolved = true;
                resolve(result);
            }
        };

        ws.on('open', () => {
            const duration = Date.now() - startTime;
            console.log(`✅ Direct backend connection opened in ${duration}ms`);
            ws.close();
            resolveTest({ success: true, duration, endpoint, direct: true });
        });

        ws.on('close', (code, reason) => {
            const duration = Date.now() - startTime;
            console.log(`🔒 Direct backend closed: code=${code}, reason=${reason || 'No reason'}`);
            if (!resolved) {
                resolveTest({ success: false, code, reason: reason.toString(), duration, endpoint, direct: true });
            }
        });

        ws.on('error', (error) => {
            const duration = Date.now() - startTime;
            console.log(`❌ Direct backend error: ${error.message}`);
            resolveTest({ success: false, error: error.message, duration, endpoint, direct: true });
        });

        setTimeout(() => {
            if (!resolved) {
                const duration = Date.now() - startTime;
                console.log(`⏰ Direct backend timeout after ${duration}ms`);
                ws.close();
                resolveTest({ success: false, error: 'Timeout', duration, endpoint, direct: true });
            }
        }, TEST_CONFIG.timeout);
    });
}

/**
 * Run all tests
 */
async function runTests() {
    console.log('🚀 Starting WebSocket Proxy Fix Tests');
    console.log('=====================================');

    const results = [];

    // Test each endpoint through proxy and direct
    for (const endpoint of TEST_CONFIG.testEndpoints) {
        try {
            // Test through proxy
            const proxyResult = await testWebSocketProxy(endpoint);
            results.push(proxyResult);

            // Test direct backend
            const directResult = await testDirectBackend(endpoint);
            results.push(directResult);

            // Wait a bit between tests
            await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
            console.error(`❌ Test failed for ${endpoint}:`, error.message);
            results.push({ success: false, error: error.message, endpoint });
        }
    }

    // Print summary
    console.log('\n📊 Test Results Summary');
    console.log('=======================');

    const proxyResults = results.filter(r => !r.direct);
    const directResults = results.filter(r => r.direct);

    console.log('\n🔌 Proxy Tests:');
    proxyResults.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration ? `${result.duration}ms` : 'N/A';
        console.log(`  ${status} ${result.endpoint} (${duration})`);
        if (!result.success && result.error) {
            console.log(`     Error: ${result.error}`);
        }
    });

    console.log('\n🎯 Direct Backend Tests:');
    directResults.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const duration = result.duration ? `${result.duration}ms` : 'N/A';
        console.log(`  ${status} ${result.endpoint} (${duration})`);
        if (!result.success && result.error) {
            console.log(`     Error: ${result.error}`);
        }
    });

    const successfulProxy = proxyResults.filter(r => r.success).length;
    const totalProxy = proxyResults.length;

    console.log(`\n📈 Proxy Success Rate: ${successfulProxy}/${totalProxy} (${Math.round(successfulProxy / totalProxy * 100)}%)`);

    if (successfulProxy > 0 || proxyResults.some(r => r.error && !r.error.includes('writeHead'))) {
        console.log('\n🎉 WebSocket proxy fix appears to be working!');
        console.log('   No "res.writeHead is not a function" errors detected.');
    } else {
        console.log('\n⚠️  WebSocket proxy may need further investigation.');
    }
}

// Run tests if this script is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests, testWebSocketProxy, testDirectBackend };
