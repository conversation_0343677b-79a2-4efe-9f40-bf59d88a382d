import { useEffect, useState, useRef, useCallback } from 'react';
import { initPerformanceMonitor } from '../utils/performanceMonitor';

/**
 * Hook for monitoring performance in React components.
 * 
 * @param {Object} options - Options for the performance monitor.
 * @param {boolean} options.enabled - Whether the performance monitor is enabled.
 * @param {boolean} options.autoMarkRenders - Whether to automatically mark component renders.
 * @param {boolean} options.autoMarkEffects - Whether to automatically mark effect runs.
 * @param {boolean} options.autoMarkEvents - Whether to automatically mark event handlers.
 * @param {number} options.reportInterval - Interval in milliseconds to generate performance reports.
 * @param {Function} options.onReport - Callback function to handle performance reports.
 * @returns {Object} Performance monitor API and state.
 */
const usePerformanceMonitor = ({
  enabled = process.env.NODE_ENV === 'development',
  autoMarkRenders = true,
  autoMarkEffects = true,
  autoMarkEvents = true,
  reportInterval = 10000,
  onReport = null,
} = {}) => {
  // State for the performance monitor
  const [isEnabled, setIsEnabled] = useState(enabled);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Refs for the performance monitor and component name
  const monitorRef = useRef(null);
  const componentNameRef = useRef(`Component-${Math.random().toString(36).substr(2, 9)}`);
  const renderCountRef = useRef(0);
  const effectCountRef = useRef(0);
  const eventCountRef = useRef(0);
  const reportIntervalIdRef = useRef(null);
  
  // Initialize the performance monitor
  useEffect(() => {
    if (isEnabled && !isInitialized) {
      monitorRef.current = initPerformanceMonitor();
      setIsInitialized(true);
      
      // Mark component mount
      if (monitorRef.current) {
        monitorRef.current.mark(`${componentNameRef.current}-mount`);
      }
    }
    
    return () => {
      // Mark component unmount
      if (monitorRef.current) {
        monitorRef.current.mark(`${componentNameRef.current}-unmount`);
        monitorRef.current.measure(
          `${componentNameRef.current}-lifetime`,
          `${componentNameRef.current}-mount`,
          `${componentNameRef.current}-unmount`
        );
      }
      
      // Clear report interval
      if (reportIntervalIdRef.current) {
        clearInterval(reportIntervalIdRef.current);
      }
    };
  }, [isEnabled, isInitialized]);
  
  // Set up automatic reporting
  useEffect(() => {
    if (isEnabled && isInitialized && onReport && reportInterval > 0) {
      reportIntervalIdRef.current = setInterval(() => {
        if (monitorRef.current) {
          const report = monitorRef.current.getPerformanceReport();
          onReport(report);
        }
      }, reportInterval);
      
      return () => {
        clearInterval(reportIntervalIdRef.current);
      };
    }
  }, [isEnabled, isInitialized, onReport, reportInterval]);
  
  // Mark component render
  useEffect(() => {
    if (isEnabled && isInitialized && autoMarkRenders && monitorRef.current) {
      renderCountRef.current += 1;
      monitorRef.current.mark(`${componentNameRef.current}-render-${renderCountRef.current}`);
      
      if (renderCountRef.current > 1) {
        monitorRef.current.measure(
          `${componentNameRef.current}-render-time-${renderCountRef.current}`,
          `${componentNameRef.current}-render-${renderCountRef.current - 1}`,
          `${componentNameRef.current}-render-${renderCountRef.current}`
        );
      }
    }
  });
  
  // Create a wrapper for effect functions
  const wrapEffect = useCallback((effectFn, effectName = 'effect') => {
    if (!isEnabled || !isInitialized || !autoMarkEffects || !monitorRef.current) {
      return effectFn;
    }
    
    return (...args) => {
      effectCountRef.current += 1;
      const effectId = `${componentNameRef.current}-${effectName}-${effectCountRef.current}`;
      monitorRef.current.mark(`${effectId}-start`);
      
      try {
        const result = effectFn(...args);
        
        // Handle promises
        if (result && typeof result.then === 'function') {
          return result.then((value) => {
            monitorRef.current.mark(`${effectId}-end`);
            monitorRef.current.measure(
              `${effectId}-time`,
              `${effectId}-start`,
              `${effectId}-end`
            );
            return value;
          }).catch((error) => {
            monitorRef.current.mark(`${effectId}-error`);
            monitorRef.current.measure(
              `${effectId}-error-time`,
              `${effectId}-start`,
              `${effectId}-error`
            );
            throw error;
          });
        }
        
        // Handle synchronous functions
        monitorRef.current.mark(`${effectId}-end`);
        monitorRef.current.measure(
          `${effectId}-time`,
          `${effectId}-start`,
          `${effectId}-end`
        );
        
        return result;
      } catch (error) {
        monitorRef.current.mark(`${effectId}-error`);
        monitorRef.current.measure(
          `${effectId}-error-time`,
          `${effectId}-start`,
          `${effectId}-error`
        );
        throw error;
      }
    };
  }, [isEnabled, isInitialized, autoMarkEffects]);
  
  // Create a wrapper for event handlers
  const wrapEvent = useCallback((eventHandler, eventName = 'event') => {
    if (!isEnabled || !isInitialized || !autoMarkEvents || !monitorRef.current) {
      return eventHandler;
    }
    
    return (...args) => {
      eventCountRef.current += 1;
      const eventId = `${componentNameRef.current}-${eventName}-${eventCountRef.current}`;
      monitorRef.current.mark(`${eventId}-start`);
      
      try {
        const result = eventHandler(...args);
        
        // Handle promises
        if (result && typeof result.then === 'function') {
          return result.then((value) => {
            monitorRef.current.mark(`${eventId}-end`);
            monitorRef.current.measure(
              `${eventId}-time`,
              `${eventId}-start`,
              `${eventId}-end`
            );
            return value;
          }).catch((error) => {
            monitorRef.current.mark(`${eventId}-error`);
            monitorRef.current.measure(
              `${eventId}-error-time`,
              `${eventId}-start`,
              `${eventId}-error`
            );
            throw error;
          });
        }
        
        // Handle synchronous functions
        monitorRef.current.mark(`${eventId}-end`);
        monitorRef.current.measure(
          `${eventId}-time`,
          `${eventId}-start`,
          `${eventId}-end`
        );
        
        return result;
      } catch (error) {
        monitorRef.current.mark(`${eventId}-error`);
        monitorRef.current.measure(
          `${eventId}-error-time`,
          `${eventId}-start`,
          `${eventId}-error`
        );
        throw error;
      }
    };
  }, [isEnabled, isInitialized, autoMarkEvents]);
  
  // Set component name
  const setComponentName = useCallback((name) => {
    componentNameRef.current = name;
  }, []);
  
  // Enable/disable the performance monitor
  const setEnabled = useCallback((enabled) => {
    setIsEnabled(enabled);
  }, []);
  
  // Get the current performance report
  const getReport = useCallback(() => {
    if (isEnabled && isInitialized && monitorRef.current) {
      return monitorRef.current.getPerformanceReport();
    }
    return null;
  }, [isEnabled, isInitialized]);
  
  // Clear all performance data
  const clearData = useCallback(() => {
    if (isEnabled && isInitialized && monitorRef.current) {
      monitorRef.current.clearMarks();
      monitorRef.current.clearMeasures();
      monitorRef.current.clearResourceTimings();
      monitorRef.current.clearErrors();
    }
  }, [isEnabled, isInitialized]);
  
  return {
    isEnabled,
    isInitialized,
    setEnabled,
    setComponentName,
    wrapEffect,
    wrapEvent,
    getReport,
    clearData,
    mark: useCallback((name) => {
      if (isEnabled && isInitialized && monitorRef.current) {
        monitorRef.current.mark(`${componentNameRef.current}-${name}`);
      }
    }, [isEnabled, isInitialized]),
    measure: useCallback((name, startMark, endMark) => {
      if (isEnabled && isInitialized && monitorRef.current) {
        monitorRef.current.measure(
          `${componentNameRef.current}-${name}`,
          `${componentNameRef.current}-${startMark}`,
          `${componentNameRef.current}-${endMark}`
        );
      }
    }, [isEnabled, isInitialized]),
  };
};

export default usePerformanceMonitor;
