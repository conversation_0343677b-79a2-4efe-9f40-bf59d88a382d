name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      
      - name: Configure Git
        run: |
          git config --global core.autocrlf false
          git config --global core.eol lf
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
      
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'

      - name: Install dependencies
        run: npm install

      - name: Run ESLint
        run: npm run lint

      - name: Build Docker images
        run: |
          docker-compose build \
            --build-arg OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }} \
            --build-arg AI_STUDIO_API_KEY=${{ secrets.AI_STUDIO_API_KEY }} \
            --build-arg STABILITY_API_KEY=${{ secrets.STABILITY_API_KEY }} \
            --build-arg ELEVENLABS_API_KEY=${{ secrets.ELEVENLABS_API_KEY }}

      - name: Test API Keys
        run: python scripts/test-api-keys.py
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          AI_STUDIO_API_KEY: ${{ secrets.AI_STUDIO_API_KEY }}
          STABILITY_API_KEY: ${{ secrets.STABILITY_API_KEY }}
          ELEVENLABS_API_KEY: ${{ secrets.ELEVENLABS_API_KEY }}
