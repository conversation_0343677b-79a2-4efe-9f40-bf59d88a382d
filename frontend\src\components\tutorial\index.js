/**
 * Tutorial System Main Export
 * 
 * Central export point for all tutorial system components and utilities.
 */

// Core Components
export { default as TutorialProvider, useTutorial } from './TutorialManager';
export { default as TutorialOverlay } from './TutorialOverlay';
export { default as TutorialProgress } from './TutorialProgress';
export { default as ContextualHelp, useContextualHelp } from './ContextualHelp';

// Data and Storage
export { default as tutorialStorage } from './TutorialStorage';
export {
  TUTORIAL_DEFINITIONS,
  TUTORIAL_CATEGORIES_CONFIG,
  FEATURE_TUTORIALS,
  LEARNING_PATHS,
  getTutorialsByCategory,
  getRecommendedTutorials
} from './TutorialContent';

// Types and Constants
export * from './types';

// Tutorial Integration Components
export { default as TutorialLauncher } from './TutorialLauncher';
export { default as TutorialDashboard } from './TutorialDashboard';
export { default as TutorialBadges } from './TutorialBadges';
export { default as TutorialRegistration } from './TutorialRegistration';
export { default as TutorialEntryPoint } from './TutorialEntryPoint';

// Visual Enhancements
export * from './TutorialVisualEnhancements';

// Progress Tracking
export * from './TutorialProgressTracker';

// Onboarding Helpers
export * from './VisualOnboardingHelpers';

// HOCs and Utilities
export { default as withTutorialSupport } from './withTutorialSupport';
export { default as TutorialTrigger } from './TutorialTrigger';

// Convenience HOCs
export {
  withComponentPaletteHelp,
  withPreviewAreaHelp,
  withPropertyEditorHelp,
  withDragDropHelp,
  withThemeManagerHelp,
  withLayoutDesignerHelp,
  withCodeExportHelp,
  withWebSocketHelp,
  createTutorialHOC
} from './withTutorialSupport';
