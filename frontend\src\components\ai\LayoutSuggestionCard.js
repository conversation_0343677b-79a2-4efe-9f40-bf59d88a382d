import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Card, Button, Badge, Tooltip, Modal, Typography, Space, Tag, Spin } from 'antd';
import {
  LayoutOutlined,
  EyeOutlined,
  CheckOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

/**
 * Layout Suggestion Card Component
 * Displays individual layout suggestions with visual previews
 */
const LayoutSuggestionCard = ({
  suggestion,
  onApply,
  onPreview,
  applied = false,
  showPreview = true,
  showScore = true,
  compact = false
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [applying, setApplying] = useState(false);

  // Handle apply button click
  const handleApply = async () => {
    if (applied || applying) return;

    setApplying(true);
    try {
      if (onApply) {
        await onApply(suggestion);
      }
    } catch (error) {
      console.error('Error applying layout suggestion:', error);
    } finally {
      setApplying(false);
    }
  };

  // Handle preview button click
  const handlePreview = () => {
    if (onPreview) {
      onPreview(suggestion);
    } else {
      setPreviewVisible(true);
    }
  };

  // Get score color based on value
  const getScoreColor = (score) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#1890ff';
    if (score >= 40) return '#faad14';
    return '#ff4d4f';
  };

  // Render layout preview
  const renderLayoutPreview = () => {
    const { structure } = suggestion;

    return (
      <div style={{
        width: '100%',
        height: '120px',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        padding: '8px',
        background: '#fafafa',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Grid Layout Preview */}
        {suggestion.id.includes('grid') && (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '4px',
            height: '100%'
          }}>
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                style={{
                  background: '#e6f7ff',
                  border: '1px solid #91d5ff',
                  borderRadius: '2px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '10px',
                  color: '#1890ff'
                }}
              >
                {i + 1}
              </div>
            ))}
          </div>
        )}

        {/* Header-Footer Layout Preview */}
        {suggestion.id.includes('header') && (
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <div style={{
              height: '20px',
              background: '#e6f7ff',
              border: '1px solid #91d5ff',
              borderRadius: '2px',
              marginBottom: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '8px',
              color: '#1890ff'
            }}>
              Header
            </div>
            <div style={{
              flex: 1,
              background: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '2px',
              marginBottom: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '8px',
              color: '#52c41a'
            }}>
              Main Content
            </div>
            <div style={{
              height: '16px',
              background: '#e6f7ff',
              border: '1px solid #91d5ff',
              borderRadius: '2px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '8px',
              color: '#1890ff'
            }}>
              Footer
            </div>
          </div>
        )}

        {/* Sidebar Layout Preview */}
        {suggestion.id.includes('sidebar') && (
          <div style={{ height: '100%', display: 'flex' }}>
            <div style={{
              width: '30%',
              background: '#fff2e8',
              border: '1px solid #ffbb96',
              borderRadius: '2px',
              marginRight: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '8px',
              color: '#fa8c16'
            }}>
              Sidebar
            </div>
            <div style={{
              flex: 1,
              background: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '2px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '8px',
              color: '#52c41a'
            }}>
              Main Content
            </div>
          </div>
        )}

        {/* Flexbox Layout Preview */}
        {suggestion.id.includes('flex') && (
          <div style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            gap: '4px'
          }}>
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                style={{
                  flex: 1,
                  background: '#f6ffed',
                  border: '1px solid #b7eb8f',
                  borderRadius: '2px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '8px',
                  color: '#52c41a'
                }}
              >
                Item {i + 1}
              </div>
            ))}
          </div>
        )}

        {/* Hero Section Preview */}
        {suggestion.id.includes('hero') && (
          <div style={{
            height: '100%',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '4px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '4px' }}>
              Hero Title
            </div>
            <div style={{ fontSize: '8px', marginBottom: '8px' }}>
              Compelling subtitle text
            </div>
            <div style={{
              background: 'rgba(255,255,255,0.2)',
              padding: '2px 8px',
              borderRadius: '2px',
              fontSize: '8px'
            }}>
              CTA Button
            </div>
          </div>
        )}

        {/* Default/Generic Preview */}
        {!['grid', 'header', 'sidebar', 'flex', 'hero'].some(type => suggestion.id.includes(type)) && (
          <div style={{
            height: '100%',
            background: '#f0f0f0',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '10px',
            color: '#999'
          }}>
            <LayoutOutlined style={{ fontSize: '24px' }} />
          </div>
        )}
      </div>
    );
  };

  // Render detailed preview modal
  const renderPreviewModal = () => (
    <Modal
      title={
        <Space>
          <LayoutOutlined />
          {suggestion.name}
          {showScore && (
            <Badge
              count={suggestion.score}
              style={{ backgroundColor: getScoreColor(suggestion.score) }}
            />
          )}
        </Space>
      }
      open={previewVisible}
      onCancel={() => setPreviewVisible(false)}
      footer={[
        <Button key="cancel" onClick={() => setPreviewVisible(false)}>
          Close
        </Button>,
        <Button
          key="apply"
          type="primary"
          onClick={() => {
            setPreviewVisible(false);
            handleApply();
          }}
          disabled={applied}
          loading={applying}
        >
          {applied ? 'Applied' : 'Apply Layout'}
        </Button>
      ]}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ height: '200px' }}>
          {renderLayoutPreview()}
        </div>

        <Paragraph>{suggestion.description}</Paragraph>

        <div>
          <Text strong>Why this layout?</Text>
          <Paragraph type="secondary">{suggestion.explanation}</Paragraph>
        </div>

        {suggestion.use_cases && (
          <div>
            <Text strong>Best for:</Text>
            <div style={{ marginTop: '8px' }}>
              {suggestion.use_cases.map((useCase, index) => (
                <Tag key={index} color="blue" style={{ marginBottom: '4px' }}>
                  {useCase.replace('_', ' ')}
                </Tag>
              ))}
            </div>
          </div>
        )}

        {suggestion.applicable_components && suggestion.applicable_components.length > 0 && (
          <div>
            <Text strong>Works with your components:</Text>
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                {suggestion.applicable_components.length} of your components are compatible
              </Text>
            </div>
          </div>
        )}
      </Space>
    </Modal>
  );

  if (compact) {
    return (
      <>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          padding: '8px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          marginBottom: '8px',
          background: applied ? '#f6ffed' : 'white'
        }}>
          <div style={{ width: '40px', height: '30px', marginRight: '12px' }}>
            {renderLayoutPreview()}
          </div>

          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>
              <Text strong style={{ fontSize: '12px' }}>{suggestion.name}</Text>
              {showScore && (
                <Badge
                  count={suggestion.score}
                  style={{
                    backgroundColor: getScoreColor(suggestion.score),
                    marginLeft: '8px'
                  }}
                />
              )}
            </div>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {suggestion.explanation}
            </Text>
          </div>

          <Space>
            {showPreview && (
              <Tooltip title="Preview">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={handlePreview}
                />
              </Tooltip>
            )}
            <Button
              type={applied ? 'default' : 'primary'}
              size="small"
              icon={applied ? <CheckOutlined /> : <ThunderboltOutlined />}
              onClick={handleApply}
              disabled={applied}
              loading={applying}
            >
              {applied ? 'Applied' : 'Apply'}
            </Button>
          </Space>
        </div>
        {renderPreviewModal()}
      </>
    );
  }

  return (
    <>
      <Card
        size="small"
        style={{
          marginBottom: '12px',
          border: applied ? '2px solid #52c41a' : '1px solid #d9d9d9'
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Space>
              <Text strong>{suggestion.name}</Text>
              {showScore && (
                <Badge
                  count={suggestion.score}
                  style={{ backgroundColor: getScoreColor(suggestion.score) }}
                />
              )}
            </Space>
            {applied && <CheckOutlined style={{ color: '#52c41a' }} />}
          </div>
        }
        extra={
          <Space>
            {showPreview && (
              <Tooltip title="Preview layout">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={handlePreview}
                />
              </Tooltip>
            )}
            <Button
              type={applied ? 'default' : 'primary'}
              size="small"
              icon={applied ? <CheckOutlined /> : <ThunderboltOutlined />}
              onClick={handleApply}
              disabled={applied}
              loading={applying}
            >
              {applied ? 'Applied' : 'Apply'}
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: '12px' }}>
          {renderLayoutPreview()}
        </div>

        <Paragraph style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#666' }}>
          {suggestion.description}
        </Paragraph>

        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InfoCircleOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
          <Text style={{ fontSize: '11px', fontStyle: 'italic' }}>
            {suggestion.explanation}
          </Text>
        </div>
      </Card>
      {renderPreviewModal()}
    </>
  );
};

LayoutSuggestionCard.propTypes = {
  suggestion: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    score: PropTypes.number.isRequired,
    explanation: PropTypes.string.isRequired,
    structure: PropTypes.object,
    use_cases: PropTypes.array,
    applicable_components: PropTypes.array
  }).isRequired,
  onApply: PropTypes.func,
  onPreview: PropTypes.func,
  applied: PropTypes.bool,
  showPreview: PropTypes.bool,
  showScore: PropTypes.bool,
  compact: PropTypes.bool
};

export default LayoutSuggestionCard;

/**
 * Layout Suggestions List Component
 * Container for multiple layout suggestion cards
 */
export const LayoutSuggestionsList = ({
  suggestions = [],
  onApply,
  onPreview,
  appliedSuggestions = new Set(),
  loading = false,
  compact = false,
  showScore = true,
  showPreview = true,
  emptyMessage = "No layout suggestions available"
}) => {
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin tip="Loading layout suggestions..." />
      </div>
    );
  }

  if (suggestions.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
        <LayoutOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
        <div>{emptyMessage}</div>
      </div>
    );
  }

  return (
    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
      {suggestions.map((suggestion) => (
        <LayoutSuggestionCard
          key={suggestion.id}
          suggestion={suggestion}
          onApply={onApply}
          onPreview={onPreview}
          applied={appliedSuggestions.has(suggestion.id)}
          compact={compact}
          showScore={showScore}
          showPreview={showPreview}
        />
      ))}
    </div>
  );
};

LayoutSuggestionsList.propTypes = {
  suggestions: PropTypes.array,
  onApply: PropTypes.func,
  onPreview: PropTypes.func,
  appliedSuggestions: PropTypes.instanceOf(Set),
  loading: PropTypes.bool,
  compact: PropTypes.bool,
  showScore: PropTypes.bool,
  showPreview: PropTypes.bool,
  emptyMessage: PropTypes.string
};
