/**
 * Enhanced Drag and Drop System
 * 
 * Comprehensive drag-and-drop system with improved visual feedback,
 * drop zone highlighting, component ghost previews, smooth animations,
 * and accessibility support.
 */

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import styled, { keyframes, css } from 'styled-components';
import { theme, animationHierarchy, accessibleHierarchy } from '../../design-system';
import { DragOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

// Drag animations
const dragPulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

const dropZoneGlow = keyframes`
  0% { box-shadow: 0 0 0 0 ${theme.colors.primary.main}40; }
  50% { box-shadow: 0 0 0 8px ${theme.colors.primary.main}20; }
  100% { box-shadow: 0 0 0 0 ${theme.colors.primary.main}40; }
`;

const ghostFloat = keyframes`
  0% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
  100% { transform: translateY(0px); }
`;

// Enhanced styled components for drag and drop
const DraggableWrapper = styled.div`
  position: relative;
  cursor: ${props => props.isDragging ? 'grabbing' : 'grab'};
  transition: ${animationHierarchy.micro.duration} ${animationHierarchy.micro.easing};
  user-select: none;
  
  ${props => props.isDraggable && !props.disabled && css`
    &:hover {
      transform: translateY(-1px);
      box-shadow: ${theme.shadows.md};
      
      .drag-indicator {
        opacity: 1;
        transform: scale(1);
      }
    }
  `}
  
  ${props => props.isDragging && css`
    opacity: 0.8;
    transform: rotate(2deg) scale(1.02);
    box-shadow: ${theme.shadows.xl};
    z-index: ${theme.zIndex.dragOverlay};
    animation: ${dragPulse} 2s infinite;
  `}
  
  ${props => props.disabled && css`
    cursor: not-allowed;
    opacity: 0.6;
    filter: grayscale(50%);
  `}
  
  /* Accessibility enhancements */
  &:focus-visible {
    outline: 2px solid ${theme.colors.primary.main};
    outline-offset: 2px;
  }
  
  /* High contrast mode support */
  ${accessibleHierarchy.highContrast}
  
  /* Reduced motion support */
  ${accessibleHierarchy.reducedMotion}
`;

const DragIndicator = styled.div`
  position: absolute;
  top: ${theme.spacing[1]};
  right: ${theme.spacing[1]};
  background: ${theme.colors.primary.main};
  color: ${theme.colors.primary.contrastText};
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  opacity: 0;
  transform: scale(0.8);
  transition: ${animationHierarchy.micro.duration} ${animationHierarchy.micro.easing};
  z-index: 2;
  
  &.drag-indicator {
    opacity: ${props => props.visible ? 1 : 0};
  }
`;

const DropZone = styled.div`
  position: relative;
  min-height: ${props => props.minHeight || '100px'};
  border: 2px dashed ${theme.colors.border.medium};
  border-radius: ${theme.borderRadius.lg};
  background: ${theme.colors.background.paper};
  transition: ${animationHierarchy.component.duration} ${animationHierarchy.component.easing};
  
  ${props => props.isActive && css`
    border-color: ${theme.colors.primary.main};
    background: ${theme.colors.primary.light};
    animation: ${dropZoneGlow} 2s infinite;
    
    .drop-message {
      opacity: 1;
      transform: translateY(0);
    }
  `}
  
  ${props => props.isValid && css`
    border-color: ${theme.colors.success.main};
    background: ${theme.colors.success.light};
    
    .drop-message {
      color: ${theme.colors.success.dark};
    }
  `}
  
  ${props => props.isInvalid && css`
    border-color: ${theme.colors.error.main};
    background: ${theme.colors.error.light};
    
    .drop-message {
      color: ${theme.colors.error.dark};
    }
  `}
  
  /* Accessibility */
  &[role="region"] {
    outline: none;
  }
  
  &:focus-visible {
    outline: 2px solid ${theme.colors.primary.main};
    outline-offset: 2px;
  }
`;

const DropMessage = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateY(10px);
  text-align: center;
  color: ${theme.colors.text.secondary};
  font-weight: ${theme.typography.fontWeight.medium};
  opacity: 0;
  transition: ${animationHierarchy.component.duration} ${animationHierarchy.component.easing};
  pointer-events: none;
  
  &.drop-message {
    opacity: ${props => props.visible ? 1 : 0};
    transform: translate(-50%, -50%) translateY(${props => props.visible ? '0' : '10px'});
  }
  
  .drop-icon {
    font-size: 24px;
    margin-bottom: ${theme.spacing[2]};
    display: block;
  }
  
  .drop-text {
    font-size: ${theme.typography.fontSize.sm};
    line-height: ${theme.typography.lineHeight.normal};
  }
  
  .drop-hint {
    font-size: ${theme.typography.fontSize.xs};
    color: ${theme.colors.text.tertiary};
    margin-top: ${theme.spacing[1]};
  }
`;

const DragGhost = styled.div`
  position: fixed;
  pointer-events: none;
  z-index: ${theme.zIndex.tooltip};
  background: ${theme.colors.background.paper};
  border: 1px solid ${theme.colors.border.medium};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.xl};
  padding: ${theme.spacing[2]} ${theme.spacing[3]};
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.text.primary};
  white-space: nowrap;
  animation: ${ghostFloat} 2s infinite;
  
  /* Smooth follow cursor */
  transition: transform 0.1s ease-out;
  
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, ${theme.colors.primary.main}, ${theme.colors.accent.main});
    border-radius: ${theme.borderRadius.md};
    z-index: -1;
    opacity: 0.3;
  }
`;

const DropPreview = styled.div`
  position: absolute;
  background: ${theme.colors.primary.light};
  border: 2px dashed ${theme.colors.primary.main};
  border-radius: ${theme.borderRadius.md};
  opacity: 0.7;
  pointer-events: none;
  z-index: ${theme.zIndex.docked};
  transition: ${animationHierarchy.micro.duration} ${animationHierarchy.micro.easing};
  
  &::after {
    content: 'Drop here';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: ${theme.typography.fontSize.xs};
    font-weight: ${theme.typography.fontWeight.medium};
    color: ${theme.colors.primary.dark};
    white-space: nowrap;
  }
`;

const DragOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: ${theme.zIndex.overlay};
  pointer-events: none;
  opacity: ${props => props.visible ? 1 : 0};
  transition: ${animationHierarchy.component.duration} ${animationHierarchy.component.easing};
`;

// Enhanced Draggable Component
export const EnhancedDraggable = ({
  children,
  data,
  disabled = false,
  showIndicator = true,
  ghostContent,
  onDragStart,
  onDragEnd,
  className,
  style,
  ariaLabel,
  ...props
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [ghostPosition, setGhostPosition] = useState({ x: 0, y: 0 });
  const dragRef = useRef(null);
  const ghostRef = useRef(null);

  const handleDragStart = useCallback((e) => {
    if (disabled) {
      e.preventDefault();
      return;
    }

    setIsDragging(true);
    
    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify(data));
    e.dataTransfer.effectAllowed = 'move';
    
    // Create custom drag image
    if (dragRef.current) {
      const dragImage = dragRef.current.cloneNode(true);
      dragImage.style.transform = 'rotate(2deg)';
      dragImage.style.opacity = '0.8';
      e.dataTransfer.setDragImage(dragImage, 50, 25);
    }
    
    // Announce drag start for screen readers
    if (ariaLabel) {
      const announcement = `Started dragging ${ariaLabel}`;
      // Create live region announcement
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'assertive');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.textContent = announcement;
      document.body.appendChild(liveRegion);
      setTimeout(() => document.body.removeChild(liveRegion), 1000);
    }
    
    if (onDragStart) {
      onDragStart(e, data);
    }
  }, [disabled, data, ariaLabel, onDragStart]);

  const handleDragEnd = useCallback((e) => {
    setIsDragging(false);
    setGhostPosition({ x: 0, y: 0 });
    
    // Announce drag end for screen readers
    if (ariaLabel) {
      const announcement = `Finished dragging ${ariaLabel}`;
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.textContent = announcement;
      document.body.appendChild(liveRegion);
      setTimeout(() => document.body.removeChild(liveRegion), 1000);
    }
    
    if (onDragEnd) {
      onDragEnd(e, data);
    }
  }, [ariaLabel, onDragEnd, data]);

  // Track mouse position for ghost
  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e) => {
      setGhostPosition({ x: e.clientX + 10, y: e.clientY + 10 });
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, [isDragging]);

  return (
    <>
      <DraggableWrapper
        ref={dragRef}
        draggable={!disabled}
        isDraggable={!disabled}
        isDragging={isDragging}
        disabled={disabled}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        className={className}
        style={style}
        role="button"
        aria-label={ariaLabel || 'Draggable item'}
        aria-grabbed={isDragging}
        tabIndex={disabled ? -1 : 0}
        {...props}
      >
        {children}
        
        {showIndicator && !disabled && (
          <DragIndicator className="drag-indicator" visible={!isDragging}>
            <DragOutlined />
          </DragIndicator>
        )}
      </DraggableWrapper>

      {/* Drag Ghost */}
      {isDragging && ghostContent && createPortal(
        <DragGhost
          ref={ghostRef}
          style={{
            left: ghostPosition.x,
            top: ghostPosition.y,
          }}
        >
          {ghostContent}
        </DragGhost>,
        document.body
      )}
    </>
  );
};

// Enhanced Drop Zone Component
export const EnhancedDropZone = ({
  children,
  onDrop,
  onDragOver,
  onDragEnter,
  onDragLeave,
  accepts = [],
  minHeight,
  disabled = false,
  showMessage = true,
  validMessage = "Drop here",
  invalidMessage = "Cannot drop here",
  emptyMessage = "Drag items here",
  className,
  style,
  ariaLabel,
  ...props
}) => {
  const [dragState, setDragState] = useState({
    isActive: false,
    isValid: false,
    draggedData: null,
  });
  const dropRef = useRef(null);
  const dragCounter = useRef(0);

  const validateDrop = useCallback((draggedData) => {
    if (accepts.length === 0) return true;
    return accepts.some(accept => {
      if (typeof accept === 'string') {
        return draggedData?.type === accept;
      }
      if (typeof accept === 'function') {
        return accept(draggedData);
      }
      return false;
    });
  }, [accepts]);

  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    dragCounter.current++;
    
    if (disabled) return;

    try {
      const draggedData = JSON.parse(e.dataTransfer.getData('application/json') || '{}');
      const isValid = validateDrop(draggedData);
      
      setDragState({
        isActive: true,
        isValid,
        draggedData,
      });
      
      if (onDragEnter) {
        onDragEnter(e, { isValid, draggedData });
      }
    } catch (error) {
      console.warn('Invalid drag data:', error);
    }
  }, [disabled, validateDrop, onDragEnter]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = dragState.isValid ? 'move' : 'none';
    
    if (onDragOver) {
      onDragOver(e, dragState);
    }
  }, [dragState, onDragOver]);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    dragCounter.current--;
    
    if (dragCounter.current === 0) {
      setDragState({
        isActive: false,
        isValid: false,
        draggedData: null,
      });
      
      if (onDragLeave) {
        onDragLeave(e);
      }
    }
  }, [onDragLeave]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    dragCounter.current = 0;
    
    if (disabled || !dragState.isValid) {
      setDragState({
        isActive: false,
        isValid: false,
        draggedData: null,
      });
      return;
    }

    // Announce successful drop for screen readers
    if (ariaLabel) {
      const announcement = `Dropped item in ${ariaLabel}`;
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'assertive');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.textContent = announcement;
      document.body.appendChild(liveRegion);
      setTimeout(() => document.body.removeChild(liveRegion), 1000);
    }

    if (onDrop) {
      onDrop(e, dragState.draggedData);
    }

    setDragState({
      isActive: false,
      isValid: false,
      draggedData: null,
    });
  }, [disabled, dragState, ariaLabel, onDrop]);

  const getMessage = () => {
    if (!dragState.isActive) return emptyMessage;
    return dragState.isValid ? validMessage : invalidMessage;
  };

  const getIcon = () => {
    if (!dragState.isActive) return <DragOutlined />;
    return dragState.isValid ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />;
  };

  return (
    <DropZone
      ref={dropRef}
      isActive={dragState.isActive}
      isValid={dragState.isValid}
      isInvalid={dragState.isActive && !dragState.isValid}
      minHeight={minHeight}
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      className={className}
      style={style}
      role="region"
      aria-label={ariaLabel || 'Drop zone'}
      aria-dropeffect={dragState.isActive ? (dragState.isValid ? 'move' : 'none') : 'none'}
      tabIndex={-1}
      {...props}
    >
      {children}
      
      {showMessage && (
        <DropMessage className="drop-message" visible={dragState.isActive || !children}>
          <span className="drop-icon">{getIcon()}</span>
          <div className="drop-text">{getMessage()}</div>
          {!dragState.isActive && (
            <div className="drop-hint">
              {accepts.length > 0 ? `Accepts: ${accepts.join(', ')}` : 'Accepts any item'}
            </div>
          )}
        </DropMessage>
      )}
    </DropZone>
  );
};

// Drag and Drop Context Provider
export const DragDropProvider = ({ children, showOverlay = true }) => {
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    const handleDragStart = () => setIsDragging(true);
    const handleDragEnd = () => setIsDragging(false);

    document.addEventListener('dragstart', handleDragStart);
    document.addEventListener('dragend', handleDragEnd);

    return () => {
      document.removeEventListener('dragstart', handleDragStart);
      document.removeEventListener('dragend', handleDragEnd);
    };
  }, []);

  return (
    <>
      {children}
      {showOverlay && <DragOverlay visible={isDragging} />}
    </>
  );
};

export default {
  Draggable: EnhancedDraggable,
  DropZone: EnhancedDropZone,
  Provider: DragDropProvider,
};
