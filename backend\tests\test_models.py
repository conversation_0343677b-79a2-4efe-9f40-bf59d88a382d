"""
Unit tests for Django models in the App Builder application.
"""

import pytest
import json
from datetime import datetime, timedelta
from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from model_bakery import baker

from my_app.models import (
    App, AppVersion, CollaborationSession, SessionParticipant,
    Comment, EditOperation, UserActivity, ComponentTemplate,
    LayoutTemplate, AppTemplate
)


@pytest.mark.django_db
class TestAppModel:
    """Test cases for the App model."""

    def test_app_creation(self):
        """Test basic app creation."""
        user = baker.make(User)
        app_data = {
            'components': [
                {'id': '1', 'type': 'button', 'props': {'text': 'Click me'}}
            ],
            'layout': {'type': 'flex', 'direction': 'column'}
        }
        
        app = App.objects.create(
            name='Test App',
            description='A test application',
            user=user,
            app_data=json.dumps(app_data),
            is_public=False
        )
        
        assert app.name == 'Test App'
        assert app.description == 'A test application'
        assert app.user == user
        assert not app.is_public
        assert app.created_at is not None
        assert app.updated_at is not None

    def test_app_str_representation(self):
        """Test string representation of App model."""
        user = baker.make(User, username='testuser')
        app = baker.make(App, name='My Awesome App', user=user)
        assert str(app) == 'My Awesome App (testuser)'

    def test_app_str_representation_anonymous(self):
        """Test string representation of App model without user."""
        app = baker.make(App, name='Anonymous App', user=None)
        assert str(app) == 'Anonymous App (Anonymous)'

    def test_app_data_json_field(self):
        """Test JSON field functionality for app_data."""
        user = baker.make(User)
        app_data = {
            'components': [
                {'id': '1', 'type': 'button', 'props': {'text': 'Button 1'}},
                {'id': '2', 'type': 'input', 'props': {'placeholder': 'Enter text'}}
            ],
            'styles': {
                'theme': 'dark',
                'primaryColor': '#007bff'
            }
        }
        
        app = App.objects.create(
            name='JSON Test App',
            user=user,
            app_data=json.dumps(app_data)
        )
        
        # Retrieve and parse the data
        retrieved_app = App.objects.get(id=app.id)
        parsed_data = json.loads(retrieved_app.app_data)
        
        assert parsed_data['components'][0]['type'] == 'button'
        assert parsed_data['styles']['theme'] == 'dark'
        assert len(parsed_data['components']) == 2

    def test_app_collaboration_settings(self):
        """Test collaboration-related fields."""
        user = baker.make(User)
        app = App.objects.create(
            name='Collaborative App',
            user=user,
            app_data='{}',
            allow_collaboration=True,
            collaboration_mode='invite_only'
        )
        
        assert app.allow_collaboration is True
        assert app.collaboration_mode == 'invite_only'

    def test_app_without_user(self):
        """Test app creation without a user (anonymous)."""
        app = App.objects.create(
            name='Anonymous App',
            app_data='{}',
            user=None
        )
        
        assert app.user is None
        assert app.name == 'Anonymous App'

    def test_app_updated_at_auto_update(self):
        """Test that updated_at field is automatically updated."""
        user = baker.make(User)
        app = App.objects.create(
            name='Update Test App',
            user=user,
            app_data='{}'
        )
        
        original_updated_at = app.updated_at
        
        # Update the app
        app.description = 'Updated description'
        app.save()
        
        assert app.updated_at > original_updated_at

    def test_get_app_data_json_valid(self):
        """Test get_app_data_json method with valid JSON."""
        user = baker.make(User)
        app_data = {
            'components': [{'id': '1', 'type': 'button'}],
            'layouts': [],
            'styles': {'theme': 'dark'},
            'data': {'title': 'My App'}
        }

        app = App.objects.create(
            name='JSON Test App',
            user=user,
            app_data=json.dumps(app_data)
        )

        result = app.get_app_data_json()
        assert result == app_data
        assert result['components'][0]['type'] == 'button'
        assert result['styles']['theme'] == 'dark'

    def test_get_app_data_json_invalid(self):
        """Test get_app_data_json method with invalid JSON."""
        user = baker.make(User)
        app = App.objects.create(
            name='Invalid JSON App',
            user=user,
            app_data='invalid json string'
        )

        result = app.get_app_data_json()
        expected_default = {"components": [], "layouts": [], "styles": {}, "data": {}}
        assert result == expected_default

    def test_get_app_data_json_empty(self):
        """Test get_app_data_json method with empty string."""
        user = baker.make(User)
        app = App.objects.create(
            name='Empty JSON App',
            user=user,
            app_data=''
        )

        result = app.get_app_data_json()
        expected_default = {"components": [], "layouts": [], "styles": {}, "data": {}}
        assert result == expected_default

    def test_create_version(self):
        """Test create_version method."""
        user = baker.make(User)
        app = App.objects.create(
            name='Versioned App',
            user=user,
            app_data='{"components": []}'
        )

        version = app.create_version('Initial version', user)

        assert version.app == app
        assert version.version_number == 1
        assert version.app_data == app.app_data
        assert version.created_by == user
        assert version.commit_message == 'Initial version'

    def test_create_multiple_versions(self):
        """Test creating multiple versions."""
        user = baker.make(User)
        app = App.objects.create(
            name='Multi-Version App',
            user=user,
            app_data='{"components": []}'
        )

        version1 = app.create_version('Version 1', user)
        version2 = app.create_version('Version 2', user)

        assert version1.version_number == 1
        assert version2.version_number == 2
        assert app.versions.count() == 2

    def test_create_collaboration_session(self):
        """Test create_collaboration_session method."""
        user = baker.make(User)
        app = App.objects.create(
            name='Collaborative App',
            user=user,
            app_data='{"components": []}'
        )

        session = app.create_collaboration_session(user)

        assert session.app == app
        assert session.created_by == user
        assert session.name == f"Collaboration on {app.name}"
        assert session.is_active is True

    def test_create_collaboration_session_custom_name(self):
        """Test create_collaboration_session with custom name."""
        user = baker.make(User)
        app = App.objects.create(
            name='Custom Session App',
            user=user,
            app_data='{"components": []}'
        )

        session = app.create_collaboration_session(user, "Custom Session Name")

        assert session.name == "Custom Session Name"

    def test_get_active_collaboration_session(self):
        """Test get_active_collaboration_session method."""
        user = baker.make(User)
        app = App.objects.create(
            name='Session Test App',
            user=user,
            app_data='{"components": []}'
        )

        # No active session initially
        assert app.get_active_collaboration_session() is None

        # Create active session
        session = app.create_collaboration_session(user)
        assert app.get_active_collaboration_session() == session

        # Deactivate session
        session.is_active = False
        session.save()
        assert app.get_active_collaboration_session() is None


@pytest.mark.django_db
class TestAppVersionModel:
    """Test cases for the AppVersion model."""

    def test_app_version_creation(self):
        """Test basic app version creation."""
        user = baker.make(User)
        app = baker.make(App, user=user)

        version = AppVersion.objects.create(
            app=app,
            version_number=1,
            app_data='{"components": []}',
            created_by=user,
            commit_message='Initial version'
        )

        assert version.app == app
        assert version.version_number == 1
        assert version.app_data == '{"components": []}'
        assert version.created_by == user
        assert version.commit_message == 'Initial version'
        assert version.created_at is not None

    def test_app_version_str_representation(self):
        """Test string representation of AppVersion model."""
        user = baker.make(User)
        app = baker.make(App, name='Test App', user=user)
        version = baker.make(AppVersion, app=app, version_number=2)

        assert str(version) == 'Test App v2'

    def test_app_version_unique_constraint(self):
        """Test unique constraint on app and version_number."""
        user = baker.make(User)
        app = baker.make(App, user=user)

        # Create first version
        AppVersion.objects.create(
            app=app,
            version_number=1,
            app_data='{"components": []}',
            created_by=user
        )

        # Attempt to create duplicate version number
        with pytest.raises(IntegrityError):
            AppVersion.objects.create(
                app=app,
                version_number=1,
                app_data='{"components": []}',
                created_by=user
            )

    def test_app_version_ordering(self):
        """Test that versions are ordered by version_number descending."""
        user = baker.make(User)
        app = baker.make(App, user=user)

        version1 = AppVersion.objects.create(
            app=app, version_number=1, app_data='{}', created_by=user
        )
        version3 = AppVersion.objects.create(
            app=app, version_number=3, app_data='{}', created_by=user
        )
        version2 = AppVersion.objects.create(
            app=app, version_number=2, app_data='{}', created_by=user
        )

        versions = list(AppVersion.objects.filter(app=app))
        assert versions[0] == version3  # Highest version first
        assert versions[1] == version2
        assert versions[2] == version1

    def test_app_version_without_created_by(self):
        """Test version creation without created_by (null allowed)."""
        user = baker.make(User)
        app = baker.make(App, user=user)

        version = AppVersion.objects.create(
            app=app,
            version_number=1,
            app_data='{"components": []}',
            created_by=None
        )

        assert version.created_by is None


@pytest.mark.django_db
class TestCollaborationSessionModel:
    """Test cases for the CollaborationSession model."""

    def test_collaboration_session_creation(self):
        """Test basic collaboration session creation."""
        user = baker.make(User)
        app = baker.make(App, user=user)

        session = CollaborationSession.objects.create(
            app=app,
            name='Test Session',
            created_by=user,
            max_participants=5,
            allow_anonymous=True,
            require_approval=False
        )

        assert session.app == app
        assert session.name == 'Test Session'
        assert session.created_by == user
        assert session.max_participants == 5
        assert session.allow_anonymous is True
        assert session.require_approval is False
        assert session.is_active is True
        assert isinstance(session.id, uuid.UUID)

    def test_collaboration_session_defaults(self):
        """Test default values for collaboration session."""
        user = baker.make(User)
        app = baker.make(App, user=user)

        session = CollaborationSession.objects.create(
            app=app,
            created_by=user
        )

        assert session.name == "Collaboration Session"
        assert session.max_participants == 10
        assert session.allow_anonymous is False
        assert session.require_approval is False
        assert session.is_active is True

    def test_collaboration_session_str_representation(self):
        """Test string representation of CollaborationSession model."""
        user = baker.make(User)
        app = baker.make(App, name='Test App', user=user)
        session = baker.make(
            CollaborationSession,
            app=app,
            name='My Session',
            created_by=user
        )

        assert str(session) == 'Session: My Session (Test App)'

    def test_active_participants_count_property(self):
        """Test active_participants_count property."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        user3 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        # Initially no participants
        assert session.active_participants_count == 0

        # Add active participants
        SessionParticipant.objects.create(session=session, user=user1, is_active=True)
        SessionParticipant.objects.create(session=session, user=user2, is_active=True)
        SessionParticipant.objects.create(session=session, user=user3, is_active=False)

        assert session.active_participants_count == 2


@pytest.mark.django_db
class TestLayoutTemplateModel:
    """Test cases for the LayoutTemplate model."""

    def test_layout_template_creation(self):
        """Test basic layout template creation."""
        user = baker.make(User)
        components = {
            'header': {'type': 'header', 'props': {'title': 'My App'}},
            'sidebar': {'type': 'sidebar', 'props': {'width': '250px'}},
            'content': {'type': 'content', 'props': {'padding': '20px'}}
        }
        
        template = LayoutTemplate.objects.create(
            name='Standard Layout',
            description='A standard app layout with header and sidebar',
            layout_type='business',
            components=components,
            user=user,
            is_public=True
        )

        assert template.name == 'Standard Layout'
        assert template.layout_type == 'business'
        assert template.components['header']['type'] == 'header'
        assert template.is_public is True

    def test_layout_template_default_props(self):
        """Test default props functionality."""
        user = baker.make(User)
        default_props = {
            'theme': 'light',
            'spacing': 'medium',
            'responsive': True
        }
        
        template = LayoutTemplate.objects.create(
            name='Responsive Layout',
            user=user,
            components={},
            default_props=default_props
        )
        
        assert template.default_props['theme'] == 'light'
        assert template.default_props['responsive'] is True

    def test_layout_template_get_components_json(self):
        """Test get_components_json method."""
        user = baker.make(User)
        components = {'header': {'type': 'header'}, 'footer': {'type': 'footer'}}

        template = LayoutTemplate.objects.create(
            name='JSON Test Layout',
            user=user,
            components=components
        )

        result = template.get_components_json()
        assert result == components
        assert result['header']['type'] == 'header'

    def test_layout_template_get_components_json_invalid(self):
        """Test get_components_json with invalid data."""
        user = baker.make(User)
        template = baker.make(LayoutTemplate, user=user, components="invalid")

        result = template.get_components_json()
        assert result == {}

    def test_layout_template_get_default_props_json(self):
        """Test get_default_props_json method."""
        user = baker.make(User)
        default_props = {'theme': 'dark', 'spacing': 'large'}

        template = LayoutTemplate.objects.create(
            name='Props Test Layout',
            user=user,
            default_props=default_props
        )

        result = template.get_default_props_json()
        assert result == default_props
        assert result['theme'] == 'dark'

    def test_layout_template_get_default_props_json_invalid(self):
        """Test get_default_props_json with invalid data."""
        user = baker.make(User)
        template = baker.make(LayoutTemplate, user=user, default_props="invalid")

        result = template.get_default_props_json()
        assert result == {}

    def test_layout_template_str_representation(self):
        """Test string representation of LayoutTemplate model."""
        user = baker.make(User)
        template = baker.make(LayoutTemplate, name='My Layout', user=user)

        assert str(template) == 'My Layout'


@pytest.mark.django_db
class TestAppTemplateModel:
    """Test cases for the AppTemplate model."""

    def test_app_template_creation(self):
        """Test basic app template creation."""
        user = baker.make(User)
        components = {
            'app': {
                'type': 'app',
                'children': [
                    {'type': 'button', 'props': {'text': 'Get Started'}},
                    {'type': 'form', 'props': {'fields': ['name', 'email']}}
                ]
            }
        }
        
        template = AppTemplate.objects.create(
            name='Contact Form App',
            description='A simple contact form application',
            app_category='business',
            components=components,
            user=user,
            preview_image='https://example.com/preview.jpg'
        )

        assert template.name == 'Contact Form App'
        assert template.app_category == 'business'
        assert template.preview_image == 'https://example.com/preview.jpg'
        assert 'app' in template.components

    def test_app_template_categories(self):
        """Test different app categories."""
        user = baker.make(User)

        categories = ['business', 'ecommerce', 'portfolio', 'blog', 'dashboard']

        for category in categories:
            template = AppTemplate.objects.create(
                name=f'{category.title()} Template',
                app_category=category,
                components={},
                user=user
            )
            assert template.app_category == category

    def test_app_template_get_components_json(self):
        """Test get_components_json method."""
        user = baker.make(User)
        components = {'app': {'type': 'app', 'children': []}}

        template = AppTemplate.objects.create(
            name='JSON Test App',
            user=user,
            components=components
        )

        result = template.get_components_json()
        assert result == components
        assert result['app']['type'] == 'app'

    def test_app_template_get_default_props_json(self):
        """Test get_default_props_json method."""
        user = baker.make(User)
        default_props = {'theme': 'modern', 'layout': 'responsive'}

        template = AppTemplate.objects.create(
            name='Props Test App',
            user=user,
            default_props=default_props
        )

        result = template.get_default_props_json()
        assert result == default_props
        assert result['theme'] == 'modern'

    def test_app_template_get_required_components_list(self):
        """Test get_required_components_list method."""
        user = baker.make(User)
        required_components = ['header', 'footer', 'navigation']

        template = AppTemplate.objects.create(
            name='Required Components Test',
            user=user,
            required_components=required_components
        )

        result = template.get_required_components_list()
        assert result == required_components
        assert 'header' in result
        assert len(result) == 3

    def test_app_template_str_representation(self):
        """Test string representation of AppTemplate model."""
        user = baker.make(User)
        template = baker.make(AppTemplate, name='My App Template', user=user)

        assert str(template) == 'My App Template'


@pytest.mark.django_db
class TestSessionParticipantModel:
    """Test cases for the SessionParticipant model."""

    def test_session_participant_creation(self):
        """Test basic session participant creation."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        participant = SessionParticipant.objects.create(
            session=session,
            user=user2,
            role='editor',
            cursor_position={'x': 100, 'y': 200},
            selected_component_id='button-1',
            current_view='canvas'
        )

        assert participant.session == session
        assert participant.user == user2
        assert participant.role == 'editor'
        assert participant.cursor_position == {'x': 100, 'y': 200}
        assert participant.selected_component_id == 'button-1'
        assert participant.current_view == 'canvas'
        assert participant.is_active is True

    def test_session_participant_defaults(self):
        """Test default values for session participant."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        participant = SessionParticipant.objects.create(
            session=session,
            user=user2
        )

        assert participant.role == 'editor'
        assert participant.cursor_position == {}
        assert participant.selected_component_id is None
        assert participant.current_view is None
        assert participant.is_active is True

    def test_session_participant_str_representation(self):
        """Test string representation of SessionParticipant model."""
        user1 = baker.make(User, username='owner')
        user2 = baker.make(User, username='participant')
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, name='Test Session', created_by=user1)
        participant = baker.make(
            SessionParticipant,
            session=session,
            user=user2,
            role='viewer'
        )

        assert str(participant) == 'participant in Test Session (viewer)'

    def test_session_participant_unique_constraint(self):
        """Test unique constraint on session and user."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        # Create first participant
        SessionParticipant.objects.create(
            session=session,
            user=user2,
            role='editor'
        )

        # Attempt to create duplicate participant
        with pytest.raises(IntegrityError):
            SessionParticipant.objects.create(
                session=session,
                user=user2,
                role='viewer'
            )

    def test_update_presence(self):
        """Test update_presence method."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)
        participant = SessionParticipant.objects.create(
            session=session,
            user=user2
        )

        original_last_seen = participant.last_seen

        # Update presence
        participant.update_presence(
            cursor_position={'x': 300, 'y': 400},
            selected_component_id='input-2',
            current_view='properties'
        )

        participant.refresh_from_db()
        assert participant.cursor_position == {'x': 300, 'y': 400}
        assert participant.selected_component_id == 'input-2'
        assert participant.current_view == 'properties'
        assert participant.last_seen > original_last_seen


@pytest.mark.django_db
class TestCommentModel:
    """Test cases for the Comment model."""

    def test_comment_creation(self):
        """Test basic comment creation."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        comment = Comment.objects.create(
            session=session,
            author=user2,
            content='This component needs better styling',
            component_id='button-1',
            canvas_position={'x': 150, 'y': 200, 'width': 100, 'height': 50},
            context_data={'type': 'feedback'}
        )

        assert comment.content == 'This component needs better styling'
        assert comment.component_id == 'button-1'
        assert comment.canvas_position == {'x': 150, 'y': 200, 'width': 100, 'height': 50}
        assert comment.context_data == {'type': 'feedback'}
        assert comment.session == session
        assert comment.author == user2
        assert comment.status == 'open'
        assert isinstance(comment.id, uuid.UUID)

    def test_comment_without_component_id(self):
        """Test comment creation without specific component."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        comment = Comment.objects.create(
            session=session,
            author=user2,
            content='General feedback about the app',
            canvas_position={'x': 100, 'y': 100}
        )

        assert comment.component_id is None
        assert comment.content == 'General feedback about the app'

    def test_comment_str_representation(self):
        """Test string representation of Comment model."""
        user1 = baker.make(User, username='owner')
        user2 = baker.make(User, username='commenter')
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, name='Test Session', created_by=user1)
        comment = baker.make(
            Comment,
            session=session,
            author=user2,
            content='Test comment'
        )

        assert str(comment) == 'Comment by commenter on Test Session'

    def test_comment_thread_properties(self):
        """Test thread-related properties."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        # Create root comment
        root_comment = Comment.objects.create(
            session=session,
            author=user1,
            content='Root comment'
        )

        # Create reply
        reply_comment = Comment.objects.create(
            session=session,
            author=user2,
            content='Reply comment',
            parent=root_comment
        )

        assert root_comment.is_thread_root is True
        assert reply_comment.is_thread_root is False
        assert root_comment.reply_count == 1
        assert reply_comment.reply_count == 0

    def test_comment_resolve_and_reopen(self):
        """Test resolve and reopen methods."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        comment = Comment.objects.create(
            session=session,
            author=user1,
            content='Test comment'
        )

        # Initially open
        assert comment.status == 'open'
        assert comment.resolved_at is None
        assert comment.resolved_by is None

        # Resolve comment
        comment.resolve(user2)
        assert comment.status == 'resolved'
        assert comment.resolved_at is not None
        assert comment.resolved_by == user2

        # Reopen comment
        comment.reopen()
        assert comment.status == 'open'
        assert comment.resolved_at is None
        assert comment.resolved_by is None

    def test_comment_mentions(self):
        """Test mentioned users functionality."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        user3 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        comment = Comment.objects.create(
            session=session,
            author=user1,
            content='Test comment with mentions'
        )

        comment.mentioned_users.add(user2, user3)

        assert comment.mentioned_users.count() == 2
        assert user2 in comment.mentioned_users.all()
        assert user3 in comment.mentioned_users.all()


@pytest.mark.django_db
class TestEditOperationModel:
    """Test cases for the EditOperation model."""

    def test_edit_operation_creation(self):
        """Test basic edit operation creation."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        operation = EditOperation.objects.create(
            session=session,
            user=user2,
            operation_type='component_add',
            target_id='button-1',
            operation_data={'type': 'button', 'props': {'text': 'Click me'}},
            vector_clock={'user1': 1, 'user2': 1},
            dependencies=['op-123', 'op-456']
        )

        assert operation.session == session
        assert operation.user == user2
        assert operation.operation_type == 'component_add'
        assert operation.target_id == 'button-1'
        assert operation.operation_data == {'type': 'button', 'props': {'text': 'Click me'}}
        assert operation.vector_clock == {'user1': 1, 'user2': 1}
        assert operation.dependencies == ['op-123', 'op-456']
        assert operation.is_applied is False
        assert isinstance(operation.id, uuid.UUID)

    def test_edit_operation_defaults(self):
        """Test default values for edit operation."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        operation = EditOperation.objects.create(
            session=session,
            user=user2,
            operation_type='component_update',
            target_id='button-1',
            operation_data={'props': {'text': 'Updated'}}
        )

        assert operation.vector_clock == {}
        assert operation.dependencies == []
        assert operation.is_applied is False
        assert operation.applied_at is None

    def test_edit_operation_str_representation(self):
        """Test string representation of EditOperation model."""
        user1 = baker.make(User, username='owner')
        user2 = baker.make(User, username='editor')
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)
        operation = baker.make(
            EditOperation,
            session=session,
            user=user2,
            operation_type='component_move',
            target_id='input-2'
        )

        assert str(operation) == 'component_move by editor on input-2'

    def test_apply_operation(self):
        """Test apply_operation method."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        operation = EditOperation.objects.create(
            session=session,
            user=user2,
            operation_type='component_delete',
            target_id='button-1',
            operation_data={}
        )

        # Initially not applied
        assert operation.is_applied is False
        assert operation.applied_at is None

        # Apply operation
        operation.apply_operation()
        assert operation.is_applied is True
        assert operation.applied_at is not None

    def test_edit_operation_types(self):
        """Test different operation types."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        operation_types = [
            'component_add', 'component_update', 'component_delete',
            'component_move', 'layout_update', 'style_update', 'data_update'
        ]

        for op_type in operation_types:
            operation = EditOperation.objects.create(
                session=session,
                user=user2,
                operation_type=op_type,
                target_id=f'target-{op_type}',
                operation_data={}
            )
            assert operation.operation_type == op_type


@pytest.mark.django_db
class TestUserActivityModel:
    """Test cases for the UserActivity model."""

    def test_user_activity_creation(self):
        """Test basic user activity creation."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        activity = UserActivity.objects.create(
            session=session,
            user=user2,
            activity_type='join',
            activity_data={'timestamp': '2023-01-01T10:00:00Z'}
        )

        assert activity.session == session
        assert activity.user == user2
        assert activity.activity_type == 'join'
        assert activity.activity_data == {'timestamp': '2023-01-01T10:00:00Z'}
        assert activity.timestamp is not None

    def test_user_activity_defaults(self):
        """Test default values for user activity."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        activity = UserActivity.objects.create(
            session=session,
            user=user2,
            activity_type='edit'
        )

        assert activity.activity_data == {}

    def test_user_activity_str_representation(self):
        """Test string representation of UserActivity model."""
        user1 = baker.make(User, username='owner')
        user2 = baker.make(User, username='participant')
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)
        activity = baker.make(
            UserActivity,
            session=session,
            user=user2,
            activity_type='comment'
        )

        expected_str = f'participant - comment at {activity.timestamp}'
        assert str(activity) == expected_str

    def test_user_activity_types(self):
        """Test different activity types."""
        user1 = baker.make(User)
        user2 = baker.make(User)
        app = baker.make(App, user=user1)
        session = baker.make(CollaborationSession, app=app, created_by=user1)

        activity_types = [
            'join', 'leave', 'edit', 'comment',
            'cursor_move', 'component_select', 'view_change'
        ]

        for activity_type in activity_types:
            activity = UserActivity.objects.create(
                session=session,
                user=user2,
                activity_type=activity_type
            )
            assert activity.activity_type == activity_type


@pytest.mark.django_db
class TestComponentTemplateModel:
    """Test cases for the ComponentTemplate model."""

    def test_component_template_creation(self):
        """Test basic component template creation."""
        user = baker.make(User)
        default_props = {'text': 'Click me', 'variant': 'primary'}

        template = ComponentTemplate.objects.create(
            name='Primary Button',
            description='A primary button component',
            component_type='button',
            default_props=json.dumps(default_props),
            user=user,
            is_public=True
        )

        assert template.name == 'Primary Button'
        assert template.description == 'A primary button component'
        assert template.component_type == 'button'
        assert template.user == user
        assert template.is_public is True

    def test_component_template_get_default_props_json(self):
        """Test get_default_props_json method."""
        user = baker.make(User)
        default_props = {'placeholder': 'Enter text', 'type': 'text'}

        template = ComponentTemplate.objects.create(
            name='Text Input',
            component_type='input',
            default_props=json.dumps(default_props),
            user=user
        )

        result = template.get_default_props_json()
        assert result == default_props
        assert result['placeholder'] == 'Enter text'

    def test_component_template_get_default_props_json_invalid(self):
        """Test get_default_props_json with invalid JSON."""
        user = baker.make(User)
        template = ComponentTemplate.objects.create(
            name='Invalid Props Template',
            component_type='button',
            default_props='invalid json',
            user=user
        )

        result = template.get_default_props_json()
        assert result == {}

    def test_component_template_str_representation(self):
        """Test string representation of ComponentTemplate model."""
        user = baker.make(User)
        template = baker.make(
            ComponentTemplate,
            name='My Component Template',
            user=user
        )

        assert str(template) == 'My Component Template'

    def test_component_template_without_user(self):
        """Test component template creation without user."""
        template = ComponentTemplate.objects.create(
            name='Public Template',
            component_type='card',
            default_props='{}',
            user=None,
            is_public=True
        )

        assert template.user is None
        assert template.is_public is True
