#!/usr/bin/env python
"""
Simple WebSocket server for testing WebSocket connections.
"""
import asyncio
import websockets
import json
import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('websocket-server')

# Connected clients
connected = set()

async def echo(websocket, path):
    """
    Handle a WebSocket connection.
    """
    # Register the client
    connected.add(websocket)
    client_id = id(websocket)
    client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
    
    logger.info(f"Client connected: {client_info} (ID: {client_id}) Path: {path}")
    
    try:
        # Send a welcome message
        await websocket.send(json.dumps({
            'type': 'welcome',
            'message': 'Welcome to the WebSocket server!',
            'timestamp': datetime.now().isoformat(),
            'client_id': client_id,
            'path': path
        }))
        
        # Handle messages
        async for message in websocket:
            logger.info(f"Received message from {client_info}: {message[:100]}{'...' if len(message) > 100 else ''}")
            
            try:
                # Try to parse as JSON
                data = json.loads(message)
                
                # Handle ping messages specially
                if isinstance(data, dict) and data.get('type') == 'ping':
                    await websocket.send(json.dumps({
                        'type': 'pong',
                        'timestamp': datetime.now().isoformat(),
                        'received_at': datetime.now().isoformat(),
                        'echo': data
                    }))
                else:
                    # Echo back the message
                    await websocket.send(json.dumps({
                        'type': 'echo',
                        'message': data,
                        'received': True,
                        'timestamp': datetime.now().isoformat()
                    }))
            except json.JSONDecodeError:
                # If not JSON, echo back as plain text
                await websocket.send(json.dumps({
                    'type': 'echo',
                    'message': message,
                    'received': True,
                    'timestamp': datetime.now().isoformat()
                }))
    except websockets.exceptions.ConnectionClosed as e:
        logger.info(f"Connection closed with client {client_info}: code={e.code}, reason={e.reason}")
    except Exception as e:
        logger.error(f"Error handling client {client_info}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # Unregister the client
        connected.remove(websocket)
        logger.info(f"Client disconnected: {client_info} (ID: {client_id})")

async def main():
    """
    Start the WebSocket server.
    """
    host = "localhost"
    port = 8765
    
    logger.info(f"Starting WebSocket server on {host}:{port}")
    
    async with websockets.serve(echo, host, port):
        logger.info(f"WebSocket server running at ws://{host}:{port}")
        await asyncio.Future()  # Run forever

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
