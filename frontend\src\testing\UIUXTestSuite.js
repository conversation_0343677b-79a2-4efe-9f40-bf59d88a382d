/**
 * UI/UX Test Suite
 *
 * Comprehensive testing suite for validating UI/UX improvements including
 * usability testing, accessibility validation, performance testing,
 * and cross-browser compatibility testing.
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Button, Progress, Alert, Space, Tabs, Typography, List, Tag, Statistic } from 'antd';
import {
    CheckCircleOutlined,
    ExclamationCircleOutlined,
    CloseCircleOutlined,
    LoadingOutlined,
    BugOutlined,
    RocketOutlined,
    EyeOutlined,
    AccessibilityOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme } from '../design-system';
import a11yUtils from '../utils/accessibility';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

// Styled components for test interface
const TestContainer = styled.div`
  padding: ${theme.spacing[6]};
  background: ${theme.colors.background.default};
  min-height: 100vh;
`;

const TestCard = styled(Card)`
  margin-bottom: ${theme.spacing[4]};

  .ant-card-head {
    background: ${theme.colors.background.tertiary};
  }

  &.test-passed {
    border-left: 4px solid ${theme.colors.success.main};
  }

  &.test-failed {
    border-left: 4px solid ${theme.colors.error.main};
  }

  &.test-warning {
    border-left: 4px solid ${theme.colors.warning.main};
  }
`;

const TestResult = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  margin-bottom: ${theme.spacing[2]};

  .test-icon {
    font-size: 16px;
  }

  .test-name {
    font-weight: ${theme.typography.fontWeight.medium};
    flex: 1;
  }

  .test-status {
    font-size: ${theme.typography.fontSize.sm};
  }
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${theme.spacing[4]};
  margin-bottom: ${theme.spacing[6]};
`;

// Test categories and their implementations
const TEST_CATEGORIES = {
    accessibility: {
        name: 'Accessibility Tests',
        icon: <AccessibilityOutlined />,
        description: 'WCAG 2.1 AA compliance and accessibility features',
    },
    usability: {
        name: 'Usability Tests',
        icon: <EyeOutlined />,
        description: 'User experience and interface usability',
    },
    performance: {
        name: 'Performance Tests',
        icon: <RocketOutlined />,
        description: 'Loading times, responsiveness, and optimization',
    },
    compatibility: {
        name: 'Browser Compatibility',
        icon: <BugOutlined />,
        description: 'Cross-browser and device compatibility',
    },
};

// Accessibility test implementations
const accessibilityTests = {
    colorContrast: {
        name: 'Color Contrast Ratios',
        description: 'Verify WCAG AA color contrast requirements',
        run: async () => {
            const elements = document.querySelectorAll('*');
            const failures = [];

            for (const element of elements) {
                const computedStyle = window.getComputedStyle(element);
                const color = computedStyle.color;
                const backgroundColor = computedStyle.backgroundColor;

                if (color && backgroundColor &&
                    color !== 'rgba(0, 0, 0, 0)' &&
                    backgroundColor !== 'rgba(0, 0, 0, 0)') {

                    const ratio = a11yUtils.contrast.getContrastRatio(color, backgroundColor);
                    if (ratio < 4.5) {
                        failures.push({
                            element: element.tagName,
                            ratio: ratio.toFixed(2),
                            required: '4.5:1',
                        });
                    }
                }
            }

            return {
                passed: failures.length === 0,
                score: Math.max(0, 100 - failures.length * 5),
                details: failures.length > 0 ? `${failures.length} contrast violations found` : 'All contrast ratios meet WCAG AA standards',
                failures,
            };
        },
    },

    keyboardNavigation: {
        name: 'Keyboard Navigation',
        description: 'Test keyboard accessibility and focus management',
        run: async () => {
            const interactiveElements = document.querySelectorAll(
                'button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
            );

            const issues = [];

            interactiveElements.forEach((element, index) => {
                // Check if element is focusable
                if (element.tabIndex < 0 && !element.hasAttribute('disabled')) {
                    issues.push(`Element ${index + 1} (${element.tagName}) not keyboard accessible`);
                }

                // Check for proper ARIA labels
                if (!element.getAttribute('aria-label') &&
                    !element.getAttribute('aria-labelledby') &&
                    !element.textContent.trim()) {
                    issues.push(`Element ${index + 1} (${element.tagName}) missing accessible name`);
                }
            });

            return {
                passed: issues.length === 0,
                score: Math.max(0, 100 - issues.length * 10),
                details: issues.length > 0 ? `${issues.length} keyboard navigation issues` : 'Keyboard navigation fully accessible',
                issues,
            };
        },
    },

    ariaAttributes: {
        name: 'ARIA Attributes',
        description: 'Validate proper ARIA attribute usage',
        run: async () => {
            const elementsWithAria = document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]');
            const violations = [];

            elementsWithAria.forEach((element) => {
                const audit = a11yUtils.testing.auditElement(element);
                if (!audit.isAccessible) {
                    violations.push(...audit.issues);
                }
            });

            return {
                passed: violations.length === 0,
                score: Math.max(0, 100 - violations.length * 8),
                details: violations.length > 0 ? `${violations.length} ARIA violations found` : 'All ARIA attributes properly implemented',
                violations,
            };
        },
    },

    focusManagement: {
        name: 'Focus Management',
        description: 'Test focus traps, restoration, and visual indicators',
        run: async () => {
            const focusableElements = document.querySelectorAll(
                'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
            );

            let focusIndicatorCount = 0;
            let skipLinkCount = 0;

            // Check for focus indicators
            focusableElements.forEach((element) => {
                const computedStyle = window.getComputedStyle(element, ':focus');
                if (computedStyle.outline !== 'none' || computedStyle.boxShadow !== 'none') {
                    focusIndicatorCount++;
                }
            });

            // Check for skip links
            skipLinkCount = document.querySelectorAll('.skip-link, [href^="#"]').length;

            const focusScore = (focusIndicatorCount / focusableElements.length) * 100;
            const hasSkipLinks = skipLinkCount > 0;

            return {
                passed: focusScore > 80 && hasSkipLinks,
                score: Math.min(100, focusScore + (hasSkipLinks ? 20 : 0)),
                details: `${focusIndicatorCount}/${focusableElements.length} elements have focus indicators. ${skipLinkCount} skip links found.`,
            };
        },
    },
};

// Usability test implementations
const usabilityTests = {
    visualHierarchy: {
        name: 'Visual Hierarchy',
        description: 'Test information architecture and visual organization',
        run: async () => {
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const buttons = document.querySelectorAll('button');
            const forms = document.querySelectorAll('form');

            let hierarchyScore = 0;
            const issues = [];

            // Check heading hierarchy
            let previousLevel = 0;
            headings.forEach((heading) => {
                const level = parseInt(heading.tagName.charAt(1));
                if (level > previousLevel + 1) {
                    issues.push(`Heading level skip: ${heading.tagName} after h${previousLevel}`);
                }
                previousLevel = level;
            });

            // Check button consistency
            const buttonStyles = new Set();
            buttons.forEach((button) => {
                const style = window.getComputedStyle(button);
                buttonStyles.add(`${style.fontSize}-${style.fontWeight}-${style.padding}`);
            });

            if (buttonStyles.size > 5) {
                issues.push('Inconsistent button styling detected');
            }

            hierarchyScore = Math.max(0, 100 - issues.length * 15);

            return {
                passed: issues.length === 0,
                score: hierarchyScore,
                details: issues.length > 0 ? `${issues.length} hierarchy issues found` : 'Visual hierarchy is well-structured',
                issues,
            };
        },
    },

    responsiveDesign: {
        name: 'Responsive Design',
        description: 'Test layout adaptation across different screen sizes',
        run: async () => {
            const breakpoints = [320, 768, 1024, 1440];
            const issues = [];
            let responsiveScore = 100;

            // Simulate different viewport sizes
            for (const width of breakpoints) {
                // Note: In a real implementation, you'd use tools like Puppeteer
                // to actually resize the viewport and test layouts

                // Check for horizontal scrollbars
                if (document.body.scrollWidth > width) {
                    issues.push(`Horizontal scroll at ${width}px width`);
                    responsiveScore -= 20;
                }

                // Check for overlapping elements (simplified check)
                const elements = document.querySelectorAll('*');
                let overlaps = 0;

                // This is a simplified overlap detection
                for (let i = 0; i < Math.min(elements.length, 50); i++) {
                    const rect = elements[i].getBoundingClientRect();
                    if (rect.width > width) {
                        overlaps++;
                    }
                }

                if (overlaps > 5) {
                    issues.push(`${overlaps} elements exceed viewport at ${width}px`);
                    responsiveScore -= 10;
                }
            }

            return {
                passed: issues.length === 0,
                score: Math.max(0, responsiveScore),
                details: issues.length > 0 ? `${issues.length} responsive issues found` : 'Responsive design working correctly',
                issues,
            };
        },
    },

    dragDropUsability: {
        name: 'Drag & Drop Usability',
        description: 'Test drag and drop interaction quality',
        run: async () => {
            const draggableElements = document.querySelectorAll('[draggable="true"]');
            const dropZones = document.querySelectorAll('[role="region"]');

            let usabilityScore = 0;
            const features = [];

            // Check for drag indicators
            const dragIndicators = document.querySelectorAll('.drag-indicator, [data-drag-indicator]');
            if (dragIndicators.length > 0) {
                features.push('Drag indicators present');
                usabilityScore += 25;
            }

            // Check for drop zone feedback
            const dropZoneStyles = Array.from(dropZones).some(zone => {
                const style = window.getComputedStyle(zone);
                return style.border.includes('dashed') || style.backgroundColor !== 'rgba(0, 0, 0, 0)';
            });

            if (dropZoneStyles) {
                features.push('Drop zone visual feedback');
                usabilityScore += 25;
            }

            // Check for accessibility features
            const accessibleDrag = Array.from(draggableElements).some(element =>
                element.hasAttribute('aria-grabbed') || element.hasAttribute('aria-describedby')
            );

            if (accessibleDrag) {
                features.push('Accessible drag and drop');
                usabilityScore += 25;
            }

            // Check for smooth animations
            const animatedElements = document.querySelectorAll('[style*="transition"], [class*="animate"]');
            if (animatedElements.length > 0) {
                features.push('Smooth animations');
                usabilityScore += 25;
            }

            return {
                passed: usabilityScore >= 75,
                score: usabilityScore,
                details: `Drag & drop features: ${features.join(', ')}`,
                features,
            };
        },
    },
};

// Performance test implementations
const performanceTests = {
    loadTime: {
        name: 'Initial Load Time',
        description: 'Measure time to interactive and first contentful paint',
        run: async () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            const paint = performance.getEntriesByType('paint');

            const loadTime = navigation.loadEventEnd - navigation.fetchStart;
            const fcp = paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0;

            let score = 100;
            if (loadTime > 3000) score -= 30;
            if (loadTime > 5000) score -= 40;
            if (fcp > 2000) score -= 20;

            return {
                passed: loadTime < 3000 && fcp < 2000,
                score: Math.max(0, score),
                details: `Load time: ${loadTime.toFixed(0)}ms, FCP: ${fcp.toFixed(0)}ms`,
                metrics: { loadTime, fcp },
            };
        },
    },

    memoryUsage: {
        name: 'Memory Usage',
        description: 'Check for memory leaks and efficient resource usage',
        run: async () => {
            if (!performance.memory) {
                return {
                    passed: true,
                    score: 100,
                    details: 'Memory API not available',
                };
            }

            const memory = performance.memory;
            const usedMB = memory.usedJSHeapSize / 1024 / 1024;
            const totalMB = memory.totalJSHeapSize / 1024 / 1024;
            const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;

            const usagePercent = (usedMB / limitMB) * 100;

            let score = 100;
            if (usagePercent > 50) score -= 20;
            if (usagePercent > 75) score -= 40;

            return {
                passed: usagePercent < 50,
                score: Math.max(0, score),
                details: `Memory usage: ${usedMB.toFixed(1)}MB (${usagePercent.toFixed(1)}% of limit)`,
                metrics: { usedMB, totalMB, limitMB, usagePercent },
            };
        },
    },

    renderPerformance: {
        name: 'Render Performance',
        description: 'Measure frame rate and rendering efficiency',
        run: async () => {
            return new Promise((resolve) => {
                let frameCount = 0;
                let startTime = performance.now();

                const measureFrames = () => {
                    frameCount++;

                    if (performance.now() - startTime < 1000) {
                        requestAnimationFrame(measureFrames);
                    } else {
                        const fps = frameCount;
                        let score = 100;

                        if (fps < 30) score -= 40;
                        if (fps < 20) score -= 40;

                        resolve({
                            passed: fps >= 30,
                            score: Math.max(0, score),
                            details: `Average FPS: ${fps}`,
                            metrics: { fps },
                        });
                    }
                };

                requestAnimationFrame(measureFrames);
            });
        },
    },
};

// Browser compatibility test implementations
const compatibilityTests = {
    browserSupport: {
        name: 'Browser Support',
        description: 'Check for modern browser feature support',
        run: async () => {
            const features = {
                'CSS Grid': CSS.supports('display', 'grid'),
                'CSS Flexbox': CSS.supports('display', 'flex'),
                'CSS Custom Properties': CSS.supports('color', 'var(--test)'),
                'ES6 Modules': typeof import !== 'undefined',
                'Fetch API': typeof fetch !== 'undefined',
                'Web Components': 'customElements' in window,
                'Service Workers': 'serviceWorker' in navigator,
                'WebSocket': typeof WebSocket !== 'undefined',
                'Local Storage': typeof localStorage !== 'undefined',
                'Session Storage': typeof sessionStorage !== 'undefined',
            };

            const supportedFeatures = Object.values(features).filter(Boolean).length;
            const totalFeatures = Object.keys(features).length;
            const supportPercentage = (supportedFeatures / totalFeatures) * 100;

            const unsupportedFeatures = Object.entries(features)
                .filter(([, supported]) => !supported)
                .map(([feature]) => feature);

            return {
                passed: supportPercentage >= 80,
                score: supportPercentage,
                details: `${supportedFeatures}/${totalFeatures} features supported`,
                features,
                unsupportedFeatures,
            };
        },
    },

    cssCompatibility: {
        name: 'CSS Compatibility',
        description: 'Test CSS feature support and fallbacks',
        run: async () => {
            const cssFeatures = {
                'CSS Grid': CSS.supports('display', 'grid'),
                'CSS Flexbox': CSS.supports('display', 'flex'),
                'CSS Variables': CSS.supports('color', 'var(--test)'),
                'CSS Transforms': CSS.supports('transform', 'translateX(10px)'),
                'CSS Transitions': CSS.supports('transition', 'all 0.3s ease'),
                'CSS Animations': CSS.supports('animation', 'test 1s ease'),
                'CSS Calc': CSS.supports('width', 'calc(100% - 10px)'),
                'CSS Viewport Units': CSS.supports('width', '100vw'),
            };

            const supportedCount = Object.values(cssFeatures).filter(Boolean).length;
            const totalCount = Object.keys(cssFeatures).length;
            const score = (supportedCount / totalCount) * 100;

            return {
                passed: score >= 90,
                score,
                details: `${supportedCount}/${totalCount} CSS features supported`,
                features: cssFeatures,
            };
        },
    },

    deviceCompatibility: {
        name: 'Device Compatibility',
        description: 'Test touch, mouse, and keyboard input support',
        run: async () => {
            const deviceFeatures = {
                'Touch Events': 'ontouchstart' in window,
                'Pointer Events': 'onpointerdown' in window,
                'Mouse Events': 'onmousedown' in window,
                'Keyboard Events': 'onkeydown' in window,
                'Orientation API': 'orientation' in screen,
                'Device Motion': 'DeviceMotionEvent' in window,
                'Vibration API': 'vibrate' in navigator,
                'Geolocation': 'geolocation' in navigator,
            };

            const supportedCount = Object.values(deviceFeatures).filter(Boolean).length;
            const totalCount = Object.keys(deviceFeatures).length;
            const score = (supportedCount / totalCount) * 100;

            return {
                passed: score >= 70,
                score,
                details: `${supportedCount}/${totalCount} device features supported`,
                features: deviceFeatures,
            };
        },
    },
};

// Main test suite component
export default function UIUXTestSuite({ onComplete, autoRun = false }) {
    const [testResults, setTestResults] = useState({});
    const [isRunning, setIsRunning] = useState(false);
    const [currentTest, setCurrentTest] = useState(null);
    const [overallScore, setOverallScore] = useState(0);
    const [activeTab, setActiveTab] = useState('accessibility');

    const allTests = {
        accessibility: accessibilityTests,
        usability: usabilityTests,
        performance: performanceTests,
        compatibility: compatibilityTests,
    };

    // Run a single test
    const runTest = useCallback(async (category, testKey, testConfig) => {
        setCurrentTest(`${category}.${testKey}`);

        try {
            const result = await testConfig.run();
            return {
                ...result,
                category,
                testKey,
                name: testConfig.name,
                description: testConfig.description,
                timestamp: new Date().toISOString(),
            };
        } catch (error) {
            return {
                passed: false,
                score: 0,
                details: `Test failed: ${error.message}`,
                category,
                testKey,
                name: testConfig.name,
                description: testConfig.description,
                timestamp: new Date().toISOString(),
                error: error.message,
            };
        }
    }, []);

    // Run all tests in a category
    const runCategoryTests = useCallback(async (category) => {
        const tests = allTests[category];
        const results = {};

        for (const [testKey, testConfig] of Object.entries(tests)) {
            const result = await runTest(category, testKey, testConfig);
            results[testKey] = result;

            // Update results incrementally
            setTestResults(prev => ({
                ...prev,
                [category]: {
                    ...prev[category],
                    [testKey]: result,
                },
            }));
        }

        return results;
    }, [allTests, runTest]);

    // Run all tests
    const runAllTests = useCallback(async () => {
        setIsRunning(true);
        setTestResults({});
        setOverallScore(0);

        const allResults = {};

        for (const category of Object.keys(allTests)) {
            const categoryResults = await runCategoryTests(category);
            allResults[category] = categoryResults;
        }

        // Calculate overall score
        let totalScore = 0;
        let testCount = 0;

        Object.values(allResults).forEach(categoryResults => {
            Object.values(categoryResults).forEach(result => {
                totalScore += result.score;
                testCount++;
            });
        });

        const finalScore = testCount > 0 ? Math.round(totalScore / testCount) : 0;
        setOverallScore(finalScore);
        setCurrentTest(null);
        setIsRunning(false);

        if (onComplete) {
            onComplete(allResults, finalScore);
        }
    }, [allTests, runCategoryTests, onComplete]);

    // Auto-run tests on mount if enabled
    useEffect(() => {
        if (autoRun) {
            runAllTests();
        }
    }, [autoRun, runAllTests]);

    // Get test status icon
    const getTestIcon = (result) => {
        if (!result) return <LoadingOutlined className="test-icon" />;
        if (result.passed) return <CheckCircleOutlined className="test-icon" style={{ color: theme.colors.success.main }} />;
        if (result.score > 50) return <ExclamationCircleOutlined className="test-icon" style={{ color: theme.colors.warning.main }} />;
        return <CloseCircleOutlined className="test-icon" style={{ color: theme.colors.error.main }} />;
    };

    // Get test status text
    const getTestStatus = (result) => {
        if (!result) return 'Pending';
        if (result.passed) return 'Passed';
        if (result.score > 50) return 'Warning';
        return 'Failed';
    };

    // Get test card class
    const getTestCardClass = (result) => {
        if (!result) return '';
        if (result.passed) return 'test-passed';
        if (result.score > 50) return 'test-warning';
        return 'test-failed';
    };

    // Calculate category scores
    const getCategoryScore = (category) => {
        const categoryResults = testResults[category];
        if (!categoryResults) return 0;

        const scores = Object.values(categoryResults).map(result => result.score);
        return scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0;
    };

    // Get category status
    const getCategoryStatus = (category) => {
        const score = getCategoryScore(category);
        if (score >= 80) return 'success';
        if (score >= 60) return 'warning';
        return 'error';
    };

    return (
        <TestContainer>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {/* Header */}
                <div>
                    <Title level={2}>UI/UX Test Suite</Title>
                    <Paragraph>
                        Comprehensive testing suite for validating UI/UX improvements including
                        accessibility, usability, performance, and browser compatibility.
                    </Paragraph>
                </div>

                {/* Overall Metrics */}
                <MetricsGrid>
                    <Card>
                        <Statistic
                            title="Overall Score"
                            value={overallScore}
                            suffix="/ 100"
                            valueStyle={{ color: overallScore >= 80 ? theme.colors.success.main : overallScore >= 60 ? theme.colors.warning.main : theme.colors.error.main }}
                        />
                    </Card>
                    <Card>
                        <Statistic
                            title="Tests Run"
                            value={Object.values(testResults).reduce((total, category) => total + Object.keys(category).length, 0)}
                            suffix={`/ ${Object.values(allTests).reduce((total, tests) => total + Object.keys(tests).length, 0)}`}
                        />
                    </Card>
                    <Card>
                        <Statistic
                            title="Tests Passed"
                            value={Object.values(testResults).reduce((total, category) =>
                                total + Object.values(category).filter(result => result.passed).length, 0
                            )}
                            valueStyle={{ color: theme.colors.success.main }}
                        />
                    </Card>
                    <Card>
                        <Statistic
                            title="Tests Failed"
                            value={Object.values(testResults).reduce((total, category) =>
                                total + Object.values(category).filter(result => !result.passed).length, 0
                            )}
                            valueStyle={{ color: theme.colors.error.main }}
                        />
                    </Card>
                </MetricsGrid>

                {/* Controls */}
                <Space>
                    <Button
                        type="primary"
                        onClick={runAllTests}
                        loading={isRunning}
                        disabled={isRunning}
                    >
                        {isRunning ? 'Running Tests...' : 'Run All Tests'}
                    </Button>

                    {Object.keys(allTests).map(category => (
                        <Button
                            key={category}
                            onClick={() => runCategoryTests(category)}
                            disabled={isRunning}
                        >
                            Run {TEST_CATEGORIES[category].name}
                        </Button>
                    ))}
                </Space>

                {/* Current Test Indicator */}
                {isRunning && currentTest && (
                    <Alert
                        message={`Running: ${currentTest}`}
                        type="info"
                        showIcon
                        icon={<LoadingOutlined />}
                    />
                )}

                {/* Test Results */}
                <Tabs activeKey={activeTab} onChange={setActiveTab}>
                    {Object.entries(TEST_CATEGORIES).map(([categoryKey, categoryInfo]) => (
                        <TabPane
                            tab={
                                <Space>
                                    {categoryInfo.icon}
                                    {categoryInfo.name}
                                    {testResults[categoryKey] && (
                                        <Tag color={getCategoryStatus(categoryKey)}>
                                            {getCategoryScore(categoryKey)}%
                                        </Tag>
                                    )}
                                </Space>
                            }
                            key={categoryKey}
                        >
                            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                                <Paragraph>{categoryInfo.description}</Paragraph>

                                {Object.entries(allTests[categoryKey]).map(([testKey, testConfig]) => {
                                    const result = testResults[categoryKey]?.[testKey];

                                    return (
                                        <TestCard
                                            key={testKey}
                                            title={
                                                <TestResult>
                                                    {getTestIcon(result)}
                                                    <span className="test-name">{testConfig.name}</span>
                                                    <Tag className="test-status" color={result ? (result.passed ? 'success' : result.score > 50 ? 'warning' : 'error') : 'default'}>
                                                        {getTestStatus(result)}
                                                    </Tag>
                                                    {result && (
                                                        <Tag color="blue">{result.score}%</Tag>
                                                    )}
                                                </TestResult>
                                            }
                                            className={getTestCardClass(result)}
                                            size="small"
                                        >
                                            <Space direction="vertical" style={{ width: '100%' }}>
                                                <Text type="secondary">{testConfig.description}</Text>

                                                {result && (
                                                    <>
                                                        <Text>{result.details}</Text>

                                                        {result.issues && result.issues.length > 0 && (
                                                            <List
                                                                size="small"
                                                                header={<Text strong>Issues Found:</Text>}
                                                                dataSource={result.issues.slice(0, 5)}
                                                                renderItem={issue => (
                                                                    <List.Item>
                                                                        <Text type="danger">{issue}</Text>
                                                                    </List.Item>
                                                                )}
                                                            />
                                                        )}

                                                        {result.features && (
                                                            <div>
                                                                <Text strong>Features: </Text>
                                                                {Object.entries(result.features).map(([feature, supported]) => (
                                                                    <Tag key={feature} color={supported ? 'success' : 'error'}>
                                                                        {feature}
                                                                    </Tag>
                                                                ))}
                                                            </div>
                                                        )}

                                                        {result.metrics && (
                                                            <div>
                                                                <Text strong>Metrics: </Text>
                                                                {Object.entries(result.metrics).map(([metric, value]) => (
                                                                    <Tag key={metric} color="blue">
                                                                        {metric}: {typeof value === 'number' ? value.toFixed(1) : value}
                                                                    </Tag>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </>
                                                )}
                                            </Space>
                                        </TestCard>
                                    );
                                })}
                            </Space>
                        </TabPane>
                    ))}
                </Tabs>
            </Space>
        </TestContainer>
    );
}