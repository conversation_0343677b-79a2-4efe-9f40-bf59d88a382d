import React from 'react';
import { Form, Switch, Select, Card, Space, Typography, Divider, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

const { Text, Title } = Typography;
const { Option } = Select;

/**
 * Export Settings
 * 
 * Configuration panel for code export options and preferences.
 */

const ExportSettings = ({
  settings = {},
  onChange,
  format = 'react'
}) => {
  const handleSettingChange = (key, value) => {
    onChange({
      ...settings,
      [key]: value
    });
  };

  const getFormatSpecificSettings = () => {
    switch (format) {
      case 'react':
      case 'react-native':
        return (
          <>
            <Form.Item label={
              <Space>
                TypeScript
                <Tooltip title="Generate TypeScript code instead of JavaScript">
                  <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }>
              <Switch
                checked={settings.typescript}
                onChange={(checked) => handleSettingChange('typescript', checked)}
              />
            </Form.Item>

            <Form.Item label={
              <Space>
                Functional Components
                <Tooltip title="Use functional components instead of class components">
                  <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }>
              <Switch
                checked={settings.functionalComponents !== false}
                onChange={(checked) => handleSettingChange('functionalComponents', checked)}
              />
            </Form.Item>

            <Form.Item label="State Management">
              <Select
                value={settings.stateManagement || 'useState'}
                onChange={(value) => handleSettingChange('stateManagement', value)}
                style={{ width: '100%' }}
              >
                <Option value="useState">React Hooks (useState)</Option>
                <Option value="redux">Redux</Option>
                <Option value="zustand">Zustand</Option>
                <Option value="context">Context API</Option>
              </Select>
            </Form.Item>
          </>
        );

      case 'vue':
        return (
          <>
            <Form.Item label="Vue Version">
              <Select
                value={settings.vueVersion || '3'}
                onChange={(value) => handleSettingChange('vueVersion', value)}
                style={{ width: '100%' }}
              >
                <Option value="2">Vue 2</Option>
                <Option value="3">Vue 3</Option>
              </Select>
            </Form.Item>

            <Form.Item label={
              <Space>
                Composition API
                <Tooltip title="Use Composition API instead of Options API">
                  <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }>
              <Switch
                checked={settings.compositionAPI}
                onChange={(checked) => handleSettingChange('compositionAPI', checked)}
                disabled={settings.vueVersion === '2'}
              />
            </Form.Item>
          </>
        );

      case 'angular':
        return (
          <>
            <Form.Item label="Angular Version">
              <Select
                value={settings.angularVersion || '15'}
                onChange={(value) => handleSettingChange('angularVersion', value)}
                style={{ width: '100%' }}
              >
                <Option value="12">Angular 12</Option>
                <Option value="13">Angular 13</Option>
                <Option value="14">Angular 14</Option>
                <Option value="15">Angular 15</Option>
                <Option value="16">Angular 16</Option>
              </Select>
            </Form.Item>

            <Form.Item label={
              <Space>
                Standalone Components
                <Tooltip title="Use standalone components (Angular 14+)">
                  <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
                </Tooltip>
              </Space>
            }>
              <Switch
                checked={settings.standaloneComponents}
                onChange={(checked) => handleSettingChange('standaloneComponents', checked)}
              />
            </Form.Item>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      <Title level={4}>Export Settings</Title>
      
      <Form layout="vertical">
        {/* General Settings */}
        <Card title="General" size="small" style={{ marginBottom: 16 }}>
          <Form.Item label={
            <Space>
              Include Styles
              <Tooltip title="Include CSS/styling files in the export">
                <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            </Space>
          }>
            <Switch
              checked={settings.includeStyles !== false}
              onChange={(checked) => handleSettingChange('includeStyles', checked)}
            />
          </Form.Item>

          <Form.Item label={
            <Space>
              Include Tests
              <Tooltip title="Generate test files for components">
                <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            </Space>
          }>
            <Switch
              checked={settings.includeTests}
              onChange={(checked) => handleSettingChange('includeTests', checked)}
            />
          </Form.Item>

          <Form.Item label={
            <Space>
              Bundled Export
              <Tooltip title="Export as a single file instead of multiple files">
                <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            </Space>
          }>
            <Switch
              checked={settings.bundled}
              onChange={(checked) => handleSettingChange('bundled', checked)}
            />
          </Form.Item>

          <Form.Item label="Code Style">
            <Select
              value={settings.codeStyle || 'prettier'}
              onChange={(value) => handleSettingChange('codeStyle', value)}
              style={{ width: '100%' }}
            >
              <Option value="prettier">Prettier</Option>
              <Option value="eslint">ESLint</Option>
              <Option value="standard">Standard</Option>
              <Option value="none">None</Option>
            </Select>
          </Form.Item>
        </Card>

        {/* Framework Specific Settings */}
        {getFormatSpecificSettings() && (
          <Card title={`${format.charAt(0).toUpperCase() + format.slice(1)} Settings`} size="small" style={{ marginBottom: 16 }}>
            {getFormatSpecificSettings()}
          </Card>
        )}

        {/* Styling Settings */}
        <Card title="Styling" size="small" style={{ marginBottom: 16 }}>
          <Form.Item label="CSS Framework">
            <Select
              value={settings.cssFramework || 'none'}
              onChange={(value) => handleSettingChange('cssFramework', value)}
              style={{ width: '100%' }}
            >
              <Option value="none">None</Option>
              <Option value="tailwind">Tailwind CSS</Option>
              <Option value="bootstrap">Bootstrap</Option>
              <Option value="antd">Ant Design</Option>
              <Option value="material-ui">Material-UI</Option>
              <Option value="chakra">Chakra UI</Option>
            </Select>
          </Form.Item>

          <Form.Item label="CSS Preprocessor">
            <Select
              value={settings.cssPreprocessor || 'css'}
              onChange={(value) => handleSettingChange('cssPreprocessor', value)}
              style={{ width: '100%' }}
            >
              <Option value="css">CSS</Option>
              <Option value="scss">SCSS</Option>
              <Option value="sass">Sass</Option>
              <Option value="less">Less</Option>
              <Option value="styled-components">Styled Components</Option>
            </Select>
          </Form.Item>

          <Form.Item label={
            <Space>
              Responsive Design
              <Tooltip title="Include responsive breakpoints and mobile-first design">
                <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            </Space>
          }>
            <Switch
              checked={settings.responsive !== false}
              onChange={(checked) => handleSettingChange('responsive', checked)}
            />
          </Form.Item>
        </Card>

        {/* Build Settings */}
        <Card title="Build Configuration" size="small">
          <Form.Item label="Build Tool">
            <Select
              value={settings.buildTool || 'webpack'}
              onChange={(value) => handleSettingChange('buildTool', value)}
              style={{ width: '100%' }}
            >
              <Option value="webpack">Webpack</Option>
              <Option value="vite">Vite</Option>
              <Option value="parcel">Parcel</Option>
              <Option value="rollup">Rollup</Option>
              <Option value="create-react-app">Create React App</Option>
            </Select>
          </Form.Item>

          <Form.Item label={
            <Space>
              Include Package.json
              <Tooltip title="Include package.json with dependencies">
                <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            </Space>
          }>
            <Switch
              checked={settings.includePackageJson !== false}
              onChange={(checked) => handleSettingChange('includePackageJson', checked)}
            />
          </Form.Item>

          <Form.Item label={
            <Space>
              Include README
              <Tooltip title="Generate README.md with setup instructions">
                <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            </Space>
          }>
            <Switch
              checked={settings.includeReadme}
              onChange={(checked) => handleSettingChange('includeReadme', checked)}
            />
          </Form.Item>
        </Card>
      </Form>
    </div>
  );
};

export default ExportSettings;
