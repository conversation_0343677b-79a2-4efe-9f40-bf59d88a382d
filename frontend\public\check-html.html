<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Check - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 HTML Source Check</h1>
        <p>This page fetches and displays the HTML source of the main page to check if webpack is injecting scripts correctly.</p>
        
        <div>
            <button onclick="fetchHTML()">🔄 Fetch Main Page HTML</button>
            <button onclick="checkScripts()">📦 Check Script Tags</button>
            <button onclick="clearOutput()">🗑️ Clear Output</button>
        </div>

        <div id="status"></div>

        <h3>📋 HTML Source</h3>
        <pre id="html-output">Click "Fetch Main Page HTML" to see the source...</pre>

        <h3>📦 Script Analysis</h3>
        <div id="script-analysis"></div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            document.getElementById('status').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
        }

        function clearOutput() {
            document.getElementById('html-output').textContent = '';
            document.getElementById('script-analysis').innerHTML = '';
            updateStatus('Output cleared', 'info');
        }

        async function fetchHTML() {
            updateStatus('🔄 Fetching main page HTML...', 'info');
            
            try {
                const response = await fetch('/');
                if (response.ok) {
                    const html = await response.text();
                    document.getElementById('html-output').textContent = html;
                    updateStatus('✅ HTML fetched successfully', 'success');
                    
                    // Auto-analyze scripts
                    analyzeScripts(html);
                } else {
                    updateStatus(`❌ Failed to fetch HTML: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ Error fetching HTML: ${error.message}`, 'error');
            }
        }

        function analyzeScripts(html) {
            const scriptMatches = html.match(/<script[^>]*src="[^"]*"[^>]*>/g) || [];
            const linkMatches = html.match(/<link[^>]*href="[^"]*\.css"[^>]*>/g) || [];
            
            let analysis = '<h4>📦 Script Tags Found:</h4>';
            if (scriptMatches.length > 0) {
                analysis += '<ul>';
                scriptMatches.forEach((script, index) => {
                    analysis += `<li><code>${script}</code></li>`;
                });
                analysis += '</ul>';
            } else {
                analysis += '<p style="color: red;">❌ No script tags found!</p>';
            }
            
            analysis += '<h4>🎨 CSS Link Tags Found:</h4>';
            if (linkMatches.length > 0) {
                analysis += '<ul>';
                linkMatches.forEach((link, index) => {
                    analysis += `<li><code>${link}</code></li>`;
                });
                analysis += '</ul>';
            } else {
                analysis += '<p style="color: red;">❌ No CSS link tags found!</p>';
            }
            
            // Check for main bundle specifically
            const mainBundleMatch = html.match(/static\/js\/main\.[a-f0-9]+\.js/);
            if (mainBundleMatch) {
                analysis += `<h4>✅ Main Bundle Found:</h4><p><code>${mainBundleMatch[0]}</code></p>`;
            } else {
                analysis += '<h4>❌ Main Bundle NOT Found</h4>';
            }
            
            // Check for root element
            if (html.includes('id="root"')) {
                analysis += '<h4>✅ Root Element Found</h4>';
            } else {
                analysis += '<h4>❌ Root Element NOT Found</h4>';
            }
            
            document.getElementById('script-analysis').innerHTML = analysis;
        }

        function checkScripts() {
            const htmlContent = document.getElementById('html-output').textContent;
            if (htmlContent) {
                analyzeScripts(htmlContent);
                updateStatus('✅ Script analysis completed', 'success');
            } else {
                updateStatus('❌ No HTML content to analyze. Fetch HTML first.', 'error');
            }
        }

        // Auto-fetch on page load
        window.addEventListener('load', () => {
            setTimeout(fetchHTML, 1000);
        });
    </script>
</body>
</html>
