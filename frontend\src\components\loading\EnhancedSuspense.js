import React, { Suspense, Component } from 'react';
import { <PERSON>, Alert, Button, Typography, Progress } from 'antd';
import { ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Text } = Typography;

/**
 * Enhanced Suspense Component
 * 
 * Provides better loading states, error boundaries, and retry functionality
 * for lazy-loaded components with progressive loading indicators.
 */

const SuspenseContainer = styled.div`
  position: relative;
  min-height: ${props => props.minHeight || '200px'};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${props => props.padding || '20px'};
  background: ${props => props.background || 'transparent'};
`;

const LoadingContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 400px;
  text-align: center;
`;

const ProgressContainer = styled.div`
  width: 100%;
  max-width: 300px;
`;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-radius: 8px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  max-width: 400px;
`;

// Enhanced loading component with progress indication
const EnhancedLoadingFallback = ({
  message = 'Loading...',
  description = null,
  showProgress = false,
  progress = 0,
  size = 'large',
  minHeight = '200px',
  background = 'transparent',
  timeout = 10000
}) => {
  const [timeoutReached, setTimeoutReached] = React.useState(false);

  React.useEffect(() => {
    if (timeout > 0) {
      const timer = setTimeout(() => {
        setTimeoutReached(true);
      }, timeout);

      return () => clearTimeout(timer);
    }
  }, [timeout]);

  if (timeoutReached) {
    return (
      <SuspenseContainer minHeight={minHeight} background={background}>
        <Alert
          message="Loading is taking longer than expected"
          description="The component is still loading. Please wait or try refreshing the page."
          type="warning"
          showIcon
          icon={<ExclamationCircleOutlined />}
          action={
            <Button size="small" onClick={() => window.location.reload()}>
              Refresh
            </Button>
          }
        />
      </SuspenseContainer>
    );
  }

  return (
    <SuspenseContainer minHeight={minHeight} background={background}>
      <LoadingContent>
        <Spin size={size} />
        <div>
          <Text strong style={{ fontSize: 16 }}>{message}</Text>
          {description && (
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 14 }}>
                {description}
              </Text>
            </div>
          )}
        </div>
        
        {showProgress && (
          <ProgressContainer>
            <Progress 
              percent={progress} 
              size="small" 
              showInfo={false}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {progress}% loaded
            </Text>
          </ProgressContainer>
        )}
      </LoadingContent>
    </SuspenseContainer>
  );
};

// Error boundary for Suspense components
class SuspenseErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Suspense Error Boundary caught an error:', error, errorInfo);
    
    // Report to error tracking service
    if (window.reportError) {
      window.reportError(error, { 
        context: 'suspense-boundary',
        component: this.props.componentName,
        ...errorInfo 
      });
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  render() {
    if (this.state.hasError) {
      const { componentName = 'Component', maxRetries = 3 } = this.props;
      const canRetry = this.state.retryCount < maxRetries;

      return (
        <SuspenseContainer>
          <ErrorContainer>
            <ExclamationCircleOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />
            <div>
              <Text strong style={{ color: '#ff4d4f' }}>
                Failed to load {componentName}
              </Text>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {this.state.error?.message || 'An unexpected error occurred'}
                </Text>
              </div>
            </div>
            
            {canRetry ? (
              <Button 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={this.handleRetry}
              >
                Try Again ({maxRetries - this.state.retryCount} attempts left)
              </Button>
            ) : (
              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Maximum retry attempts reached. Please refresh the page.
                </Text>
                <Button 
                  style={{ marginTop: 8 }}
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
              </div>
            )}
          </ErrorContainer>
        </SuspenseContainer>
      );
    }

    return this.props.children;
  }
}

// Enhanced Suspense wrapper component
const EnhancedSuspense = ({
  children,
  fallback = null,
  componentName = 'Component',
  loadingMessage = 'Loading...',
  loadingDescription = null,
  showProgress = false,
  progress = 0,
  minHeight = '200px',
  background = 'transparent',
  timeout = 10000,
  maxRetries = 3,
  onError = null
}) => {
  const defaultFallback = fallback || (
    <EnhancedLoadingFallback
      message={loadingMessage}
      description={loadingDescription}
      showProgress={showProgress}
      progress={progress}
      minHeight={minHeight}
      background={background}
      timeout={timeout}
    />
  );

  return (
    <SuspenseErrorBoundary 
      componentName={componentName}
      maxRetries={maxRetries}
      onError={onError}
    >
      <Suspense fallback={defaultFallback}>
        {children}
      </Suspense>
    </SuspenseErrorBoundary>
  );
};

// Predefined loading configurations
export const LoadingConfigurations = {
  page: {
    minHeight: '100vh',
    background: '#f5f5f5',
    loadingMessage: 'Loading page...',
    timeout: 15000
  },
  
  component: {
    minHeight: '200px',
    background: 'transparent',
    loadingMessage: 'Loading component...',
    timeout: 10000
  },
  
  modal: {
    minHeight: '300px',
    background: 'white',
    loadingMessage: 'Loading...',
    timeout: 8000
  },
  
  inline: {
    minHeight: '100px',
    background: 'transparent',
    loadingMessage: 'Loading...',
    timeout: 5000
  }
};

export { EnhancedLoadingFallback, SuspenseErrorBoundary };
export default EnhancedSuspense;
