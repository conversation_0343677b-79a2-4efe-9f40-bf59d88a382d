/**
 * Enhanced Component Palette with improved visual hierarchy and categorization
 */

import React, { useState, useRef, useCallback, useMemo } from 'react';
import {
  Typography,
  Input,
  Card,
  Space,
  Switch,
  Badge,
  Dropdown,
  Menu,
  Button,
  Tooltip,
  Collapse,
  Tag
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  DownOutlined,
  LayoutOutlined,
  AppstoreOutlined,
  FileTextOutlined,
  FontSizeOutlined,
  CreditCardOutlined,
  BarsOutlined,
  PictureOutlined,
  OrderedListOutlined,
  TagsOutlined,
  RobotOutlined,
  DragOutlined,
  StarOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme, a11yUtils } from '../../design-system';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';

const { Search } = Input;
const { Text, Title } = Typography;
const { Panel } = Collapse;

// Enhanced styled components
const PaletteContainer = styled.div`
  background: ${theme.colors.background.paper};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.lg};
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid ${theme.colors.border.light};
`;

const PaletteHeader = styled.div`
  padding: ${theme.spacing[4]};
  background: linear-gradient(135deg, ${theme.colors.primary.main} 0%, ${theme.colors.accent.main} 100%);
  color: ${theme.colors.primary.contrastText};
  border-bottom: 1px solid ${theme.colors.border.light};
  
  .ant-typography {
    color: ${theme.colors.primary.contrastText} !important;
    margin-bottom: ${theme.spacing[2]};
  }
`;

const SearchContainer = styled.div`
  padding: ${theme.spacing[3]} ${theme.spacing[4]};
  background: ${theme.colors.background.secondary};
  border-bottom: 1px solid ${theme.colors.border.light};
`;

const FilterControls = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${theme.spacing[2]};
  flex-wrap: wrap;
  gap: ${theme.spacing[2]};
`;

const ComponentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: ${theme.spacing[2]};
  padding: ${theme.spacing[3]};
  
  ${theme.mediaQueries.maxMd} {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: ${theme.spacing[1]};
    padding: ${theme.spacing[2]};
  }
`;

const ComponentCard = styled(Card)`
  cursor: grab;
  transition: ${theme.transitions.default};
  border: 1px solid ${theme.colors.border.light};
  border-radius: ${theme.borderRadius.md};
  position: relative;
  min-height: 140px;
  overflow: hidden;
  background: ${props => props.isDragging ? theme.colors.primary.light : theme.colors.background.paper};
  
  /* Enhanced visual hierarchy */
  box-shadow: ${theme.shadows.sm};
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: ${theme.shadows.xl};
    border-color: ${theme.colors.primary.main};
    background: ${theme.colors.primary.main}05;
    
    .component-icon {
      transform: scale(1.15);
      background: ${theme.colors.primary.main}20;
    }
    
    .component-label {
      color: ${theme.colors.primary.main};
      font-weight: ${theme.typography.fontWeight.semibold};
    }
  }
  
  &:active {
    cursor: grabbing;
    transform: scale(0.95) translateY(-1px);
  }
  
  &:focus-visible {
    outline: 2px solid ${theme.colors.primary.main};
    outline-offset: 2px;
  }
  
  /* Complexity indicator */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: ${props => {
    switch (props.complexity) {
      case 'simple': return theme.colors.success.main;
      case 'medium': return theme.colors.warning.main;
      case 'complex': return theme.colors.error.main;
      default: return theme.colors.neutral[300];
    }
  }};
    opacity: 0.8;
  }
  
  .ant-card-body {
    padding: ${theme.spacing[3]};
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
`;

const ComponentIcon = styled.div`
  font-size: 28px;
  color: ${props => props.color || theme.colors.primary.main};
  margin-bottom: ${theme.spacing[2]};
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: ${theme.borderRadius.lg};
  background: ${props => props.gradient || `linear-gradient(135deg, ${theme.colors.primary.main}15 0%, ${theme.colors.primary.main}25 100%)`};
  margin: 0 auto ${theme.spacing[2]};
  transition: ${theme.transitions.default};
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  @media (prefers-contrast: high) {
    border: 2px solid currentColor;
    background: transparent;
  }
`;

const ComponentLabel = styled.div`
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.text.primary};
  margin-bottom: ${theme.spacing[1]};
  line-height: ${theme.typography.lineHeight.tight};
`;

const ComponentDescription = styled.div`
  font-size: ${theme.typography.fontSize.xs};
  color: ${theme.colors.text.secondary};
  line-height: ${theme.typography.lineHeight.normal};
  text-align: center;
  margin-top: ${theme.spacing[1]};
`;

const ComponentPreview = styled.div`
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 24px;
  height: 16px;
  background: ${theme.colors.neutral[100]};
  border: 1px solid ${theme.colors.border.light};
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: ${theme.colors.text.secondary};
  opacity: 0.7;
  transition: ${theme.transitions.default};
  
  ${ComponentCard}:hover & {
    opacity: 1;
    transform: scale(1.1);
    background: ${theme.colors.background.paper};
    box-shadow: ${theme.shadows.sm};
  }
`;

const ComplexityBadge = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => {
    switch (props.complexity) {
      case 'simple': return theme.colors.success.main;
      case 'medium': return theme.colors.warning.main;
      case 'complex': return theme.colors.error.main;
      default: return theme.colors.neutral[300];
    }
  }};
  opacity: 0.8;
  transition: ${theme.transitions.default};
  
  ${ComponentCard}:hover & {
    opacity: 1;
    transform: scale(1.2);
  }
`;

const CategoryHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${theme.spacing[3]} ${theme.spacing[4]};
  background: ${theme.colors.background.tertiary};
  border-bottom: 1px solid ${theme.colors.border.light};
  font-weight: ${theme.typography.fontWeight.semibold};
  color: ${theme.colors.text.primary};
  cursor: pointer;
  transition: ${theme.transitions.default};
  
  &:hover {
    background: ${theme.colors.interactive.hover};
  }
  
  &:focus {
    ${a11yUtils.focusRing()};
  }
`;

const ScrollableContent = styled.div`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${theme.colors.background.secondary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.border.medium};
    border-radius: ${theme.borderRadius.full};
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: ${theme.colors.border.dark};
  }
`;

// Component data with enhanced metadata
const COMPONENT_GROUPS = [
  {
    id: 'layout',
    title: 'Layout',
    description: 'Structural components for organizing content',
    color: theme.colors.success.main,
    icon: <LayoutOutlined />,
    components: [
      {
        type: 'header',
        icon: <FontSizeOutlined />,
        label: 'Header',
        description: 'Page or section header with title and navigation',
        usage: 'Use for page titles, navigation bars, or section headers',
        tags: ['layout', 'navigation', 'title'],
        complexity: 'simple',
        preview: '═══'
      },
      {
        type: 'section',
        icon: <LayoutOutlined />,
        label: 'Section',
        description: 'Container for grouping related content',
        usage: 'Organize content into logical sections',
        tags: ['layout', 'container', 'organization'],
        complexity: 'simple',
        preview: '▢'
      },
      {
        type: 'card',
        icon: <CreditCardOutlined />,
        label: 'Card',
        description: 'Flexible content container with optional header and footer',
        usage: 'Display content in a clean, contained format',
        tags: ['layout', 'container', 'content'],
        complexity: 'medium',
        preview: '▢'
      }
    ]
  },
  {
    id: 'basic',
    title: 'Basic Components',
    description: 'Essential UI elements for content and interaction',
    color: theme.colors.primary.main,
    icon: <AppstoreOutlined />,
    components: [
      {
        type: 'text',
        icon: <FileTextOutlined />,
        label: 'Text',
        description: 'Formatted text content with typography options',
        usage: 'Display paragraphs, headings, and formatted text',
        tags: ['content', 'text', 'typography'],
        complexity: 'simple',
        preview: 'Aa'
      },
      {
        type: 'button',
        icon: <AppstoreOutlined />,
        label: 'Button',
        description: 'Interactive button for user actions',
        usage: 'Trigger actions, submit forms, or navigate',
        tags: ['interaction', 'action', 'click'],
        complexity: 'simple',
        preview: '▢'
      },
      {
        type: 'image',
        icon: <PictureOutlined />,
        label: 'Image',
        description: 'Display images with responsive sizing',
        usage: 'Show photos, illustrations, or graphics',
        tags: ['media', 'visual', 'content'],
        complexity: 'simple',
        preview: '🖼'
      }
    ]
  }
];

export default function EnhancedComponentPaletteFixed({
  onAddComponent,
  onDragStart,
  onDragEnd,
  components = [],
  selectedComponent = null,
  showAISuggestions = true,
  loading = false
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState(['layout', 'basic']);
  const [showDescriptions, setShowDescriptions] = useState(true);
  const [showTags, setShowTags] = useState(false);
  const [filterBy, setFilterBy] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [draggedComponent, setDraggedComponent] = useState(null);
  const dragPreviewRef = useRef(null);

  // AI suggestions hook
  const {
    suggestions,
    loading: aiLoading,
    applyComponentCombination,
    hasLayoutSuggestions,
    hasCombinationSuggestions
  } = useAIDesignSuggestions({
    autoRefresh: true,
    context: { selectedComponent }
  });

  // Filter and sort components
  const filteredGroups = useMemo(() => {
    let groups = [...COMPONENT_GROUPS];

    // Add AI suggestions group if enabled
    if (showAISuggestions && hasCombinationSuggestions) {
      groups.unshift({
        id: 'ai',
        title: 'AI Suggestions',
        description: 'Smart component recommendations based on your current app',
        color: theme.colors.accent.main,
        icon: <RobotOutlined />,
        isAI: true,
        components: [] // Will be populated with AI suggestions
      });
    }

    // Filter by search term
    if (searchTerm) {
      groups = groups.map(group => ({
        ...group,
        components: group.components.filter(component =>
          component.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
        )
      })).filter(group => group.components.length > 0 || group.isAI);
    }

    // Filter by complexity
    if (filterBy !== 'all') {
      groups = groups.map(group => ({
        ...group,
        components: group.components.filter(component => component.complexity === filterBy)
      })).filter(group => group.components.length > 0 || group.isAI);
    }

    // Sort components within each group
    groups = groups.map(group => ({
      ...group,
      components: [...group.components].sort((a, b) => {
        switch (sortBy) {
          case 'name':
            return a.label.localeCompare(b.label);
          case 'complexity':
            const complexityOrder = { simple: 0, medium: 1, complex: 2 };
            return complexityOrder[a.complexity] - complexityOrder[b.complexity];
          default:
            return 0;
        }
      })
    }));

    return groups;
  }, [searchTerm, filterBy, sortBy, showAISuggestions, hasCombinationSuggestions]);

  const handleDragStart = useCallback((e, component) => {
    setDraggedComponent(component);

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.type,
      label: component.label,
      source: 'palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';

    if (onDragStart) {
      onDragStart(component);
    }
  }, [onDragStart]);

  const handleDragEnd = useCallback((e) => {
    setDraggedComponent(null);
    if (onDragEnd) {
      onDragEnd();
    }
  }, [onDragEnd]);

  const handleCategoryToggle = useCallback((categoryId) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(cat => cat !== categoryId)
        : [...prev, categoryId]
    );
  }, []);

  const handleComponentClick = useCallback((component) => {
    if (onAddComponent) {
      onAddComponent(component.type);
    }
  }, [onAddComponent]);

  // Filter menu for dropdown
  const filterMenu = (
    <Menu
      selectedKeys={[filterBy]}
      onClick={({ key }) => setFilterBy(key)}
    >
      <Menu.Item key="all">All Components</Menu.Item>
      <Menu.Item key="simple">Simple</Menu.Item>
      <Menu.Item key="medium">Medium</Menu.Item>
      <Menu.Item key="complex">Complex</Menu.Item>
    </Menu>
  );

  // Sort menu for dropdown
  const sortMenu = (
    <Menu
      selectedKeys={[sortBy]}
      onClick={({ key }) => setSortBy(key)}
    >
      <Menu.Item key="name">Sort by Name</Menu.Item>
      <Menu.Item key="complexity">Sort by Complexity</Menu.Item>
    </Menu>
  );

  return (
    <PaletteContainer role="region" aria-label="Component Palette">
      <PaletteHeader>
        <Title level={5} style={{ margin: 0, color: 'white' }}>
          Component Palette
        </Title>
        <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
          Drag components to the canvas or click to add
        </Text>
      </PaletteHeader>

      <SearchContainer>
        <Search
          placeholder="Search components..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          prefix={<SearchOutlined />}
          allowClear
          style={{ marginBottom: theme.spacing[2] }}
          aria-label="Search components"
        />

        <FilterControls>
          <Space size="small">
            <Dropdown overlay={filterMenu} trigger={['click']}>
              <Button size="small" icon={<FilterOutlined />}>
                Filter <DownOutlined />
              </Button>
            </Dropdown>

            <Dropdown overlay={sortMenu} trigger={['click']}>
              <Button size="small">
                Sort <DownOutlined />
              </Button>
            </Dropdown>
          </Space>

          <Space size="small">
            <Text style={{ fontSize: theme.typography.fontSize.xs }}>Descriptions:</Text>
            <Switch
              size="small"
              checked={showDescriptions}
              onChange={setShowDescriptions}
            />
          </Space>
        </FilterControls>
      </SearchContainer>

      <ScrollableContent>
        {!loading && filteredGroups.map((group) => (
          <div key={group.id}>
            <CategoryHeader
              onClick={() => handleCategoryToggle(group.id)}
              tabIndex={0}
              role="button"
              aria-expanded={expandedCategories.includes(group.id)}
              aria-controls={`category-${group.id}`}
            >
              <Space>
                <div
                  style={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: group.color
                  }}
                />
                {group.icon}
                <span>{group.title}</span>
                <Badge count={group.components.length} size="small" />
                {group.isAI && aiLoading && (
                  <Badge status="processing" />
                )}
              </Space>
              <DownOutlined
                style={{
                  transform: expandedCategories.includes(group.id) ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: theme.transitions.default
                }}
              />
            </CategoryHeader>

            {expandedCategories.includes(group.id) && (
              <ComponentGrid id={`category-${group.id}`}>
                {group.components.map((component) => (
                  <ComponentCard
                    key={component.type}
                    size="small"
                    hoverable
                    complexity={component.complexity}
                    isDragging={draggedComponent?.type === component.type}
                    draggable
                    onDragStart={(e) => handleDragStart(e, component)}
                    onDragEnd={handleDragEnd}
                    onClick={() => handleComponentClick(component)}
                    tabIndex={0}
                    role="button"
                    aria-label={`Add ${component.label} component`}
                  >
                    <ComponentIcon
                      className="component-icon"
                      color={group.color}
                    >
                      {component.icon}
                    </ComponentIcon>

                    <ComponentLabel className="component-label">
                      {component.label}
                    </ComponentLabel>

                    {showDescriptions && (
                      <ComponentDescription>
                        {component.description}
                      </ComponentDescription>
                    )}

                    <ComponentPreview>
                      {component.preview}
                    </ComponentPreview>

                    <ComplexityBadge complexity={component.complexity} />

                    {showTags && (
                      <div style={{ marginTop: theme.spacing[1] }}>
                        {component.tags.slice(0, 2).map(tag => (
                          <Tag key={tag} size="small" style={{ fontSize: '10px' }}>
                            {tag}
                          </Tag>
                        ))}
                      </div>
                    )}
                  </ComponentCard>
                ))}
              </ComponentGrid>
            )}
          </div>
        ))}
      </ScrollableContent>
    </PaletteContainer>
  );
}
