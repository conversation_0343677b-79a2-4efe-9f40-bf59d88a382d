"""
Integration tests for database operations in the App Builder application.
Tests complex queries, transactions, and data integrity.
"""

import pytest
import json
from django.test import TransactionTestCase
from django.db import transaction, IntegrityError
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from model_bakery import baker
from datetime import datetime, timedelta

from my_app.models import (
    App, LayoutTemplate, AppTemplate, Comment, 
    Collaboration, ExportHistory, AIInteraction
)


@pytest.mark.django_db(transaction=True)
class TestDatabaseIntegration(TransactionTestCase):
    """Integration tests for database operations."""

    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='pass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='pass123'
        )

    def test_app_creation_with_related_data(self):
        """Test creating an app with all related data."""
        with transaction.atomic():
            # Create app
            app = App.objects.create(
                name='Complex App',
                description='An app with all features',
                user=self.user1,
                app_data=json.dumps({
                    'components': [
                        {'id': '1', 'type': 'button', 'props': {'text': 'Button'}},
                        {'id': '2', 'type': 'input', 'props': {'placeholder': 'Input'}}
                    ],
                    'layout': {'type': 'grid', 'columns': 2}
                }),
                is_public=True,
                allow_collaboration=True
            )

            # Add collaborator
            collaboration = Collaboration.objects.create(
                app=app,
                user=self.user2,
                role='editor',
                invited_by=self.user1
            )

            # Add comments
            comment1 = Comment.objects.create(
                app=app,
                user=self.user1,
                text='Initial comment',
                component_id='1',
                position_x=100,
                position_y=150
            )

            comment2 = Comment.objects.create(
                app=app,
                user=self.user2,
                text='Collaborator comment',
                component_id='2',
                position_x=200,
                position_y=250
            )

            # Add export history
            export = ExportHistory.objects.create(
                app=app,
                user=self.user1,
                export_format='react',
                file_size=2048,
                download_url='https://example.com/export.zip'
            )

            # Add AI interaction
            ai_interaction = AIInteraction.objects.create(
                app=app,
                user=self.user1,
                interaction_type='suggestion',
                prompt='Improve this layout',
                response={'suggestions': ['Add spacing', 'Use grid']},
                confidence_score=0.85
            )

        # Verify all data was created correctly
        self.assertEqual(App.objects.count(), 1)
        self.assertEqual(Collaboration.objects.count(), 1)
        self.assertEqual(Comment.objects.count(), 2)
        self.assertEqual(ExportHistory.objects.count(), 1)
        self.assertEqual(AIInteraction.objects.count(), 1)

        # Verify relationships
        self.assertEqual(app.collaboration_set.count(), 1)
        self.assertEqual(app.comment_set.count(), 2)
        self.assertEqual(app.exporthistory_set.count(), 1)
        self.assertEqual(app.aiinteraction_set.count(), 1)

    def test_complex_queries_and_filtering(self):
        """Test complex database queries and filtering."""
        # Create test data
        apps = []
        for i in range(10):
            app = App.objects.create(
                name=f'App {i}',
                user=self.user1 if i % 2 == 0 else self.user2,
                app_data=json.dumps({
                    'components': [{'id': str(j), 'type': 'button'} for j in range(i + 1)]
                }),
                is_public=i % 3 == 0,
                created_at=datetime.now() - timedelta(days=i)
            )
            apps.append(app)

            # Add comments to some apps
            if i % 2 == 0:
                Comment.objects.create(
                    app=app,
                    user=self.user1,
                    text=f'Comment for app {i}',
                    component_id='1'
                )

        # Test complex queries
        
        # 1. Apps with comments
        apps_with_comments = App.objects.filter(comment__isnull=False).distinct()
        self.assertEqual(apps_with_comments.count(), 5)

        # 2. Public apps by user1
        public_user1_apps = App.objects.filter(user=self.user1, is_public=True)
        expected_count = len([i for i in range(10) if i % 2 == 0 and i % 3 == 0])
        self.assertEqual(public_user1_apps.count(), expected_count)

        # 3. Apps created in last 5 days
        recent_apps = App.objects.filter(
            created_at__gte=datetime.now() - timedelta(days=5)
        )
        self.assertEqual(recent_apps.count(), 6)  # Apps 0-5

        # 4. Apps with more than 3 components
        apps_with_many_components = []
        for app in App.objects.all():
            app_data = json.loads(app.app_data)
            if len(app_data.get('components', [])) > 3:
                apps_with_many_components.append(app)
        
        self.assertEqual(len(apps_with_many_components), 6)  # Apps 4-9

        # 5. User statistics
        user1_stats = {
            'total_apps': App.objects.filter(user=self.user1).count(),
            'public_apps': App.objects.filter(user=self.user1, is_public=True).count(),
            'total_comments': Comment.objects.filter(user=self.user1).count()
        }
        
        self.assertEqual(user1_stats['total_apps'], 5)
        self.assertEqual(user1_stats['total_comments'], 5)

    def test_transaction_rollback_on_error(self):
        """Test that transactions are properly rolled back on errors."""
        initial_app_count = App.objects.count()
        initial_comment_count = Comment.objects.count()

        try:
            with transaction.atomic():
                # Create app
                app = App.objects.create(
                    name='Transaction Test App',
                    user=self.user1,
                    app_data='{}'
                )

                # Create comment
                Comment.objects.create(
                    app=app,
                    user=self.user1,
                    text='Test comment'
                )

                # Force an error
                raise IntegrityError("Simulated database error")

        except IntegrityError:
            pass  # Expected error

        # Verify rollback - no new records should exist
        self.assertEqual(App.objects.count(), initial_app_count)
        self.assertEqual(Comment.objects.count(), initial_comment_count)

    def test_cascade_deletion(self):
        """Test cascade deletion behavior."""
        # Create app with related data
        app = App.objects.create(
            name='Cascade Test App',
            user=self.user1,
            app_data='{}'
        )

        # Add related objects
        collaboration = Collaboration.objects.create(
            app=app,
            user=self.user2,
            role='viewer',
            invited_by=self.user1
        )

        comment = Comment.objects.create(
            app=app,
            user=self.user1,
            text='Test comment'
        )

        export = ExportHistory.objects.create(
            app=app,
            user=self.user1,
            export_format='vue',
            file_size=1024
        )

        ai_interaction = AIInteraction.objects.create(
            app=app,
            user=self.user1,
            interaction_type='generation',
            prompt='Generate components',
            response={}
        )

        # Verify related objects exist
        self.assertEqual(Collaboration.objects.filter(app=app).count(), 1)
        self.assertEqual(Comment.objects.filter(app=app).count(), 1)
        self.assertEqual(ExportHistory.objects.filter(app=app).count(), 1)
        self.assertEqual(AIInteraction.objects.filter(app=app).count(), 1)

        # Delete app
        app.delete()

        # Verify cascade deletion
        self.assertEqual(Collaboration.objects.filter(app_id=app.id).count(), 0)
        self.assertEqual(Comment.objects.filter(app_id=app.id).count(), 0)
        self.assertEqual(ExportHistory.objects.filter(app_id=app.id).count(), 0)
        self.assertEqual(AIInteraction.objects.filter(app_id=app.id).count(), 0)

    def test_data_integrity_constraints(self):
        """Test database integrity constraints."""
        app = App.objects.create(
            name='Integrity Test App',
            user=self.user1,
            app_data='{}'
        )

        # Test unique constraint for collaboration
        Collaboration.objects.create(
            app=app,
            user=self.user2,
            role='editor',
            invited_by=self.user1
        )

        # Attempting to create duplicate collaboration should fail
        with self.assertRaises(IntegrityError):
            Collaboration.objects.create(
                app=app,
                user=self.user2,  # Same user
                role='viewer',
                invited_by=self.user1
            )

        # Test foreign key constraints
        with self.assertRaises(IntegrityError):
            Comment.objects.create(
                app_id=99999,  # Non-existent app
                user=self.user1,
                text='Invalid comment'
            )

    def test_json_field_operations(self):
        """Test JSON field operations and queries."""
        # Create apps with different JSON structures
        app1 = App.objects.create(
            name='JSON Test App 1',
            user=self.user1,
            app_data=json.dumps({
                'components': [
                    {'id': '1', 'type': 'button', 'props': {'text': 'Button', 'color': 'blue'}},
                    {'id': '2', 'type': 'input', 'props': {'placeholder': 'Email'}}
                ],
                'theme': 'dark',
                'version': '1.0'
            })
        )

        app2 = App.objects.create(
            name='JSON Test App 2',
            user=self.user1,
            app_data=json.dumps({
                'components': [
                    {'id': '1', 'type': 'header', 'props': {'title': 'Header'}},
                    {'id': '2', 'type': 'button', 'props': {'text': 'Submit', 'color': 'red'}}
                ],
                'theme': 'light',
                'version': '2.0'
            })
        )

        # Test JSON field access and manipulation
        for app in [app1, app2]:
            app_data = json.loads(app.app_data)
            
            # Verify structure
            self.assertIn('components', app_data)
            self.assertIn('theme', app_data)
            self.assertIn('version', app_data)
            
            # Count components
            component_count = len(app_data['components'])
            self.assertEqual(component_count, 2)
            
            # Find buttons
            buttons = [c for c in app_data['components'] if c['type'] == 'button']
            self.assertGreaterEqual(len(buttons), 1)

        # Test updating JSON data
        app1_data = json.loads(app1.app_data)
        app1_data['components'].append({
            'id': '3',
            'type': 'footer',
            'props': {'text': 'Footer'}
        })
        app1_data['version'] = '1.1'
        
        app1.app_data = json.dumps(app1_data)
        app1.save()

        # Verify update
        app1.refresh_from_db()
        updated_data = json.loads(app1.app_data)
        self.assertEqual(len(updated_data['components']), 3)
        self.assertEqual(updated_data['version'], '1.1')

    def test_bulk_operations(self):
        """Test bulk database operations."""
        # Bulk create apps
        apps_data = []
        for i in range(100):
            apps_data.append(App(
                name=f'Bulk App {i}',
                user=self.user1 if i % 2 == 0 else self.user2,
                app_data=json.dumps({
                    'components': [{'id': str(j), 'type': 'component'} for j in range(i % 5 + 1)]
                }),
                is_public=i % 10 == 0
            ))

        created_apps = App.objects.bulk_create(apps_data)
        self.assertEqual(len(created_apps), 100)

        # Bulk update
        App.objects.filter(user=self.user1).update(description='Bulk updated')
        
        updated_count = App.objects.filter(
            user=self.user1,
            description='Bulk updated'
        ).count()
        self.assertEqual(updated_count, 50)

        # Bulk delete
        deleted_count, _ = App.objects.filter(is_public=True).delete()
        self.assertEqual(deleted_count, 10)

        # Verify remaining count
        remaining_count = App.objects.count()
        self.assertEqual(remaining_count, 90)

    def test_database_performance_with_large_dataset(self):
        """Test database performance with large datasets."""
        # Create large dataset
        apps = []
        comments = []
        
        for i in range(1000):
            app = App(
                name=f'Performance App {i}',
                user=self.user1 if i % 2 == 0 else self.user2,
                app_data=json.dumps({
                    'components': [{'id': str(j)} for j in range(10)]
                })
            )
            apps.append(app)

        # Bulk create apps
        created_apps = App.objects.bulk_create(apps)

        # Create comments for some apps
        for i, app in enumerate(created_apps[:500]):
            comment = Comment(
                app=app,
                user=self.user1,
                text=f'Comment {i}',
                component_id='1'
            )
            comments.append(comment)

        Comment.objects.bulk_create(comments)

        # Test query performance
        import time

        # Test 1: Simple filter
        start_time = time.time()
        user1_apps = list(App.objects.filter(user=self.user1))
        filter_time = time.time() - start_time
        
        self.assertLess(filter_time, 1.0)  # Should complete in less than 1 second
        self.assertEqual(len(user1_apps), 500)

        # Test 2: Join query
        start_time = time.time()
        apps_with_comments = list(App.objects.filter(comment__isnull=False).distinct())
        join_time = time.time() - start_time
        
        self.assertLess(join_time, 1.0)  # Should complete in less than 1 second
        self.assertEqual(len(apps_with_comments), 500)

        # Test 3: Aggregation
        start_time = time.time()
        from django.db.models import Count
        user_stats = User.objects.annotate(
            app_count=Count('app'),
            comment_count=Count('comment')
        ).values('username', 'app_count', 'comment_count')
        list(user_stats)  # Force evaluation
        aggregation_time = time.time() - start_time
        
        self.assertLess(aggregation_time, 1.0)  # Should complete in less than 1 second

    def test_concurrent_database_access(self):
        """Test concurrent database access and locking."""
        app = App.objects.create(
            name='Concurrent Test App',
            user=self.user1,
            app_data=json.dumps({'version': 1})
        )

        # Simulate concurrent updates
        def update_app_version(app_id, new_version):
            try:
                with transaction.atomic():
                    app = App.objects.select_for_update().get(id=app_id)
                    app_data = json.loads(app.app_data)
                    app_data['version'] = new_version
                    app.app_data = json.dumps(app_data)
                    app.save()
                    return True
            except Exception:
                return False

        # Test sequential updates (should all succeed)
        for version in range(2, 6):
            result = update_app_version(app.id, version)
            self.assertTrue(result)

        # Verify final version
        app.refresh_from_db()
        final_data = json.loads(app.app_data)
        self.assertEqual(final_data['version'], 5)

    def test_database_migrations_compatibility(self):
        """Test that current data structure is compatible with expected schema."""
        # Test that all expected fields exist and have correct types
        
        # App model
        app = App.objects.create(
            name='Schema Test App',
            user=self.user1,
            app_data='{}'
        )
        
        # Verify field types and constraints
        self.assertIsInstance(app.name, str)
        self.assertIsInstance(app.created_at, datetime)
        self.assertIsInstance(app.updated_at, datetime)
        self.assertIsInstance(app.is_public, bool)
        
        # Template models
        layout_template = LayoutTemplate.objects.create(
            name='Schema Test Layout',
            user=self.user1,
            components={}
        )
        
        self.assertIsInstance(layout_template.components, dict)
        
        # Comment model
        comment = Comment.objects.create(
            app=app,
            user=self.user1,
            text='Schema test comment'
        )
        
        self.assertIsInstance(comment.position_x, (int, type(None)))
        self.assertIsInstance(comment.position_y, (int, type(None)))
        
        # Verify all models can be saved and retrieved
        self.assertEqual(App.objects.count(), 1)
        self.assertEqual(LayoutTemplate.objects.count(), 1)
        self.assertEqual(Comment.objects.count(), 1)
