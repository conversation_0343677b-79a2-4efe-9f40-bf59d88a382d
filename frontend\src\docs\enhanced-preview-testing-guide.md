# Enhanced Preview Functionality Testing Guide

This guide provides comprehensive testing scenarios for the enhanced real-time preview functionality in the App Builder application.

## Overview

The enhanced preview system includes:
- **Real-time preview updates**: Instant visual feedback when modifying components
- **Multi-device preview modes**: Mobile, tablet, and desktop views with device frames
- **WebSocket synchronization**: Real-time collaboration and state synchronization
- **Performance optimizations**: Debouncing, virtual rendering, and caching

## Prerequisites

1. Ensure the backend server is running at `http://localhost:8000`
2. WebSocket server should be available at `ws://localhost:8000/ws/`
3. Frontend development server running at `http://localhost:3000`

## Test Scenarios

### 1. Real-Time Preview Updates

#### Test 1.1: Instant Component Updates
**Objective**: Verify that component changes are reflected immediately in the preview

**Steps**:
1. Navigate to the Component Builder tab
2. Add a button component to the canvas
3. Select the button component
4. In the property editor, change the button text
5. Observe the preview area

**Expected Result**: 
- Button text should update instantly in the preview without manual refresh
- Real-time indicator should show "Live" status
- No noticeable delay between property change and preview update

#### Test 1.2: Property Editor Integration
**Objective**: Test seamless integration between property editor and preview

**Steps**:
1. Add multiple components (button, text, card) to the canvas
2. Select each component and modify various properties:
   - Button: text, type, size
   - Text: content, font size, color
   - Card: title, content, background
3. Toggle the real-time preview switch in the header

**Expected Result**:
- All property changes should reflect immediately when real-time is enabled
- When real-time is disabled, changes should only apply when "Apply Changes" is clicked
- Performance metrics should show reasonable render times (<16ms)

### 2. Multi-Device Preview Modes

#### Test 2.1: Device Switching
**Objective**: Verify device preview modes work correctly

**Steps**:
1. Add several components to the canvas
2. Click the "Mobile" device button in the preview toolbar
3. Observe the preview changes
4. Switch to "Tablet" mode
5. Switch to "Desktop" mode
6. Test orientation toggle for mobile/tablet

**Expected Result**:
- Preview should show device frame for mobile/tablet
- Components should scale appropriately for each device
- Orientation toggle should work for mobile/tablet (not available for desktop)
- Device dimensions should be displayed correctly

#### Test 2.2: Responsive Component Behavior
**Objective**: Test that components adapt to different device sizes

**Steps**:
1. Add a table component to the canvas
2. Switch between device modes
3. Add a form component
4. Test button sizes across devices
5. Verify text scaling

**Expected Result**:
- Table should show horizontal scroll on mobile
- Form layout should adapt (vertical on mobile)
- Button sizes should be smaller on mobile
- Text should scale appropriately for each device

### 3. WebSocket Synchronization

#### Test 3.1: Connection Status
**Objective**: Verify WebSocket connection indicators work correctly

**Steps**:
1. Check the connection status indicators in the header
2. Toggle collaboration switch in the preview controls
3. Observe real-time status badge
4. Simulate connection loss (stop backend server)
5. Restart backend server

**Expected Result**:
- Connection indicators should show correct status (green=connected, red=disconnected)
- Collaboration toggle should be disabled when WebSocket is disconnected
- Status should update automatically when connection is restored

#### Test 3.2: Collaborative Editing (Simulated)
**Objective**: Test collaborative features with multiple browser tabs

**Steps**:
1. Open the App Builder in two browser tabs
2. Enable collaboration in both tabs
3. Make changes in one tab
4. Observe the other tab
5. Test cursor tracking (if implemented)

**Expected Result**:
- Changes in one tab should appear in the other tab
- Collaborator count should be accurate
- No conflicts should occur with simultaneous edits

### 4. Performance Optimization

#### Test 4.1: Large Component Lists
**Objective**: Test performance with many components

**Steps**:
1. Add 50+ components to the canvas
2. Enable performance monitoring
3. Observe render times and frame rates
4. Test scrolling performance
5. Make rapid property changes

**Expected Result**:
- Render times should remain under 16ms for 60fps
- Virtual scrolling should activate for large lists
- Frame rate should stay above 30fps
- Memory usage should remain reasonable

#### Test 4.2: Rapid Updates
**Objective**: Test debouncing and performance under rapid changes

**Steps**:
1. Select a component
2. Rapidly change properties (e.g., drag a color slider quickly)
3. Observe performance metrics
4. Test with multiple components selected

**Expected Result**:
- Updates should be debounced (not every keystroke)
- Performance should remain stable
- No visual lag or stuttering
- Memory leaks should not occur

### 5. Error Handling

#### Test 5.1: Network Disconnection
**Objective**: Test graceful handling of network issues

**Steps**:
1. Start with working WebSocket connection
2. Disconnect network or stop backend
3. Continue using the application
4. Reconnect network/restart backend

**Expected Result**:
- Application should continue working in offline mode
- Clear indicators of connection loss
- Automatic reconnection when network is restored
- No data loss during disconnection

#### Test 5.2: Invalid Component Data
**Objective**: Test handling of corrupted or invalid data

**Steps**:
1. Manually corrupt component data in browser dev tools
2. Try to render invalid components
3. Test with missing required properties
4. Test with null/undefined values

**Expected Result**:
- Application should not crash
- Error boundaries should catch rendering errors
- Graceful fallbacks for missing data
- Clear error messages for debugging

### 6. User Experience

#### Test 6.1: Visual Feedback
**Objective**: Ensure users receive appropriate visual feedback

**Steps**:
1. Observe loading states during operations
2. Check for update indicators
3. Test hover states and animations
4. Verify accessibility features

**Expected Result**:
- Clear loading indicators during operations
- Smooth animations and transitions
- Appropriate hover and focus states
- Screen reader compatibility

#### Test 6.2: Performance Monitoring UI
**Objective**: Test the floating performance monitor

**Steps**:
1. Enable performance monitoring in preview controls
2. Observe the floating performance panel
3. Test expand/collapse functionality
4. Check performance recommendations

**Expected Result**:
- Performance metrics should be accurate
- Panel should not interfere with normal usage
- Recommendations should be helpful and actionable
- Toggle functionality should work smoothly

## Performance Benchmarks

### Target Metrics
- **Render Time**: < 16ms (60fps)
- **Frame Rate**: > 30fps consistently
- **Memory Usage**: < 100MB for typical usage
- **Update Latency**: < 300ms for real-time updates
- **WebSocket Reconnection**: < 5 seconds

### Monitoring Tools
- Built-in performance monitor
- Browser DevTools Performance tab
- React DevTools Profiler
- Network tab for WebSocket monitoring

## Troubleshooting

### Common Issues
1. **Slow Performance**: Check component count, enable virtualization
2. **WebSocket Connection Failed**: Verify backend server is running
3. **Preview Not Updating**: Check real-time toggle, WebSocket status
4. **Device Frames Not Showing**: Verify device type selection
5. **Memory Leaks**: Check for proper cleanup in useEffect hooks

### Debug Mode
Enable debug mode by setting `localStorage.setItem('debug', 'true')` in browser console for additional logging.

## Automated Testing

Run the test suite with:
```bash
npm test -- --testPathPattern=enhancedPreview
```

For continuous testing during development:
```bash
npm test -- --watch --testPathPattern=enhancedPreview
```

## Reporting Issues

When reporting issues, please include:
1. Browser and version
2. Steps to reproduce
3. Expected vs actual behavior
4. Console errors (if any)
5. Performance metrics (if relevant)
6. Screenshots or screen recordings

## Success Criteria

The enhanced preview functionality is considered successful when:
- All test scenarios pass without errors
- Performance metrics meet target benchmarks
- User experience is smooth and responsive
- WebSocket synchronization works reliably
- Multi-device preview is accurate and useful
- Error handling is graceful and informative
