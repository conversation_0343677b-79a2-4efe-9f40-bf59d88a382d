<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Status Test - App Builder 201</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .status-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #10b981;
            background: #f0fdf4;
        }
        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
        .warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }
        .info {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }
        .code {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .test-results {
            margin-top: 20px;
        }
        .cache-info {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Service Worker Status Test - App Builder 201</h1>
        
        <div id="browser-support" class="status-section">
            <h3>Browser Support</h3>
            <div id="support-status"></div>
        </div>

        <div id="registration-status" class="status-section">
            <h3>Registration Status</h3>
            <div id="registration-info"></div>
        </div>

        <div id="worker-state" class="status-section">
            <h3>Service Worker State</h3>
            <div id="state-info"></div>
        </div>

        <div id="cache-status" class="status-section">
            <h3>Cache Status</h3>
            <div id="cache-info"></div>
        </div>

        <div id="network-test" class="status-section">
            <h3>Network Interception Test</h3>
            <div id="network-info"></div>
        </div>

        <div class="test-results">
            <h3>Actions</h3>
            <button onclick="registerServiceWorker()">Register Service Worker</button>
            <button onclick="unregisterServiceWorker()">Unregister Service Worker</button>
            <button onclick="updateServiceWorker()">Update Service Worker</button>
            <button onclick="testCaching()">Test Caching</button>
            <button onclick="testOffline()">Test Offline Mode</button>
            <button onclick="refreshStatus()">Refresh Status</button>
        </div>

        <div id="console-output" class="status-section">
            <h3>Console Output</h3>
            <div class="code" id="console-log"></div>
        </div>
    </div>

    <script>
        let consoleOutput = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            consoleOutput.push(logEntry);
            
            // Update console output display
            const consoleDiv = document.getElementById('console-log');
            consoleDiv.innerHTML = consoleOutput.slice(-20).join('<br>');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            
            // Also log to browser console
            console.log(logEntry);
        }

        async function checkBrowserSupport() {
            const supportDiv = document.getElementById('support-status');
            
            if ('serviceWorker' in navigator) {
                supportDiv.innerHTML = `
                    <div class="status-section">
                        ✅ Service Worker API is supported
                    </div>
                `;
                log('Service Worker API is supported');
                return true;
            } else {
                supportDiv.innerHTML = `
                    <div class="status-section error">
                        ❌ Service Worker API is not supported in this browser
                    </div>
                `;
                log('Service Worker API is not supported', 'error');
                return false;
            }
        }

        async function checkRegistrationStatus() {
            const regDiv = document.getElementById('registration-info');
            
            if (!('serviceWorker' in navigator)) {
                regDiv.innerHTML = '<div class="error">Service Worker not supported</div>';
                return;
            }

            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                log(`Found ${registrations.length} service worker registrations`);
                
                if (registrations.length === 0) {
                    regDiv.innerHTML = `
                        <div class="warning">
                            ⚠️ No service workers are currently registered
                        </div>
                    `;
                } else {
                    let html = '<div class="info">📋 Registered Service Workers:</div>';
                    registrations.forEach((reg, index) => {
                        html += `
                            <div class="cache-info">
                                <strong>Registration ${index + 1}:</strong><br>
                                Scope: ${reg.scope}<br>
                                State: ${reg.active ? reg.active.state : 'No active worker'}<br>
                                Script URL: ${reg.active ? reg.active.scriptURL : 'N/A'}
                            </div>
                        `;
                    });
                    regDiv.innerHTML = html;
                }
            } catch (error) {
                regDiv.innerHTML = `<div class="error">❌ Error checking registrations: ${error.message}</div>`;
                log(`Error checking registrations: ${error.message}`, 'error');
            }
        }

        async function checkWorkerState() {
            const stateDiv = document.getElementById('state-info');
            
            if (!('serviceWorker' in navigator)) {
                stateDiv.innerHTML = '<div class="error">Service Worker not supported</div>';
                return;
            }

            try {
                const registration = await navigator.serviceWorker.getRegistration();
                
                if (!registration) {
                    stateDiv.innerHTML = '<div class="warning">⚠️ No service worker registration found</div>';
                    return;
                }

                let html = '<div class="info">🔄 Service Worker States:</div>';
                
                if (registration.installing) {
                    html += `<div class="cache-info">Installing: ${registration.installing.state}</div>`;
                }
                if (registration.waiting) {
                    html += `<div class="cache-info">Waiting: ${registration.waiting.state}</div>`;
                }
                if (registration.active) {
                    html += `<div class="cache-info">Active: ${registration.active.state}</div>`;
                }
                
                stateDiv.innerHTML = html;
                log(`Service worker states checked`);
            } catch (error) {
                stateDiv.innerHTML = `<div class="error">❌ Error checking worker state: ${error.message}</div>`;
                log(`Error checking worker state: ${error.message}`, 'error');
            }
        }

        async function checkCacheStatus() {
            const cacheDiv = document.getElementById('cache-info');
            
            if (!('caches' in window)) {
                cacheDiv.innerHTML = '<div class="error">❌ Cache API not supported</div>';
                return;
            }

            try {
                const cacheNames = await caches.keys();
                log(`Found ${cacheNames.length} cache stores`);
                
                if (cacheNames.length === 0) {
                    cacheDiv.innerHTML = '<div class="warning">⚠️ No caches found</div>';
                } else {
                    let html = '<div class="info">💾 Cache Stores:</div>';
                    
                    for (const cacheName of cacheNames) {
                        const cache = await caches.open(cacheName);
                        const keys = await cache.keys();
                        html += `
                            <div class="cache-info">
                                <strong>${cacheName}</strong>: ${keys.length} cached items
                            </div>
                        `;
                    }
                    cacheDiv.innerHTML = html;
                }
            } catch (error) {
                cacheDiv.innerHTML = `<div class="error">❌ Error checking caches: ${error.message}</div>`;
                log(`Error checking caches: ${error.message}`, 'error');
            }
        }

        async function testNetworkInterception() {
            const networkDiv = document.getElementById('network-info');
            
            try {
                // Test if service worker is intercepting requests
                const testUrl = '/service-worker-test-endpoint';
                const response = await fetch(testUrl);
                
                const isIntercepted = response.headers.get('X-Service-Worker') === 'true';
                
                if (isIntercepted) {
                    networkDiv.innerHTML = '<div class="status-section">✅ Service Worker is intercepting network requests</div>';
                    log('Service Worker is intercepting network requests');
                } else {
                    networkDiv.innerHTML = '<div class="warning">⚠️ Service Worker may not be intercepting requests</div>';
                    log('Service Worker may not be intercepting requests');
                }
            } catch (error) {
                networkDiv.innerHTML = `<div class="info">ℹ️ Network test: ${error.message}</div>`;
                log(`Network test: ${error.message}`);
            }
        }

        async function registerServiceWorker() {
            if (!('serviceWorker' in navigator)) {
                log('Service Worker not supported', 'error');
                return;
            }

            try {
                log('Attempting to register service worker...');
                const registration = await navigator.serviceWorker.register('/service-worker.js');
                log(`Service Worker registered successfully: ${registration.scope}`);
                await refreshStatus();
            } catch (error) {
                log(`Service Worker registration failed: ${error.message}`, 'error');
            }
        }

        async function unregisterServiceWorker() {
            if (!('serviceWorker' in navigator)) {
                log('Service Worker not supported', 'error');
                return;
            }

            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                for (const registration of registrations) {
                    await registration.unregister();
                    log(`Unregistered service worker: ${registration.scope}`);
                }
                await refreshStatus();
            } catch (error) {
                log(`Error unregistering service workers: ${error.message}`, 'error');
            }
        }

        async function updateServiceWorker() {
            if (!('serviceWorker' in navigator)) {
                log('Service Worker not supported', 'error');
                return;
            }

            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    await registration.update();
                    log('Service Worker update triggered');
                } else {
                    log('No service worker registration found to update', 'warning');
                }
            } catch (error) {
                log(`Error updating service worker: ${error.message}`, 'error');
            }
        }

        async function testCaching() {
            if (!('caches' in window)) {
                log('Cache API not supported', 'error');
                return;
            }

            try {
                const cache = await caches.open('test-cache');
                const testUrl = '/test-cache-item';
                const testResponse = new Response('Test cache content');
                
                await cache.put(testUrl, testResponse);
                const cachedResponse = await cache.match(testUrl);
                
                if (cachedResponse) {
                    log('Cache test successful - item stored and retrieved');
                } else {
                    log('Cache test failed - item not found', 'error');
                }
            } catch (error) {
                log(`Cache test error: ${error.message}`, 'error');
            }
        }

        async function testOffline() {
            log('Testing offline functionality...');
            
            // Simulate offline by trying to fetch a non-existent resource
            try {
                const response = await fetch('/offline-test-' + Date.now());
                log(`Offline test response: ${response.status}`);
            } catch (error) {
                log(`Offline test (expected failure): ${error.message}`);
            }
        }

        async function refreshStatus() {
            log('Refreshing service worker status...');
            await checkBrowserSupport();
            await checkRegistrationStatus();
            await checkWorkerState();
            await checkCacheStatus();
            await testNetworkInterception();
        }

        // Initialize on page load
        window.addEventListener('load', async () => {
            log('Service Worker Test Page loaded');
            await refreshStatus();
        });
    </script>
</body>
</html>
