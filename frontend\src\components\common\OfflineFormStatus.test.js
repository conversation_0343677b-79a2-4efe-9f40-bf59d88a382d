import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import OfflineFormStatus from './OfflineFormStatus';
import { getPendingFormSubmissions, syncOfflineForms } from '../../utils/offlineFormHandler';

// Mock the offlineFormHandler module
jest.mock('../../utils/offlineFormHandler', () => ({
  getPendingFormSubmissions: jest.fn(),
  syncOfflineForms: jest.fn()
}));

describe('OfflineFormStatus', () => {
  // Mock data for pending forms
  const mockPendingForms = [
    {
      id: 'form_1',
      url: '/api/submit',
      method: 'POST',
      data: { name: '<PERSON>', email: '<EMAIL>' },
      timestamp: '2023-01-01T12:00:00.000Z'
    },
    {
      id: 'form_2',
      url: '/api/contact',
      method: 'POST',
      data: { subject: 'Question', message: 'Hello there' },
      timestamp: '2023-01-02T14:30:00.000Z'
    }
  ];

  // Setup and teardown
  beforeEach(() => {
    // Reset mocks
    getPendingFormSubmissions.mockReset();
    syncOfflineForms.mockReset();
    
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      configurable: true,
      value: true
    });
    
    // Mock getPendingFormSubmissions to return empty array by default
    getPendingFormSubmissions.mockResolvedValue([]);
  });

  test('renders with no pending forms', async () => {
    render(<OfflineFormStatus />);
    
    // Wait for component to load data
    await waitFor(() => {
      expect(getPendingFormSubmissions).toHaveBeenCalled();
    });
    
    // Should show "No Pending Forms" button
    expect(screen.getByText('No Pending Forms')).toBeInTheDocument();
    
    // Badge should show 0
    expect(screen.getByText('0')).toBeInTheDocument();
  });

  test('renders with pending forms', async () => {
    // Mock pending forms
    getPendingFormSubmissions.mockResolvedValue(mockPendingForms);
    
    render(<OfflineFormStatus />);
    
    // Wait for component to load data
    await waitFor(() => {
      expect(getPendingFormSubmissions).toHaveBeenCalled();
    });
    
    // Should show "Pending Forms" button
    expect(screen.getByText('Pending Forms')).toBeInTheDocument();
    
    // Badge should show 2
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  test('opens drawer when button is clicked', async () => {
    // Mock pending forms
    getPendingFormSubmissions.mockResolvedValue(mockPendingForms);
    
    render(<OfflineFormStatus />);
    
    // Wait for component to load data
    await waitFor(() => {
      expect(getPendingFormSubmissions).toHaveBeenCalled();
    });
    
    // Click the button to open drawer
    fireEvent.click(screen.getByText('Pending Forms'));
    
    // Drawer should be visible
    expect(screen.getByText('Offline Form Submissions')).toBeInTheDocument();
    
    // Should show form details
    expect(screen.getByText('Form Submission (1/1/2023, 12:00:00 PM)')).toBeInTheDocument();
    expect(screen.getByText('Form Submission (1/2/2023, 2:30:00 PM)')).toBeInTheDocument();
  });

  test('triggers sync when sync button is clicked', async () => {
    // Mock pending forms
    getPendingFormSubmissions.mockResolvedValue(mockPendingForms);
    syncOfflineForms.mockResolvedValue(true);
    
    render(<OfflineFormStatus />);
    
    // Wait for component to load data
    await waitFor(() => {
      expect(getPendingFormSubmissions).toHaveBeenCalled();
    });
    
    // Click the button to open drawer
    fireEvent.click(screen.getByText('Pending Forms'));
    
    // Click the sync button
    fireEvent.click(screen.getByText('Sync Now'));
    
    // Should call syncOfflineForms
    expect(syncOfflineForms).toHaveBeenCalled();
    
    // Should show sync status
    await waitFor(() => {
      expect(screen.getByText('Syncing...')).toBeInTheDocument();
    });
  });

  test('disables sync button when offline', async () => {
    // Mock offline status
    Object.defineProperty(navigator, 'onLine', {
      configurable: true,
      value: false
    });
    
    // Mock pending forms
    getPendingFormSubmissions.mockResolvedValue(mockPendingForms);
    
    render(<OfflineFormStatus />);
    
    // Wait for component to load data
    await waitFor(() => {
      expect(getPendingFormSubmissions).toHaveBeenCalled();
    });
    
    // Click the button to open drawer
    fireEvent.click(screen.getByText('Pending Forms'));
    
    // Should show offline alert
    expect(screen.getByText('Offline')).toBeInTheDocument();
    
    // Sync button should be disabled
    const syncButton = screen.getByText('Sync Now');
    expect(syncButton).toBeDisabled();
  });

  test('handles sync completion event', async () => {
    // Mock pending forms
    getPendingFormSubmissions.mockResolvedValue(mockPendingForms);
    
    render(<OfflineFormStatus />);
    
    // Wait for component to load data
    await waitFor(() => {
      expect(getPendingFormSubmissions).toHaveBeenCalled();
    });
    
    // Click the button to open drawer
    fireEvent.click(screen.getByText('Pending Forms'));
    
    // Simulate sync completion event
    act(() => {
      window.dispatchEvent(new CustomEvent('offlineSync', {
        detail: {
          results: {
            total: 2,
            successful: 1,
            failed: 1,
            remaining: 1
          }
        }
      }));
    });
    
    // Should show sync status
    await waitFor(() => {
      expect(screen.getByText('Sync Successful')).toBeInTheDocument();
      expect(screen.getByText('Sync completed: 1 submitted, 1 failed, 1 remaining.')).toBeInTheDocument();
    });
  });
});
