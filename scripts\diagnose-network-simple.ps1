# PowerShell script to diagnose network issues

Write-Host "Running network diagnostics..." -ForegroundColor Cyan

# Check if localhost resolves
Write-Host "`nChecking localhost DNS resolution:" -ForegroundColor Yellow
try {
    $ipAddress = [System.Net.Dns]::GetHostAddresses("localhost")
    Write-Host "[OK] localhost resolves to $($ipAddress -join ', ')" -ForegroundColor Green
} catch {
    $errorMsg = $_.Exception.Message
    Write-Host "[ERROR] Error resolving localhost: $errorMsg" -ForegroundColor Red
}

# Check if ports are in use
function Test-Port {
    param(
        [int]$Port
    )
    
    Write-Host "`nChecking if port $Port is in use:" -ForegroundColor Yellow
    
    try {
        $listener = New-Object System.Net.Sockets.TcpListener([System.Net.IPAddress]::Loopback, $Port)
        $listener.Start()
        Write-Host "[OK] Port $Port is available" -ForegroundColor Green
        $listener.Stop()
    } catch {
        Write-Host "[ERROR] Port $Port is already in use" -ForegroundColor Red
        
        # Try to identify what's using the port
        try {
            $process = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | 
                       Select-Object -First 1 OwningProcess
            
            if ($process) {
                $processInfo = Get-Process -Id $process.OwningProcess -ErrorAction SilentlyContinue
                if ($processInfo) {
                    Write-Host "Process using the port: $($processInfo.Name) (PID: $($process.OwningProcess))" -ForegroundColor Yellow
                }
            }
        } catch {
            $errorMsg = $_.Exception.Message
            Write-Host "Could not identify process: $errorMsg" -ForegroundColor Red
        }
    }
}

Test-Port -Port 3000
Test-Port -Port 8000

# Test HTTP connections
Write-Host "`nTesting HTTP connections:" -ForegroundColor Yellow

function Test-HttpConnection {
    param(
        [string]$Url,
        [string]$Name
    )
    
    Write-Host "Testing connection to $Name ($Url)..." -ForegroundColor Yellow
    
    try {
        $request = [System.Net.WebRequest]::Create($Url)
        $request.Timeout = 5000
        $response = $request.GetResponse()
        
        Write-Host "[OK] Connected to $Name - Status: $([int]$response.StatusCode)" -ForegroundColor Green
        
        $stream = $response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $data = $reader.ReadToEnd()
        $reader.Close()
        $response.Close()
        
        if ($data.Length -gt 0) {
            $preview = if ($data.Length -gt 100) { $data.Substring(0, 100) + "..." } else { $data }
            Write-Host "Response data (first 100 chars): $preview" -ForegroundColor Gray
        } else {
            Write-Host "No data received in response" -ForegroundColor Yellow
        }
    } catch [System.Net.WebException] {
        if ($_.Exception.Status -eq [System.Net.WebExceptionStatus]::Timeout) {
            Write-Host "[ERROR] Connection to $Name timed out" -ForegroundColor Red
        } else {
            $errorMsg = $_.Exception.Message
            Write-Host "[ERROR] Failed to connect to $Name: $errorMsg" -ForegroundColor Red
        }
    } catch {
        $errorMsg = $_.Exception.Message
        Write-Host "[ERROR] Error testing connection to $Name: $errorMsg" -ForegroundColor Red
    }
}

# Test Docker container status
Write-Host "`nChecking Docker container status:" -ForegroundColor Yellow
try {
    $containers = docker ps --format "{{.Names}}: {{.Status}}"
    if ($containers) {
        Write-Host "Running containers:" -ForegroundColor Green
        $containers | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
    } else {
        Write-Host "No running containers found" -ForegroundColor Yellow
    }
} catch {
    $errorMsg = $_.Exception.Message
    Write-Host "[ERROR] Error checking Docker containers: $errorMsg" -ForegroundColor Red
}

# Test standard connections
Test-HttpConnection -Url "http://localhost:3000" -Name "Frontend"
Test-HttpConnection -Url "http://localhost:8000" -Name "Backend"
Test-HttpConnection -Url "http://localhost:3000/fallback.html" -Name "Fallback page"
Test-HttpConnection -Url "http://localhost:8000/api/health-check" -Name "Backend health check"

# Test WebSocket connection
Write-Host "`nTesting WebSocket connection:" -ForegroundColor Yellow
Write-Host "Note: PowerShell doesn't have built-in WebSocket client capabilities." -ForegroundColor Yellow
Write-Host "To test WebSockets, use a browser or a dedicated WebSocket testing tool." -ForegroundColor Yellow
Write-Host "WebSocket URL: ws://localhost:8000/ws/" -ForegroundColor Cyan

Write-Host "`nDiagnostics complete!" -ForegroundColor Cyan
