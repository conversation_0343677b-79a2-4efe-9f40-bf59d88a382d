/**
 * Export Service for handling code generation and export operations
 * Provides methods for exporting applications in various formats with advanced options
 */

import { message } from 'antd';

class ExportService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || '/api';
    this.exportHistory = this.loadExportHistory();
  }

  /**
   * Load export history from localStorage
   */
  loadExportHistory() {
    try {
      const history = localStorage.getItem('exportHistory');
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Failed to load export history:', error);
      return [];
    }
  }

  /**
   * Save export history to localStorage
   */
  saveExportHistory(history) {
    try {
      localStorage.setItem('exportHistory', JSON.stringify(history));
      this.exportHistory = history;
    } catch (error) {
      console.error('Failed to save export history:', error);
    }
  }

  /**
   * Add export record to history
   */
  addToHistory(exportRecord) {
    const newHistory = [exportRecord, ...this.exportHistory.slice(0, 9)]; // Keep last 10
    this.saveExportHistory(newHistory);
    return newHistory;
  }

  /**
   * Clear export history
   */
  clearHistory() {
    this.saveExportHistory([]);
    return [];
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  /**
   * Enhanced export with backend processing
   */
  async enhancedExport(appData, format, options = {}) {
    try {
      const response = await fetch(`${this.baseURL}/enhanced-export/`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          app_id: appData.id || 1,
          format,
          options
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Export failed');
      }

      const result = await response.json();
      
      // Add to export history
      const exportRecord = {
        id: Date.now(),
        format,
        options,
        timestamp: new Date().toISOString(),
        status: 'success',
        type: result.type,
        size: this.calculateSize(result)
      };
      
      this.addToHistory(exportRecord);
      
      return result;
    } catch (error) {
      // Add failed export to history
      const exportRecord = {
        id: Date.now(),
        format,
        options,
        timestamp: new Date().toISOString(),
        status: 'failed',
        error: error.message
      };
      
      this.addToHistory(exportRecord);
      throw error;
    }
  }

  /**
   * Client-side export using local code generators
   */
  async clientSideExport(appData, format, options = {}) {
    try {
      // Import the code generator dynamically
      const { generateCode } = await import('../utils/code_generator');
      
      const code = generateCode(appData, format, options);
      
      const exportRecord = {
        id: Date.now(),
        format,
        options,
        timestamp: new Date().toISOString(),
        status: 'success',
        type: typeof code === 'string' ? 'single-file' : 'multi-file',
        size: this.calculateSize({ code })
      };
      
      this.addToHistory(exportRecord);
      
      return {
        code,
        type: typeof code === 'string' ? 'single-file' : 'multi-file',
        format,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      const exportRecord = {
        id: Date.now(),
        format,
        options,
        timestamp: new Date().toISOString(),
        status: 'failed',
        error: error.message
      };
      
      this.addToHistory(exportRecord);
      throw error;
    }
  }

  /**
   * Calculate size of export result
   */
  calculateSize(result) {
    if (result.code) {
      return result.code.length;
    } else if (result.files) {
      return Object.values(result.files).reduce((total, content) => total + content.length, 0);
    }
    return 0;
  }

  /**
   * Download single file
   */
  downloadFile(content, filename, mimeType = 'text/plain') {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Download multiple files as zip (requires JSZip)
   */
  async downloadMultipleFiles(files, zipName = 'app-export.zip') {
    try {
      // Try to use JSZip if available
      const JSZip = await import('jszip');
      const zip = new JSZip.default();
      
      Object.entries(files).forEach(([filename, content]) => {
        zip.file(filename, content);
      });
      
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      this.downloadFile(zipBlob, zipName, 'application/zip');
    } catch (error) {
      console.warn('JSZip not available, downloading main file only');
      // Fallback: download the main file
      const mainFile = files['App.jsx'] || files['App.tsx'] || files['index.html'] || Object.values(files)[0];
      const mainFilename = Object.keys(files)[0] || 'app.js';
      if (mainFile) {
        this.downloadFile(mainFile, mainFilename);
      }
    }
  }

  /**
   * Download zip file from base64 data
   */
  downloadZipFile(base64Data, filename = 'app-export.zip') {
    try {
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const blob = new Blob([bytes], { type: 'application/zip' });
      this.downloadFile(blob, filename, 'application/zip');
    } catch (error) {
      console.error('Failed to download zip file:', error);
      message.error('Failed to download zip file');
    }
  }

  /**
   * Handle export result and trigger download
   */
  async handleExportResult(result, format) {
    try {
      const filename = this.getFilename(format, result.type);
      
      if (result.type === 'single-file') {
        this.downloadFile(result.code, filename);
        message.success('File downloaded successfully!');
      } else if (result.type === 'multi-file') {
        await this.downloadMultipleFiles(result.files);
        message.success('Project files downloaded successfully!');
      } else if (result.type === 'zip') {
        this.downloadZipFile(result.zip_data, filename);
        message.success('Project zip downloaded successfully!');
      }
    } catch (error) {
      console.error('Failed to handle export result:', error);
      message.error('Failed to download export');
    }
  }

  /**
   * Get appropriate filename for export format
   */
  getFilename(format, type = 'single-file') {
    const extensions = {
      'react': 'jsx',
      'react-ts': 'tsx',
      'vue': 'vue',
      'vue-ts': 'vue',
      'angular': 'ts',
      'svelte': 'svelte',
      'next': 'jsx',
      'nuxt': 'vue',
      'html': 'html',
      'react-native': 'jsx',
      'flutter': 'dart',
      'ionic': 'ts'
    };

    if (type === 'zip' || type === 'multi-file') {
      return `app-export-${format}.zip`;
    }

    const extension = extensions[format] || 'js';
    return `App.${extension}`;
  }

  /**
   * Validate export options
   */
  validateExportOptions(format, options) {
    const errors = [];

    // Check if format is supported
    const supportedFormats = [
      'react', 'react-ts', 'vue', 'vue-ts', 'angular', 'svelte',
      'next', 'nuxt', 'html', 'react-native', 'flutter', 'ionic'
    ];

    if (!supportedFormats.includes(format)) {
      errors.push(`Unsupported export format: ${format}`);
    }

    // Validate TypeScript options
    if (options.typescript && !['react-ts', 'vue-ts', 'angular'].includes(format)) {
      if (!format.includes('ts')) {
        errors.push('TypeScript option is not compatible with the selected format');
      }
    }

    // Validate style framework compatibility
    const styleFrameworkCompatibility = {
      'material-ui': ['react', 'react-ts'],
      'chakra-ui': ['react', 'react-ts'],
      'styled-components': ['react', 'react-ts', 'react-native']
    };

    if (options.styleFramework && styleFrameworkCompatibility[options.styleFramework]) {
      if (!styleFrameworkCompatibility[options.styleFramework].includes(format)) {
        errors.push(`${options.styleFramework} is not compatible with ${format}`);
      }
    }

    return errors;
  }

  /**
   * Get export recommendations based on app data
   */
  getExportRecommendations(appData) {
    const recommendations = [];

    // Analyze component complexity
    const componentCount = appData.components?.length || 0;
    const layoutCount = appData.layouts?.length || 0;

    if (componentCount > 10) {
      recommendations.push({
        type: 'structure',
        message: 'Consider using multi-file project structure for better organization',
        suggestion: 'projectStructure: "multi-file"'
      });
    }

    if (layoutCount > 5) {
      recommendations.push({
        type: 'state',
        message: 'Consider using Redux or Zustand for complex state management',
        suggestion: 'stateManagement: "redux"'
      });
    }

    // Check for accessibility needs
    const hasInteractiveComponents = appData.components?.some(c => 
      ['button', 'input', 'form'].includes(c.type?.toLowerCase())
    );

    if (hasInteractiveComponents) {
      recommendations.push({
        type: 'accessibility',
        message: 'Enable accessibility features for better user experience',
        suggestion: 'includeAccessibility: true'
      });
    }

    return recommendations;
  }

  /**
   * Get export statistics
   */
  getExportStats() {
    const stats = {
      totalExports: this.exportHistory.length,
      successfulExports: this.exportHistory.filter(e => e.status === 'success').length,
      failedExports: this.exportHistory.filter(e => e.status === 'failed').length,
      formatUsage: {},
      recentActivity: this.exportHistory.slice(0, 5)
    };

    // Calculate format usage
    this.exportHistory.forEach(export_ => {
      stats.formatUsage[export_.format] = (stats.formatUsage[export_.format] || 0) + 1;
    });

    return stats;
  }
}

// Create singleton instance
const exportService = new ExportService();

export default exportService;
