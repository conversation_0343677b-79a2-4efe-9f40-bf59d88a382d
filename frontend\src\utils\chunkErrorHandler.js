/**
 * Chunk Error Handler Utility
 * Handles chunk loading errors and provides fallback mechanisms
 */

/**
 * Handle chunk loading errors by refreshing the page
 * This is useful when webpack chunks become stale
 */
export const handleChunkError = (error) => {
  console.error('Chunk loading error:', error);
  
  // Check if it's a chunk loading error
  if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) {
    console.warn('Chunk loading failed, attempting to reload...');
    
    // Show user-friendly message
    if (window.confirm('The application needs to reload to load the latest version. Click OK to continue.')) {
      window.location.reload();
    }
    
    return true; // Handled
  }
  
  return false; // Not handled
};

/**
 * Add global error handler for chunk loading errors
 */
export const setupChunkErrorHandler = () => {
  // Handle unhandled promise rejections (common for chunk loading errors)
  window.addEventListener('unhandledrejection', (event) => {
    if (handleChunkError(event.reason)) {
      event.preventDefault(); // Prevent default error handling
    }
  });
  
  // Handle regular errors
  window.addEventListener('error', (event) => {
    if (handleChunkError(event.error)) {
      event.preventDefault(); // Prevent default error handling
    }
  });
  
  console.log('Chunk error handler setup complete');
};

/**
 * Retry loading a chunk with exponential backoff
 */
export const retryChunkLoad = async (chunkLoader, maxRetries = 3) => {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      return await chunkLoader();
    } catch (error) {
      retries++;
      
      if (retries >= maxRetries) {
        throw error;
      }
      
      // Exponential backoff: 1s, 2s, 4s
      const delay = Math.pow(2, retries - 1) * 1000;
      console.warn(`Chunk load failed, retrying in ${delay}ms... (attempt ${retries}/${maxRetries})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

export default {
  handleChunkError,
  setupChunkErrorHandler,
  retryChunkLoad
};
