<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Test - App Builder 201</title>
    <link rel="manifest" href="/manifest.json">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .icon-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .icon-display {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .icon-display img {
            max-width: 100%;
            max-height: 100%;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .code {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Icon Test - App Builder 201</h1>
        
        <div class="icon-test">
            <div class="icon-card">
                <div class="icon-display">
                    <img src="/logo.svg" alt="SVG Logo" id="svg-logo">
                </div>
                <h3>SVG Logo</h3>
                <div class="status" id="svg-status">Testing...</div>
                <div class="code">/logo.svg</div>
            </div>

            <div class="icon-card">
                <div class="icon-display">
                    <img src="/favicon.ico" alt="Favicon" id="favicon">
                </div>
                <h3>Favicon</h3>
                <div class="status" id="favicon-status">Testing...</div>
                <div class="code">/favicon.ico</div>
            </div>

            <div class="icon-card">
                <div class="icon-display">
                    <img src="/logo192.png" alt="PNG Logo" id="png-logo">
                </div>
                <h3>PNG Logo (Old)</h3>
                <div class="status" id="png-status">Testing...</div>
                <div class="code">/logo192.png</div>
            </div>
        </div>

        <div class="icon-card">
            <h3>Manifest Icon Test</h3>
            <button onclick="testManifestIcons()">Test Manifest Icons</button>
            <div id="manifest-results" class="code">Click button to test manifest icons...</div>
        </div>

        <div class="icon-card">
            <h3>Service Worker Cache Test</h3>
            <button onclick="testCachedIcons()">Test Cached Icons</button>
            <div id="cache-results" class="code">Click button to test cached icons...</div>
        </div>

        <div class="icon-card">
            <h3>PWA Installation Test</h3>
            <button onclick="testPWAIcons()">Test PWA Icons</button>
            <div id="pwa-results" class="code">Click button to test PWA icon configuration...</div>
        </div>

        <div class="icon-card">
            <h3>Actions</h3>
            <button onclick="refreshServiceWorker()">Refresh Service Worker</button>
            <button onclick="clearCache()">Clear Cache</button>
            <button onclick="reloadPage()">Reload Page</button>
        </div>
    </div>

    <script>
        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${success ? 'success' : 'error'}`;
            element.textContent = success ? '✅ ' + message : '❌ ' + message;
        }

        function testImageLoad(imgId, statusId, description) {
            const img = document.getElementById(imgId);
            
            img.onload = () => {
                updateStatus(statusId, true, `${description} loaded successfully`);
            };
            
            img.onerror = () => {
                updateStatus(statusId, false, `${description} failed to load`);
            };
            
            // Force reload to test
            img.src = img.src + '?t=' + Date.now();
        }

        async function testManifestIcons() {
            const resultsDiv = document.getElementById('manifest-results');
            
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                
                let results = 'Manifest Icons:\n';
                
                for (const icon of manifest.icons) {
                    try {
                        const iconResponse = await fetch(icon.src);
                        const status = iconResponse.ok ? '✅' : '❌';
                        results += `${status} ${icon.src} (${icon.sizes}) - ${iconResponse.status}\n`;
                    } catch (error) {
                        results += `❌ ${icon.src} - Error: ${error.message}\n`;
                    }
                }
                
                resultsDiv.textContent = results;
            } catch (error) {
                resultsDiv.textContent = `Error testing manifest: ${error.message}`;
            }
        }

        async function testCachedIcons() {
            const resultsDiv = document.getElementById('cache-results');
            
            try {
                const cacheNames = await caches.keys();
                const appCache = cacheNames.find(name => name.includes('app-builder-cache'));
                
                if (!appCache) {
                    resultsDiv.textContent = 'No app cache found';
                    return;
                }
                
                const cache = await caches.open(appCache);
                const cachedRequests = await cache.keys();
                
                let results = 'Cached Icons:\n';
                
                const iconUrls = ['/logo.svg', '/favicon.ico', '/logo192.png'];
                
                for (const url of iconUrls) {
                    const isCached = cachedRequests.some(req => req.url.includes(url));
                    const status = isCached ? '✅' : '❌';
                    results += `${status} ${url} - ${isCached ? 'Cached' : 'Not cached'}\n`;
                }
                
                resultsDiv.textContent = results;
            } catch (error) {
                resultsDiv.textContent = `Error testing cache: ${error.message}`;
            }
        }

        async function testPWAIcons() {
            const resultsDiv = document.getElementById('pwa-results');
            
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                
                let results = 'PWA Icon Configuration:\n';
                results += `App Name: ${manifest.name}\n`;
                results += `Icons Defined: ${manifest.icons.length}\n`;
                results += `Shortcuts: ${manifest.shortcuts ? manifest.shortcuts.length : 0}\n\n`;
                
                results += 'Icon Details:\n';
                manifest.icons.forEach((icon, index) => {
                    results += `${index + 1}. ${icon.src} (${icon.sizes}) - ${icon.type}\n`;
                });
                
                resultsDiv.textContent = results;
            } catch (error) {
                resultsDiv.textContent = `Error testing PWA icons: ${error.message}`;
            }
        }

        async function refreshServiceWorker() {
            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    await registration.update();
                    alert('Service Worker updated successfully');
                } else {
                    alert('No service worker registration found');
                }
            } catch (error) {
                alert(`Error updating service worker: ${error.message}`);
            }
        }

        async function clearCache() {
            try {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                alert('All caches cleared successfully');
            } catch (error) {
                alert(`Error clearing cache: ${error.message}`);
            }
        }

        function reloadPage() {
            window.location.reload();
        }

        // Test all images on load
        window.addEventListener('load', () => {
            testImageLoad('svg-logo', 'svg-status', 'SVG Logo');
            testImageLoad('favicon', 'favicon-status', 'Favicon');
            testImageLoad('png-logo', 'png-status', 'PNG Logo');
        });
    </script>
</body>
</html>
