/**
 * Tutorial Storage Service
 * 
 * Handles persistence of tutorial progress, preferences, and state
 * using localStorage with fallback mechanisms and data validation.
 */

import {
  STORAGE_KEYS,
  TUTORIAL_STATUS,
  DEFAULT_TUTORIAL_PREFERENCES,
  createTutorialProgress
} from './types';

class TutorialStorage {
  constructor() {
    this.isLocalStorageAvailable = this.checkLocalStorageAvailability();
    this.memoryFallback = new Map();
  }

  /**
   * Check if localStorage is available
   */
  checkLocalStorageAvailability() {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      console.warn('localStorage not available, using memory fallback');
      return false;
    }
  }

  /**
   * Generic storage methods with fallback
   */
  setItem(key, value) {
    try {
      const serializedValue = JSON.stringify(value);
      if (this.isLocalStorageAvailable) {
        localStorage.setItem(key, serializedValue);
      } else {
        this.memoryFallback.set(key, serializedValue);
      }
    } catch (error) {
      console.error('Error storing data:', error);
      this.memoryFallback.set(key, JSON.stringify(value));
    }
  }

  getItem(key, defaultValue = null) {
    try {
      let value;
      if (this.isLocalStorageAvailable) {
        value = localStorage.getItem(key);
      } else {
        value = this.memoryFallback.get(key);
      }
      
      return value ? JSON.parse(value) : defaultValue;
    } catch (error) {
      console.error('Error retrieving data:', error);
      return defaultValue;
    }
  }

  removeItem(key) {
    try {
      if (this.isLocalStorageAvailable) {
        localStorage.removeItem(key);
      } else {
        this.memoryFallback.delete(key);
      }
    } catch (error) {
      console.error('Error removing data:', error);
    }
  }

  /**
   * Tutorial Progress Management
   */
  getTutorialProgress(tutorialId, userId = 'anonymous') {
    const allProgress = this.getItem(STORAGE_KEYS.TUTORIAL_PROGRESS, {});
    const userProgress = allProgress[userId] || {};
    
    if (userProgress[tutorialId]) {
      return userProgress[tutorialId];
    }
    
    // Return default progress if none exists
    return createTutorialProgress({ tutorialId, userId });
  }

  saveTutorialProgress(progress) {
    const allProgress = this.getItem(STORAGE_KEYS.TUTORIAL_PROGRESS, {});
    
    if (!allProgress[progress.userId]) {
      allProgress[progress.userId] = {};
    }
    
    allProgress[progress.userId][progress.tutorialId] = {
      ...progress,
      lastUpdated: new Date().toISOString()
    };
    
    this.setItem(STORAGE_KEYS.TUTORIAL_PROGRESS, allProgress);
  }

  getAllTutorialProgress(userId = 'anonymous') {
    const allProgress = this.getItem(STORAGE_KEYS.TUTORIAL_PROGRESS, {});
    return allProgress[userId] || {};
  }

  clearTutorialProgress(tutorialId, userId = 'anonymous') {
    const allProgress = this.getItem(STORAGE_KEYS.TUTORIAL_PROGRESS, {});
    
    if (allProgress[userId] && allProgress[userId][tutorialId]) {
      delete allProgress[userId][tutorialId];
      this.setItem(STORAGE_KEYS.TUTORIAL_PROGRESS, allProgress);
    }
  }

  /**
   * Tutorial Preferences Management
   */
  getTutorialPreferences() {
    return this.getItem(STORAGE_KEYS.TUTORIAL_PREFERENCES, DEFAULT_TUTORIAL_PREFERENCES);
  }

  saveTutorialPreferences(preferences) {
    const currentPreferences = this.getTutorialPreferences();
    const updatedPreferences = {
      ...currentPreferences,
      ...preferences,
      lastUpdated: new Date().toISOString()
    };
    
    this.setItem(STORAGE_KEYS.TUTORIAL_PREFERENCES, updatedPreferences);
  }

  resetTutorialPreferences() {
    this.setItem(STORAGE_KEYS.TUTORIAL_PREFERENCES, DEFAULT_TUTORIAL_PREFERENCES);
  }

  /**
   * Help Context Tracking
   */
  getShownHelpContexts(userId = 'anonymous') {
    const allShown = this.getItem(STORAGE_KEYS.HELP_CONTEXT_SHOWN, {});
    return allShown[userId] || [];
  }

  markHelpContextShown(contextId, userId = 'anonymous') {
    const allShown = this.getItem(STORAGE_KEYS.HELP_CONTEXT_SHOWN, {});
    
    if (!allShown[userId]) {
      allShown[userId] = [];
    }
    
    if (!allShown[userId].includes(contextId)) {
      allShown[userId].push(contextId);
      this.setItem(STORAGE_KEYS.HELP_CONTEXT_SHOWN, allShown);
    }
  }

  /**
   * Tutorial Completion Badges
   */
  getTutorialBadges(userId = 'anonymous') {
    const allBadges = this.getItem(STORAGE_KEYS.TUTORIAL_COMPLETION_BADGES, {});
    return allBadges[userId] || [];
  }

  addTutorialBadge(badge, userId = 'anonymous') {
    const allBadges = this.getItem(STORAGE_KEYS.TUTORIAL_COMPLETION_BADGES, {});
    
    if (!allBadges[userId]) {
      allBadges[userId] = [];
    }
    
    const badgeWithTimestamp = {
      ...badge,
      earnedAt: new Date().toISOString()
    };
    
    allBadges[userId].push(badgeWithTimestamp);
    this.setItem(STORAGE_KEYS.TUTORIAL_COMPLETION_BADGES, allBadges);
  }

  /**
   * Analytics and Statistics
   */
  getTutorialStatistics(userId = 'anonymous') {
    const progress = this.getAllTutorialProgress(userId);
    const badges = this.getTutorialBadges(userId);
    
    const stats = {
      totalTutorialsStarted: 0,
      totalTutorialsCompleted: 0,
      totalTutorialsSkipped: 0,
      totalTimeSpent: 0,
      averageCompletionTime: 0,
      badgesEarned: badges.length,
      completionRate: 0
    };
    
    Object.values(progress).forEach(tutorialProgress => {
      if (tutorialProgress.status !== TUTORIAL_STATUS.NOT_STARTED) {
        stats.totalTutorialsStarted++;
      }
      
      if (tutorialProgress.status === TUTORIAL_STATUS.COMPLETED) {
        stats.totalTutorialsCompleted++;
      }
      
      if (tutorialProgress.status === TUTORIAL_STATUS.SKIPPED) {
        stats.totalTutorialsSkipped++;
      }
      
      stats.totalTimeSpent += tutorialProgress.timeSpent || 0;
    });
    
    if (stats.totalTutorialsCompleted > 0) {
      stats.averageCompletionTime = stats.totalTimeSpent / stats.totalTutorialsCompleted;
    }
    
    if (stats.totalTutorialsStarted > 0) {
      stats.completionRate = (stats.totalTutorialsCompleted / stats.totalTutorialsStarted) * 100;
    }
    
    return stats;
  }

  /**
   * Data Export/Import for backup and migration
   */
  exportTutorialData(userId = 'anonymous') {
    return {
      progress: this.getAllTutorialProgress(userId),
      preferences: this.getTutorialPreferences(),
      badges: this.getTutorialBadges(userId),
      shownContexts: this.getShownHelpContexts(userId),
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
  }

  importTutorialData(data, userId = 'anonymous') {
    try {
      if (data.progress) {
        const allProgress = this.getItem(STORAGE_KEYS.TUTORIAL_PROGRESS, {});
        allProgress[userId] = data.progress;
        this.setItem(STORAGE_KEYS.TUTORIAL_PROGRESS, allProgress);
      }
      
      if (data.preferences) {
        this.saveTutorialPreferences(data.preferences);
      }
      
      if (data.badges) {
        const allBadges = this.getItem(STORAGE_KEYS.TUTORIAL_COMPLETION_BADGES, {});
        allBadges[userId] = data.badges;
        this.setItem(STORAGE_KEYS.TUTORIAL_COMPLETION_BADGES, allBadges);
      }
      
      if (data.shownContexts) {
        const allShown = this.getItem(STORAGE_KEYS.HELP_CONTEXT_SHOWN, {});
        allShown[userId] = data.shownContexts;
        this.setItem(STORAGE_KEYS.HELP_CONTEXT_SHOWN, allShown);
      }
      
      return true;
    } catch (error) {
      console.error('Error importing tutorial data:', error);
      return false;
    }
  }

  /**
   * Clear all tutorial data
   */
  clearAllTutorialData(userId = 'anonymous') {
    const allProgress = this.getItem(STORAGE_KEYS.TUTORIAL_PROGRESS, {});
    const allBadges = this.getItem(STORAGE_KEYS.TUTORIAL_COMPLETION_BADGES, {});
    const allShown = this.getItem(STORAGE_KEYS.HELP_CONTEXT_SHOWN, {});
    
    delete allProgress[userId];
    delete allBadges[userId];
    delete allShown[userId];
    
    this.setItem(STORAGE_KEYS.TUTORIAL_PROGRESS, allProgress);
    this.setItem(STORAGE_KEYS.TUTORIAL_COMPLETION_BADGES, allBadges);
    this.setItem(STORAGE_KEYS.HELP_CONTEXT_SHOWN, allShown);
    
    // Reset preferences to default
    this.resetTutorialPreferences();
  }
}

// Create singleton instance
const tutorialStorage = new TutorialStorage();

export default tutorialStorage;
