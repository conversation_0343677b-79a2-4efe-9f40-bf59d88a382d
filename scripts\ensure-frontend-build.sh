#!/bin/bash
# Ensure Frontend Build Script for App Builder 201

echo "🏗️ Ensuring frontend build exists..."

# Navigate to frontend directory
cd frontend

# Check if build directory exists
if [ ! -d "build" ]; then
    echo "📦 Build directory not found. Creating production build..."
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        echo "📥 Installing frontend dependencies..."
        npm install
    fi
    
    # Create production build
    echo "🔨 Building frontend for production..."
    npm run build
    
    if [ $? -eq 0 ]; then
        echo "✅ Frontend build completed successfully!"
    else
        echo "❌ Frontend build failed!"
        exit 1
    fi
else
    echo "✅ Frontend build directory already exists"
    
    # Check if build is recent (less than 1 hour old)
    if [ $(find build -maxdepth 0 -mmin -60 2>/dev/null | wc -l) -eq 0 ]; then
        echo "⚠️ Build directory is older than 1 hour. Consider rebuilding."
        echo "💡 Run: npm run build (in frontend directory)"
    fi
fi

# Verify static files exist
if [ -d "build/static" ]; then
    file_count=$(find build/static -type f | wc -l)
    echo "📊 Found $file_count static files in build directory"
    
    if [ $file_count -gt 0 ]; then
        echo "✅ Static files are ready for Django"
    else
        echo "⚠️ No static files found in build directory"
    fi
else
    echo "❌ build/static directory not found!"
    exit 1
fi

echo "🎉 Frontend build verification complete!"
