import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { requestPasswordReset } from '../../utils/auth';

/**
 * Forgot Password Component
 * 
 * This component provides a form for requesting a password reset.
 */
const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  
  // Handle input change
  const handleChange = (e) => {
    setEmail(e.target.value);
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate email
    if (!email) {
      setError('Please enter your email address');
      return;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Request password reset
      const result = await requestPasswordReset(email);
      
      if (result.success) {
        setSuccess(true);
      } else {
        setError(result.error || 'Password reset request failed');
      }
    } catch (error) {
      console.error('Password reset request error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="forgot-password-container">
      <div className="forgot-password-card">
        <div className="forgot-password-header">
          <h2>Forgot Password</h2>
          <p>Enter your email address to reset your password.</p>
        </div>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        {success ? (
          <div className="success-message">
            <h3>Check Your Email</h3>
            <p>
              We've sent a password reset link to <strong>{email}</strong>.
              Please check your email and follow the instructions to reset your password.
            </p>
            <p>
              If you don't receive an email within a few minutes, please check your spam folder.
            </p>
            <div className="success-actions">
              <Link to="/login" className="back-to-login">
                Back to Login
              </Link>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="forgot-password-form">
            <div className="form-group">
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={handleChange}
                placeholder="Enter your email"
                disabled={loading}
                autoComplete="email"
                required
              />
            </div>
            
            <button
              type="submit"
              className="reset-button"
              disabled={loading}
            >
              {loading ? 'Sending...' : 'Reset Password'}
            </button>
            
            <div className="form-footer">
              <Link to="/login" className="back-to-login">
                Back to Login
              </Link>
            </div>
          </form>
        )}
      </div>
      
      <style jsx>{`
        .forgot-password-container {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          padding: var(--spacing-md);
          background-color: var(--color-background);
        }
        
        .forgot-password-card {
          width: 100%;
          max-width: 400px;
          padding: var(--spacing-lg);
          background-color: var(--color-surface);
          border-radius: var(--border-radius-lg);
          box-shadow: var(--shadow-md);
        }
        
        .forgot-password-header {
          margin-bottom: var(--spacing-lg);
          text-align: center;
        }
        
        .forgot-password-header h2 {
          margin-bottom: var(--spacing-xs);
          color: var(--color-text);
        }
        
        .forgot-password-header p {
          color: var(--color-textSecondary);
        }
        
        .error-message {
          padding: var(--spacing-sm);
          margin-bottom: var(--spacing-md);
          background-color: rgba(var(--color-error-rgb), 0.1);
          border: 1px solid var(--color-error);
          border-radius: var(--border-radius-md);
          color: var(--color-error);
          font-size: var(--font-size-sm);
        }
        
        .success-message {
          padding: var(--spacing-md);
          margin-bottom: var(--spacing-md);
          background-color: rgba(var(--color-success-rgb), 0.1);
          border: 1px solid var(--color-success);
          border-radius: var(--border-radius-md);
          color: var(--color-text);
        }
        
        .success-message h3 {
          margin-bottom: var(--spacing-sm);
          color: var(--color-success);
        }
        
        .success-actions {
          margin-top: var(--spacing-md);
          text-align: center;
        }
        
        .forgot-password-form {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);
        }
        
        .form-group {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-xs);
        }
        
        .form-group label {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .form-group input {
          padding: var(--spacing-sm) var(--spacing-md);
          border: 1px solid var(--color-border);
          border-radius: var(--border-radius-md);
          background-color: var(--color-background);
          color: var(--color-text);
          font-size: var(--font-size-md);
          transition: border-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .form-group input:focus {
          outline: none;
          border-color: var(--color-primary);
        }
        
        .reset-button {
          padding: var(--spacing-sm) var(--spacing-md);
          background-color: var(--color-primary);
          color: white;
          border: none;
          border-radius: var(--border-radius-md);
          font-size: var(--font-size-md);
          font-weight: var(--font-weight-medium);
          cursor: pointer;
          transition: background-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .reset-button:hover {
          background-color: color-mix(in srgb, var(--color-primary) 80%, black);
        }
        
        .reset-button:disabled {
          background-color: var(--color-border);
          color: var(--color-textSecondary);
          cursor: not-allowed;
        }
        
        .form-footer {
          margin-top: var(--spacing-md);
          text-align: center;
        }
        
        .back-to-login {
          color: var(--color-primary);
          text-decoration: none;
          font-size: var(--font-size-sm);
        }
        
        .back-to-login:hover {
          text-decoration: underline;
        }
      `}</style>
    </div>
  );
};

export default ForgotPassword;
