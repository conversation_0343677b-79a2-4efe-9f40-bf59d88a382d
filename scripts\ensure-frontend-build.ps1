# Ensure Frontend Build Script for App Builder 201
Write-Host "Ensuring frontend build exists..." -ForegroundColor Cyan

# Navigate to frontend directory
if (-not (Test-Path "frontend")) {
    Write-Host "ERROR: frontend directory not found. Please run from project root." -ForegroundColor Red
    exit 1
}

Push-Location frontend

try {
    # Check if build directory exists
    if (-not (Test-Path "build")) {
        Write-Host "Build directory not found. Creating production build..." -ForegroundColor Yellow
        
        # Install dependencies if node_modules doesn't exist
        if (-not (Test-Path "node_modules")) {
            Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
            npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
                exit 1
            }
        }
        
        # Create production build
        Write-Host "Building frontend for production..." -ForegroundColor Yellow
        npm run build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: Frontend build completed!" -ForegroundColor Green
        } else {
            Write-Host "ERROR: Frontend build failed!" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "SUCCESS: Frontend build directory already exists" -ForegroundColor Green
        
        # Check if build is recent (less than 1 hour old)
        $buildDir = Get-Item "build"
        $hourAgo = (Get-Date).AddHours(-1)
        
        if ($buildDir.LastWriteTime -lt $hourAgo) {
            Write-Host "WARNING: Build directory is older than 1 hour. Consider rebuilding." -ForegroundColor Yellow
            Write-Host "TIP: Run 'npm run build' in frontend directory" -ForegroundColor Blue
        }
    }

    # Verify static files exist
    if (Test-Path "build/static") {
        $fileCount = (Get-ChildItem -Path "build/static" -Recurse -File).Count
        Write-Host "Found $fileCount static files in build directory" -ForegroundColor Blue
        
        if ($fileCount -gt 0) {
            Write-Host "SUCCESS: Static files are ready for Django" -ForegroundColor Green
        } else {
            Write-Host "WARNING: No static files found in build directory" -ForegroundColor Yellow
        }
    } else {
        Write-Host "ERROR: build/static directory not found!" -ForegroundColor Red
        exit 1
    }

    Write-Host "Frontend build verification complete!" -ForegroundColor Green

} finally {
    Pop-Location
}
