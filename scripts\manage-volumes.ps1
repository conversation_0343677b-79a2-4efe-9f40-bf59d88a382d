# PowerShell script to manage Docker volumes for the App Builder project
# This script helps with listing, cleaning, and verifying Docker volumes

param (
    [Parameter(Mandatory = $false)]
    [ValidateSet("list", "clean", "verify", "recreate", "help")]
    [string]$Action = "help",

    [Parameter(Mandatory = $false)]
    [switch]$Force = $false
)

# Define project name for volume prefixes
$projectName = "app-builder-201"

# Define colors for output
$colors = @{
    Success   = "Green"
    Warning   = "Yellow"
    Error     = "Red"
    Info      = "Cyan"
    Highlight = "Magenta"
}

# Function to display help
function Show-Help {
    Write-Host "`nDocker Volume Management Script" -ForegroundColor $colors.Highlight
    Write-Host "==============================" -ForegroundColor $colors.Highlight
    Write-Host "This script helps manage Docker volumes for the App Builder project.`n" -ForegroundColor $colors.Info

    Write-Host "Usage:" -ForegroundColor $colors.Highlight
    Write-Host "  .\manage-volumes.ps1 [Action] [-Force]`n" -ForegroundColor $colors.Info

    Write-Host "Actions:" -ForegroundColor $colors.Highlight
    Write-Host "  list    - List all Docker volumes related to this project" -ForegroundColor $colors.Info
    Write-Host "  clean   - Remove unused Docker volumes" -ForegroundColor $colors.Info
    Write-Host "  verify  - Verify volume mounts are working correctly" -ForegroundColor $colors.Info
    Write-Host "  recreate - Recreate specific volumes (e.g., node_modules)" -ForegroundColor $colors.Info
    Write-Host "  help    - Show this help message`n" -ForegroundColor $colors.Info

    Write-Host "Options:" -ForegroundColor $colors.Highlight
    Write-Host "  -Force  - Skip confirmation prompts for destructive actions`n" -ForegroundColor $colors.Info

    Write-Host "Examples:" -ForegroundColor $colors.Highlight
    Write-Host "  .\manage-volumes.ps1 list" -ForegroundColor $colors.Info
    Write-Host "  .\manage-volumes.ps1 clean" -ForegroundColor $colors.Info
    Write-Host "  .\manage-volumes.ps1 recreate -Force" -ForegroundColor $colors.Info
}

# Function to list volumes
function Get-DockerVolumes {
    Write-Host "`nListing Docker volumes for $projectName..." -ForegroundColor $colors.Info

    $volumes = docker volume ls --format "{{.Name}}" | Where-Object { $_ -like "*$projectName*" }

    if ($volumes) {
        Write-Host "`nProject volumes:" -ForegroundColor $colors.Highlight
        foreach ($volume in $volumes) {
            Write-Host "  - $volume" -ForegroundColor $colors.Success
        }

        Write-Host "`nTotal project volumes: $($volumes.Count)" -ForegroundColor $colors.Info
    }
    else {
        Write-Host "No volumes found for $projectName" -ForegroundColor $colors.Warning
    }

    # Show volume details
    Write-Host "`nVolume details:" -ForegroundColor $colors.Highlight
    foreach ($volume in $volumes) {
        Write-Host "`nInspecting ${volume}:" -ForegroundColor $colors.Info
        docker volume inspect $volume
    }
}

# Function to clean volumes
function Remove-UnusedVolumes {
    Write-Host "`nCleaning unused Docker volumes..." -ForegroundColor $colors.Info

    if (-not $Force) {
        $confirmation = Read-Host "This will remove all unused volumes. Continue? (y/n)"
        if ($confirmation -ne "y") {
            Write-Host "Operation cancelled." -ForegroundColor $colors.Warning
            return
        }
    }

    Write-Host "Removing unused volumes..." -ForegroundColor $colors.Info
    docker volume prune -f

    Write-Host "Unused volumes removed." -ForegroundColor $colors.Success
}

# Function to verify volume mounts
function Test-VolumeMounts {
    Write-Host "`nVerifying Docker volume mounts..." -ForegroundColor $colors.Info

    # Check if containers are running
    $runningContainers = docker-compose ps --services --filter "status=running"

    if (-not $runningContainers) {
        Write-Host "No containers are running. Starting containers..." -ForegroundColor $colors.Warning
        docker-compose up -d
    }

    # Verify frontend volume
    Write-Host "`nVerifying frontend volume mount:" -ForegroundColor $colors.Highlight
    docker-compose exec frontend ls -la /app | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Frontend volume mount is working" -ForegroundColor $colors.Success
    }
    else {
        Write-Host "❌ Frontend volume mount is NOT working" -ForegroundColor $colors.Error
    }

    # Verify node_modules volume
    Write-Host "`nVerifying node_modules volume mount:" -ForegroundColor $colors.Highlight
    docker-compose exec frontend ls -la /app/node_modules | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node modules volume mount is working" -ForegroundColor $colors.Success
    }
    else {
        Write-Host "❌ Node modules volume mount is NOT working" -ForegroundColor $colors.Error
    }

    # Verify backend volume
    Write-Host "`nVerifying backend volume mount:" -ForegroundColor $colors.Highlight
    docker-compose exec backend ls -la /app | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend volume mount is working" -ForegroundColor $colors.Success
    }
    else {
        Write-Host "❌ Backend volume mount is NOT working" -ForegroundColor $colors.Error
    }

    # Verify database volume
    Write-Host "`nVerifying database volume mount:" -ForegroundColor $colors.Highlight
    docker-compose exec db ls -la /var/lib/postgresql/data | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database volume mount is working" -ForegroundColor $colors.Success
    }
    else {
        Write-Host "❌ Database volume mount is NOT working" -ForegroundColor $colors.Error
    }
}

# Function to recreate specific volumes
function Reset-DockerVolumes {
    Write-Host "`nRecreating Docker volumes..." -ForegroundColor $colors.Info

    if (-not $Force) {
        $confirmation = Read-Host "This will stop containers and recreate volumes. Continue? (y/n)"
        if ($confirmation -ne "y") {
            Write-Host "Operation cancelled." -ForegroundColor $colors.Warning
            return
        }
    }

    # Stop containers
    Write-Host "Stopping containers..." -ForegroundColor $colors.Info
    docker-compose down

    # Remove node_modules volume
    Write-Host "Removing node_modules volume..." -ForegroundColor $colors.Info
    docker volume rm ${projectName}_frontend_node_modules -f

    # Start containers
    Write-Host "Starting containers..." -ForegroundColor $colors.Info
    docker-compose up -d

    Write-Host "Volumes recreated successfully." -ForegroundColor $colors.Success
}

# Main script execution
switch ($Action) {
    "list" {
        Get-DockerVolumes
    }
    "clean" {
        Remove-UnusedVolumes
    }
    "verify" {
        Test-VolumeMounts
    }
    "recreate" {
        Reset-DockerVolumes
    }
    "help" {
        Show-Help
    }
    default {
        Write-Host "Unknown action: $Action" -ForegroundColor $colors.Error
        Show-Help
    }
}
