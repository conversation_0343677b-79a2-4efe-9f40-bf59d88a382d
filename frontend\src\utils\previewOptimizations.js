import { debounce, throttle } from 'lodash';

/**
 * Performance optimization utilities for the preview system
 */

// Component update batching
class UpdateBatcher {
  constructor(batchSize = 10, batchDelay = 16) {
    this.batchSize = batchSize;
    this.batchDelay = batchDelay;
    this.pendingUpdates = new Map();
    this.batchTimeout = null;
    this.callbacks = new Set();
  }

  addUpdate(componentId, updateData) {
    this.pendingUpdates.set(componentId, {
      ...this.pendingUpdates.get(componentId),
      ...updateData,
      timestamp: Date.now()
    });

    this.scheduleBatch();
  }

  scheduleBatch() {
    if (this.batchTimeout) return;

    this.batchTimeout = setTimeout(() => {
      this.processBatch();
    }, this.batchDelay);
  }

  processBatch() {
    if (this.pendingUpdates.size === 0) return;

    const updates = Array.from(this.pendingUpdates.entries());
    this.pendingUpdates.clear();
    this.batchTimeout = null;

    // Notify all callbacks
    this.callbacks.forEach(callback => {
      try {
        callback(updates);
      } catch (error) {
        console.error('Error in update batch callback:', error);
      }
    });
  }

  onBatch(callback) {
    this.callbacks.add(callback);
    return () => this.callbacks.delete(callback);
  }

  flush() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }
    this.processBatch();
  }

  clear() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }
    this.pendingUpdates.clear();
  }
}

// Memory management for component cache
class ComponentCacheManager {
  constructor(maxSize = 100, ttl = 300000) { // 5 minutes TTL
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.cache = new Map();
    this.accessTimes = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  set(key, value) {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, value);
    this.accessTimes.set(key, Date.now());
  }

  get(key) {
    const value = this.cache.get(key);
    if (value !== undefined) {
      this.accessTimes.set(key, Date.now());
    }
    return value;
  }

  has(key) {
    return this.cache.has(key);
  }

  delete(key) {
    this.cache.delete(key);
    this.accessTimes.delete(key);
  }

  evictOldest() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  cleanup() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, time] of this.accessTimes) {
      if (now - time > this.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.delete(key));
  }

  clear() {
    this.cache.clear();
    this.accessTimes.clear();
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
    };
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Render optimization utilities
export const createOptimizedRenderer = (renderFunction, options = {}) => {
  const {
    debounceDelay = 16,
    throttleDelay = 8,
    enableBatching = true,
    batchSize = 10
  } = options;

  let batcher = null;
  if (enableBatching) {
    batcher = new UpdateBatcher(batchSize, debounceDelay);
  }

  const debouncedRender = debounce(renderFunction, debounceDelay);
  const throttledRender = throttle(renderFunction, throttleDelay);

  return {
    render: (data, immediate = false) => {
      if (immediate) {
        renderFunction(data);
      } else if (batcher) {
        batcher.addUpdate(data.id || 'default', data);
      } else {
        debouncedRender(data);
      }
    },
    
    renderThrottled: throttledRender,
    renderDebounced: debouncedRender,
    
    flush: () => {
      if (batcher) batcher.flush();
      debouncedRender.flush();
    },
    
    cancel: () => {
      if (batcher) batcher.clear();
      debouncedRender.cancel();
      throttledRender.cancel();
    },

    onBatch: batcher ? batcher.onBatch.bind(batcher) : null
  };
};

// Virtual scrolling utilities
export const calculateVisibleRange = (
  scrollTop,
  containerHeight,
  itemHeight,
  totalItems,
  overscan = 5
) => {
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + overscan,
    totalItems
  );

  return {
    start: Math.max(0, startIndex - overscan),
    end: endIndex
  };
};

// Performance monitoring utilities
export class PerformanceTracker {
  constructor() {
    this.metrics = new Map();
    this.startTimes = new Map();
  }

  start(label) {
    this.startTimes.set(label, performance.now());
  }

  end(label) {
    const startTime = this.startTimes.get(label);
    if (startTime === undefined) return null;

    const duration = performance.now() - startTime;
    this.startTimes.delete(label);

    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }

    const measurements = this.metrics.get(label);
    measurements.push(duration);

    // Keep only last 100 measurements
    if (measurements.length > 100) {
      measurements.shift();
    }

    return duration;
  }

  getAverage(label) {
    const measurements = this.metrics.get(label);
    if (!measurements || measurements.length === 0) return 0;

    return measurements.reduce((sum, val) => sum + val, 0) / measurements.length;
  }

  getMetrics(label) {
    const measurements = this.metrics.get(label) || [];
    if (measurements.length === 0) return null;

    return {
      count: measurements.length,
      average: this.getAverage(label),
      min: Math.min(...measurements),
      max: Math.max(...measurements),
      latest: measurements[measurements.length - 1]
    };
  }

  getAllMetrics() {
    const result = {};
    for (const [label, measurements] of this.metrics) {
      result[label] = this.getMetrics(label);
    }
    return result;
  }

  clear(label) {
    if (label) {
      this.metrics.delete(label);
      this.startTimes.delete(label);
    } else {
      this.metrics.clear();
      this.startTimes.clear();
    }
  }
}

// Frame rate monitoring
export class FrameRateMonitor {
  constructor(callback) {
    this.callback = callback;
    this.frameCount = 0;
    this.lastTime = performance.now();
    this.isRunning = false;
    this.animationId = null;
  }

  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.frameCount = 0;
    this.lastTime = performance.now();
    this.tick();
  }

  stop() {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  tick() {
    if (!this.isRunning) return;

    const now = performance.now();
    this.frameCount++;

    if (now - this.lastTime >= 1000) {
      const fps = Math.round((this.frameCount * 1000) / (now - this.lastTime));
      this.callback(fps);
      
      this.frameCount = 0;
      this.lastTime = now;
    }

    this.animationId = requestAnimationFrame(() => this.tick());
  }
}

// Export instances for global use
export const globalUpdateBatcher = new UpdateBatcher();
export const globalCacheManager = new ComponentCacheManager();
export const globalPerformanceTracker = new PerformanceTracker();

// Cleanup function
export const cleanup = () => {
  globalUpdateBatcher.clear();
  globalCacheManager.destroy();
  globalPerformanceTracker.clear();
};
