/**
 * Final API Verification Test
 * 
 * This script comprehensively tests that all API and service worker issues are resolved:
 * 1. Service worker is registered but doesn't intercept API calls
 * 2. Backend API endpoints respond correctly
 * 3. Frontend proxy routes API calls properly
 * 4. No authentication errors (401) occur
 * 5. All endpoints return expected JSON responses
 */

async function runFinalVerificationTest() {
  console.log('🔍 Running Final API Verification Test...');
  console.log('=====================================\n');
  
  const results = {
    serviceWorker: false,
    backendDirect: false,
    frontendProxy: false,
    authenticationFixed: false,
    overallSuccess: false
  };

  // Test 1: Service Worker Registration (should work)
  console.log('1️⃣ Testing Service Worker Registration...');
  try {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration && registration.active) {
        console.log('✅ Service Worker is registered and active');
        console.log(`   Scope: ${registration.scope}`);
        results.serviceWorker = true;
      } else {
        console.log('❌ Service Worker not active');
      }
    } else {
      console.log('❌ Service Worker not supported');
    }
  } catch (error) {
    console.log(`❌ Service Worker error: ${error.message}`);
  }

  // Test 2: Backend Direct API Access (should work)
  console.log('\n2️⃣ Testing Backend Direct API Access...');
  const backendEndpoints = [
    'http://localhost:8000/api/status/',
    'http://localhost:8000/api/health/'
  ];

  let backendSuccess = true;
  for (const endpoint of backendEndpoints) {
    try {
      const response = await fetch(endpoint, { 
        method: 'GET',
        mode: 'cors'
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${endpoint} - Status: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(data).substring(0, 80)}...`);
      } else {
        console.log(`❌ ${endpoint} - Status: ${response.status}`);
        backendSuccess = false;
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Error: ${error.message}`);
      backendSuccess = false;
    }
  }
  results.backendDirect = backendSuccess;

  // Test 3: Frontend Proxy API Access (should work)
  console.log('\n3️⃣ Testing Frontend Proxy API Access...');
  const proxyEndpoints = [
    '/api/status/',
    '/api/health/',
    '/api/'
  ];

  let proxySuccess = true;
  let authFixed = true;
  
  for (const endpoint of proxyEndpoints) {
    try {
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      console.log(`📡 ${endpoint} - Status: ${response.status}`);
      
      if (response.status === 401) {
        console.log(`❌ ${endpoint} - Authentication error (401)`);
        authFixed = false;
        proxySuccess = false;
      } else if (response.ok) {
        try {
          const data = await response.json();
          console.log(`✅ ${endpoint} - Success`);
          console.log(`   Response: ${JSON.stringify(data).substring(0, 80)}...`);
        } catch (parseError) {
          const text = await response.text();
          console.log(`✅ ${endpoint} - Success (non-JSON response)`);
          console.log(`   Response: ${text.substring(0, 80)}...`);
        }
      } else {
        console.log(`⚠️ ${endpoint} - Status: ${response.status}`);
        if (response.status !== 404) { // 404 might be expected for some endpoints
          proxySuccess = false;
        }
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Error: ${error.message}`);
      proxySuccess = false;
    }
  }
  
  results.frontendProxy = proxySuccess;
  results.authenticationFixed = authFixed;

  // Test 4: Service Worker Non-Interference
  console.log('\n4️⃣ Testing Service Worker Non-Interference...');
  try {
    // Check if service worker is intercepting API calls
    const testResponse = await fetch('/api/status/', {
      method: 'GET',
      headers: {
        'X-Test-Header': 'service-worker-test'
      }
    });
    
    if (testResponse.ok) {
      console.log('✅ Service Worker is not interfering with API calls');
    } else {
      console.log('⚠️ API call failed, but this might not be service worker related');
    }
  } catch (error) {
    console.log(`❌ Service Worker interference test failed: ${error.message}`);
  }

  // Overall Results
  console.log('\n📊 Final Test Results:');
  console.log('======================');
  console.log(`Service Worker Registration: ${results.serviceWorker ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Backend Direct Access: ${results.backendDirect ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend Proxy Access: ${results.frontendProxy ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Authentication Fixed: ${results.authenticationFixed ? '✅ PASS' : '❌ FAIL'}`);

  results.overallSuccess = results.serviceWorker && 
                          results.backendDirect && 
                          results.frontendProxy && 
                          results.authenticationFixed;

  console.log(`\n🎯 Overall Result: ${results.overallSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  if (results.overallSuccess) {
    console.log('\n🎉 SUCCESS! All API and service worker issues have been resolved:');
    console.log('   • Service worker is working but not interfering with API calls');
    console.log('   • Backend API endpoints are accessible and returning proper responses');
    console.log('   • Frontend proxy is correctly routing API calls to the backend');
    console.log('   • No authentication errors (401) are occurring');
    console.log('   • Your App Builder application should now work correctly!');
  } else {
    console.log('\n⚠️ Some issues remain. Please check the failed tests above.');
  }

  return results;
}

// Auto-run the test when the script loads
if (typeof window !== 'undefined') {
  window.runFinalVerificationTest = runFinalVerificationTest;
  console.log('🔧 Final API Verification Test loaded. Run runFinalVerificationTest() to start.');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runFinalVerificationTest };
}
