import React, { useState, useEffect } from 'react';
import { ColorPicker, Input, Space, Button, Popover, Typography, Divider } from 'antd';
import { BgColorsOutlined, EyeDropperOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';
import theme from '../../../design-system/theme';

const { Text } = Typography;

const ColorInputContainer = styled.div`
  width: 100%;
`;

const ColorPreview = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${props => props.color || '#ffffff'};
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #ccc 75%), 
                linear-gradient(-45deg, transparent 75%, #ccc 75%);
    background-size: 8px 8px;
    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
    z-index: -1;
  }
`;

const ColorModeToggle = styled.div`
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
`;

const ModeButton = styled(Button)`
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
`;

const PresetGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  margin-top: 8px;
`;

const PresetColor = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  background: ${props => props.color};
  
  &:hover {
    border-color: #1890ff;
    transform: scale(1.1);
  }
`;

/**
 * Enhanced color picker with multiple format support and presets
 */
const ColorInput = ({
  value,
  onChange,
  showPresets = true,
  showModeToggle = true,
  presets = [],
  placeholder = 'Enter color',
  ...props
}) => {
  const [colorMode, setColorMode] = useState('hex');
  const [colorValue, setColorValue] = useState(value || '#ffffff');
  const [inputValue, setInputValue] = useState('');

  // Default color presets
  const defaultPresets = [
    '#ffffff', '#f5f5f5', '#d9d9d9', '#bfbfbf',
    '#8c8c8c', '#595959', '#262626', '#000000',
    '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d',
    '#bae637', '#73d13d', '#40a9ff', '#597ef7',
    '#9254de', '#f759ab', '#ff85c0', '#ffc069',
    ...theme.colors.primary ? [theme.colors.primary[500]] : [],
    ...theme.colors.secondary ? [theme.colors.secondary[500]] : [],
    ...presets
  ];

  useEffect(() => {
    if (value) {
      setColorValue(value);
      setInputValue(formatColorForMode(value, colorMode));
    }
  }, [value, colorMode]);

  // Convert color to different formats
  const formatColorForMode = (color, mode) => {
    if (!color) return '';
    
    try {
      // Simple format conversion (could be enhanced with a color library)
      switch (mode) {
        case 'hex':
          return color.startsWith('#') ? color : `#${color}`;
        case 'rgb':
          return convertToRgb(color);
        case 'hsl':
          return convertToHsl(color);
        default:
          return color;
      }
    } catch (error) {
      return color;
    }
  };

  // Simple hex to RGB conversion
  const convertToRgb = (hex) => {
    if (!hex.startsWith('#')) return hex;
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgb(${r}, ${g}, ${b})`;
  };

  // Simple hex to HSL conversion (simplified)
  const convertToHsl = (hex) => {
    if (!hex.startsWith('#')) return hex;
    // This is a simplified conversion - in a real app you'd use a color library
    return `hsl(0, 0%, 50%)`; // Placeholder
  };

  const handleColorChange = (color) => {
    const colorString = color.toHexString();
    setColorValue(colorString);
    setInputValue(formatColorForMode(colorString, colorMode));
    onChange?.(colorString);
  };

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Validate and update color if valid
    if (isValidColor(newValue)) {
      setColorValue(newValue);
      onChange?.(newValue);
    }
  };

  const handlePresetClick = (color) => {
    setColorValue(color);
    setInputValue(formatColorForMode(color, colorMode));
    onChange?.(color);
  };

  const isValidColor = (color) => {
    // Simple color validation
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/;
    const hslRegex = /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/;
    
    return hexRegex.test(color) || rgbRegex.test(color) || hslRegex.test(color);
  };

  const colorPickerContent = (
    <div style={{ width: 280 }}>
      {showModeToggle && (
        <>
          <ColorModeToggle>
            <ModeButton 
              type={colorMode === 'hex' ? 'primary' : 'default'}
              size="small"
              onClick={() => setColorMode('hex')}
            >
              HEX
            </ModeButton>
            <ModeButton 
              type={colorMode === 'rgb' ? 'primary' : 'default'}
              size="small"
              onClick={() => setColorMode('rgb')}
            >
              RGB
            </ModeButton>
            <ModeButton 
              type={colorMode === 'hsl' ? 'primary' : 'default'}
              size="small"
              onClick={() => setColorMode('hsl')}
            >
              HSL
            </ModeButton>
          </ColorModeToggle>
          <Divider style={{ margin: '8px 0' }} />
        </>
      )}
      
      <ColorPicker
        value={colorValue}
        onChange={handleColorChange}
        showText
        size="large"
        {...props}
      />
      
      {showPresets && (
        <>
          <Divider style={{ margin: '8px 0' }} />
          <Text strong style={{ fontSize: '12px' }}>Color Presets</Text>
          <PresetGrid>
            {defaultPresets.slice(0, 24).map((preset, index) => (
              <PresetColor
                key={index}
                color={preset}
                onClick={() => handlePresetClick(preset)}
                title={preset}
              />
            ))}
          </PresetGrid>
        </>
      )}
    </div>
  );

  return (
    <ColorInputContainer>
      <Space.Compact style={{ width: '100%' }}>
        <Popover
          content={colorPickerContent}
          trigger="click"
          placement="bottomLeft"
        >
          <ColorPreview color={colorValue}>
            <BgColorsOutlined style={{ color: 'rgba(0,0,0,0.3)' }} />
          </ColorPreview>
        </Popover>
        
        <Input
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          style={{ flex: 1 }}
        />
      </Space.Compact>
    </ColorInputContainer>
  );
};

export default ColorInput;
