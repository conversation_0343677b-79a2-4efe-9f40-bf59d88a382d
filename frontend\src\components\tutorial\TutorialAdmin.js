/**
 * Tutorial Admin Component
 * 
 * Administrative interface for creating, editing, and managing tutorials.
 * Allows easy addition of new tutorials and modification of existing ones.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Table,
  Modal,
  message,
  Tabs,
  Typography,
  Tag,
  Popconfirm,
  Switch,
  InputNumber,
  Divider,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  EyeOutlined,
  CopyOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { useTutorial } from './TutorialManager';
import {
  TUTORIAL_CATEGORIES,
  TUTORIAL_STEP_TYPES,
  createTutorial,
  createTutorialStep
} from './types';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

const TutorialAdmin = () => {
  const {
    getAllTutorials,
    registerTutorial,
    unregisterTutorial,
    startTutorial
  } = useTutorial();

  const [tutorials, setTutorials] = useState([]);
  const [editingTutorial, setEditingTutorial] = useState(null);
  const [showTutorialModal, setShowTutorialModal] = useState(false);
  const [showStepModal, setShowStepModal] = useState(false);
  const [editingStep, setEditingStep] = useState(null);
  const [currentSteps, setCurrentSteps] = useState([]);
  const [form] = Form.useForm();
  const [stepForm] = Form.useForm();

  useEffect(() => {
    setTutorials(getAllTutorials());
  }, [getAllTutorials]);

  const handleCreateTutorial = () => {
    setEditingTutorial(null);
    setCurrentSteps([]);
    form.resetFields();
    setShowTutorialModal(true);
  };

  const handleEditTutorial = (tutorial) => {
    setEditingTutorial(tutorial);
    setCurrentSteps(tutorial.steps || []);
    form.setFieldsValue({
      ...tutorial,
      prerequisites: tutorial.prerequisites || [],
      tags: tutorial.tags || []
    });
    setShowTutorialModal(true);
  };

  const handleSaveTutorial = async (values) => {
    try {
      const tutorialData = createTutorial({
        id: editingTutorial?.id || `tutorial_${Date.now()}`,
        ...values,
        steps: currentSteps
      });

      if (editingTutorial) {
        // Update existing tutorial
        unregisterTutorial(editingTutorial.id);
      }

      registerTutorial(tutorialData);
      setTutorials(getAllTutorials());
      setShowTutorialModal(false);
      message.success(`Tutorial ${editingTutorial ? 'updated' : 'created'} successfully`);
    } catch (error) {
      message.error('Failed to save tutorial');
      console.error(error);
    }
  };

  const handleDeleteTutorial = (tutorialId) => {
    unregisterTutorial(tutorialId);
    setTutorials(getAllTutorials());
    message.success('Tutorial deleted successfully');
  };

  const handleAddStep = () => {
    setEditingStep(null);
    stepForm.resetFields();
    setShowStepModal(true);
  };

  const handleEditStep = (step, index) => {
    setEditingStep({ ...step, index });
    stepForm.setFieldsValue(step);
    setShowStepModal(true);
  };

  const handleSaveStep = (values) => {
    const stepData = createTutorialStep({
      id: editingStep?.id || `step_${Date.now()}`,
      ...values
    });

    if (editingStep && editingStep.index !== undefined) {
      // Update existing step
      const newSteps = [...currentSteps];
      newSteps[editingStep.index] = stepData;
      setCurrentSteps(newSteps);
    } else {
      // Add new step
      setCurrentSteps([...currentSteps, stepData]);
    }

    setShowStepModal(false);
    message.success(`Step ${editingStep ? 'updated' : 'added'} successfully`);
  };

  const handleDeleteStep = (index) => {
    const newSteps = currentSteps.filter((_, i) => i !== index);
    setCurrentSteps(newSteps);
    message.success('Step deleted successfully');
  };

  const handleMoveStep = (fromIndex, toIndex) => {
    const newSteps = [...currentSteps];
    const [movedStep] = newSteps.splice(fromIndex, 1);
    newSteps.splice(toIndex, 0, movedStep);
    setCurrentSteps(newSteps);
  };

  const handleTestTutorial = (tutorial) => {
    startTutorial(tutorial.id);
    message.info('Starting tutorial test...');
  };

  const handleExportTutorials = () => {
    const exportData = {
      tutorials: tutorials,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'tutorials-export.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const tutorialColumns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title, record) => (
        <div>
          <Text strong>{title}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.id}
          </Text>
        </div>
      )
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (category) => (
        <Tag color={
          category === TUTORIAL_CATEGORIES.BEGINNER ? 'green' :
          category === TUTORIAL_CATEGORIES.INTERMEDIATE ? 'blue' :
          category === TUTORIAL_CATEGORIES.ADVANCED ? 'red' : 'purple'
        }>
          {category.replace('_', ' ')}
        </Tag>
      )
    },
    {
      title: 'Steps',
      key: 'steps',
      render: (_, record) => record.steps?.length || 0
    },
    {
      title: 'Difficulty',
      dataIndex: 'difficulty',
      key: 'difficulty',
      render: (difficulty) => '★'.repeat(difficulty) + '☆'.repeat(5 - difficulty)
    },
    {
      title: 'Duration',
      dataIndex: 'estimatedDuration',
      key: 'estimatedDuration',
      render: (duration) => `${duration} min`
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleTestTutorial(record)}
          />
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditTutorial(record)}
          />
          <Popconfirm
            title="Are you sure you want to delete this tutorial?"
            onConfirm={() => handleDeleteTutorial(record.id)}
          >
            <Button
              size="small"
              icon={<DeleteOutlined />}
              danger
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  const stepColumns = [
    {
      title: 'Order',
      key: 'order',
      render: (_, __, index) => index + 1,
      width: 60
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag>{type.replace('_', ' ')}</Tag>
      )
    },
    {
      title: 'Target',
      dataIndex: 'targetSelector',
      key: 'targetSelector',
      render: (target) => target ? <Text code>{target}</Text> : '-'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record, index) => (
        <Space>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditStep(record, index)}
          />
          <Popconfirm
            title="Are you sure you want to delete this step?"
            onConfirm={() => handleDeleteStep(index)}
          >
            <Button
              size="small"
              icon={<DeleteOutlined />}
              danger
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>Tutorial Administration</Title>
        <Space>
          <Button
            icon={<ExportOutlined />}
            onClick={handleExportTutorials}
          >
            Export
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateTutorial}
          >
            Create Tutorial
          </Button>
        </Space>
      </div>

      <Alert
        message="Tutorial Management"
        description="Create and manage interactive tutorials for the App Builder. Use this interface to add new tutorials, edit existing ones, and test tutorial flows."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Card>
        <Table
          dataSource={tutorials}
          columns={tutorialColumns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* Tutorial Creation/Edit Modal */}
      <Modal
        title={editingTutorial ? 'Edit Tutorial' : 'Create Tutorial'}
        open={showTutorialModal}
        onCancel={() => setShowTutorialModal(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveTutorial}
        >
          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please enter tutorial title' }]}
          >
            <Input placeholder="Enter tutorial title" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter tutorial description' }]}
          >
            <TextArea rows={3} placeholder="Enter tutorial description" />
          </Form.Item>

          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select category' }]}
          >
            <Select placeholder="Select category">
              {Object.values(TUTORIAL_CATEGORIES).map(category => (
                <Option key={category} value={category}>
                  {category.replace('_', ' ')}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="difficulty"
            label="Difficulty (1-5 stars)"
            rules={[{ required: true, message: 'Please set difficulty' }]}
          >
            <InputNumber min={1} max={5} />
          </Form.Item>

          <Form.Item
            name="estimatedDuration"
            label="Estimated Duration (minutes)"
            rules={[{ required: true, message: 'Please set duration' }]}
          >
            <InputNumber min={1} />
          </Form.Item>

          <Form.Item
            name="prerequisites"
            label="Prerequisites"
          >
            <Select
              mode="multiple"
              placeholder="Select prerequisite tutorials"
              options={tutorials.map(t => ({ label: t.title, value: t.id }))}
            />
          </Form.Item>

          <Form.Item
            name="tags"
            label="Tags"
          >
            <Select
              mode="tags"
              placeholder="Add tags"
            />
          </Form.Item>

          <Form.Item
            name="isRequired"
            label="Required Tutorial"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Divider>Tutorial Steps</Divider>

          <div style={{ marginBottom: 16 }}>
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={handleAddStep}
              block
            >
              Add Step
            </Button>
          </div>

          <Table
            dataSource={currentSteps}
            columns={stepColumns}
            rowKey="id"
            size="small"
            pagination={false}
          />

          <div style={{ marginTop: 24, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowTutorialModal(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                Save Tutorial
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* Step Creation/Edit Modal */}
      <Modal
        title={editingStep ? 'Edit Step' : 'Add Step'}
        open={showStepModal}
        onCancel={() => setShowStepModal(false)}
        footer={null}
        width={600}
      >
        <Form
          form={stepForm}
          layout="vertical"
          onFinish={handleSaveStep}
        >
          <Form.Item
            name="title"
            label="Step Title"
            rules={[{ required: true, message: 'Please enter step title' }]}
          >
            <Input placeholder="Enter step title" />
          </Form.Item>

          <Form.Item
            name="content"
            label="Content"
            rules={[{ required: true, message: 'Please enter step content' }]}
          >
            <TextArea rows={3} placeholder="Enter step content" />
          </Form.Item>

          <Form.Item
            name="type"
            label="Step Type"
            rules={[{ required: true, message: 'Please select step type' }]}
          >
            <Select placeholder="Select step type">
              {Object.values(TUTORIAL_STEP_TYPES).map(type => (
                <Option key={type} value={type}>
                  {type.replace('_', ' ')}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="targetSelector"
            label="Target Selector (CSS selector)"
          >
            <Input placeholder="e.g., [data-help-context='component-palette']" />
          </Form.Item>

          <Form.Item
            name="position"
            label="Position"
          >
            <Select placeholder="Select position" defaultValue="bottom">
              <Option value="top">Top</Option>
              <Option value="bottom">Bottom</Option>
              <Option value="left">Left</Option>
              <Option value="right">Right</Option>
              <Option value="auto">Auto</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="autoAdvance"
            label="Auto Advance"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="autoAdvanceDelay"
            label="Auto Advance Delay (ms)"
          >
            <InputNumber min={0} defaultValue={3000} />
          </Form.Item>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowStepModal(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                Save Step
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default TutorialAdmin;
