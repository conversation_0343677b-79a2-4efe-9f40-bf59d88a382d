name: Comprehensive Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run nightly tests at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  POSTGRES_VERSION: '14'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Install backend dependencies
        working-directory: ./backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: Run ESLint
        working-directory: ./frontend
        run: npm run lint:ci

      - name: <PERSON> Prettier check
        working-directory: ./frontend
        run: npm run format:check

      - name: Run Python linting (flake8)
        working-directory: ./backend
        run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics

      - name: Run Python security check (bandit)
        working-directory: ./backend
        run: bandit -r . -f json -o bandit-report.json || true

      - name: Run dependency vulnerability check
        working-directory: ./frontend
        run: npm audit --audit-level=high

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            backend/bandit-report.json
            frontend/npm-audit.json

  # Frontend Tests
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-type: [unit, integration, e2e]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Build application
        working-directory: ./frontend
        run: npm run build

      - name: Run unit tests
        if: matrix.test-type == 'unit'
        working-directory: ./frontend
        run: npm run test:unit:ci

      - name: Run integration tests
        if: matrix.test-type == 'integration'
        working-directory: ./frontend
        run: npm run test:integration:ci

      - name: Install Playwright browsers
        if: matrix.test-type == 'e2e'
        working-directory: ./frontend
        run: npx playwright install --with-deps

      - name: Start backend for E2E tests
        if: matrix.test-type == 'e2e'
        working-directory: ./backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          python manage.py migrate
          python manage.py runserver &
          sleep 10
        env:
          DATABASE_URL: sqlite:///test.db
          DJANGO_SETTINGS_MODULE: app_builder.settings.test

      - name: Run E2E tests
        if: matrix.test-type == 'e2e'
        working-directory: ./frontend
        run: npm run test:e2e:ci
        env:
          PLAYWRIGHT_TEST_BASE_URL: http://localhost:3000

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: frontend-test-results-${{ matrix.test-type }}
          path: |
            frontend/coverage/
            frontend/test-results/
            frontend/playwright-report/

  # Backend Tests
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_app_builder
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    strategy:
      matrix:
        test-type: [unit, integration, security]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        working-directory: ./backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: Run database migrations
        working-directory: ./backend
        run: python manage.py migrate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_app_builder
          REDIS_URL: redis://localhost:6379/0

      - name: Run unit tests
        if: matrix.test-type == 'unit'
        working-directory: ./backend
        run: |
          coverage run --source='.' manage.py test tests.test_models tests.test_views
          coverage xml
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_app_builder
          REDIS_URL: redis://localhost:6379/0

      - name: Run integration tests
        if: matrix.test-type == 'integration'
        working-directory: ./backend
        run: |
          coverage run --source='.' manage.py test tests.test_integration_api tests.test_integration_websocket tests.test_integration_database
          coverage xml
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_app_builder
          REDIS_URL: redis://localhost:6379/0

      - name: Run security tests
        if: matrix.test-type == 'security'
        working-directory: ./backend
        run: |
          coverage run --source='.' manage.py test tests.test_security
          coverage xml
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_app_builder
          REDIS_URL: redis://localhost:6379/0

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage.xml
          flags: backend-${{ matrix.test-type }}
          name: backend-${{ matrix.test-type }}-coverage

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: backend-test-results-${{ matrix.test-type }}
          path: |
            backend/coverage.xml
            backend/test-results/

  # Cross-browser Testing
  cross-browser-tests:
    name: Cross-browser Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Install Playwright browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: Run cross-browser tests
        working-directory: ./frontend
        run: npx playwright test --project=${{ matrix.browser }}

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cross-browser-results-${{ matrix.browser }}
          path: |
            frontend/test-results/
            frontend/playwright-report/

  # Performance Testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Build application
        working-directory: ./frontend
        run: npm run build

      - name: Run bundle size analysis
        working-directory: ./frontend
        run: npm run test:bundle-size

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Start application
        working-directory: ./frontend
        run: |
          npm run serve &
          sleep 10

      - name: Run Lighthouse CI
        working-directory: ./frontend
        run: lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-results
          path: |
            frontend/test-results/
            frontend/.lighthouseci/

  # Accessibility Testing
  accessibility-tests:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Install Playwright browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps chromium

      - name: Run accessibility tests
        working-directory: ./frontend
        run: npx playwright test --project=accessibility

      - name: Upload accessibility results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: accessibility-results
          path: |
            frontend/test-results/
            frontend/accessibility-report/

  # Coverage Report
  coverage-report:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v3

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install coverage tools
        run: |
          npm install -g nyc
          pip install coverage

      - name: Merge frontend coverage
        run: |
          mkdir -p merged-coverage/frontend
          find . -name "coverage" -type d -path "*/frontend-test-results-*/*" -exec cp -r {} merged-coverage/frontend/ \;

      - name: Merge backend coverage
        run: |
          mkdir -p merged-coverage/backend
          find . -name "coverage.xml" -path "*/backend-test-results-*/*" -exec cp {} merged-coverage/backend/ \;

      - name: Generate combined coverage report
        run: |
          echo "# Test Coverage Report" > coverage-summary.md
          echo "Generated on: $(date)" >> coverage-summary.md
          echo "" >> coverage-summary.md
          
          if [ -d "merged-coverage/frontend" ]; then
            echo "## Frontend Coverage" >> coverage-summary.md
            echo "Coverage reports available in artifacts" >> coverage-summary.md
          fi
          
          if [ -d "merged-coverage/backend" ]; then
            echo "## Backend Coverage" >> coverage-summary.md
            echo "Coverage reports available in artifacts" >> coverage-summary.md
          fi

      - name: Upload combined coverage
        uses: actions/upload-artifact@v3
        with:
          name: combined-coverage-report
          path: |
            merged-coverage/
            coverage-summary.md

  # Quality Gates
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    needs: [code-quality, frontend-tests, backend-tests, cross-browser-tests, performance-tests, accessibility-tests]
    if: always()
    steps:
      - name: Check test results
        run: |
          echo "Checking quality gates..."
          
          # Check if any job failed
          if [[ "${{ needs.code-quality.result }}" == "failure" ]]; then
            echo "❌ Code quality checks failed"
            exit 1
          fi
          
          if [[ "${{ needs.frontend-tests.result }}" == "failure" ]]; then
            echo "❌ Frontend tests failed"
            exit 1
          fi
          
          if [[ "${{ needs.backend-tests.result }}" == "failure" ]]; then
            echo "❌ Backend tests failed"
            exit 1
          fi
          
          if [[ "${{ needs.cross-browser-tests.result }}" == "failure" ]]; then
            echo "⚠️ Cross-browser tests failed (warning)"
          fi
          
          if [[ "${{ needs.performance-tests.result }}" == "failure" ]]; then
            echo "⚠️ Performance tests failed (warning)"
          fi
          
          if [[ "${{ needs.accessibility-tests.result }}" == "failure" ]]; then
            echo "⚠️ Accessibility tests failed (warning)"
          fi
          
          echo "✅ Quality gates passed"

      - name: Post results to PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const results = {
              codeQuality: '${{ needs.code-quality.result }}',
              frontendTests: '${{ needs.frontend-tests.result }}',
              backendTests: '${{ needs.backend-tests.result }}',
              crossBrowser: '${{ needs.cross-browser-tests.result }}',
              performance: '${{ needs.performance-tests.result }}',
              accessibility: '${{ needs.accessibility-tests.result }}'
            };
            
            let comment = '## 🧪 Test Results\n\n';
            
            for (const [test, result] of Object.entries(results)) {
              const emoji = result === 'success' ? '✅' : result === 'failure' ? '❌' : '⚠️';
              comment += `${emoji} ${test}: ${result}\n`;
            }
            
            comment += '\n📊 Detailed reports are available in the workflow artifacts.';
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # Deployment (only on main branch)
  deploy:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quality-gates]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          # Add your deployment steps here
          echo "✅ Deployment completed"
