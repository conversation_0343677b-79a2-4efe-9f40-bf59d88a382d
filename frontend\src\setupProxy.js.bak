const { createProxyMiddleware } = require('http-proxy-middleware');

const backendUrl = process.env.REACT_APP_BACKEND_HOST
  ? `http://${process.env.REACT_APP_BACKEND_HOST}:8000`
  : 'http://localhost:8000';

module.exports = function (app) {
  console.log('🔧 Setting up proxy middleware for real backend connections');
  console.log('🌐 Backend URL:', backendUrl);
  console.log('🔌 WebSocket proxy will route /ws to backend WebSocket server');

  // API proxy for regular HTTP requests
  app.use(
    '/api',
    createProxyMiddleware({
      target: backendUrl,
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      onProxyReq: (proxyReq, req, res) => {
        // Log proxy requests for debugging
        console.log(`Proxying API request: ${req.method} ${req.url} -> ${backendUrl}${req.url}`);
      },
      onError: (err, req, res) => {
        console.error('API proxy error:', err);
        if (res && !res.headersSent) {
          res.writeHead(502, {
            'Content-Type': 'application/json',
          });
          res.end(JSON.stringify({
            error: 'Backend service unavailable',
            message: 'The backend service is currently unavailable. Please try again later.'
          }));
        }
      }
    })
  );

  // Enable WebSocket proxy for development - this routes to real backend WebSocket server
  console.log('🔌 WebSocket proxy enabled. Proxying /ws to', backendUrl);
  app.use(
    '/ws',
    createProxyMiddleware({
      target: backendUrl,
      changeOrigin: true,
      ws: true, // Enable WebSocket proxying
      logLevel: 'debug',
      secure: false,
      onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying WebSocket request: ${req.url} -> ${backendUrl}${req.url}`);
      },
      onError: (err, _req, res) => {
        console.error('WebSocket proxy error:', err);
        if (res && res.writeHead) {
          res.writeHead(502, {
            'Content-Type': 'text/plain',
          });
          res.end(`WebSocket proxy error: ${err.message}`);
        }
      }
    })
  );
};

