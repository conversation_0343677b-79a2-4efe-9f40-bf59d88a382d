import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Dropdown, 
  <PERSON>u, 
  Modal, 
  Input,
  message,
  Space,
  Divider,
  Switch
} from 'antd';
import { 
  TeamOutlined, 
  CommentOutlined, 
  ShareAltOutlined, 
  SettingOutlined,
  WifiOutlined,
  DisconnectOutlined,
  UserAddOutlined,
  LinkOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useCollaboration } from '../../contexts/CollaborationContext';
import { useAuth } from '../../contexts/AuthContext';
import { CollaboratorsBar } from './UserPresence';

// Styled components
const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  background: ${props => {
    switch (props.status) {
      case 'connected': return '#f6ffed';
      case 'connecting': return '#fff7e6';
      case 'disconnected': return '#fff2f0';
      default: return '#f5f5f5';
    }
  }};
  border: 1px solid ${props => {
    switch (props.status) {
      case 'connected': return '#b7eb8f';
      case 'connecting': return '#ffd591';
      case 'disconnected': return '#ffb3b3';
      default: return '#d9d9d9';
    }
  }};
  font-size: 12px;
  color: ${props => {
    switch (props.status) {
      case 'connected': return '#52c41a';
      case 'connecting': return '#fa8c16';
      case 'disconnected': return '#ff4d4f';
      default: return '#666';
    }
  }};
`;

const SessionInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;

const SessionName = styled.div`
  font-weight: 500;
  font-size: 14px;
`;

const SessionDetails = styled.div`
  font-size: 12px;
  color: #666;
`;

// Share session modal
const ShareSessionModal = ({ visible, onClose, sessionId }) => {
  const [inviteLink, setInviteLink] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const generateInviteLink = async () => {
    setIsGenerating(true);
    try {
      // In a real app, this would call an API to generate a secure invite link
      const link = `${window.location.origin}/collaborate/${sessionId}?invite=true`;
      setInviteLink(link);
      message.success('Invite link generated');
    } catch (error) {
      message.error('Failed to generate invite link');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(inviteLink);
    message.success('Link copied to clipboard');
  };

  return (
    <Modal
      title="Share Collaboration Session"
      visible={visible}
      onCancel={onClose}
      footer={null}
      width={500}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <div>
          <p>Invite others to collaborate on this project by sharing the link below:</p>
          
          <Space.Compact style={{ width: '100%' }}>
            <Input
              value={inviteLink}
              placeholder="Click 'Generate Link' to create an invite link"
              readOnly
            />
            <Button 
              icon={<LinkOutlined />}
              onClick={generateInviteLink}
              loading={isGenerating}
            >
              Generate Link
            </Button>
            <Button 
              icon={<LinkOutlined />}
              onClick={copyToClipboard}
              disabled={!inviteLink}
            >
              Copy
            </Button>
          </Space.Compact>
        </div>

        <Divider />

        <div>
          <h4>Collaboration Settings</h4>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>Allow anonymous users</span>
              <Switch size="small" />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>Require approval to join</span>
              <Switch size="small" />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>Show cursors and selections</span>
              <Switch size="small" defaultChecked />
            </div>
          </Space>
        </div>
      </Space>
    </Modal>
  );
};

// Main collaboration toolbar component
export const CollaborationToolbar = ({ 
  showComments = true,
  showShare = true,
  showSettings = true,
  onToggleComments,
  onTogglePresence,
  commentsVisible = false,
  presenceVisible = true
}) => {
  const { 
    currentSession, 
    isConnected, 
    isJoining, 
    activeParticipants, 
    comments,
    leaveSession 
  } = useCollaboration();
  
  const { user } = useAuth();
  const [shareModalVisible, setShareModalVisible] = useState(false);

  const getConnectionStatus = () => {
    if (isJoining) return 'connecting';
    if (isConnected) return 'connected';
    return 'disconnected';
  };

  const getStatusText = () => {
    if (isJoining) return 'Connecting...';
    if (isConnected) return 'Connected';
    return 'Disconnected';
  };

  const getStatusIcon = () => {
    if (isJoining) return <WifiOutlined spin />;
    if (isConnected) return <WifiOutlined />;
    return <DisconnectOutlined />;
  };

  const handleLeaveSession = () => {
    Modal.confirm({
      title: 'Leave Collaboration Session',
      content: 'Are you sure you want to leave this collaboration session?',
      onOk: () => {
        leaveSession();
        message.info('Left collaboration session');
      }
    });
  };

  const settingsMenu = (
    <Menu>
      <Menu.Item key="presence" onClick={onTogglePresence}>
        {presenceVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
        {presenceVisible ? 'Hide' : 'Show'} User Presence
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="leave" onClick={handleLeaveSession} danger>
        <DisconnectOutlined />
        Leave Session
      </Menu.Item>
    </Menu>
  );

  // Don't render if not in a collaboration session
  if (!currentSession) {
    return null;
  }

  const unreadCommentsCount = comments.filter(c => c.status === 'open').length;

  return (
    <ToolbarContainer>
      <LeftSection>
        {/* Session info */}
        <SessionInfo>
          <SessionName>{currentSession.name}</SessionName>
          <SessionDetails>
            {activeParticipants.length} collaborator{activeParticipants.length !== 1 ? 's' : ''}
          </SessionDetails>
        </SessionInfo>

        {/* Connection status */}
        <StatusIndicator status={getConnectionStatus()}>
          {getStatusIcon()}
          {getStatusText()}
        </StatusIndicator>

        {/* Active collaborators */}
        <CollaboratorsBar maxVisible={4} size="small" />
      </LeftSection>

      <RightSection>
        {/* Comments toggle */}
        {showComments && (
          <Tooltip title={commentsVisible ? 'Hide Comments' : 'Show Comments'}>
            <Badge count={unreadCommentsCount} size="small">
              <Button
                type={commentsVisible ? 'primary' : 'default'}
                icon={<CommentOutlined />}
                onClick={onToggleComments}
                size="small"
              >
                Comments
              </Button>
            </Badge>
          </Tooltip>
        )}

        {/* Share session */}
        {showShare && (
          <Tooltip title="Share Session">
            <Button
              icon={<ShareAltOutlined />}
              onClick={() => setShareModalVisible(true)}
              size="small"
            >
              Share
            </Button>
          </Tooltip>
        )}

        {/* Settings */}
        {showSettings && (
          <Dropdown overlay={settingsMenu} trigger={['click']}>
            <Button
              icon={<SettingOutlined />}
              size="small"
            />
          </Dropdown>
        )}
      </RightSection>

      {/* Share modal */}
      <ShareSessionModal
        visible={shareModalVisible}
        onClose={() => setShareModalVisible(false)}
        sessionId={currentSession?.id}
      />
    </ToolbarContainer>
  );
};

// Compact version for smaller spaces
export const CompactCollaborationToolbar = ({ onToggleComments, commentsVisible }) => {
  const { isConnected, activeParticipants, comments } = useCollaboration();
  
  if (!isConnected) return null;

  const unreadCommentsCount = comments.filter(c => c.status === 'open').length;

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: 8,
      padding: '4px 8px',
      background: 'rgba(255, 255, 255, 0.9)',
      borderRadius: 6,
      backdropFilter: 'blur(4px)'
    }}>
      <CollaboratorsBar maxVisible={3} size="small" />
      
      <Badge count={unreadCommentsCount} size="small">
        <Button
          type={commentsVisible ? 'primary' : 'default'}
          icon={<CommentOutlined />}
          onClick={onToggleComments}
          size="small"
        />
      </Badge>
    </div>
  );
};

export default CollaborationToolbar;
