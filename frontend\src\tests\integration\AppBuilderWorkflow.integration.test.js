import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import main components
import IntegratedAppBuilder from '../../components/builder/IntegratedAppBuilder';

// Mock external services
jest.mock('../../services/apiService', () => ({
  saveApp: jest.fn(() => Promise.resolve({ id: 'app-123', name: 'Test App' })),
  loadApp: jest.fn(() => Promise.resolve({ 
    id: 'app-123', 
    name: 'Test App',
    components: [
      { id: '1', type: 'button', props: { text: 'Test Button' } }
    ]
  })),
  getTemplates: jest.fn(() => Promise.resolve([
    { id: 'template-1', name: 'Basic Layout', components: {} }
  ])),
}));

jest.mock('../../services/aiDesignService', () => ({
  generateSuggestions: jest.fn(() => Promise.resolve([
    { id: 'suggestion-1', type: 'layout', title: 'Add Container' }
  ])),
  applySuggestion: jest.fn(() => Promise.resolve(true)),
}));

// Mock WebSocket service
const mockWebSocketService = {
  connect: jest.fn(),
  disconnect: jest.fn(),
  send: jest.fn(),
  onMessage: jest.fn(),
  onConnect: jest.fn(),
  onDisconnect: jest.fn(),
  isConnected: jest.fn(() => true),
};

jest.mock('../../services/webSocketService', () => ({
  default: mockWebSocketService,
}));

// Create comprehensive store
const createIntegrationStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: (state = {
        components: [],
        selectedComponent: null,
        layouts: [],
        styles: {},
        history: [],
        currentProject: null,
        ...initialState.app
      }, action) => {
        switch (action.type) {
          case 'app/addComponent':
            return {
              ...state,
              components: [...state.components, action.payload],
              history: [...state.history, { type: 'add', component: action.payload }]
            };
          case 'app/selectComponent':
            return { ...state, selectedComponent: action.payload };
          case 'app/updateComponent':
            return {
              ...state,
              components: state.components.map(comp =>
                comp.id === action.payload.id ? { ...comp, ...action.payload } : comp
              )
            };
          case 'app/deleteComponent':
            return {
              ...state,
              components: state.components.filter(comp => comp.id !== action.payload)
            };
          default:
            return state;
        }
      },
      ui: (state = {
        sidebarOpen: true,
        currentView: 'components',
        previewMode: false,
        loading: false,
        ...initialState.ui
      }, action) => {
        switch (action.type) {
          case 'ui/toggleSidebar':
            return { ...state, sidebarOpen: !state.sidebarOpen };
          case 'ui/setCurrentView':
            return { ...state, currentView: action.payload };
          case 'ui/togglePreviewMode':
            return { ...state, previewMode: !state.previewMode };
          default:
            return state;
        }
      },
      websocket: (state = {
        connected: false,
        connecting: false,
        messages: [],
        ...initialState.websocket
      }, action) => {
        switch (action.type) {
          case 'websocket/connect':
            return { ...state, connected: true, connecting: false };
          case 'websocket/disconnect':
            return { ...state, connected: false, connecting: false };
          case 'websocket/addMessage':
            return { ...state, messages: [...state.messages, action.payload] };
          default:
            return state;
        }
      },
      ai: (state = {
        suggestions: [],
        loading: false,
        history: [],
        ...initialState.ai
      }, action) => {
        switch (action.type) {
          case 'ai/setSuggestions':
            return { ...state, suggestions: action.payload };
          case 'ai/setLoading':
            return { ...state, loading: action.payload };
          default:
            return state;
        }
      },
      templates: (state = {
        items: [],
        loading: false,
        ...initialState.templates
      }, action) => {
        switch (action.type) {
          case 'templates/setTemplates':
            return { ...state, items: action.payload };
          default:
            return state;
        }
      },
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
};

// Test wrapper
const IntegrationTestWrapper = ({ children, store = createIntegrationStore() }) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('App Builder Integration Tests', () => {
  let user;
  let store;

  beforeEach(() => {
    user = userEvent.setup();
    store = createIntegrationStore();
    jest.clearAllMocks();
  });

  describe('Complete App Building Workflow', () => {
    test('creates app from scratch to completion', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder 
            enableFeatures={{
              websocket: true,
              aiSuggestions: true,
              templates: true,
              codeExport: true,
            }}
          />
        </IntegrationTestWrapper>
      );

      // 1. Start with empty canvas
      expect(store.getState().app.components).toHaveLength(0);

      // 2. Add first component (simulate drag and drop)
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '1', type: 'button', props: { text: 'My Button' } }
      });

      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(1);
      });

      // 3. Select the component
      store.dispatch({
        type: 'app/selectComponent',
        payload: '1'
      });

      expect(store.getState().app.selectedComponent).toBe('1');

      // 4. Modify component properties
      store.dispatch({
        type: 'app/updateComponent',
        payload: { id: '1', props: { text: 'Updated Button', color: 'blue' } }
      });

      const updatedComponent = store.getState().app.components.find(c => c.id === '1');
      expect(updatedComponent.props.text).toBe('Updated Button');
      expect(updatedComponent.props.color).toBe('blue');

      // 5. Add more components
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '2', type: 'input', props: { placeholder: 'Enter text' } }
      });

      expect(store.getState().app.components).toHaveLength(2);
    });

    test('handles undo/redo operations', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder />
        </IntegrationTestWrapper>
      );

      // Add component
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '1', type: 'button', props: { text: 'Button' } }
      });

      expect(store.getState().app.components).toHaveLength(1);
      expect(store.getState().app.history).toHaveLength(1);

      // In a real implementation, undo/redo would be handled
      // by the history middleware or reducer
    });
  });

  describe('WebSocket Integration', () => {
    test('establishes WebSocket connection on mount', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ websocket: true }} />
        </IntegrationTestWrapper>
      );

      // WebSocket should attempt to connect
      await waitFor(() => {
        expect(mockWebSocketService.connect).toHaveBeenCalled();
      });
    });

    test('handles real-time collaboration messages', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ websocket: true }} />
        </IntegrationTestWrapper>
      );

      // Simulate receiving a collaboration message
      const collaborationMessage = {
        type: 'component_update',
        componentId: '1',
        changes: { props: { text: 'Updated by collaborator' } },
        userId: 'other-user'
      };

      store.dispatch({
        type: 'websocket/addMessage',
        payload: collaborationMessage
      });

      expect(store.getState().websocket.messages).toContainEqual(collaborationMessage);
    });

    test('synchronizes component changes via WebSocket', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ websocket: true }} />
        </IntegrationTestWrapper>
      );

      // Add component locally
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '1', type: 'button', props: { text: 'Button' } }
      });

      // In a real implementation, this would trigger a WebSocket message
      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(1);
      });
    });
  });

  describe('AI Integration Workflow', () => {
    test('generates and applies AI suggestions', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ aiSuggestions: true }} />
        </IntegrationTestWrapper>
      );

      // Add some components to trigger AI suggestions
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '1', type: 'button', props: { text: 'Button' } }
      });

      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '2', type: 'input', props: { placeholder: 'Input' } }
      });

      // Simulate AI suggestions being generated
      const suggestions = [
        { id: 'suggestion-1', type: 'layout', title: 'Add Container' },
        { id: 'suggestion-2', type: 'styling', title: 'Improve Spacing' }
      ];

      store.dispatch({
        type: 'ai/setSuggestions',
        payload: suggestions
      });

      expect(store.getState().ai.suggestions).toHaveLength(2);

      // Apply a suggestion
      const appliedSuggestion = suggestions[0];
      // In a real implementation, applying would modify the app state
      expect(appliedSuggestion.type).toBe('layout');
    });

    test('handles AI suggestion errors gracefully', async () => {
      const aiService = require('../../services/aiDesignService');
      aiService.generateSuggestions.mockRejectedValueOnce(new Error('AI service unavailable'));

      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ aiSuggestions: true }} />
        </IntegrationTestWrapper>
      );

      // Error should be handled gracefully
      expect(store.getState().ai.suggestions).toEqual([]);
    });
  });

  describe('Template System Integration', () => {
    test('loads and applies templates', async () => {
      const templates = [
        {
          id: 'template-1',
          name: 'Basic Layout',
          components: {
            header: { type: 'header', props: { title: 'My App' } },
            content: { type: 'content', props: {} }
          }
        }
      ];

      store.dispatch({
        type: 'templates/setTemplates',
        payload: templates
      });

      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ templates: true }} />
        </IntegrationTestWrapper>
      );

      expect(store.getState().templates.items).toHaveLength(1);

      // Apply template
      const template = templates[0];
      const templateComponents = Object.values(template.components).map((comp, index) => ({
        ...comp,
        id: `template-comp-${index}`
      }));

      templateComponents.forEach(component => {
        store.dispatch({
          type: 'app/addComponent',
          payload: component
        });
      });

      expect(store.getState().app.components).toHaveLength(2);
    });

    test('saves current app as template', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ templates: true }} />
        </IntegrationTestWrapper>
      );

      // Create an app with components
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '1', type: 'button', props: { text: 'Button' } }
      });

      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '2', type: 'input', props: { placeholder: 'Input' } }
      });

      // Save as template (in real implementation)
      const currentComponents = store.getState().app.components;
      expect(currentComponents).toHaveLength(2);

      // Template would be created from current state
      const newTemplate = {
        id: 'new-template',
        name: 'My Custom Template',
        components: currentComponents.reduce((acc, comp) => {
          acc[comp.id] = comp;
          return acc;
        }, {})
      };

      expect(Object.keys(newTemplate.components)).toHaveLength(2);
    });
  });

  describe('State Management Integration', () => {
    test('maintains consistent state across actions', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder />
        </IntegrationTestWrapper>
      );

      // Perform multiple state changes
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '1', type: 'button', props: { text: 'Button 1' } }
      });

      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '2', type: 'input', props: { placeholder: 'Input 1' } }
      });

      store.dispatch({
        type: 'app/selectComponent',
        payload: '1'
      });

      store.dispatch({
        type: 'ui/setCurrentView',
        payload: 'properties'
      });

      // Verify state consistency
      const state = store.getState();
      expect(state.app.components).toHaveLength(2);
      expect(state.app.selectedComponent).toBe('1');
      expect(state.ui.currentView).toBe('properties');
    });

    test('handles concurrent state updates', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder />
        </IntegrationTestWrapper>
      );

      // Simulate concurrent updates
      const actions = [
        { type: 'app/addComponent', payload: { id: '1', type: 'button' } },
        { type: 'app/addComponent', payload: { id: '2', type: 'input' } },
        { type: 'app/selectComponent', payload: '1' },
        { type: 'ui/toggleSidebar' },
        { type: 'ui/setCurrentView', payload: 'preview' },
      ];

      actions.forEach(action => store.dispatch(action));

      const finalState = store.getState();
      expect(finalState.app.components).toHaveLength(2);
      expect(finalState.app.selectedComponent).toBe('1');
      expect(finalState.ui.currentView).toBe('preview');
    });
  });

  describe('Error Handling Integration', () => {
    test('handles API errors gracefully', async () => {
      const apiService = require('../../services/apiService');
      apiService.saveApp.mockRejectedValueOnce(new Error('Network error'));

      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder />
        </IntegrationTestWrapper>
      );

      // Add component and attempt to save
      store.dispatch({
        type: 'app/addComponent',
        payload: { id: '1', type: 'button', props: { text: 'Button' } }
      });

      // Error should be handled gracefully
      expect(store.getState().app.components).toHaveLength(1);
    });

    test('recovers from WebSocket disconnection', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder enableFeatures={{ websocket: true }} />
        </IntegrationTestWrapper>
      );

      // Simulate connection
      store.dispatch({ type: 'websocket/connect' });
      expect(store.getState().websocket.connected).toBe(true);

      // Simulate disconnection
      store.dispatch({ type: 'websocket/disconnect' });
      expect(store.getState().websocket.connected).toBe(false);

      // Should attempt to reconnect
      await waitFor(() => {
        expect(mockWebSocketService.connect).toHaveBeenCalled();
      });
    });
  });

  describe('Performance Integration', () => {
    test('handles large number of components efficiently', async () => {
      render(
        <IntegrationTestWrapper store={store}>
          <IntegratedAppBuilder />
        </IntegrationTestWrapper>
      );

      const startTime = performance.now();

      // Add many components
      for (let i = 0; i < 100; i++) {
        store.dispatch({
          type: 'app/addComponent',
          payload: { id: `comp-${i}`, type: 'button', props: { text: `Button ${i}` } }
        });
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(store.getState().app.components).toHaveLength(100);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});
