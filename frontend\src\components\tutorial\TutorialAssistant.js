import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, But<PERSON>, Steps, Progress, Typography, Space, Tooltip, FloatButton } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StepForwardOutlined, 
  StepBackwardOutlined,
  CloseOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import TutorialOverlay from './TutorialOverlay';
import TutorialProgress from './TutorialProgress';
import ContextualHelp from './ContextualHelp';

const { Title, Text } = Typography;
const { Step } = Steps;

/**
 * Tutorial Assistant
 * 
 * Main tutorial interface component that provides guided tutorials
 * with step-by-step instructions, progress tracking, and contextual help.
 */

const TutorialContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 80vh;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  
  @media (max-width: 768px) {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    max-height: 70vh;
  }
`;

const TutorialHeader = styled.div`
  padding: 16px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  
  .tutorial-title {
    color: white;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
  
  .tutorial-subtitle {
    color: rgba(255, 255, 255, 0.8);
    margin: 4px 0 0 0;
    font-size: 12px;
  }
`;

const TutorialContent = styled.div`
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
`;

const TutorialControls = styled.div`
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StepContent = styled.div`
  margin: 16px 0;
  
  .step-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #262626;
  }
  
  .step-description {
    color: #595959;
    line-height: 1.6;
    margin-bottom: 12px;
  }
  
  .step-actions {
    margin-top: 12px;
  }
`;

const TutorialAssistant = ({
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSkip,
  onComplete,
  onPause,
  onResume,
  compact = false
}) => {
  const {
    activeTutorial,
    isActive,
    isPaused,
    currentStepIndex,
    nextStep,
    previousStep,
    pauseTutorial,
    resumeTutorial,
    skipTutorial,
    completeTutorial
  } = useTutorial();

  const [showOverlay, setShowOverlay] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  // Use tutorial context if available, otherwise use props
  const tutorial = activeTutorial;
  const step = activeTutorial?.steps[currentStepIndex] || currentStep;
  const stepIndex = currentStepIndex;
  const total = activeTutorial?.steps.length || totalSteps;
  const active = isActive;
  const paused = isPaused;

  // Handle step navigation
  const handleNext = useCallback(() => {
    if (onNext) {
      onNext();
    } else {
      nextStep();
    }
  }, [onNext, nextStep]);

  const handlePrevious = useCallback(() => {
    if (onPrevious) {
      onPrevious();
    } else {
      previousStep();
    }
  }, [onPrevious, previousStep]);

  const handlePause = useCallback(() => {
    if (onPause) {
      onPause();
    } else {
      pauseTutorial();
    }
  }, [onPause, pauseTutorial]);

  const handleResume = useCallback(() => {
    if (onResume) {
      onResume();
    } else {
      resumeTutorial();
    }
  }, [onResume, resumeTutorial]);

  const handleSkip = useCallback(() => {
    if (onSkip) {
      onSkip();
    } else {
      skipTutorial();
    }
  }, [onSkip, skipTutorial]);

  const handleComplete = useCallback(() => {
    if (onComplete) {
      onComplete();
    } else {
      completeTutorial();
    }
  }, [onComplete, completeTutorial]);

  // Show overlay for current step if it has overlay content
  useEffect(() => {
    if (step && step.overlay) {
      setShowOverlay(true);
    } else {
      setShowOverlay(false);
    }
  }, [step]);

  // Don't render if no active tutorial
  if (!active || !step) {
    return (
      <>
        {/* Floating help button */}
        <FloatButton
          icon={<QuestionCircleOutlined />}
          tooltip="Tutorial Help"
          onClick={() => setShowHelp(!showHelp)}
          style={{ right: 24, bottom: 24 }}
        />
        
        {showHelp && <ContextualHelp />}
      </>
    );
  }

  const progress = total > 0 ? ((stepIndex + 1) / total) * 100 : 0;
  const isLastStep = stepIndex >= total - 1;
  const isFirstStep = stepIndex <= 0;

  if (compact) {
    return (
      <>
        <Card
          size="small"
          style={{ width: 280, position: 'fixed', top: 20, right: 20, zIndex: 1000 }}
          title={
            <Space>
              <Text strong>{tutorial?.title || 'Tutorial'}</Text>
              <Text type="secondary">({stepIndex + 1}/{total})</Text>
            </Space>
          }
          extra={
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={handleSkip}
            />
          }
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Progress percent={progress} size="small" />
            <Text>{step.title}</Text>
            <Space>
              <Button
                size="small"
                disabled={isFirstStep}
                onClick={handlePrevious}
              >
                Previous
              </Button>
              <Button
                type="primary"
                size="small"
                onClick={isLastStep ? handleComplete : handleNext}
              >
                {isLastStep ? 'Complete' : 'Next'}
              </Button>
            </Space>
          </Space>
        </Card>
        
        {showOverlay && (
          <TutorialOverlay
            step={step}
            onNext={handleNext}
            onPrevious={handlePrevious}
            onSkip={handleSkip}
          />
        )}
      </>
    );
  }

  return (
    <>
      <TutorialContainer>
        <TutorialHeader>
          <Title level={4} className="tutorial-title">
            {tutorial?.title || 'Tutorial'}
          </Title>
          <Text className="tutorial-subtitle">
            Step {stepIndex + 1} of {total} • {tutorial?.category || 'General'}
          </Text>
        </TutorialHeader>

        <TutorialContent>
          <TutorialProgress
            current={stepIndex}
            total={total}
            percent={progress}
          />

          <StepContent>
            <div className="step-title">{step.title}</div>
            <div className="step-description">{step.description}</div>
            
            {step.tips && (
              <div style={{ marginTop: 12 }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  💡 Tip: {step.tips}
                </Text>
              </div>
            )}
          </StepContent>

          {tutorial?.steps && (
            <Steps
              current={stepIndex}
              direction="vertical"
              size="small"
              style={{ marginTop: 16 }}
            >
              {tutorial.steps.map((tutorialStep, index) => (
                <Step
                  key={index}
                  title={tutorialStep.title}
                  description={index === stepIndex ? tutorialStep.description : null}
                  icon={index < stepIndex ? <CheckCircleOutlined /> : undefined}
                />
              ))}
            </Steps>
          )}
        </TutorialContent>

        <TutorialControls>
          <Space>
            <Tooltip title={paused ? "Resume" : "Pause"}>
              <Button
                type="text"
                icon={paused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                onClick={paused ? handleResume : handlePause}
              />
            </Tooltip>
            
            <Tooltip title="Skip Tutorial">
              <Button
                type="text"
                icon={<CloseOutlined />}
                onClick={handleSkip}
              />
            </Tooltip>
          </Space>

          <Space>
            <Button
              icon={<StepBackwardOutlined />}
              disabled={isFirstStep}
              onClick={handlePrevious}
            >
              Previous
            </Button>
            
            <Button
              type="primary"
              icon={isLastStep ? <CheckCircleOutlined /> : <StepForwardOutlined />}
              onClick={isLastStep ? handleComplete : handleNext}
            >
              {isLastStep ? 'Complete' : 'Next'}
            </Button>
          </Space>
        </TutorialControls>
      </TutorialContainer>

      {/* Tutorial Overlay for highlighting elements */}
      {showOverlay && (
        <TutorialOverlay
          step={step}
          onNext={handleNext}
          onPrevious={handlePrevious}
          onSkip={handleSkip}
          onComplete={isLastStep ? handleComplete : undefined}
        />
      )}

      {/* Contextual Help */}
      {showHelp && <ContextualHelp />}
    </>
  );
};

export default TutorialAssistant;
