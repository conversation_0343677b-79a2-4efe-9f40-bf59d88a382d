/**
 * Real-time Collaboration Service
 * 
 * Handles real-time collaborative features for the App Builder
 */

import EnhancedWebSocketClient from './EnhancedWebSocketClient';
import { getWebSocketUrl } from '../utils/websocket';

class CollaborationService {
  constructor() {
    this.wsClient = null;
    this.isConnected = false;
    this.currentDocument = null;
    this.collaborators = new Map();
    this.eventListeners = new Map();
    this.operationQueue = [];
    this.isProcessingOperations = false;
    this.debug = process.env.NODE_ENV === 'development';
  }

  /**
   * Initialize collaboration for a session
   * @param {string} sessionId - The collaboration session ID
   * @param {Object} user - Current user information
   */
  async initializeCollaboration(sessionId, user) {
    try {
      this.currentDocument = sessionId;

      // Create WebSocket connection for collaboration
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/collaboration/${sessionId}/`;

      this.wsClient = new EnhancedWebSocketClient({
        url: wsUrl,
        autoConnect: true,
        autoReconnect: true,
        debug: this.debug,
        heartbeatInterval: 30000
      });

      // Set up event listeners
      this._setupEventListeners();

      // Join the collaboration session
      await this._joinSession(sessionId, user);

      this.isConnected = true;
      this._log('Collaboration initialized for session:', sessionId);

    } catch (error) {
      this._error('Failed to initialize collaboration:', error);
      throw error;
    }
  }

  /**
   * Join a collaboration session
   * @param {string} sessionId - Session ID
   * @param {Object} user - User information
   */
  async _joinSession(sessionId, user) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Join session timeout'));
      }, 5000);

      // Listen for join confirmation
      const handleJoinResponse = (data) => {
        if (data.type === 'session_joined' && data.session_id === sessionId) {
          clearTimeout(timeout);
          this.removeEventListener('message', handleJoinResponse);
          this.collaborators = new Map((data.data.participants || []).map(p => [p.user_id, p]));
          resolve(data);
        }
      };

      this.addEventListener('message', handleJoinResponse);

      // Send join request
      this.wsClient.send({
        type: 'join_session',
        session_id: sessionId
      });
    });
  }

  /**
   * Leave the current collaboration session
   */
  async leaveSession() {
    if (this.currentDocument && this.wsClient) {
      this.wsClient.send({
        type: 'leave_session'
      });
    }

    this.currentDocument = null;
    this.collaborators.clear();
    this.operationQueue = [];
  }

  /**
   * Send a real-time operation (like text changes, component updates)
   * @param {Object} operation - The operation to send
   */
  sendOperation(operation) {
    if (!this.isConnected || !this.currentDocument) {
      this._log('Cannot send operation: not connected or no document');
      return;
    }

    const operationData = {
      type: 'operation',
      documentId: this.currentDocument,
      operation: {
        ...operation,
        timestamp: Date.now(),
        id: this._generateOperationId()
      }
    };

    this.wsClient.send(operationData);
    this._log('Operation sent:', operationData);
  }

  /**
   * Send cursor position update
   * @param {Object} cursor - Cursor position data
   */
  sendCursorUpdate(cursor) {
    if (!this.isConnected || !this.currentDocument) return;

    this.wsClient.send({
      type: 'cursor_update',
      documentId: this.currentDocument,
      cursor: {
        ...cursor,
        timestamp: Date.now()
      }
    });
  }

  /**
   * Send user presence update
   * @param {Object} presence - Presence data
   */
  sendPresenceUpdate(presence) {
    if (!this.isConnected || !this.currentDocument) return;

    this.wsClient.send({
      type: 'presence_update',
      presence: {
        ...presence,
        timestamp: Date.now()
      }
    });
  }

  /**
   * Create a comment
   * @param {string} content - Comment content
   * @param {string} componentId - Component ID (optional)
   * @param {Object} canvasPosition - Canvas position (optional)
   * @param {string} parentId - Parent comment ID for replies (optional)
   */
  createComment(content, componentId = null, canvasPosition = null, parentId = null) {
    if (!this.isConnected || !this.currentDocument) {
      throw new Error('Not connected to collaboration session');
    }

    this.wsClient.send({
      type: 'create_comment',
      content,
      component_id: componentId,
      canvas_position: canvasPosition,
      parent_id: parentId
    });
  }

  /**
   * Update a comment
   * @param {string} commentId - Comment ID
   * @param {string} content - New content
   */
  updateComment(commentId, content) {
    if (!this.isConnected || !this.currentDocument) {
      throw new Error('Not connected to collaboration session');
    }

    this.wsClient.send({
      type: 'update_comment',
      comment_id: commentId,
      content
    });
  }

  /**
   * Resolve a comment
   * @param {string} commentId - Comment ID
   */
  resolveComment(commentId) {
    if (!this.isConnected || !this.currentDocument) {
      throw new Error('Not connected to collaboration session');
    }

    this.wsClient.send({
      type: 'resolve_comment',
      comment_id: commentId
    });
  }

  /**
   * Send a component operation
   * @param {string} operationType - Type of operation
   * @param {string} targetId - Target component ID
   * @param {Object} operationData - Operation data
   */
  sendComponentOperation(operationType, targetId, operationData) {
    if (!this.isConnected || !this.currentDocument) {
      throw new Error('Not connected to collaboration session');
    }

    this.wsClient.send({
      type: 'component_operation',
      operation_type: operationType,
      target_id: targetId,
      operation_data: operationData,
      timestamp: Date.now()
    });
  }

  /**
   * Get list of current collaborators
   * @returns {Array} List of collaborators
   */
  getCollaborators() {
    return Array.from(this.collaborators.values());
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event).add(callback);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).delete(callback);
    }
  }

  /**
   * Emit event to listeners
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  _emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          this._error('Error in event listener:', error);
        }
      });
    }
  }

  /**
   * Set up WebSocket event listeners
   */
  _setupEventListeners() {
    this.wsClient.addEventListener('message', (data) => {
      this._handleMessage(data);
    });

    this.wsClient.addEventListener('open', () => {
      this._log('Collaboration WebSocket connected');
      this._emit('connected');
    });

    this.wsClient.addEventListener('close', () => {
      this._log('Collaboration WebSocket disconnected');
      this.isConnected = false;
      this._emit('disconnected');
    });

    this.wsClient.addEventListener('error', (error) => {
      this._error('Collaboration WebSocket error:', error);
      this._emit('error', error);
    });
  }

  /**
   * Handle incoming WebSocket messages
   * @param {Object} data - Message data
   */
  _handleMessage(data) {
    this._log('Received message:', data);

    switch (data.type) {
      case 'operation':
      case 'component_operation':
        this._handleOperation(data.operation || data);
        break;

      case 'cursor_position':
        this._handleCursorUpdate(data.position, data.user_id, data.username);
        break;

      case 'selection_change':
        this._handleSelectionChange(data.selection, data.user_id, data.username);
        break;

      case 'presence_update':
        this._handlePresenceUpdate(data.presence, data.user_id);
        break;

      case 'user_joined':
        this._handleCollaboratorJoined({
          id: data.user_id,
          username: data.username,
          timestamp: data.timestamp
        });
        break;

      case 'user_left':
        this._handleCollaboratorLeft(data.user_id);
        break;

      case 'session_joined':
        this._handleSessionJoined(data);
        break;

      case 'comment_created':
        this._handleCommentCreated(data.comment);
        break;

      case 'comment_updated':
        this._handleCommentUpdated(data.comment);
        break;

      case 'comment_resolved':
        this._handleCommentResolved(data.comment);
        break;

      case 'edit_confirmed':
        this._handleEditConfirmed(data);
        break;

      default:
        this._log('Unknown message type:', data.type);
    }

    // Emit generic message event
    this._emit('message', data);
  }

  /**
   * Handle incoming operations
   * @param {Object} operation - Operation data
   */
  _handleOperation(operation) {
    this.operationQueue.push(operation);
    this._processOperationQueue();
    this._emit('operation', operation);
  }

  /**
   * Handle cursor updates
   * @param {Object} cursor - Cursor data
   * @param {string} userId - User ID
   */
  _handleCursorUpdate(cursor, userId) {
    this._emit('cursor_update', { cursor, userId });
  }

  /**
   * Handle presence updates
   * @param {Object} presence - Presence data
   * @param {string} userId - User ID
   */
  _handlePresenceUpdate(presence, userId) {
    if (this.collaborators.has(userId)) {
      const collaborator = this.collaborators.get(userId);
      collaborator.presence = presence;
      this.collaborators.set(userId, collaborator);
    }
    this._emit('presence_update', { presence, userId });
  }

  /**
   * Handle collaborator joined
   * @param {Object} collaborator - Collaborator data
   */
  _handleCollaboratorJoined(collaborator) {
    this.collaborators.set(collaborator.id, collaborator);
    this._emit('collaborator_joined', collaborator);
  }

  /**
   * Handle collaborator left
   * @param {string} userId - User ID
   */
  _handleCollaboratorLeft(userId) {
    const collaborator = this.collaborators.get(userId);
    this.collaborators.delete(userId);
    this._emit('collaborator_left', { userId, collaborator });
  }

  /**
   * Handle session joined confirmation
   * @param {Object} data - Join data
   */
  _handleSessionJoined(data) {
    this._log('Successfully joined session:', data.session_id);
    this._emit('session_joined', data);
  }

  /**
   * Handle selection change
   * @param {Object} selection - Selection data
   * @param {string} userId - User ID
   * @param {string} username - Username
   */
  _handleSelectionChange(selection, userId, username) {
    this._emit('selection_change', { selection, userId, username });
  }

  /**
   * Handle comment created
   * @param {Object} comment - Comment data
   */
  _handleCommentCreated(comment) {
    this._emit('comment_created', comment);
  }

  /**
   * Handle comment updated
   * @param {Object} comment - Comment data
   */
  _handleCommentUpdated(comment) {
    this._emit('comment_updated', comment);
  }

  /**
   * Handle comment resolved
   * @param {Object} comment - Comment data
   */
  _handleCommentResolved(comment) {
    this._emit('comment_resolved', comment);
  }

  /**
   * Handle edit confirmed
   * @param {Object} data - Confirmation data
   */
  _handleEditConfirmed(data) {
    this._emit('edit_confirmed', data);
  }

  /**
   * Process operation queue
   */
  async _processOperationQueue() {
    if (this.isProcessingOperations || this.operationQueue.length === 0) {
      return;
    }

    this.isProcessingOperations = true;

    try {
      while (this.operationQueue.length > 0) {
        const operation = this.operationQueue.shift();
        // Process operation (transform, apply, etc.)
        await this._processOperation(operation);
      }
    } catch (error) {
      this._error('Error processing operations:', error);
    } finally {
      this.isProcessingOperations = false;
    }
  }

  /**
   * Process a single operation
   * @param {Object} operation - Operation to process
   */
  async _processOperation(operation) {
    // This would contain the operational transformation logic
    // For now, just emit the operation
    this._emit('operation_processed', operation);
  }

  /**
   * Generate unique operation ID
   * @returns {string} Operation ID
   */
  _generateOperationId() {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Disconnect from collaboration
   */
  disconnect() {
    if (this.wsClient) {
      this.wsClient.disconnect();
      this.wsClient = null;
    }

    this.isConnected = false;
    this.currentDocument = null;
    this.collaborators.clear();
    this.operationQueue = [];
    this.eventListeners.clear();
  }

  /**
   * Log debug message
   * @param {...any} args - Arguments to log
   */
  _log(...args) {
    if (this.debug) {
      console.log('[CollaborationService]', ...args);
    }
  }

  /**
   * Log error message
   * @param {...any} args - Arguments to log
   */
  _error(...args) {
    console.error('[CollaborationService]', ...args);
  }
}

// Export singleton instance
export default new CollaborationService();
