<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Service Worker - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Service Worker Cleanup</h1>
        <p>This tool helps clear service workers and caches that might be interfering with React app loading.</p>
        
        <div id="status">
            <div class="status info">Ready to clear service workers and caches...</div>
        </div>

        <div>
            <button onclick="clearServiceWorkers()">🗑️ Clear Service Workers</button>
            <button onclick="clearAllCaches()">🧹 Clear All Caches</button>
            <button onclick="clearEverything()" class="danger">💥 Clear Everything</button>
            <button onclick="checkStatus()">🔍 Check Status</button>
        </div>

        <h3>📋 Results</h3>
        <pre id="results">Click a button to start cleanup...</pre>

        <div>
            <button onclick="testMainApp()">🚀 Test Main App</button>
            <button onclick="reloadPage()">🔄 Reload Page</button>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            document.getElementById('status').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
        }

        function log(message) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.textContent += `[${timestamp}] ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }

        async function clearServiceWorkers() {
            updateStatus('🗑️ Clearing service workers...', 'info');
            log('Starting service worker cleanup...');
            
            try {
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    log(`Found ${registrations.length} service worker registration(s)`);
                    
                    for (const registration of registrations) {
                        log(`Unregistering service worker: ${registration.scope}`);
                        await registration.unregister();
                        log('✅ Service worker unregistered');
                    }
                    
                    if (registrations.length === 0) {
                        log('No service workers found to unregister');
                    }
                    
                    updateStatus('✅ Service workers cleared successfully', 'success');
                } else {
                    log('Service Worker API not available');
                    updateStatus('⚠️ Service Worker API not available', 'warning');
                }
            } catch (error) {
                log(`❌ Error clearing service workers: ${error.message}`);
                updateStatus('❌ Error clearing service workers', 'error');
            }
        }

        async function clearAllCaches() {
            updateStatus('🧹 Clearing all caches...', 'info');
            log('Starting cache cleanup...');
            
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    log(`Found ${cacheNames.length} cache(s): ${cacheNames.join(', ')}`);
                    
                    for (const cacheName of cacheNames) {
                        log(`Deleting cache: ${cacheName}`);
                        await caches.delete(cacheName);
                        log('✅ Cache deleted');
                    }
                    
                    if (cacheNames.length === 0) {
                        log('No caches found to delete');
                    }
                    
                    updateStatus('✅ All caches cleared successfully', 'success');
                } else {
                    log('Cache API not available');
                    updateStatus('⚠️ Cache API not available', 'warning');
                }
            } catch (error) {
                log(`❌ Error clearing caches: ${error.message}`);
                updateStatus('❌ Error clearing caches', 'error');
            }
        }

        async function clearEverything() {
            updateStatus('💥 Clearing everything...', 'info');
            log('Starting complete cleanup...');
            
            await clearServiceWorkers();
            await clearAllCaches();
            
            // Clear localStorage
            try {
                const localStorageKeys = Object.keys(localStorage);
                log(`Clearing ${localStorageKeys.length} localStorage item(s)`);
                localStorage.clear();
                log('✅ localStorage cleared');
            } catch (error) {
                log(`⚠️ Error clearing localStorage: ${error.message}`);
            }
            
            // Clear sessionStorage
            try {
                const sessionStorageKeys = Object.keys(sessionStorage);
                log(`Clearing ${sessionStorageKeys.length} sessionStorage item(s)`);
                sessionStorage.clear();
                log('✅ sessionStorage cleared');
            } catch (error) {
                log(`⚠️ Error clearing sessionStorage: ${error.message}`);
            }
            
            log('🎉 Complete cleanup finished!');
            updateStatus('🎉 Everything cleared! Reload the page to test.', 'success');
        }

        async function checkStatus() {
            updateStatus('🔍 Checking status...', 'info');
            log('Checking current status...');
            
            // Check service workers
            if ('serviceWorker' in navigator) {
                const registrations = await navigator.serviceWorker.getRegistrations();
                log(`Service Workers: ${registrations.length} active`);
                registrations.forEach((reg, index) => {
                    log(`  ${index + 1}. Scope: ${reg.scope}`);
                });
            } else {
                log('Service Workers: API not available');
            }
            
            // Check caches
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                log(`Caches: ${cacheNames.length} found`);
                cacheNames.forEach((name, index) => {
                    log(`  ${index + 1}. ${name}`);
                });
            } else {
                log('Caches: API not available');
            }
            
            // Check storage
            log(`localStorage: ${Object.keys(localStorage).length} items`);
            log(`sessionStorage: ${Object.keys(sessionStorage).length} items`);
            
            updateStatus('✅ Status check complete', 'success');
        }

        function testMainApp() {
            window.open('/', '_blank');
        }

        function reloadPage() {
            window.location.reload(true); // Force reload from server
        }

        // Auto-check status on page load
        window.addEventListener('load', () => {
            setTimeout(checkStatus, 500);
        });
    </script>
</body>
</html>
