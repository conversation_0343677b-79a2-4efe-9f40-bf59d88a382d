/**
 * AI WebSocket Service
 * Handles real-time AI suggestions via WebSocket connection
 */

class AIWebSocketService {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.listeners = new Map();
    this.messageQueue = [];
    this.subscriptions = new Set();
    
    // Get WebSocket URL
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = process.env.REACT_APP_WS_HOST || window.location.host;
    this.wsUrl = `${protocol}//${host}/ws/ai-suggestions/`;
  }

  /**
   * Connect to AI suggestions WebSocket
   */
  connect() {
    if (this.isConnected || this.ws) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.wsUrl);

        this.ws.onopen = () => {
          console.log('AI WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Process queued messages
          this.processMessageQueue();
          
          // Emit connection event
          this.emit('connected');
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Error parsing AI WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('AI WebSocket disconnected:', event.code, event.reason);
          this.isConnected = false;
          this.ws = null;
          
          // Emit disconnection event
          this.emit('disconnected', { code: event.code, reason: event.reason });
          
          // Attempt reconnection if not intentional
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('AI WebSocket error:', error);
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        console.error('Error creating AI WebSocket:', error);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
      this.isConnected = false;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Scheduling AI WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect().catch(error => {
          console.error('AI WebSocket reconnect failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Send message to WebSocket
   */
  send(message) {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for later
      this.messageQueue.push(message);
      
      // Try to connect if not connected
      if (!this.isConnected) {
        this.connect();
      }
    }
  }

  /**
   * Process queued messages
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(data) {
    const { type } = data;
    
    switch (type) {
      case 'connection_established':
        console.log('AI WebSocket connection established');
        break;
        
      case 'layout_suggestions':
        this.emit('layoutSuggestions', data.suggestions);
        break;
        
      case 'component_combinations':
        this.emit('componentCombinations', data.suggestions);
        break;
        
      case 'app_analysis':
        this.emit('appAnalysis', data.analysis);
        break;
        
      case 'layout_suggestions_broadcast':
        this.emit('layoutSuggestionsBroadcast', data.suggestions);
        break;
        
      case 'component_combinations_broadcast':
        this.emit('componentCombinationsBroadcast', data.suggestions);
        break;
        
      case 'ai_suggestion_update':
        this.emit('aiSuggestionUpdate', data);
        break;
        
      case 'error':
        console.error('AI WebSocket error:', data.message);
        this.emit('error', new Error(data.message));
        break;
        
      case 'pong':
        this.emit('pong', data);
        break;
        
      default:
        console.log('Unknown AI WebSocket message type:', type, data);
    }
  }

  /**
   * Request layout suggestions
   */
  requestLayoutSuggestions(components, layouts = [], context = {}, broadcast = false) {
    this.send({
      type: 'get_layout_suggestions',
      components,
      layouts,
      context,
      broadcast,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Request component combinations
   */
  requestComponentCombinations(components, selectedComponent = null, context = {}, broadcast = false) {
    this.send({
      type: 'get_component_combinations',
      components,
      selected_component: selectedComponent,
      context,
      broadcast,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Request app structure analysis
   */
  requestAppAnalysis(components, layouts = []) {
    this.send({
      type: 'analyze_app_structure',
      components,
      layouts,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Subscribe to AI updates
   */
  subscribeToUpdates(subscriptionType = 'all') {
    this.subscriptions.add(subscriptionType);
    this.send({
      type: 'subscribe_to_updates',
      subscription_type: subscriptionType,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send ping to keep connection alive
   */
  ping() {
    this.send({
      type: 'ping',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Add event listener
   */
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  /**
   * Remove event listener
   */
  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in AI WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions),
      queuedMessages: this.messageQueue.length
    };
  }
}

// Create singleton instance
const aiWebSocketService = new AIWebSocketService();

// Auto-connect when service is imported
if (typeof window !== 'undefined') {
  // Connect after a short delay to allow app initialization
  setTimeout(() => {
    aiWebSocketService.connect().catch(error => {
      console.warn('Initial AI WebSocket connection failed:', error);
    });
  }, 1000);
}

export default aiWebSocketService;
