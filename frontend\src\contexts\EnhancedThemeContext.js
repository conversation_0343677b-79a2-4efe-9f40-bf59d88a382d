import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { ConfigProvider, theme } from 'antd';

// Enhanced theme context
const EnhancedThemeContext = createContext({
  isDarkMode: false,
  themeMode: 'light', // 'light', 'dark', 'system'
  toggleDarkMode: () => { },
  setThemeMode: () => { },
  colors: {},
  systemPrefersDark: false,
});

// Theme colors for light and dark modes
const lightTheme = {
  primary: '#1890ff',
  primaryHover: '#40a9ff',
  secondary: '#52c41a',
  background: '#ffffff',
  backgroundSecondary: '#f5f5f5',
  backgroundTertiary: '#fafafa',
  surface: '#ffffff',
  text: '#000000d9',
  textSecondary: '#00000073',
  textTertiary: '#00000040',
  border: '#d9d9d9',
  borderLight: '#f0f0f0',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#1890ff',
};

const darkTheme = {
  primary: '#1890ff',
  primaryHover: '#40a9ff',
  secondary: '#52c41a',
  background: '#141414',
  backgroundSecondary: '#1f1f1f',
  backgroundTertiary: '#262626',
  surface: '#1f1f1f',
  text: '#ffffffd9',
  textSecondary: '#ffffff73',
  textTertiary: '#ffffff40',
  border: '#434343',
  borderLight: '#303030',
  shadow: 'rgba(0, 0, 0, 0.3)',
  shadowLight: 'rgba(0, 0, 0, 0.2)',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#1890ff',
};

// Enhanced Theme Provider Component
export const EnhancedThemeProvider = ({ children }) => {
  const [themeMode, setThemeMode] = useState(() => {
    // Check localStorage first, then system preference
    const savedTheme = localStorage.getItem('app-theme-mode');
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      return savedTheme;
    }
    return 'system';
  });

  const [systemPrefersDark, setSystemPrefersDark] = useState(() => {
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  // Calculate effective dark mode state
  const isDarkMode = themeMode === 'dark' || (themeMode === 'system' && systemPrefersDark);

  // Get current theme colors
  const colors = isDarkMode ? darkTheme : lightTheme;

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e) => {
      setSystemPrefersDark(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;

    // Set CSS custom properties
    Object.entries(colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // Set data attribute for CSS selectors
    root.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

    // Add/remove dark class for compatibility
    if (isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', colors.primary);
    }
  }, [colors, isDarkMode]);

  // Save theme preference to localStorage
  useEffect(() => {
    localStorage.setItem('app-theme-mode', themeMode);
  }, [themeMode]);

  // Toggle between light and dark mode
  const toggleDarkMode = useCallback(() => {
    setThemeMode(current => {
      if (current === 'system') {
        return systemPrefersDark ? 'light' : 'dark';
      }
      return current === 'light' ? 'dark' : 'light';
    });
  }, [systemPrefersDark]);

  // Set specific theme mode
  const handleSetThemeMode = useCallback((mode) => {
    if (['light', 'dark', 'system'].includes(mode)) {
      setThemeMode(mode);
    }
  }, []);

  // Ant Design theme configuration
  const antdThemeConfig = {
    algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: colors.primary,
      colorSuccess: colors.success,
      colorWarning: colors.warning,
      colorError: colors.error,
      colorInfo: colors.info,
      colorBgBase: colors.background,
      colorBgContainer: colors.surface,
      colorText: colors.text,
      colorTextSecondary: colors.textSecondary,
      colorBorder: colors.border,
      borderRadius: 6,
      wireframe: false,
    },
    components: {
      Layout: {
        bodyBg: colors.background,
        headerBg: colors.surface,
        footerBg: colors.surface,
      },
      Card: {
        colorBgContainer: colors.surface,
      },
      Menu: {
        colorBgContainer: colors.surface,
      },
    },
  };

  const contextValue = {
    isDarkMode,
    themeMode,
    toggleDarkMode,
    setThemeMode: handleSetThemeMode,
    colors,
    systemPrefersDark,
  };

  return (
    <EnhancedThemeContext.Provider value={contextValue}>
      <ConfigProvider theme={antdThemeConfig}>
        {children}
      </ConfigProvider>
    </EnhancedThemeContext.Provider>
  );
};

// Custom hook to use the enhanced theme context
export const useEnhancedTheme = () => {
  const context = useContext(EnhancedThemeContext);
  if (!context) {
    throw new Error('useEnhancedTheme must be used within an EnhancedThemeProvider');
  }
  return context;
};

export default EnhancedThemeContext;
