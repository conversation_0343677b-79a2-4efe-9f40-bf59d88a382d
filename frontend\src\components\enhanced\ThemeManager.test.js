import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import EnhancedThemeManager from './ThemeManager';
import { addTheme, updateTheme, removeTheme, setActiveTheme } from '../../redux/actions';

// Mock redux store
const mockStore = configureStore([]);

// Mock the service worker
const mockPostMessage = jest.fn();
Object.defineProperty(navigator, 'serviceWorker', {
  value: {
    controller: {
      postMessage: mockPostMessage
    },
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  },
  configurable: true
});

describe('EnhancedThemeManager', () => {
  let store;
  
  beforeEach(() => {
    store = mockStore({
      themes: [
        {
          id: 'default',
          name: 'Default Theme',
          primaryColor: '#2563EB',
          secondaryColor: '#10B981',
          backgroundColor: '#FFFFFF',
          textColor: '#111827',
          fontFamily: 'Inter, sans-serif'
        },
        {
          id: 'dark',
          name: 'Dark Theme',
          primaryColor: '#3B82F6',
          secondaryColor: '#10B981',
          backgroundColor: '#111827',
          textColor: '#F9FAFB',
          fontFamily: 'Inter, sans-serif'
        }
      ],
      activeTheme: 'default'
    });
    
    // Clear mock calls
    mockPostMessage.mockClear();
    store.dispatch = jest.fn();
  });
  
  test('renders theme manager component', () => {
    render(
      <Provider store={store}>
        <EnhancedThemeManager />
      </Provider>
    );
    
    expect(screen.getByText('Create Theme')).toBeInTheDocument();
    expect(screen.getByText('Theme Preview')).toBeInTheDocument();
    expect(screen.getByText('Available Themes')).toBeInTheDocument();
  });
  
  test('adds a new theme', () => {
    render(
      <Provider store={store}>
        <EnhancedThemeManager />
      </Provider>
    );
    
    // Fill in the form
    fireEvent.change(screen.getByLabelText('Theme Name'), { target: { value: 'Test Theme' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('Add Theme'));
    
    // Check if the action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'ADD_THEME',
        payload: expect.objectContaining({
          name: 'Test Theme'
        })
      })
    );
    
    // Check if service worker was notified
    expect(mockPostMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'THEME_UPDATED'
      })
    );
  });
  
  test('updates an existing theme', () => {
    render(
      <Provider store={store}>
        <EnhancedThemeManager />
      </Provider>
    );
    
    // Select a theme to edit
    fireEvent.click(screen.getAllByText('Edit')[0]);
    
    // Change the theme name
    fireEvent.change(screen.getByLabelText('Theme Name'), { target: { value: 'Updated Theme' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('Update Theme'));
    
    // Check if the action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'UPDATE_THEME',
        payload: expect.objectContaining({
          name: 'Updated Theme'
        })
      })
    );
    
    // Check if service worker was notified
    expect(mockPostMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'THEME_UPDATED'
      })
    );
  });
  
  test('sets a theme as active', () => {
    render(
      <Provider store={store}>
        <EnhancedThemeManager />
      </Provider>
    );
    
    // Find and click the Activate button for the dark theme
    const activateButtons = screen.getAllByText('Activate');
    fireEvent.click(activateButtons[0]);
    
    // Check if the action was dispatched
    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'SET_ACTIVE_THEME'
      })
    );
    
    // Check if service worker was notified
    expect(mockPostMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'THEME_UPDATED'
      })
    );
  });
});
