"""
Enhanced code generation service for App Builder
Supports multiple frameworks, TypeScript, accessibility, and modern best practices
"""

import json
import os
import zipfile
from io import BytesIO
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum


class ExportFormat(Enum):
    """Supported export formats"""
    REACT = "react"
    REACT_TS = "react-ts"
    VUE = "vue"
    VUE_TS = "vue-ts"
    ANGULAR = "angular"
    SVELTE = "svelte"
    NEXT_JS = "next"
    NUXT = "nuxt"
    HTML = "html"
    REACT_NATIVE = "react-native"
    FLUTTER = "flutter"
    IONIC = "ionic"
    EXPRESS_API = "express-api"
    DJANGO_API = "django-api"
    FASTAPI = "fastapi"


class StyleFramework(Enum):
    """Supported styling frameworks"""
    STYLED_COMPONENTS = "styled-components"
    EMOTION = "emotion"
    TAILWIND = "tailwind"
    CSS_MODULES = "css-modules"
    MATERIAL_UI = "material-ui"
    CHAKRA_UI = "chakra-ui"
    BOOTSTRAP = "bootstrap"


@dataclass
class ExportOptions:
    """Configuration options for code export"""
    format: ExportFormat
    typescript: bool = False
    include_accessibility: bool = True
    include_tests: bool = False
    include_storybook: bool = False
    style_framework: StyleFramework = StyleFramework.STYLED_COMPONENTS
    state_management: str = "useState"  # useState, redux, zustand, context
    project_structure: str = "single-file"  # single-file, multi-file, full-project
    bundler: str = "vite"  # vite, webpack, parcel
    package_manager: str = "npm"  # npm, yarn, pnpm
    include_docker: bool = False
    include_ci_cd: bool = False
    target_directory: Optional[str] = None


class EnhancedCodeGenerator:
    """Enhanced code generator with support for multiple frameworks and modern practices"""
    
    def __init__(self):
        self.template_cache = {}
    
    def generate_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str], bytes]:
        """
        Generate code based on app data and export options
        
        Args:
            app_data: Application data structure
            options: Export configuration options
            
        Returns:
            Generated code as string, dict of files, or zip bytes
        """
        try:
            if options.format == ExportFormat.REACT or options.format == ExportFormat.REACT_TS:
                return self._generate_react_code(app_data, options)
            elif options.format == ExportFormat.VUE or options.format == ExportFormat.VUE_TS:
                return self._generate_vue_code(app_data, options)
            elif options.format == ExportFormat.ANGULAR:
                return self._generate_angular_code(app_data, options)
            elif options.format == ExportFormat.SVELTE:
                return self._generate_svelte_code(app_data, options)
            elif options.format == ExportFormat.NEXT_JS:
                return self._generate_nextjs_code(app_data, options)
            elif options.format == ExportFormat.REACT_NATIVE:
                return self._generate_react_native_code(app_data, options)
            elif options.format == ExportFormat.FLUTTER:
                return self._generate_flutter_code(app_data, options)
            elif options.format == ExportFormat.HTML:
                return self._generate_html_code(app_data, options)
            elif options.format == ExportFormat.EXPRESS_API:
                return self._generate_express_api(app_data, options)
            elif options.format == ExportFormat.DJANGO_API:
                return self._generate_django_api(app_data, options)
            elif options.format == ExportFormat.FASTAPI:
                return self._generate_fastapi_code(app_data, options)
            else:
                raise ValueError(f"Unsupported export format: {options.format}")
                
        except Exception as e:
            raise Exception(f"Code generation failed: {str(e)}")
    
    def _generate_react_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate React code with modern best practices"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        styles = app_data.get('styles', {})
        data = app_data.get('data', {})
        
        # Generate imports
        imports = self._generate_react_imports(components, options)
        
        # Generate TypeScript interfaces if needed
        interfaces = ""
        if options.typescript:
            interfaces = self._generate_typescript_interfaces(components, layouts)
        
        # Generate styled components or CSS
        styles_code = self._generate_react_styles(styles, components, layouts, options)
        
        # Generate component definitions
        component_definitions = self._generate_react_components(components, options)
        
        # Generate main App component
        app_component = self._generate_react_app_component(components, layouts, data, options)
        
        # Combine all parts
        file_extension = "tsx" if options.typescript else "jsx"
        code = f"{imports}\n{interfaces}\n{styles_code}\n{component_definitions}\n{app_component}"
        
        if options.project_structure == "single-file":
            return code
        elif options.project_structure == "multi-file":
            return {
                f"App.{file_extension}": code,
                "package.json": self._generate_package_json("react-app", options),
                "README.md": self._generate_readme("React", options)
            }
        else:  # full-project
            return self._generate_react_project(app_data, options)
    
    def _generate_react_imports(self, components: List[Dict], options: ExportOptions) -> str:
        """Generate React imports based on components and options"""
        imports = ["import React"]
        
        if options.state_management == "useState":
            imports[0] += ", { useState, useEffect }"
        
        imports[0] += " from 'react';"
        
        # Style framework imports
        if options.style_framework == StyleFramework.STYLED_COMPONENTS:
            imports.append("import styled from 'styled-components';")
        elif options.style_framework == StyleFramework.EMOTION:
            imports.append("import styled from '@emotion/styled';")
        elif options.style_framework == StyleFramework.MATERIAL_UI:
            imports.append("import { ThemeProvider, createTheme } from '@mui/material/styles';")
            imports.append("import CssBaseline from '@mui/material/CssBaseline';")
        
        # State management imports
        if options.state_management == "redux":
            imports.append("import { useSelector, useDispatch } from 'react-redux';")
        elif options.state_management == "zustand":
            imports.append("import { useStore } from './store';")
        
        # PropTypes for non-TypeScript projects
        if not options.typescript:
            imports.append("import PropTypes from 'prop-types';")
        
        return "\n".join(imports) + "\n"
    
    def _generate_typescript_interfaces(self, components: List[Dict], layouts: List[Dict]) -> str:
        """Generate TypeScript interfaces for components and data"""
        interfaces = ["// TypeScript Interfaces"]
        
        # Component props interfaces
        component_types = list(set(c.get('type', '') for c in components))
        for comp_type in component_types:
            component = next((c for c in components if c.get('type') == comp_type), None)
            if component and component.get('props'):
                interfaces.append(f"interface {comp_type}Props {{")
                for prop, value in component['props'].items():
                    prop_type = self._get_typescript_type(value)
                    interfaces.append(f"  {prop}?: {prop_type};")
                interfaces.append("  className?: string;")
                interfaces.append("  children?: React.ReactNode;")
                interfaces.append("}\n")
        
        # App data interface
        interfaces.extend([
            "interface AppData {",
            "  components: ComponentData[];",
            "  layouts: LayoutData[];",
            "}\n"
        ])
        
        return "\n".join(interfaces)
    
    def _generate_react_styles(self, styles: Dict, components: List[Dict], layouts: List[Dict], options: ExportOptions) -> str:
        """Generate React styles based on framework choice"""
        if options.style_framework in [StyleFramework.STYLED_COMPONENTS, StyleFramework.EMOTION]:
            return self._generate_styled_components(styles, components, layouts)
        elif options.style_framework == StyleFramework.TAILWIND:
            return "// Tailwind CSS classes will be used inline\n"
        elif options.style_framework == StyleFramework.MATERIAL_UI:
            return self._generate_material_ui_theme()
        else:
            return self._generate_css_modules(styles)
    
    def _generate_styled_components(self, styles: Dict, components: List[Dict], layouts: List[Dict]) -> str:
        """Generate styled-components code"""
        styled_code = ["// Styled Components"]
        
        # Container styles
        styled_code.extend([
            "const AppContainer = styled.div`",
            "  min-height: 100vh;",
            "  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;",
            "  line-height: 1.6;",
            "  color: #333;",
            "`;\n"
        ])
        
        # Layout styles
        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'Layout'))
            layout_name = self._pascal_case(layout_name)
            styled_code.extend([
                f"const {layout_name}Container = styled.div`",
                "  display: flex;",
                "  flex-direction: column;",
                "  gap: 1rem;",
                "  padding: 1rem;",
                "`;\n"
            ])
        
        # Component styles
        component_types = list(set(c.get('type', '') for c in components))
        for comp_type in component_types:
            styled_code.extend([
                f"const Styled{comp_type} = styled.div`",
                f"  {self._get_component_styles(comp_type)}",
                "`;\n"
            ])
        
        return "\n".join(styled_code)
    
    def _generate_material_ui_theme(self) -> str:
        """Generate Material-UI theme configuration"""
        return """// Material-UI Theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
  },
});

"""
    
    def _generate_react_components(self, components: List[Dict], options: ExportOptions) -> str:
        """Generate React component definitions"""
        component_code = ["// Component Definitions"]
        
        component_types = list(set(c.get('type', '') for c in components))
        for comp_type in component_types:
            component = next((c for c in components if c.get('type') == comp_type), None)
            if not component:
                continue
                
            props_type = f"{comp_type}Props" if options.typescript else ""
            
            component_code.extend([
                f"const {comp_type} = ({f'props: {props_type}' if options.typescript else 'props'}) => {{",
                f"  const {{ {', '.join(component.get('props', {}).keys())}, className, children, ...rest }} = props;",
                "",
                "  return (",
                f"    <Styled{comp_type}",
                "      className={className}",
            ])
            
            if options.include_accessibility:
                aria_role = self._get_aria_role(comp_type)
                component_code.append(f'      role="{aria_role}"')
                if comp_type == "Button":
                    component_code.append("      aria-label={props['aria-label'] || props.text || 'Button'}")
            
            component_code.extend([
                "      {...rest}",
                "    >",
                f"      {self._get_component_content(comp_type, component.get('props', {}))}",
                "      {children}",
                f"    </Styled{comp_type}>",
                "  );",
                "};\n"
            ])
            
            # Add PropTypes for non-TypeScript projects
            if not options.typescript:
                component_code.extend([
                    f"{comp_type}.propTypes = {{",
                    *[f"  {prop}: PropTypes.{self._get_prop_type(value)}," 
                      for prop, value in component.get('props', {}).items()],
                    "  className: PropTypes.string,",
                    "  children: PropTypes.node",
                    "};\n"
                ])
        
        return "\n".join(component_code)
    
    def _generate_react_app_component(self, components: List[Dict], layouts: List[Dict], data: Dict, options: ExportOptions) -> str:
        """Generate the main React App component"""
        app_code = ["// Main App Component"]
        
        # State management setup
        if options.state_management == "useState" and data:
            app_code.append("const App = () => {")
            for key, value in data.items():
                app_code.append(f"  const [{key}, set{self._pascal_case(key)}] = useState({json.dumps(value)});")
            app_code.append("")
        else:
            app_code.append("const App = () => {")
        
        # Material-UI theme provider wrapper
        if options.style_framework == StyleFramework.MATERIAL_UI:
            app_code.extend([
                "  return (",
                "    <ThemeProvider theme={theme}>",
                "      <CssBaseline />",
                f"      <AppContainer{' role=\"main\"' if options.include_accessibility else ''}>"
            ])
        else:
            app_code.extend([
                "  return (",
                f"    <AppContainer{' role=\"main\"' if options.include_accessibility else ''}>"
            ])
        
        # Generate layout structure
        for layout in layouts:
            layout_name = self._pascal_case(layout.get('name', layout.get('type', 'Layout')))
            app_code.append(f"      <{layout_name}Container>")
            
            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        comp_type = component.get('type', '')
                        app_code.append(f"        <{comp_type}")
                        for prop, value in component.get('props', {}).items():
                            if isinstance(value, str):
                                app_code.append(f'          {prop}="{value}"')
                            else:
                                app_code.append(f"          {prop}={{{json.dumps(value)}}}")
                        app_code.append("        />")
            else:
                app_code.append("        {/* Add components here */}")
            
            app_code.append(f"      </{layout_name}Container>")
        
        if options.style_framework == StyleFramework.MATERIAL_UI:
            app_code.extend([
                "    </AppContainer>",
                "    </ThemeProvider>",
                "  );",
                "};\n",
                "export default App;"
            ])
        else:
            app_code.extend([
                "    </AppContainer>",
                "  );",
                "};\n",
                "export default App;"
            ])
        
        return "\n".join(app_code)
    
    # Utility methods
    def _pascal_case(self, text: str) -> str:
        """Convert text to PascalCase"""
        return ''.join(word.capitalize() for word in text.replace('-', ' ').replace('_', ' ').split())
    
    def _get_typescript_type(self, value: Any) -> str:
        """Get TypeScript type for a value"""
        if isinstance(value, str):
            return "string"
        elif isinstance(value, (int, float)):
            return "number"
        elif isinstance(value, bool):
            return "boolean"
        elif isinstance(value, list):
            return "any[]"
        elif isinstance(value, dict):
            return "object"
        else:
            return "any"
    
    def _get_aria_role(self, component_type: str) -> str:
        """Get appropriate ARIA role for component type"""
        role_map = {
            'Button': 'button',
            'Input': 'textbox',
            'Text': 'text',
            'Image': 'img',
            'Header': 'banner',
            'Section': 'region',
            'Card': 'article',
            'List': 'list'
        }
        return role_map.get(component_type, 'generic')
    
    def _get_component_content(self, component_type: str, props: Dict) -> str:
        """Generate component content based on type"""
        content_map = {
            'Button': props.get('text', 'Button'),
            'Text': props.get('content', 'Text content'),
            'Input': '',  # Self-closing
            'Image': '',  # Self-closing
        }
        return content_map.get(component_type, props.get('content', component_type))
    
    def _get_component_styles(self, component_type: str) -> str:
        """Get default styles for component type"""
        style_map = {
            'Button': """
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #0056b3;
  }
  
  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }""",
            'Input': """
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 0.25rem;
  font-size: 1rem;
  
  &:focus {
    outline: 2px solid #007bff;
    border-color: #007bff;
  }""",
            'Text': """
  margin: 0;
  line-height: 1.5;""",
            'Image': """
  max-width: 100%;
  height: auto;""",
            'Card': """
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);"""
        }
        return style_map.get(component_type, f"/* Add styles for {component_type} */")
    
    def _get_prop_type(self, value: Any) -> str:
        """Get PropTypes type for a value"""
        if isinstance(value, str):
            return "string"
        elif isinstance(value, (int, float)):
            return "number"
        elif isinstance(value, bool):
            return "bool"
        elif isinstance(value, list):
            return "array"
        elif isinstance(value, dict):
            return "object"
        else:
            return "any"

    def _generate_css_modules(self, styles: Dict) -> str:
        """Generate CSS modules styles"""
        css = ""
        for selector, style in styles.items():
            css += f"{selector} {{\n"
            for prop, value in style.items():
                css += f"  {prop}: {value};\n"
            css += "}\n\n"
        return css

    def _generate_package_json(self, project_type: str, options: ExportOptions) -> str:
        """Generate package.json for the project"""
        base_package = {
            "name": "app-builder-generated-app",
            "version": "0.1.0",
            "private": True,
            "description": "Generated by App Builder",
            "author": "App Builder",
            "license": "MIT"
        }

        if project_type == "react-app":
            package = {
                **base_package,
                "scripts": {
                    "start": "react-scripts start",
                    "build": "react-scripts build",
                    "test": "react-scripts test",
                    "eject": "react-scripts eject",
                    "lint": "eslint src --ext .js,.jsx,.ts,.tsx",
                    "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"
                },
                "dependencies": {
                    "react": "^18.2.0",
                    "react-dom": "^18.2.0",
                    "react-scripts": "5.0.1",
                    "web-vitals": "^2.1.4"
                },
                "devDependencies": {
                    "@testing-library/jest-dom": "^5.16.4",
                    "@testing-library/react": "^13.4.0",
                    "@testing-library/user-event": "^13.5.0",
                    "eslint": "^8.36.0",
                    "eslint-plugin-react": "^7.32.2",
                    "eslint-plugin-react-hooks": "^4.6.0"
                }
            }

            # Add style framework dependencies
            if options.style_framework == StyleFramework.STYLED_COMPONENTS:
                package["dependencies"]["styled-components"] = "^5.3.9"
            elif options.style_framework == StyleFramework.EMOTION:
                package["dependencies"]["@emotion/react"] = "^11.10.6"
                package["dependencies"]["@emotion/styled"] = "^11.10.6"
            elif options.style_framework == StyleFramework.MATERIAL_UI:
                package["dependencies"]["@mui/material"] = "^5.11.10"
                package["dependencies"]["@emotion/react"] = "^11.10.6"
                package["dependencies"]["@emotion/styled"] = "^11.10.6"
            elif options.style_framework == StyleFramework.TAILWIND:
                package["dependencies"]["tailwindcss"] = "^3.2.7"

            # Add TypeScript dependencies
            if options.typescript:
                package["devDependencies"].update({
                    "@types/react": "^18.0.28",
                    "@types/react-dom": "^18.0.11",
                    "@types/node": "^16.18.23",
                    "typescript": "^4.9.5"
                })
            else:
                package["dependencies"]["prop-types"] = "^15.8.1"

        return json.dumps(package, indent=2)

    def _generate_readme(self, framework: str, options: ExportOptions) -> str:
        """Generate README.md file"""
        return f"""# Generated App

This project was generated using App Builder.

## Framework: {framework}{'with TypeScript' if options.typescript else ''}

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- {options.package_manager}

### Installation

```bash
# Install dependencies
{options.package_manager} install
```

### Development

```bash
# Start development server
{options.package_manager} start
```

### Building for Production

```bash
# Build for production
{options.package_manager} run build
```

## Project Structure

```
src/
├── components/     # Reusable components
├── pages/         # Page components
├── styles/        # Stylesheets
├── utils/         # Utility functions
└── App.{'tsx' if options.typescript else 'jsx'}        # Main application component
```

## Features

- ✅ Modern {framework} application
- ✅ Responsive design
- ✅ Accessibility features
{'- ✅ TypeScript support' if options.typescript else ''}
- ✅ ESLint configuration
- ✅ Production-ready build

## Generated by App Builder

This application was automatically generated by App Builder. You can customize and extend it according to your needs.

## License

MIT
"""

    def _generate_react_project(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate full React project structure"""
        file_extension = "tsx" if options.typescript else "jsx"

        project_structure = {
            "package.json": self._generate_package_json("react-app", options),
            "README.md": self._generate_readme("React", options),
            f"src/App.{file_extension}": self._generate_react_code(app_data, ExportOptions(
                format=options.format,
                typescript=options.typescript,
                project_structure="single-file",
                **options.__dict__
            )),
            f"src/index.{file_extension}": self._generate_react_index(options.typescript),
            "src/index.css": self._generate_global_styles(),
            "public/index.html": self._generate_index_html(),
            ".gitignore": self._generate_gitignore(),
            ".eslintrc.json": self._generate_eslint_config("react", options.typescript)
        }

        if options.typescript:
            project_structure["tsconfig.json"] = self._generate_tsconfig()

        if options.include_docker:
            project_structure["Dockerfile"] = self._generate_dockerfile("react")
            project_structure["docker-compose.yml"] = self._generate_docker_compose()

        if options.include_ci_cd:
            project_structure[".github/workflows/ci.yml"] = self._generate_github_actions("react")

        return project_structure

    def _generate_react_index(self, typescript: bool) -> str:
        """Generate React index file"""
        return f"""import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root'){'as HTMLElement' if typescript else ''}
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);"""

    def _generate_global_styles(self) -> str:
        """Generate global CSS styles"""
        return """body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

button {
  cursor: pointer;
}

input, textarea, select {
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
}"""

    def _generate_index_html(self) -> str:
        """Generate index.html"""
        return """<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Generated by App Builder" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>Generated App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>"""

    def _generate_gitignore(self) -> str:
        """Generate .gitignore file"""
        return """# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary folders
tmp/
temp/"""

    def _generate_eslint_config(self, framework: str, typescript: bool) -> str:
        """Generate ESLint configuration"""
        config = {
            "env": {
                "browser": True,
                "es2021": True,
                "node": True
            },
            "extends": ["eslint:recommended"],
            "parserOptions": {
                "ecmaVersion": "latest",
                "sourceType": "module"
            },
            "rules": {
                "no-unused-vars": "warn",
                "no-console": "warn",
                "prefer-const": "error"
            }
        }

        if framework == "react":
            config["extends"].extend(["plugin:react/recommended", "plugin:react-hooks/recommended"])
            config["plugins"] = ["react", "react-hooks"]
            config["parserOptions"]["ecmaFeatures"] = {"jsx": True}
            config["settings"] = {"react": {"version": "detect"}}

        if typescript:
            config["extends"].append("@typescript-eslint/recommended")
            config["parser"] = "@typescript-eslint/parser"
            config["plugins"] = config.get("plugins", []) + ["@typescript-eslint"]

        return json.dumps(config, indent=2)

    def _generate_tsconfig(self) -> str:
        """Generate TypeScript configuration"""
        config = {
            "compilerOptions": {
                "target": "es5",
                "lib": ["dom", "dom.iterable", "es6"],
                "allowJs": True,
                "skipLibCheck": True,
                "esModuleInterop": True,
                "allowSyntheticDefaultImports": True,
                "strict": True,
                "forceConsistentCasingInFileNames": True,
                "noFallthroughCasesInSwitch": True,
                "module": "esnext",
                "moduleResolution": "node",
                "resolveJsonModule": True,
                "isolatedModules": True,
                "noEmit": True,
                "jsx": "react-jsx"
            },
            "include": ["src"],
            "exclude": ["node_modules"]
        }
        return json.dumps(config, indent=2)

    def _generate_dockerfile(self, framework: str) -> str:
        """Generate Dockerfile"""
        return """# Use official Node.js runtime as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
"""

    def _generate_docker_compose(self) -> str:
        """Generate Docker Compose file"""
        return """version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - .:/app
      - /app/node_modules
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    restart: unless-stopped
"""

    def _generate_github_actions(self, framework: str) -> str:
        """Generate GitHub Actions workflow"""
        return """name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build application
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build

    - name: Deploy to production
      run: echo "Deploy to your hosting platform"
"""

    # Framework-specific code generators
    def _generate_vue_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate Vue.js code"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        styles = app_data.get('styles', {})
        data = app_data.get('data', {})

        vue_code = f"""<template>
  <div class="app"{'role="main"' if options.include_accessibility else ''}>"""

        # Generate template structure
        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'layout'))
            vue_code += f"""
    <div class="{layout_name}-container">"""

            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        vue_code += f"""
      <{component.get('type', '').lower()}"""
                        for prop, value in component.get('props', {}).items():
                            if isinstance(value, str):
                                vue_code += f' {prop}="{value}"'
                            else:
                                vue_code += f' :{prop}="{json.dumps(value)}"'
                        vue_code += " />"

            vue_code += f"""
    </div>"""

        vue_code += f"""
  </div>
</template>

<script{'lang="ts"' if options.typescript else ''}>
import {{ defineComponent, ref, reactive }} from 'vue';

export default defineComponent({{
  name: 'App',
  setup() {{"""

        # Add reactive data
        if data:
            for key, value in data.items():
                vue_code += f"""
    const {key} = ref({json.dumps(value)});"""

        vue_code += f"""

    return {{"""

        if data:
            for key in data.keys():
                vue_code += f"""
      {key},"""

        vue_code += f"""
    }};
  }}
}});
</script>

<style scoped>
.app {{
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}}

{self._generate_vue_styles(styles, components, layouts)}
</style>"""

        if options.project_structure == "single-file":
            return vue_code
        else:
            return {
                "App.vue": vue_code,
                "package.json": self._generate_package_json("vue-app", options),
                "README.md": self._generate_readme("Vue.js", options)
            }

    def _generate_angular_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate Angular code"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        styles = app_data.get('styles', {})
        data = app_data.get('data', {})

        # Component TypeScript
        component_code = f"""import {{ Component }} from '@angular/core';

@Component({{
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
}})
export class AppComponent {{
  title = 'Generated App';"""

        # Add component properties
        if data:
            for key, value in data.items():
                component_code += f"""
  {key} = {json.dumps(value)};"""

        component_code += """
}"""

        # Template HTML
        template_code = f"""<div class="app"{'role="main"' if options.include_accessibility else ''}>"""

        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'layout'))
            template_code += f"""
  <div class="{layout_name}-container">"""

            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        comp_type = component.get('type', '').lower()
                        template_code += f"""
    <app-{comp_type}"""
                        for prop, value in component.get('props', {}).items():
                            if isinstance(value, str):
                                template_code += f' {prop}="{value}"'
                            else:
                                template_code += f' [{prop}]="{json.dumps(value)}"'
                        template_code += f"></app-{comp_type}>"

            template_code += f"""
  </div>"""

        template_code += """
</div>"""

        # Styles CSS
        styles_code = f""".app {{
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}}

{self._generate_angular_styles(styles, components, layouts)}"""

        return {
            "app.component.ts": component_code,
            "app.component.html": template_code,
            "app.component.css": styles_code,
            "package.json": self._generate_package_json("angular-app", options),
            "README.md": self._generate_readme("Angular", options)
        }

    def _generate_html_code(self, app_data: Dict[str, Any], options: ExportOptions) -> str:
        """Generate HTML code"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        styles = app_data.get('styles', {})

        # Generate CSS
        css = ""
        for selector, style in styles.items():
            css += f"{selector} {{\n"
            for prop, value in style.items():
                css += f"  {prop}: {value};\n"
            css += "}\n\n"

        # Generate HTML
        html_body = ""
        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'layout'))
            html_body += f'<div class="{layout_name}-container">\n'

            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        comp_type = component.get('type', '')
                        props = component.get('props', {})

                        if comp_type == 'Button':
                            html_body += f'  <button class="{props.get("className", "")}">{props.get("text", "Button")}</button>\n'
                        elif comp_type == 'Input':
                            html_body += f'  <input type="{props.get("type", "text")}" placeholder="{props.get("placeholder", "")}" class="{props.get("className", "")}" />\n'
                        elif comp_type == 'Text':
                            html_body += f'  <p class="{props.get("className", "")}">{props.get("content", "")}</p>\n'
                        elif comp_type == 'Image':
                            html_body += f'  <img src="{props.get("src", "")}" alt="{props.get("alt", "")}" class="{props.get("className", "")}" />\n'
                        else:
                            html_body += f'  <div class="{comp_type.lower()}">{props.get("content", comp_type)}</div>\n'

            html_body += '</div>\n'

        return f"""<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated App</title>
  <style>
{css}
  </style>
</head>
<body>
  <div class="app">
{html_body}
  </div>
</body>
</html>"""

    def _generate_vue_styles(self, styles: Dict, components: List[Dict], layouts: List[Dict]) -> str:
        """Generate Vue-specific styles"""
        css = ""
        for selector, style in styles.items():
            css += f"{selector} {{\n"
            for prop, value in style.items():
                css += f"  {prop}: {value};\n"
            css += "}\n\n"
        return css

    def _generate_angular_styles(self, styles: Dict, components: List[Dict], layouts: List[Dict]) -> str:
        """Generate Angular-specific styles"""
        css = ""
        for selector, style in styles.items():
            css += f"{selector} {{\n"
            for prop, value in style.items():
                css += f"  {prop}: {value};\n"
            css += "}\n\n"
        return css

    # Complete framework implementations
    def _generate_svelte_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate Svelte code"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        styles = app_data.get('styles', {})
        data = app_data.get('data', {})

        svelte_code = f"""<script{'lang="ts"' if options.typescript else ''}>"""

        # Add reactive data
        if data:
            for key, value in data.items():
                svelte_code += f"""
  let {key} = {json.dumps(value)};"""

        svelte_code += f"""
</script>

<div class="app"{'role="main"' if options.include_accessibility else ''}>"""

        # Generate template structure
        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'layout'))
            svelte_code += f"""
  <div class="{layout_name}-container">"""

            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        comp_type = component.get('type', '')
                        props = component.get('props', {})
                        svelte_code += f"""
    <{comp_type}"""
                        for prop, value in props.items():
                            if isinstance(value, str):
                                svelte_code += f' {prop}="{value}"'
                            else:
                                svelte_code += f' {prop}={{{json.dumps(value)}}}'
                        svelte_code += " />"

            svelte_code += f"""
  </div>"""

        svelte_code += f"""
</div>

<style>
  .app {{
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
  }}

  {self._generate_svelte_styles(styles, components, layouts)}
</style>"""

        if options.project_structure == "single-file":
            return svelte_code
        else:
            return {
                "App.svelte": svelte_code,
                "package.json": self._generate_package_json("svelte-app", options),
                "README.md": self._generate_readme("Svelte", options)
            }

    def _generate_nextjs_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate Next.js code"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        styles = app_data.get('styles', {})
        data = app_data.get('data', {})

        file_extension = "tsx" if options.typescript else "jsx"

        # Generate Next.js page
        nextjs_code = f"""import React from 'react';
import Head from 'next/head';
import styles from '../styles/Home.module.css';

export default function Home() {{
  return (
    <div className={{styles.container}}>
      <Head>
        <title>Generated App</title>
        <meta name="description" content="Generated by App Builder" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className={{styles.main}}{'role="main"' if options.include_accessibility else ''}>"""

        # Generate layout structure
        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'layout'))
            nextjs_code += f"""
        <div className={{styles.{layout_name}Container}}>"""

            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        comp_type = component.get('type', '')
                        props = component.get('props', {})
                        nextjs_code += f"""
          <{comp_type}"""
                        for prop, value in props.items():
                            if isinstance(value, str):
                                nextjs_code += f' {prop}="{value}"'
                            else:
                                nextjs_code += f' {prop}={{{json.dumps(value)}}}'
                        nextjs_code += " />"

            nextjs_code += f"""
        </div>"""

        nextjs_code += f"""
      </main>
    </div>
  );
}}"""

        # Generate CSS modules
        css_modules = f""".container {{
  padding: 0 2rem;
}}

.main {{
  min-height: 100vh;
  padding: 4rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}}

{self._generate_nextjs_styles(styles, components, layouts)}"""

        return {
            f"pages/index.{file_extension}": nextjs_code,
            "styles/Home.module.css": css_modules,
            "package.json": self._generate_package_json("nextjs-app", options),
            "README.md": self._generate_readme("Next.js", options)
        }

    def _generate_react_native_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate React Native code"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        styles = app_data.get('styles', {})
        data = app_data.get('data', {})

        file_extension = "tsx" if options.typescript else "jsx"

        rn_code = f"""import React{', {{ useState }}' if data else ''} from 'react';
import {{
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
}} from 'react-native';

const App = () => {{"""

        # Add state management
        if data:
            for key, value in data.items():
                rn_code += f"""
  const [{key}, set{self._pascal_case(key)}] = useState({json.dumps(value)});"""

        rn_code += f"""
  return (
    <SafeAreaView style={{styles.container}}>
      <StatusBar barStyle="dark-content" />
      <ScrollView contentInsetAdjustmentBehavior="automatic">"""

        # Generate layout structure
        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'layout'))
            rn_code += f"""
        <View style={{styles.{layout_name}Container}}>"""

            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        rn_component = self._map_to_react_native_component(component.get('type', ''))
                        props = component.get('props', {})
                        rn_code += f"""
          <{rn_component}"""
                        for prop, value in props.items():
                            mapped_prop = self._map_to_react_native_prop(prop, component.get('type', ''))
                            if isinstance(value, str):
                                rn_code += f' {mapped_prop}="{value}"'
                            else:
                                rn_code += f' {mapped_prop}={{{json.dumps(value)}}}'
                        rn_code += " />"

            rn_code += f"""
        </View>"""

        rn_code += f"""
      </ScrollView>
    </SafeAreaView>
  );
}};

const styles = StyleSheet.create({{
  container: {{
    flex: 1,
    backgroundColor: '#fff',
  }},
  {self._generate_react_native_styles(styles, components, layouts)}
}});

export default App;"""

        if options.project_structure == "single-file":
            return rn_code
        else:
            return {
                f"App.{file_extension}": rn_code,
                "package.json": self._generate_package_json("react-native-app", options),
                "README.md": self._generate_readme("React Native", options)
            }

    def _generate_flutter_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate Flutter code"""
        components = app_data.get('components', [])
        layouts = app_data.get('layouts', [])
        data = app_data.get('data', {})

        flutter_code = f"""import 'package:flutter/material.dart';

void main() {{
  runApp(MyApp());
}}

class MyApp extends StatelessWidget {{
  @override
  Widget build(BuildContext context) {{
    return MaterialApp(
      title: 'Generated App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: MyHomePage(),
    );
  }}
}}

class MyHomePage extends StatefulWidget {{
  @override
  _MyHomePageState createState() => _MyHomePageState();
}}

class _MyHomePageState extends State<MyHomePage> {{"""

        # Add state variables
        if data:
            for key, value in data.items():
                dart_type = self._get_dart_type(value)
                flutter_code += f"""
  {dart_type} {key} = {self._format_dart_value(value)};"""

        flutter_code += f"""

  @override
  Widget build(BuildContext context) {{
    return Scaffold(
      appBar: AppBar(
        title: Text('Generated App'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: ["""

        # Generate layout structure
        for layout in layouts:
            flutter_code += f"""
            Container(
              padding: EdgeInsets.all(16.0),
              child: Column(
                children: ["""

            if layout.get('components'):
                for component_id in layout['components']:
                    component = next((c for c in components if c.get('id') == component_id), None)
                    if component:
                        flutter_widget = self._map_to_flutter_widget(component.get('type', ''))
                        props = component.get('props', {})
                        flutter_code += f"""
                  {flutter_widget}("""

                        # Add widget properties
                        if component.get('type') == 'Text':
                            flutter_code += f"""'{props.get("content", "Text")}'"""
                        elif component.get('type') == 'Button':
                            flutter_code += f"""
                    onPressed: () {{}},
                    child: Text('{props.get("text", "Button")}')"""

                        flutter_code += """),"""

            flutter_code += f"""
                ],
              ),
            ),"""

        flutter_code += f"""
          ],
        ),
      ),
    );
  }}
}}"""

        if options.project_structure == "single-file":
            return flutter_code
        else:
            return {
                "lib/main.dart": flutter_code,
                "pubspec.yaml": self._generate_flutter_pubspec(),
                "README.md": self._generate_readme("Flutter", options)
            }

    # Helper methods for new frameworks
    def _generate_svelte_styles(self, styles: Dict, components: List[Dict], layouts: List[Dict]) -> str:
        """Generate Svelte-specific styles"""
        css = ""
        for selector, style in styles.items():
            css += f"  {selector} {{\n"
            for prop, value in style.items():
                css += f"    {prop}: {value};\n"
            css += "  }\n\n"
        return css

    def _generate_nextjs_styles(self, styles: Dict, components: List[Dict], layouts: List[Dict]) -> str:
        """Generate Next.js CSS modules"""
        css = ""
        for selector, style in styles.items():
            css += f"{selector.replace('#', '').replace('.', '')} {{\n"
            for prop, value in style.items():
                css += f"  {prop}: {value};\n"
            css += "}\n\n"
        return css

    def _generate_react_native_styles(self, styles: Dict, components: List[Dict], layouts: List[Dict]) -> str:
        """Generate React Native StyleSheet"""
        rn_styles = ""
        for layout in layouts:
            layout_name = layout.get('name', layout.get('type', 'layout'))
            rn_styles += f"""  {layout_name}Container: {{
    padding: 16,
    marginVertical: 8,
  }},"""
        return rn_styles

    def _map_to_react_native_component(self, component_type: str) -> str:
        """Map web component to React Native component"""
        mapping = {
            'Button': 'TouchableOpacity',
            'Text': 'Text',
            'Input': 'TextInput',
            'Image': 'Image',
            'Card': 'View',
            'Section': 'View',
            'Header': 'View'
        }
        return mapping.get(component_type, 'View')

    def _map_to_react_native_prop(self, prop: str, component_type: str) -> str:
        """Map web prop to React Native prop"""
        mapping = {
            'text': 'title',
            'content': 'children',
            'src': 'source'
        }
        return mapping.get(prop, prop)

    def _map_to_flutter_widget(self, component_type: str) -> str:
        """Map web component to Flutter widget"""
        mapping = {
            'Button': 'ElevatedButton',
            'Text': 'Text',
            'Input': 'TextField',
            'Image': 'Image',
            'Card': 'Card',
            'Section': 'Container',
            'Header': 'AppBar'
        }
        return mapping.get(component_type, 'Container')

    def _get_dart_type(self, value: Any) -> str:
        """Get Dart type for a value"""
        if isinstance(value, str):
            return "String"
        elif isinstance(value, int):
            return "int"
        elif isinstance(value, float):
            return "double"
        elif isinstance(value, bool):
            return "bool"
        elif isinstance(value, list):
            return "List"
        else:
            return "dynamic"

    def _format_dart_value(self, value: Any) -> str:
        """Format value for Dart code"""
        if isinstance(value, str):
            return f'"{value}"'
        elif isinstance(value, bool):
            return str(value).lower()
        else:
            return str(value)

    def _generate_flutter_pubspec(self) -> str:
        """Generate Flutter pubspec.yaml"""
        return """name: generated_app
description: Generated by App Builder

version: 1.0.0+1

environment:
  sdk: ">=2.12.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true"""

    # Backend API generators
    def _generate_express_api(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate Express.js API"""
        components = app_data.get('components', [])

        express_code = f"""const express = require('express');
const cors = require('cors');
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Routes based on components
{self._generate_express_routes(components)}

// Start server
app.listen(PORT, () => {{
  console.log(`Server running on port ${{PORT}}`);
}});"""

        package_json = f"""{{
  "name": "generated-api",
  "version": "1.0.0",
  "description": "Generated API by App Builder",
  "main": "server.js",
  "scripts": {{
    "start": "node server.js",
    "dev": "nodemon server.js"
  }},
  "dependencies": {{
    "express": "^4.18.2",
    "cors": "^2.8.5"
  }},
  "devDependencies": {{
    "nodemon": "^2.0.20"
  }}
}}"""

        return {
            "server.js": express_code,
            "package.json": package_json,
            "README.md": self._generate_readme("Express.js API", options)
        }

    def _generate_django_api(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate Django API"""
        components = app_data.get('components', [])

        # Django views
        views_code = f"""from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json

{self._generate_django_views(components)}"""

        # Django URLs
        urls_code = f"""from django.urls import path
from . import views

urlpatterns = [
{self._generate_django_urls(components)}
]"""

        # Django models
        models_code = f"""from django.db import models

{self._generate_django_models(components)}"""

        return {
            "views.py": views_code,
            "urls.py": urls_code,
            "models.py": models_code,
            "README.md": self._generate_readme("Django API", options)
        }

    def _generate_fastapi_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate FastAPI code"""
        components = app_data.get('components', [])

        fastapi_code = f"""from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional

app = FastAPI(title="Generated API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

{self._generate_fastapi_models(components)}

{self._generate_fastapi_routes(components)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)"""

        requirements = f"""fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0"""

        return {
            "main.py": fastapi_code,
            "requirements.txt": requirements,
            "README.md": self._generate_readme("FastAPI", options)
        }

    # Helper methods for API generation
    def _generate_express_routes(self, components: List[Dict]) -> str:
        """Generate Express.js routes based on components"""
        routes = ""
        for component in components:
            comp_type = component.get('type', '').lower()
            if comp_type in ['form', 'input', 'button']:
                routes += f"""
// Route for {component.get('type', 'component')}
app.get('/api/{comp_type}', (req, res) => {{
  res.json({{ message: 'Hello from {comp_type}' }});
}});

app.post('/api/{comp_type}', (req, res) => {{
  res.json({{ message: 'Data received', data: req.body }});
}});"""
        return routes

    def _generate_django_views(self, components: List[Dict]) -> str:
        """Generate Django views based on components"""
        views = ""
        for component in components:
            comp_type = component.get('type', '').lower()
            if comp_type in ['form', 'input', 'button']:
                views += f"""
@csrf_exempt
@require_http_methods(["GET", "POST"])
def {comp_type}_view(request):
    if request.method == 'GET':
        return JsonResponse({{'message': 'Hello from {comp_type}'}})
    elif request.method == 'POST':
        data = json.loads(request.body)
        return JsonResponse({{'message': 'Data received', 'data': data}})"""
        return views

    def _generate_django_urls(self, components: List[Dict]) -> str:
        """Generate Django URLs based on components"""
        urls = ""
        for component in components:
            comp_type = component.get('type', '').lower()
            if comp_type in ['form', 'input', 'button']:
                urls += f"""    path('api/{comp_type}/', views.{comp_type}_view, name='{comp_type}'),\n"""
        return urls

    def _generate_django_models(self, components: List[Dict]) -> str:
        """Generate Django models based on components"""
        models = ""
        for component in components:
            comp_type = component.get('type', '').lower()
            if comp_type in ['form', 'input']:
                models += f"""
class {comp_type.capitalize()}Data(models.Model):
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{comp_type.capitalize()} - {{self.created_at}}"
"""
        return models

    def _generate_fastapi_models(self, components: List[Dict]) -> str:
        """Generate FastAPI Pydantic models based on components"""
        models = ""
        for component in components:
            comp_type = component.get('type', '').lower()
            if comp_type in ['form', 'input']:
                models += f"""
class {comp_type.capitalize()}Data(BaseModel):
    content: str
    metadata: Optional[dict] = None
"""
        return models

    def _generate_fastapi_routes(self, components: List[Dict]) -> str:
        """Generate FastAPI routes based on components"""
        routes = ""
        for component in components:
            comp_type = component.get('type', '').lower()
            if comp_type in ['form', 'input', 'button']:
                routes += f"""
@app.get("/api/{comp_type}")
async def get_{comp_type}():
    return {{"message": "Hello from {comp_type}"}}

@app.post("/api/{comp_type}")
async def post_{comp_type}(data: {comp_type.capitalize()}Data):
    return {{"message": "Data received", "data": data}}
"""
        return routes
