/**
 * Global teardown for Playwright E2E tests
 * This file runs once after all tests complete
 */

const path = require('path');
const fs = require('fs');

async function globalTeardown(config) {
  console.log('🧹 Starting global teardown for E2E tests...');

  try {
    await cleanupTestData();
    await generateTestReport();
    await cleanupTempFiles();
    await archiveTestResults();
    
    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid masking test failures
  }
}

/**
 * Clean up test data and temporary files
 */
async function cleanupTestData() {
  console.log('🗑️ Cleaning up test data...');

  try {
    // Clean up test uploads directory
    const uploadsDir = path.join(__dirname, '../../../test-uploads');
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir);
      for (const file of files) {
        const filePath = path.join(uploadsDir, file);
        fs.unlinkSync(filePath);
      }
      console.log(`Cleaned up ${files.length} test upload files`);
    }

    // Clean up temporary test files
    const tempDir = path.join(__dirname, '../../../temp');
    if (fs.existsSync(tempDir)) {
      const files = fs.readdirSync(tempDir);
      for (const file of files) {
        if (file.startsWith('test-')) {
          const filePath = path.join(tempDir, file);
          fs.unlinkSync(filePath);
        }
      }
    }

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.warn('⚠️ Test data cleanup failed:', error.message);
  }
}

/**
 * Generate comprehensive test report
 */
async function generateTestReport() {
  console.log('📊 Generating test report...');

  try {
    const reportsDir = path.join(__dirname, '../../../test-results');
    
    // Read test results
    const resultsFile = path.join(reportsDir, 'results.json');
    let testResults = {};
    
    if (fs.existsSync(resultsFile)) {
      const resultsData = fs.readFileSync(resultsFile, 'utf8');
      testResults = JSON.parse(resultsData);
    }

    // Generate summary report
    const summary = generateTestSummary(testResults);
    
    // Write summary to file
    const summaryFile = path.join(reportsDir, 'test-summary.json');
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));

    // Generate HTML report
    const htmlReport = generateHtmlReport(summary);
    const htmlFile = path.join(reportsDir, 'test-summary.html');
    fs.writeFileSync(htmlFile, htmlReport);

    console.log(`✅ Test report generated: ${htmlFile}`);
  } catch (error) {
    console.warn('⚠️ Test report generation failed:', error.message);
  }
}

/**
 * Generate test summary from results
 */
function generateTestSummary(testResults) {
  const summary = {
    timestamp: new Date().toISOString(),
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    duration: 0,
    coverage: null,
    performance: {
      averageLoadTime: 0,
      slowestTest: null,
      fastestTest: null
    },
    browsers: [],
    errors: [],
    warnings: []
  };

  if (testResults.suites) {
    // Process test suites
    testResults.suites.forEach(suite => {
      if (suite.specs) {
        suite.specs.forEach(spec => {
          summary.total++;
          
          if (spec.tests) {
            spec.tests.forEach(test => {
              switch (test.status) {
                case 'passed':
                  summary.passed++;
                  break;
                case 'failed':
                  summary.failed++;
                  if (test.error) {
                    summary.errors.push({
                      test: test.title,
                      error: test.error.message
                    });
                  }
                  break;
                case 'skipped':
                  summary.skipped++;
                  break;
              }

              // Track performance
              if (test.duration) {
                summary.duration += test.duration;
                
                if (!summary.performance.slowestTest || 
                    test.duration > summary.performance.slowestTest.duration) {
                  summary.performance.slowestTest = {
                    title: test.title,
                    duration: test.duration
                  };
                }
                
                if (!summary.performance.fastestTest || 
                    test.duration < summary.performance.fastestTest.duration) {
                  summary.performance.fastestTest = {
                    title: test.title,
                    duration: test.duration
                  };
                }
              }
            });
          }
        });
      }
    });
  }

  // Calculate success rate
  summary.successRate = summary.total > 0 ? 
    Math.round((summary.passed / summary.total) * 100) : 0;

  return summary;
}

/**
 * Generate HTML report
 */
function generateHtmlReport(summary) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E2E Test Report - App Builder</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-card.passed { border-left-color: #28a745; }
        .stat-card.failed { border-left-color: #dc3545; }
        .stat-card.skipped { border-left-color: #ffc107; }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .section {
            padding: 30px;
            border-top: 1px solid #eee;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
        }
        .error-list {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .error-item {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f5c6cb;
        }
        .error-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .performance-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>E2E Test Report</h1>
            <p>App Builder - Generated on ${new Date(summary.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${summary.total}</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat-card passed">
                <div class="stat-number">${summary.passed}</div>
                <div class="stat-label">Passed</div>
            </div>
            <div class="stat-card failed">
                <div class="stat-number">${summary.failed}</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card skipped">
                <div class="stat-number">${summary.skipped}</div>
                <div class="stat-label">Skipped</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${summary.successRate}%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${Math.round(summary.duration / 1000)}s</div>
                <div class="stat-label">Total Duration</div>
            </div>
        </div>

        ${summary.errors.length > 0 ? `
        <div class="section">
            <h2>Failed Tests</h2>
            <div class="error-list">
                ${summary.errors.map(error => `
                    <div class="error-item">
                        <strong>${error.test}</strong><br>
                        <code>${error.error}</code>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        <div class="section">
            <h2>Performance Metrics</h2>
            <div class="performance-grid">
                ${summary.performance.slowestTest ? `
                <div class="performance-card">
                    <h3>Slowest Test</h3>
                    <p><strong>${summary.performance.slowestTest.title}</strong></p>
                    <p>Duration: ${Math.round(summary.performance.slowestTest.duration)}ms</p>
                </div>
                ` : ''}
                ${summary.performance.fastestTest ? `
                <div class="performance-card">
                    <h3>Fastest Test</h3>
                    <p><strong>${summary.performance.fastestTest.title}</strong></p>
                    <p>Duration: ${Math.round(summary.performance.fastestTest.duration)}ms</p>
                </div>
                ` : ''}
            </div>
        </div>

        <div class="footer">
            <p>Generated by App Builder E2E Test Suite</p>
        </div>
    </div>
</body>
</html>
  `;
}

/**
 * Clean up temporary files
 */
async function cleanupTempFiles() {
  console.log('🧽 Cleaning up temporary files...');

  try {
    const tempDirs = [
      path.join(__dirname, '../../../.temp'),
      path.join(__dirname, '../../../tmp'),
    ];

    for (const tempDir of tempDirs) {
      if (fs.existsSync(tempDir)) {
        const files = fs.readdirSync(tempDir);
        for (const file of files) {
          if (file.startsWith('playwright-') || file.startsWith('test-')) {
            const filePath = path.join(tempDir, file);
            try {
              if (fs.statSync(filePath).isDirectory()) {
                fs.rmSync(filePath, { recursive: true, force: true });
              } else {
                fs.unlinkSync(filePath);
              }
            } catch (error) {
              console.warn(`Failed to delete ${filePath}:`, error.message);
            }
          }
        }
      }
    }

    console.log('✅ Temporary files cleanup completed');
  } catch (error) {
    console.warn('⚠️ Temporary files cleanup failed:', error.message);
  }
}

/**
 * Archive test results for historical tracking
 */
async function archiveTestResults() {
  console.log('📦 Archiving test results...');

  try {
    const reportsDir = path.join(__dirname, '../../../test-results');
    const archiveDir = path.join(__dirname, '../../../test-archives');
    
    if (!fs.existsSync(archiveDir)) {
      fs.mkdirSync(archiveDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archiveName = `test-results-${timestamp}`;
    const archivePath = path.join(archiveDir, archiveName);

    if (fs.existsSync(reportsDir)) {
      // Copy reports to archive
      fs.mkdirSync(archivePath, { recursive: true });
      
      const files = fs.readdirSync(reportsDir);
      for (const file of files) {
        const srcPath = path.join(reportsDir, file);
        const destPath = path.join(archivePath, file);
        
        if (fs.statSync(srcPath).isFile()) {
          fs.copyFileSync(srcPath, destPath);
        }
      }

      console.log(`✅ Test results archived to: ${archivePath}`);
    }

    // Clean up old archives (keep last 10)
    const archives = fs.readdirSync(archiveDir)
      .filter(name => name.startsWith('test-results-'))
      .sort()
      .reverse();

    if (archives.length > 10) {
      const toDelete = archives.slice(10);
      for (const archive of toDelete) {
        const archivePath = path.join(archiveDir, archive);
        fs.rmSync(archivePath, { recursive: true, force: true });
      }
      console.log(`Cleaned up ${toDelete.length} old archives`);
    }

  } catch (error) {
    console.warn('⚠️ Test results archiving failed:', error.message);
  }
}

module.exports = globalTeardown;
