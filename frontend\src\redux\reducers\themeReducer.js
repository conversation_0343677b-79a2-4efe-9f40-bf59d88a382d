/**
 * Theme Reducer
 *
 * This reducer handles theme state, including theme creation, updating, and selection.
 */

import * as types from '../actions/types';

// Default themes
const defaultTheme = {
  id: 'default',
  name: 'Default Blue',
  primaryColor: '#2563EB',
  secondaryColor: '#10B981',
  backgroundColor: '#FFFFFF',
  textColor: '#111827',
  fontFamily: 'Inter, sans-serif',
  createdAt: new Date().toISOString()
};

const darkTheme = {
  id: 'dark',
  name: 'Dark Mode',
  primaryColor: '#3B82F6',
  secondaryColor: '#10B981',
  backgroundColor: '#1F2937',
  textColor: '#F9FAFB',
  fontFamily: 'Inter, sans-serif',
  createdAt: new Date().toISOString()
};

const purpleTheme = {
  id: 'purple',
  name: 'Purple Elegance',
  primaryColor: '#8B5CF6',
  secondaryColor: '#EC4899',
  backgroundColor: '#FFFFFF',
  textColor: '#4B5563',
  fontFamily: 'Poppins, sans-serif',
  createdAt: new Date().toISOString()
};

const warmTheme = {
  id: 'warm',
  name: 'Warm Sunset',
  primaryColor: '#F59E0B',
  secondaryColor: '#EF4444',
  backgroundColor: '#FFFBEB',
  textColor: '#78350F',
  fontFamily: 'Roboto, sans-serif',
  createdAt: new Date().toISOString()
};

// Initial state with default themes
const initialState = {
  themes: [defaultTheme, darkTheme, purpleTheme, warmTheme],
  activeTheme: 'default',
  loading: false,
  error: null,
  userPreferences: {
    savedTheme: null,
    autoApplyTheme: true
  }
};

/**
 * Theme reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
const themeReducer = (state = initialState, action) => {
  switch (action.type) {
    // Add a new theme
    case types.ADD_THEME:
      // Check if theme with same ID already exists
      if (state.themes.some(theme => theme.id === action.payload.id)) {
        return {
          ...state,
          error: 'Theme with this ID already exists'
        };
      }

      return {
        ...state,
        themes: [...state.themes, action.payload],
        error: null
      };

    // Update an existing theme
    case types.UPDATE_THEME:
      return {
        ...state,
        themes: state.themes.map(theme =>
          theme.id === action.payload.id ? action.payload : theme
        ),
        error: null
      };

    // Remove a theme
    case types.REMOVE_THEME:
      // Don't allow removing the default theme
      if (action.payload.id === 'default') {
        return {
          ...state,
          error: 'Cannot remove default theme'
        };
      }

      // If removing the active theme, set active theme to default
      const newActiveTheme = state.activeTheme === action.payload.id
        ? 'default'
        : state.activeTheme;

      return {
        ...state,
        themes: state.themes.filter(theme => theme.id !== action.payload.id),
        activeTheme: newActiveTheme,
        error: null
      };

    // Set the active theme
    case types.SET_ACTIVE_THEME:
      // Validate that the theme exists
      const themeExists = state.themes.some(theme => theme.id === action.payload) || action.payload === 'default';

      if (!themeExists) {
        return {
          ...state,
          error: 'Theme does not exist'
        };
      }

      // If auto-apply is enabled, also save as user preference
      return {
        ...state,
        activeTheme: action.payload,
        userPreferences: state.userPreferences.autoApplyTheme ?
          { ...state.userPreferences, savedTheme: action.payload } :
          state.userPreferences,
        error: null
      };

    // Save user theme preference
    case types.SAVE_USER_THEME_PREFERENCE:
      return {
        ...state,
        userPreferences: {
          ...state.userPreferences,
          savedTheme: action.payload
        },
        error: null
      };

    // Toggle auto-apply theme setting
    case types.TOGGLE_AUTO_APPLY_THEME:
      return {
        ...state,
        userPreferences: {
          ...state.userPreferences,
          autoApplyTheme: !state.userPreferences.autoApplyTheme
        },
        error: null
      };

    default:
      return state;
  }
};

export default themeReducer;
