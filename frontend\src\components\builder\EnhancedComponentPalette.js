import React, { useState, useMemo, useRef } from 'react';
import { Typo<PERSON>, But<PERSON>, Divider, Tooltip, Input, Card, Badge, Space, Collapse, Switch, message } from 'antd';
import {
  FormOutlined,
  TableOutlined,
  Bar<PERSON>hartOutlined,
  PictureOutlined,
  FontSizeOutlined,
  AppstoreOutlined,
  OrderedListOutlined,
  CheckSquareOutlined,
  CalendarOutlined,
  SlidersFilled,
  TagsOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  BarsOutlined,
  LayoutOutlined,
  SearchOutlined,
  DragOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  DownOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  BulbOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';
import { ComponentCombinationsList } from '../ai/ComponentCombinationCard';

const { Search } = Input;
const { Text, Title } = Typography;
const { Panel } = Collapse;

// Styled components for enhanced visual design
const PaletteContainer = styled.div`
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const PaletteHeader = styled.div`
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .ant-typography {
    color: white !important;
    margin-bottom: 8px;
  }
`;

const SearchContainer = styled.div`
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
`;

const ComponentCard = styled(Card)`
  margin: 4px;
  cursor: grab;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: ${props => props.isDragging ? '#e6f7ff' : '#fff'};
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #1890ff;
  }
  
  &:active {
    cursor: grabbing;
    transform: scale(0.98);
  }
  
  .ant-card-body {
    padding: 12px;
    text-align: center;
  }
`;

const ComponentIcon = styled.div`
  font-size: 24px;
  color: #1890ff;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(24, 144, 255, 0.1);
  margin: 0 auto 8px;
  transition: all 0.3s ease;
  
  ${ComponentCard}:hover & {
    background: rgba(24, 144, 255, 0.2);
    transform: scale(1.1);
  }
`;

const ComponentLabel = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
`;

const ComponentDescription = styled.div`
  font-size: 10px;
  color: #666;
  line-height: 1.2;
`;

const CategoryHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #f0f2f5;
  border-bottom: 1px solid #d9d9d9;
  font-weight: 600;
  color: #333;
`;

const ComponentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  padding: 16px;
`;

const DragIndicator = styled.div`
  position: absolute;
  top: 4px;
  right: 4px;
  color: #bbb;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  ${ComponentCard}:hover & {
    opacity: 1;
  }
`;

const EnhancedComponentPalette = ({
  onAddComponent,
  onDragStart,
  onDragEnd,
  components = [],
  selectedComponent = null,
  showAISuggestions = true
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState(['AI Suggestions', 'Layout', 'Basic Components']);
  const [showDescriptions, setShowDescriptions] = useState(true);
  const [draggedComponent, setDraggedComponent] = useState(null);
  const dragPreviewRef = useRef(null);

  // AI suggestions hook
  const {
    suggestions,
    loading,
    applyComponentCombination,
    hasLayoutSuggestions,
    hasCombinationSuggestions
  } = useAIDesignSuggestions({
    autoRefresh: true,
    context: { selectedComponent }
  });

  // Handle AI component combination application
  const handleApplyAICombination = async (suggestion) => {
    try {
      const success = applyComponentCombination(suggestion);
      if (success) {
        // Also add components via the parent callback
        if (suggestion.missing_components) {
          suggestion.missing_components.forEach(componentType => {
            onAddComponent(componentType);
          });
        }
        message.success(`Applied AI suggestion: ${suggestion.name}`);
        return true;
      }
    } catch (error) {
      console.error('Error applying AI combination:', error);
      message.error('Failed to apply AI suggestion');
    }
    return false;
  };

  // Get smart component suggestions based on current selection
  const getSmartSuggestions = () => {
    if (!selectedComponent) return [];

    const componentType = selectedComponent.type;
    const currentTypes = components.map(c => c.type);

    const smartSuggestions = [];

    // Context-aware suggestions
    if (componentType === 'button' && !currentTypes.includes('form')) {
      smartSuggestions.push({
        type: 'form',
        reason: 'Buttons often work with forms',
        priority: 'high',
        icon: <FormOutlined />,
        label: 'Form'
      });
    }

    if (componentType === 'form' && !currentTypes.includes('input')) {
      smartSuggestions.push({
        type: 'input',
        reason: 'Forms need input fields',
        priority: 'high',
        icon: <FormOutlined />,
        label: 'Input'
      });
    }

    if (componentType === 'text' && !currentTypes.includes('image')) {
      smartSuggestions.push({
        type: 'image',
        reason: 'Text and images work well together',
        priority: 'medium',
        icon: <PictureOutlined />,
        label: 'Image'
      });
    }

    if (componentType === 'card' && !currentTypes.includes('button')) {
      smartSuggestions.push({
        type: 'button',
        reason: 'Cards often need action buttons',
        priority: 'medium',
        icon: <AppstoreOutlined />,
        label: 'Button'
      });
    }

    return smartSuggestions;
  };

  const smartSuggestions = getSmartSuggestions();

  // Enhanced component data with descriptions and usage hints
  const componentGroups = [
    // AI Suggestions Group (conditionally added)
    ...(showAISuggestions && (hasCombinationSuggestions || smartSuggestions.length > 0) ? [{
      title: 'AI Suggestions',
      description: 'Smart component recommendations based on your current app',
      color: '#1890ff',
      isAI: true,
      components: [
        // Smart contextual suggestions
        ...smartSuggestions.map(suggestion => ({
          type: suggestion.type,
          icon: suggestion.icon,
          label: suggestion.label,
          description: suggestion.reason,
          usage: `Recommended for your ${selectedComponent?.type}`,
          tags: ['ai', 'smart', 'contextual'],
          priority: suggestion.priority,
          isAISuggestion: true
        }))
      ]
    }] : []),
    {
      title: 'Layout',
      description: 'Structural components for organizing content',
      color: '#52c41a',
      components: [
        {
          type: 'header',
          icon: <FontSizeOutlined />,
          label: 'Header',
          description: 'Page or section header with title and navigation',
          usage: 'Use for page titles, navigation bars, or section headers',
          tags: ['layout', 'navigation', 'title']
        },
        {
          type: 'section',
          icon: <LayoutOutlined />,
          label: 'Section',
          description: 'Container for grouping related content',
          usage: 'Organize content into logical sections',
          tags: ['layout', 'container', 'organization']
        },
        {
          type: 'card',
          icon: <CreditCardOutlined />,
          label: 'Card',
          description: 'Flexible content container with optional header and footer',
          usage: 'Display content in a clean, contained format',
          tags: ['layout', 'container', 'content']
        },
        {
          type: 'tabs',
          icon: <BarsOutlined />,
          label: 'Tabs',
          description: 'Tabbed interface for organizing content',
          usage: 'Switch between different views or content sections',
          tags: ['layout', 'navigation', 'organization']
        },
        {
          type: 'divider',
          icon: <BarsOutlined />,
          label: 'Divider',
          description: 'Visual separator between content sections',
          usage: 'Separate content sections visually',
          tags: ['layout', 'separator', 'visual']
        },
      ]
    },
    {
      title: 'Basic Components',
      description: 'Essential UI elements for content and interaction',
      color: '#1890ff',
      components: [
        {
          type: 'text',
          icon: <FileTextOutlined />,
          label: 'Text',
          description: 'Formatted text content with typography options',
          usage: 'Display paragraphs, headings, and formatted text',
          tags: ['content', 'text', 'typography']
        },
        {
          type: 'button',
          icon: <AppstoreOutlined />,
          label: 'Button',
          description: 'Interactive button for user actions',
          usage: 'Trigger actions, submit forms, or navigate',
          tags: ['interaction', 'action', 'click']
        },
        {
          type: 'image',
          icon: <PictureOutlined />,
          label: 'Image',
          description: 'Display images with responsive sizing',
          usage: 'Show photos, illustrations, or graphics',
          tags: ['media', 'visual', 'content']
        },
        {
          type: 'list',
          icon: <OrderedListOutlined />,
          label: 'List',
          description: 'Ordered or unordered list of items',
          usage: 'Display collections of related items',
          tags: ['content', 'organization', 'items']
        },
        {
          type: 'tag',
          icon: <TagsOutlined />,
          label: 'Tag',
          description: 'Small label for categorization or status',
          usage: 'Label content, show status, or categorize',
          tags: ['label', 'status', 'category']
        },
      ]
    },
    {
      title: 'Form Components',
      description: 'Interactive elements for user input and data collection',
      color: '#722ed1',
      components: [
        {
          type: 'form',
          icon: <FormOutlined />,
          label: 'Form',
          description: 'Container for form fields with validation',
          usage: 'Collect user input with validation and submission',
          tags: ['input', 'validation', 'data']
        },
        {
          type: 'input',
          icon: <FormOutlined />,
          label: 'Input',
          description: 'Text input field for user data entry',
          usage: 'Collect text, numbers, or other typed input',
          tags: ['input', 'text', 'data']
        },
        {
          type: 'select',
          icon: <FormOutlined />,
          label: 'Select',
          description: 'Dropdown selection from predefined options',
          usage: 'Choose from a list of predefined options',
          tags: ['input', 'selection', 'dropdown']
        },
        {
          type: 'checkbox',
          icon: <CheckSquareOutlined />,
          label: 'Checkbox',
          description: 'Boolean input for yes/no or multiple selections',
          usage: 'Select multiple options or toggle settings',
          tags: ['input', 'boolean', 'selection']
        },
        {
          type: 'datepicker',
          icon: <CalendarOutlined />,
          label: 'Date Picker',
          description: 'Calendar interface for date selection',
          usage: 'Select dates, date ranges, or schedule events',
          tags: ['input', 'date', 'calendar']
        },
        {
          type: 'slider',
          icon: <SlidersFilled />,
          label: 'Slider',
          description: 'Range input with visual slider interface',
          usage: 'Select numeric values within a range',
          tags: ['input', 'range', 'numeric']
        },
      ]
    },
    {
      title: 'Data Components',
      description: 'Components for displaying and visualizing data',
      color: '#fa8c16',
      components: [
        {
          type: 'table',
          icon: <TableOutlined />,
          label: 'Table',
          description: 'Structured data display with sorting and filtering',
          usage: 'Display tabular data with advanced features',
          tags: ['data', 'table', 'structured']
        },
        {
          type: 'chart',
          icon: <BarChartOutlined />,
          label: 'Chart',
          description: 'Visual data representation with multiple chart types',
          usage: 'Visualize data trends and comparisons',
          tags: ['data', 'visualization', 'analytics']
        },
        {
          type: 'statistic',
          icon: <BarChartOutlined />,
          label: 'Statistic',
          description: 'Highlighted numeric data with formatting',
          usage: 'Display key metrics and KPIs prominently',
          tags: ['data', 'metrics', 'numbers']
        },
      ]
    }
  ];

  // Filter components based on search term
  const filteredGroups = useMemo(() => {
    if (!searchTerm) return componentGroups;

    return componentGroups.map(group => ({
      ...group,
      components: group.components.filter(component =>
        component.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    })).filter(group => group.components.length > 0);
  }, [searchTerm]);

  const handleDragStart = (e, component) => {
    setDraggedComponent(component);

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.type,
      label: component.label,
      source: 'palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';

    // Create drag preview
    if (dragPreviewRef.current) {
      const dragImage = dragPreviewRef.current.cloneNode(true);
      dragImage.style.transform = 'rotate(5deg)';
      dragImage.style.opacity = '0.8';
      e.dataTransfer.setDragImage(dragImage, 60, 30);
    }

    if (onDragStart) {
      onDragStart(component);
    }
  };

  const handleDragEnd = (e) => {
    setDraggedComponent(null);
    if (onDragEnd) {
      onDragEnd();
    }
  };

  const handleCategoryToggle = (categoryTitle) => {
    setExpandedCategories(prev =>
      prev.includes(categoryTitle)
        ? prev.filter(cat => cat !== categoryTitle)
        : [...prev, categoryTitle]
    );
  };

  return (
    <PaletteContainer>
      <PaletteHeader>
        <Title level={5} style={{ margin: 0, color: 'white' }}>Component Palette</Title>
        <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
          Drag components to the canvas or click to add
        </Text>
      </PaletteHeader>

      <SearchContainer>
        <Search
          placeholder="Search components..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          prefix={<SearchOutlined />}
          allowClear
          style={{ marginBottom: 8 }}
        />
        <Space>
          <Text style={{ fontSize: 12 }}>Show descriptions:</Text>
          <Switch
            size="small"
            checked={showDescriptions}
            onChange={setShowDescriptions}
          />
        </Space>
      </SearchContainer>

      {filteredGroups.map((group) => (
        <div key={group.title}>
          <CategoryHeader onClick={() => handleCategoryToggle(group.title)}>
            <Space>
              <div
                style={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: group.color
                }}
              />
              {group.isAI && <RobotOutlined style={{ color: group.color }} />}
              <span>{group.title}</span>
              <Badge count={group.components.length} size="small" />
              {group.isAI && loading.combinations && (
                <Badge status="processing" />
              )}
            </Space>
            <DownOutlined
              style={{
                transform: expandedCategories.includes(group.title) ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.3s ease'
              }}
            />
          </CategoryHeader>

          {expandedCategories.includes(group.title) && (
            <>
              {/* AI-powered combination suggestions for AI group */}
              {group.isAI && hasCombinationSuggestions && (
                <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
                  <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: '8px' }}>
                    <BulbOutlined /> AI-powered component combinations:
                  </Text>
                  <ComponentCombinationsList
                    suggestions={suggestions.combinations}
                    onApply={handleApplyAICombination}
                    loading={loading.combinations}
                    compact={true}
                    showScore={false}
                    showPreview={false}
                    emptyMessage="No AI combinations available"
                  />
                </div>
              )}

              <ComponentGrid>
                {group.components.map((component) => (
                  <ComponentCard
                    key={component.type}
                    size="small"
                    hoverable
                    isDragging={draggedComponent?.type === component.type}
                    draggable
                    onDragStart={(e) => handleDragStart(e, component)}
                    onDragEnd={handleDragEnd}
                    onClick={() => onAddComponent(component.type)}
                    ref={draggedComponent?.type === component.type ? dragPreviewRef : null}
                    style={{
                      border: component.isAISuggestion ? '2px solid #52c41a' : undefined,
                      background: component.isAISuggestion ? '#f6ffed' : undefined
                    }}
                  >
                    <DragIndicator>
                      <DragOutlined />
                    </DragIndicator>

                    {component.isAISuggestion && (
                      <div style={{
                        position: 'absolute',
                        top: 4,
                        right: 4,
                        background: component.priority === 'high' ? '#52c41a' : '#1890ff',
                        color: 'white',
                        borderRadius: '50%',
                        width: 16,
                        height: 16,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: 10
                      }}>
                        <ThunderboltOutlined />
                      </div>
                    )}

                    <ComponentIcon style={{
                      background: component.isAISuggestion
                        ? (component.priority === 'high' ? 'rgba(82, 196, 26, 0.1)' : 'rgba(24, 144, 255, 0.1)')
                        : undefined
                    }}>
                      {component.icon}
                    </ComponentIcon>

                    <ComponentLabel>{component.label}</ComponentLabel>

                    {showDescriptions && (
                      <ComponentDescription>
                        {component.description}
                      </ComponentDescription>
                    )}

                    <Tooltip
                      title={
                        <div>
                          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                            {component.label}
                            {component.isAISuggestion && (
                              <Badge
                                count="AI"
                                style={{
                                  backgroundColor: '#52c41a',
                                  marginLeft: 8
                                }}
                              />
                            )}
                          </div>
                          <div style={{ marginBottom: 8 }}>
                            {component.description}
                          </div>
                          <div style={{ fontSize: 11, color: '#ccc' }}>
                            <strong>Usage:</strong> {component.usage}
                          </div>
                          <div style={{ fontSize: 11, color: '#ccc', marginTop: 4 }}>
                            <strong>Tags:</strong> {component.tags.join(', ')}
                          </div>
                        </div>
                      }
                      placement="right"
                    >
                      <InfoCircleOutlined
                        style={{
                          position: 'absolute',
                          top: 4,
                          left: 4,
                          fontSize: 10,
                          color: component.isAISuggestion ? '#52c41a' : '#bbb',
                          opacity: 0.7
                        }}
                      />
                    </Tooltip>
                  </ComponentCard>
                ))}
              </ComponentGrid>
            </>
          )}
        </div>
      ))}

      {filteredGroups.length === 0 && (
        <div style={{ padding: 32, textAlign: 'center', color: '#999' }}>
          <SearchOutlined style={{ fontSize: 24, marginBottom: 8 }} />
          <div>No components found matching "{searchTerm}"</div>
          <Button
            type="link"
            size="small"
            onClick={() => setSearchTerm('')}
            style={{ padding: 0, marginTop: 8 }}
          >
            Clear search
          </Button>
        </div>
      )}
    </PaletteContainer>
  );
};

export default EnhancedComponentPalette;
