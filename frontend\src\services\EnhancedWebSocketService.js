/**
 * Enhanced WebSocket Service
 * 
 * This service provides a robust WebSocket connection with automatic reconnection,
 * message queuing, and error handling.
 */

import { getWebSocketUrl } from '../utils/websocket';
import { handleError, ErrorTypes } from '../utils/errorHandling';

// WebSocket connection states
export const ConnectionState = {
  CONNECTING: 'CONNECTING',
  OPEN: 'OPEN',
  CLOSING: 'CLOSING',
  CLOSED: 'CLOSED',
  RECONNECTING: 'RECONNECTING',
  FAILED: 'FAILED'
};

class EnhancedWebSocketService {
  constructor(options = {}) {
    // Configuration
    this.baseUrl = options.baseUrl || '';
    this.endpoint = options.endpoint || '/app_builder/';
    this.debug = options.debug || false;
    this.autoReconnect = options.autoReconnect !== false;
    this.reconnectInterval = options.reconnectInterval || 1000;
    this.maxReconnectInterval = options.maxReconnectInterval || 30000;
    this.reconnectDecay = options.reconnectDecay || 1.5;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.heartbeatInterval = options.heartbeatInterval || 30000;
    this.connectionTimeout = options.connectionTimeout || 5000;
    this.queueOfflineMessages = options.queueOfflineMessages !== false;
    
    // State
    this.socket = null;
    this.connectionState = ConnectionState.CLOSED;
    this.reconnectAttempts = 0;
    this.reconnectTimeoutId = null;
    this.heartbeatTimeoutId = null;
    this.connectionTimeoutId = null;
    this.messageQueue = [];
    this.listeners = {
      open: [],
      message: [],
      close: [],
      error: [],
      stateChange: []
    };
    
    // Bind methods to preserve 'this' context
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.send = this.send.bind(this);
    this.reconnect = this.reconnect.bind(this);
    this.startHeartbeat = this.startHeartbeat.bind(this);
    this.stopHeartbeat = this.stopHeartbeat.bind(this);
    this.handleOpen = this.handleOpen.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
    
    // Initialize
    this._debug('EnhancedWebSocketService initialized with options:', options);
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise<void>} Promise that resolves when connected
   */
  connect() {
    return new Promise((resolve, reject) => {
      // If already connected or connecting, return
      if (this.socket && (this.connectionState === ConnectionState.OPEN || this.connectionState === ConnectionState.CONNECTING)) {
        this._debug('Already connected or connecting');
        if (this.connectionState === ConnectionState.OPEN) {
          resolve();
        }
        return;
      }
      
      // Update state
      this._updateState(ConnectionState.CONNECTING);
      
      // Clear any existing timeouts
      this._clearTimeouts();
      
      try {
        // Get WebSocket URL
        const url = this._getWebSocketUrl();
        this._debug('Connecting to WebSocket at', url);
        
        // Create WebSocket
        this.socket = new WebSocket(url);
        
        // Set up event handlers
        this.socket.onopen = this.handleOpen(resolve);
        this.socket.onmessage = this.handleMessage;
        this.socket.onclose = this.handleClose(reject);
        this.socket.onerror = this.handleError(reject);
        
        // Set connection timeout
        this.connectionTimeoutId = setTimeout(() => {
          this._debug('Connection timeout');
          if (this.connectionState === ConnectionState.CONNECTING) {
            this._updateState(ConnectionState.FAILED);
            this.socket.close();
            reject(new Error('Connection timeout'));
          }
        }, this.connectionTimeout);
      } catch (error) {
        this._debug('Error creating WebSocket:', error);
        this._updateState(ConnectionState.FAILED);
        reject(error);
      }
    });
  }
  
  /**
   * Disconnect from the WebSocket server
   */
  disconnect() {
    this._debug('Disconnecting');
    
    // Clear timeouts
    this._clearTimeouts();
    
    // Update state
    this._updateState(ConnectionState.CLOSING);
    
    // Close socket
    if (this.socket) {
      this.socket.close();
    }
  }
  
  /**
   * Send a message to the WebSocket server
   * @param {Object|string} message - Message to send
   * @returns {Promise<void>} Promise that resolves when the message is sent
   */
  send(message) {
    return new Promise((resolve, reject) => {
      // If not connected, queue message if enabled
      if (this.connectionState !== ConnectionState.OPEN) {
        if (this.queueOfflineMessages) {
          this._debug('Queueing message:', message);
          this.messageQueue.push({ message, resolve, reject });
          
          // Try to connect if not already connecting
          if (this.connectionState === ConnectionState.CLOSED || this.connectionState === ConnectionState.FAILED) {
            this.connect().catch(error => {
              this._debug('Failed to connect when sending message:', error);
            });
          }
          
          return;
        } else {
          reject(new Error('Not connected'));
          return;
        }
      }
      
      try {
        // Convert message to string if needed
        const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
        
        // Send message
        this.socket.send(messageStr);
        this._debug('Message sent:', message);
        resolve();
      } catch (error) {
        this._debug('Error sending message:', error);
        reject(error);
      }
    });
  }
  
  /**
   * Add an event listener
   * @param {string} event - Event name (open, message, close, error, stateChange)
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
  }
  
  /**
   * Remove an event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }
  
  /**
   * Get the current connection state
   * @returns {string} Connection state
   */
  getState() {
    return this.connectionState;
  }
  
  /**
   * Check if connected
   * @returns {boolean} Whether connected
   */
  isConnected() {
    return this.connectionState === ConnectionState.OPEN;
  }
  
  /**
   * Handle WebSocket open event
   * @param {Function} resolve - Promise resolve function
   * @returns {Function} Event handler
   */
  handleOpen(resolve) {
    return () => {
      this._debug('WebSocket connected');
      
      // Clear connection timeout
      if (this.connectionTimeoutId) {
        clearTimeout(this.connectionTimeoutId);
        this.connectionTimeoutId = null;
      }
      
      // Update state
      this._updateState(ConnectionState.OPEN);
      
      // Reset reconnect attempts
      this.reconnectAttempts = 0;
      
      // Start heartbeat
      this.startHeartbeat();
      
      // Process message queue
      this._processMessageQueue();
      
      // Notify listeners
      this._notifyListeners('open', {});
      
      // Resolve promise
      resolve();
    };
  }
  
  /**
   * Handle WebSocket message event
   * @param {MessageEvent} event - Message event
   */
  handleMessage(event) {
    this._debug('WebSocket message received:', event.data);
    
    try {
      // Parse message if it's JSON
      let data = event.data;
      try {
        data = JSON.parse(event.data);
      } catch (e) {
        // Not JSON, use as is
      }
      
      // Notify listeners
      this._notifyListeners('message', { data });
    } catch (error) {
      this._debug('Error handling message:', error);
    }
  }
  
  /**
   * Handle WebSocket close event
   * @param {Function} reject - Promise reject function
   * @returns {Function} Event handler
   */
  handleClose(reject) {
    return (event) => {
      this._debug('WebSocket closed:', event);
      
      // Clear timeouts
      this._clearTimeouts();
      
      // Update state
      this._updateState(ConnectionState.CLOSED);
      
      // Notify listeners
      this._notifyListeners('close', { event });
      
      // Reconnect if enabled
      if (this.autoReconnect && !event.wasClean) {
        this.reconnect();
      } else {
        // Reject promise if still connecting
        if (this.connectionState === ConnectionState.CONNECTING) {
          reject(new Error('Connection closed'));
        }
      }
    };
  }
  
  /**
   * Handle WebSocket error event
   * @param {Function} reject - Promise reject function
   * @returns {Function} Event handler
   */
  handleError(reject) {
    return (event) => {
      this._debug('WebSocket error:', event);
      
      // Create error object
      const error = new Error('WebSocket error');
      error.event = event;
      
      // Notify listeners
      this._notifyListeners('error', { error });
      
      // Reject promise if still connecting
      if (this.connectionState === ConnectionState.CONNECTING) {
        reject(error);
      }
    };
  }
  
  /**
   * Reconnect to the WebSocket server
   */
  reconnect() {
    // If already reconnecting, return
    if (this.connectionState === ConnectionState.RECONNECTING) {
      return;
    }
    
    // Update state
    this._updateState(ConnectionState.RECONNECTING);
    
    // Increment reconnect attempts
    this.reconnectAttempts++;
    
    // Check if max reconnect attempts reached
    if (this.reconnectAttempts > this.maxReconnectAttempts) {
      this._debug('Max reconnect attempts reached');
      this._updateState(ConnectionState.FAILED);
      return;
    }
    
    // Calculate reconnect delay with exponential backoff
    const delay = Math.min(
      this.reconnectInterval * Math.pow(this.reconnectDecay, this.reconnectAttempts - 1),
      this.maxReconnectInterval
    );
    
    this._debug(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    // Set reconnect timeout
    this.reconnectTimeoutId = setTimeout(() => {
      this.connect().catch(error => {
        this._debug('Reconnect failed:', error);
      });
    }, delay);
  }
  
  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this._debug('Starting heartbeat');
    
    // Clear any existing heartbeat
    this.stopHeartbeat();
    
    // Set heartbeat interval
    this.heartbeatTimeoutId = setInterval(() => {
      if (this.connectionState === ConnectionState.OPEN) {
        this._debug('Sending heartbeat');
        this.send({ type: 'ping', timestamp: Date.now() }).catch(error => {
          this._debug('Error sending heartbeat:', error);
        });
      }
    }, this.heartbeatInterval);
  }
  
  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatTimeoutId) {
      clearInterval(this.heartbeatTimeoutId);
      this.heartbeatTimeoutId = null;
    }
  }
  
  /**
   * Process message queue
   * @private
   */
  _processMessageQueue() {
    if (this.messageQueue.length === 0) {
      return;
    }
    
    this._debug(`Processing message queue (${this.messageQueue.length} messages)`);
    
    // Process all messages in the queue
    const queue = [...this.messageQueue];
    this.messageQueue = [];
    
    queue.forEach(({ message, resolve, reject }) => {
      this.send(message).then(resolve).catch(reject);
    });
  }
  
  /**
   * Update connection state
   * @param {string} state - New state
   * @private
   */
  _updateState(state) {
    if (this.connectionState === state) {
      return;
    }
    
    this._debug(`State changed: ${this.connectionState} -> ${state}`);
    
    const oldState = this.connectionState;
    this.connectionState = state;
    
    // Notify listeners
    this._notifyListeners('stateChange', { oldState, newState: state });
  }
  
  /**
   * Notify event listeners
   * @param {string} event - Event name
   * @param {Object} data - Event data
   * @private
   */
  _notifyListeners(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }
  
  /**
   * Clear all timeouts
   * @private
   */
  _clearTimeouts() {
    // Clear connection timeout
    if (this.connectionTimeoutId) {
      clearTimeout(this.connectionTimeoutId);
      this.connectionTimeoutId = null;
    }
    
    // Clear reconnect timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }
    
    // Clear heartbeat
    this.stopHeartbeat();
  }
  
  /**
   * Get WebSocket URL
   * @returns {string} WebSocket URL
   * @private
   */
  _getWebSocketUrl() {
    if (this.baseUrl) {
      return `${this.baseUrl}${this.endpoint}`;
    }
    
    return getWebSocketUrl(this.endpoint.replace(/^\//, '').replace(/\/$/, ''));
  }
  
  /**
   * Log debug message
   * @param {...any} args - Arguments to log
   * @private
   */
  _debug(...args) {
    if (this.debug) {
      console.log('[EnhancedWebSocketService]', ...args);
    }
  }
  
  /**
   * Get instance (singleton)
   * @param {Object} options - Options
   * @returns {EnhancedWebSocketService} Instance
   * @static
   */
  static getInstance(options = {}) {
    if (!EnhancedWebSocketService.instance) {
      EnhancedWebSocketService.instance = new EnhancedWebSocketService(options);
    }
    
    return EnhancedWebSocketService.instance;
  }
}

export default EnhancedWebSocketService;
