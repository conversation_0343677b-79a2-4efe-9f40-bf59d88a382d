/**
 * Test script to verify service worker API fix
 * 
 * This script tests that:
 * 1. The service worker is registered
 * 2. API requests are not intercepted by the service worker
 * 3. API requests go through the proxy correctly
 */

async function testServiceWorkerFix() {
  console.log('🧪 Testing Service Worker API Fix...');
  
  // Test 1: Check if service worker is registered
  console.log('\n1. Checking Service Worker registration...');
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration) {
        console.log('✅ Service Worker is registered:', registration.scope);
      } else {
        console.log('❌ Service Worker is not registered');
        return false;
      }
    } catch (error) {
      console.error('❌ Error checking Service Worker:', error);
      return false;
    }
  } else {
    console.log('❌ Service Worker not supported');
    return false;
  }

  // Test 2: Test API request (should go through proxy, not service worker)
  console.log('\n2. Testing API request routing...');
  try {
    const response = await fetch('/api/status/', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log('📡 API Response Status:', response.status);
    console.log('📡 API Response Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API request successful:', data);
      return true;
    } else {
      console.log('⚠️ API request returned non-200 status:', response.status);
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ API request failed:', error);
    return false;
  }
}

// Test 3: Check service worker fetch event handling
async function testServiceWorkerFetchHandling() {
  console.log('\n3. Testing Service Worker fetch event handling...');
  
  // Listen for service worker messages
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      console.log('📨 Service Worker message:', event.data);
    });
    
    // Test a non-API request (should be handled by service worker)
    try {
      const response = await fetch('/manifest.json');
      console.log('📄 Manifest request status:', response.status);
      
      // Test an API request (should NOT be handled by service worker)
      const apiResponse = await fetch('/api/health/');
      console.log('🏥 Health API request status:', apiResponse.status);
      
      return true;
    } catch (error) {
      console.error('❌ Fetch test failed:', error);
      return false;
    }
  }
  
  return false;
}

// Run the tests
async function runAllTests() {
  console.log('🚀 Starting Service Worker Fix Tests...');
  
  const test1 = await testServiceWorkerFix();
  const test2 = await testServiceWorkerFetchHandling();
  
  console.log('\n📊 Test Results:');
  console.log('Service Worker & API Test:', test1 ? '✅ PASS' : '❌ FAIL');
  console.log('Fetch Handling Test:', test2 ? '✅ PASS' : '❌ FAIL');
  
  if (test1 && test2) {
    console.log('\n🎉 All tests passed! Service Worker fix is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the service worker configuration.');
  }
}

// Export for use in browser console or as module
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testServiceWorkerFix, testServiceWorkerFetchHandling, runAllTests };
} else {
  // Run tests immediately if in browser
  window.testServiceWorkerFix = runAllTests;
  console.log('🔧 Service Worker test functions loaded. Run testServiceWorkerFix() to start tests.');
}
