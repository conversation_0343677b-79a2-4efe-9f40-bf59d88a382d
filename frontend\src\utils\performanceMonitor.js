/**
 * Performance monitoring utility for the App Builder application.
 * 
 * This utility provides functions to measure and report performance metrics.
 */

// Store for performance marks and measures
const performanceStore = {
  marks: {},
  measures: {},
  resources: [],
  errors: [],
};

/**
 * Initialize the performance monitor.
 * 
 * @returns {Object} The performance monitor API.
 */
export const initPerformanceMonitor = () => {
  // Check if the Performance API is available
  if (!window.performance) {
    console.warn('Performance API is not available in this browser.');
    return null;
  }

  // Clear existing performance data
  performanceStore.marks = {};
  performanceStore.measures = {};
  performanceStore.resources = [];
  performanceStore.errors = [];

  // Set up resource timing buffer
  if (window.performance.setResourceTimingBufferSize) {
    window.performance.setResourceTimingBufferSize(500);
  }

  // Set up observers
  setupObservers();

  return {
    mark,
    measure,
    getMarks,
    getMeasures,
    getResourceTimings,
    getErrors,
    clearMarks,
    clearMeasures,
    clearResourceTimings,
    clearErrors,
    getPerformanceReport,
  };
};

/**
 * Set up performance observers.
 */
const setupObservers = () => {
  // Set up resource timing observer
  if (window.PerformanceObserver) {
    try {
      // Resource timing observer
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        performanceStore.resources = [...performanceStore.resources, ...entries];
      });
      resourceObserver.observe({ entryTypes: ['resource'] });

      // Long task observer
      const longTaskObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          console.warn('Long task detected:', entry);
          performanceStore.errors.push({
            type: 'long-task',
            message: `Long task detected: ${entry.name} (${entry.duration}ms)`,
            timestamp: new Date().toISOString(),
            details: entry,
          });
        });
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });

      // Paint timing observer
      const paintObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          mark(`paint-${entry.name}`, entry.startTime);
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });

      // First Input Delay observer
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          mark(`fid-${entry.name}`, entry.startTime);
          if (entry.duration > 100) {
            performanceStore.errors.push({
              type: 'fid',
              message: `High First Input Delay: ${entry.duration}ms`,
              timestamp: new Date().toISOString(),
              details: entry,
            });
          }
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Layout Shift observer
      const lsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.value > 0.1) {
            performanceStore.errors.push({
              type: 'layout-shift',
              message: `High Layout Shift: ${entry.value}`,
              timestamp: new Date().toISOString(),
              details: entry,
            });
          }
        });
      });
      lsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Error setting up PerformanceObserver:', error);
    }
  }
};

/**
 * Create a performance mark.
 * 
 * @param {string} name - The name of the mark.
 * @param {number} [startTime] - Optional start time for the mark.
 */
const mark = (name, startTime) => {
  try {
    if (startTime !== undefined) {
      window.performance.mark(name, { startTime });
    } else {
      window.performance.mark(name);
    }
    
    // Store the mark
    const marks = window.performance.getEntriesByName(name, 'mark');
    if (marks.length > 0) {
      performanceStore.marks[name] = marks[marks.length - 1];
    }
  } catch (error) {
    console.warn(`Error creating mark "${name}":`, error);
  }
};

/**
 * Create a performance measure between two marks.
 * 
 * @param {string} name - The name of the measure.
 * @param {string} startMark - The name of the start mark.
 * @param {string} endMark - The name of the end mark.
 */
const measure = (name, startMark, endMark) => {
  try {
    window.performance.measure(name, startMark, endMark);
    
    // Store the measure
    const measures = window.performance.getEntriesByName(name, 'measure');
    if (measures.length > 0) {
      performanceStore.measures[name] = measures[measures.length - 1];
    }
  } catch (error) {
    console.warn(`Error creating measure "${name}":`, error);
  }
};

/**
 * Get all performance marks.
 * 
 * @returns {Object} All performance marks.
 */
const getMarks = () => {
  return { ...performanceStore.marks };
};

/**
 * Get all performance measures.
 * 
 * @returns {Object} All performance measures.
 */
const getMeasures = () => {
  return { ...performanceStore.measures };
};

/**
 * Get all resource timings.
 * 
 * @returns {Array} All resource timings.
 */
const getResourceTimings = () => {
  return [...performanceStore.resources];
};

/**
 * Get all performance errors.
 * 
 * @returns {Array} All performance errors.
 */
const getErrors = () => {
  return [...performanceStore.errors];
};

/**
 * Clear all performance marks.
 */
const clearMarks = () => {
  try {
    window.performance.clearMarks();
    performanceStore.marks = {};
  } catch (error) {
    console.warn('Error clearing marks:', error);
  }
};

/**
 * Clear all performance measures.
 */
const clearMeasures = () => {
  try {
    window.performance.clearMeasures();
    performanceStore.measures = {};
  } catch (error) {
    console.warn('Error clearing measures:', error);
  }
};

/**
 * Clear all resource timings.
 */
const clearResourceTimings = () => {
  try {
    window.performance.clearResourceTimings();
    performanceStore.resources = [];
  } catch (error) {
    console.warn('Error clearing resource timings:', error);
  }
};

/**
 * Clear all performance errors.
 */
const clearErrors = () => {
  performanceStore.errors = [];
};

/**
 * Get a comprehensive performance report.
 * 
 * @returns {Object} A comprehensive performance report.
 */
const getPerformanceReport = () => {
  return {
    marks: getMarks(),
    measures: getMeasures(),
    resources: getResourceTimings(),
    errors: getErrors(),
    navigation: window.performance.timing ? {
      navigationStart: window.performance.timing.navigationStart,
      loadEventEnd: window.performance.timing.loadEventEnd,
      domComplete: window.performance.timing.domComplete,
      domInteractive: window.performance.timing.domInteractive,
      domContentLoadedEventEnd: window.performance.timing.domContentLoadedEventEnd,
    } : null,
    memory: window.performance.memory ? {
      jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit,
      totalJSHeapSize: window.performance.memory.totalJSHeapSize,
      usedJSHeapSize: window.performance.memory.usedJSHeapSize,
    } : null,
  };
};

export default initPerformanceMonitor;
