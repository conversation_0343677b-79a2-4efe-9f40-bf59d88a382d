import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';

/**
 * Custom hook for managing templates
 */
export const useTemplates = (options = {}) => {
  const {
    enabled = true,
    projectId
  } = options;

  // Local state
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mock templates data
  const mockTemplates = [
    {
      id: 'template_1',
      name: 'Basic Layout',
      description: 'A simple layout with header, content, and footer',
      type: 'layout',
      category: 'basic',
      components: [
        { id: 'header_1', type: 'Header', props: { title: 'My App' } },
        { id: 'content_1', type: 'Content', props: {} },
        { id: 'footer_1', type: 'Footer', props: { text: 'Copyright 2024' } }
      ],
      thumbnail: '/templates/basic-layout.png',
      isPublic: true,
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'template_2',
      name: 'Dashboard Layout',
      description: 'A dashboard layout with sidebar and main content area',
      type: 'layout',
      category: 'dashboard',
      components: [
        { id: 'sidebar_1', type: 'Sidebar', props: { width: 250 } },
        { id: 'main_1', type: 'MainContent', props: {} },
        { id: 'header_2', type: 'Header', props: { title: 'Dashboard' } }
      ],
      thumbnail: '/templates/dashboard-layout.png',
      isPublic: true,
      createdAt: '2024-01-02T00:00:00Z'
    },
    {
      id: 'template_3',
      name: 'Contact Form',
      description: 'A complete contact form with validation',
      type: 'component',
      category: 'forms',
      components: [
        { id: 'form_1', type: 'Form', props: { title: 'Contact Us' } },
        { id: 'input_1', type: 'Input', props: { label: 'Name', required: true } },
        { id: 'input_2', type: 'Input', props: { label: 'Email', type: 'email', required: true } },
        { id: 'textarea_1', type: 'TextArea', props: { label: 'Message', required: true } },
        { id: 'button_1', type: 'Button', props: { text: 'Send Message', type: 'primary' } }
      ],
      thumbnail: '/templates/contact-form.png',
      isPublic: true,
      createdAt: '2024-01-03T00:00:00Z'
    }
  ];

  // Load templates
  const loadTemplates = useCallback(async () => {
    if (!enabled) return;

    try {
      setLoading(true);
      setError(null);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, this would be an API call
      // const response = await fetch('/api/templates');
      // const data = await response.json();
      
      setTemplates(mockTemplates);
      
    } catch (err) {
      console.error('Error loading templates:', err);
      setError(err);
      message.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  }, [enabled]);

  // Save as template
  const saveAsTemplate = useCallback(async (templateData) => {
    if (!enabled) return null;

    try {
      setLoading(true);
      setError(null);

      const newTemplate = {
        id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...templateData,
        createdAt: new Date().toISOString(),
        isPublic: false,
        projectId
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real app, this would be an API call
      // const response = await fetch('/api/templates', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(newTemplate)
      // });
      // const savedTemplate = await response.json();

      setTemplates(prev => [...prev, newTemplate]);
      message.success('Template saved successfully');
      
      return newTemplate;
    } catch (err) {
      console.error('Error saving template:', err);
      setError(err);
      message.error('Failed to save template');
      return null;
    } finally {
      setLoading(false);
    }
  }, [enabled, projectId]);

  // Load template
  const loadTemplate = useCallback(async (templateId) => {
    if (!enabled) return null;

    try {
      setLoading(true);
      setError(null);

      const template = templates.find(t => t.id === templateId);
      if (!template) {
        throw new Error('Template not found');
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      message.success(`Template "${template.name}" loaded`);
      
      return {
        components: template.components,
        metadata: {
          name: template.name,
          description: template.description,
          type: template.type,
          category: template.category
        }
      };
    } catch (err) {
      console.error('Error loading template:', err);
      setError(err);
      message.error('Failed to load template');
      return null;
    } finally {
      setLoading(false);
    }
  }, [enabled, templates]);

  // Delete template
  const deleteTemplate = useCallback(async (templateId) => {
    if (!enabled) return false;

    try {
      setLoading(true);
      setError(null);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // In a real app, this would be an API call
      // await fetch(`/api/templates/${templateId}`, { method: 'DELETE' });

      setTemplates(prev => prev.filter(t => t.id !== templateId));
      message.success('Template deleted successfully');
      
      return true;
    } catch (err) {
      console.error('Error deleting template:', err);
      setError(err);
      message.error('Failed to delete template');
      return false;
    } finally {
      setLoading(false);
    }
  }, [enabled]);

  // Get templates by category
  const getTemplatesByCategory = useCallback((category) => {
    return templates.filter(template => template.category === category);
  }, [templates]);

  // Get templates by type
  const getTemplatesByType = useCallback((type) => {
    return templates.filter(template => template.type === type);
  }, [templates]);

  // Search templates
  const searchTemplates = useCallback((query) => {
    if (!query) return templates;
    
    const lowercaseQuery = query.toLowerCase();
    return templates.filter(template => 
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery) ||
      template.category.toLowerCase().includes(lowercaseQuery)
    );
  }, [templates]);

  // Load templates on mount
  useEffect(() => {
    if (enabled) {
      loadTemplates();
    }
  }, [enabled, loadTemplates]);

  return {
    // State
    templates,
    loading,
    error,

    // Actions
    loadTemplates,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,

    // Utility functions
    getTemplatesByCategory,
    getTemplatesByType,
    searchTemplates,

    // Computed values
    templateCount: templates.length,
    categories: [...new Set(templates.map(t => t.category))],
    types: [...new Set(templates.map(t => t.type))],
    hasTemplates: templates.length > 0
  };
};

export default useTemplates;
