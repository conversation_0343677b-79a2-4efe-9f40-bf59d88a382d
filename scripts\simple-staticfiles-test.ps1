# Simple Django Staticfiles Fix Test
Write-Host "Testing Django Staticfiles Configuration" -ForegroundColor Cyan

$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

# Test 1: Frontend build exists
Write-Host "`nChecking frontend build..." -ForegroundColor Yellow
if (Test-Path "frontend/build/static") {
    $fileCount = (Get-ChildItem -Path "frontend/build/static" -Recurse -File).Count
    Write-Host "SUCCESS: Frontend build exists with $fileCount static files" -ForegroundColor Green
} else {
    Write-Host "ERROR: Frontend build directory missing" -ForegroundColor Red
    exit 1
}

# Test 2: Docker Compose settings
Write-Host "`nChecking Docker Compose..." -ForegroundColor Yellow
$dockerContent = Get-Content "docker-compose.yml" -Raw

if ($dockerContent -match "app_builder_201\.settings") {
    Write-Host "SUCCESS: Correct Django settings module" -ForegroundColor Green
} else {
    Write-Host "ERROR: Wrong Django settings module" -ForegroundColor Red
}

if ($dockerContent -match "frontend/build.*usr/src/app/frontend/build") {
    Write-Host "SUCCESS: Frontend build volume mount configured" -ForegroundColor Green
} else {
    Write-Host "ERROR: Frontend build volume mount missing" -ForegroundColor Red
}

# Test 3: Django settings
Write-Host "`nChecking Django settings..." -ForegroundColor Yellow
if (Test-Path "backend/app_builder_201/settings.py") {
    $settingsContent = Get-Content "backend/app_builder_201/settings.py" -Raw
    
    if ($settingsContent -match "STATICFILES_DIRS") {
        Write-Host "SUCCESS: STATICFILES_DIRS configured" -ForegroundColor Green
    } else {
        Write-Host "ERROR: STATICFILES_DIRS not found" -ForegroundColor Red
    }
} else {
    Write-Host "ERROR: Django settings file not found" -ForegroundColor Red
}

Write-Host "`nStaticfiles fix verification complete!" -ForegroundColor Green
Write-Host "You can now start containers with: scripts/start-containers.ps1" -ForegroundColor Blue
