const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

/**
 * Bundle Structure Analyzer
 * Analyzes the webpack build output to identify large chunks and optimization opportunities
 */

const BUNDLE_SIZE_LIMIT = 244 * 1024; // 244 KB target
const BUILD_DIR = path.join(__dirname, '../build/static/js');

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeJSFiles() {
  if (!fs.existsSync(BUILD_DIR)) {
    console.error(chalk.red('Build directory not found. Run "npm run build" first.'));
    return;
  }

  const files = fs.readdirSync(BUILD_DIR)
    .filter(file => file.endsWith('.js') && !file.endsWith('.LICENSE.txt'))
    .map(file => {
      const filePath = path.join(BUILD_DIR, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        path: filePath
      };
    })
    .sort((a, b) => b.size - a.size);

  console.log(chalk.blue.bold('\n📊 Bundle Structure Analysis\n'));
  console.log(chalk.yellow(`Target: ${formatBytes(BUNDLE_SIZE_LIMIT)}`));
  console.log(chalk.yellow(`Current Total: ${formatBytes(files.reduce((sum, file) => sum + file.size, 0))}\n`));

  // Categorize files
  const categories = {
    main: files.filter(f => f.name.includes('main')),
    antd: files.filter(f => f.name.includes('antd')),
    vendors: files.filter(f => f.name.includes('vendors')),
    react: files.filter(f => f.name.includes('react')),
    common: files.filter(f => f.name.includes('common')),
    runtime: files.filter(f => f.name.includes('runtime')),
    chunks: files.filter(f => /^\d+\./.test(f.name))
  };

  // Display analysis by category
  Object.entries(categories).forEach(([category, categoryFiles]) => {
    if (categoryFiles.length === 0) return;

    const totalSize = categoryFiles.reduce((sum, file) => sum + file.size, 0);
    const isOverLimit = totalSize > BUNDLE_SIZE_LIMIT;

    console.log(chalk.bold(`\n${category.toUpperCase()} (${formatBytes(totalSize)}):`));

    categoryFiles.slice(0, 5).forEach(file => {
      const sizeColor = file.size > BUNDLE_SIZE_LIMIT ? chalk.red :
        file.size > BUNDLE_SIZE_LIMIT * 0.5 ? chalk.yellow : chalk.green;
      console.log(`  ${sizeColor(formatBytes(file.size).padEnd(10))} ${file.name}`);
    });

    if (categoryFiles.length > 5) {
      console.log(`  ... and ${categoryFiles.length - 5} more files`);
    }
  });

  // Identify optimization opportunities
  console.log(chalk.blue.bold('\n🎯 Optimization Opportunities:\n'));

  const largeFiles = files.filter(f => f.size > BUNDLE_SIZE_LIMIT * 0.1);
  if (largeFiles.length > 0) {
    console.log(chalk.yellow('Large files that could benefit from lazy loading:'));
    largeFiles.forEach(file => {
      console.log(`  • ${chalk.red(formatBytes(file.size))} - ${file.name}`);
    });
  }

  const antdTotal = categories.antd.reduce((sum, file) => sum + file.size, 0);
  if (antdTotal > BUNDLE_SIZE_LIMIT) {
    console.log(chalk.yellow(`\nAnt Design bundle is ${formatBytes(antdTotal)} - consider tree shaking optimization`));
  }

  const vendorTotal = categories.vendors.reduce((sum, file) => sum + file.size, 0);
  if (vendorTotal > BUNDLE_SIZE_LIMIT * 2) {
    console.log(chalk.yellow(`\nVendor bundle is ${formatBytes(vendorTotal)} - consider splitting into smaller chunks`));
  }

  // Calculate potential savings
  const potentialSavings = calculatePotentialSavings(files);
  console.log(chalk.green.bold(`\n💡 Potential bundle size reduction: ${formatBytes(potentialSavings)}`));

  return {
    totalSize: files.reduce((sum, file) => sum + file.size, 0),
    categories,
    largeFiles,
    potentialSavings
  };
}

function calculatePotentialSavings(files) {
  // Estimate potential savings from lazy loading large components
  const largeChunks = files.filter(f => f.size > 50 * 1024); // Files > 50KB
  const estimatedSavings = largeChunks.reduce((sum, file) => {
    // Assume we can lazy load 70% of large chunks
    return sum + (file.size * 0.7);
  }, 0);

  return estimatedSavings;
}

function generateLazyLoadingPlan(analysis) {
  console.log(chalk.blue.bold('\n📋 Lazy Loading Implementation Plan:\n'));

  const plan = [
    {
      priority: 1,
      component: 'Tutorial System',
      estimatedSaving: '150-200 KB',
      implementation: 'React.lazy() for TutorialAssistant and related components'
    },
    {
      priority: 2,
      component: 'AI Suggestions',
      estimatedSaving: '100-150 KB',
      implementation: 'Dynamic import for AIDesignSuggestions'
    },
    {
      priority: 3,
      component: 'Template Manager',
      estimatedSaving: '80-120 KB',
      implementation: 'Lazy load template management UI'
    },
    {
      priority: 4,
      component: 'Code Exporter',
      estimatedSaving: '60-100 KB',
      implementation: 'Dynamic import for export functionality'
    },
    {
      priority: 5,
      component: 'Advanced Property Editors',
      estimatedSaving: '50-80 KB',
      implementation: 'Lazy load color pickers, spacing editors'
    }
  ];

  plan.forEach(item => {
    console.log(`${chalk.green(`Priority ${item.priority}:`)} ${chalk.bold(item.component)}`);
    console.log(`  Estimated saving: ${chalk.yellow(item.estimatedSaving)}`);
    console.log(`  Implementation: ${item.implementation}\n`);
  });

  return plan;
}

/**
 * Bundle size monitoring and tracking
 */
function trackBundleSize(analysis) {
  const HISTORY_FILE = path.join(__dirname, '../build/bundle-history.json');
  const TARGET_SIZE = 244 * 1024; // 244 KB target

  let history = [];

  // Load existing history
  try {
    if (fs.existsSync(HISTORY_FILE)) {
      history = JSON.parse(fs.readFileSync(HISTORY_FILE, 'utf8'));
    }
  } catch (error) {
    console.warn('Could not load bundle history:', error.message);
  }

  // Add current analysis
  const currentEntry = {
    timestamp: new Date().toISOString(),
    totalSize: analysis.totalSize,
    targetSize: TARGET_SIZE,
    progress: Math.min(100, ((analysis.totalSize - TARGET_SIZE) / analysis.totalSize) * 100),
    categories: Object.fromEntries(
      Object.entries(analysis.categories).map(([name, files]) => [
        name,
        files.reduce((sum, file) => sum + file.size, 0)
      ])
    ),
    largeFiles: analysis.largeFiles.map(f => ({ name: f.name, size: f.size }))
  };

  history.push(currentEntry);

  // Keep only last 50 entries
  if (history.length > 50) {
    history = history.slice(-50);
  }

  // Save history
  try {
    fs.writeFileSync(HISTORY_FILE, JSON.stringify(history, null, 2));
  } catch (error) {
    console.warn('Could not save bundle history:', error.message);
  }

  // Display progress
  console.log(chalk.blue.bold('\n📈 Bundle Size Progress:\n'));

  const reduction = history.length > 1
    ? history[history.length - 2].totalSize - currentEntry.totalSize
    : 0;

  if (reduction > 0) {
    console.log(chalk.green(`✅ Reduced by ${formatBytes(reduction)} since last build`));
  } else if (reduction < 0) {
    console.log(chalk.red(`⚠️  Increased by ${formatBytes(Math.abs(reduction))} since last build`));
  }

  const remaining = Math.max(0, currentEntry.totalSize - TARGET_SIZE);
  if (remaining > 0) {
    console.log(chalk.yellow(`🎯 ${formatBytes(remaining)} remaining to reach target`));
    console.log(chalk.yellow(`📊 Progress: ${(100 - currentEntry.progress).toFixed(1)}% complete`));
  } else {
    console.log(chalk.green('🎉 Target achieved!'));
  }

  return currentEntry;
}

/**
 * Generate bundle size report
 */
function generateSizeReport(analysis) {
  const report = {
    summary: {
      totalSize: analysis.totalSize,
      targetSize: 244 * 1024,
      isUnderTarget: analysis.totalSize <= 244 * 1024,
      categories: Object.fromEntries(
        Object.entries(analysis.categories).map(([name, files]) => [
          name,
          {
            count: files.length,
            totalSize: files.reduce((sum, file) => sum + file.size, 0),
            files: files.map(f => ({ name: f.name, size: f.size }))
          }
        ])
      )
    },
    recommendations: [],
    warnings: []
  };

  // Add recommendations
  if (analysis.totalSize > 244 * 1024) {
    report.recommendations.push('Bundle size exceeds 244 KB target');

    // Check for large files
    analysis.largeFiles.forEach(file => {
      if (file.size > 100 * 1024) {
        report.recommendations.push(`Consider lazy loading: ${file.name} (${formatBytes(file.size)})`);
      }
    });

    // Check categories
    Object.entries(analysis.categories).forEach(([name, files]) => {
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      if (totalSize > 500 * 1024) {
        report.warnings.push(`${name} category is very large: ${formatBytes(totalSize)}`);
      }
    });
  }

  return report;
}

// Run analysis
if (require.main === module) {
  const analysis = analyzeJSFiles();
  if (analysis) {
    generateLazyLoadingPlan(analysis);
    const tracking = trackBundleSize(analysis);
    const report = generateSizeReport(analysis);

    // Save detailed report
    try {
      const reportPath = path.join(__dirname, '../build/bundle-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(chalk.blue(`\n📄 Detailed report saved to: ${reportPath}`));
    } catch (error) {
      console.warn('Could not save bundle report:', error.message);
    }
  }
}

module.exports = {
  analyzeJSFiles,
  generateLazyLoadingPlan,
  trackBundleSize,
  generateSizeReport,
  formatBytes
};
