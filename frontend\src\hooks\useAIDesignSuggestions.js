import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import aiDesignService from '../services/aiDesignService';
import { addComponent, updateComponent } from '../redux/actions';

/**
 * Custom hook for managing AI design suggestions
 * Provides layout suggestions, component combinations, and app analysis
 */
export const useAIDesignSuggestions = (options = {}) => {
  const {
    autoRefresh = true,
    refreshInterval = 30000, // 30 seconds
    enableCache = true,
    context = {}
  } = options;

  const dispatch = useDispatch();
  
  // Get app state from Redux
  const components = useSelector(state => state.app?.components || state.components || []);
  const layouts = useSelector(state => state.app?.layouts || state.layouts || []);
  const selectedComponent = useSelector(state => state.ui?.selectedComponent || null);

  // Local state
  const [suggestions, setSuggestions] = useState({
    layout: [],
    combinations: [],
    analysis: null
  });

  const [loading, setLoading] = useState({
    layout: false,
    combinations: false,
    analysis: false
  });

  const [error, setError] = useState(null);
  const [lastRefresh, setLastRefresh] = useState(null);

  // Refs for cleanup
  const refreshIntervalRef = useRef(null);
  const abortControllerRef = useRef(null);

  // Load all suggestions
  const loadSuggestions = useCallback(async (force = false) => {
    if (!components || components.length === 0) {
      setSuggestions({ layout: [], combinations: [], analysis: null });
      return;
    }

    // Abort previous requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    setError(null);
    setLoading({ layout: true, combinations: true, analysis: true });

    try {
      // Clear cache if force refresh
      if (force && enableCache) {
        aiDesignService.clearCache();
      }

      // Load all suggestions in parallel
      const [layoutResponse, combinationsResponse, analysisResponse] = await Promise.allSettled([
        aiDesignService.generateLayoutSuggestions(components, layouts, context),
        aiDesignService.generateComponentCombinations(components, selectedComponent, context),
        aiDesignService.analyzeAppStructure(components, layouts)
      ]);

      // Process layout suggestions
      const layoutSuggestions = layoutResponse.status === 'fulfilled' 
        ? layoutResponse.value.suggestions || []
        : [];

      // Process combination suggestions
      const combinationSuggestions = combinationsResponse.status === 'fulfilled'
        ? combinationsResponse.value.suggestions || []
        : [];

      // Process analysis
      const analysis = analysisResponse.status === 'fulfilled'
        ? analysisResponse.value.analysis || null
        : null;

      setSuggestions({
        layout: layoutSuggestions,
        combinations: combinationSuggestions,
        analysis
      });

      setLastRefresh(new Date());

      // Log any errors
      [layoutResponse, combinationsResponse, analysisResponse].forEach((response, index) => {
        if (response.status === 'rejected') {
          const names = ['layout', 'combinations', 'analysis'];
          console.warn(`Failed to load ${names[index]} suggestions:`, response.reason);
        }
      });

    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(`Failed to load suggestions: ${err.message}`);
      }
    } finally {
      setLoading({ layout: false, combinations: false, analysis: false });
    }
  }, [components, layouts, selectedComponent, context, enableCache]);

  // Load specific suggestion type
  const loadLayoutSuggestions = useCallback(async () => {
    if (!components || components.length === 0) return;

    setLoading(prev => ({ ...prev, layout: true }));
    setError(null);

    try {
      const response = await aiDesignService.generateLayoutSuggestions(components, layouts, context);
      setSuggestions(prev => ({
        ...prev,
        layout: response.suggestions || []
      }));
    } catch (err) {
      setError(`Failed to load layout suggestions: ${err.message}`);
    } finally {
      setLoading(prev => ({ ...prev, layout: false }));
    }
  }, [components, layouts, context]);

  const loadCombinationSuggestions = useCallback(async () => {
    if (!components || components.length === 0) return;

    setLoading(prev => ({ ...prev, combinations: true }));
    setError(null);

    try {
      const response = await aiDesignService.generateComponentCombinations(components, selectedComponent, context);
      setSuggestions(prev => ({
        ...prev,
        combinations: response.suggestions || []
      }));
    } catch (err) {
      setError(`Failed to load combination suggestions: ${err.message}`);
    } finally {
      setLoading(prev => ({ ...prev, combinations: false }));
    }
  }, [components, selectedComponent, context]);

  // Apply layout suggestion
  const applyLayoutSuggestion = useCallback((suggestion) => {
    try {
      // This would integrate with your layout system
      // For now, we'll dispatch a generic action
      console.log('Applying layout suggestion:', suggestion);
      
      // You could dispatch a specific action here
      // dispatch(applyLayout(suggestion));
      
      return true;
    } catch (err) {
      setError(`Failed to apply layout suggestion: ${err.message}`);
      return false;
    }
  }, []);

  // Apply component combination suggestion
  const applyComponentCombination = useCallback((suggestion) => {
    try {
      if (suggestion.missing_components && suggestion.missing_components.length > 0) {
        // Add missing components
        suggestion.missing_components.forEach(componentType => {
          const newComponent = {
            type: componentType,
            props: {},
            id: `${componentType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
          };
          dispatch(addComponent(newComponent.type, newComponent.props));
        });
      }

      console.log('Applied component combination:', suggestion);
      return true;
    } catch (err) {
      setError(`Failed to apply component combination: ${err.message}`);
      return false;
    }
  }, [dispatch]);

  // Refresh suggestions
  const refresh = useCallback(() => {
    loadSuggestions(true);
  }, [loadSuggestions]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Setup auto-refresh
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        loadSuggestions();
      }, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, loadSuggestions]);

  // Load suggestions when dependencies change
  useEffect(() => {
    loadSuggestions();
  }, [loadSuggestions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    // Data
    suggestions,
    loading,
    error,
    lastRefresh,

    // Actions
    loadSuggestions,
    loadLayoutSuggestions,
    loadCombinationSuggestions,
    applyLayoutSuggestion,
    applyComponentCombination,
    refresh,
    clearError,

    // Computed values
    hasLayoutSuggestions: suggestions.layout.length > 0,
    hasCombinationSuggestions: suggestions.combinations.length > 0,
    hasAnalysis: suggestions.analysis !== null,
    isLoading: loading.layout || loading.combinations || loading.analysis,
    
    // Component counts for display
    componentCount: components.length,
    layoutCount: layouts.length,
    selectedComponentType: selectedComponent?.type || null
  };
};

export default useAIDesignSuggestions;
