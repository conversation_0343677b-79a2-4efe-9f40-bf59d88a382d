import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { addComponent, updateComponent, removeComponent } from '../redux/minimal-store';

/**
 * DragDropComponentBuilder Component
 * 
 * A component builder with drag-and-drop functionality:
 * - Drag components from palette to canvas
 * - Rearrange components on canvas
 * - Edit component properties
 * - Preview components
 */
const DragDropComponentBuilder = () => {
  // State
  const [draggedComponent, setDraggedComponent] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingProps, setEditingProps] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const canvasRef = useRef(null);
  
  // Redux
  const dispatch = useDispatch();
  const components = useSelector(state => state.components || []);
  
  // Component types with default props
  const componentTypes = [
    { 
      type: 'container', 
      label: 'Container', 
      icon: '🔲',
      defaultProps: { 
        width: '100%', 
        height: 'auto', 
        padding: '16px', 
        backgroundColor: '#f5f5f5',
        borderRadius: '4px'
      } 
    },
    { 
      type: 'text', 
      label: 'Text', 
      icon: '📝',
      defaultProps: { 
        text: 'Text content', 
        fontSize: '16px', 
        color: '#333333',
        fontWeight: 'normal'
      } 
    },
    { 
      type: 'button', 
      label: 'Button', 
      icon: '🔘',
      defaultProps: { 
        text: 'Button', 
        backgroundColor: '#1890ff', 
        color: 'white',
        padding: '8px 16px',
        borderRadius: '4px',
        border: 'none'
      } 
    },
    { 
      type: 'input', 
      label: 'Input Field', 
      icon: '📋',
      defaultProps: { 
        placeholder: 'Enter text...', 
        width: '100%',
        padding: '8px',
        borderRadius: '4px',
        border: '1px solid #d9d9d9'
      } 
    },
    { 
      type: 'image', 
      label: 'Image', 
      icon: '🖼️',
      defaultProps: { 
        src: 'https://via.placeholder.com/150', 
        alt: 'Image',
        width: '100%',
        height: 'auto'
      } 
    },
    { 
      type: 'card', 
      label: 'Card', 
      icon: '🗃️',
      defaultProps: { 
        title: 'Card Title',
        content: 'Card content goes here',
        width: '100%',
        padding: '16px',
        borderRadius: '4px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        backgroundColor: 'white'
      } 
    }
  ];
  
  // Handle drag start from palette
  const handleDragStart = (e, componentType) => {
    const componentInfo = componentTypes.find(c => c.type === componentType);
    
    if (componentInfo) {
      const newComponent = {
        id: `${componentType}-${Date.now()}`,
        type: componentType,
        props: { ...componentInfo.defaultProps },
        position: { x: 0, y: 0 }
      };
      
      setDraggedComponent(newComponent);
      
      // Set drag image
      const dragImage = document.createElement('div');
      dragImage.style.width = '100px';
      dragImage.style.height = '50px';
      dragImage.style.backgroundColor = '#1890ff';
      dragImage.style.color = 'white';
      dragImage.style.display = 'flex';
      dragImage.style.justifyContent = 'center';
      dragImage.style.alignItems = 'center';
      dragImage.style.borderRadius = '4px';
      dragImage.textContent = componentInfo.label;
      document.body.appendChild(dragImage);
      
      e.dataTransfer.setDragImage(dragImage, 50, 25);
      
      // Clean up drag image after drag
      setTimeout(() => {
        document.body.removeChild(dragImage);
      }, 0);
    }
  };
  
  // Handle drag over on canvas
  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };
  
  // Handle drop on canvas
  const handleDrop = (e) => {
    e.preventDefault();
    
    if (draggedComponent && canvasRef.current) {
      const canvasRect = canvasRef.current.getBoundingClientRect();
      const x = e.clientX - canvasRect.left;
      const y = e.clientY - canvasRect.top;
      
      const newComponent = {
        ...draggedComponent,
        position: { x, y }
      };
      
      dispatch(addComponent(newComponent));
      setDraggedComponent(null);
    }
  };
  
  // Handle component click on canvas
  const handleComponentClick = (e, component) => {
    e.stopPropagation();
    setSelectedComponent(component);
    setIsEditing(false);
    setEditingProps(JSON.stringify(component.props, null, 2));
  };
  
  // Handle canvas click (deselect)
  const handleCanvasClick = () => {
    setSelectedComponent(null);
    setIsEditing(false);
  };
  
  // Handle edit button click
  const handleEditClick = () => {
    setIsEditing(true);
  };
  
  // Handle props change
  const handlePropsChange = (e) => {
    setEditingProps(e.target.value);
  };
  
  // Handle save props
  const handleSaveProps = () => {
    try {
      const newProps = JSON.parse(editingProps);
      
      if (selectedComponent) {
        const updatedComponent = {
          ...selectedComponent,
          props: newProps
        };
        
        dispatch(updateComponent(updatedComponent));
        setSelectedComponent(updatedComponent);
        setIsEditing(false);
      }
    } catch (error) {
      alert(`Invalid JSON: ${error.message}`);
    }
  };
  
  // Handle delete component
  const handleDeleteComponent = () => {
    if (selectedComponent) {
      dispatch(removeComponent(selectedComponent.id));
      setSelectedComponent(null);
      setIsEditing(false);
    }
  };
  
  // Handle duplicate component
  const handleDuplicateComponent = () => {
    if (selectedComponent) {
      const duplicatedComponent = {
        ...selectedComponent,
        id: `${selectedComponent.type}-${Date.now()}`,
        position: {
          x: selectedComponent.position.x + 20,
          y: selectedComponent.position.y + 20
        }
      };
      
      dispatch(addComponent(duplicatedComponent));
    }
  };
  
  // Render component on canvas
  const renderComponent = (component) => {
    const isSelected = selectedComponent && selectedComponent.id === component.id;
    
    const style = {
      position: 'absolute',
      left: `${component.position.x}px`,
      top: `${component.position.y}px`,
      cursor: 'move',
      border: isSelected ? '2px solid #1890ff' : '2px solid transparent',
      zIndex: isSelected ? 10 : 1,
      ...getComponentStyle(component)
    };
    
    return (
      <div
        key={component.id}
        style={style}
        onClick={(e) => handleComponentClick(e, component)}
        draggable
        onDragStart={(e) => {
          setDraggedComponent(component);
          e.dataTransfer.setData('text/plain', component.id);
        }}
      >
        {renderComponentContent(component)}
        
        {isSelected && (
          <div style={styles.componentControls}>
            <button 
              style={styles.controlButton} 
              onClick={handleEditClick}
              title="Edit"
            >
              ✏️
            </button>
            <button 
              style={styles.controlButton} 
              onClick={handleDuplicateComponent}
              title="Duplicate"
            >
              📋
            </button>
            <button 
              style={styles.controlButton} 
              onClick={handleDeleteComponent}
              title="Delete"
            >
              🗑️
            </button>
          </div>
        )}
      </div>
    );
  };
  
  // Get component style based on type and props
  const getComponentStyle = (component) => {
    const { type, props } = component;
    
    switch (type) {
      case 'container':
        return {
          width: props.width || '100%',
          height: props.height || 'auto',
          padding: props.padding || '16px',
          backgroundColor: props.backgroundColor || '#f5f5f5',
          borderRadius: props.borderRadius || '4px',
          minWidth: '100px',
          minHeight: '50px'
        };
      case 'text':
        return {
          padding: '8px',
          fontSize: props.fontSize || '16px',
          color: props.color || '#333333',
          fontWeight: props.fontWeight || 'normal'
        };
      case 'button':
        return {
          padding: props.padding || '8px 16px',
          backgroundColor: props.backgroundColor || '#1890ff',
          color: props.color || 'white',
          borderRadius: props.borderRadius || '4px',
          border: props.border || 'none',
          cursor: 'pointer'
        };
      case 'input':
        return {
          width: props.width || '200px',
          padding: props.padding || '8px',
          borderRadius: props.borderRadius || '4px',
          border: props.border || '1px solid #d9d9d9'
        };
      case 'image':
        return {
          width: props.width || '150px',
          height: props.height || 'auto',
          objectFit: 'cover'
        };
      case 'card':
        return {
          width: props.width || '300px',
          padding: props.padding || '16px',
          borderRadius: props.borderRadius || '4px',
          boxShadow: props.boxShadow || '0 2px 8px rgba(0, 0, 0, 0.1)',
          backgroundColor: props.backgroundColor || 'white'
        };
      default:
        return {};
    }
  };
  
  // Render component content based on type
  const renderComponentContent = (component) => {
    const { type, props } = component;
    
    switch (type) {
      case 'container':
        return <div>Container</div>;
      case 'text':
        return <div>{props.text || 'Text content'}</div>;
      case 'button':
        return <button style={{ cursor: 'pointer' }}>{props.text || 'Button'}</button>;
      case 'input':
        return <input type="text" placeholder={props.placeholder || 'Enter text...'} style={{ cursor: 'text' }} onClick={(e) => e.stopPropagation()} />;
      case 'image':
        return <img src={props.src || 'https://via.placeholder.com/150'} alt={props.alt || 'Image'} style={{ maxWidth: '100%' }} />;
      case 'card':
        return (
          <div>
            <h3 style={{ margin: '0 0 8px 0' }}>{props.title || 'Card Title'}</h3>
            <p style={{ margin: 0 }}>{props.content || 'Card content goes here'}</p>
          </div>
        );
      default:
        return <div>Unknown component type</div>;
    }
  };
  
  // Render component
  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h2 style={styles.title}>Component Builder</h2>
        <button 
          style={{
            ...styles.button,
            backgroundColor: showPreview ? '#52c41a' : '#1890ff'
          }} 
          onClick={() => setShowPreview(!showPreview)}
        >
          {showPreview ? 'Exit Preview' : 'Preview'}
        </button>
      </div>
      
      <div style={styles.content}>
        {/* Component Palette */}
        {!showPreview && (
          <div style={styles.palette}>
            <h3 style={styles.sectionTitle}>Component Palette</h3>
            
            <div style={styles.componentList}>
              {componentTypes.map((componentType) => (
                <div
                  key={componentType.type}
                  style={styles.componentItem}
                  draggable
                  onDragStart={(e) => handleDragStart(e, componentType.type)}
                >
                  <span style={styles.componentIcon}>{componentType.icon}</span>
                  <span style={styles.componentLabel}>{componentType.label}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Canvas */}
        <div 
          ref={canvasRef}
          style={{
            ...styles.canvas,
            flex: showPreview ? 1 : undefined
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleCanvasClick}
        >
          <h3 style={styles.sectionTitle}>
            {showPreview ? 'Preview' : 'Canvas'}
          </h3>
          
          {components.length === 0 ? (
            <div style={styles.emptyCanvas}>
              {showPreview ? 'No components to preview' : 'Drag components here'}
            </div>
          ) : (
            components.map((component) => renderComponent(component))
          )}
        </div>
        
        {/* Properties Panel */}
        {!showPreview && selectedComponent && (
          <div style={styles.propertiesPanel}>
            <h3 style={styles.sectionTitle}>Properties</h3>
            
            <div style={styles.componentInfo}>
              <div style={styles.componentInfoItem}>
                <span style={styles.componentInfoLabel}>Type:</span>
                <span style={styles.componentInfoValue}>{selectedComponent.type}</span>
              </div>
              
              <div style={styles.componentInfoItem}>
                <span style={styles.componentInfoLabel}>ID:</span>
                <span style={styles.componentInfoValue}>{selectedComponent.id}</span>
              </div>
            </div>
            
            {isEditing ? (
              <div style={styles.propsEditor}>
                <textarea
                  style={styles.propsTextarea}
                  value={editingProps}
                  onChange={handlePropsChange}
                />
                
                <div style={styles.propsActions}>
                  <button 
                    style={{...styles.button, ...styles.saveButton}} 
                    onClick={handleSaveProps}
                  >
                    Save
                  </button>
                  
                  <button 
                    style={{...styles.button, ...styles.cancelButton}} 
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div style={styles.propsViewer}>
                <pre style={styles.propsDisplay}>
                  {JSON.stringify(selectedComponent.props, null, 2)}
                </pre>
                
                <button 
                  style={styles.button} 
                  onClick={handleEditClick}
                >
                  Edit Properties
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Component styles
const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    padding: '16px',
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    maxWidth: '1200px',
    margin: '0 auto',
    height: '700px'
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  title: {
    margin: 0,
    fontSize: '24px',
    fontWeight: 'bold'
  },
  content: {
    display: 'flex',
    gap: '16px',
    flex: 1,
    overflow: 'hidden'
  },
  palette: {
    width: '200px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    overflow: 'auto'
  },
  canvas: {
    flex: 2,
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    position: 'relative',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px',
    overflow: 'auto',
    minHeight: '500px'
  },
  propertiesPanel: {
    width: '300px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    overflow: 'auto'
  },
  sectionTitle: {
    margin: '0 0 8px 0',
    fontSize: '16px',
    fontWeight: 'bold',
    padding: '8px',
    backgroundColor: '#f0f0f0',
    borderRadius: '4px'
  },
  componentList: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  componentItem: {
    padding: '8px',
    backgroundColor: '#f0f0f0',
    borderRadius: '4px',
    cursor: 'grab',
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  componentIcon: {
    fontSize: '20px'
  },
  componentLabel: {
    fontWeight: 'bold'
  },
  emptyCanvas: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    color: '#8c8c8c',
    fontStyle: 'italic'
  },
  componentInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
    padding: '8px',
    backgroundColor: '#f0f0f0',
    borderRadius: '4px',
    marginBottom: '8px'
  },
  componentInfoItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  componentInfoLabel: {
    fontWeight: 'bold'
  },
  componentInfoValue: {
    color: '#1890ff'
  },
  propsViewer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  propsDisplay: {
    padding: '8px',
    backgroundColor: '#f0f0f0',
    borderRadius: '4px',
    fontSize: '14px',
    fontFamily: 'monospace',
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    margin: 0,
    overflow: 'auto',
    maxHeight: '300px'
  },
  propsEditor: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  propsTextarea: {
    padding: '8px',
    borderRadius: '4px',
    border: '1px solid #d9d9d9',
    fontSize: '14px',
    fontFamily: 'monospace',
    resize: 'vertical',
    minHeight: '200px'
  },
  propsActions: {
    display: 'flex',
    gap: '8px',
    justifyContent: 'flex-end'
  },
  button: {
    padding: '8px 16px',
    backgroundColor: '#1890ff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px'
  },
  saveButton: {
    backgroundColor: '#52c41a'
  },
  cancelButton: {
    backgroundColor: '#ff4d4f'
  },
  componentControls: {
    position: 'absolute',
    top: '-30px',
    right: 0,
    display: 'flex',
    gap: '4px',
    backgroundColor: 'white',
    padding: '4px',
    borderRadius: '4px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
  },
  controlButton: {
    width: '24px',
    height: '24px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px'
  }
};

export default DragDropComponentBuilder;
