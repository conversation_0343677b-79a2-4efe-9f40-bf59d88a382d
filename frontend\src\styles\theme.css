/**
 * Theme CSS Variables
 * 
 * This file defines CSS variables for theming the application.
 */

:root {
  /* Default light theme colors */
  --color-primary: #1976d2;
  --color-secondary: #f50057;
  --color-background: #ffffff;
  --color-surface: #f5f5f5;
  --color-text: #212121;
  --color-textSecondary: #757575;
  --color-border: #e0e0e0;
  --color-error: #d32f2f;
  --color-warning: #f57c00;
  --color-info: #0288d1;
  --color-success: #388e3c;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-xxl: 32px;
  
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  /* Border radius */
  --border-radius-sm: 2px;
  --border-radius-md: 4px;
  --border-radius-lg: 8px;
  --border-radius-xl: 16px;
  --border-radius-circle: 50%;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 15px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.05);
  
  /* Z-index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  
  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  --transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark theme */
[data-theme="dark"] {
  --color-primary: #90caf9;
  --color-secondary: #f48fb1;
  --color-background: #121212;
  --color-surface: #1e1e1e;
  --color-text: #ffffff;
  --color-textSecondary: #b0b0b0;
  --color-border: #333333;
  --color-error: #f44336;
  --color-warning: #ff9800;
  --color-info: #29b6f6;
  --color-success: #66bb6a;
  
  /* Shadows for dark theme */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.24), 0 1px 2px rgba(0, 0, 0, 0.36);
  --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.24);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 15px 25px rgba(0, 0, 0, 0.3), 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* Global styles */
body {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  color: var(--color-text);
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  transition: background-color var(--transition-normal) var(--transition-timing-function);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

h1 {
  font-size: var(--font-size-xxl);
}

h2 {
  font-size: var(--font-size-xl);
}

h3 {
  font-size: var(--font-size-lg);
}

h4 {
  font-size: var(--font-size-md);
}

p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast) var(--transition-timing-function);
}

a:hover {
  text-decoration: underline;
}

/* Buttons */
button, .button {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  background-color: var(--color-primary);
  color: white;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition-fast) var(--transition-timing-function);
}

button:hover, .button:hover {
  background-color: color-mix(in srgb, var(--color-primary) 80%, black);
}

button:disabled, .button:disabled {
  background-color: var(--color-border);
  color: var(--color-textSecondary);
  cursor: not-allowed;
}

/* Secondary button */
button.secondary, .button.secondary {
  background-color: transparent;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
}

button.secondary:hover, .button.secondary:hover {
  background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);
}

/* Forms */
input, select, textarea {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  background-color: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-md);
  transition: border-color var(--transition-fast) var(--transition-timing-function);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

/* Cards */
.card {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  transition: box-shadow var(--transition-fast) var(--transition-timing-function);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* Utility classes */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-error { color: var(--color-error); }
.text-warning { color: var(--color-warning); }
.text-info { color: var(--color-info); }
.text-success { color: var(--color-success); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-error { background-color: var(--color-error); }
.bg-warning { background-color: var(--color-warning); }
.bg-info { background-color: var(--color-info); }
.bg-success { background-color: var(--color-success); }

.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary); }
.border-error { border-color: var(--color-error); }
.border-warning { border-color: var(--color-warning); }
.border-info { border-color: var(--color-info); }
.border-success { border-color: var(--color-success); }

/* Responsive breakpoints */
@media (max-width: 576px) {
  :root {
    --font-size-xxl: 28px;
    --font-size-xl: 22px;
    --font-size-lg: 16px;
    --spacing-xl: 24px;
  }
}

@media (prefers-reduced-motion) {
  :root {
    --transition-fast: 0ms;
    --transition-normal: 0ms;
    --transition-slow: 0ms;
  }
}
