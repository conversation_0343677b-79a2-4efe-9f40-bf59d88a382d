import React, { useState, useEffect } from 'react';
import { Select, Space, Typography, Divider } from 'antd';
import { FontSizeOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';
import NumberInput from './NumberInput';

const { Text } = Typography;
const { Option } = Select;

const FontContainer = styled.div`
  width: 100%;
`;

const FontPreview = styled.div`
  width: 100%;
  padding: 16px;
  margin: 12px 0;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  font-family: ${props => props.fontFamily || 'inherit'};
  font-size: ${props => props.fontSize || '16px'};
  font-weight: ${props => props.fontWeight || 'normal'};
  line-height: ${props => props.lineHeight || '1.5'};
  text-align: center;
`;

const PropertyRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
`;

const PropertyLabel = styled(Text)`
  min-width: 80px;
  font-size: 12px;
  font-weight: 500;
`;

/**
 * Font selector with family, size, weight, and line height controls
 */
const FontSelector = ({
  value,
  onChange,
  showPreview = true,
  ...props
}) => {
  const [fontFamily, setFontFamily] = useState('inherit');
  const [fontSize, setFontSize] = useState('16px');
  const [fontWeight, setFontWeight] = useState('normal');
  const [lineHeight, setLineHeight] = useState('1.5');

  // Parse font value on mount and when value changes
  useEffect(() => {
    if (value) {
      const parsed = parseFontValue(value);
      setFontFamily(parsed.family);
      setFontSize(parsed.size);
      setFontWeight(parsed.weight);
      setLineHeight(parsed.lineHeight);
    }
  }, [value]);

  // Parse font value (object or individual properties)
  const parseFontValue = (val) => {
    if (!val) {
      return {
        family: 'inherit',
        size: '16px',
        weight: 'normal',
        lineHeight: '1.5'
      };
    }
    
    if (typeof val === 'object') {
      return {
        family: val.fontFamily || val.family || 'inherit',
        size: val.fontSize || val.size || '16px',
        weight: val.fontWeight || val.weight || 'normal',
        lineHeight: val.lineHeight || '1.5'
      };
    }
    
    // If it's a string, assume it's just the font family
    return {
      family: val,
      size: fontSize,
      weight: fontWeight,
      lineHeight: lineHeight
    };
  };

  // Format font value for output
  const formatFontValue = (family, size, weight, height) => {
    return {
      fontFamily: family,
      fontSize: size,
      fontWeight: weight,
      lineHeight: height
    };
  };

  // Handle value changes
  const handleValueChange = (property, newValue) => {
    let newFamily = fontFamily;
    let newSize = fontSize;
    let newWeight = fontWeight;
    let newLineHeight = lineHeight;

    switch (property) {
      case 'family':
        newFamily = newValue;
        setFontFamily(newValue);
        break;
      case 'size':
        newSize = newValue;
        setFontSize(newValue);
        break;
      case 'weight':
        newWeight = newValue;
        setFontWeight(newValue);
        break;
      case 'lineHeight':
        newLineHeight = newValue;
        setLineHeight(newValue);
        break;
    }

    const formattedValue = formatFontValue(newFamily, newSize, newWeight, newLineHeight);
    onChange?.(formattedValue);
  };

  const fontFamilies = [
    { value: 'inherit', label: 'Inherit' },
    { value: 'Arial, sans-serif', label: 'Arial' },
    { value: 'Helvetica, Arial, sans-serif', label: 'Helvetica' },
    { value: '"Times New Roman", Times, serif', label: 'Times New Roman' },
    { value: 'Georgia, serif', label: 'Georgia' },
    { value: '"Courier New", Courier, monospace', label: 'Courier New' },
    { value: 'Verdana, Geneva, sans-serif', label: 'Verdana' },
    { value: '"Trebuchet MS", Helvetica, sans-serif', label: 'Trebuchet MS' },
    { value: '"Lucida Sans Unicode", "Lucida Grande", sans-serif', label: 'Lucida Sans' },
    { value: 'Impact, Charcoal, sans-serif', label: 'Impact' },
    { value: '"Comic Sans MS", cursive', label: 'Comic Sans MS' },
    { value: '"Palatino Linotype", "Book Antiqua", Palatino, serif', label: 'Palatino' },
    { value: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif', label: 'Inter' },
    { value: '"Roboto", sans-serif', label: 'Roboto' },
    { value: '"Open Sans", sans-serif', label: 'Open Sans' },
    { value: '"Lato", sans-serif', label: 'Lato' },
    { value: '"Montserrat", sans-serif', label: 'Montserrat' },
    { value: '"Source Sans Pro", sans-serif', label: 'Source Sans Pro' }
  ];

  const fontWeights = [
    { value: '100', label: 'Thin (100)' },
    { value: '200', label: 'Extra Light (200)' },
    { value: '300', label: 'Light (300)' },
    { value: 'normal', label: 'Normal (400)' },
    { value: '500', label: 'Medium (500)' },
    { value: '600', label: 'Semi Bold (600)' },
    { value: 'bold', label: 'Bold (700)' },
    { value: '800', label: 'Extra Bold (800)' },
    { value: '900', label: 'Black (900)' }
  ];

  return (
    <FontContainer>
      <Space direction="vertical" style={{ width: '100%' }}>
        <PropertyRow>
          <PropertyLabel>Family:</PropertyLabel>
          <Select
            value={fontFamily}
            onChange={(val) => handleValueChange('family', val)}
            style={{ flex: 1 }}
            size="small"
            showSearch
            placeholder="Select font family"
          >
            {fontFamilies.map(font => (
              <Option key={font.value} value={font.value}>
                <span style={{ fontFamily: font.value }}>{font.label}</span>
              </Option>
            ))}
          </Select>
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Size:</PropertyLabel>
          <div style={{ flex: 1 }}>
            <NumberInput
              value={fontSize}
              onChange={(val) => handleValueChange('size', val)}
              min={8}
              max={72}
              step={1}
              unit="px"
              units={['px', 'em', 'rem', '%']}
              size="small"
            />
          </div>
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Weight:</PropertyLabel>
          <Select
            value={fontWeight}
            onChange={(val) => handleValueChange('weight', val)}
            style={{ flex: 1 }}
            size="small"
          >
            {fontWeights.map(weight => (
              <Option key={weight.value} value={weight.value}>
                {weight.label}
              </Option>
            ))}
          </Select>
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Line Height:</PropertyLabel>
          <div style={{ flex: 1 }}>
            <NumberInput
              value={lineHeight}
              onChange={(val) => handleValueChange('lineHeight', val)}
              min={0.5}
              max={3}
              step={0.1}
              precision={1}
              showUnit={false}
              size="small"
              tooltip="Line height as a multiplier (e.g., 1.5 = 150%)"
            />
          </div>
        </PropertyRow>

        {showPreview && (
          <>
            <Divider style={{ margin: '8px 0' }} />
            <Text style={{ fontSize: '12px', marginBottom: '4px' }}>Preview:</Text>
            <FontPreview
              fontFamily={fontFamily}
              fontSize={fontSize}
              fontWeight={fontWeight}
              lineHeight={lineHeight}
            >
              <FontSizeOutlined style={{ marginRight: '8px' }} />
              The quick brown fox jumps over the lazy dog
            </FontPreview>
            <Text type="secondary" style={{ fontSize: '11px', textAlign: 'center' }}>
              {fontFamily} • {fontSize} • {fontWeight} • {lineHeight}
            </Text>
          </>
        )}
      </Space>
    </FontContainer>
  );
};

export default FontSelector;
