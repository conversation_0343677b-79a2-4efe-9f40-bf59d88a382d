import React, { useState, useEffect } from 'react';
import connectionManager, { CONNECTION_STATES } from '../services/ConnectionManager';

/**
 * Connection Status Component
 * 
 * This component displays the current connection status to backend services.
 */
const ConnectionStatus = ({ showDetails = false, onStatusChange = null }) => {
  const [status, setStatus] = useState(connectionManager.getStatus());
  
  useEffect(() => {
    // Initialize connection manager if not already initialized
    connectionManager.init();
    
    // Add listener for connection status changes
    const removeListener = connectionManager.addListener((type, newStatus, oldStatus) => {
      setStatus(connectionManager.getStatus());
      
      // Call onStatusChange callback if provided
      if (onStatusChange) {
        onStatusChange(type, newStatus, oldStatus);
      }
    });
    
    // Clean up listener when component unmounts
    return () => {
      removeListener();
    };
  }, [onStatusChange]);
  
  // Get status color
  const getStatusColor = (statusValue) => {
    switch (statusValue) {
      case CONNECTION_STATES.CONNECTED:
        return '#2e7d32'; // Green
      case CONNECTION_STATES.MOCK:
        return '#f57c00'; // Orange
      case CONNECTION_STATES.ERROR:
        return '#c62828'; // Red
      case CONNECTION_STATES.DISCONNECTED:
        return '#c62828'; // Red
      case CONNECTION_STATES.CHECKING:
        return '#1976d2'; // Blue
      default:
        return '#757575'; // Gray
    }
  };
  
  // Get status text
  const getStatusText = (statusValue) => {
    switch (statusValue) {
      case CONNECTION_STATES.CONNECTED:
        return 'Connected';
      case CONNECTION_STATES.MOCK:
        return 'Mock';
      case CONNECTION_STATES.ERROR:
        return 'Error';
      case CONNECTION_STATES.DISCONNECTED:
        return 'Disconnected';
      case CONNECTION_STATES.CHECKING:
        return 'Checking...';
      default:
        return 'Unknown';
    }
  };
  
  // Format date
  const formatDate = (date) => {
    if (!date) return 'Never';
    return date.toLocaleTimeString();
  };
  
  return (
    <div className="connection-status">
      <div className="status-indicators">
        <div className="status-indicator">
          <div 
            className="status-dot" 
            style={{ backgroundColor: getStatusColor(status.api.status) }}
          ></div>
          <div className="status-label">
            API: {getStatusText(status.api.status)}
          </div>
        </div>
        
        <div className="status-indicator">
          <div 
            className="status-dot" 
            style={{ backgroundColor: getStatusColor(status.ws.status) }}
          ></div>
          <div className="status-label">
            WebSocket: {getStatusText(status.ws.status)}
          </div>
        </div>
      </div>
      
      {showDetails && (
        <div className="status-details">
          <div className="status-detail">
            <strong>API Endpoint:</strong> {status.api.activeEndpoint || 'None'}
          </div>
          <div className="status-detail">
            <strong>Last API Check:</strong> {formatDate(status.api.lastCheck)}
          </div>
          <div className="status-detail">
            <strong>WebSocket Endpoint:</strong> {status.ws.activeEndpoint || 'None'}
          </div>
          <div className="status-detail">
            <strong>Last WebSocket Check:</strong> {formatDate(status.ws.lastCheck)}
          </div>
          <div className="status-detail">
            <strong>Mock Mode:</strong> {status.mockMode ? 'Enabled' : 'Disabled'}
          </div>
          <button 
            className="check-button"
            onClick={() => connectionManager.checkConnections()}
          >
            Check Connections
          </button>
        </div>
      )}
      
      <style jsx>{`
        .connection-status {
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-bottom: 10px;
          background-color: #f9f9f9;
        }
        
        .status-indicators {
          display: flex;
          gap: 20px;
        }
        
        .status-indicator {
          display: flex;
          align-items: center;
          gap: 5px;
        }
        
        .status-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
        
        .status-label {
          font-size: 14px;
        }
        
        .status-details {
          margin-top: 10px;
          padding-top: 10px;
          border-top: 1px solid #ddd;
          font-size: 12px;
        }
        
        .status-detail {
          margin-bottom: 5px;
        }
        
        .check-button {
          margin-top: 10px;
          padding: 5px 10px;
          background-color: #1976d2;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .check-button:hover {
          background-color: #1565c0;
        }
      `}</style>
    </div>
  );
};

export default ConnectionStatus;
