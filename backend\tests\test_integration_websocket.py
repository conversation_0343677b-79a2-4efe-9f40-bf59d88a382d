"""
Integration tests for WebSocket functionality in the App Builder application.
Tests real-time communication, collaboration, and message handling.
"""

import pytest
import json
import asyncio
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.contrib.auth.models import User
from django.test import TransactionTestCase
from model_bakery import baker
from unittest.mock import patch, AsyncMock

# Import your WebSocket consumers and routing
# from my_app.consumers import AppBuilderConsumer, CollaborationConsumer
# from my_app.routing import websocket_urlpatterns


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
class TestWebSocketIntegration:
    """Integration tests for WebSocket functionality."""

    async def test_websocket_connection_flow(self):
        """Test complete WebSocket connection and authentication flow."""
        # Create test user
        user = await database_sync_to_async(User.objects.create_user)(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Mock WebSocket consumer for testing
        class MockAppBuilderConsumer:
            def __init__(self):
                self.user = None
                self.groups = []
                self.messages = []

            async def connect(self):
                # Simulate authentication
                self.user = user
                await self.accept()

            async def disconnect(self, close_code):
                # Cleanup groups
                for group in self.groups:
                    await self.channel_layer.group_discard(group, self.channel_name)

            async def receive(self, text_data):
                data = json.loads(text_data)
                await self.handle_message(data)

            async def handle_message(self, data):
                message_type = data.get('type')
                
                if message_type == 'ping':
                    await self.send(text_data=json.dumps({
                        'type': 'pong',
                        'timestamp': data.get('timestamp')
                    }))
                elif message_type == 'join_room':
                    room_id = data.get('room_id')
                    group_name = f'app_{room_id}'
                    self.groups.append(group_name)
                    await self.send(text_data=json.dumps({
                        'type': 'room_joined',
                        'room_id': room_id
                    }))

        # Test connection would go here with actual consumer
        # communicator = WebsocketCommunicator(AppBuilderConsumer.as_asgi(), "/ws/app-builder/")
        # connected, subprotocol = await communicator.connect()
        # assert connected

        # For now, just verify the mock setup
        consumer = MockAppBuilderConsumer()
        await consumer.connect()
        assert consumer.user == user

    async def test_real_time_collaboration_flow(self):
        """Test real-time collaboration message flow."""
        # Create test users
        user1 = await database_sync_to_async(User.objects.create_user)(
            username='user1', email='<EMAIL>', password='pass123'
        )
        user2 = await database_sync_to_async(User.objects.create_user)(
            username='user2', email='<EMAIL>', password='pass123'
        )

        # Create test app
        app = await database_sync_to_async(baker.make)(
            'my_app.App',
            name='Collaboration Test App',
            user=user1,
            app_data=json.dumps({
                'components': [
                    {'id': '1', 'type': 'button', 'props': {'text': 'Button'}}
                ]
            })
        )

        # Mock collaboration flow
        collaboration_messages = []

        class MockCollaborationConsumer:
            def __init__(self, user):
                self.user = user
                self.room_groups = []

            async def join_app_room(self, app_id):
                group_name = f'app_{app_id}'
                self.room_groups.append(group_name)
                
                # Broadcast user joined
                join_message = {
                    'type': 'user_joined',
                    'user_id': str(self.user.id),
                    'user_name': self.user.username,
                    'app_id': app_id
                }
                collaboration_messages.append(join_message)
                return join_message

            async def send_component_update(self, app_id, component_data):
                update_message = {
                    'type': 'component_update',
                    'app_id': app_id,
                    'component': component_data,
                    'user_id': str(self.user.id),
                    'timestamp': '2024-01-01T00:00:00Z'
                }
                collaboration_messages.append(update_message)
                return update_message

            async def send_cursor_update(self, app_id, cursor_position):
                cursor_message = {
                    'type': 'cursor_update',
                    'app_id': app_id,
                    'user_id': str(self.user.id),
                    'position': cursor_position,
                    'timestamp': '2024-01-01T00:00:00Z'
                }
                collaboration_messages.append(cursor_message)
                return cursor_message

        # Simulate collaboration flow
        consumer1 = MockCollaborationConsumer(user1)
        consumer2 = MockCollaborationConsumer(user2)

        # User 1 joins app room
        join_msg1 = await consumer1.join_app_room(str(app.id))
        assert join_msg1['type'] == 'user_joined'
        assert join_msg1['user_name'] == 'user1'

        # User 2 joins app room
        join_msg2 = await consumer2.join_app_room(str(app.id))
        assert join_msg2['type'] == 'user_joined'
        assert join_msg2['user_name'] == 'user2'

        # User 1 updates a component
        component_update = {
            'id': '1',
            'type': 'button',
            'props': {'text': 'Updated Button', 'color': 'blue'}
        }
        update_msg = await consumer1.send_component_update(str(app.id), component_update)
        assert update_msg['type'] == 'component_update'
        assert update_msg['component']['props']['text'] == 'Updated Button'

        # User 2 sends cursor update
        cursor_msg = await consumer2.send_cursor_update(str(app.id), {'x': 150, 'y': 200})
        assert cursor_msg['type'] == 'cursor_update'
        assert cursor_msg['position']['x'] == 150

        # Verify all messages were recorded
        assert len(collaboration_messages) == 4

    async def test_comment_system_integration(self):
        """Test real-time comment system integration."""
        # Create test data
        user = await database_sync_to_async(User.objects.create_user)(
            username='commenter', email='<EMAIL>', password='pass123'
        )
        app = await database_sync_to_async(baker.make)(
            'my_app.App',
            name='Comment Test App',
            user=user,
            app_data='{}'
        )

        # Mock comment flow
        comment_messages = []

        class MockCommentConsumer:
            def __init__(self, user):
                self.user = user

            async def add_comment(self, app_id, comment_data):
                # Simulate database save
                comment = {
                    'id': 'comment-123',
                    'app_id': app_id,
                    'user_id': str(self.user.id),
                    'user_name': self.user.username,
                    'text': comment_data['text'],
                    'component_id': comment_data.get('component_id'),
                    'position': comment_data.get('position', {}),
                    'timestamp': '2024-01-01T00:00:00Z'
                }

                # Broadcast comment added
                broadcast_message = {
                    'type': 'comment_added',
                    'comment': comment
                }
                comment_messages.append(broadcast_message)
                return broadcast_message

            async def update_comment(self, comment_id, update_data):
                updated_comment = {
                    'id': comment_id,
                    'text': update_data['text'],
                    'updated_at': '2024-01-01T00:00:00Z'
                }

                broadcast_message = {
                    'type': 'comment_updated',
                    'comment': updated_comment
                }
                comment_messages.append(broadcast_message)
                return broadcast_message

            async def delete_comment(self, comment_id):
                broadcast_message = {
                    'type': 'comment_deleted',
                    'comment_id': comment_id
                }
                comment_messages.append(broadcast_message)
                return broadcast_message

        # Test comment flow
        consumer = MockCommentConsumer(user)

        # Add comment
        comment_data = {
            'text': 'This component needs improvement',
            'component_id': 'button-1',
            'position': {'x': 150, 'y': 200}
        }
        add_msg = await consumer.add_comment(str(app.id), comment_data)
        assert add_msg['type'] == 'comment_added'
        assert add_msg['comment']['text'] == 'This component needs improvement'

        # Update comment
        update_msg = await consumer.update_comment('comment-123', {'text': 'Updated comment'})
        assert update_msg['type'] == 'comment_updated'
        assert update_msg['comment']['text'] == 'Updated comment'

        # Delete comment
        delete_msg = await consumer.delete_comment('comment-123')
        assert delete_msg['type'] == 'comment_deleted'
        assert delete_msg['comment_id'] == 'comment-123'

        assert len(comment_messages) == 3

    async def test_websocket_error_handling(self):
        """Test WebSocket error handling and recovery."""
        error_scenarios = []

        class MockErrorHandlingConsumer:
            def __init__(self):
                self.connected = False
                self.errors = []

            async def connect(self):
                self.connected = True

            async def handle_invalid_message(self, data):
                try:
                    # Simulate processing invalid data
                    if not data.get('type'):
                        raise ValueError("Message type is required")
                    
                    if data['type'] == 'invalid_action':
                        raise ValueError("Invalid action type")
                    
                    return {'status': 'success'}
                except Exception as e:
                    error_response = {
                        'type': 'error',
                        'error': str(e),
                        'code': 'INVALID_MESSAGE'
                    }
                    self.errors.append(error_response)
                    error_scenarios.append(error_response)
                    return error_response

            async def handle_permission_error(self, user_id, action):
                if action == 'admin_only' and user_id != 'admin':
                    error_response = {
                        'type': 'error',
                        'error': 'Permission denied',
                        'code': 'PERMISSION_DENIED'
                    }
                    self.errors.append(error_response)
                    error_scenarios.append(error_response)
                    return error_response
                return {'status': 'success'}

        # Test error scenarios
        consumer = MockErrorHandlingConsumer()
        await consumer.connect()

        # Test invalid message
        invalid_msg = {}
        error1 = await consumer.handle_invalid_message(invalid_msg)
        assert error1['type'] == 'error'
        assert 'required' in error1['error']

        # Test invalid action
        invalid_action = {'type': 'invalid_action'}
        error2 = await consumer.handle_invalid_message(invalid_action)
        assert error2['code'] == 'INVALID_MESSAGE'

        # Test permission error
        error3 = await consumer.handle_permission_error('user123', 'admin_only')
        assert error3['code'] == 'PERMISSION_DENIED'

        assert len(error_scenarios) == 3

    async def test_websocket_performance_under_load(self):
        """Test WebSocket performance under load."""
        # Simulate multiple concurrent connections
        connection_count = 50
        message_count = 100
        
        performance_metrics = {
            'connections': 0,
            'messages_sent': 0,
            'messages_received': 0,
            'errors': 0,
            'avg_response_time': 0
        }

        class MockPerformanceConsumer:
            def __init__(self, consumer_id):
                self.id = consumer_id
                self.connected = False
                self.messages = []

            async def connect(self):
                self.connected = True
                performance_metrics['connections'] += 1

            async def send_message(self, message):
                # Simulate message processing time
                await asyncio.sleep(0.001)  # 1ms processing time
                
                self.messages.append(message)
                performance_metrics['messages_sent'] += 1
                
                # Echo back
                response = {
                    'type': 'echo',
                    'original': message,
                    'consumer_id': self.id
                }
                performance_metrics['messages_received'] += 1
                return response

        # Create multiple consumers
        consumers = []
        for i in range(connection_count):
            consumer = MockPerformanceConsumer(f'consumer-{i}')
            await consumer.connect()
            consumers.append(consumer)

        # Send messages from all consumers
        tasks = []
        for consumer in consumers:
            for j in range(message_count // connection_count):
                message = {
                    'type': 'test_message',
                    'data': f'Message {j} from {consumer.id}',
                    'timestamp': '2024-01-01T00:00:00Z'
                }
                task = consumer.send_message(message)
                tasks.append(task)

        # Execute all tasks concurrently
        start_time = asyncio.get_event_loop().time()
        await asyncio.gather(*tasks)
        end_time = asyncio.get_event_loop().time()

        # Calculate performance metrics
        total_time = end_time - start_time
        performance_metrics['avg_response_time'] = total_time / len(tasks)

        # Verify performance
        assert performance_metrics['connections'] == connection_count
        assert performance_metrics['messages_sent'] == message_count
        assert performance_metrics['messages_received'] == message_count
        assert performance_metrics['avg_response_time'] < 0.1  # Less than 100ms average

    async def test_websocket_message_ordering(self):
        """Test that WebSocket messages maintain proper ordering."""
        message_order = []

        class MockOrderingConsumer:
            def __init__(self):
                self.sequence = 0

            async def send_ordered_message(self, content):
                self.sequence += 1
                message = {
                    'sequence': self.sequence,
                    'content': content,
                    'timestamp': asyncio.get_event_loop().time()
                }
                message_order.append(message)
                return message

        consumer = MockOrderingConsumer()

        # Send messages in sequence
        messages = ['First', 'Second', 'Third', 'Fourth', 'Fifth']
        for content in messages:
            await consumer.send_ordered_message(content)

        # Verify ordering
        assert len(message_order) == 5
        for i, message in enumerate(message_order):
            assert message['sequence'] == i + 1
            assert message['content'] == messages[i]

        # Verify timestamps are in order
        for i in range(1, len(message_order)):
            assert message_order[i]['timestamp'] >= message_order[i-1]['timestamp']

    async def test_websocket_reconnection_handling(self):
        """Test WebSocket reconnection and state recovery."""
        reconnection_events = []

        class MockReconnectionConsumer:
            def __init__(self):
                self.connected = False
                self.connection_count = 0
                self.state = {}

            async def connect(self):
                self.connected = True
                self.connection_count += 1
                reconnection_events.append({
                    'type': 'connected',
                    'connection_count': self.connection_count,
                    'timestamp': asyncio.get_event_loop().time()
                })

            async def disconnect(self):
                self.connected = False
                reconnection_events.append({
                    'type': 'disconnected',
                    'connection_count': self.connection_count,
                    'timestamp': asyncio.get_event_loop().time()
                })

            async def save_state(self, state_data):
                self.state.update(state_data)

            async def restore_state(self):
                if self.state:
                    reconnection_events.append({
                        'type': 'state_restored',
                        'state': self.state.copy(),
                        'timestamp': asyncio.get_event_loop().time()
                    })
                return self.state

        # Test reconnection flow
        consumer = MockReconnectionConsumer()

        # Initial connection
        await consumer.connect()
        await consumer.save_state({'user_id': '123', 'room': 'app_456'})

        # Simulate disconnection
        await consumer.disconnect()

        # Simulate reconnection
        await consumer.connect()
        restored_state = await consumer.restore_state()

        # Verify reconnection handling
        assert len(reconnection_events) == 4  # connect, disconnect, connect, state_restored
        assert reconnection_events[0]['type'] == 'connected'
        assert reconnection_events[1]['type'] == 'disconnected'
        assert reconnection_events[2]['type'] == 'connected'
        assert reconnection_events[3]['type'] == 'state_restored'
        assert restored_state['user_id'] == '123'
        assert consumer.connection_count == 2
