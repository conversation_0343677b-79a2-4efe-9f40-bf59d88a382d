<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Error Fix Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .success { 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
        }
        .error { 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #dc3545;
        }
        .warning { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffc107;
        }
        .info { 
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-color: #17a2b8;
        }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-weight: 500;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 13px;
            line-height: 1.4;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .metric {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 10px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }
        .metric-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Service Worker Error Fix Test</h1>
        <p>Comprehensive testing of service worker error fixes and network connectivity</p>

        <div class="test-section info">
            <h3>📋 Test Coverage</h3>
            <ul>
                <li>✅ Background fetch error handling</li>
                <li>✅ Network connection error resolution</li>
                <li>✅ Favicon.ico loading and fallback</li>
                <li>✅ Service worker cache strategy</li>
                <li>✅ Offline functionality</li>
                <li>✅ Error logging improvements</li>
                <li>✅ Resource loading reliability</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <button onclick="runCompleteTest()">🚀 Run Complete Test</button>
            <button onclick="testFaviconHandling()">🎯 Test Favicon Handling</button>
            <button onclick="testNetworkConnectivity()">🌐 Test Network Connectivity</button>
            <button onclick="testServiceWorkerErrors()">⚠️ Test Error Handling</button>
            <button onclick="simulateOfflineMode()">📴 Simulate Offline</button>
            <button onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <div class="test-grid">
            <div class="metric">
                <div class="metric-value" id="errorCount">0</div>
                <div class="metric-label">Errors Detected</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="successCount">0</div>
                <div class="metric-label">Tests Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="networkStatus">Online</div>
                <div class="metric-label">Network Status</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="swStatus">Unknown</div>
                <div class="metric-label">SW Status</div>
            </div>
        </div>

        <div class="test-section" id="resultsSection">
            <h3>📊 Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>📝 Test Log</h3>
            <div id="log" class="log">Ready to test service worker error fixes...\n</div>
        </div>
    </div>

    <script>
        let errorCount = 0;
        let successCount = 0;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateMetrics() {
            document.getElementById('errorCount').textContent = errorCount;
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('networkStatus').textContent = navigator.onLine ? 'Online' : 'Offline';
        }

        function addResult(testName, success, message, details = '') {
            if (success) {
                successCount++;
            } else {
                errorCount++;
            }
            
            const resultsElement = document.getElementById('results');
            const statusClass = success ? 'success' : 'error';
            const statusIcon = success ? '✅' : '❌';
            const statusIndicator = success ? 'status-success' : 'status-error';
            
            const resultHtml = `
                <div class="test-section ${statusClass}">
                    <h4><span class="status-indicator ${statusIndicator}"></span>${statusIcon} ${testName}</h4>
                    <p><strong>Result:</strong> ${message}</p>
                    ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
                </div>
            `;
            
            resultsElement.innerHTML += resultHtml;
            updateMetrics();
        }

        async function testFaviconHandling() {
            log('🎯 Testing favicon handling...');
            
            try {
                // Test direct favicon access
                const response = await fetch('/favicon.ico', { cache: 'no-cache' });
                if (response.ok) {
                    addResult('Favicon Direct Access', true, `Status: ${response.status}`);
                    log('✅ Favicon loads correctly');
                } else {
                    addResult('Favicon Direct Access', false, `Status: ${response.status}`);
                    log(`❌ Favicon failed with status: ${response.status}`);
                }
                
                // Test favicon in service worker cache
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    let faviconCached = false;
                    
                    for (const cacheName of cacheNames) {
                        const cache = await caches.open(cacheName);
                        const cachedResponse = await cache.match('/favicon.ico');
                        if (cachedResponse) {
                            faviconCached = true;
                            break;
                        }
                    }
                    
                    addResult('Favicon Caching', faviconCached, faviconCached ? 'Favicon is cached' : 'Favicon not in cache');
                    log(`${faviconCached ? '✅' : 'ℹ️'} Favicon caching: ${faviconCached ? 'Cached' : 'Not cached'}`);
                }
                
            } catch (error) {
                addResult('Favicon Handling', false, `Error: ${error.message}`);
                log(`❌ Favicon test error: ${error.message}`);
            }
        }

        async function testNetworkConnectivity() {
            log('🌐 Testing network connectivity...');
            
            // Test basic connectivity
            const isOnline = navigator.onLine;
            addResult('Browser Online Status', isOnline, `Navigator.onLine: ${isOnline}`);
            log(`${isOnline ? '✅' : '❌'} Browser reports online: ${isOnline}`);
            
            // Test actual network requests
            const testUrls = [
                '/',
                '/manifest.json',
                '/logo.svg',
                '/favicon.ico'
            ];
            
            for (const url of testUrls) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(url, { cache: 'no-cache' });
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);
                    
                    if (response.ok) {
                        addResult(`Network Test: ${url}`, true, `Status: ${response.status} (${responseTime}ms)`);
                        log(`✅ ${url} - ${response.status} (${responseTime}ms)`);
                    } else {
                        addResult(`Network Test: ${url}`, false, `Status: ${response.status}`);
                        log(`❌ ${url} - ${response.status}`);
                    }
                } catch (error) {
                    addResult(`Network Test: ${url}`, false, `Error: ${error.message}`);
                    log(`❌ ${url} - ${error.message}`);
                }
            }
        }

        async function testServiceWorkerErrors() {
            log('⚠️ Testing service worker error handling...');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        addResult('Service Worker Registration', true, 'Service Worker is registered');
                        log('✅ Service Worker is registered');
                        
                        document.getElementById('swStatus').textContent = 'Active';
                        
                        // Test service worker message handling
                        if (registration.active) {
                            registration.active.postMessage({ action: 'test', timestamp: Date.now() });
                            addResult('Service Worker Communication', true, 'Message sent to service worker');
                            log('✅ Service Worker communication test sent');
                        }
                        
                    } else {
                        addResult('Service Worker Registration', false, 'Service Worker not registered');
                        log('❌ Service Worker not registered');
                        document.getElementById('swStatus').textContent = 'Not Registered';
                    }
                } catch (error) {
                    addResult('Service Worker Error Test', false, `Error: ${error.message}`);
                    log(`❌ Service Worker error: ${error.message}`);
                    document.getElementById('swStatus').textContent = 'Error';
                }
            } else {
                addResult('Service Worker Support', false, 'Service Worker not supported');
                log('❌ Service Worker not supported');
                document.getElementById('swStatus').textContent = 'Not Supported';
            }
        }

        async function simulateOfflineMode() {
            log('📴 Simulating offline mode...');
            
            // Note: We can't actually control navigator.onLine, but we can test offline scenarios
            addResult('Offline Simulation', true, 'Testing offline resource access');
            log('ℹ️ Testing cached resource access (simulating offline)');
            
            // Test cached resources
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                log(`Found ${cacheNames.length} cache(s): ${cacheNames.join(', ')}`);
                
                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const requests = await cache.keys();
                    log(`Cache "${cacheName}" contains ${requests.length} entries`);
                    
                    // Test a few cached resources
                    const testRequests = requests.slice(0, 3);
                    for (const request of testRequests) {
                        const cachedResponse = await cache.match(request);
                        if (cachedResponse) {
                            log(`✅ Cached resource available: ${request.url}`);
                        }
                    }
                }
                
                addResult('Offline Resource Access', true, `${cacheNames.length} cache(s) available for offline use`);
            }
        }

        async function runCompleteTest() {
            log('🚀 Starting complete service worker error fix test...');
            
            // Reset counters
            errorCount = 0;
            successCount = 0;
            document.getElementById('results').innerHTML = '';
            
            // Run all tests
            await testServiceWorkerErrors();
            await testFaviconHandling();
            await testNetworkConnectivity();
            await simulateOfflineMode();
            
            log('\n📊 Complete test finished');
            log(`Results: ${successCount} passed, ${errorCount} failed`);
            
            if (errorCount === 0) {
                log('🎉 All service worker error fixes are working correctly!');
            } else {
                log('⚠️ Some issues detected. Check the results above.');
            }
            
            updateMetrics();
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Ready to test service worker error fixes...\n';
            document.getElementById('results').innerHTML = '';
            errorCount = 0;
            successCount = 0;
            updateMetrics();
        }

        // Monitor network status changes
        window.addEventListener('online', () => {
            log('🌐 Network status: Online');
            updateMetrics();
        });

        window.addEventListener('offline', () => {
            log('📴 Network status: Offline');
            updateMetrics();
        });

        // Auto-run basic check when page loads
        window.addEventListener('load', () => {
            log('🔧 Service Worker Error Fix Test loaded');
            updateMetrics();
            
            // Quick initial check
            setTimeout(() => {
                log('Running initial service worker check...');
                testServiceWorkerErrors();
            }, 1000);
        });
    </script>
</body>
</html>
