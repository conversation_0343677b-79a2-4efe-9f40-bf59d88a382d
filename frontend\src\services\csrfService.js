/**
 * CSRF Service
 *
 * This service handles CSRF token management for API requests.
 * It fetches the CSRF token from the backend and provides utilities
 * to include it in requests.
 */

import { API_ENDPOINTS } from '../config/api';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';

class CSRFService {
  constructor() {
    this.token = null;
    this.tokenPromise = null;
  }

  /**
   * Get CSRF token from cookie
   * @returns {string|null} CSRF token
   */
  getTokenFromCookie() {
    const name = 'csrftoken';
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop().split(';').shift();
    }
    return null;
  }

  /**
   * Fetch CSRF token from backend
   * @returns {Promise<string>} CSRF token
   */
  async fetchToken() {
    try {
      const response = await fetch(API_ENDPOINTS.CSRF_TOKEN, {
        method: 'GET',
        credentials: 'include', // Include cookies
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch CSRF token: ${response.status}`);
      }

      const data = await response.json();
      this.token = data.csrfToken;
      return this.token;
    } catch (error) {
      console.error('Error fetching CSRF token:', error);
      throw error;
    }
  }

  /**
   * Get CSRF token (from cache, cookie, or fetch from backend)
   * @returns {Promise<string>} CSRF token
   */
  async getToken() {
    // Return cached token if available
    if (this.token) {
      return this.token;
    }

    // Return existing promise if already fetching
    if (this.tokenPromise) {
      return this.tokenPromise;
    }

    // Try to get token from cookie first
    const cookieToken = this.getTokenFromCookie();
    if (cookieToken) {
      this.token = cookieToken;
      return this.token;
    }

    // Fetch token from backend
    this.tokenPromise = this.fetchToken();

    try {
      const token = await this.tokenPromise;
      this.tokenPromise = null;
      return token;
    } catch (error) {
      this.tokenPromise = null;
      throw error;
    }
  }

  /**
   * Clear cached token
   */
  clearToken() {
    this.token = null;
    this.tokenPromise = null;
  }

  /**
   * Get headers with CSRF token
   * @param {Object} additionalHeaders - Additional headers to include
   * @returns {Promise<Object>} Headers object with CSRF token
   */
  async getHeaders(additionalHeaders = {}) {
    try {
      const token = await this.getToken();
      return {
        'X-CSRFToken': token,
        'Content-Type': 'application/json',
        ...additionalHeaders,
      };
    } catch (error) {
      console.warn('Failed to get CSRF token, proceeding without it:', error);
      return {
        'Content-Type': 'application/json',
        ...additionalHeaders,
      };
    }
  }

  /**
   * Make a request with CSRF token
   * @param {string} url - Request URL
   * @param {Object} options - Fetch options
   * @returns {Promise<Response>} Fetch response
   */
  async request(url, options = {}) {
    const headers = await this.getHeaders(options.headers);

    return fetch(url, {
      ...options,
      headers,
      credentials: 'include', // Include cookies
    });
  }

  /**
   * Initialize CSRF service by fetching token
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this.getToken();
      console.log('CSRF service initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize CSRF service:', error);
    }
  }
}

// Create and export singleton instance
const csrfService = new CSRFService();

export default csrfService;

// Export utility functions
export const getCSRFToken = () => csrfService.getToken();
export const getCSRFHeaders = (additionalHeaders) => csrfService.getHeaders(additionalHeaders);
export const makeCSRFRequest = (url, options) => csrfService.request(url, options);
export const initializeCSRF = () => csrfService.initialize();
export const clearCSRFToken = () => csrfService.clearToken();
