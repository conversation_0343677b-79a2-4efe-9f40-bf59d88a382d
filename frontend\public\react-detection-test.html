<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Detection & Debugging Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .success { 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
        }
        .error { 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #dc3545;
        }
        .warning { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffc107;
        }
        .info { 
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-color: #17a2b8;
        }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-weight: 500;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 13px;
            line-height: 1.4;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 React Detection & Debugging Test</h1>
        <p>Comprehensive analysis of React framework loading and availability</p>

        <div class="test-section info">
            <h3>📋 What This Test Checks</h3>
            <ul>
                <li>✅ React global availability (window.React)</li>
                <li>✅ React version and build information</li>
                <li>✅ ReactDOM availability and functionality</li>
                <li>✅ Bundle loading and script errors</li>
                <li>✅ DOM mounting and component rendering</li>
                <li>✅ Service worker interference detection</li>
                <li>✅ Network requests and resource loading</li>
                <li>✅ Console errors and warnings</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <button onclick="runCompleteReactTest()">🚀 Run Complete React Test</button>
            <button onclick="testReactGlobal()">🌐 Test React Global</button>
            <button onclick="testBundleLoading()">📦 Test Bundle Loading</button>
            <button onclick="testDOMRendering()">🏗️ Test DOM Rendering</button>
            <button onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <div class="test-section" id="resultsSection">
            <h3>📊 Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>📝 Detailed Log</h3>
            <div id="log" class="log">Ready to run React detection tests...\n</div>
        </div>

        <div class="test-section">
            <h3>🧪 Manual Test Area</h3>
            <div id="reactTestContainer"></div>
            <button onclick="createTestComponent()">Create Test React Component</button>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function addResult(testName, success, message, details = '') {
            const resultsElement = document.getElementById('results');
            const statusClass = success ? 'success' : 'error';
            const statusIcon = success ? '✅' : '❌';
            const statusIndicator = success ? 'status-success' : 'status-error';
            
            const resultHtml = `
                <div class="test-section ${statusClass}">
                    <h4><span class="status-indicator ${statusIndicator}"></span>${statusIcon} ${testName}</h4>
                    <p><strong>Result:</strong> ${message}</p>
                    ${details ? `<div class="code-block">${details}</div>` : ''}
                </div>
            `;
            
            resultsElement.innerHTML += resultHtml;
        }

        function testReactGlobal() {
            log('🌐 Testing React Global Availability...');
            
            // Test 1: Check if React is available globally
            const reactAvailable = typeof window.React !== 'undefined';
            if (reactAvailable) {
                const reactVersion = window.React.version || 'Unknown';
                addResult('React Global Availability', true, `React is available globally`, `Version: ${reactVersion}\nType: ${typeof window.React}\nKeys: ${Object.keys(window.React).slice(0, 10).join(', ')}...`);
                log(`✅ React available globally - Version: ${reactVersion}`);
            } else {
                addResult('React Global Availability', false, 'React is not available in global scope');
                log('❌ React not available globally');
            }

            // Test 2: Check ReactDOM
            const reactDOMAvailable = typeof window.ReactDOM !== 'undefined';
            if (reactDOMAvailable) {
                addResult('ReactDOM Global Availability', true, 'ReactDOM is available globally', `Type: ${typeof window.ReactDOM}\nKeys: ${Object.keys(window.ReactDOM).join(', ')}`);
                log('✅ ReactDOM available globally');
            } else {
                addResult('ReactDOM Global Availability', false, 'ReactDOM is not available in global scope');
                log('❌ ReactDOM not available globally');
            }

            // Test 3: Check for React in modules
            const scripts = Array.from(document.querySelectorAll('script[src]'));
            const reactScripts = scripts.filter(script => 
                script.src.includes('react') || 
                script.src.includes('bundle') || 
                script.src.includes('main')
            );
            
            if (reactScripts.length > 0) {
                const scriptUrls = reactScripts.map(s => s.src).join('\n');
                addResult('React Scripts Detection', true, `Found ${reactScripts.length} potential React scripts`, scriptUrls);
                log(`✅ Found ${reactScripts.length} React-related scripts`);
            } else {
                addResult('React Scripts Detection', false, 'No React scripts detected');
                log('❌ No React scripts found');
            }

            return reactAvailable;
        }

        function testBundleLoading() {
            log('📦 Testing Bundle Loading...');
            
            // Check for main bundle
            const scripts = Array.from(document.querySelectorAll('script'));
            const bundleScripts = scripts.filter(script => 
                script.src && (
                    script.src.includes('bundle') || 
                    script.src.includes('main') ||
                    script.src.includes('chunk')
                )
            );

            if (bundleScripts.length > 0) {
                const bundleInfo = bundleScripts.map(script => ({
                    src: script.src,
                    loaded: script.readyState === 'complete' || !script.readyState,
                    error: script.onerror !== null
                }));
                
                const allLoaded = bundleInfo.every(bundle => bundle.loaded);
                const bundleDetails = bundleInfo.map(b => `${b.src} - ${b.loaded ? 'Loaded' : 'Loading'}`).join('\n');
                
                addResult('Bundle Loading', allLoaded, `${bundleScripts.length} bundles detected`, bundleDetails);
                log(`${allLoaded ? '✅' : '⚠️'} Bundle loading status: ${allLoaded ? 'All loaded' : 'Some still loading'}`);
            } else {
                addResult('Bundle Loading', false, 'No bundle scripts detected');
                log('❌ No bundle scripts found');
            }

            // Check for errors in console
            const originalError = console.error;
            const errors = [];
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };

            setTimeout(() => {
                console.error = originalError;
                if (errors.length > 0) {
                    addResult('Console Errors', false, `${errors.length} errors detected`, errors.join('\n'));
                    log(`❌ Found ${errors.length} console errors`);
                } else {
                    addResult('Console Errors', true, 'No console errors detected');
                    log('✅ No console errors found');
                }
            }, 1000);
        }

        function testDOMRendering() {
            log('🏗️ Testing DOM Rendering...');
            
            // Check for React root
            const rootElement = document.getElementById('root');
            if (rootElement) {
                const hasContent = rootElement.children.length > 0;
                const contentInfo = `Children: ${rootElement.children.length}\nInnerHTML length: ${rootElement.innerHTML.length}`;
                
                addResult('React Root Element', true, 'Root element found', contentInfo);
                log(`✅ React root element found with ${rootElement.children.length} children`);
                
                if (hasContent) {
                    addResult('React Content Rendering', true, 'Content is rendered in root element');
                    log('✅ React content is rendered');
                } else {
                    addResult('React Content Rendering', false, 'Root element is empty');
                    log('❌ React root element is empty');
                }
            } else {
                addResult('React Root Element', false, 'Root element not found');
                log('❌ React root element not found');
            }

            // Check for React-specific attributes
            const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
            if (reactElements.length > 0) {
                addResult('React DOM Attributes', true, `Found ${reactElements.length} React DOM attributes`);
                log(`✅ Found ${reactElements.length} React DOM attributes`);
            } else {
                addResult('React DOM Attributes', false, 'No React DOM attributes found');
                log('❌ No React DOM attributes found');
            }
        }

        function createTestComponent() {
            log('🧪 Creating test React component...');
            
            if (typeof window.React === 'undefined') {
                log('❌ Cannot create test component - React not available');
                return;
            }

            try {
                const testContainer = document.getElementById('reactTestContainer');
                
                // Create a simple React element
                const TestComponent = window.React.createElement('div', {
                    style: { 
                        padding: '20px', 
                        background: '#e8f5e8', 
                        border: '2px solid #4caf50',
                        borderRadius: '8px',
                        margin: '10px 0'
                    }
                }, 
                    window.React.createElement('h4', null, '🎉 React Test Component'),
                    window.React.createElement('p', null, 'This component was created using React.createElement!'),
                    window.React.createElement('p', null, `React Version: ${window.React.version || 'Unknown'}`)
                );

                // Try to render it
                if (window.ReactDOM && window.ReactDOM.createRoot) {
                    const root = window.ReactDOM.createRoot(testContainer);
                    root.render(TestComponent);
                    log('✅ Test component created and rendered successfully!');
                    addResult('Test Component Creation', true, 'React component created and rendered');
                } else {
                    log('❌ ReactDOM.createRoot not available');
                    addResult('Test Component Creation', false, 'ReactDOM.createRoot not available');
                }
            } catch (error) {
                log(`❌ Error creating test component: ${error.message}`);
                addResult('Test Component Creation', false, `Error: ${error.message}`);
            }
        }

        async function runCompleteReactTest() {
            log('🚀 Starting Complete React Detection Test...');
            document.getElementById('results').innerHTML = '';
            
            // Run all tests
            testReactGlobal();
            testBundleLoading();
            testDOMRendering();
            
            // Wait a moment for async operations
            setTimeout(() => {
                log('\n📊 Complete React Test Finished');
                log('Check the results above for detailed information');
            }, 2000);
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Ready to run React detection tests...\n';
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run basic test when page loads
        window.addEventListener('load', () => {
            log('🔧 React Detection Test loaded');
            log('Click "Run Complete React Test" to start comprehensive testing');
            
            // Quick initial check
            setTimeout(() => {
                const reactAvailable = typeof window.React !== 'undefined';
                log(`Initial React check: ${reactAvailable ? '✅ Available' : '❌ Not Available'}`);
            }, 1000);
        });
    </script>
</body>
</html>
