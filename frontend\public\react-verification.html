<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Verification - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 React Application Verification</h1>
        <p>This page verifies that the React application is loading and working correctly.</p>
        
        <div class="grid">
            <div class="section">
                <h3>🚀 Quick Actions</h3>
                <button onclick="runVerification()">🔄 Run Verification</button>
                <button onclick="openMainApp()">📱 Open Main App</button>
                <button onclick="clearConsole()">🗑️ Clear Console</button>
                <button onclick="refreshPage()">🔄 Refresh Page</button>
            </div>
            
            <div class="section">
                <h3>📊 Status</h3>
                <div id="status-display">
                    <div class="status info">Ready to run verification...</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📱 Main App Preview</h3>
            <iframe id="app-frame" src="/" title="Main React App"></iframe>
        </div>

        <div class="section">
            <h3>📋 Verification Console Output</h3>
            <pre id="console-output">Click "Run Verification" to start...</pre>
        </div>

        <div class="section">
            <h3>🔧 Manual Tests</h3>
            <button onclick="testReactGlobal()">Test React Global</button>
            <button onclick="testRootElement()">Test Root Element</button>
            <button onclick="testBundleLoading()">Test Bundle Loading</button>
            <button onclick="testFrameContent()">Test Frame Content</button>
        </div>
    </div>

    <script>
        let consoleOutput = [];
        
        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function captureConsole(type, args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.join(' ');
            consoleOutput.push(`[${timestamp}] ${message}`);
            updateConsoleDisplay();
        }
        
        console.log = function(...args) {
            captureConsole('LOG', args);
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            captureConsole('ERROR', args);
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            captureConsole('WARN', args);
            originalWarn.apply(console, args);
        };

        function updateConsoleDisplay() {
            document.getElementById('console-output').textContent = consoleOutput.slice(-100).join('\n');
        }

        function clearConsole() {
            consoleOutput = [];
            updateConsoleDisplay();
        }

        function updateStatus(message, type = 'info') {
            document.getElementById('status-display').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
        }

        function runVerification() {
            updateStatus('🔄 Running verification...', 'info');
            clearConsole();
            
            // Load and run the verification script
            const script = document.createElement('script');
            script.src = '/verify-react.js';
            script.onload = () => {
                console.log('🔍 Verification script loaded');
                if (typeof window.verifyReactApplication === 'function') {
                    setTimeout(() => {
                        const result = window.verifyReactApplication();
                        if (result >= 0.8) {
                            updateStatus('✅ Verification completed successfully!', 'success');
                        } else if (result >= 0.5) {
                            updateStatus('⚠️ Verification completed with warnings', 'warning');
                        } else {
                            updateStatus('❌ Verification found issues', 'error');
                        }
                    }, 1000);
                } else {
                    updateStatus('❌ Verification script not available', 'error');
                }
            };
            script.onerror = () => {
                updateStatus('❌ Failed to load verification script', 'error');
            };
            document.head.appendChild(script);
        }

        function openMainApp() {
            window.open('/', '_blank');
        }

        function refreshPage() {
            window.location.reload();
        }

        function testReactGlobal() {
            console.log('🧪 Testing React global availability...');
            if (typeof window.React !== 'undefined') {
                console.log('✅ React is available globally');
                console.log(`Version: ${window.React.version || 'Unknown'}`);
            } else {
                console.log('❌ React is not available globally');
            }
        }

        function testRootElement() {
            console.log('🧪 Testing root element...');
            const root = document.getElementById('root');
            if (root) {
                console.log('✅ Root element found in current page');
            } else {
                console.log('❌ Root element not found in current page');
            }
            
            // Test in iframe
            try {
                const iframe = document.getElementById('app-frame');
                const iframeDoc = iframe.contentDocument;
                if (iframeDoc) {
                    const iframeRoot = iframeDoc.getElementById('root');
                    if (iframeRoot) {
                        console.log('✅ Root element found in iframe');
                        console.log(`Iframe root has ${iframeRoot.children.length} children`);
                    } else {
                        console.log('❌ Root element not found in iframe');
                    }
                }
            } catch (error) {
                console.log(`⚠️ Cannot access iframe content: ${error.message}`);
            }
        }

        function testBundleLoading() {
            console.log('🧪 Testing bundle loading...');
            
            fetch('/static/js/main.92165ac7.js')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Main bundle is accessible');
                        const size = response.headers.get('content-length');
                        if (size) {
                            console.log(`Bundle size: ${Math.round(size / 1024 / 1024 * 100) / 100} MB`);
                        }
                    } else {
                        console.log(`❌ Main bundle not accessible: ${response.status}`);
                    }
                })
                .catch(error => {
                    console.log(`❌ Error accessing main bundle: ${error.message}`);
                });
        }

        function testFrameContent() {
            console.log('🧪 Testing iframe content...');
            
            const iframe = document.getElementById('app-frame');
            
            iframe.onload = () => {
                setTimeout(() => {
                    try {
                        const iframeWindow = iframe.contentWindow;
                        const iframeDoc = iframe.contentDocument;
                        
                        if (iframeDoc) {
                            console.log('✅ Iframe document accessible');
                            console.log(`Document title: ${iframeDoc.title}`);
                            console.log(`Document ready state: ${iframeDoc.readyState}`);
                            
                            if (iframeWindow.React) {
                                console.log('✅ React available in iframe');
                            } else {
                                console.log('❌ React not available in iframe');
                            }
                        }
                    } catch (error) {
                        console.log(`❌ Cannot access iframe: ${error.message}`);
                    }
                }, 2000);
            };
            
            // Trigger reload if already loaded
            if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                iframe.src = iframe.src;
            }
        }

        // Auto-run verification when page loads
        window.addEventListener('load', () => {
            setTimeout(runVerification, 1000);
        });
    </script>
</body>
</html>
