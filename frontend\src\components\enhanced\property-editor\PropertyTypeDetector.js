/**
 * Property Type Detection System
 * Automatically detects property types and provides metadata for rendering appropriate UI controls
 */

// Property type definitions
export const PropertyTypes = {
  TEXT: 'text',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  COLOR: 'color',
  SELECT: 'select',
  SPACING: 'spacing',
  BORDER: 'border',
  SHADOW: 'shadow',
  FONT: 'font',
  ARRAY: 'array',
  OBJECT: 'object',
  JSON: 'json'
};

// Component property schemas
export const ComponentSchemas = {
  button: {
    text: { type: PropertyTypes.TEXT, label: 'Button Text', placeholder: 'Enter button text' },
    variant: { 
      type: PropertyTypes.SELECT, 
      label: 'Variant',
      options: [
        { value: 'primary', label: 'Primary' },
        { value: 'secondary', label: 'Secondary' },
        { value: 'text', label: 'Text' },
        { value: 'link', label: 'Link' },
        { value: 'ghost', label: 'Ghost' },
        { value: 'dashed', label: 'Dashed' }
      ]
    },
    size: {
      type: PropertyTypes.SELECT,
      label: 'Size',
      options: [
        { value: 'small', label: 'Small' },
        { value: 'medium', label: 'Medium' },
        { value: 'large', label: 'Large' }
      ]
    },
    disabled: { type: PropertyTypes.BOOLEAN, label: 'Disabled' },
    block: { type: PropertyTypes.BOOLEAN, label: 'Full Width' },
    onClick: { type: PropertyTypes.TEXT, label: 'onClick Handler', placeholder: 'Enter function name' }
  },
  
  text: {
    content: { type: PropertyTypes.TEXT, label: 'Text Content', multiline: true, placeholder: 'Enter text content' },
    variant: {
      type: PropertyTypes.SELECT,
      label: 'Variant',
      options: [
        { value: 'h1', label: 'Heading 1' },
        { value: 'h2', label: 'Heading 2' },
        { value: 'h3', label: 'Heading 3' },
        { value: 'h4', label: 'Heading 4' },
        { value: 'h5', label: 'Heading 5' },
        { value: 'h6', label: 'Heading 6' },
        { value: 'p', label: 'Paragraph' },
        { value: 'span', label: 'Span' }
      ]
    },
    color: { type: PropertyTypes.COLOR, label: 'Text Color' },
    align: {
      type: PropertyTypes.SELECT,
      label: 'Text Alignment',
      options: [
        { value: 'left', label: 'Left' },
        { value: 'center', label: 'Center' },
        { value: 'right', label: 'Right' },
        { value: 'justify', label: 'Justify' }
      ]
    }
  },
  
  input: {
    label: { type: PropertyTypes.TEXT, label: 'Input Label', placeholder: 'Enter input label' },
    placeholder: { type: PropertyTypes.TEXT, label: 'Placeholder', placeholder: 'Enter placeholder text' },
    type: {
      type: PropertyTypes.SELECT,
      label: 'Input Type',
      options: [
        { value: 'text', label: 'Text' },
        { value: 'password', label: 'Password' },
        { value: 'email', label: 'Email' },
        { value: 'number', label: 'Number' },
        { value: 'tel', label: 'Telephone' },
        { value: 'url', label: 'URL' }
      ]
    },
    required: { type: PropertyTypes.BOOLEAN, label: 'Required' },
    disabled: { type: PropertyTypes.BOOLEAN, label: 'Disabled' },
    validation: {
      type: PropertyTypes.SELECT,
      label: 'Validation',
      options: [
        { value: 'none', label: 'None' },
        { value: 'email', label: 'Email' },
        { value: 'url', label: 'URL' },
        { value: 'phone', label: 'Phone' },
        { value: 'custom', label: 'Custom' }
      ]
    }
  },
  
  card: {
    title: { type: PropertyTypes.TEXT, label: 'Card Title', placeholder: 'Enter card title' },
    description: { type: PropertyTypes.TEXT, label: 'Description', multiline: true, placeholder: 'Enter card description' },
    image: { type: PropertyTypes.TEXT, label: 'Image URL', placeholder: 'Enter image URL' },
    elevation: {
      type: PropertyTypes.SELECT,
      label: 'Elevation',
      options: [
        { value: 'none', label: 'None' },
        { value: 'sm', label: 'Small' },
        { value: 'md', label: 'Medium' },
        { value: 'lg', label: 'Large' }
      ]
    },
    bordered: { type: PropertyTypes.BOOLEAN, label: 'Bordered' }
  }
};

// Style property schemas (common across all components)
export const StyleSchemas = {
  // Dimensions
  width: { type: PropertyTypes.TEXT, label: 'Width', placeholder: 'e.g., 100%, 200px', group: 'dimensions' },
  height: { type: PropertyTypes.TEXT, label: 'Height', placeholder: 'e.g., 100%, 200px', group: 'dimensions' },
  minWidth: { type: PropertyTypes.TEXT, label: 'Min Width', placeholder: 'e.g., 100px', group: 'dimensions' },
  maxWidth: { type: PropertyTypes.TEXT, label: 'Max Width', placeholder: 'e.g., 500px', group: 'dimensions' },
  minHeight: { type: PropertyTypes.TEXT, label: 'Min Height', placeholder: 'e.g., 100px', group: 'dimensions' },
  maxHeight: { type: PropertyTypes.TEXT, label: 'Max Height', placeholder: 'e.g., 500px', group: 'dimensions' },
  
  // Spacing
  margin: { type: PropertyTypes.SPACING, label: 'Margin', group: 'spacing' },
  padding: { type: PropertyTypes.SPACING, label: 'Padding', group: 'spacing' },
  
  // Typography
  fontSize: { 
    type: PropertyTypes.NUMBER, 
    label: 'Font Size', 
    min: 8, 
    max: 72, 
    unit: 'px',
    units: ['px', 'em', 'rem', '%'],
    group: 'typography' 
  },
  fontWeight: {
    type: PropertyTypes.SELECT,
    label: 'Font Weight',
    options: [
      { value: 'normal', label: 'Normal' },
      { value: 'bold', label: 'Bold' },
      { value: 'lighter', label: 'Lighter' },
      { value: 'bolder', label: 'Bolder' },
      { value: '100', label: '100' },
      { value: '200', label: '200' },
      { value: '300', label: '300' },
      { value: '400', label: '400' },
      { value: '500', label: '500' },
      { value: '600', label: '600' },
      { value: '700', label: '700' },
      { value: '800', label: '800' },
      { value: '900', label: '900' }
    ],
    group: 'typography'
  },
  lineHeight: { 
    type: PropertyTypes.NUMBER, 
    label: 'Line Height', 
    min: 0.5, 
    max: 3, 
    step: 0.1,
    precision: 1,
    showUnit: false,
    group: 'typography' 
  },
  fontFamily: { type: PropertyTypes.FONT, label: 'Font Family', group: 'typography' },
  
  // Colors
  color: { type: PropertyTypes.COLOR, label: 'Text Color', group: 'colors' },
  backgroundColor: { type: PropertyTypes.COLOR, label: 'Background Color', group: 'colors' },
  
  // Border
  border: { type: PropertyTypes.BORDER, label: 'Border', group: 'border' },
  borderTop: { type: PropertyTypes.BORDER, label: 'Border Top', group: 'border' },
  borderRight: { type: PropertyTypes.BORDER, label: 'Border Right', group: 'border' },
  borderBottom: { type: PropertyTypes.BORDER, label: 'Border Bottom', group: 'border' },
  borderLeft: { type: PropertyTypes.BORDER, label: 'Border Left', group: 'border' },
  borderRadius: { 
    type: PropertyTypes.NUMBER, 
    label: 'Border Radius', 
    min: 0, 
    max: 50, 
    unit: 'px',
    units: ['px', 'em', 'rem', '%'],
    group: 'border' 
  },
  
  // Shadow
  boxShadow: { type: PropertyTypes.SHADOW, label: 'Box Shadow', group: 'shadow' },
  textShadow: { type: PropertyTypes.SHADOW, label: 'Text Shadow', group: 'shadow' },
  
  // Layout
  display: {
    type: PropertyTypes.SELECT,
    label: 'Display',
    options: [
      { value: 'block', label: 'Block' },
      { value: 'inline', label: 'Inline' },
      { value: 'inline-block', label: 'Inline Block' },
      { value: 'flex', label: 'Flex' },
      { value: 'grid', label: 'Grid' },
      { value: 'none', label: 'None' }
    ],
    group: 'layout'
  },
  position: {
    type: PropertyTypes.SELECT,
    label: 'Position',
    options: [
      { value: 'static', label: 'Static' },
      { value: 'relative', label: 'Relative' },
      { value: 'absolute', label: 'Absolute' },
      { value: 'fixed', label: 'Fixed' },
      { value: 'sticky', label: 'Sticky' }
    ],
    group: 'layout'
  }
};

/**
 * Detect property type based on property name and value
 */
export const detectPropertyType = (propertyName, value, componentType) => {
  // Check component-specific schema first
  if (componentType && ComponentSchemas[componentType] && ComponentSchemas[componentType][propertyName]) {
    return ComponentSchemas[componentType][propertyName];
  }
  
  // Check style schema
  if (StyleSchemas[propertyName]) {
    return StyleSchemas[propertyName];
  }
  
  // Fallback to value-based detection
  return detectTypeFromValue(propertyName, value);
};

/**
 * Detect property type from value and property name patterns
 */
export const detectTypeFromValue = (propertyName, value) => {
  const name = propertyName.toLowerCase();
  
  // Color detection
  if (name.includes('color') || name.includes('background')) {
    return { type: PropertyTypes.COLOR, label: formatLabel(propertyName) };
  }
  
  // Number detection
  if (typeof value === 'number' || (typeof value === 'string' && /^\d+(\.\d+)?(px|em|rem|%)?$/.test(value))) {
    return { type: PropertyTypes.NUMBER, label: formatLabel(propertyName) };
  }
  
  // Boolean detection
  if (typeof value === 'boolean') {
    return { type: PropertyTypes.BOOLEAN, label: formatLabel(propertyName) };
  }
  
  // Spacing detection
  if (name.includes('margin') || name.includes('padding')) {
    return { type: PropertyTypes.SPACING, label: formatLabel(propertyName) };
  }
  
  // Border detection
  if (name.includes('border') && !name.includes('radius')) {
    return { type: PropertyTypes.BORDER, label: formatLabel(propertyName) };
  }
  
  // Shadow detection
  if (name.includes('shadow')) {
    return { type: PropertyTypes.SHADOW, label: formatLabel(propertyName) };
  }
  
  // Font detection
  if (name.includes('font') && !name.includes('size') && !name.includes('weight')) {
    return { type: PropertyTypes.FONT, label: formatLabel(propertyName) };
  }
  
  // Array detection
  if (Array.isArray(value)) {
    return { type: PropertyTypes.ARRAY, label: formatLabel(propertyName) };
  }
  
  // Object detection
  if (typeof value === 'object' && value !== null) {
    return { type: PropertyTypes.OBJECT, label: formatLabel(propertyName) };
  }
  
  // Default to text
  return { type: PropertyTypes.TEXT, label: formatLabel(propertyName) };
};

/**
 * Format property name into a readable label
 */
export const formatLabel = (propertyName) => {
  return propertyName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
};

/**
 * Get all properties for a component type
 */
export const getComponentProperties = (componentType) => {
  return ComponentSchemas[componentType] || {};
};

/**
 * Get style properties grouped by category
 */
export const getStylePropertiesGrouped = () => {
  const grouped = {};
  
  Object.entries(StyleSchemas).forEach(([key, schema]) => {
    const group = schema.group || 'other';
    if (!grouped[group]) {
      grouped[group] = {};
    }
    grouped[group][key] = schema;
  });
  
  return grouped;
};

/**
 * Validate property value based on its type
 */
export const validatePropertyValue = (value, schema) => {
  if (!schema) return { valid: true };
  
  switch (schema.type) {
    case PropertyTypes.NUMBER:
      const num = parseFloat(value);
      if (isNaN(num)) {
        return { valid: false, error: 'Must be a valid number' };
      }
      if (schema.min !== undefined && num < schema.min) {
        return { valid: false, error: `Must be at least ${schema.min}` };
      }
      if (schema.max !== undefined && num > schema.max) {
        return { valid: false, error: `Must be at most ${schema.max}` };
      }
      break;
      
    case PropertyTypes.COLOR:
      const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/;
      if (value && !colorRegex.test(value)) {
        return { valid: false, error: 'Must be a valid color value' };
      }
      break;
      
    case PropertyTypes.SELECT:
      if (schema.options && value && !schema.options.some(opt => opt.value === value)) {
        return { valid: false, error: 'Must be one of the available options' };
      }
      break;
  }
  
  return { valid: true };
};
