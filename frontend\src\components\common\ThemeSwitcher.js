import React, { useState } from 'react';
import { Button, Dropdown, Menu, Switch, Tooltip, message } from 'antd';
import { 
  BgColorsOutlined, 
  CheckOutlined, 
  DownOutlined,
  BulbOutlined,
  BulbFilled
} from '@ant-design/icons';
import styled from 'styled-components';
import { useEnhancedTheme } from '../enhanced/EnhancedThemeProvider';

const ThemeSwitcherContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ThemeButton = styled(Button)`
  display: flex;
  align-items: center;
  justify-content: center;
  
  .theme-icon {
    margin-right: 8px;
  }
  
  &.theme-button-dark {
    background-color: #1f2937;
    color: #f9fafb;
    border-color: #374151;
    
    &:hover {
      background-color: #111827;
      border-color: #1f2937;
    }
  }
  
  &.theme-button-light {
    background-color: #f9fafb;
    color: #111827;
    border-color: #e5e7eb;
    
    &:hover {
      background-color: #f3f4f6;
      border-color: #d1d5db;
    }
  }
`;

const ThemeColorPreview = styled.span`
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  background-color: ${props => props.color};
  border: 1px solid rgba(0, 0, 0, 0.1);
`;

/**
 * ThemeSwitcher component
 * Provides a UI for switching between themes and theme modes
 */
const ThemeSwitcher = ({ position = 'dropdown' }) => {
  const { 
    currentTheme, 
    setThemeById, 
    availableThemes, 
    themeMode, 
    toggleThemeMode, 
    setThemeMode,
    availableThemeModes
  } = useEnhancedTheme();
  
  const [visible, setVisible] = useState(false);
  
  // Handle theme selection
  const handleThemeSelect = (themeId) => {
    setThemeById(themeId);
    setVisible(false);
  };
  
  // Handle theme mode toggle
  const handleThemeModeToggle = () => {
    toggleThemeMode();
  };
  
  // Handle specific theme mode selection
  const handleThemeModeSelect = (mode) => {
    setThemeMode(mode);
    setVisible(false);
  };
  
  // Generate theme menu items
  const themeMenuItems = availableThemes.map(theme => ({
    key: theme.id,
    label: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <ThemeColorPreview color={theme.primaryColor} />
        {theme.name}
        {currentTheme.id === theme.id && (
          <CheckOutlined style={{ marginLeft: 'auto', color: 'var(--primary-color)' }} />
        )}
      </div>
    ),
    onClick: () => handleThemeSelect(theme.id)
  }));
  
  // Generate theme mode menu items
  const themeModeMenuItems = availableThemeModes.map(mode => ({
    key: mode,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', textTransform: 'capitalize' }}>
        {mode}
        {themeMode === mode && (
          <CheckOutlined style={{ marginLeft: 'auto', color: 'var(--primary-color)' }} />
        )}
      </div>
    ),
    onClick: () => handleThemeModeSelect(mode)
  }));
  
  // Combined menu items
  const menuItems = [
    {
      key: 'themes',
      type: 'group',
      label: 'Themes',
      children: themeMenuItems
    },
    {
      key: 'divider',
      type: 'divider'
    },
    {
      key: 'modes',
      type: 'group',
      label: 'Theme Modes',
      children: themeModeMenuItems
    }
  ];
  
  // Render as a simple toggle if position is 'toggle'
  if (position === 'toggle') {
    return (
      <Tooltip title={`Switch to ${themeMode === 'light' ? 'dark' : 'light'} mode`}>
        <Button
          type="text"
          icon={themeMode === 'light' ? <BulbOutlined /> : <BulbFilled />}
          onClick={handleThemeModeToggle}
          aria-label={`Switch to ${themeMode === 'light' ? 'dark' : 'light'} mode`}
        />
      </Tooltip>
    );
  }
  
  // Render as a switch if position is 'switch'
  if (position === 'switch') {
    return (
      <ThemeSwitcherContainer>
        <BulbOutlined style={{ color: themeMode === 'dark' ? 'var(--text-color)' : undefined }} />
        <Switch
          checked={themeMode === 'dark'}
          onChange={handleThemeModeToggle}
          aria-label={`Switch to ${themeMode === 'light' ? 'dark' : 'light'} mode`}
        />
        <BulbFilled style={{ color: themeMode === 'light' ? 'var(--text-color)' : undefined }} />
      </ThemeSwitcherContainer>
    );
  }
  
  // Default: render as dropdown
  return (
    <Dropdown
      menu={{ items: menuItems }}
      trigger={['click']}
      onOpenChange={setVisible}
      open={visible}
    >
      <ThemeButton
        className={`theme-button-${themeMode}`}
      >
        <BgColorsOutlined className="theme-icon" />
        {currentTheme.name}
        <DownOutlined style={{ marginLeft: 8 }} />
      </ThemeButton>
    </Dropdown>
  );
};

export default ThemeSwitcher;
