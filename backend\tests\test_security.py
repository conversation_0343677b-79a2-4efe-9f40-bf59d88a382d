"""
Security tests for the Django backend
Tests for authentication, authorization, input validation, and security headers
"""

import pytest
import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.conf import settings
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from model_bakery import baker

from my_app.models import App, Comment


class SecurityTestCase(TestCase):
    """Base class for security tests."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.api_client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.token = Token.objects.create(user=self.user)


@pytest.mark.django_db
class TestAuthenticationSecurity(SecurityTestCase):
    """Test authentication security measures."""

    def test_password_requirements(self):
        """Test that password requirements are enforced."""
        weak_passwords = [
            '123',
            'password',
            '12345678',
            'qwerty',
            'abc123',
            'password123'
        ]
        
        for weak_password in weak_passwords:
            response = self.client.post('/api/auth/register/', {
                'username': f'user_{weak_password}',
                'email': f'{weak_password}@example.com',
                'password': weak_password,
                'password_confirm': weak_password
            })
            
            # Should reject weak passwords
            self.assertIn(response.status_code, [400, 422])

    def test_brute_force_protection(self):
        """Test protection against brute force attacks."""
        # Attempt multiple failed logins
        for i in range(10):
            response = self.client.post('/api/auth/login/', {
                'username': 'testuser',
                'password': 'wrongpassword'
            })
            self.assertEqual(response.status_code, 401)
        
        # After multiple failures, should be rate limited
        response = self.client.post('/api/auth/login/', {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        # Should be rate limited (429) or still unauthorized (401)
        self.assertIn(response.status_code, [401, 429])

    def test_session_security(self):
        """Test session security settings."""
        # Login to create session
        self.client.login(username='testuser', password='testpass123')
        
        # Check session cookie settings
        response = self.client.get('/')
        
        if 'sessionid' in response.cookies:
            session_cookie = response.cookies['sessionid']
            
            # Session cookie should be secure and httponly
            self.assertTrue(session_cookie.get('secure', False))
            self.assertTrue(session_cookie.get('httponly', False))
            
            # Should have SameSite protection
            samesite = session_cookie.get('samesite')
            self.assertIn(samesite, ['Strict', 'Lax'])

    def test_token_security(self):
        """Test API token security."""
        # Test token authentication
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        response = self.api_client.get('/api/apps/')
        self.assertEqual(response.status_code, 200)
        
        # Test invalid token
        self.api_client.credentials(HTTP_AUTHORIZATION='Token invalid-token')
        response = self.api_client.get('/api/apps/')
        self.assertEqual(response.status_code, 401)
        
        # Test missing token
        self.api_client.credentials()
        response = self.api_client.get('/api/apps/')
        self.assertEqual(response.status_code, 401)


@pytest.mark.django_db
class TestInputValidationSecurity(SecurityTestCase):
    """Test input validation and sanitization."""

    def test_xss_protection(self):
        """Test XSS protection in user inputs."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        xss_payloads = [
            '<script>alert("xss")</script>',
            '<img src="x" onerror="alert(\'xss\')">',
            'javascript:alert("xss")',
            '<svg onload="alert(\'xss\')">',
            '"><script>alert("xss")</script>'
        ]
        
        for payload in xss_payloads:
            # Test XSS in app creation
            response = self.api_client.post('/api/apps/', {
                'name': payload,
                'description': payload,
                'app_data': json.dumps({'components': []})
            })
            
            if response.status_code == 201:
                app_id = response.data['id']
                
                # Retrieve the app and check that XSS is escaped
                get_response = self.api_client.get(f'/api/apps/{app_id}/')
                self.assertEqual(get_response.status_code, 200)
                
                # XSS payload should be escaped or sanitized
                self.assertNotIn('<script>', get_response.data['name'])
                self.assertNotIn('javascript:', get_response.data['name'])
                self.assertNotIn('onerror=', get_response.data['description'])

    def test_sql_injection_protection(self):
        """Test SQL injection protection."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE apps; --",
            "' UNION SELECT * FROM auth_user --",
            "admin'--",
            "' OR 1=1#"
        ]
        
        for payload in sql_payloads:
            # Test SQL injection in search
            response = self.api_client.get('/api/apps/', {'search': payload})
            
            # Should not cause SQL errors
            self.assertNotEqual(response.status_code, 500)
            
            # Response should not contain SQL error messages
            if hasattr(response, 'content'):
                content = response.content.decode('utf-8').lower()
                sql_error_indicators = [
                    'sql syntax',
                    'mysql error',
                    'postgresql error',
                    'sqlite error',
                    'database error'
                ]
                
                for indicator in sql_error_indicators:
                    self.assertNotIn(indicator, content)

    def test_command_injection_protection(self):
        """Test command injection protection."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        command_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& whoami",
            "`id`",
            "$(whoami)"
        ]
        
        for payload in command_payloads:
            # Test in file upload or processing endpoints
            response = self.api_client.post('/api/apps/', {
                'name': f'test_{payload}',
                'app_data': json.dumps({'components': []})
            })
            
            # Should not execute commands or cause errors
            self.assertNotEqual(response.status_code, 500)

    def test_json_injection_protection(self):
        """Test JSON injection protection."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        # Test malformed JSON
        malformed_json_payloads = [
            '{"name": "test", "extra": }',
            '{"name": "test"} {"injection": "attempt"}',
            '{"name": "test", "components": [{"id": "1", "type": "button"}, ]}',
        ]
        
        for payload in malformed_json_payloads:
            response = self.api_client.post('/api/apps/', {
                'name': 'Test App',
                'app_data': payload
            }, format='json')
            
            # Should handle malformed JSON gracefully
            self.assertIn(response.status_code, [400, 422])

    def test_file_upload_security(self):
        """Test file upload security."""
        # Test malicious file types
        malicious_files = [
            ('test.php', b'<?php echo "malicious"; ?>'),
            ('test.jsp', b'<% out.println("malicious"); %>'),
            ('test.exe', b'MZ\x90\x00'),  # PE header
            ('test.sh', b'#!/bin/bash\necho "malicious"'),
        ]
        
        for filename, content in malicious_files:
            # If your app has file upload endpoints, test them here
            # This is a placeholder - adjust based on your actual endpoints
            pass


@pytest.mark.django_db
class TestAuthorizationSecurity(SecurityTestCase):
    """Test authorization and access control."""

    def test_user_isolation(self):
        """Test that users can only access their own data."""
        # Create another user and their app
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='otherpass123'
        )
        other_app = App.objects.create(
            name='Other User App',
            user=other_user,
            app_data='{}'
        )
        
        # Try to access other user's app
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        response = self.api_client.get(f'/api/apps/{other_app.id}/')
        
        # Should not be able to access other user's private app
        self.assertEqual(response.status_code, 404)

    def test_privilege_escalation_protection(self):
        """Test protection against privilege escalation."""
        # Create a regular user
        regular_user = User.objects.create_user(
            username='regular',
            email='<EMAIL>',
            password='regularpass123'
        )
        regular_token = Token.objects.create(user=regular_user)
        
        # Try to access admin endpoints
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {regular_token.key}')
        
        admin_endpoints = [
            '/admin/',
            '/api/admin/',
            '/api/users/',
        ]
        
        for endpoint in admin_endpoints:
            try:
                response = self.api_client.get(endpoint)
                # Should be forbidden or not found
                self.assertIn(response.status_code, [403, 404])
            except Exception:
                # Some endpoints might not exist, which is fine
                pass

    def test_object_level_permissions(self):
        """Test object-level permission enforcement."""
        # Create an app
        app = App.objects.create(
            name='Test App',
            user=self.user,
            app_data='{}'
        )
        
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='otherpass123'
        )
        other_token = Token.objects.create(user=other_user)
        
        # Other user should not be able to modify the app
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {other_token.key}')
        response = self.api_client.patch(f'/api/apps/{app.id}/', {
            'name': 'Hacked App Name'
        })
        
        self.assertIn(response.status_code, [403, 404])


@pytest.mark.django_db
class TestSecurityHeaders(SecurityTestCase):
    """Test security headers."""

    def test_security_headers_present(self):
        """Test that security headers are present."""
        response = self.client.get('/')
        
        # Check for security headers
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': ['strict-origin-when-cross-origin', 'same-origin'],
        }
        
        for header, expected_values in security_headers.items():
            if header in response:
                if isinstance(expected_values, list):
                    self.assertIn(response[header], expected_values)
                else:
                    self.assertEqual(response[header], expected_values)

    def test_csrf_protection(self):
        """Test CSRF protection."""
        # Test that CSRF protection is enabled
        self.assertTrue(getattr(settings, 'CSRF_COOKIE_SECURE', False))
        self.assertTrue(getattr(settings, 'CSRF_COOKIE_HTTPONLY', False))
        
        # Test CSRF token requirement
        response = self.client.post('/api/apps/', {
            'name': 'Test App',
            'app_data': '{}'
        })
        
        # Should require CSRF token
        self.assertEqual(response.status_code, 403)

    def test_cors_configuration(self):
        """Test CORS configuration."""
        # Test preflight request
        response = self.client.options('/api/apps/', 
            HTTP_ORIGIN='http://malicious-site.com',
            HTTP_ACCESS_CONTROL_REQUEST_METHOD='POST'
        )
        
        # Should not allow arbitrary origins
        cors_origin = response.get('Access-Control-Allow-Origin', '')
        self.assertNotEqual(cors_origin, '*')
        self.assertNotIn('malicious-site.com', cors_origin)


@pytest.mark.django_db
class TestDataProtection(SecurityTestCase):
    """Test data protection and privacy."""

    def test_password_storage(self):
        """Test that passwords are properly hashed."""
        user = User.objects.create_user(
            username='testpassword',
            email='<EMAIL>',
            password='testpassword123'
        )
        
        # Password should be hashed, not stored in plaintext
        self.assertNotEqual(user.password, 'testpassword123')
        self.assertTrue(user.password.startswith('pbkdf2_sha256$'))

    def test_sensitive_data_exposure(self):
        """Test that sensitive data is not exposed in API responses."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        # Get user data
        response = self.api_client.get('/api/user/')
        
        if response.status_code == 200:
            # Should not expose sensitive fields
            sensitive_fields = ['password', 'password_hash', 'secret_key']
            
            for field in sensitive_fields:
                self.assertNotIn(field, response.data)

    def test_error_message_information_disclosure(self):
        """Test that error messages don't disclose sensitive information."""
        # Test with invalid data to trigger errors
        response = self.api_client.post('/api/apps/', {
            'invalid_field': 'test'
        })
        
        if response.status_code >= 400:
            error_content = str(response.content)
            
            # Should not expose internal paths or system information
            sensitive_patterns = [
                '/home/',
                '/var/',
                'C:\\',
                'django.db',
                'traceback',
                'stack trace'
            ]
            
            for pattern in sensitive_patterns:
                self.assertNotIn(pattern, error_content)


@pytest.mark.django_db
class TestRateLimiting(SecurityTestCase):
    """Test rate limiting protection."""

    def test_api_rate_limiting(self):
        """Test API rate limiting."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        # Make many requests rapidly
        responses = []
        for i in range(100):
            response = self.api_client.get('/api/apps/')
            responses.append(response.status_code)
            
            # If rate limited, break
            if response.status_code == 429:
                break
        
        # Should eventually be rate limited
        rate_limited = any(status == 429 for status in responses)
        
        # Rate limiting might not be implemented yet, so just log the result
        if not rate_limited:
            print("Note: API rate limiting not detected")

    def test_login_rate_limiting(self):
        """Test login rate limiting."""
        # Attempt many failed logins
        responses = []
        for i in range(20):
            response = self.client.post('/api/auth/login/', {
                'username': 'testuser',
                'password': 'wrongpassword'
            })
            responses.append(response.status_code)
            
            # If rate limited, break
            if response.status_code == 429:
                break
        
        # Should eventually be rate limited
        rate_limited = any(status == 429 for status in responses)
        
        # Rate limiting might not be implemented yet, so just log the result
        if not rate_limited:
            print("Note: Login rate limiting not detected")
