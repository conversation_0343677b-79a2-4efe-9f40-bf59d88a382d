"""
Export Template Service for App Builder
Integrates with the hierarchical template system to provide enhanced export functionality
"""

import json
import zipfile
from io import BytesIO
from typing import Dict, List, Any, Optional, Union
from django.core.exceptions import ValidationError
from django.utils import timezone
from ..models import App, LayoutTemplate, AppTemplate
from core.enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat, StyleFramework


class ExportTemplateService:
    """
    Service for handling export operations with template integration
    """
    
    def __init__(self):
        self.code_generator = EnhancedCodeGenerator()
    
    def export_app_with_templates(self, app_id: int, export_format: str, options: Dict[str, Any], user=None) -> Dict[str, Any]:
        """
        Export an app with its associated templates
        
        Args:
            app_id: ID of the app to export
            export_format: Target export format
            options: Export configuration options
            user: User performing the export
            
        Returns:
            Export result with generated code and metadata
        """
        try:
            # Get the app
            app = App.objects.get(id=app_id)
            if user and app.user != user:
                raise ValidationError("Access denied")
            
            # Parse app data
            app_data = json.loads(app.app_data)
            
            # Enhance app data with template information
            enhanced_app_data = self._enhance_app_data_with_templates(app_data, user)
            
            # Create export options
            export_options = self._create_export_options(export_format, options)
            
            # Generate code
            generated_code = self.code_generator.generate_code(enhanced_app_data, export_options)
            
            # Create export metadata
            export_metadata = {
                'app_id': app.id,
                'app_name': app.name,
                'format': export_format,
                'options': options,
                'templates_used': self._get_templates_used(app_data),
                'generated_at': timezone.now().isoformat(),
                'user': user.username if user else None,
                'version': '2.0'
            }
            
            # Package the result
            if isinstance(generated_code, str):
                return {
                    'type': 'single-file',
                    'code': generated_code,
                    'metadata': export_metadata
                }
            elif isinstance(generated_code, dict):
                return {
                    'type': 'multi-file',
                    'files': generated_code,
                    'metadata': export_metadata
                }
            else:
                return {
                    'type': 'zip',
                    'zip_data': generated_code,
                    'metadata': export_metadata
                }
                
        except Exception as e:
            raise Exception(f"Export failed: {str(e)}")
    
    def export_template_as_project(self, template_id: int, template_type: str, export_format: str, options: Dict[str, Any], user=None) -> Dict[str, Any]:
        """
        Export a template as a complete project
        
        Args:
            template_id: ID of the template
            template_type: Type of template ('layout' or 'app')
            export_format: Target export format
            options: Export configuration options
            user: User performing the export
            
        Returns:
            Export result with generated project
        """
        try:
            # Get the template
            if template_type == 'layout':
                template = LayoutTemplate.objects.get(id=template_id)
                if user and template.user != user and not template.is_public:
                    raise ValidationError("Access denied")
                
                # Convert layout template to app data
                app_data = self._layout_template_to_app_data(template)
                
            elif template_type == 'app':
                template = AppTemplate.objects.get(id=template_id)
                if user and template.user != user and not template.is_public:
                    raise ValidationError("Access denied")
                
                # Convert app template to app data
                app_data = self._app_template_to_app_data(template)
                
            else:
                raise ValidationError("Invalid template type")
            
            # Force full project structure for template exports
            options['project_structure'] = 'full-project'
            
            # Create export options
            export_options = self._create_export_options(export_format, options)
            
            # Generate code
            generated_code = self.code_generator.generate_code(app_data, export_options)
            
            # Create export metadata
            export_metadata = {
                'template_id': template.id,
                'template_name': template.name,
                'template_type': template_type,
                'format': export_format,
                'options': options,
                'generated_at': timezone.now().isoformat(),
                'user': user.username if user else None,
                'version': '2.0'
            }
            
            return {
                'type': 'project',
                'files': generated_code,
                'metadata': export_metadata
            }
            
        except Exception as e:
            raise Exception(f"Template export failed: {str(e)}")
    
    def batch_export_apps(self, app_ids: List[int], export_format: str, options: Dict[str, Any], user=None) -> bytes:
        """
        Export multiple apps as a single zip file
        
        Args:
            app_ids: List of app IDs to export
            export_format: Target export format
            options: Export configuration options
            user: User performing the export
            
        Returns:
            Zip file bytes containing all exported apps
        """
        try:
            zip_buffer = BytesIO()
            
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for app_id in app_ids:
                    try:
                        # Export individual app
                        result = self.export_app_with_templates(app_id, export_format, options, user)
                        
                        # Add to zip
                        app_name = result['metadata']['app_name']
                        safe_app_name = self._sanitize_filename(app_name)
                        
                        if result['type'] == 'single-file':
                            filename = f"{safe_app_name}/App.{self._get_file_extension(export_format)}"
                            zip_file.writestr(filename, result['code'])
                        elif result['type'] == 'multi-file':
                            for file_path, content in result['files'].items():
                                filename = f"{safe_app_name}/{file_path}"
                                zip_file.writestr(filename, content)
                        
                        # Add metadata
                        metadata_filename = f"{safe_app_name}/export-metadata.json"
                        zip_file.writestr(metadata_filename, json.dumps(result['metadata'], indent=2))
                        
                    except Exception as e:
                        # Add error file for failed exports
                        error_filename = f"failed-exports/app-{app_id}-error.txt"
                        zip_file.writestr(error_filename, f"Export failed: {str(e)}")
            
            zip_buffer.seek(0)
            return zip_buffer.getvalue()
            
        except Exception as e:
            raise Exception(f"Batch export failed: {str(e)}")
    
    def _enhance_app_data_with_templates(self, app_data: Dict[str, Any], user=None) -> Dict[str, Any]:
        """
        Enhance app data with template information and resolve template references
        """
        enhanced_data = app_data.copy()
        
        # Resolve layout template references
        if 'layout_templates' in app_data:
            resolved_layouts = []
            for template_ref in app_data['layout_templates']:
                try:
                    template = LayoutTemplate.objects.get(id=template_ref['id'])
                    if user and template.user != user and not template.is_public:
                        continue
                    
                    layout_data = {
                        'id': f"template-{template.id}",
                        'name': template.name,
                        'type': template.layout_type,
                        'components': template.get_components_json(),
                        'styles': template.get_default_props_json(),
                        'template_source': {
                            'id': template.id,
                            'name': template.name,
                            'type': 'layout'
                        }
                    }
                    resolved_layouts.append(layout_data)
                except LayoutTemplate.DoesNotExist:
                    continue
            
            enhanced_data['layouts'].extend(resolved_layouts)
        
        # Resolve app template references
        if 'app_templates' in app_data:
            for template_ref in app_data['app_templates']:
                try:
                    template = AppTemplate.objects.get(id=template_ref['id'])
                    if user and template.user != user and not template.is_public:
                        continue
                    
                    template_data = template.get_components_json()
                    if template_data:
                        # Merge template components and layouts
                        enhanced_data['components'].extend(template_data.get('components', []))
                        enhanced_data['layouts'].extend(template_data.get('layouts', []))
                        
                        # Add template metadata
                        enhanced_data.setdefault('template_sources', []).append({
                            'id': template.id,
                            'name': template.name,
                            'type': 'app',
                            'category': template.app_category
                        })
                        
                except AppTemplate.DoesNotExist:
                    continue
        
        return enhanced_data
    
    def _layout_template_to_app_data(self, template: LayoutTemplate) -> Dict[str, Any]:
        """
        Convert a layout template to app data format
        """
        components_data = template.get_components_json()
        default_props = template.get_default_props_json()
        
        return {
            'components': components_data.get('components', []),
            'layouts': [{
                'id': f"layout-{template.id}",
                'name': template.name,
                'type': template.layout_type,
                'components': components_data.get('component_refs', []),
                'styles': default_props
            }],
            'styles': default_props.get('styles', {}),
            'data': default_props.get('data', {}),
            'template_source': {
                'id': template.id,
                'name': template.name,
                'type': 'layout'
            }
        }
    
    def _app_template_to_app_data(self, template: AppTemplate) -> Dict[str, Any]:
        """
        Convert an app template to app data format
        """
        components_data = template.get_components_json()
        default_props = template.get_default_props_json()
        
        return {
            'components': components_data.get('components', []),
            'layouts': components_data.get('layouts', []),
            'styles': default_props.get('styles', {}),
            'data': default_props.get('data', {}),
            'template_source': {
                'id': template.id,
                'name': template.name,
                'type': 'app',
                'category': template.app_category
            }
        }
    
    def _create_export_options(self, export_format: str, options: Dict[str, Any]) -> ExportOptions:
        """
        Create ExportOptions object from format and options dict
        """
        # Map format string to enum
        format_mapping = {
            'react': ExportFormat.REACT,
            'react-ts': ExportFormat.REACT_TS,
            'vue': ExportFormat.VUE,
            'vue-ts': ExportFormat.VUE_TS,
            'angular': ExportFormat.ANGULAR,
            'svelte': ExportFormat.SVELTE,
            'next': ExportFormat.NEXT_JS,
            'nuxt': ExportFormat.NUXT,
            'html': ExportFormat.HTML,
            'react-native': ExportFormat.REACT_NATIVE,
            'flutter': ExportFormat.FLUTTER,
            'ionic': ExportFormat.IONIC,
            'express-api': ExportFormat.EXPRESS_API,
            'django-api': ExportFormat.DJANGO_API,
            'fastapi': ExportFormat.FASTAPI
        }
        
        # Map style framework string to enum
        style_mapping = {
            'styled-components': StyleFramework.STYLED_COMPONENTS,
            'emotion': StyleFramework.EMOTION,
            'tailwind': StyleFramework.TAILWIND,
            'css-modules': StyleFramework.CSS_MODULES,
            'material-ui': StyleFramework.MATERIAL_UI,
            'chakra-ui': StyleFramework.CHAKRA_UI,
            'bootstrap': StyleFramework.BOOTSTRAP
        }
        
        return ExportOptions(
            format=format_mapping.get(export_format, ExportFormat.REACT),
            typescript=options.get('typescript', False),
            include_accessibility=options.get('include_accessibility', True),
            include_tests=options.get('include_tests', False),
            include_storybook=options.get('include_storybook', False),
            style_framework=style_mapping.get(
                options.get('style_framework', 'styled-components'),
                StyleFramework.STYLED_COMPONENTS
            ),
            state_management=options.get('state_management', 'useState'),
            project_structure=options.get('project_structure', 'single-file'),
            bundler=options.get('bundler', 'vite'),
            package_manager=options.get('package_manager', 'npm'),
            include_docker=options.get('include_docker', False),
            include_ci_cd=options.get('include_ci_cd', False)
        )
    
    def _get_templates_used(self, app_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract information about templates used in the app
        """
        templates_used = []
        
        # Check for layout template references
        if 'layout_templates' in app_data:
            for template_ref in app_data['layout_templates']:
                templates_used.append({
                    'type': 'layout',
                    'id': template_ref.get('id'),
                    'name': template_ref.get('name')
                })
        
        # Check for app template references
        if 'app_templates' in app_data:
            for template_ref in app_data['app_templates']:
                templates_used.append({
                    'type': 'app',
                    'id': template_ref.get('id'),
                    'name': template_ref.get('name')
                })
        
        return templates_used
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename for safe use in zip files
        """
        import re
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '-', filename)
        # Remove leading/trailing spaces and dots
        sanitized = sanitized.strip(' .')
        # Limit length
        return sanitized[:50] if len(sanitized) > 50 else sanitized
    
    def _get_file_extension(self, export_format: str) -> str:
        """
        Get appropriate file extension for export format
        """
        extensions = {
            'react': 'jsx',
            'react-ts': 'tsx',
            'vue': 'vue',
            'vue-ts': 'vue',
            'angular': 'ts',
            'svelte': 'svelte',
            'next': 'jsx',
            'nuxt': 'vue',
            'html': 'html',
            'react-native': 'jsx',
            'flutter': 'dart',
            'ionic': 'ts'
        }
        return extensions.get(export_format, 'js')
