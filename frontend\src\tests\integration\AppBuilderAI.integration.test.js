import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import main App Builder components
import AppBuilderEnhanced from '../../pages/AppBuilderEnhanced';

// Mock external services
jest.mock('../../services/aiDesignService');
jest.mock('../../services/aiWebSocketService');

// Mock API calls
global.fetch = jest.fn();

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: (state = {
        components: [],
        layouts: [],
        styles: {},
        currentApp: null
      }, action) => {
        switch (action.type) {
          case 'ADD_COMPONENT':
            return {
              ...state,
              components: [...state.components, action.payload]
            };
          case 'UPDATE_COMPONENT':
            return {
              ...state,
              components: state.components.map(comp =>
                comp.id === action.payload.id ? { ...comp, ...action.payload } : comp
              )
            };
          default:
            return state;
        }
      },
      ui: (state = {
        selectedComponent: null,
        aiSuggestionsVisible: false
      }, action) => {
        switch (action.type) {
          case 'SELECT_COMPONENT':
            return { ...state, selectedComponent: action.payload };
          case 'TOGGLE_AI_SUGGESTIONS':
            return { ...state, aiSuggestionsVisible: !state.aiSuggestionsVisible };
          default:
            return state;
        }
      }
    },
    preloadedState: initialState
  });
};

describe('App Builder AI Integration Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
    
    // Mock successful API responses
    fetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        suggestions: [],
        status: 'success'
      })
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Full App Builder with AI Features', () => {
    test('renders App Builder with AI integration', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Check that main App Builder components are present
      await waitFor(() => {
        expect(screen.getByText(/app builder/i)).toBeInTheDocument();
      });
    });

    test('AI suggestions appear when components are added', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Add a component to trigger AI suggestions
      const addComponentButton = screen.getByText('Button');
      if (addComponentButton) {
        fireEvent.click(addComponentButton);
      }

      // Wait for AI suggestions to potentially appear
      await waitFor(() => {
        // AI suggestions should be available after adding components
        expect(store.getState().app.components.length).toBeGreaterThanOrEqual(0);
      });
    });

    test('AI assistant button appears and functions', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Look for AI assistant button (it might be floating)
      await waitFor(() => {
        const aiButtons = screen.queryAllByRole('button');
        expect(aiButtons.length).toBeGreaterThan(0);
      });
    });
  });

  describe('AI Workflow Integration', () => {
    test('complete AI suggestion workflow', async () => {
      const storeWithComponents = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: {} },
            { id: '2', type: 'text', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // The app should render with existing components
      await waitFor(() => {
        expect(storeWithComponents.getState().app.components).toHaveLength(2);
      });
    });

    test('AI suggestions update when components change', async () => {
      const storeWithComponents = createMockStore({
        app: {
          components: [
            { id: '1', type: 'form', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // AI should suggest complementary components for forms
      await waitFor(() => {
        expect(storeWithComponents.getState().app.components).toHaveLength(1);
      });
    });
  });

  describe('Performance and Error Handling', () => {
    test('handles AI service errors gracefully', async () => {
      // Mock API error
      fetch.mockRejectedValue(new Error('AI Service Unavailable'));

      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // App should still render even if AI services fail
      await waitFor(() => {
        expect(screen.getByText(/app builder/i)).toBeInTheDocument();
      });
    });

    test('AI features do not block main functionality', async () => {
      // Mock slow AI response
      fetch.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: async () => ({ suggestions: [], status: 'success' })
          }), 5000)
        )
      );

      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Main app functionality should work immediately
      await waitFor(() => {
        expect(screen.getByText(/app builder/i)).toBeInTheDocument();
      });
    });

    test('caching prevents excessive API calls', async () => {
      const storeWithComponents = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Re-render with same components
      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Should not make duplicate API calls due to caching
      await waitFor(() => {
        expect(fetch).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Accessibility and UX', () => {
    test('AI features are accessible via keyboard', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Test keyboard navigation
      const focusableElements = screen.getAllByRole('button');
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
        expect(document.activeElement).toBe(focusableElements[0]);
      }
    });

    test('AI suggestions have proper ARIA labels', async () => {
      const storeWithComponents = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Check for accessibility attributes
      await waitFor(() => {
        const buttons = screen.getAllByRole('button');
        buttons.forEach(button => {
          expect(button).toBeInTheDocument();
        });
      });
    });

    test('loading states are properly indicated', async () => {
      // Mock slow response
      fetch.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: async () => ({ suggestions: [], status: 'success' })
          }), 1000)
        )
      );

      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Should show loading indicators
      await waitFor(() => {
        expect(screen.getByText(/app builder/i)).toBeInTheDocument();
      });
    });
  });

  describe('WebSocket Integration', () => {
    test('WebSocket connection is established', async () => {
      const aiWebSocketService = require('../../services/aiWebSocketService').default;
      
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // WebSocket should attempt to connect
      await waitFor(() => {
        expect(aiWebSocketService.connect).toBeDefined();
      });
    });

    test('real-time suggestions work via WebSocket', async () => {
      const aiWebSocketService = require('../../services/aiWebSocketService').default;
      
      // Mock WebSocket connection
      aiWebSocketService.getStatus.mockReturnValue({ connected: true });

      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      await waitFor(() => {
        expect(screen.getByText(/app builder/i)).toBeInTheDocument();
      });
    });
  });

  describe('Undo/Redo Integration', () => {
    test('AI actions can be undone', async () => {
      const storeWithHistory = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithHistory}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Test undo functionality
      await waitFor(() => {
        expect(storeWithHistory.getState().app.components).toHaveLength(1);
      });
    });
  });
});

describe('Cross-browser Compatibility', () => {
  test('AI features work in different browsers', async () => {
    // Mock different browser environments
    const originalUserAgent = navigator.userAgent;
    
    // Test Chrome
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      configurable: true
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <AppBuilderEnhanced />
        </BrowserRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByText(/app builder/i)).toBeInTheDocument();
    });

    // Restore original user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: originalUserAgent,
      configurable: true
    });
  });
});
