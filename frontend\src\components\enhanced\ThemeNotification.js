import React, { useEffect, useState } from 'react';
import { notification, Button } from 'antd';
import { BgColorsOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { styled } from '../../design-system';

const ThemePreview = styled.div`
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: ${props => props.backgroundColor || 'white'};
  color: ${props => props.textColor || 'black'};
  font-family: ${props => props.fontFamily || 'inherit'};
  border: 1px solid rgba(0, 0, 0, 0.1);
`;

const ColorSwatch = styled.div`
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
`;

const SwatchContainer = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 8px;
`;

/**
 * ThemeNotification Component
 *
 * Shows notifications when themes are updated, added, or activated.
 * Listens for theme changes in the Redux store.
 */
const ThemeNotification = () => {
  const [api, contextHolder] = notification.useNotification();
  const [lastNotifiedTheme, setLastNotifiedTheme] = useState(null);

  // Get themes from Redux store with error handling
  let themes = [];
  let activeThemeId = null;

  try {
    themes = useSelector(state => {
      if (!state || !state.themes) {
        console.warn('Redux state or themes slice not found in ThemeNotification, using fallback values');
        return [];
      }
      return state.themes.themes || [];
    });

    activeThemeId = useSelector(state => {
      if (!state || !state.themes) {
        return null;
      }
      return state.themes.activeTheme;
    });
  } catch (error) {
    console.error('Error accessing Redux context in ThemeNotification:', error);
    // Continue with default values
  }

  // Get the active theme object
  const activeTheme = themes.find(theme => theme.id === activeThemeId) || {
    id: 'default',
    name: 'Default Theme',
    primaryColor: '#2563EB',
    secondaryColor: '#10B981',
    backgroundColor: '#FFFFFF',
    textColor: '#111827',
    fontFamily: 'Inter, sans-serif'
  };

  // Show notification when active theme changes
  useEffect(() => {
    // Skip initial notification
    if (!lastNotifiedTheme) {
      setLastNotifiedTheme(activeThemeId);
      return;
    }

    // Skip if the theme hasn't changed
    if (lastNotifiedTheme === activeThemeId) {
      return;
    }

    // Show notification
    showThemeActivatedNotification(activeTheme);

    // Update last notified theme
    setLastNotifiedTheme(activeThemeId);
    // We need to include activeThemeId in the dependency array, but we're handling
    // the potential infinite loop by checking if the theme has changed
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeThemeId]);

  // Listen for service worker messages
  useEffect(() => {
    const handleServiceWorkerMessage = (event) => {
      if (event.data && event.data.type === 'THEME_CACHE_UPDATED') {
        // Show notification that theme cache was updated
        api.info({
          message: 'Theme Cache Updated',
          description: 'Theme assets have been updated in the cache.',
          icon: <BgColorsOutlined style={{ color: '#1890ff' }} />,
          placement: 'bottomRight',
          duration: 3
        });
      }
    };

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
    }

    return () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
  }, []);

  // Show notification when a theme is activated
  const showThemeActivatedNotification = (theme) => {
    api.open({
      message: `Theme Applied: ${theme.name}`,
      description: (
        <div>
          <p>The theme has been applied to the application.</p>
          <ThemePreview
            backgroundColor={theme.backgroundColor}
            textColor={theme.textColor}
            fontFamily={theme.fontFamily}
          >
            <div style={{ fontWeight: 'bold' }}>Theme Preview</div>
            <p style={{ margin: '4px 0' }}>This is how your theme looks.</p>
            <div style={{
              display: 'flex',
              gap: '8px',
              marginTop: '8px'
            }}>
              <button style={{
                backgroundColor: theme.primaryColor,
                color: 'white',
                border: 'none',
                padding: '4px 8px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}>
                Primary
              </button>
              <button style={{
                backgroundColor: theme.secondaryColor,
                color: 'white',
                border: 'none',
                padding: '4px 8px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}>
                Secondary
              </button>
            </div>
          </ThemePreview>
          <SwatchContainer>
            <ColorSwatch style={{ backgroundColor: theme.primaryColor }} />
            <ColorSwatch style={{ backgroundColor: theme.secondaryColor }} />
            <ColorSwatch style={{ backgroundColor: theme.backgroundColor }} />
            <ColorSwatch style={{ backgroundColor: theme.textColor }} />
          </SwatchContainer>
        </div>
      ),
      icon: <BgColorsOutlined style={{ color: theme.primaryColor }} />,
      placement: 'bottomRight',
      duration: 5,
      btn: (
        <Button type="primary" size="small" icon={<CheckOutlined />}>
          OK
        </Button>
      )
    });
  };

  // Show notification when a theme is added
  const showThemeAddedNotification = (theme) => {
    api.success({
      message: `Theme Created: ${theme.name}`,
      description: 'The theme has been created successfully.',
      icon: <BgColorsOutlined style={{ color: theme.primaryColor }} />,
      placement: 'bottomRight',
      duration: 4,
      btn: (
        <Button type="primary" size="small" icon={<CheckOutlined />}>
          OK
        </Button>
      )
    });
  };

  // Show notification when a theme is updated
  const showThemeUpdatedNotification = (theme) => {
    api.success({
      message: `Theme Updated: ${theme.name}`,
      description: 'The theme has been updated successfully.',
      icon: <BgColorsOutlined style={{ color: theme.primaryColor }} />,
      placement: 'bottomRight',
      duration: 4,
      btn: (
        <Button type="primary" size="small" icon={<CheckOutlined />}>
          OK
        </Button>
      )
    });
  };

  return contextHolder;
};

export default ThemeNotification;
