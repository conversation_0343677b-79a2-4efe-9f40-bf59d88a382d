# Production Environment Variables for Django Backend
# Generated on 2025-06-17 13:31:52

# Django Core Settings
DEBUG=False
SECRET_KEY=bKmXZrzxgZimpBXqIeG1ecsnysYFuGT1oNQpI2AWqkE_phVpgzRqvyOs6JWmMUJOlqw
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,localhost

# Database Configuration (PostgreSQL)
DB_ENGINE=django.db.backends.postgresql
DB_NAME=app_builder_201_prod
DB_USER=app_builder_user
DB_PASSWORD=your_secure_database_password_here
DB_HOST=db
DB_PORT=5432

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://redis:6379/0
CACHE_URL=redis://redis:6379/1

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password_here

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# CORS Settings
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
CORS_ALLOW_CREDENTIALS=True

# Static Files (AWS S3 or similar)
USE_S3=True
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=your-app-static-files
AWS_S3_REGION_NAME=us-east-1

# Monitoring and Logging
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=INFO

# Application Settings
ENVIRONMENT=production
APP_VERSION=1.0.0
