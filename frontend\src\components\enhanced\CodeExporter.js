import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Card,
  Typography,
  Button,
  Tabs,
  Select,
  Space,
  Divider,
  Checkbox,
  Radio,
  message,
  Tooltip,
  Alert
} from 'antd';
import {
  CodeOutlined,
  DownloadOutlined,
  CopyOutlined,
  ExportOutlined,
  FileZipOutlined,
  GithubOutlined,
  CheckOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { styled } from '../../design-system';
import theme from '../../design-system/theme';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const ExporterContainer = styled.div`
  padding: ${theme.spacing[3]};
`;

const CodePreview = styled.pre`
  background-color: ${theme.colors.neutral[100]};
  border: 1px solid ${theme.colors.neutral[300]};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing[3]};
  overflow: auto;
  max-height: 500px;
  font-family: ${theme.typography.fontFamily.code};
  font-size: ${theme.typography.fontSize.sm};
  line-height: 1.5;
`;

const OptionsContainer = styled.div`
  margin-bottom: ${theme.spacing[4]};
`;

const OptionGroup = styled.div`
  margin-bottom: ${theme.spacing[3]};
`;

/**
 * CodeExporter component
 * Exports components and layouts as code in various formats
 */
const CodeExporter = () => {
  const components = useSelector(state => state.app?.components || []);
  const layouts = useSelector(state => state.app?.layouts || []);
  const activeTheme = useSelector(state => state.themes?.activeTheme || 'default');
  const themes = useSelector(state => state.themes?.themes || []);
  
  const [exportFormat, setExportFormat] = useState('react');
  const [includeStyles, setIncludeStyles] = useState(true);
  const [includeTheme, setIncludeTheme] = useState(true);
  const [bundleType, setBundleType] = useState('components');
  const [selectedComponents, setSelectedComponents] = useState([]);
  const [selectedLayouts, setSelectedLayouts] = useState([]);
  const [codePreview, setCodePreview] = useState('');
  const [copied, setCopied] = useState(false);
  
  // Update selected components and layouts when they change
  useEffect(() => {
    if (components.length > 0 && selectedComponents.length === 0) {
      setSelectedComponents(components.map(c => c.id));
    }
    
    if (layouts.length > 0 && selectedLayouts.length === 0) {
      setSelectedLayouts(layouts.map(l => l.id));
    }
  }, [components, layouts, selectedComponents.length, selectedLayouts.length]);
  
  // Generate code preview when options change
  useEffect(() => {
    generateCodePreview();
  }, [exportFormat, includeStyles, includeTheme, bundleType, selectedComponents, selectedLayouts]);
  
  // Generate code preview based on selected options
  const generateCodePreview = () => {
    // Get selected components and layouts
    const filteredComponents = components.filter(c => selectedComponents.includes(c.id));
    const filteredLayouts = layouts.filter(l => selectedLayouts.includes(l.id));
    
    // Get active theme
    const theme = themes.find(t => t.id === activeTheme) || themes[0];
    
    let code = '';
    
    switch (exportFormat) {
      case 'react':
        code = generateReactCode(filteredComponents, filteredLayouts, theme);
        break;
      case 'vue':
        code = generateVueCode(filteredComponents, filteredLayouts, theme);
        break;
      case 'angular':
        code = generateAngularCode(filteredComponents, filteredLayouts, theme);
        break;
      case 'html':
        code = generateHtmlCode(filteredComponents, filteredLayouts, theme);
        break;
      default:
        code = generateReactCode(filteredComponents, filteredLayouts, theme);
    }
    
    setCodePreview(code);
  };
  
  // Generate React code
  const generateReactCode = (components, layouts, theme) => {
    let code = '';
    
    // Import statements
    code += "import React from 'react';\n";
    
    if (includeStyles) {
      code += "import styled from 'styled-components';\n";
    }
    
    code += "\n";
    
    // Theme definition
    if (includeTheme && theme) {
      code += "// Theme definition\n";
      code += `const theme = ${JSON.stringify(theme, null, 2)};\n\n`;
    }
    
    // Component definitions
    if (bundleType === 'components' || bundleType === 'all') {
      components.forEach(component => {
        code += `// ${component.name} component\n`;
        
        if (includeStyles && component.props.customStyles) {
          code += `const Styled${component.name.replace(/\s/g, '')} = styled.div\`\n`;
          code += `  ${component.props.customStyles}\n`;
          code += `\`;\n\n`;
        }
        
        code += `const ${component.name.replace(/\s/g, '')} = (props) => {\n`;
        code += `  return (\n`;
        
        if (includeStyles && component.props.customStyles) {
          code += `    <Styled${component.name.replace(/\s/g, '')}>\n`;
          code += `      <div>${component.name}</div>\n`;
          code += `    </Styled${component.name.replace(/\s/g, '')}>\n`;
        } else {
          code += `    <div className="${component.name.toLowerCase().replace(/\s/g, '-')}">\n`;
          code += `      <div>${component.name}</div>\n`;
          code += `    </div>\n`;
        }
        
        code += `  );\n`;
        code += `};\n\n`;
      });
    }
    
    // Layout definitions
    if (bundleType === 'layouts' || bundleType === 'all') {
      layouts.forEach(layout => {
        code += `// ${layout.name || 'Layout'} layout\n`;
        
        if (includeStyles) {
          code += `const ${layout.name?.replace(/\s/g, '') || 'Layout'}Container = styled.div\`\n`;
          code += `  display: grid;\n`;
          code += `  grid-template-columns: repeat(12, 1fr);\n`;
          code += `  gap: 16px;\n`;
          code += `\`;\n\n`;
        }
        
        code += `const ${layout.name?.replace(/\s/g, '') || 'Layout'} = () => {\n`;
        code += `  return (\n`;
        
        if (includeStyles) {
          code += `    <${layout.name?.replace(/\s/g, '') || 'Layout'}Container>\n`;
          
          // Add components to layout
          if (layout.components && layout.components.length > 0) {
            layout.components.forEach(componentId => {
              const component = components.find(c => c.id === componentId);
              if (component) {
                code += `      <${component.name.replace(/\s/g, '')} />\n`;
              }
            });
          } else {
            code += `      {/* Add components here */}\n`;
          }
          
          code += `    </${layout.name?.replace(/\s/g, '') || 'Layout'}Container>\n`;
        } else {
          code += `    <div className="${(layout.name || 'layout').toLowerCase().replace(/\s/g, '-')}-container">\n`;
          
          // Add components to layout
          if (layout.components && layout.components.length > 0) {
            layout.components.forEach(componentId => {
              const component = components.find(c => c.id === componentId);
              if (component) {
                code += `      <${component.name.replace(/\s/g, '')} />\n`;
              }
            });
          } else {
            code += `      {/* Add components here */}\n`;
          }
          
          code += `    </div>\n`;
        }
        
        code += `  );\n`;
        code += `};\n\n`;
      });
    }
    
    // Export statements
    code += "// Exports\n";
    
    if (bundleType === 'components' || bundleType === 'all') {
      components.forEach(component => {
        code += `export { ${component.name.replace(/\s/g, '')} };\n`;
      });
    }
    
    if (bundleType === 'layouts' || bundleType === 'all') {
      layouts.forEach(layout => {
        code += `export { ${layout.name?.replace(/\s/g, '') || 'Layout'} };\n`;
      });
    }
    
    return code;
  };
  
  // Generate Vue code (simplified for demo)
  const generateVueCode = (components, layouts, theme) => {
    return `// Vue.js code export is coming soon
// Selected ${components.length} components and ${layouts.length} layouts
// Export format: Vue.js
// Include styles: ${includeStyles ? 'Yes' : 'No'}
// Include theme: ${includeTheme ? 'Yes' : 'No'}
// Bundle type: ${bundleType}`;
  };
  
  // Generate Angular code (simplified for demo)
  const generateAngularCode = (components, layouts, theme) => {
    return `// Angular code export is coming soon
// Selected ${components.length} components and ${layouts.length} layouts
// Export format: Angular
// Include styles: ${includeStyles ? 'Yes' : 'No'}
// Include theme: ${includeTheme ? 'Yes' : 'No'}
// Bundle type: ${bundleType}`;
  };
  
  // Generate HTML code (simplified for demo)
  const generateHtmlCode = (components, layouts, theme) => {
    return `<!-- HTML code export is coming soon -->
<!-- Selected ${components.length} components and ${layouts.length} layouts -->
<!-- Export format: HTML -->
<!-- Include styles: ${includeStyles ? 'Yes' : 'No'} -->
<!-- Include theme: ${includeTheme ? 'Yes' : 'No'} -->
<!-- Bundle type: ${bundleType} -->`;
  };
  
  // Copy code to clipboard
  const handleCopyCode = () => {
    navigator.clipboard.writeText(codePreview)
      .then(() => {
        setCopied(true);
        message.success('Code copied to clipboard');
        
        // Reset copied state after 2 seconds
        setTimeout(() => {
          setCopied(false);
        }, 2000);
      })
      .catch(error => {
        console.error('Failed to copy code:', error);
        message.error('Failed to copy code');
      });
  };
  
  // Download code as file
  const handleDownloadCode = () => {
    const fileExtension = exportFormat === 'html' ? 'html' : 'js';
    const fileName = `app-builder-export.${fileExtension}`;
    
    const blob = new Blob([codePreview], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success(`Code downloaded as ${fileName}`);
  };
  
  return (
    <ExporterContainer>
      <Title level={4}>Export Code</Title>
      <Paragraph>
        Export your components and layouts as code in various formats.
      </Paragraph>
      
      <Divider />
      
      <OptionsContainer>
        <OptionGroup>
          <Text strong>Export Format</Text>
          <Select
            value={exportFormat}
            onChange={setExportFormat}
            style={{ width: '100%', marginTop: theme.spacing[1] }}
          >
            <Option value="react">React</Option>
            <Option value="vue">Vue.js</Option>
            <Option value="angular">Angular</Option>
            <Option value="html">HTML</Option>
          </Select>
        </OptionGroup>
        
        <OptionGroup>
          <Text strong>Bundle Type</Text>
          <Radio.Group
            value={bundleType}
            onChange={e => setBundleType(e.target.value)}
            style={{ display: 'block', marginTop: theme.spacing[1] }}
          >
            <Radio value="components">Components Only</Radio>
            <Radio value="layouts">Layouts Only</Radio>
            <Radio value="all">Components & Layouts</Radio>
          </Radio.Group>
        </OptionGroup>
        
        <OptionGroup>
          <Space direction="vertical">
            <Checkbox
              checked={includeStyles}
              onChange={e => setIncludeStyles(e.target.checked)}
            >
              Include Styles
            </Checkbox>
            
            <Checkbox
              checked={includeTheme}
              onChange={e => setIncludeTheme(e.target.checked)}
            >
              Include Theme
            </Checkbox>
          </Space>
        </OptionGroup>
      </OptionsContainer>
      
      <Alert
        message="Export Preview"
        description="This is a preview of the exported code. You can copy it to clipboard or download it as a file."
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: theme.spacing[3] }}
      />
      
      <CodePreview>
        {codePreview}
      </CodePreview>
      
      <Divider />
      
      <Space>
        <Button
          type="primary"
          icon={<CopyOutlined />}
          onClick={handleCopyCode}
        >
          {copied ? 'Copied!' : 'Copy Code'}
        </Button>
        
        <Button
          icon={<DownloadOutlined />}
          onClick={handleDownloadCode}
        >
          Download
        </Button>
      </Space>
    </ExporterContainer>
  );
};

export default CodeExporter;
