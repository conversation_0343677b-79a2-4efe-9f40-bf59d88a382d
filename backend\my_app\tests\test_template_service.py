"""
Tests for template service functionality.
"""
from django.test import TestCase
from django.contrib.auth.models import User
from my_app.models import ComponentTemplate, LayoutTemplate, AppTemplate
from my_app.services.template_service import TemplateService


class TemplateServiceTest(TestCase):
    """Test cases for TemplateService"""
    
    def setUp(self):
        """Set up test data"""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test templates
        self.component_template = ComponentTemplate.objects.create(
            name='Test Button',
            component_type='button',
            default_props='{"type": "primary"}',
            user=self.user1,
            is_public=True
        )
        
        self.layout_template = LayoutTemplate.objects.create(
            name='Grid Layout',
            layout_type='grid',
            components={'columns': 12},
            user=self.user1,
            is_public=True
        )
        
        self.app_template = AppTemplate.objects.create(
            name='Business App',
            app_category='business',
            components={'pages': ['home', 'about']},
            user=self.user1,
            is_public=True
        )
    
    def test_get_component_categories(self):
        """Test getting component template categories"""
        # Create additional component templates
        ComponentTemplate.objects.create(
            name='Test Input',
            component_type='input',
            user=self.user1
        )
        ComponentTemplate.objects.create(
            name='Another Button',
            component_type='button',
            user=self.user2
        )
        
        categories = TemplateService.get_component_categories()
        
        self.assertIsInstance(categories, list)
        self.assertTrue(len(categories) >= 2)  # button and input
        
        # Check structure
        for category in categories:
            self.assertIn('value', category)
            self.assertIn('label', category)
            self.assertIn('count', category)
        
        # Find button category
        button_category = next((c for c in categories if c['value'] == 'button'), None)
        self.assertIsNotNone(button_category)
        self.assertEqual(button_category['count'], 2)  # Two button templates
    
    def test_get_layout_categories(self):
        """Test getting layout template categories"""
        # Create additional layout templates
        LayoutTemplate.objects.create(
            name='Flex Layout',
            layout_type='flex',
            user=self.user1
        )
        LayoutTemplate.objects.create(
            name='Another Grid',
            layout_type='grid',
            user=self.user2
        )
        
        categories = TemplateService.get_layout_categories()
        
        self.assertIsInstance(categories, list)
        self.assertTrue(len(categories) >= 2)  # grid and flex
        
        # Find grid category
        grid_category = next((c for c in categories if c['value'] == 'grid'), None)
        self.assertIsNotNone(grid_category)
        self.assertEqual(grid_category['count'], 2)  # Two grid templates
    
    def test_get_app_categories(self):
        """Test getting app template categories"""
        # Create additional app templates
        AppTemplate.objects.create(
            name='E-commerce App',
            app_category='ecommerce',
            user=self.user1
        )
        AppTemplate.objects.create(
            name='Another Business App',
            app_category='business',
            user=self.user2
        )
        
        categories = TemplateService.get_app_categories()
        
        self.assertIsInstance(categories, list)
        self.assertTrue(len(categories) >= 2)  # business and ecommerce
        
        # Check that labels are properly mapped
        business_category = next((c for c in categories if c['value'] == 'business'), None)
        self.assertIsNotNone(business_category)
        self.assertEqual(business_category['label'], 'Business Apps')
        self.assertEqual(business_category['count'], 2)
    
    def test_search_templates_all_types(self):
        """Test searching across all template types"""
        results = TemplateService.search_templates(
            query='test',
            is_public=True
        )
        
        self.assertIn('components', results)
        self.assertIn('layouts', results)
        self.assertIn('apps', results)
        
        # Should find the component template with 'test' in name
        self.assertEqual(len(results['components']), 1)
        self.assertEqual(results['components'][0]['name'], 'Test Button')
    
    def test_search_templates_specific_type(self):
        """Test searching specific template type"""
        results = TemplateService.search_templates(
            query='grid',
            template_type='layouts',
            is_public=True
        )
        
        self.assertEqual(len(results['components']), 0)
        self.assertEqual(len(results['layouts']), 1)
        self.assertEqual(len(results['apps']), 0)
        self.assertEqual(results['layouts'][0]['name'], 'Grid Layout')
    
    def test_search_templates_by_category(self):
        """Test searching templates by category"""
        results = TemplateService.search_templates(
            template_type='apps',
            category='business',
            is_public=True
        )
        
        self.assertEqual(len(results['apps']), 1)
        self.assertEqual(results['apps'][0]['name'], 'Business App')
    
    def test_search_templates_by_user(self):
        """Test searching templates by user"""
        results = TemplateService.search_templates(
            user=self.user1
        )
        
        # Should find all templates created by user1
        self.assertEqual(len(results['components']), 1)
        self.assertEqual(len(results['layouts']), 1)
        self.assertEqual(len(results['apps']), 1)
    
    def test_get_featured_templates(self):
        """Test getting featured templates"""
        # Create more templates to test limiting
        for i in range(10):
            ComponentTemplate.objects.create(
                name=f'Component {i}',
                component_type='button',
                user=self.user1,
                is_public=True
            )
        
        featured = TemplateService.get_featured_templates()
        
        self.assertIn('components', featured)
        self.assertIn('layouts', featured)
        self.assertIn('apps', featured)
        
        # Should limit to 6 items each
        self.assertLessEqual(len(featured['components']), 6)
        self.assertLessEqual(len(featured['layouts']), 6)
        self.assertLessEqual(len(featured['apps']), 6)
    
    def test_get_template_stats(self):
        """Test getting template statistics"""
        stats = TemplateService.get_template_stats()
        
        self.assertIn('total_templates', stats)
        self.assertIn('public_templates', stats)
        self.assertIn('component_templates', stats)
        self.assertIn('layout_templates', stats)
        self.assertIn('app_templates', stats)
        self.assertIn('categories', stats)
        
        # Check counts
        self.assertEqual(stats['component_templates'], 1)
        self.assertEqual(stats['layout_templates'], 1)
        self.assertEqual(stats['app_templates'], 1)
        self.assertEqual(stats['total_templates'], 3)
        self.assertEqual(stats['public_templates'], 3)  # All are public
        
        # Check categories
        self.assertIn('components', stats['categories'])
        self.assertIn('layouts', stats['categories'])
        self.assertIn('apps', stats['categories'])
    
    def test_clone_component_template(self):
        """Test cloning a component template"""
        cloned = TemplateService.clone_template(
            template_id=self.component_template.id,
            template_type='component',
            user=self.user2,
            new_name='Cloned Button'
        )
        
        self.assertIsInstance(cloned, ComponentTemplate)
        self.assertEqual(cloned.name, 'Cloned Button')
        self.assertEqual(cloned.component_type, 'button')
        self.assertEqual(cloned.user, self.user2)
        self.assertFalse(cloned.is_public)  # Clones should be private
        self.assertEqual(cloned.default_props, self.component_template.default_props)
    
    def test_clone_layout_template(self):
        """Test cloning a layout template"""
        cloned = TemplateService.clone_template(
            template_id=self.layout_template.id,
            template_type='layout',
            user=self.user2
        )
        
        self.assertIsInstance(cloned, LayoutTemplate)
        self.assertEqual(cloned.name, 'Grid Layout (Copy)')  # Default name
        self.assertEqual(cloned.layout_type, 'grid')
        self.assertEqual(cloned.user, self.user2)
        self.assertFalse(cloned.is_public)
    
    def test_clone_app_template(self):
        """Test cloning an app template"""
        cloned = TemplateService.clone_template(
            template_id=self.app_template.id,
            template_type='app',
            user=self.user2,
            new_name='My Business App'
        )
        
        self.assertIsInstance(cloned, AppTemplate)
        self.assertEqual(cloned.name, 'My Business App')
        self.assertEqual(cloned.app_category, 'business')
        self.assertEqual(cloned.user, self.user2)
        self.assertFalse(cloned.is_public)
    
    def test_clone_template_invalid_type(self):
        """Test cloning with invalid template type"""
        with self.assertRaises(Exception) as context:
            TemplateService.clone_template(
                template_id=self.component_template.id,
                template_type='invalid',
                user=self.user2
            )
        
        self.assertIn('Invalid template type', str(context.exception))
    
    def test_clone_template_nonexistent(self):
        """Test cloning a non-existent template"""
        with self.assertRaises(Exception):
            TemplateService.clone_template(
                template_id=99999,
                template_type='component',
                user=self.user2
            )


class TemplateServiceIntegrationTest(TestCase):
    """Integration tests for TemplateService with complex scenarios"""
    
    def setUp(self):
        """Set up complex test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create a variety of templates
        self.create_test_templates()
    
    def create_test_templates(self):
        """Create a variety of test templates"""
        # Component templates
        ComponentTemplate.objects.create(
            name='Primary Button',
            component_type='button',
            user=self.user,
            is_public=True
        )
        ComponentTemplate.objects.create(
            name='Search Input',
            component_type='input',
            user=self.user,
            is_public=True
        )
        ComponentTemplate.objects.create(
            name='Data Table',
            component_type='table',
            user=self.user,
            is_public=True
        )
        
        # Layout templates
        LayoutTemplate.objects.create(
            name='Dashboard Grid',
            layout_type='grid',
            user=self.user,
            is_public=True
        )
        LayoutTemplate.objects.create(
            name='Sidebar Layout',
            layout_type='sidebar',
            user=self.user,
            is_public=True
        )
        
        # App templates
        AppTemplate.objects.create(
            name='E-commerce Store',
            app_category='ecommerce',
            user=self.user,
            is_public=True
        )
        AppTemplate.objects.create(
            name='Portfolio Site',
            app_category='portfolio',
            user=self.user,
            is_public=True
        )
        AppTemplate.objects.create(
            name='Private Data App',
            app_category='business',
            user=self.user,
            is_public=False
        )
    
    def test_comprehensive_search(self):
        """Test comprehensive search across all templates"""
        # Search for 'data' should find Data Table, Dashboard Grid, and Private Data App
        results = TemplateService.search_templates(query='data')

        total_results = (
            len(results['components']) +
            len(results['layouts']) +
            len(results['apps'])
        )
        # Should find at least Data Table and Dashboard Grid (both public)
        self.assertGreaterEqual(total_results, 2)
    
    def test_category_distribution(self):
        """Test that categories are properly distributed"""
        component_cats = TemplateService.get_component_categories()
        layout_cats = TemplateService.get_layout_categories()
        app_cats = TemplateService.get_app_categories()
        
        # Should have multiple categories
        self.assertGreaterEqual(len(component_cats), 2)
        self.assertGreaterEqual(len(layout_cats), 2)
        self.assertGreaterEqual(len(app_cats), 2)
        
        # Total counts should match
        total_components = sum(cat['count'] for cat in component_cats)
        total_layouts = sum(cat['count'] for cat in layout_cats)
        total_apps = sum(cat['count'] for cat in app_cats)
        
        self.assertEqual(total_components, ComponentTemplate.objects.count())
        self.assertEqual(total_layouts, LayoutTemplate.objects.count())
        self.assertEqual(total_apps, AppTemplate.objects.count())
    
    def test_public_vs_private_filtering(self):
        """Test filtering between public and private templates"""
        public_results = TemplateService.search_templates(is_public=True)
        private_results = TemplateService.search_templates(is_public=False)
        
        # Should have both public and private templates
        public_count = (
            len(public_results['components']) + 
            len(public_results['layouts']) + 
            len(public_results['apps'])
        )
        private_count = (
            len(private_results['components']) + 
            len(private_results['layouts']) + 
            len(private_results['apps'])
        )
        
        self.assertGreater(public_count, 0)
        self.assertGreater(private_count, 0)
        
        # Total should match all templates
        total_templates = (
            ComponentTemplate.objects.count() + 
            LayoutTemplate.objects.count() + 
            AppTemplate.objects.count()
        )
        self.assertEqual(public_count + private_count, total_templates)
