"""
Integration tests for API endpoints in the App Builder application.
Tests the complete flow from HTTP request to database operations.
"""

import pytest
import json
from django.test import TransactionTestCase
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from model_bakery import baker
from unittest.mock import patch, Mock

from my_app.models import App, LayoutTemplate, AppTemplate, Comment, Collaboration


@pytest.mark.django_db(transaction=True)
class TestAppAPIIntegration(TransactionTestCase):
    """Integration tests for App API endpoints."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

    def test_complete_app_lifecycle(self):
        """Test complete app creation, update, and deletion flow."""
        # Create app
        app_data = {
            'name': 'Integration Test App',
            'description': 'An app for integration testing',
            'app_data': json.dumps({
                'components': [
                    {'id': '1', 'type': 'button', 'props': {'text': 'Click me'}}
                ],
                'layout': {'type': 'flex', 'direction': 'column'}
            }),
            'is_public': False
        }

        create_response = self.client.post(reverse('app-list'), app_data, format='json')
        self.assertEqual(create_response.status_code, status.HTTP_201_CREATED)
        
        app_id = create_response.data['id']
        self.assertIsNotNone(app_id)

        # Verify app was created in database
        app = App.objects.get(id=app_id)
        self.assertEqual(app.name, 'Integration Test App')
        self.assertEqual(app.user, self.user)

        # Update app
        update_data = {
            'name': 'Updated Integration Test App',
            'app_data': json.dumps({
                'components': [
                    {'id': '1', 'type': 'button', 'props': {'text': 'Updated button'}},
                    {'id': '2', 'type': 'input', 'props': {'placeholder': 'Enter text'}}
                ]
            })
        }

        update_response = self.client.patch(
            reverse('app-detail', kwargs={'pk': app_id}),
            update_data,
            format='json'
        )
        self.assertEqual(update_response.status_code, status.HTTP_200_OK)

        # Verify update in database
        app.refresh_from_db()
        self.assertEqual(app.name, 'Updated Integration Test App')
        app_data_parsed = json.loads(app.app_data)
        self.assertEqual(len(app_data_parsed['components']), 2)

        # List apps
        list_response = self.client.get(reverse('app-list'))
        self.assertEqual(list_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(list_response.data['results']), 1)

        # Delete app
        delete_response = self.client.delete(reverse('app-detail', kwargs={'pk': app_id}))
        self.assertEqual(delete_response.status_code, status.HTTP_204_NO_CONTENT)

        # Verify deletion
        self.assertFalse(App.objects.filter(id=app_id).exists())

    def test_app_collaboration_workflow(self):
        """Test app collaboration features integration."""
        # Create app
        app = App.objects.create(
            name='Collaborative App',
            user=self.user,
            app_data='{}',
            allow_collaboration=True
        )

        # Create another user
        collaborator = User.objects.create_user(
            username='collaborator',
            email='<EMAIL>',
            password='collabpass123'
        )

        # Add collaborator
        collaboration_data = {
            'app': app.id,
            'user': collaborator.id,
            'role': 'editor'
        }

        collab_response = self.client.post(
            reverse('collaboration-list'),
            collaboration_data,
            format='json'
        )
        self.assertEqual(collab_response.status_code, status.HTTP_201_CREATED)

        # Verify collaboration in database
        collaboration = Collaboration.objects.get(app=app, user=collaborator)
        self.assertEqual(collaboration.role, 'editor')
        self.assertEqual(collaboration.invited_by, self.user)

        # Test collaborator access
        collab_token = Token.objects.create(user=collaborator)
        collab_client = APIClient()
        collab_client.credentials(HTTP_AUTHORIZATION=f'Token {collab_token.key}')

        # Collaborator should be able to access the app
        app_response = collab_client.get(reverse('app-detail', kwargs={'pk': app.id}))
        self.assertEqual(app_response.status_code, status.HTTP_200_OK)

        # Collaborator should be able to edit (if role allows)
        update_data = {'description': 'Updated by collaborator'}
        update_response = collab_client.patch(
            reverse('app-detail', kwargs={'pk': app.id}),
            update_data,
            format='json'
        )
        self.assertEqual(update_response.status_code, status.HTTP_200_OK)

    def test_app_comments_integration(self):
        """Test app comments functionality integration."""
        # Create app
        app = App.objects.create(
            name='Commented App',
            user=self.user,
            app_data=json.dumps({
                'components': [
                    {'id': 'button-1', 'type': 'button', 'props': {'text': 'Button'}}
                ]
            })
        )

        # Add comment
        comment_data = {
            'app': app.id,
            'text': 'This button needs better styling',
            'component_id': 'button-1',
            'position_x': 150,
            'position_y': 200
        }

        comment_response = self.client.post(
            reverse('comment-list'),
            comment_data,
            format='json'
        )
        self.assertEqual(comment_response.status_code, status.HTTP_201_CREATED)

        # Verify comment in database
        comment = Comment.objects.get(app=app, user=self.user)
        self.assertEqual(comment.text, 'This button needs better styling')
        self.assertEqual(comment.component_id, 'button-1')

        # List comments for app
        list_response = self.client.get(
            reverse('comment-list'),
            {'app': app.id}
        )
        self.assertEqual(list_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(list_response.data['results']), 1)

        # Update comment
        comment_id = comment_response.data['id']
        update_data = {'text': 'Updated comment text'}
        update_response = self.client.patch(
            reverse('comment-detail', kwargs={'pk': comment_id}),
            update_data,
            format='json'
        )
        self.assertEqual(update_response.status_code, status.HTTP_200_OK)

        # Verify update
        comment.refresh_from_db()
        self.assertEqual(comment.text, 'Updated comment text')

    def test_template_integration_workflow(self):
        """Test template creation and usage integration."""
        # Create layout template
        template_data = {
            'name': 'Business Layout',
            'description': 'A professional business layout',
            'category': 'business',
            'components': {
                'header': {
                    'type': 'header',
                    'props': {'title': 'Business Header', 'height': '80px'}
                },
                'sidebar': {
                    'type': 'sidebar',
                    'props': {'width': '250px', 'position': 'left'}
                },
                'content': {
                    'type': 'content',
                    'props': {'padding': '20px'}
                }
            },
            'is_public': True
        }

        template_response = self.client.post(
            reverse('layouttemplate-list'),
            template_data,
            format='json'
        )
        self.assertEqual(template_response.status_code, status.HTTP_201_CREATED)

        template_id = template_response.data['id']

        # Verify template in database
        template = LayoutTemplate.objects.get(id=template_id)
        self.assertEqual(template.name, 'Business Layout')
        self.assertEqual(template.category, 'business')

        # Use template to create app
        app_from_template_data = {
            'name': 'App from Template',
            'description': 'Created using business template',
            'app_data': json.dumps({
                'template_id': template_id,
                'components': template.components
            })
        }

        app_response = self.client.post(
            reverse('app-list'),
            app_from_template_data,
            format='json'
        )
        self.assertEqual(app_response.status_code, status.HTTP_201_CREATED)

        # Verify app was created with template data
        app = App.objects.get(id=app_response.data['id'])
        app_data = json.loads(app.app_data)
        self.assertEqual(app_data['template_id'], template_id)
        self.assertIn('header', app_data['components'])

    @patch('my_app.services.ai_service.generate_suggestions')
    def test_ai_integration_workflow(self, mock_ai_service):
        """Test AI suggestions integration."""
        # Mock AI service response
        mock_ai_service.return_value = [
            {
                'id': 'suggestion-1',
                'type': 'layout',
                'title': 'Add Container',
                'description': 'Wrap components in a container',
                'confidence': 0.8,
                'changes': {
                    'add_component': {
                        'type': 'container',
                        'props': {'padding': '20px'}
                    }
                }
            }
        ]

        # Create app with components
        app = App.objects.create(
            name='AI Test App',
            user=self.user,
            app_data=json.dumps({
                'components': [
                    {'id': '1', 'type': 'button', 'props': {'text': 'Button'}},
                    {'id': '2', 'type': 'input', 'props': {'placeholder': 'Input'}}
                ]
            })
        )

        # Request AI suggestions
        ai_request_data = {
            'app_id': app.id,
            'context': 'layout_improvement'
        }

        ai_response = self.client.post(
            reverse('ai-suggestions'),
            ai_request_data,
            format='json'
        )
        self.assertEqual(ai_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(ai_response.data['suggestions']), 1)

        # Verify AI service was called
        mock_ai_service.assert_called_once()

        # Apply suggestion
        suggestion_id = ai_response.data['suggestions'][0]['id']
        apply_data = {
            'app_id': app.id,
            'suggestion_id': suggestion_id
        }

        apply_response = self.client.post(
            reverse('ai-apply-suggestion'),
            apply_data,
            format='json'
        )
        self.assertEqual(apply_response.status_code, status.HTTP_200_OK)

    def test_export_integration_workflow(self):
        """Test code export integration."""
        # Create app with components
        app = App.objects.create(
            name='Export Test App',
            user=self.user,
            app_data=json.dumps({
                'components': [
                    {'id': '1', 'type': 'button', 'props': {'text': 'Export Button'}},
                    {'id': '2', 'type': 'input', 'props': {'placeholder': 'Export Input'}}
                ],
                'styles': {
                    'theme': 'modern',
                    'primaryColor': '#007bff'
                }
            })
        )

        # Request export
        export_data = {
            'app_id': app.id,
            'format': 'react',
            'options': {
                'typescript': True,
                'styled_components': True
            }
        }

        with patch('my_app.services.export_service.generate_export') as mock_export:
            mock_export.return_value = {
                'download_url': 'https://example.com/exports/app-123.zip',
                'file_size': 2048,
                'format': 'react'
            }

            export_response = self.client.post(
                reverse('export-app'),
                export_data,
                format='json'
            )
            self.assertEqual(export_response.status_code, status.HTTP_200_OK)
            self.assertIn('download_url', export_response.data)

            # Verify export service was called
            mock_export.assert_called_once()

    def test_error_handling_integration(self):
        """Test error handling across the API."""
        # Test invalid app data
        invalid_app_data = {
            'name': '',  # Invalid: empty name
            'app_data': 'invalid json'  # Invalid: malformed JSON
        }

        response = self.client.post(reverse('app-list'), invalid_app_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test unauthorized access
        self.client.credentials()  # Remove authentication
        response = self.client.get(reverse('app-list'))
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Test accessing non-existent resource
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        response = self.client.get(reverse('app-detail', kwargs={'pk': 99999}))
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_pagination_integration(self):
        """Test API pagination."""
        # Create multiple apps
        for i in range(15):
            App.objects.create(
                name=f'Test App {i}',
                user=self.user,
                app_data='{}'
            )

        # Test first page
        response = self.client.get(reverse('app-list'), {'page': 1, 'page_size': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 10)
        self.assertIsNotNone(response.data['next'])

        # Test second page
        response = self.client.get(reverse('app-list'), {'page': 2, 'page_size': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 5)
        self.assertIsNone(response.data['next'])

    def test_filtering_and_search_integration(self):
        """Test API filtering and search functionality."""
        # Create apps with different properties
        App.objects.create(name='React Dashboard', user=self.user, app_data='{}', is_public=True)
        App.objects.create(name='Vue Portfolio', user=self.user, app_data='{}', is_public=False)
        App.objects.create(name='Angular Blog', user=self.user, app_data='{}', is_public=True)

        # Test search
        response = self.client.get(reverse('app-list'), {'search': 'React'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'React Dashboard')

        # Test filtering by public status
        response = self.client.get(reverse('app-list'), {'is_public': 'true'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

        # Test ordering
        response = self.client.get(reverse('app-list'), {'ordering': '-created_at'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should be ordered by creation date descending
        results = response.data['results']
        self.assertGreaterEqual(len(results), 3)

    def test_concurrent_access_integration(self):
        """Test concurrent access to the same resource."""
        # Create app
        app = App.objects.create(
            name='Concurrent Test App',
            user=self.user,
            app_data=json.dumps({'version': 1})
        )

        # Create second user
        user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='pass123'
        )
        token2 = Token.objects.create(user=user2)
        client2 = APIClient()
        client2.credentials(HTTP_AUTHORIZATION=f'Token {token2.key}')

        # Add user2 as collaborator
        Collaboration.objects.create(
            app=app,
            user=user2,
            role='editor',
            invited_by=self.user
        )

        # Both users update the app simultaneously
        update_data1 = {'description': 'Updated by user 1'}
        update_data2 = {'description': 'Updated by user 2'}

        response1 = self.client.patch(
            reverse('app-detail', kwargs={'pk': app.id}),
            update_data1,
            format='json'
        )
        response2 = client2.patch(
            reverse('app-detail', kwargs={'pk': app.id}),
            update_data2,
            format='json'
        )

        # Both updates should succeed (last one wins)
        self.assertEqual(response1.status_code, status.HTTP_200_OK)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)

        # Verify final state
        app.refresh_from_db()
        self.assertIn(app.description, ['Updated by user 1', 'Updated by user 2'])
