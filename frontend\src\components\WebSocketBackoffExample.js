/**
 * Example component demonstrating the enhanced WebSocket service
 * with exponential backoff and jitter
 */

import React, { useState, useCallback } from 'react';
import { Card, Button, Badge, Typography, Space, Divider, Alert, Input } from 'antd';
import { useWebSocketWithBackoff, ConnectionState } from '../services/websocket';

const { Title, Text, Paragraph } = Typography;

const WebSocketBackoffExample = () => {
  const [messages, setMessages] = useState([]);
  const [messageToSend, setMessageToSend] = useState('');
  const [reconnectInfo, setReconnectInfo] = useState(null);

  // WebSocket URL - adjust as needed
  const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws/app_builder/';

  // Initialize WebSocket with enhanced backoff
  const {
    connect,
    disconnect,
    send,
    isConnected,
    getConnectionState,
    connectionAttempts,
    maxAttempts
  } = useWebSocketWithBackoff({
    url: wsUrl,
    autoConnect: true,
    autoReconnect: true,
    maxReconnectAttempts: 10,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    jitterFactor: 0.3, // 30% jitter
    debug: true,
    onOpen: (event) => {
      console.log('WebSocket opened:', event);
      setMessages(prev => [...prev, {
        type: 'system',
        message: 'Connected to WebSocket server',
        timestamp: new Date().toLocaleTimeString()
      }]);
      setReconnectInfo(null);
    },
    onClose: (event) => {
      console.log('WebSocket closed:', event);
      setMessages(prev => [...prev, {
        type: 'system',
        message: `Connection closed (Code: ${event.code}, Reason: ${event.reason})`,
        timestamp: new Date().toLocaleTimeString()
      }]);
    },
    onMessage: (data) => {
      console.log('Message received:', data);
      setMessages(prev => [...prev, {
        type: 'message',
        message: typeof data === 'object' ? JSON.stringify(data, null, 2) : data,
        timestamp: new Date().toLocaleTimeString()
      }]);
    },
    onError: (error) => {
      console.error('WebSocket error:', error);
      setMessages(prev => [...prev, {
        type: 'error',
        message: `Error: ${error.message || 'Unknown error'}`,
        timestamp: new Date().toLocaleTimeString()
      }]);
    },
    onReconnecting: (info) => {
      console.log('Reconnecting:', info);
      setReconnectInfo(info);
      setMessages(prev => [...prev, {
        type: 'system',
        message: `Reconnecting... (Attempt ${info.attempt}/${info.maxAttempts}, Delay: ${Math.round(info.delay/1000)}s)`,
        timestamp: new Date().toLocaleTimeString()
      }]);
    }
  });

  // Get connection status badge
  const getStatusBadge = () => {
    const state = getConnectionState();
    const connected = isConnected();
    
    switch (state) {
      case ConnectionState.CONNECTED:
        return <Badge status="success" text="Connected" />;
      case ConnectionState.CONNECTING:
        return <Badge status="processing" text="Connecting..." />;
      case ConnectionState.RECONNECTING:
        return <Badge status="warning" text="Reconnecting..." />;
      case ConnectionState.FAILED:
        return <Badge status="error" text="Failed" />;
      default:
        return <Badge status="default" text="Disconnected" />;
    }
  };

  // Handle sending messages
  const handleSendMessage = useCallback(() => {
    if (messageToSend.trim()) {
      const success = send({
        type: 'test_message',
        content: messageToSend,
        timestamp: Date.now()
      });
      
      if (success) {
        setMessages(prev => [...prev, {
          type: 'sent',
          message: messageToSend,
          timestamp: new Date().toLocaleTimeString()
        }]);
        setMessageToSend('');
      } else {
        setMessages(prev => [...prev, {
          type: 'error',
          message: 'Failed to send message - not connected',
          timestamp: new Date().toLocaleTimeString()
        }]);
      }
    }
  }, [messageToSend, send]);

  // Clear messages
  const clearMessages = () => {
    setMessages([]);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title level={3}>WebSocket with Exponential Backoff & Jitter</Title>
        
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* Connection Status */}
          <div>
            <Text strong>Status: </Text>
            {getStatusBadge()}
            <Text style={{ marginLeft: '16px' }}>
              Attempts: {connectionAttempts}/{maxAttempts}
            </Text>
          </div>

          {/* Reconnection Info */}
          {reconnectInfo && (
            <Alert
              message="Reconnecting..."
              description={`Attempt ${reconnectInfo.attempt} of ${reconnectInfo.maxAttempts}. Next attempt in ${Math.round(reconnectInfo.delay/1000)} seconds.`}
              type="warning"
              showIcon
            />
          )}

          {/* Connection Controls */}
          <Space>
            <Button 
              type="primary" 
              onClick={connect}
              disabled={isConnected()}
            >
              Connect
            </Button>
            <Button 
              onClick={disconnect}
              disabled={!isConnected()}
            >
              Disconnect
            </Button>
            <Button onClick={clearMessages}>
              Clear Messages
            </Button>
          </Space>

          <Divider />

          {/* Message Sending */}
          <div>
            <Title level={4}>Send Message</Title>
            <Space.Compact style={{ width: '100%' }}>
              <Input
                placeholder="Enter message to send..."
                value={messageToSend}
                onChange={(e) => setMessageToSend(e.target.value)}
                onPressEnter={handleSendMessage}
                disabled={!isConnected()}
              />
              <Button 
                type="primary" 
                onClick={handleSendMessage}
                disabled={!isConnected() || !messageToSend.trim()}
              >
                Send
              </Button>
            </Space.Compact>
          </div>

          <Divider />

          {/* Messages Log */}
          <div>
            <Title level={4}>Messages ({messages.length})</Title>
            <div 
              style={{ 
                height: '300px', 
                overflowY: 'auto', 
                border: '1px solid #d9d9d9', 
                padding: '8px',
                backgroundColor: '#fafafa'
              }}
            >
              {messages.length === 0 ? (
                <Text type="secondary">No messages yet...</Text>
              ) : (
                messages.map((msg, index) => (
                  <div key={index} style={{ marginBottom: '8px' }}>
                    <Text 
                      type={msg.type === 'error' ? 'danger' : msg.type === 'system' ? 'warning' : 'default'}
                      style={{ fontSize: '12px' }}
                    >
                      [{msg.timestamp}]
                    </Text>
                    <Text 
                      style={{ 
                        marginLeft: '8px',
                        color: msg.type === 'error' ? '#ff4d4f' : 
                               msg.type === 'system' ? '#fa8c16' :
                               msg.type === 'sent' ? '#1890ff' : '#000'
                      }}
                    >
                      {msg.message}
                    </Text>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Implementation Details */}
          <Divider />
          <div>
            <Title level={4}>Implementation Features</Title>
            <Paragraph>
              <ul>
                <li><strong>Exponential Backoff:</strong> Reconnection delays increase exponentially (1s, 2s, 4s, 8s, ...)</li>
                <li><strong>Jitter:</strong> ±30% random variation to prevent thundering herd problem</li>
                <li><strong>Max Delay Cap:</strong> Delays capped at 30 seconds</li>
                <li><strong>Automatic Reconnection:</strong> Attempts up to 10 times before giving up</li>
                <li><strong>Connection State Tracking:</strong> Detailed state management and logging</li>
                <li><strong>Message Queuing:</strong> Safe message handling with error recovery</li>
              </ul>
            </Paragraph>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default WebSocketBackoffExample;
