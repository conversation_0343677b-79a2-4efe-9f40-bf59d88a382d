import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { debounce, throttle } from 'lodash';

/**
 * Custom hook for optimizing preview performance
 * Handles virtual rendering, component caching, and performance monitoring
 */
const usePreviewPerformance = ({
  components = [],
  containerHeight = 600,
  itemHeight = 100,
  overscan = 5,
  enableVirtualization = true,
  enablePerformanceMonitoring = true
}) => {
  // State for virtualization
  const [scrollTop, setScrollTop] = useState(0);
  const [containerRef, setContainerRef] = useState(null);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 });
  
  // Performance monitoring state
  const [renderTime, setRenderTime] = useState(0);
  const [frameRate, setFrameRate] = useState(60);
  const [memoryUsage, setMemoryUsage] = useState(0);
  
  // Refs for performance tracking
  const renderStartTime = useRef(0);
  const frameCount = useRef(0);
  const lastFrameTime = useRef(performance.now());
  const componentCache = useRef(new Map());
  const intersectionObserver = useRef(null);
  
  // Calculate visible items for virtualization
  const calculateVisibleRange = useCallback(() => {
    if (!enableVirtualization || !containerRef || components.length === 0) {
      return { start: 0, end: components.length };
    }
    
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + overscan,
      components.length
    );
    
    return {
      start: Math.max(0, startIndex - overscan),
      end: endIndex
    };
  }, [scrollTop, itemHeight, containerHeight, overscan, components.length, enableVirtualization, containerRef]);

  // Update visible range when scroll changes
  useEffect(() => {
    const newRange = calculateVisibleRange();
    setVisibleRange(newRange);
  }, [calculateVisibleRange]);

  // Throttled scroll handler
  const handleScroll = useCallback(
    throttle((event) => {
      if (event.target) {
        setScrollTop(event.target.scrollTop);
      }
    }, 16), // ~60fps
    []
  );

  // Get visible components for rendering
  const visibleComponents = useMemo(() => {
    if (!enableVirtualization) {
      return components.map((component, index) => ({ component, index }));
    }
    
    return components
      .slice(visibleRange.start, visibleRange.end)
      .map((component, relativeIndex) => ({
        component,
        index: visibleRange.start + relativeIndex
      }));
  }, [components, visibleRange, enableVirtualization]);

  // Component caching for performance
  const getCachedComponent = useCallback((componentId, renderFunction) => {
    const cacheKey = `${componentId}_${JSON.stringify(components.find(c => c.id === componentId))}`;
    
    if (componentCache.current.has(cacheKey)) {
      return componentCache.current.get(cacheKey);
    }
    
    const renderedComponent = renderFunction();
    componentCache.current.set(cacheKey, renderedComponent);
    
    // Limit cache size to prevent memory leaks
    if (componentCache.current.size > 100) {
      const firstKey = componentCache.current.keys().next().value;
      componentCache.current.delete(firstKey);
    }
    
    return renderedComponent;
  }, [components]);

  // Performance monitoring
  const startRenderMeasurement = useCallback(() => {
    if (enablePerformanceMonitoring) {
      renderStartTime.current = performance.now();
    }
  }, [enablePerformanceMonitoring]);

  const endRenderMeasurement = useCallback(() => {
    if (enablePerformanceMonitoring && renderStartTime.current > 0) {
      const renderDuration = performance.now() - renderStartTime.current;
      setRenderTime(renderDuration);
      renderStartTime.current = 0;
    }
  }, [enablePerformanceMonitoring]);

  // Frame rate monitoring
  useEffect(() => {
    if (!enablePerformanceMonitoring) return;
    
    let animationId;
    
    const measureFrameRate = () => {
      const now = performance.now();
      const delta = now - lastFrameTime.current;
      
      if (delta >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / delta);
        setFrameRate(fps);
        frameCount.current = 0;
        lastFrameTime.current = now;
      } else {
        frameCount.current++;
      }
      
      animationId = requestAnimationFrame(measureFrameRate);
    };
    
    animationId = requestAnimationFrame(measureFrameRate);
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enablePerformanceMonitoring]);

  // Memory usage monitoring
  useEffect(() => {
    if (!enablePerformanceMonitoring || !performance.memory) return;
    
    const measureMemory = () => {
      const memoryInfo = performance.memory;
      const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
      setMemoryUsage(usedMB);
    };
    
    const interval = setInterval(measureMemory, 5000);
    measureMemory(); // Initial measurement
    
    return () => clearInterval(interval);
  }, [enablePerformanceMonitoring]);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!enableVirtualization) return;
    
    intersectionObserver.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Component is visible, ensure it's rendered
            const componentId = entry.target.dataset.componentId;
            if (componentId) {
              // Trigger re-render if needed
            }
          }
        });
      },
      {
        root: containerRef,
        rootMargin: `${overscan * itemHeight}px`,
        threshold: 0.1
      }
    );
    
    return () => {
      if (intersectionObserver.current) {
        intersectionObserver.current.disconnect();
      }
    };
  }, [containerRef, overscan, itemHeight, enableVirtualization]);

  // Clear cache when components change significantly
  useEffect(() => {
    const componentIds = new Set(components.map(c => c.id));
    const cachedIds = new Set(
      Array.from(componentCache.current.keys()).map(key => key.split('_')[0])
    );
    
    // Remove cached components that no longer exist
    cachedIds.forEach(cachedId => {
      if (!componentIds.has(cachedId)) {
        Array.from(componentCache.current.keys())
          .filter(key => key.startsWith(cachedId))
          .forEach(key => componentCache.current.delete(key));
      }
    });
  }, [components]);

  // Get container props for virtualization
  const getContainerProps = useCallback(() => {
    if (!enableVirtualization) {
      return {};
    }
    
    return {
      ref: setContainerRef,
      onScroll: handleScroll,
      style: {
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }
    };
  }, [enableVirtualization, containerHeight, handleScroll]);

  // Get spacer props for virtual scrolling
  const getSpacerProps = useCallback(() => {
    if (!enableVirtualization) {
      return { before: {}, after: {} };
    }
    
    const totalHeight = components.length * itemHeight;
    const beforeHeight = visibleRange.start * itemHeight;
    const afterHeight = totalHeight - (visibleRange.end * itemHeight);
    
    return {
      before: {
        style: {
          height: beforeHeight,
          width: '100%'
        }
      },
      after: {
        style: {
          height: afterHeight,
          width: '100%'
        }
      }
    };
  }, [enableVirtualization, components.length, itemHeight, visibleRange]);

  // Performance optimization utilities
  const optimizationUtils = {
    clearCache: () => componentCache.current.clear(),
    getCacheSize: () => componentCache.current.size,
    getPerformanceMetrics: () => ({
      renderTime,
      frameRate,
      memoryUsage,
      cacheSize: componentCache.current.size,
      visibleComponents: visibleComponents.length,
      totalComponents: components.length
    }),
    shouldRender: (componentId) => {
      // Check if component should be rendered based on visibility
      if (!enableVirtualization) return true;
      
      const componentIndex = components.findIndex(c => c.id === componentId);
      return componentIndex >= visibleRange.start && componentIndex < visibleRange.end;
    }
  };

  return {
    // Virtualization
    visibleComponents,
    visibleRange,
    getContainerProps,
    getSpacerProps,
    
    // Performance monitoring
    renderTime,
    frameRate,
    memoryUsage,
    startRenderMeasurement,
    endRenderMeasurement,
    
    // Caching
    getCachedComponent,
    
    // Utilities
    ...optimizationUtils
  };
};

export default usePreviewPerformance;
