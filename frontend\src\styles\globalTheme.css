/**
 * Global theme styles
 * These styles ensure consistent theming across the application
 */

/* Apply theme variables to all elements */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Ant Design overrides */
.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.ant-btn-default {
  border-color: var(--border-color);
  color: var(--text-color);
}

.ant-btn-default:hover,
.ant-btn-default:focus {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector,
.ant-picker,
.ant-input-number,
.ant-cascader-picker,
.ant-mentions,
.ant-input-search {
  border-color: var(--border-color) !important;
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
}

.ant-input:hover,
.ant-input-affix-wrapper:hover,
.ant-select-selector:hover,
.ant-picker:hover,
.ant-input-number:hover,
.ant-cascader-picker:hover,
.ant-mentions:hover,
.ant-input-search:hover {
  border-color: var(--primary-color) !important;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-select-selector:focus,
.ant-picker:focus,
.ant-input-number:focus,
.ant-cascader-picker:focus,
.ant-mentions:focus,
.ant-input-search:focus,
.ant-input-focused,
.ant-input-affix-wrapper-focused,
.ant-select-focused .ant-select-selector,
.ant-picker-focused,
.ant-input-number-focused,
.ant-cascader-picker-focused,
.ant-mentions-focused,
.ant-input-search-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2) !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-radio-checked .ant-radio-inner {
  border-color: var(--primary-color);
}

.ant-radio-inner::after {
  background-color: var(--primary-color);
}

.ant-switch-checked {
  background-color: var(--primary-color);
}

.ant-slider-track {
  background-color: var(--primary-color);
}

.ant-slider-handle {
  border-color: var(--primary-color);
}

.ant-slider-handle:focus {
  box-shadow: 0 0 0 5px rgba(var(--primary-color-rgb), 0.2);
}

.ant-pagination-item-active {
  border-color: var(--primary-color);
}

.ant-pagination-item-active a {
  color: var(--primary-color);
}

.ant-tabs-ink-bar {
  background-color: var(--primary-color);
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--primary-color);
}

.ant-tabs-tab:hover {
  color: var(--primary-dark);
}

.ant-menu-item-selected {
  color: var(--primary-color) !important;
}

.ant-menu-item-selected::after {
  border-bottom-color: var(--primary-color) !important;
}

.ant-menu-item:hover,
.ant-menu-submenu-title:hover {
  color: var(--primary-color) !important;
}

.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.ant-card {
  background-color: var(--background-secondary);
  border-color: var(--border-color);
}

.ant-card-head {
  color: var(--text-color);
  border-bottom-color: var(--border-color);
}

.ant-modal-content,
.ant-modal-header {
  background-color: var(--background-secondary);
  color: var(--text-color);
}

.ant-modal-title {
  color: var(--text-color);
}

.ant-modal-close {
  color: var(--text-color);
}

.ant-drawer-content {
  background-color: var(--background-secondary);
}

.ant-drawer-header {
  border-bottom-color: var(--border-color);
}

.ant-drawer-title {
  color: var(--text-color);
}

.ant-table {
  background-color: var(--background-secondary);
  color: var(--text-color);
}

.ant-table-thead > tr > th {
  background-color: var(--background-secondary);
  color: var(--text-color);
  border-bottom-color: var(--border-color);
}

.ant-table-tbody > tr > td {
  border-bottom-color: var(--border-color);
}

.ant-table-tbody > tr:hover > td {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.ant-select-dropdown {
  background-color: var(--background-secondary);
}

.ant-select-item {
  color: var(--text-color);
}

.ant-select-item-option-selected {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.ant-select-item-option-active {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

/* Dark mode specific adjustments */
[data-theme="dark"] .ant-card,
[data-theme="dark"] .ant-modal-content,
[data-theme="dark"] .ant-drawer-content,
[data-theme="dark"] .ant-table {
  background-color: var(--background-secondary);
}

[data-theme="dark"] .ant-input,
[data-theme="dark"] .ant-input-affix-wrapper,
[data-theme="dark"] .ant-select-selector,
[data-theme="dark"] .ant-picker,
[data-theme="dark"] .ant-input-number,
[data-theme="dark"] .ant-cascader-picker,
[data-theme="dark"] .ant-mentions,
[data-theme="dark"] .ant-input-search {
  background-color: rgba(255, 255, 255, 0.04) !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
}
