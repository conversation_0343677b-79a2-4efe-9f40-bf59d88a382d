/**
 * API Client
 * 
 * This service provides methods for making API requests with automatic
 * error handling, retries, and authentication.
 */

import axios from 'axios';
import { getToken, refreshToken } from '../utils/auth';
import csrfService from './csrfService';

// Default configuration
const DEFAULT_CONFIG = {
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

class ApiClient {
  constructor(config = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.client = axios.create(this.config);
    this.endpoints = [];
    this.initialized = false;

    // Add request interceptor for authentication and CSRF
    this.client.interceptors.request.use(
      async (config) => {
        // Add authentication token if available
        const token = getToken();

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add CSRF token for state-changing requests
        if (config.method && ['post', 'put', 'patch', 'delete'].includes(config.method.toLowerCase())) {
          try {
            const csrfHeaders = await csrfService.getHeaders();
            config.headers = { ...config.headers, ...csrfHeaders };
          } catch (error) {
            console.warn('Failed to get CSRF token:', error);
            if (process.env.NODE_ENV === 'development') {
              console.error('CSRF token fetch failed for request:', config.url, error);
            }
          }
        }

        // Include credentials for CSRF
        config.withCredentials = true;

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for CSRF error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle CSRF token errors
        if (error.response?.status === 403 &&
          (error.response.data?.message?.includes('CSRF') ||
            error.response.data?.detail?.includes('CSRF')) &&
          !originalRequest._retry) {

          console.warn('CSRF token invalid, clearing and retrying request...');
          originalRequest._retry = true;

          // Clear the invalid token
          csrfService.clearToken();

          // Get fresh CSRF token and retry
          try {
            const csrfHeaders = await csrfService.getHeaders();
            originalRequest.headers = { ...originalRequest.headers, ...csrfHeaders };
            return this.client(originalRequest);
          } catch (csrfError) {
            console.error('Failed to refresh CSRF token:', csrfError);
          }
        }

        return Promise.reject(error);
      }
    );

    // Add response interceptor for handling common errors
    this.client.interceptors.response.use(
      (response) => {
        return response.data;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle token expiration
        if (error.response && error.response.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Try to refresh the token
            const newToken = await refreshToken();

            if (newToken) {
              // Update the authorization header
              this.client.defaults.headers.common.Authorization = `Bearer ${newToken}`;
              originalRequest.headers.Authorization = `Bearer ${newToken}`;

              // Retry the original request
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);

            // Redirect to login page if token refresh fails
            window.location.href = '/login';

            return Promise.reject(refreshError);
          }
        }

        // Handle other errors
        return Promise.reject(error);
      }
    );
  }

  /**
   * Initialize the API client
   * @returns {Promise<Object>} Initialization result
   */
  async initServices() {
    try {
      // Discover available endpoints
      await this.discoverEndpoints();

      this.initialized = true;

      return {
        initialized: true,
        endpoints: this.endpoints
      };
    } catch (error) {
      console.warn('API client initialization failed:', error);

      // In development mode, we can continue with mock data
      if (process.env.NODE_ENV === 'development') {
        console.log('Development mode: Using mock API');

        this.initialized = true;

        return {
          initialized: true,
          mock: true
        };
      }

      throw error;
    }
  }

  /**
   * Discover available API endpoints
   * @returns {Promise<Array>} Available endpoints
   */
  async discoverEndpoints() {
    try {
      // Try to get API documentation or service discovery endpoint
      const response = await this.client.get('/');

      if (response && response.endpoints) {
        this.endpoints = response.endpoints;
        return this.endpoints;
      }

      // If no endpoints are returned, use default endpoints
      this.endpoints = [
        { path: '/status', method: 'GET', description: 'Get API status' },
        { path: '/app-data', method: 'GET', description: 'Get app data' },
        { path: '/components', method: 'GET', description: 'Get components' },
        { path: '/templates', method: 'GET', description: 'Get templates' },
        { path: '/projects', method: 'GET', description: 'Get projects' },
        { path: '/users', method: 'GET', description: 'Get users' },
        { path: '/auth', method: 'POST', description: 'Authenticate user' }
      ];

      return this.endpoints;
    } catch (error) {
      console.warn('API endpoint discovery failed:', error);

      // Use default endpoints
      this.endpoints = [
        { path: '/status', method: 'GET', description: 'Get API status' },
        { path: '/app-data', method: 'GET', description: 'Get app data' },
        { path: '/components', method: 'GET', description: 'Get components' },
        { path: '/templates', method: 'GET', description: 'Get templates' },
        { path: '/projects', method: 'GET', description: 'Get projects' },
        { path: '/users', method: 'GET', description: 'Get users' },
        { path: '/auth', method: 'POST', description: 'Authenticate user' }
      ];

      return this.endpoints;
    }
  }

  /**
   * Make a GET request
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async get(url, options = {}) {
    return this.client.get(url, options);
  }

  /**
   * Make a POST request
   * @param {string} url - Request URL
   * @param {Object} data - Request data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async post(url, data = {}, options = {}) {
    return this.client.post(url, data, options);
  }

  /**
   * Make a PUT request
   * @param {string} url - Request URL
   * @param {Object} data - Request data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async put(url, data = {}, options = {}) {
    return this.client.put(url, data, options);
  }

  /**
   * Make a PATCH request
   * @param {string} url - Request URL
   * @param {Object} data - Request data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async patch(url, data = {}, options = {}) {
    return this.client.patch(url, data, options);
  }

  /**
   * Make a DELETE request
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async delete(url, options = {}) {
    return this.client.delete(url, options);
  }

  /**
   * Check if an endpoint exists
   * @param {string} path - Endpoint path
   * @param {string} method - HTTP method
   * @returns {boolean} Whether the endpoint exists
   */
  hasEndpoint(path, method = 'GET') {
    return this.endpoints.some(endpoint =>
      endpoint.path === path && endpoint.method === method
    );
  }

  /**
   * Get endpoint description
   * @param {string} path - Endpoint path
   * @param {string} method - HTTP method
   * @returns {string} Endpoint description
   */
  getEndpointDescription(path, method = 'GET') {
    const endpoint = this.endpoints.find(endpoint =>
      endpoint.path === path && endpoint.method === method
    );

    return endpoint ? endpoint.description : '';
  }
}

// Create singleton instance
const apiClient = new ApiClient();

export default apiClient;
