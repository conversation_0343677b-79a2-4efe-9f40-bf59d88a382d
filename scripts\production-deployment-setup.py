#!/usr/bin/env python3
"""
Production Deployment Setup Script for App Builder 201
Creates production-ready configurations, environment variables, and deployment scripts
"""

import os
import json
import logging
import secrets
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionDeploymentSetup:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.deployment_configs = []

    def create_production_env_files(self):
        """Create production environment files"""
        logger.info("🔧 Creating Production Environment Files...")

        # Generate secure secret key
        secret_key = secrets.token_urlsafe(50)

        # Backend production environment
        backend_env_content = f"""# Production Environment Variables for Django Backend
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# Django Core Settings
DEBUG=False
SECRET_KEY={secret_key}
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,localhost

# Database Configuration (PostgreSQL)
DB_ENGINE=django.db.backends.postgresql
DB_NAME=app_builder_201_prod
DB_USER=app_builder_user
DB_PASSWORD=your_secure_database_password_here
DB_HOST=db
DB_PORT=5432

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://redis:6379/0
CACHE_URL=redis://redis:6379/1

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password_here

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# CORS Settings
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
CORS_ALLOW_CREDENTIALS=True

# Static Files (AWS S3 or similar)
USE_S3=True
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=your-app-static-files
AWS_S3_REGION_NAME=us-east-1

# Monitoring and Logging
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=INFO

# Application Settings
ENVIRONMENT=production
APP_VERSION=1.0.0
"""

        # Frontend production environment
        frontend_env_content = f"""# Production Environment Variables for React Frontend
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# API Configuration
REACT_APP_API_URL=https://api.your-domain.com
REACT_APP_WS_URL=wss://api.your-domain.com

# Application Settings
REACT_APP_ENVIRONMENT=production
REACT_APP_VERSION=1.0.0
REACT_APP_APP_NAME=App Builder 201

# Analytics and Monitoring
REACT_APP_GOOGLE_ANALYTICS_ID=your_ga_id_here
REACT_APP_SENTRY_DSN=your_frontend_sentry_dsn

# Feature Flags
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_OFFLINE_MODE=true
REACT_APP_ENABLE_ANALYTICS=true

# Build Configuration
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false
"""

        # Write environment files
        backend_env_file = self.project_root / "backend" / ".env.production"
        frontend_env_file = self.project_root / "frontend" / ".env.production"

        with open(backend_env_file, 'w') as f:
            f.write(backend_env_content)

        with open(frontend_env_file, 'w') as f:
            f.write(frontend_env_content)

        # Create environment template files
        backend_env_template = self.project_root / "backend" / ".env.production.template"
        frontend_env_template = self.project_root / "frontend" / ".env.production.template"

        # Remove sensitive values for template
        backend_template = backend_env_content.replace(secret_key, "your_secret_key_here")
        backend_template = backend_template.replace("your_secure_database_password_here", "your_database_password")

        with open(backend_env_template, 'w') as f:
            f.write(backend_template)

        with open(frontend_env_template, 'w') as f:
            f.write(frontend_env_content)

        logger.info("✅ Production environment files created")
        self.deployment_configs.append("Production environment files")

    def create_production_docker_compose(self):
        """Create production Docker Compose configuration"""
        logger.info("🐳 Creating Production Docker Compose...")

        docker_compose_prod_content = """version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: app-builder-backend-prod
    restart: unless-stopped
    env_file:
      - ./backend/.env.production
    volumes:
      - static_volume:/usr/src/app/staticfiles
      - media_volume:/usr/src/app/media
    depends_on:
      - db
      - redis
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: app-builder-frontend-prod
    restart: unless-stopped
    env_file:
      - ./frontend/.env.production
    depends_on:
      - backend
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: app-builder-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - static_volume:/usr/src/app/staticfiles:ro
      - media_volume:/usr/src/app/media:ro
    depends_on:
      - backend
      - frontend
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    container_name: app-builder-db-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: app_builder_201_prod
      POSTGRES_USER: app_builder_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U app_builder_user -d app_builder_201_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: app-builder-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  certbot:
    image: certbot/certbot
    container_name: app-builder-certbot
    volumes:
      - ./nginx/ssl:/etc/letsencrypt
      - ./nginx/certbot-webroot:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d your-domain.com -d www.your-domain.com

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local

networks:
  app-network:
    driver: bridge
"""

        docker_compose_prod_file = self.project_root / "docker-compose.prod.yml"

        with open(docker_compose_prod_file, 'w') as f:
            f.write(docker_compose_prod_content)

        logger.info("✅ Production Docker Compose created")
        self.deployment_configs.append("Production Docker Compose")

    def create_production_dockerfiles(self):
        """Create production Dockerfiles"""
        logger.info("📦 Creating Production Dockerfiles...")

        # Backend production Dockerfile
        backend_dockerfile_prod = """# Production Dockerfile for Django Backend
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=app_builder_201.settings

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /usr/src/app

# Install system dependencies
RUN apt-get update \\
    && apt-get install -y --no-install-recommends \\
        postgresql-client \\
        curl \\
        build-essential \\
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . .

# Create necessary directories
RUN mkdir -p staticfiles media logs

# Collect static files
RUN python manage.py collectstatic --noinput

# Change ownership to appuser
RUN chown -R appuser:appuser /usr/src/app

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \\
    CMD curl -f http://localhost:8000/health/ || exit 1

# Expose port
EXPOSE 8000

# Run application
CMD ["daphne", "-b", "0.0.0.0", "-p", "8000", "app_builder_201.asgi:application"]
"""

        # Frontend production Dockerfile
        frontend_dockerfile_prod = """# Production Dockerfile for React Frontend
# Build stage
FROM node:18-alpine AS build

WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Create non-root user
RUN addgroup -g 1001 -S appuser && adduser -S appuser -G appuser

# Copy built application
COPY --from=build /usr/src/app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Change ownership
RUN chown -R appuser:appuser /usr/share/nginx/html
RUN chown -R appuser:appuser /var/cache/nginx
RUN chown -R appuser:appuser /var/log/nginx
RUN chown -R appuser:appuser /etc/nginx/conf.d

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \\
    CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
"""

        # Write Dockerfiles
        backend_dockerfile_prod_file = self.project_root / "backend" / "Dockerfile.prod"
        frontend_dockerfile_prod_file = self.project_root / "frontend" / "Dockerfile.prod"

        with open(backend_dockerfile_prod_file, 'w') as f:
            f.write(backend_dockerfile_prod)

        with open(frontend_dockerfile_prod_file, 'w') as f:
            f.write(frontend_dockerfile_prod)

        logger.info("✅ Production Dockerfiles created")
        self.deployment_configs.append("Production Dockerfiles")

    def create_nginx_configuration(self):
        """Create Nginx configuration for production"""
        logger.info("🌐 Creating Nginx Configuration...")

        # Create nginx directory
        nginx_dir = self.project_root / "nginx"
        nginx_dir.mkdir(exist_ok=True)

        nginx_conf_content = """upstream backend {
    server backend:8000;
}

upstream frontend {
    server frontend:80;
}

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' wss: ws:;" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # API Routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Admin Routes
    location /admin/ {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket Routes
    location /ws/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }

    # Static Files
    location /static/ {
        alias /usr/src/app/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media Files
    location /media/ {
        alias /usr/src/app/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # Frontend Routes
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Handle client-side routing
        try_files $uri $uri/ @fallback;
    }

    location @fallback {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Let's Encrypt verification
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\\n";
        add_header Content-Type text/plain;
    }
}
"""

        nginx_conf_file = nginx_dir / "nginx.conf"

        with open(nginx_conf_file, 'w') as f:
            f.write(nginx_conf_content)

        logger.info("✅ Nginx configuration created")
        self.deployment_configs.append("Nginx configuration")

    def create_deployment_scripts(self):
        """Create deployment scripts"""
        logger.info("🚀 Creating Deployment Scripts...")

        # Create scripts directory
        scripts_dir = self.project_root / "deployment"
        scripts_dir.mkdir(exist_ok=True)

        # Production deployment script
        deploy_script_content = """#!/bin/bash
# Production Deployment Script for App Builder 201

set -e

echo "Starting production deployment..."

# Configuration
COMPOSE_FILE="docker-compose.prod.yml"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
NC='\\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check if required files exist
if [ ! -f "$COMPOSE_FILE" ]; then
    log_error "Docker compose file not found: $COMPOSE_FILE"
    exit 1
fi

if [ ! -f "backend/.env.production" ]; then
    log_error "Backend production environment file not found"
    exit 1
fi

if [ ! -f "frontend/.env.production" ]; then
    log_error "Frontend production environment file not found"
    exit 1
fi

# Check Docker and Docker Compose
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose is not installed"
    exit 1
fi

# Create backup directory
log_info "Creating backup directory..."
mkdir -p "$BACKUP_DIR"

# Backup database
log_info "Backing up database..."
if docker-compose -f "$COMPOSE_FILE" ps db | grep -q "Up"; then
    docker-compose -f "$COMPOSE_FILE" exec -T db pg_dump -U app_builder_user app_builder_201_prod > "$BACKUP_DIR/database.sql"
    log_info "Database backup created: $BACKUP_DIR/database.sql"
else
    log_warn "Database container not running, skipping backup"
fi

# Pull latest images
log_info "Pulling latest images..."
docker-compose -f "$COMPOSE_FILE" pull

# Build new images
log_info "Building application images..."
docker-compose -f "$COMPOSE_FILE" build --no-cache

# Stop services gracefully
log_info "Stopping services..."
docker-compose -f "$COMPOSE_FILE" down --remove-orphans

# Start services
log_info "Starting services..."
docker-compose -f "$COMPOSE_FILE" up -d

# Wait for services to be healthy
log_info "Waiting for services to be healthy..."
sleep 30

# Run database migrations
log_info "Running database migrations..."
docker-compose -f "$COMPOSE_FILE" exec -T backend python manage.py migrate

# Collect static files
log_info "Collecting static files..."
docker-compose -f "$COMPOSE_FILE" exec -T backend python manage.py collectstatic --noinput

# Health check
log_info "Performing health checks..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    log_info "Application is healthy"
else
    log_error "Application health check failed"
    exit 1
fi

# Cleanup old images
log_info "Cleaning up old Docker images..."
docker image prune -f

log_info "Deployment completed successfully!"
log_info "Application is available at: https://your-domain.com"
"""

        # SSL setup script
        ssl_setup_script = """#!/bin/bash
# SSL Certificate Setup Script using Let's Encrypt

set -e

echo "Setting up SSL certificates..."

# Configuration
DOMAIN="your-domain.com"
EMAIL="<EMAIL>"

# Create directories
mkdir -p nginx/ssl
mkdir -p nginx/certbot-webroot

# Initial certificate request
docker-compose -f docker-compose.prod.yml run --rm certbot \\
    certonly --webroot \\
    --webroot-path=/var/www/certbot \\
    --email $EMAIL \\
    --agree-tos \\
    --no-eff-email \\
    -d $DOMAIN \\
    -d www.$DOMAIN

# Set up automatic renewal
echo "0 12 * * * /usr/bin/docker-compose -f /path/to/docker-compose.prod.yml run --rm certbot renew --quiet" | crontab -

echo "SSL certificates configured successfully!"
"""

        # Write deployment scripts
        deploy_script_file = scripts_dir / "deploy.sh"
        ssl_script_file = scripts_dir / "setup-ssl.sh"

        with open(deploy_script_file, 'w') as f:
            f.write(deploy_script_content)

        with open(ssl_script_file, 'w') as f:
            f.write(ssl_setup_script)

        # Make scripts executable
        deploy_script_file.chmod(0o755)
        ssl_script_file.chmod(0o755)

        logger.info("✅ Deployment scripts created")
        self.deployment_configs.append("Deployment scripts")

    def create_monitoring_configuration(self):
        """Create monitoring and logging configuration"""
        logger.info("📊 Creating Monitoring Configuration...")

        # Create monitoring directory
        monitoring_dir = self.project_root / "monitoring"
        monitoring_dir.mkdir(exist_ok=True)

        # Prometheus configuration
        prometheus_config = """global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'app-builder-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'app-builder-frontend'
    static_configs:
      - targets: ['frontend:80']
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['db:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
"""

        # Docker Compose monitoring stack
        monitoring_compose = """version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: app-builder-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: app-builder-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - monitoring

  alertmanager:
    image: prom/alertmanager:latest
    container_name: app-builder-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
"""

        # Write monitoring files
        prometheus_file = monitoring_dir / "prometheus.yml"
        monitoring_compose_file = monitoring_dir / "docker-compose.monitoring.yml"

        with open(prometheus_file, 'w') as f:
            f.write(prometheus_config)

        with open(monitoring_compose_file, 'w') as f:
            f.write(monitoring_compose)

        logger.info("✅ Monitoring configuration created")
        self.deployment_configs.append("Monitoring configuration")

    def create_deployment_documentation(self):
        """Create comprehensive deployment documentation"""
        logger.info("📚 Creating Deployment Documentation...")

        deployment_guide = f"""# Production Deployment Guide - App Builder 201

## Overview
This guide covers the complete production deployment process for App Builder 201.

**Generated on**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Prerequisites

### System Requirements
- Ubuntu 20.04+ or CentOS 8+ (recommended)
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 50GB+ disk space
- Domain name with DNS configured

### Required Services
- PostgreSQL 15+
- Redis 7+
- Nginx (reverse proxy)
- SSL certificates (Let's Encrypt)

## Deployment Steps

### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
```

### 2. Application Deployment
```bash
# Clone repository
git clone https://github.com/your-username/app-builder-201.git
cd app-builder-201

# Configure environment variables
cp backend/.env.production.template backend/.env.production
cp frontend/.env.production.template frontend/.env.production

# Edit environment files with your values
nano backend/.env.production
nano frontend/.env.production

# Run deployment script
./deployment/deploy.sh
```

### 3. SSL Certificate Setup
```bash
# Configure domain in SSL script
nano deployment/setup-ssl.sh

# Run SSL setup
./deployment/setup-ssl.sh
```

### 4. Monitoring Setup (Optional)
```bash
# Start monitoring stack
docker-compose -f monitoring/docker-compose.monitoring.yml up -d

# Access Grafana at http://your-domain:3001
# Default credentials: admin/admin123
```

## Configuration Files

### Environment Variables

#### Backend (.env.production)
- `DEBUG=False` - Disable debug mode
- `SECRET_KEY` - Django secret key (generate new)
- `ALLOWED_HOSTS` - Your domain names
- `DB_*` - Database configuration
- `REDIS_URL` - Redis connection string
- `EMAIL_*` - Email service configuration
- `AWS_*` - S3 storage configuration (optional)

#### Frontend (.env.production)
- `REACT_APP_API_URL` - Backend API URL
- `REACT_APP_WS_URL` - WebSocket URL
- `REACT_APP_ENVIRONMENT=production`

### Security Configuration
- SSL/TLS encryption enabled
- Security headers configured
- CORS properly restricted
- Rate limiting implemented
- Non-root containers

## Maintenance

### Regular Tasks
1. **Database Backups**
   ```bash
   docker-compose -f docker-compose.prod.yml exec db pg_dump -U app_builder_user app_builder_201_prod > backup.sql
   ```

2. **Log Rotation**
   ```bash
   docker-compose -f docker-compose.prod.yml logs --tail=1000 > app.log
   ```

3. **Security Updates**
   ```bash
   docker-compose -f docker-compose.prod.yml pull
   docker-compose -f docker-compose.prod.yml up -d
   ```

4. **SSL Certificate Renewal**
   ```bash
   docker-compose -f docker-compose.prod.yml run --rm certbot renew
   ```

### Monitoring and Alerts
- **Grafana Dashboard**: http://your-domain:3001
- **Prometheus Metrics**: http://your-domain:9090
- **Application Health**: https://your-domain/health

### Backup Strategy
1. **Database**: Daily automated backups
2. **Media Files**: Weekly S3 sync
3. **Configuration**: Version controlled
4. **SSL Certificates**: Automatic renewal

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs [service-name]

# Check container status
docker-compose -f docker-compose.prod.yml ps
```

#### Database Connection Issues
```bash
# Test database connection
docker-compose -f docker-compose.prod.yml exec backend python manage.py dbshell
```

#### SSL Certificate Issues
```bash
# Check certificate status
docker-compose -f docker-compose.prod.yml run --rm certbot certificates

# Renew certificates
docker-compose -f docker-compose.prod.yml run --rm certbot renew --force-renewal
```

### Performance Optimization
1. **Database Tuning**: Optimize PostgreSQL configuration
2. **Caching**: Configure Redis caching
3. **CDN**: Use CloudFlare or AWS CloudFront
4. **Monitoring**: Set up alerts for performance metrics

## Security Checklist

- [ ] SSL certificates configured and auto-renewing
- [ ] Security headers enabled in Nginx
- [ ] Database credentials secured
- [ ] API rate limiting configured
- [ ] Container security (non-root users)
- [ ] Regular security updates scheduled
- [ ] Backup and disaster recovery tested
- [ ] Monitoring and alerting configured

## Support

### Documentation
- [Django Deployment Guide](https://docs.djangoproject.com/en/stable/howto/deployment/)
- [React Production Build](https://create-react-app.dev/docs/production-build/)
- [Docker Compose Production](https://docs.docker.com/compose/production/)

### Monitoring
- Application logs: `docker-compose logs`
- System metrics: Grafana dashboard
- Error tracking: Sentry (if configured)

### Emergency Contacts
- DevOps Team: <EMAIL>
- Security Team: <EMAIL>
- On-call Engineer: +1-xxx-xxx-xxxx

---
*This deployment guide is part of the App Builder 201 production setup.*
"""

        deployment_guide_file = self.project_root / "DEPLOYMENT_GUIDE.md"

        with open(deployment_guide_file, 'w') as f:
            f.write(deployment_guide)

        logger.info("✅ Deployment documentation created")
        self.deployment_configs.append("Deployment documentation")

    def run_production_setup(self):
        """Run complete production deployment setup"""
        logger.info("🚀 Starting Production Deployment Setup...")
        logger.info("=" * 60)

        # Create all production configurations
        self.create_production_env_files()
        self.create_production_docker_compose()
        self.create_production_dockerfiles()
        self.create_nginx_configuration()
        self.create_deployment_scripts()
        self.create_monitoring_configuration()
        self.create_deployment_documentation()

        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("🚀 PRODUCTION DEPLOYMENT SETUP COMPLETE")
        logger.info("=" * 60)
        logger.info(f"✅ Created {len(self.deployment_configs)} configuration sets:")
        for config in self.deployment_configs:
            logger.info(f"  - {config}")

        logger.info("\n📋 Next Steps:")
        logger.info("  1. Review and customize environment variables")
        logger.info("  2. Update domain names in configuration files")
        logger.info("  3. Configure DNS records for your domain")
        logger.info("  4. Run deployment script: ./deployment/deploy.sh")
        logger.info("  5. Set up SSL certificates: ./deployment/setup-ssl.sh")
        logger.info("  6. Configure monitoring (optional)")

        logger.info("\n📄 Documentation:")
        logger.info("  - Read DEPLOYMENT_GUIDE.md for detailed instructions")
        logger.info("  - Review security configurations")
        logger.info("  - Test deployment in staging environment first")

        logger.info("\n🎉 Production deployment setup completed!")

def main():
    """Main production setup function"""
    setup = ProductionDeploymentSetup()
    setup.run_production_setup()

if __name__ == "__main__":
    main()