import React, { useState, useEffect } from 'react';
import { Badge, Avatar, Tooltip, Space, Button, Popover, Typography, Divider } from 'antd';
import { 
  UserOutlined, 
  TeamOutlined, 
  WifiOutlined,
  MessageOutlined,
  SettingOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import CollaborationPanel from './CollaborationPanel';
import { CollaboratorAvatar } from './UserPresence';

const { Text } = Typography;

/**
 * Collaboration Indicator
 * 
 * Shows real-time collaboration status, connected users, and provides
 * access to collaboration features like chat and shared editing.
 */

const IndicatorContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: ${props => props.connected ? '#f6ffed' : '#fff2f0'};
  border: 1px solid ${props => props.connected ? '#b7eb8f' : '#ffb3b3'};
  border-radius: 6px;
  transition: all 0.2s;
  
  &:hover {
    background: ${props => props.connected ? '#f0f9ff' : '#fef2f2'};
  }
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.connected ? '#52c41a' : '#ff4d4f'};
  animation: ${props => props.connected ? 'pulse 2s infinite' : 'none'};
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const CollaboratorsList = styled.div`
  display: flex;
  align-items: center;
  gap: -4px; /* Overlap avatars slightly */
  
  .ant-avatar {
    border: 2px solid white;
    cursor: pointer;
    transition: transform 0.2s;
    
    &:hover {
      transform: scale(1.1);
      z-index: 10;
    }
  }
`;

const CollaborationIndicator = ({
  connected = false,
  collaborators = [],
  activeUsers = [],
  onOpenChat,
  onOpenPanel,
  compact = false,
  showUserCount = true,
  maxVisibleUsers = 3
}) => {
  const [panelVisible, setPanelVisible] = useState(false);
  const [lastActivity, setLastActivity] = useState(Date.now());

  // Combine collaborators and active users
  const allUsers = React.useMemo(() => {
    const userMap = new Map();
    
    // Add collaborators
    collaborators.forEach(user => {
      userMap.set(user.id || user.username, {
        ...user,
        type: 'collaborator',
        isActive: true
      });
    });
    
    // Add active users
    activeUsers.forEach(user => {
      const existing = userMap.get(user.id || user.username);
      userMap.set(user.id || user.username, {
        ...existing,
        ...user,
        type: existing ? 'collaborator' : 'viewer',
        isActive: true
      });
    });
    
    return Array.from(userMap.values());
  }, [collaborators, activeUsers]);

  // Update last activity when users change
  useEffect(() => {
    if (allUsers.length > 0) {
      setLastActivity(Date.now());
    }
  }, [allUsers]);

  const getStatusText = () => {
    if (!connected) return 'Disconnected';
    if (allUsers.length === 0) return 'Connected';
    if (allUsers.length === 1) return '1 user online';
    return `${allUsers.length} users online`;
  };

  const getStatusColor = () => {
    if (!connected) return 'error';
    if (allUsers.length === 0) return 'default';
    return 'success';
  };

  const renderUserPopover = (user) => (
    <div style={{ maxWidth: 200 }}>
      <Space direction="vertical" size="small">
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <CollaboratorAvatar
            user={user}
            color={user.color || '#1890ff'}
            status={user.status || 'active'}
            size="small"
          />
          <div>
            <Text strong>{user.username || user.name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {user.type === 'collaborator' ? 'Collaborator' : 'Viewer'}
            </Text>
          </div>
        </div>
        
        {user.lastSeen && (
          <Text type="secondary" style={{ fontSize: 11 }}>
            Last seen: {new Date(user.lastSeen).toLocaleTimeString()}
          </Text>
        )}
        
        {user.currentAction && (
          <Text style={{ fontSize: 11, color: '#1890ff' }}>
            {user.currentAction}
          </Text>
        )}
      </Space>
    </div>
  );

  const renderCollaboratorsList = () => {
    const visibleUsers = allUsers.slice(0, maxVisibleUsers);
    const hiddenCount = Math.max(0, allUsers.length - maxVisibleUsers);

    return (
      <CollaboratorsList>
        {visibleUsers.map((user, index) => (
          <Popover
            key={user.id || user.username}
            content={renderUserPopover(user)}
            placement="bottom"
            trigger="hover"
          >
            <CollaboratorAvatar
              user={user}
              color={user.color || '#1890ff'}
              status={user.status || 'active'}
              size="small"
              style={{ marginLeft: index > 0 ? -4 : 0 }}
            />
          </Popover>
        ))}
        
        {hiddenCount > 0 && (
          <Tooltip title={`${hiddenCount} more user${hiddenCount > 1 ? 's' : ''}`}>
            <Avatar
              size="small"
              style={{
                backgroundColor: '#f0f0f0',
                color: '#666',
                marginLeft: -4,
                fontSize: 11
              }}
            >
              +{hiddenCount}
            </Avatar>
          </Tooltip>
        )}
      </CollaboratorsList>
    );
  };

  const renderStatusPopover = () => (
    <div style={{ minWidth: 200 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <StatusDot connected={connected} />
          <Text strong>{getStatusText()}</Text>
        </div>
        
        <Divider style={{ margin: '8px 0' }} />
        
        {allUsers.length > 0 ? (
          <div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              Active Users:
            </Text>
            <div style={{ marginTop: 8 }}>
              {allUsers.slice(0, 5).map(user => (
                <div key={user.id || user.username} style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 8, 
                  marginBottom: 4 
                }}>
                  <CollaboratorAvatar
                    user={user}
                    color={user.color || '#1890ff'}
                    status={user.status || 'active'}
                    size="small"
                  />
                  <Text style={{ fontSize: 12 }}>
                    {user.username || user.name}
                  </Text>
                </div>
              ))}
              {allUsers.length > 5 && (
                <Text type="secondary" style={{ fontSize: 11 }}>
                  and {allUsers.length - 5} more...
                </Text>
              )}
            </div>
          </div>
        ) : (
          <Text type="secondary" style={{ fontSize: 12 }}>
            No other users online
          </Text>
        )}
        
        <Divider style={{ margin: '8px 0' }} />
        
        <Space>
          <Button
            size="small"
            icon={<TeamOutlined />}
            onClick={() => setPanelVisible(true)}
          >
            Open Panel
          </Button>
          
          {onOpenChat && (
            <Button
              size="small"
              icon={<MessageOutlined />}
              onClick={onOpenChat}
            >
              Chat
            </Button>
          )}
        </Space>
      </Space>
    </div>
  );

  // Compact mode for minimal space
  if (compact) {
    return (
      <>
        <Popover
          content={renderStatusPopover()}
          placement="bottomRight"
          trigger="hover"
        >
          <IndicatorContainer connected={connected}>
            <StatusDot connected={connected} />
            {allUsers.length > 0 && (
              <Badge count={allUsers.length} size="small">
                <TeamOutlined style={{ fontSize: 14 }} />
              </Badge>
            )}
          </IndicatorContainer>
        </Popover>

        <CollaborationPanel
          visible={panelVisible}
          onClose={() => setPanelVisible(false)}
          users={allUsers}
          connected={connected}
        />
      </>
    );
  }

  // Full mode with user avatars
  return (
    <>
      <Space align="center">
        <Popover
          content={renderStatusPopover()}
          placement="bottom"
          trigger="hover"
        >
          <IndicatorContainer connected={connected}>
            <StatusDot connected={connected} />
            <WifiOutlined style={{ fontSize: 12 }} />
            {showUserCount && (
              <Text style={{ fontSize: 11, color: connected ? '#52c41a' : '#ff4d4f' }}>
                {connected ? 'Online' : 'Offline'}
              </Text>
            )}
          </IndicatorContainer>
        </Popover>

        {allUsers.length > 0 && renderCollaboratorsList()}

        <Button
          type="text"
          size="small"
          icon={<SettingOutlined />}
          onClick={() => setPanelVisible(true)}
          style={{ opacity: 0.7 }}
        />
      </Space>

      <CollaborationPanel
        visible={panelVisible}
        onClose={() => setPanelVisible(false)}
        users={allUsers}
        connected={connected}
      />
    </>
  );
};

export default CollaborationIndicator;
