/**
 * Theme Manager Component Styles
 */

:root {
  --primary-color: #1890ff;
  --secondary-color: #52c41a;
  --background-color: #ffffff;
  --text-color: #000000;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Additional variables */
  --border-radius: 4px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Theme-specific styles */
[data-theme="default"] {
  --primary-color: #1890ff;
  --secondary-color: #52c41a;
  --background-color: #ffffff;
  --text-color: #000000;
}

[data-theme="dark"] {
  --primary-color: #1890ff;
  --secondary-color: #52c41a;
  --background-color: #141414;
  --text-color: #ffffff;
}

[data-theme="blue"] {
  --primary-color: #0050b3;
  --secondary-color: #52c41a;
  --background-color: #e6f7ff;
  --text-color: #000000;
}

[data-theme="high-contrast"] {
  --primary-color: #ffff00;
  --secondary-color: #00ff00;
  --background-color: #000000;
  --text-color: #ffffff;
}

/* Theme Manager Component */
.theme-manager {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  max-width: 800px;
  margin: 0 auto;
}

.theme-manager h2 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

/* Theme Selector */
.theme-selector {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.theme-selector label {
  margin-right: var(--spacing-md);
  font-weight: 500;
}

.theme-select {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--primary-color);
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: var(--font-family);
  min-width: 200px;
}

/* Theme Editor */
.theme-editor {
  margin-top: var(--spacing-lg);
}

.theme-editor-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid #d9d9d9;
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: var(--font-family);
}

.form-group input[type="color"] {
  height: 40px;
  padding: 2px;
}

/* Theme Preview */
.theme-preview {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  border: 1px solid #d9d9d9;
  border-radius: var(--border-radius);
}

.preview-title {
  color: var(--primary-color);
  margin-top: 0;
}

.preview-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-family: var(--font-family);
  margin-right: var(--spacing-sm);
}

.preview-button.secondary {
  background-color: var(--secondary-color);
}

.preview-text {
  color: var(--text-color);
  margin-top: var(--spacing-md);
}

/* Theme List */
.theme-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.theme-card {
  border: 1px solid #d9d9d9;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  position: relative;
}

.theme-card.active {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.theme-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.theme-card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.theme-card-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.theme-card-action {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--primary-color);
  padding: var(--spacing-xs);
}

.theme-card-colors {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.color-preview {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #d9d9d9;
}

/* Theme Editor Modal */
.theme-editor-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.theme-editor-modal {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.theme-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid #d9d9d9;
}

.theme-editor-header h2 {
  margin: 0;
  color: var(--primary-color);
}

.theme-editor-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-color);
}

.theme-editor-content {
  padding: var(--spacing-md);
}

.theme-editor-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

/* Responsive styles */
@media (max-width: 768px) {
  .theme-editor-form {
    grid-template-columns: 1fr;
  }

  .theme-list {
    grid-template-columns: 1fr;
  }
}