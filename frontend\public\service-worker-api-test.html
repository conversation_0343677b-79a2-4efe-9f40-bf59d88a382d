<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker API Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Service Worker API Fix Test</h1>
        <p>This page tests the service worker fix for API request handling.</p>

        <div class="test-section info">
            <h3>📋 Test Overview</h3>
            <p>This test verifies that:</p>
            <ul>
                <li>Service worker is registered and active</li>
                <li>API requests are NOT intercepted by the service worker</li>
                <li>API requests go through the webpack proxy correctly</li>
                <li>Backend API endpoints are accessible</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🚀 Run Tests</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="testServiceWorkerRegistration()">Test SW Registration</button>
            <button onclick="testApiRequests()">Test API Requests</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>📝 Test Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let testResults = {};
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function updateResults() {
            const resultsElement = document.getElementById('results');
            let html = '';
            
            for (const [test, result] of Object.entries(testResults)) {
                const status = result.success ? '✅ PASS' : '❌ FAIL';
                const className = result.success ? 'success' : 'error';
                html += `<div class="test-section ${className}">
                    <strong>${test}:</strong> ${status}
                    <br><small>${result.message}</small>
                </div>`;
            }
            
            resultsElement.innerHTML = html;
        }

        async function testServiceWorkerRegistration() {
            log('🔍 Testing Service Worker Registration...');
            
            try {
                if (!('serviceWorker' in navigator)) {
                    testResults['Service Worker Support'] = {
                        success: false,
                        message: 'Service Worker not supported in this browser'
                    };
                    log('❌ Service Worker not supported');
                    updateResults();
                    return false;
                }

                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    testResults['Service Worker Registration'] = {
                        success: true,
                        message: `Registered with scope: ${registration.scope}`
                    };
                    log(`✅ Service Worker registered: ${registration.scope}`);
                    
                    // Check if service worker is active
                    if (registration.active) {
                        log(`✅ Service Worker is active: ${registration.active.scriptURL}`);
                    } else {
                        log('⚠️ Service Worker is not active yet');
                    }
                    
                    return true;
                } else {
                    testResults['Service Worker Registration'] = {
                        success: false,
                        message: 'Service Worker not registered'
                    };
                    log('❌ Service Worker not registered');
                    updateResults();
                    return false;
                }
            } catch (error) {
                testResults['Service Worker Registration'] = {
                    success: false,
                    message: `Error: ${error.message}`
                };
                log(`❌ Service Worker registration error: ${error.message}`);
                updateResults();
                return false;
            }
        }

        async function testApiRequests() {
            log('🌐 Testing API Requests...');
            
            const apiEndpoints = [
                '/api/status/',
                '/api/health/',
                '/api/'
            ];

            let allPassed = true;

            for (const endpoint of apiEndpoints) {
                try {
                    log(`📡 Testing ${endpoint}...`);
                    
                    const response = await fetch(endpoint, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    log(`📡 ${endpoint} - Status: ${response.status}`);
                    
                    if (response.ok) {
                        const data = await response.text();
                        log(`✅ ${endpoint} - Success: ${data.substring(0, 100)}...`);
                        testResults[`API ${endpoint}`] = {
                            success: true,
                            message: `Status: ${response.status}`
                        };
                    } else {
                        log(`⚠️ ${endpoint} - Non-200 status: ${response.status}`);
                        testResults[`API ${endpoint}`] = {
                            success: false,
                            message: `Status: ${response.status}`
                        };
                        allPassed = false;
                    }
                } catch (error) {
                    log(`❌ ${endpoint} - Error: ${error.message}`);
                    testResults[`API ${endpoint}`] = {
                        success: false,
                        message: `Error: ${error.message}`
                    };
                    allPassed = false;
                }
            }

            updateResults();
            return allPassed;
        }

        async function runAllTests() {
            log('🚀 Starting all tests...');
            testResults = {};
            
            const swTest = await testServiceWorkerRegistration();
            const apiTest = await testApiRequests();
            
            log('\n📊 Test Summary:');
            log(`Service Worker: ${swTest ? '✅ PASS' : '❌ FAIL'}`);
            log(`API Requests: ${apiTest ? '✅ PASS' : '❌ FAIL'}`);
            
            if (swTest && apiTest) {
                log('\n🎉 All tests passed! Service Worker fix is working correctly.');
            } else {
                log('\n⚠️ Some tests failed. Please check the service worker configuration.');
            }
            
            updateResults();
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            log('🔧 Service Worker API Fix Test Page Loaded');
            log('Click "Run All Tests" to start testing...');
        });
    </script>
</body>
</html>
