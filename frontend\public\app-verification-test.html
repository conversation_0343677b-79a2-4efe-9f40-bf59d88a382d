<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Builder - Complete Verification Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .test-section:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .success { 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
        }
        .error { 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #dc3545;
        }
        .warning { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffc107;
        }
        .info { 
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-color: #17a2b8;
        }
        .running {
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            border-color: #6c757d;
        }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 13px;
            line-height: 1.4;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-running { background-color: #6c757d; animation: pulse 1.5s infinite; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            width: 0%;
            transition: width 0.3s ease;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }
        .metric-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 App Builder - Complete Verification Test</h1>
            <p>Comprehensive testing of service worker fixes, API communication, and application functionality</p>
        </div>

        <div class="test-section info">
            <h3>📋 Test Overview</h3>
            <p>This comprehensive test verifies:</p>
            <ul>
                <li>✅ Service worker registration and proper API handling</li>
                <li>✅ Backend API endpoints accessibility and responses</li>
                <li>✅ Frontend proxy configuration and routing</li>
                <li>✅ Authentication and CSRF token handling</li>
                <li>✅ App Builder application functionality</li>
                <li>✅ Performance and browser compatibility</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <button onclick="runCompleteVerification()" id="runAllBtn">🚀 Run Complete Verification</button>
            <button onclick="testServiceWorker()" id="swBtn">🔧 Test Service Worker</button>
            <button onclick="testAPIEndpoints()" id="apiBtn">🌐 Test API Endpoints</button>
            <button onclick="testAppBuilder()" id="appBtn">🏗️ Test App Builder</button>
            <button onclick="clearResults()" id="clearBtn">🧹 Clear Results</button>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="test-grid">
            <div class="metric">
                <div class="metric-value" id="testsPassedCount">0</div>
                <div class="metric-label">Tests Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="testsFailedCount">0</div>
                <div class="metric-label">Tests Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="apiResponseTime">-</div>
                <div class="metric-label">Avg API Response (ms)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="overallStatus">Ready</div>
                <div class="metric-label">Overall Status</div>
            </div>
        </div>

        <div class="test-section" id="resultsSection">
            <h3>📊 Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>📝 Detailed Log</h3>
            <div id="log" class="log">Ready to run tests...\n</div>
        </div>
    </div>

    <script src="/final-api-verification-test.js"></script>
    <script>
        let testResults = {};
        let testsPassed = 0;
        let testsFailed = 0;
        let apiResponseTimes = [];
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        function updateMetrics() {
            document.getElementById('testsPassedCount').textContent = testsPassed;
            document.getElementById('testsFailedCount').textContent = testsFailed;
            
            if (apiResponseTimes.length > 0) {
                const avgTime = apiResponseTimes.reduce((a, b) => a + b, 0) / apiResponseTimes.length;
                document.getElementById('apiResponseTime').textContent = Math.round(avgTime);
            }
            
            const totalTests = testsPassed + testsFailed;
            if (totalTests > 0) {
                const successRate = (testsPassed / totalTests * 100).toFixed(1);
                document.getElementById('overallStatus').textContent = `${successRate}% Success`;
            }
        }

        function addResult(testName, success, message, details = '') {
            if (success) {
                testsPassed++;
            } else {
                testsFailed++;
            }
            
            const resultsElement = document.getElementById('results');
            const statusClass = success ? 'success' : 'error';
            const statusIcon = success ? '✅' : '❌';
            const statusIndicator = success ? 'status-success' : 'status-error';
            
            const resultHtml = `
                <div class="test-section ${statusClass}">
                    <h4><span class="status-indicator ${statusIndicator}"></span>${statusIcon} ${testName}</h4>
                    <p><strong>Result:</strong> ${message}</p>
                    ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
                </div>
            `;
            
            resultsElement.innerHTML += resultHtml;
            updateMetrics();
        }

        async function testServiceWorker() {
            log('🔧 Testing Service Worker...');
            
            try {
                if (!('serviceWorker' in navigator)) {
                    addResult('Service Worker Support', false, 'Service Worker not supported in this browser');
                    return false;
                }

                const registration = await navigator.serviceWorker.getRegistration();
                if (registration && registration.active) {
                    addResult('Service Worker Registration', true, 'Service Worker is registered and active', `Scope: ${registration.scope}`);
                    log(`✅ Service Worker active: ${registration.active.scriptURL}`);
                    return true;
                } else {
                    addResult('Service Worker Registration', false, 'Service Worker not active');
                    return false;
                }
            } catch (error) {
                addResult('Service Worker Registration', false, `Error: ${error.message}`);
                return false;
            }
        }

        async function testAPIEndpoints() {
            log('🌐 Testing API Endpoints...');
            
            const endpoints = [
                { url: '/api/status/', name: 'Status API' },
                { url: '/api/health/', name: 'Health API' },
                { url: '/api/csrf-token/', name: 'CSRF Token API' }
            ];

            let allPassed = true;

            for (const endpoint of endpoints) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(endpoint.url, {
                        method: 'GET',
                        headers: { 'Content-Type': 'application/json' }
                    });
                    const endTime = performance.now();
                    const responseTime = endTime - startTime;
                    apiResponseTimes.push(responseTime);

                    if (response.ok) {
                        const data = await response.json();
                        addResult(endpoint.name, true, `Status: ${response.status}`, `Response time: ${Math.round(responseTime)}ms`);
                        log(`✅ ${endpoint.name} - Success (${Math.round(responseTime)}ms)`);
                    } else {
                        addResult(endpoint.name, false, `Status: ${response.status}`);
                        allPassed = false;
                    }
                } catch (error) {
                    addResult(endpoint.name, false, `Error: ${error.message}`);
                    allPassed = false;
                }
            }

            return allPassed;
        }

        async function testAppBuilder() {
            log('🏗️ Testing App Builder Application...');
            
            try {
                // Test if the main app is accessible
                const response = await fetch('/', { method: 'GET' });
                if (response.ok) {
                    addResult('App Builder Access', true, 'Main application is accessible');
                } else {
                    addResult('App Builder Access', false, `Status: ${response.status}`);
                    return false;
                }

                // Test if React components are loaded
                if (typeof React !== 'undefined') {
                    addResult('React Framework', true, 'React is loaded and available');
                } else {
                    addResult('React Framework', false, 'React not detected');
                }

                return true;
            } catch (error) {
                addResult('App Builder Application', false, `Error: ${error.message}`);
                return false;
            }
        }

        async function runCompleteVerification() {
            log('🚀 Starting Complete Verification Test...');
            
            // Reset counters
            testsPassed = 0;
            testsFailed = 0;
            apiResponseTimes = [];
            document.getElementById('results').innerHTML = '';
            
            // Disable buttons during test
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => btn.disabled = true);
            
            try {
                updateProgress(10);
                const swResult = await testServiceWorker();
                
                updateProgress(40);
                const apiResult = await testAPIEndpoints();
                
                updateProgress(70);
                const appResult = await testAppBuilder();
                
                updateProgress(100);
                
                log('\n📊 Complete Verification Results:');
                log(`Service Worker: ${swResult ? '✅ PASS' : '❌ FAIL'}`);
                log(`API Endpoints: ${apiResult ? '✅ PASS' : '❌ FAIL'}`);
                log(`App Builder: ${appResult ? '✅ PASS' : '❌ FAIL'}`);
                
                const overallSuccess = swResult && apiResult && appResult;
                
                if (overallSuccess) {
                    log('\n🎉 ALL TESTS PASSED! Your App Builder application is working correctly.');
                    addResult('Overall Verification', true, 'All systems operational', 'Service worker, API, and application are all functioning correctly');
                } else {
                    log('\n⚠️ Some tests failed. Please check the results above.');
                    addResult('Overall Verification', false, 'Some systems need attention', 'Check individual test results for details');
                }
                
            } finally {
                // Re-enable buttons
                buttons.forEach(btn => btn.disabled = false);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('log').textContent = 'Ready to run tests...\n';
            testsPassed = 0;
            testsFailed = 0;
            apiResponseTimes = [];
            updateProgress(0);
            updateMetrics();
        }

        // Auto-run a quick test when page loads
        window.addEventListener('load', () => {
            log('🔧 App Builder Verification Test loaded');
            log('Click "Run Complete Verification" to test all systems');
        });
    </script>
</body>
</html>
