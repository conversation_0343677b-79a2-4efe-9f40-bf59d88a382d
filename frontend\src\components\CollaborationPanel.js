import React, { useState, useEffect, useRef } from 'react';
import useImprovedWebSocket from '../hooks/useImprovedWebSocket';
import { getWebSocketUrl } from '../config/env';

/**
 * CollaborationPanel Component
 * 
 * A real-time collaboration panel with:
 * - User presence indicators
 * - Shared editing capabilities
 * - Real-time notifications
 */
const CollaborationPanel = ({ appId, username = 'Anonymous' }) => {
  // State
  const [users, setUsers] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [sharedContent, setSharedContent] = useState('');
  const [editingContent, setEditingContent] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(null);
  const [userColors, setUserColors] = useState({});
  const contentRef = useRef(null);
  const lastCursorUpdateRef = useRef(0);
  const debounceTimeoutRef = useRef(null);

  // Generate a random color for a user
  const generateUserColor = (username) => {
    // List of distinct colors
    const colors = [
      '#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#712fd1',
      '#eb2f96', '#13c2c2', '#1890ff', '#52c41a', '#fa8c16'
    ];
    
    // Use a hash function to consistently assign the same color to the same username
    const hash = username.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  // Use our improved WebSocket hook
  const {
    connected,
    send,
    messages
  } = useImprovedWebSocket({
    endpoint: 'collaboration',
    autoConnect: true,
    debug: true,
    heartbeatInterval: 30000
  });

  // Initialize user color
  useEffect(() => {
    if (username && !userColors[username]) {
      setUserColors(prev => ({
        ...prev,
        [username]: generateUserColor(username)
      }));
    }
  }, [username]);

  // Process incoming messages
  useEffect(() => {
    if (messages.length > 0) {
      const latestMessage = messages[messages.length - 1];
      
      switch (latestMessage.type) {
        case 'user_joined':
          handleUserJoined(latestMessage);
          break;
        case 'user_left':
          handleUserLeft(latestMessage);
          break;
        case 'content_update':
          handleContentUpdate(latestMessage);
          break;
        case 'cursor_update':
          handleCursorUpdate(latestMessage);
          break;
        case 'notification':
          handleNotification(latestMessage);
          break;
        case 'users_list':
          handleUsersList(latestMessage);
          break;
        default:
          // Ignore other message types
          break;
      }
    }
  }, [messages]);

  // Send join message when connected
  useEffect(() => {
    if (connected) {
      send({
        type: 'join',
        username,
        appId,
        timestamp: Date.now()
      });
      
      // Request current content and users list
      send({
        type: 'get_content',
        appId,
        timestamp: Date.now()
      });
      
      send({
        type: 'get_users',
        appId,
        timestamp: Date.now()
      });
    }
    
    // Send leave message when unmounting
    return () => {
      if (connected) {
        send({
          type: 'leave',
          username,
          appId,
          timestamp: Date.now()
        });
      }
    };
  }, [connected, username, appId]);

  // Handle user joined
  const handleUserJoined = (message) => {
    const { username: joinedUser } = message;
    
    // Add user to list if not already present
    setUsers(prev => {
      if (prev.some(user => user.username === joinedUser)) {
        return prev;
      }
      
      // Generate color for new user
      if (!userColors[joinedUser]) {
        setUserColors(prev => ({
          ...prev,
          [joinedUser]: generateUserColor(joinedUser)
        }));
      }
      
      return [...prev, { 
        username: joinedUser, 
        joinedAt: Date.now(),
        lastActive: Date.now(),
        cursor: null
      }];
    });
    
    // Add notification
    addNotification(`${joinedUser} joined the session`);
  };

  // Handle user left
  const handleUserLeft = (message) => {
    const { username: leftUser } = message;
    
    // Remove user from list
    setUsers(prev => prev.filter(user => user.username !== leftUser));
    
    // Add notification
    addNotification(`${leftUser} left the session`);
  };

  // Handle content update
  const handleContentUpdate = (message) => {
    const { content, username: updatedBy } = message;
    
    // Update content
    setSharedContent(content);
    setEditingContent(content);
    
    // Add notification if updated by someone else
    if (updatedBy !== username) {
      addNotification(`${updatedBy} updated the content`);
    }
    
    // Update user's last active timestamp
    updateUserActivity(updatedBy);
  };

  // Handle cursor update
  const handleCursorUpdate = (message) => {
    const { username: cursorUser, position } = message;
    
    // Update user's cursor position
    setUsers(prev => prev.map(user => {
      if (user.username === cursorUser) {
        return { ...user, cursor: position, lastActive: Date.now() };
      }
      return user;
    }));
  };

  // Handle notification
  const handleNotification = (message) => {
    addNotification(message.message);
  };

  // Handle users list
  const handleUsersList = (message) => {
    const { users: usersList } = message;
    
    // Update users list
    setUsers(usersList.map(user => ({
      ...user,
      joinedAt: user.joinedAt || Date.now(),
      lastActive: user.lastActive || Date.now()
    })));
    
    // Generate colors for all users
    const newColors = { ...userColors };
    usersList.forEach(user => {
      if (!newColors[user.username]) {
        newColors[user.username] = generateUserColor(user.username);
      }
    });
    setUserColors(newColors);
  };

  // Add notification
  const addNotification = (message) => {
    setNotifications(prev => [
      ...prev, 
      { 
        id: Date.now(), 
        message, 
        timestamp: Date.now() 
      }
    ].slice(-5)); // Keep only the last 5 notifications
  };

  // Update user activity
  const updateUserActivity = (activeUser) => {
    setUsers(prev => prev.map(user => {
      if (user.username === activeUser) {
        return { ...user, lastActive: Date.now() };
      }
      return user;
    }));
  };

  // Handle content edit
  const handleContentEdit = (e) => {
    setEditingContent(e.target.value);
    
    // Debounce content updates
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    debounceTimeoutRef.current = setTimeout(() => {
      send({
        type: 'content_update',
        username,
        appId,
        content: e.target.value,
        timestamp: Date.now()
      });
    }, 500);
    
    // Update cursor position
    updateCursorPosition(e.target);
  };

  // Update cursor position
  const updateCursorPosition = (element) => {
    const now = Date.now();
    
    // Throttle cursor updates to max once per 200ms
    if (now - lastCursorUpdateRef.current > 200) {
      lastCursorUpdateRef.current = now;
      
      const position = element.selectionStart;
      setCursorPosition(position);
      
      send({
        type: 'cursor_update',
        username,
        appId,
        position,
        timestamp: now
      });
    }
  };

  // Start editing
  const handleStartEditing = () => {
    setIsEditing(true);
    setEditingContent(sharedContent);
  };

  // Save changes
  const handleSaveChanges = () => {
    setIsEditing(false);
    setSharedContent(editingContent);
    
    send({
      type: 'content_update',
      username,
      appId,
      content: editingContent,
      timestamp: Date.now()
    });
    
    addNotification('Changes saved');
  };

  // Cancel editing
  const handleCancelEditing = () => {
    setIsEditing(false);
    setEditingContent(sharedContent);
  };

  // Format time
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleTimeString();
  };

  // Get user status
  const getUserStatus = (user) => {
    const now = Date.now();
    const inactiveThreshold = 60000; // 1 minute
    
    if (now - user.lastActive < inactiveThreshold) {
      return 'active';
    }
    
    return 'inactive';
  };

  // Render component
  return (
    <div style={styles.container}>
      <h2 style={styles.title}>Collaboration Panel</h2>
      
      {/* Connection Status */}
      <div style={styles.statusContainer}>
        <div style={styles.statusIndicator}>
          <div 
            style={{
              ...styles.statusDot,
              backgroundColor: connected ? '#52c41a' : '#ff4d4f'
            }}
          />
          <span style={styles.statusText}>
            {connected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        <div style={styles.userInfo}>
          <span style={styles.username}>
            {username}
          </span>
          <div 
            style={{
              ...styles.userAvatar,
              backgroundColor: userColors[username] || '#1890ff'
            }}
          >
            {username.charAt(0).toUpperCase()}
          </div>
        </div>
      </div>
      
      {/* Main Content Area */}
      <div style={styles.contentContainer}>
        {/* Users Panel */}
        <div style={styles.usersPanel}>
          <h3 style={styles.panelTitle}>Users</h3>
          
          <div style={styles.usersList}>
            {users.length === 0 ? (
              <p style={styles.emptyMessage}>No users connected</p>
            ) : (
              users.map(user => (
                <div 
                  key={user.username} 
                  style={{
                    ...styles.userItem,
                    borderLeft: `4px solid ${userColors[user.username] || '#1890ff'}`
                  }}
                >
                  <div style={styles.userItemHeader}>
                    <span style={styles.userItemName}>{user.username}</span>
                    <div 
                      style={{
                        ...styles.userStatusDot,
                        backgroundColor: getUserStatus(user) === 'active' ? '#52c41a' : '#d9d9d9'
                      }}
                    />
                  </div>
                  
                  <div style={styles.userItemDetails}>
                    <span style={styles.userItemDetail}>
                      Joined: {formatTime(user.joinedAt)}
                    </span>
                    <span style={styles.userItemDetail}>
                      Last active: {formatTime(user.lastActive)}
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        
        {/* Shared Content */}
        <div style={styles.sharedContent}>
          <h3 style={styles.panelTitle}>Shared Content</h3>
          
          {isEditing ? (
            <div style={styles.editingContainer}>
              <textarea
                ref={contentRef}
                style={styles.contentTextarea}
                value={editingContent}
                onChange={handleContentEdit}
                onSelect={(e) => updateCursorPosition(e.target)}
              />
              
              <div style={styles.editingActions}>
                <button 
                  style={{...styles.button, ...styles.saveButton}} 
                  onClick={handleSaveChanges}
                >
                  Save Changes
                </button>
                
                <button 
                  style={{...styles.button, ...styles.cancelButton}} 
                  onClick={handleCancelEditing}
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div style={styles.viewingContainer}>
              <pre style={styles.contentDisplay}>
                {sharedContent || 'No content yet. Click "Edit" to add content.'}
              </pre>
              
              <button 
                style={styles.button} 
                onClick={handleStartEditing}
              >
                Edit
              </button>
            </div>
          )}
          
          {/* Cursor Indicators */}
          {!isEditing && contentRef.current && users.filter(user => user.username !== username && user.cursor !== null).map(user => (
            <div
              key={`cursor-${user.username}`}
              style={{
                ...styles.cursorIndicator,
                backgroundColor: userColors[user.username] || '#1890ff',
                left: `${user.cursor * 8}px` // Approximate character width
              }}
            >
              <div style={styles.cursorTooltip}>
                {user.username}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Notifications */}
      <div style={styles.notificationsContainer}>
        <h3 style={styles.panelTitle}>Notifications</h3>
        
        <div style={styles.notificationsList}>
          {notifications.length === 0 ? (
            <p style={styles.emptyMessage}>No notifications</p>
          ) : (
            notifications.map(notification => (
              <div key={notification.id} style={styles.notificationItem}>
                <span style={styles.notificationTime}>
                  {formatTime(notification.timestamp)}
                </span>
                <span style={styles.notificationMessage}>
                  {notification.message}
                </span>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

// Component styles
const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    padding: '16px',
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    maxWidth: '800px',
    margin: '0 auto'
  },
  title: {
    margin: '0 0 16px 0',
    fontSize: '24px',
    fontWeight: 'bold'
  },
  statusContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px'
  },
  statusIndicator: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  statusDot: {
    width: '12px',
    height: '12px',
    borderRadius: '50%'
  },
  statusText: {
    fontWeight: 'bold'
  },
  userInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  username: {
    fontWeight: 'bold'
  },
  userAvatar: {
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: 'white',
    fontWeight: 'bold'
  },
  contentContainer: {
    display: 'flex',
    gap: '16px',
    height: '400px'
  },
  usersPanel: {
    width: '200px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  panelTitle: {
    margin: '0 0 8px 0',
    fontSize: '16px',
    fontWeight: 'bold'
  },
  usersList: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    overflowY: 'auto',
    flex: 1
  },
  userItem: {
    padding: '8px',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px'
  },
  userItemHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '4px'
  },
  userItemName: {
    fontWeight: 'bold'
  },
  userStatusDot: {
    width: '8px',
    height: '8px',
    borderRadius: '50%'
  },
  userItemDetails: {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
    fontSize: '12px',
    color: '#8c8c8c'
  },
  userItemDetail: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  },
  sharedContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    position: 'relative'
  },
  editingContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    flex: 1
  },
  contentTextarea: {
    flex: 1,
    padding: '8px',
    borderRadius: '4px',
    border: '1px solid #d9d9d9',
    fontSize: '14px',
    fontFamily: 'monospace',
    resize: 'none'
  },
  editingActions: {
    display: 'flex',
    gap: '8px',
    justifyContent: 'flex-end'
  },
  viewingContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    flex: 1
  },
  contentDisplay: {
    flex: 1,
    padding: '8px',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px',
    fontSize: '14px',
    fontFamily: 'monospace',
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    margin: 0,
    overflow: 'auto'
  },
  button: {
    padding: '8px 16px',
    backgroundColor: '#1890ff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px'
  },
  saveButton: {
    backgroundColor: '#52c41a'
  },
  cancelButton: {
    backgroundColor: '#ff4d4f'
  },
  cursorIndicator: {
    position: 'absolute',
    width: '2px',
    height: '16px',
    top: '40px', // Adjust based on your layout
    zIndex: 10
  },
  cursorTooltip: {
    position: 'absolute',
    top: '-20px',
    left: '-10px',
    padding: '2px 6px',
    backgroundColor: 'inherit',
    color: 'white',
    borderRadius: '4px',
    fontSize: '12px',
    whiteSpace: 'nowrap'
  },
  notificationsContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  notificationsList: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    maxHeight: '150px',
    overflowY: 'auto',
    padding: '8px',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px'
  },
  notificationItem: {
    padding: '8px',
    backgroundColor: 'white',
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  notificationTime: {
    fontSize: '12px',
    color: '#8c8c8c',
    whiteSpace: 'nowrap'
  },
  notificationMessage: {
    flex: 1
  },
  emptyMessage: {
    textAlign: 'center',
    color: '#8c8c8c',
    fontStyle: 'italic',
    margin: '16px 0'
  }
};

export default CollaborationPanel;
