#!/usr/bin/env python3
"""
Test Environment Variable Loading
Tests that containers can access and use configured environment variables
"""

import subprocess
import json
import time
import sys

def run_docker_command(command):
    """Run a docker command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def test_backend_env_vars():
    """Test backend environment variables"""
    print("\n🔧 Testing Backend Environment Variables:")
    
    # Test database connection variables
    db_vars = [
        'POSTGRES_DB',
        'POSTGRES_USER', 
        'POSTGRES_PASSWORD',
        'POSTGRES_HOST',
        'USE_POSTGRES'
    ]
    
    for var in db_vars:
        success, stdout, stderr = run_docker_command(
            f'docker-compose exec -T backend printenv {var}'
        )
        if success and stdout.strip():
            print(f"  ✅ {var}: {stdout.strip()}")
        else:
            print(f"  ❌ {var}: Not set or accessible")
    
    # Test Django settings
    django_vars = [
        'DJANGO_SETTINGS_MODULE',
        'DJANGO_SECRET_KEY',
        'DJANGO_DEBUG',
        'DJANGO_ALLOWED_HOSTS'
    ]
    
    for var in django_vars:
        success, stdout, stderr = run_docker_command(
            f'docker-compose exec -T backend printenv {var}'
        )
        if success and stdout.strip():
            print(f"  ✅ {var}: {stdout.strip()}")
        else:
            print(f"  ❌ {var}: Not set or accessible")

def test_frontend_env_vars():
    """Test frontend environment variables"""
    print("\n🌐 Testing Frontend Environment Variables:")
    
    frontend_vars = [
        'REACT_APP_API_URL',
        'REACT_APP_WS_URL',
        'REACT_APP_WS_ENDPOINT',
        'API_TARGET',
        'REACT_APP_WS_PROXY_TARGET',
        'NODE_ENV'
    ]
    
    for var in frontend_vars:
        success, stdout, stderr = run_docker_command(
            f'docker-compose exec -T frontend printenv {var}'
        )
        if success and stdout.strip():
            print(f"  ✅ {var}: {stdout.strip()}")
        else:
            print(f"  ❌ {var}: Not set or accessible")

def test_database_connection():
    """Test database connection from backend"""
    print("\n🗄️ Testing Database Connection:")
    
    # Test database connectivity
    success, stdout, stderr = run_docker_command(
        'docker-compose exec -T backend python -c "import os; import socket; sock = socket.socket(); sock.settimeout(5); result = sock.connect_ex((os.environ.get(\'POSTGRES_HOST\', \'db\'), 5432)); sock.close(); print(\'Connected\' if result == 0 else \'Failed\')"'
    )
    
    if success and 'Connected' in stdout:
        print("  ✅ Database connection: Success")
    else:
        print("  ❌ Database connection: Failed")
        if stderr:
            print(f"    Error: {stderr}")

def test_api_endpoints():
    """Test API endpoint accessibility"""
    print("\n🔗 Testing API Endpoints:")
    
    # Test backend health endpoint
    success, stdout, stderr = run_docker_command(
        'docker-compose exec -T backend curl -f http://localhost:8000/health/ || echo "Failed"'
    )
    
    if success and 'Failed' not in stdout:
        print("  ✅ Backend health endpoint: Accessible")
    else:
        print("  ❌ Backend health endpoint: Not accessible")

def test_websocket_config():
    """Test WebSocket configuration"""
    print("\n🔌 Testing WebSocket Configuration:")
    
    # Check if WebSocket endpoint is configured
    success, stdout, stderr = run_docker_command(
        'docker-compose exec -T backend python -c "from django.conf import settings; print(getattr(settings, \'WEBSOCKET_ALLOWED_ORIGINS\', []))"'
    )
    
    if success and stdout.strip():
        print(f"  ✅ WebSocket allowed origins: {stdout.strip()}")
    else:
        print("  ❌ WebSocket configuration: Not accessible")

def test_container_health():
    """Test container health status"""
    print("\n🏥 Testing Container Health:")
    
    success, stdout, stderr = run_docker_command('docker-compose ps --format json')
    
    if success:
        try:
            # Parse each line as JSON (docker-compose ps outputs one JSON object per line)
            containers = []
            for line in stdout.strip().split('\n'):
                if line.strip():
                    containers.append(json.loads(line))
            
            for container in containers:
                name = container.get('Name', 'Unknown')
                state = container.get('State', 'Unknown')
                health = container.get('Health', 'Unknown')
                
                if state == 'running':
                    if health in ['healthy', 'Unknown']:
                        print(f"  ✅ {name}: {state} ({health})")
                    else:
                        print(f"  ⚠️ {name}: {state} ({health})")
                else:
                    print(f"  ❌ {name}: {state}")
                    
        except json.JSONDecodeError:
            # Fallback to simple parsing
            lines = stdout.strip().split('\n')
            for line in lines[1:]:  # Skip header
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        name = parts[0]
                        state = parts[1] if len(parts) > 1 else 'Unknown'
                        print(f"  {'✅' if 'Up' in state else '❌'} {name}: {state}")
    else:
        print("  ❌ Could not get container status")

def main():
    """Main testing function"""
    print("🧪 App Builder Environment Variable Loading Test")
    print("=" * 55)
    
    # Check if containers are running
    success, stdout, stderr = run_docker_command('docker-compose ps -q')
    if not success or not stdout.strip():
        print("❌ No containers are running. Please start with: docker-compose up")
        sys.exit(1)
    
    # Run tests
    test_container_health()
    test_backend_env_vars()
    test_frontend_env_vars()
    test_database_connection()
    test_api_endpoints()
    test_websocket_config()
    
    print("\n✅ Environment loading test complete!")
    print("\n💡 If any tests failed:")
    print("  1. Check docker-compose.yml environment sections")
    print("  2. Restart containers: docker-compose restart")
    print("  3. Check container logs: docker-compose logs [service]")

if __name__ == "__main__":
    main()
