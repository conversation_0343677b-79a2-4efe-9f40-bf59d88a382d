import React, { useState, useEffect } from 'react';
import { Space, Typography, Divider, Switch } from 'antd';
import { styled } from '../../../design-system';
import NumberInput from './NumberInput';
import ColorInput from './ColorInput';

const { Text } = Typography;

const ShadowContainer = styled.div`
  width: 100%;
`;

const ShadowPreview = styled.div`
  width: 100%;
  height: 80px;
  margin: 12px 0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  box-shadow: ${props => props.shadowStyle || 'none'};
  border: 1px solid #f0f0f0;
`;

const PropertyRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
`;

const PropertyLabel = styled(Text)`
  min-width: 80px;
  font-size: 12px;
  font-weight: 500;
`;

const PreviewBox = styled.div`
  width: 60px;
  height: 40px;
  background: #1890ff;
  border-radius: 4px;
  opacity: 0.8;
`;

/**
 * Visual shadow editor with offset, blur, spread, and color controls
 */
const ShadowEditor = ({
  value,
  onChange,
  showPreview = true,
  ...props
}) => {
  const [offsetX, setOffsetX] = useState('0px');
  const [offsetY, setOffsetY] = useState('2px');
  const [blurRadius, setBlurRadius] = useState('4px');
  const [spreadRadius, setSpreadRadius] = useState('0px');
  const [shadowColor, setShadowColor] = useState('rgba(0, 0, 0, 0.1)');
  const [inset, setInset] = useState(false);

  // Parse shadow value on mount and when value changes
  useEffect(() => {
    if (value) {
      const parsed = parseShadowValue(value);
      setOffsetX(parsed.offsetX);
      setOffsetY(parsed.offsetY);
      setBlurRadius(parsed.blurRadius);
      setSpreadRadius(parsed.spreadRadius);
      setShadowColor(parsed.color);
      setInset(parsed.inset);
    }
  }, [value]);

  // Parse shadow value like "2px 4px 8px rgba(0,0,0,0.1)" or object
  const parseShadowValue = (val) => {
    if (!val || val === 'none') {
      return {
        offsetX: '0px',
        offsetY: '2px',
        blurRadius: '4px',
        spreadRadius: '0px',
        color: 'rgba(0, 0, 0, 0.1)',
        inset: false
      };
    }
    
    if (typeof val === 'object') {
      return {
        offsetX: val.offsetX || '0px',
        offsetY: val.offsetY || '2px',
        blurRadius: val.blurRadius || '4px',
        spreadRadius: val.spreadRadius || '0px',
        color: val.color || 'rgba(0, 0, 0, 0.1)',
        inset: val.inset || false
      };
    }
    
    if (typeof val === 'string') {
      // Parse string like "inset 2px 4px 8px 2px rgba(0,0,0,0.1)"
      let shadowString = val.trim();
      let isInset = false;
      
      if (shadowString.startsWith('inset ')) {
        isInset = true;
        shadowString = shadowString.replace('inset ', '');
      }
      
      // Extract color (rgba, rgb, hex, or named color)
      let color = 'rgba(0, 0, 0, 0.1)';
      const colorMatch = shadowString.match(/(rgba?\([^)]+\)|hsla?\([^)]+\)|#[a-fA-F0-9]{3,8}|\b\w+\b)$/);
      if (colorMatch) {
        color = colorMatch[1];
        shadowString = shadowString.replace(colorMatch[1], '').trim();
      }
      
      // Parse remaining values (offsetX offsetY blurRadius spreadRadius)
      const values = shadowString.split(/\s+/).filter(v => v);
      
      return {
        offsetX: values[0] || '0px',
        offsetY: values[1] || '2px',
        blurRadius: values[2] || '4px',
        spreadRadius: values[3] || '0px',
        color,
        inset: isInset
      };
    }
    
    return {
      offsetX: '0px',
      offsetY: '2px',
      blurRadius: '4px',
      spreadRadius: '0px',
      color: 'rgba(0, 0, 0, 0.1)',
      inset: false
    };
  };

  // Format shadow value for output
  const formatShadowValue = (x, y, blur, spread, color, isInset) => {
    const parts = [x, y, blur, spread, color];
    const shadowValue = parts.join(' ');
    return isInset ? `inset ${shadowValue}` : shadowValue;
  };

  // Handle value changes
  const handleValueChange = (property, newValue) => {
    let newOffsetX = offsetX;
    let newOffsetY = offsetY;
    let newBlurRadius = blurRadius;
    let newSpreadRadius = spreadRadius;
    let newShadowColor = shadowColor;
    let newInset = inset;

    switch (property) {
      case 'offsetX':
        newOffsetX = newValue;
        setOffsetX(newValue);
        break;
      case 'offsetY':
        newOffsetY = newValue;
        setOffsetY(newValue);
        break;
      case 'blurRadius':
        newBlurRadius = newValue;
        setBlurRadius(newValue);
        break;
      case 'spreadRadius':
        newSpreadRadius = newValue;
        setSpreadRadius(newValue);
        break;
      case 'color':
        newShadowColor = newValue;
        setShadowColor(newValue);
        break;
      case 'inset':
        newInset = newValue;
        setInset(newValue);
        break;
    }

    const formattedValue = formatShadowValue(
      newOffsetX, newOffsetY, newBlurRadius, newSpreadRadius, newShadowColor, newInset
    );
    onChange?.(formattedValue);
  };

  const currentShadowStyle = formatShadowValue(offsetX, offsetY, blurRadius, spreadRadius, shadowColor, inset);

  return (
    <ShadowContainer>
      <Space direction="vertical" style={{ width: '100%' }}>
        <PropertyRow>
          <PropertyLabel>Inset:</PropertyLabel>
          <Switch
            checked={inset}
            onChange={(checked) => handleValueChange('inset', checked)}
            size="small"
          />
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Offset X:</PropertyLabel>
          <div style={{ flex: 1 }}>
            <NumberInput
              value={offsetX}
              onChange={(val) => handleValueChange('offsetX', val)}
              min={-50}
              max={50}
              step={1}
              unit="px"
              units={['px', 'em', 'rem']}
              size="small"
            />
          </div>
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Offset Y:</PropertyLabel>
          <div style={{ flex: 1 }}>
            <NumberInput
              value={offsetY}
              onChange={(val) => handleValueChange('offsetY', val)}
              min={-50}
              max={50}
              step={1}
              unit="px"
              units={['px', 'em', 'rem']}
              size="small"
            />
          </div>
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Blur:</PropertyLabel>
          <div style={{ flex: 1 }}>
            <NumberInput
              value={blurRadius}
              onChange={(val) => handleValueChange('blurRadius', val)}
              min={0}
              max={100}
              step={1}
              unit="px"
              units={['px', 'em', 'rem']}
              size="small"
            />
          </div>
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Spread:</PropertyLabel>
          <div style={{ flex: 1 }}>
            <NumberInput
              value={spreadRadius}
              onChange={(val) => handleValueChange('spreadRadius', val)}
              min={-50}
              max={50}
              step={1}
              unit="px"
              units={['px', 'em', 'rem']}
              size="small"
            />
          </div>
        </PropertyRow>

        <PropertyRow>
          <PropertyLabel>Color:</PropertyLabel>
          <div style={{ flex: 1 }}>
            <ColorInput
              value={shadowColor}
              onChange={(val) => handleValueChange('color', val)}
              placeholder="Shadow color"
            />
          </div>
        </PropertyRow>

        {showPreview && (
          <>
            <Divider style={{ margin: '8px 0' }} />
            <Text style={{ fontSize: '12px', marginBottom: '4px' }}>Preview:</Text>
            <ShadowPreview shadowStyle={currentShadowStyle}>
              <PreviewBox />
            </ShadowPreview>
            <Text type="secondary" style={{ fontSize: '11px', textAlign: 'center' }}>
              {currentShadowStyle}
            </Text>
          </>
        )}
      </Space>
    </ShadowContainer>
  );
};

export default ShadowEditor;
