@echo off
REM Database Setup Script for Windows
REM This script helps you choose and configure the database for the application

setlocal enabledelayedexpansion

echo.
echo ================================
echo 🚀 Database Setup for App Builder 201
echo ================================
echo.

REM Check if <PERSON><PERSON> is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)
echo ✅ Docker is running

REM Check if we're in the right directory
if not exist docker-compose.yml (
    echo ❌ docker-compose.yml not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

:menu
echo.
echo Choose your database option:
echo 1) SQLite (Recommended for development - no external dependencies)
echo 2) PostgreSQL (Production-like setup - requires Docker)
echo 3) Check current configuration
echo 4) Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto setup_sqlite
if "%choice%"=="2" goto setup_postgresql
if "%choice%"=="3" goto check_config
if "%choice%"=="4" goto exit
echo ❌ Invalid choice. Please enter 1, 2, 3, or 4.
goto menu

:setup_sqlite
echo.
echo ================================
echo 🚀 Setting up SQLite Database
echo ================================
echo.

REM Create .env file with SQLite configuration
(
echo # Database Configuration - Using SQLite
echo USE_POSTGRES=false
echo.
echo # Django Configuration
echo DJANGO_DEBUG=true
echo DJANGO_SECRET_KEY=dev-secret-key-change-in-production
echo.
echo # Frontend Configuration
echo REACT_APP_API_BASE_URL=http://localhost:8000
echo REACT_APP_API_URL=
echo REACT_APP_BACKEND_HOST=backend
echo.
echo # Development Settings
echo NODE_ENV=development
echo CHOKIDAR_USEPOLLING=true
) > .env

echo ✅ SQLite configuration created in .env file

echo ℹ️  Running database setup...
docker-compose run --rm backend python setup_db.py

if errorlevel 1 (
    echo ❌ Database setup failed
    pause
    exit /b 1
)

echo ✅ SQLite database setup completed successfully!
echo ℹ️  You can now run: docker-compose up
goto end

:setup_postgresql
echo.
echo ================================
echo 🚀 Setting up PostgreSQL Database
echo ================================
echo.

REM Create .env file with PostgreSQL configuration
(
echo # Database Configuration - Using PostgreSQL
echo USE_POSTGRES=true
echo POSTGRES_DB=myapp
echo POSTGRES_USER=myappuser
echo POSTGRES_PASSWORD=myapppassword
echo.
echo # Django Configuration
echo DJANGO_DEBUG=true
echo DJANGO_SECRET_KEY=dev-secret-key-change-in-production
echo.
echo # Frontend Configuration
echo REACT_APP_API_BASE_URL=http://localhost:8000
echo REACT_APP_API_URL=
echo REACT_APP_BACKEND_HOST=backend
echo.
echo # Development Settings
echo NODE_ENV=development
echo CHOKIDAR_USEPOLLING=true
) > .env

echo ✅ PostgreSQL configuration created in .env file

echo ℹ️  Starting PostgreSQL container...
docker-compose up -d db

echo ℹ️  Waiting for PostgreSQL to be ready...
timeout /t 10 /nobreak >nul

echo ℹ️  Running database setup...
docker-compose run --rm backend python setup_db.py

if errorlevel 1 (
    echo ❌ Database setup failed
    echo ⚠️  You might want to try SQLite instead
    pause
    exit /b 1
)

echo ✅ PostgreSQL database setup completed successfully!
echo ℹ️  You can now run: docker-compose up
goto end

:check_config
echo.
echo ================================
echo 🚀 Current Configuration
echo ================================
echo.

if exist .env (
    echo ℹ️  Found .env file with the following database configuration:
    echo.
    findstr /R "USE_POSTGRES POSTGRES_" .env 2>nul
    if errorlevel 1 echo No database configuration found
    echo.
) else (
    echo ⚠️  No .env file found
)

if exist docker-compose.yml (
    echo ℹ️  Docker Compose configuration found
)

goto menu

:exit
echo ℹ️  Exiting...
exit /b 0

:end
echo.
echo Press any key to exit...
pause >nul
