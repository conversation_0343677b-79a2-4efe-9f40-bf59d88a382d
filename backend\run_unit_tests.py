#!/usr/bin/env python
"""
Comprehensive unit test runner for the Django backend.
Runs all unit tests with coverage reporting and detailed output.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app_builder.settings')

import django
django.setup()

from django.test.utils import get_runner
from django.conf import settings
from django.core.management import execute_from_command_line


class UnitTestRunner:
    """Comprehensive unit test runner with coverage and reporting."""
    
    def __init__(self):
        self.backend_dir = Path(__file__).parent
        self.test_results_dir = self.backend_dir / 'test-results'
        self.coverage_dir = self.backend_dir / 'htmlcov'
        
        # Ensure test results directory exists
        self.test_results_dir.mkdir(exist_ok=True)

    def run_tests(self, test_modules=None, coverage=True, verbose=True, failfast=False):
        """Run unit tests with optional coverage."""
        print("🧪 Running Django Backend Unit Tests")
        print("=" * 50)
        
        # Default test modules if none specified
        if not test_modules:
            test_modules = [
                'tests.test_models',
                'tests.test_views', 
                'tests.test_serializers',
                'tests.test_services',
                'tests.test_security'
            ]
        
        # Build test command
        cmd = ['python', 'manage.py', 'test']
        
        if coverage:
            # Use coverage.py to run tests
            cmd = ['coverage', 'run', '--source=.', 'manage.py', 'test']
        
        # Add test modules
        cmd.extend(test_modules)
        
        # Add options
        if verbose:
            cmd.append('--verbosity=2')
        if failfast:
            cmd.append('--failfast')
        
        # Set environment variables
        env = os.environ.copy()
        env['DJANGO_SETTINGS_MODULE'] = 'app_builder.settings.test'
        
        print(f"Running command: {' '.join(cmd)}")
        print()
        
        try:
            # Run tests
            result = subprocess.run(
                cmd,
                cwd=self.backend_dir,
                env=env,
                capture_output=False,
                text=True
            )
            
            if coverage and result.returncode == 0:
                self.generate_coverage_reports()
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Error running tests: {e}")
            return False

    def generate_coverage_reports(self):
        """Generate coverage reports in multiple formats."""
        print("\n📊 Generating coverage reports...")
        
        try:
            # Generate HTML coverage report
            subprocess.run([
                'coverage', 'html', 
                '--directory', str(self.coverage_dir)
            ], cwd=self.backend_dir, check=True)
            print(f"✅ HTML coverage report: {self.coverage_dir}/index.html")
            
            # Generate XML coverage report
            xml_file = self.test_results_dir / 'coverage.xml'
            subprocess.run([
                'coverage', 'xml', 
                '--output', str(xml_file)
            ], cwd=self.backend_dir, check=True)
            print(f"✅ XML coverage report: {xml_file}")
            
            # Generate JSON coverage report
            json_file = self.test_results_dir / 'coverage.json'
            subprocess.run([
                'coverage', 'json', 
                '--output', str(json_file)
            ], cwd=self.backend_dir, check=True)
            print(f"✅ JSON coverage report: {json_file}")
            
            # Print coverage summary
            print("\n📈 Coverage Summary:")
            subprocess.run(['coverage', 'report'], cwd=self.backend_dir)
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Error generating coverage reports: {e}")

    def run_specific_test_class(self, test_class):
        """Run a specific test class."""
        print(f"🎯 Running specific test class: {test_class}")
        
        cmd = ['python', 'manage.py', 'test', test_class, '--verbosity=2']
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.backend_dir,
                capture_output=False,
                text=True
            )
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Error running test class: {e}")
            return False

    def run_specific_test_method(self, test_method):
        """Run a specific test method."""
        print(f"🎯 Running specific test method: {test_method}")
        
        cmd = ['python', 'manage.py', 'test', test_method, '--verbosity=2']
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.backend_dir,
                capture_output=False,
                text=True
            )
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Error running test method: {e}")
            return False

    def run_model_tests(self):
        """Run only model tests."""
        return self.run_tests(['tests.test_models'], coverage=True)

    def run_view_tests(self):
        """Run only view tests."""
        return self.run_tests(['tests.test_views'], coverage=True)

    def run_serializer_tests(self):
        """Run only serializer tests."""
        return self.run_tests(['tests.test_serializers'], coverage=True)

    def run_service_tests(self):
        """Run only service tests."""
        return self.run_tests(['tests.test_services'], coverage=True)

    def run_security_tests(self):
        """Run only security tests."""
        return self.run_tests(['tests.test_security'], coverage=True)

    def check_test_environment(self):
        """Check if test environment is properly set up."""
        print("🔍 Checking test environment...")
        
        # Check if required packages are installed
        required_packages = [
            'django', 'pytest', 'pytest-django', 'model-bakery', 
            'coverage', 'djangorestframework'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing packages: {', '.join(missing_packages)}")
            print("Install with: pip install " + ' '.join(missing_packages))
            return False
        
        print("✅ All required packages are installed")
        
        # Check Django configuration
        try:
            from django.conf import settings
            print(f"✅ Django settings loaded: {settings.SETTINGS_MODULE}")
        except Exception as e:
            print(f"❌ Django configuration error: {e}")
            return False
        
        return True

    def clean_test_artifacts(self):
        """Clean up test artifacts and cache files."""
        print("🧹 Cleaning test artifacts...")
        
        # Remove coverage files
        coverage_files = [
            '.coverage',
            'htmlcov',
            'test-results/coverage.xml',
            'test-results/coverage.json'
        ]
        
        for file_path in coverage_files:
            full_path = self.backend_dir / file_path
            if full_path.exists():
                if full_path.is_dir():
                    import shutil
                    shutil.rmtree(full_path)
                else:
                    full_path.unlink()
                print(f"🗑️ Removed: {file_path}")
        
        # Remove Python cache
        import shutil
        for cache_dir in self.backend_dir.rglob('__pycache__'):
            shutil.rmtree(cache_dir)
            print(f"🗑️ Removed: {cache_dir}")
        
        print("✅ Cleanup complete")


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(description='Django Backend Unit Test Runner')
    parser.add_argument('--no-coverage', action='store_true', 
                       help='Run tests without coverage')
    parser.add_argument('--failfast', action='store_true',
                       help='Stop on first test failure')
    parser.add_argument('--models', action='store_true',
                       help='Run only model tests')
    parser.add_argument('--views', action='store_true',
                       help='Run only view tests')
    parser.add_argument('--serializers', action='store_true',
                       help='Run only serializer tests')
    parser.add_argument('--services', action='store_true',
                       help='Run only service tests')
    parser.add_argument('--security', action='store_true',
                       help='Run only security tests')
    parser.add_argument('--test-class', type=str,
                       help='Run specific test class (e.g., tests.test_models.TestAppModel)')
    parser.add_argument('--test-method', type=str,
                       help='Run specific test method (e.g., tests.test_models.TestAppModel.test_creation)')
    parser.add_argument('--check-env', action='store_true',
                       help='Check test environment setup')
    parser.add_argument('--clean', action='store_true',
                       help='Clean test artifacts and cache files')
    
    args = parser.parse_args()
    
    runner = UnitTestRunner()
    
    # Handle specific actions
    if args.check_env:
        success = runner.check_test_environment()
        sys.exit(0 if success else 1)
    
    if args.clean:
        runner.clean_test_artifacts()
        sys.exit(0)
    
    if args.test_class:
        success = runner.run_specific_test_class(args.test_class)
        sys.exit(0 if success else 1)
    
    if args.test_method:
        success = runner.run_specific_test_method(args.test_method)
        sys.exit(0 if success else 1)
    
    # Handle specific test suites
    if args.models:
        success = runner.run_model_tests()
    elif args.views:
        success = runner.run_view_tests()
    elif args.serializers:
        success = runner.run_serializer_tests()
    elif args.services:
        success = runner.run_service_tests()
    elif args.security:
        success = runner.run_security_tests()
    else:
        # Run all tests
        success = runner.run_tests(
            coverage=not args.no_coverage,
            failfast=args.failfast
        )
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)


if __name__ == '__main__':
    main()
