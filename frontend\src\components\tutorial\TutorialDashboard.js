/**
 * Tutorial Dashboard Component
 * 
 * Comprehensive dashboard for tutorial management, progress tracking,
 * and learning analytics.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Timeline,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Tabs,
  Alert,
  Empty,
  Select,
  DatePicker,
  Tooltip
} from 'antd';
import {
  TrophyOutlined,
  ClockCircleOutlined,
  BookOutlined,
  StarOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  Bar<PERSON>hartOutlined,
  CalendarOutlined,
  TargetOutlined,
  RocketOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import TutorialProgress from './TutorialProgress';
import TutorialBadges from './TutorialBadges';
import {
  TUTORIAL_CATEGORIES_CONFIG,
  LEARNING_PATHS,
  getTutorialsByCategory
} from './TutorialContent';
import { TUTORIAL_STATUS, TUTORIAL_CATEGORIES } from './types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

// Styled Components
const DashboardCard = styled(Card)`
  margin-bottom: 16px;
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }
`;

const StatCard = styled(Card)`
  text-align: center;
  border: 1px solid #f0f0f0;
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }
`;

const ChartContainer = styled.div`
  height: 300px;
  margin: 16px 0;
`;

const TutorialDashboard = ({ userId = 'anonymous' }) => {
  const {
    getAllTutorials,
    getTutorialProgress,
    getStatistics,
    getBadges,
    startTutorial
  } = useTutorial();

  const [activeTab, setActiveTab] = useState('overview');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [dateRange, setDateRange] = useState(null);

  const allTutorials = getAllTutorials();
  const statistics = getStatistics();
  const badges = getBadges();

  // Calculate detailed analytics
  const getDetailedAnalytics = () => {
    const analytics = {
      categoryProgress: {},
      difficultyProgress: {},
      timeSpentByCategory: {},
      completionTrend: [],
      recentActivity: []
    };

    // Category progress
    Object.keys(TUTORIAL_CATEGORIES).forEach(category => {
      const categoryTutorials = getTutorialsByCategory(category);
      const completed = categoryTutorials.filter(tutorial => {
        const progress = getTutorialProgress(tutorial.id);
        return progress?.status === TUTORIAL_STATUS.COMPLETED;
      }).length;

      analytics.categoryProgress[category] = {
        total: categoryTutorials.length,
        completed,
        percentage: categoryTutorials.length > 0 ? (completed / categoryTutorials.length) * 100 : 0
      };
    });

    // Difficulty progress
    [1, 2, 3, 4, 5].forEach(difficulty => {
      const difficultyTutorials = allTutorials.filter(t => t.difficulty === difficulty);
      const completed = difficultyTutorials.filter(tutorial => {
        const progress = getTutorialProgress(tutorial.id);
        return progress?.status === TUTORIAL_STATUS.COMPLETED;
      }).length;

      analytics.difficultyProgress[difficulty] = {
        total: difficultyTutorials.length,
        completed,
        percentage: difficultyTutorials.length > 0 ? (completed / difficultyTutorials.length) * 100 : 0
      };
    });

    // Recent activity
    analytics.recentActivity = allTutorials
      .map(tutorial => {
        const progress = getTutorialProgress(tutorial.id);
        return { tutorial, progress };
      })
      .filter(({ progress }) => progress && progress.lastUpdated)
      .sort((a, b) => new Date(b.progress.lastUpdated) - new Date(a.progress.lastUpdated))
      .slice(0, 10);

    return analytics;
  };

  const analytics = getDetailedAnalytics();

  // Chart data preparation
  const categoryChartData = Object.entries(analytics.categoryProgress).map(([category, data]) => ({
    category: TUTORIAL_CATEGORIES_CONFIG[category]?.title || category,
    completed: data.completed,
    total: data.total,
    percentage: data.percentage
  }));

  const difficultyChartData = Object.entries(analytics.difficultyProgress).map(([difficulty, data]) => ({
    difficulty: `${difficulty} Star${difficulty > 1 ? 's' : ''}`,
    completed: data.completed,
    total: data.total,
    percentage: data.percentage
  }));

  // Table columns for tutorial list
  const tutorialColumns = [
    {
      title: 'Tutorial',
      dataIndex: 'title',
      key: 'title',
      render: (title, record) => (
        <Space>
          <div style={{ fontSize: '16px' }}>
            {record.icon || <BookOutlined />}
          </div>
          <div>
            <Text strong>{title}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.category.replace('_', ' ')} • {record.estimatedDuration}min
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => {
        const progress = getTutorialProgress(record.id);
        const status = progress?.status || TUTORIAL_STATUS.NOT_STARTED;
        
        const statusConfig = {
          [TUTORIAL_STATUS.COMPLETED]: { color: 'success', icon: <CheckCircleOutlined /> },
          [TUTORIAL_STATUS.IN_PROGRESS]: { color: 'processing', icon: <PlayCircleOutlined /> },
          [TUTORIAL_STATUS.SKIPPED]: { color: 'default', icon: null },
          [TUTORIAL_STATUS.NOT_STARTED]: { color: 'default', icon: null }
        };

        return (
          <Tag color={statusConfig[status].color} icon={statusConfig[status].icon}>
            {status.replace('_', ' ')}
          </Tag>
        );
      }
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        const progress = getTutorialProgress(record.id);
        if (!progress || progress.status === TUTORIAL_STATUS.NOT_STARTED) {
          return <Progress percent={0} size="small" />;
        }
        
        const percentage = (progress.completedSteps.length / record.steps.length) * 100;
        return <Progress percent={percentage} size="small" />;
      }
    },
    {
      title: 'Difficulty',
      dataIndex: 'difficulty',
      key: 'difficulty',
      render: (difficulty) => (
        <Space>
          {Array.from({ length: 5 }, (_, i) => (
            <StarOutlined 
              key={i} 
              style={{ 
                color: i < difficulty ? '#faad14' : '#d9d9d9',
                fontSize: '12px'
              }} 
            />
          ))}
        </Space>
      )
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => {
        const progress = getTutorialProgress(record.id);
        const status = progress?.status || TUTORIAL_STATUS.NOT_STARTED;
        
        return (
          <Button
            type={status === TUTORIAL_STATUS.IN_PROGRESS ? 'default' : 'primary'}
            size="small"
            onClick={() => startTutorial(record.id)}
          >
            {status === TUTORIAL_STATUS.IN_PROGRESS ? 'Continue' : 
             status === TUTORIAL_STATUS.COMPLETED ? 'Restart' : 'Start'}
          </Button>
        );
      }
    }
  ];

  const filteredTutorials = selectedCategory 
    ? allTutorials.filter(t => t.category === selectedCategory)
    : allTutorials;

  return (
    <div>
      <Title level={2}>
        <Space>
          <BarChartOutlined />
          Tutorial Dashboard
        </Space>
      </Title>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* Overview Tab */}
        <TabPane tab="Overview" key="overview">
          {/* Key Statistics */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <StatCard>
                <Statistic
                  title="Total Progress"
                  value={statistics.completionRate}
                  precision={1}
                  suffix="%"
                  prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
                />
              </StatCard>
            </Col>
            <Col span={6}>
              <StatCard>
                <Statistic
                  title="Completed"
                  value={statistics.totalTutorialsCompleted}
                  suffix={`/ ${allTutorials.length}`}
                  prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                />
              </StatCard>
            </Col>
            <Col span={6}>
              <StatCard>
                <Statistic
                  title="Time Spent"
                  value={statistics.totalTimeSpent}
                  suffix="min"
                  prefix={<ClockCircleOutlined style={{ color: '#1890ff' }} />}
                />
              </StatCard>
            </Col>
            <Col span={6}>
              <StatCard>
                <Statistic
                  title="Badges Earned"
                  value={badges.length}
                  prefix={<StarOutlined style={{ color: '#722ed1' }} />}
                />
              </StatCard>
            </Col>
          </Row>

          {/* Progress by Category */}
          <DashboardCard title="Progress by Category">
            <ChartContainer>
              <Column
                data={categoryChartData}
                xField="category"
                yField="percentage"
                color="#1890ff"
                columnStyle={{
                  radius: [4, 4, 0, 0],
                }}
                meta={{
                  percentage: {
                    alias: 'Completion %',
                  },
                }}
              />
            </ChartContainer>
          </DashboardCard>

          {/* Recent Activity */}
          <DashboardCard title="Recent Activity">
            {analytics.recentActivity.length > 0 ? (
              <Timeline>
                {analytics.recentActivity.slice(0, 5).map(({ tutorial, progress }) => (
                  <Timeline.Item
                    key={tutorial.id}
                    dot={
                      progress.status === TUTORIAL_STATUS.COMPLETED ? (
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <PlayCircleOutlined style={{ color: '#1890ff' }} />
                      )
                    }
                  >
                    <Space direction="vertical" size="small">
                      <Text strong>{tutorial.title}</Text>
                      <Text type="secondary">
                        {progress.status === TUTORIAL_STATUS.COMPLETED ? 'Completed' : 'In Progress'} • 
                        {new Date(progress.lastUpdated).toLocaleDateString()}
                      </Text>
                    </Space>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Empty description="No recent activity" />
            )}
          </DashboardCard>
        </TabPane>

        {/* Detailed Progress Tab */}
        <TabPane tab="Detailed Progress" key="progress">
          <TutorialProgress userId={userId} />
        </TabPane>

        {/* Tutorials Tab */}
        <TabPane tab="All Tutorials" key="tutorials">
          <Space style={{ marginBottom: 16 }}>
            <Select
              placeholder="Filter by category"
              value={selectedCategory}
              onChange={setSelectedCategory}
              allowClear
              style={{ width: 200 }}
            >
              {Object.entries(TUTORIAL_CATEGORIES_CONFIG).map(([key, config]) => (
                <Select.Option key={key} value={key}>
                  {config.title}
                </Select.Option>
              ))}
            </Select>
          </Space>

          <Table
            dataSource={filteredTutorials}
            columns={tutorialColumns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </TabPane>

        {/* Badges Tab */}
        <TabPane tab="Achievements" key="badges">
          <TutorialBadges userId={userId} />
        </TabPane>

        {/* Analytics Tab */}
        <TabPane tab="Analytics" key="analytics">
          <Row gutter={16}>
            <Col span={12}>
              <DashboardCard title="Progress by Difficulty">
                <ChartContainer>
                  <Pie
                    data={difficultyChartData}
                    angleField="completed"
                    colorField="difficulty"
                    radius={0.8}
                    label={{
                      type: 'outer',
                      content: '{name} ({percentage})',
                    }}
                  />
                </ChartContainer>
              </DashboardCard>
            </Col>
            <Col span={12}>
              <DashboardCard title="Learning Path Progress">
                <Space direction="vertical" style={{ width: '100%' }}>
                  {Object.entries(LEARNING_PATHS).map(([pathKey, path]) => {
                    const completed = path.tutorials.filter(tutorialId => {
                      const progress = getTutorialProgress(tutorialId);
                      return progress?.status === TUTORIAL_STATUS.COMPLETED;
                    }).length;
                    
                    const percentage = (completed / path.tutorials.length) * 100;
                    
                    return (
                      <div key={pathKey}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                          <Text strong>{path.title}</Text>
                          <Text type="secondary">{completed}/{path.tutorials.length}</Text>
                        </div>
                        <Progress percent={percentage} size="small" />
                      </div>
                    );
                  })}
                </Space>
              </DashboardCard>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default TutorialDashboard;
