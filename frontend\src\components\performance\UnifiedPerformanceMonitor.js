import React, { useState, useEffect } from 'react';
import { unifiedPerformanceMonitor } from '../../utils/performance';

/**
 * Unified Performance Monitor Component
 * Consolidates all performance monitoring into a single, configurable component
 */
const UnifiedPerformanceMonitor = ({ 
  enabled = true,
  initiallyMinimized = true,
  refreshInterval = 5000,
  showMemory = true,
  showNetwork = true,
  showFps = true,
  showWebSocket = true,
  style = {}
}) => {
  const [minimized, setMinimized] = useState(initiallyMinimized);
  const [metrics, setMetrics] = useState(unifiedPerformanceMonitor.getMetrics());
  
  useEffect(() => {
    if (!enabled) return;
    
    // Initialize the monitor if not already
    unifiedPerformanceMonitor.init();
    
    // Set up refresh interval
    const intervalId = setInterval(() => {
      setMetrics(unifiedPerformanceMonitor.getMetrics());
    }, refreshInterval);
    
    // Clean up on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [enabled, refreshInterval]);
  
  if (!enabled) return null;
  
  // Render the component with metrics
  return (
    <div className="performance-monitor" style={{ ...style }}>
      {/* Toggle button */}
      <button 
        onClick={() => setMinimized(!minimized)}
        className="performance-monitor-toggle"
      >
        {minimized ? 'Show' : 'Hide'} Performance Monitor
      </button>
      
      {!minimized && (
        <div className="performance-monitor-content">
          {/* Render metrics based on configuration */}
          {showFps && (
            <div className="metric-card">
              <h3>FPS</h3>
              <div className="metric-value">{metrics.fps}</div>
            </div>
          )}
          
          {showMemory && (
            <div className="metric-card">
              <h3>Memory</h3>
              <div className="metric-value">
                {metrics.memory.used}MB / {metrics.memory.limit ? `${metrics.memory.limit}MB` : 'Unknown'}
              </div>
            </div>
          )}
          
          {/* Add other metric displays */}
        </div>
      )}
    </div>
  );
};

export default UnifiedPerformanceMonitor;