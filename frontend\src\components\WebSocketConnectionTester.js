import React, { useState, useEffect, useRef } from 'react';
import { getWebSocketUrl } from '../utils/websocket';
import EnhancedWebSocketService from '../services/EnhancedWebSocketService';

/**
 * WebSocket Connection Tester Component
 * 
 * This component provides a UI for testing WebSocket connections and diagnosing issues.
 * It allows users to connect to different WebSocket endpoints, send messages, and view responses.
 */
const WebSocketConnectionTester = () => {
  // State
  const [endpoint, setEndpoint] = useState('app_builder');
  const [customEndpoint, setCustomEndpoint] = useState('');
  const [useCustomEndpoint, setUseCustomEndpoint] = useState(false);
  const [connectionState, setConnectionState] = useState('CLOSED');
  const [messages, setMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [messageType, setMessageType] = useState('ping');
  const [error, setError] = useState(null);
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const [latency, setLatency] = useState(null);
  const [pingStartTime, setPingStartTime] = useState(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Refs
  const wsRef = useRef(null);
  const messagesEndRef = useRef(null);
  
  // Predefined endpoints
  const endpoints = [
    { value: 'app_builder', label: 'App Builder' },
    { value: 'test', label: 'Test' },
    { value: 'echo', label: 'Echo' },
    { value: 'health', label: 'Health' },
    { value: 'simple', label: 'Simple' }
  ];
  
  // Predefined message types
  const messageTypes = [
    { value: 'ping', label: 'Ping' },
    { value: 'get_app_data', label: 'Get App Data' },
    { value: 'update_app_data', label: 'Update App Data' },
    { value: 'custom', label: 'Custom' }
  ];
  
  // Connect to WebSocket
  const connect = () => {
    // Disconnect if already connected
    if (wsRef.current) {
      wsRef.current.disconnect();
    }
    
    // Get WebSocket URL
    const wsUrl = useCustomEndpoint ? 
      customEndpoint : 
      getWebSocketUrl(endpoint);
    
    // Create WebSocket service
    const ws = new EnhancedWebSocketService({
      baseUrl: wsUrl.substring(0, wsUrl.lastIndexOf('/') + 1),
      endpoint: wsUrl.substring(wsUrl.lastIndexOf('/') + 1),
      debug: true,
      autoReconnect: true,
      maxReconnectAttempts: 5
    });
    
    // Store reference
    wsRef.current = ws;
    
    // Add event listeners
    ws.addEventListener('open', () => {
      addMessage('system', 'Connected to WebSocket server');
      setError(null);
    });
    
    ws.addEventListener('message', (event) => {
      // Check if this is a response to a ping
      if (pingStartTime && event.data && 
          (typeof event.data === 'string' && event.data.includes('ping')) || 
          (typeof event.data === 'object' && event.data.type === 'ping')) {
        const endTime = Date.now();
        const pingLatency = endTime - pingStartTime;
        setLatency(pingLatency);
        setPingStartTime(null);
      }
      
      // Add message to list
      addMessage('received', event.data);
    });
    
    ws.addEventListener('close', () => {
      addMessage('system', 'Disconnected from WebSocket server');
    });
    
    ws.addEventListener('error', (error) => {
      addMessage('error', `WebSocket error: ${error.message || 'Unknown error'}`);
      setError(error.message || 'Unknown error');
    });
    
    ws.addEventListener('stateChange', (event) => {
      setConnectionState(event.newState);
      
      if (event.newState === 'RECONNECTING') {
        setReconnectAttempt(prev => prev + 1);
      } else if (event.newState === 'OPEN') {
        setReconnectAttempt(0);
      }
    });
    
    // Connect
    ws.connect()
      .catch(error => {
        setError(error.message);
        addMessage('error', `Connection error: ${error.message}`);
      });
  };
  
  // Disconnect from WebSocket
  const disconnect = () => {
    if (wsRef.current) {
      wsRef.current.disconnect();
      wsRef.current = null;
    }
  };
  
  // Send message
  const sendMessage = () => {
    if (!wsRef.current || connectionState !== 'OPEN') {
      setError('Not connected to WebSocket server');
      return;
    }
    
    try {
      // Create message based on type
      let message;
      
      switch (messageType) {
        case 'ping':
          message = { type: 'ping', timestamp: Date.now() };
          setPingStartTime(Date.now());
          break;
        case 'get_app_data':
          message = { type: 'request_app_data' };
          break;
        case 'update_app_data':
          message = { 
            type: 'update_app_data',
            data: {
              components: [],
              layouts: [],
              styles: {},
              data: {}
            }
          };
          break;
        case 'custom':
          try {
            message = JSON.parse(messageInput);
          } catch (error) {
            message = messageInput;
          }
          break;
        default:
          message = messageInput;
      }
      
      // Send message
      wsRef.current.send(message)
        .then(() => {
          addMessage('sent', message);
          
          // Clear input for custom messages
          if (messageType === 'custom') {
            setMessageInput('');
          }
        })
        .catch(error => {
          setError(error.message);
          addMessage('error', `Send error: ${error.message}`);
        });
    } catch (error) {
      setError(error.message);
      addMessage('error', `Error creating message: ${error.message}`);
    }
  };
  
  // Add message to list
  const addMessage = (type, content) => {
    const message = {
      id: Date.now(),
      type,
      content: typeof content === 'object' ? JSON.stringify(content, null, 2) : content,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, message]);
  };
  
  // Clear messages
  const clearMessages = () => {
    setMessages([]);
  };
  
  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
    };
  }, []);
  
  // Get connection state color
  const getConnectionStateColor = () => {
    switch (connectionState) {
      case 'OPEN':
        return '#52c41a'; // green
      case 'CONNECTING':
      case 'RECONNECTING':
        return '#faad14'; // yellow
      case 'CLOSING':
      case 'CLOSED':
      case 'FAILED':
        return '#f5222d'; // red
      default:
        return '#8c8c8c'; // gray
    }
  };
  
  // Get message type color
  const getMessageTypeColor = (type) => {
    switch (type) {
      case 'system':
        return '#8c8c8c'; // gray
      case 'sent':
        return '#1890ff'; // blue
      case 'received':
        return '#52c41a'; // green
      case 'error':
        return '#f5222d'; // red
      default:
        return '#8c8c8c'; // gray
    }
  };
  
  // Styles
  const styles = {
    container: {
      padding: '20px',
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
    },
    header: {
      marginBottom: '20px'
    },
    connectionControls: {
      display: 'flex',
      flexWrap: 'wrap',
      gap: '10px',
      marginBottom: '20px'
    },
    endpointSelector: {
      display: 'flex',
      flexDirection: 'column',
      gap: '5px',
      flex: '1 1 300px'
    },
    connectionButtons: {
      display: 'flex',
      gap: '10px',
      alignItems: 'flex-end',
      flex: '0 0 auto'
    },
    messagesContainer: {
      border: '1px solid #e8e8e8',
      borderRadius: '4px',
      height: '300px',
      overflowY: 'auto',
      padding: '10px',
      marginBottom: '20px',
      backgroundColor: '#f9f9f9'
    },
    message: {
      padding: '5px 10px',
      marginBottom: '5px',
      borderRadius: '4px',
      fontSize: '14px',
      fontFamily: 'monospace',
      whiteSpace: 'pre-wrap',
      wordBreak: 'break-word'
    },
    messageControls: {
      display: 'flex',
      flexWrap: 'wrap',
      gap: '10px',
      marginBottom: '20px'
    },
    messageTypeSelector: {
      display: 'flex',
      flexDirection: 'column',
      gap: '5px',
      flex: '0 0 200px'
    },
    messageInput: {
      display: 'flex',
      flexDirection: 'column',
      gap: '5px',
      flex: '1 1 300px'
    },
    sendButton: {
      display: 'flex',
      alignItems: 'flex-end',
      flex: '0 0 auto'
    },
    statusBar: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '10px',
      backgroundColor: '#f0f0f0',
      borderRadius: '4px'
    },
    connectionState: {
      display: 'flex',
      alignItems: 'center',
      gap: '5px'
    },
    connectionIndicator: {
      width: '10px',
      height: '10px',
      borderRadius: '50%',
      backgroundColor: getConnectionStateColor()
    },
    button: {
      padding: '8px 16px',
      backgroundColor: '#1890ff',
      color: 'white',
      border: 'none',
      borderRadius: '4px',
      cursor: 'pointer'
    },
    disabledButton: {
      padding: '8px 16px',
      backgroundColor: '#d9d9d9',
      color: 'rgba(0, 0, 0, 0.25)',
      border: 'none',
      borderRadius: '4px',
      cursor: 'not-allowed'
    },
    input: {
      padding: '8px',
      border: '1px solid #d9d9d9',
      borderRadius: '4px',
      width: '100%'
    },
    select: {
      padding: '8px',
      border: '1px solid #d9d9d9',
      borderRadius: '4px',
      width: '100%'
    },
    label: {
      fontWeight: 'bold',
      marginBottom: '5px'
    },
    error: {
      color: '#f5222d',
      marginBottom: '10px'
    },
    advancedToggle: {
      marginTop: '20px',
      textAlign: 'center'
    },
    advancedSection: {
      marginTop: '20px',
      padding: '15px',
      backgroundColor: '#f9f9f9',
      borderRadius: '4px',
      border: '1px solid #e8e8e8'
    }
  };
  
  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h2>WebSocket Connection Tester</h2>
        <p>Test WebSocket connections and diagnose issues</p>
      </div>
      
      {error && (
        <div style={styles.error}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      <div style={styles.connectionControls}>
        <div style={styles.endpointSelector}>
          <label style={styles.label}>
            <input
              type="checkbox"
              checked={useCustomEndpoint}
              onChange={(e) => setUseCustomEndpoint(e.target.checked)}
            />
            {' '}
            Use custom endpoint
          </label>
          
          {useCustomEndpoint ? (
            <input
              type="text"
              value={customEndpoint}
              onChange={(e) => setCustomEndpoint(e.target.value)}
              placeholder="ws://localhost:8000/ws/app_builder/"
              style={styles.input}
            />
          ) : (
            <select
              value={endpoint}
              onChange={(e) => setEndpoint(e.target.value)}
              style={styles.select}
            >
              {endpoints.map((ep) => (
                <option key={ep.value} value={ep.value}>
                  {ep.label} ({ep.value})
                </option>
              ))}
            </select>
          )}
        </div>
        
        <div style={styles.connectionButtons}>
          <button
            onClick={connect}
            disabled={connectionState === 'CONNECTING' || connectionState === 'RECONNECTING'}
            style={connectionState === 'CONNECTING' || connectionState === 'RECONNECTING' ? styles.disabledButton : styles.button}
          >
            Connect
          </button>
          
          <button
            onClick={disconnect}
            disabled={connectionState === 'CLOSED' || connectionState === 'FAILED'}
            style={connectionState === 'CLOSED' || connectionState === 'FAILED' ? styles.disabledButton : styles.button}
          >
            Disconnect
          </button>
        </div>
      </div>
      
      <div style={styles.messagesContainer}>
        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              ...styles.message,
              backgroundColor: getMessageTypeColor(message.type) + '10',
              borderLeft: `4px solid ${getMessageTypeColor(message.type)}`
            }}
          >
            <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '3px' }}>
              [{new Date(message.timestamp).toLocaleTimeString()}] [{message.type.toUpperCase()}]
            </div>
            {message.content}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      <div style={styles.messageControls}>
        <div style={styles.messageTypeSelector}>
          <label style={styles.label}>Message Type</label>
          <select
            value={messageType}
            onChange={(e) => setMessageType(e.target.value)}
            style={styles.select}
          >
            {messageTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
        
        {messageType === 'custom' && (
          <div style={styles.messageInput}>
            <label style={styles.label}>Message</label>
            <input
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              placeholder="Enter message or JSON"
              style={styles.input}
            />
          </div>
        )}
        
        <div style={styles.sendButton}>
          <button
            onClick={sendMessage}
            disabled={connectionState !== 'OPEN'}
            style={connectionState !== 'OPEN' ? styles.disabledButton : styles.button}
          >
            Send
          </button>
        </div>
        
        <div style={styles.sendButton}>
          <button
            onClick={clearMessages}
            style={{ ...styles.button, backgroundColor: '#8c8c8c' }}
          >
            Clear
          </button>
        </div>
      </div>
      
      <div style={styles.statusBar}>
        <div style={styles.connectionState}>
          <div style={styles.connectionIndicator} />
          <span>{connectionState}</span>
          {reconnectAttempt > 0 && (
            <span style={{ marginLeft: '10px', fontSize: '12px' }}>
              (Reconnect attempt: {reconnectAttempt})
            </span>
          )}
        </div>
        
        {latency !== null && (
          <div>
            <span>Latency: {latency}ms</span>
          </div>
        )}
      </div>
      
      <div style={styles.advancedToggle}>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          style={{ ...styles.button, backgroundColor: '#8c8c8c' }}
        >
          {showAdvanced ? 'Hide Advanced Options' : 'Show Advanced Options'}
        </button>
      </div>
      
      {showAdvanced && (
        <div style={styles.advancedSection}>
          <h3>WebSocket URL</h3>
          <p>
            {useCustomEndpoint
              ? customEndpoint
              : getWebSocketUrl(endpoint)}
          </p>
          
          <h3>Connection Information</h3>
          <p>
            <strong>State:</strong> {connectionState}<br />
            <strong>Reconnect Attempts:</strong> {reconnectAttempt}<br />
            <strong>Latency:</strong> {latency !== null ? `${latency}ms` : 'N/A'}
          </p>
        </div>
      )}
    </div>
  );
};

export default WebSocketConnectionTester;
