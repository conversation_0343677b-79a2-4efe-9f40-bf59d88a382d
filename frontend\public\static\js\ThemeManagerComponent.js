/**
 * ThemeManagerComponent.js
 *
 * A standalone theme manager component that can be loaded by the service worker.
 * This component allows users to create, edit, and manage themes.
 */

(function (window) {
  'use strict';

  // Default themes
  const DEFAULT_THEMES = [
    {
      id: 'default',
      name: 'Default Theme',
      primaryColor: '#1890ff',
      secondaryColor: '#52c41a',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      fontFamily: 'Inter, sans-serif'
    },
    {
      id: 'dark',
      name: 'Dark Theme',
      primaryColor: '#1890ff',
      secondaryColor: '#52c41a',
      backgroundColor: '#141414',
      textColor: '#ffffff',
      fontFamily: 'Inter, sans-serif'
    },
    {
      id: 'blue',
      name: 'Blue Theme',
      primaryColor: '#0050b3',
      secondaryColor: '#52c41a',
      backgroundColor: '#e6f7ff',
      textColor: '#000000',
      fontFamily: 'Inter, sans-serif'
    },
    {
      id: 'high-contrast',
      name: 'High Contrast Theme',
      primaryColor: '#ffff00',
      secondaryColor: '#00ff00',
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'Inter, sans-serif'
    }
  ];

  // Theme Manager Class
  class ThemeManager {
    constructor(options = {}) {
      this.themes = [...DEFAULT_THEMES];
      this.activeTheme = 'default';
      this.container = null;
      this.options = {
        storageKey: 'app-builder-themes',
        activeThemeKey: 'app-builder-active-theme',
        ...options
      };

      // Load saved themes from localStorage
      this.loadThemes();
    }

    // Initialize the theme manager
    init(containerId) {
      this.container = document.getElementById(containerId);
      if (!this.container) {
        console.error(`ThemeManager: Container with ID "${containerId}" not found`);
        return;
      }

      this.render();
      this.applyActiveTheme();
    }

    // Load themes from localStorage
    loadThemes() {
      try {
        const savedThemes = localStorage.getItem(this.options.storageKey);
        if (savedThemes) {
          this.themes = JSON.parse(savedThemes);
        }

        const activeTheme = localStorage.getItem(this.options.activeThemeKey);
        if (activeTheme) {
          this.activeTheme = activeTheme;
        }
      } catch (error) {
        console.error('ThemeManager: Error loading themes from localStorage', error);
      }
    }

    // Save themes to localStorage
    saveThemes() {
      try {
        localStorage.setItem(this.options.storageKey, JSON.stringify(this.themes));
        localStorage.setItem(this.options.activeThemeKey, this.activeTheme);
      } catch (error) {
        console.error('ThemeManager: Error saving themes to localStorage', error);
      }
    }

    // Add a new theme
    addTheme(theme) {
      if (!theme.id) {
        theme.id = 'theme-' + Date.now();
      }

      // Check if theme with this ID already exists
      const existingThemeIndex = this.themes.findIndex(t => t.id === theme.id);
      if (existingThemeIndex >= 0) {
        this.themes[existingThemeIndex] = theme;
      } else {
        this.themes.push(theme);
      }

      this.saveThemes();
      this.render();
    }

    // Remove a theme
    removeTheme(themeId) {
      // Don't allow removing the default theme
      if (themeId === 'default') {
        console.warn('ThemeManager: Cannot remove default theme');
        return;
      }

      this.themes = this.themes.filter(theme => theme.id !== themeId);

      // If removing the active theme, set active theme to default
      if (this.activeTheme === themeId) {
        this.activeTheme = 'default';
      }

      this.saveThemes();
      this.render();
      this.applyActiveTheme();
    }

    // Set the active theme
    setActiveTheme(themeId) {
      const theme = this.themes.find(t => t.id === themeId);
      if (!theme) {
        console.error(`ThemeManager: Theme with ID "${themeId}" not found`);
        return;
      }

      this.activeTheme = themeId;
      this.saveThemes();
      this.applyActiveTheme();
      this.render();
    }

    // Apply the active theme to the document
    applyActiveTheme() {
      const theme = this.themes.find(t => t.id === this.activeTheme);
      if (!theme) {
        console.error(`ThemeManager: Active theme "${this.activeTheme}" not found`);
        return;
      }

      // Apply theme to document
      document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
      document.documentElement.style.setProperty('--secondary-color', theme.secondaryColor);
      document.documentElement.style.setProperty('--background-color', theme.backgroundColor);
      document.documentElement.style.setProperty('--text-color', theme.textColor);
      document.documentElement.style.setProperty('--font-family', theme.fontFamily);

      // Set data attribute for CSS selectors
      document.documentElement.setAttribute('data-theme', theme.id);

      // Dispatch event for other components
      window.dispatchEvent(new CustomEvent('themechange', { detail: theme }));
    }

    // Render the theme manager UI
    render() {
      if (!this.container) return;

      // Clear container
      this.container.innerHTML = '';

      // Create theme manager UI
      const themeManager = document.createElement('div');
      themeManager.className = 'theme-manager';

      // Create theme selector
      const themeSelector = document.createElement('div');
      themeSelector.className = 'theme-selector';

      const selectorLabel = document.createElement('label');
      selectorLabel.textContent = 'Select Theme:';
      themeSelector.appendChild(selectorLabel);

      const select = document.createElement('select');
      select.className = 'theme-select';
      select.addEventListener('change', (e) => {
        this.setActiveTheme(e.target.value);
      });

      this.themes.forEach(theme => {
        const option = document.createElement('option');
        option.value = theme.id;
        option.textContent = theme.name;
        option.selected = theme.id === this.activeTheme;
        select.appendChild(option);
      });

      themeSelector.appendChild(select);
      themeManager.appendChild(themeSelector);

      // Create theme list
      const themeList = document.createElement('div');
      themeList.className = 'theme-list';

      this.themes.forEach(theme => {
        const themeCard = document.createElement('div');
        themeCard.className = `theme-card ${theme.id === this.activeTheme ? 'active' : ''}`;

        // Theme card header
        const cardHeader = document.createElement('div');
        cardHeader.className = 'theme-card-header';

        const cardTitle = document.createElement('h3');
        cardTitle.className = 'theme-card-title';
        cardTitle.textContent = theme.name;
        cardHeader.appendChild(cardTitle);

        // Theme card actions
        const cardActions = document.createElement('div');
        cardActions.className = 'theme-card-actions';

        // Apply button
        const applyBtn = document.createElement('button');
        applyBtn.className = 'theme-card-action';
        applyBtn.textContent = 'Apply';
        applyBtn.addEventListener('click', () => {
          this.setActiveTheme(theme.id);
        });
        cardActions.appendChild(applyBtn);

        // Edit button
        const editBtn = document.createElement('button');
        editBtn.className = 'theme-card-action';
        editBtn.textContent = 'Edit';
        editBtn.addEventListener('click', () => {
          this.showThemeEditor(theme);
        });
        cardActions.appendChild(editBtn);

        // Delete button (only for non-default themes)
        if (theme.id !== 'default') {
          const deleteBtn = document.createElement('button');
          deleteBtn.className = 'theme-card-action';
          deleteBtn.textContent = 'Delete';
          deleteBtn.addEventListener('click', () => {
            if (confirm(`Are you sure you want to delete the theme "${theme.name}"?`)) {
              this.removeTheme(theme.id);
            }
          });
          cardActions.appendChild(deleteBtn);
        }

        cardHeader.appendChild(cardActions);
        themeCard.appendChild(cardHeader);

        // Theme card colors
        const cardColors = document.createElement('div');
        cardColors.className = 'theme-card-colors';

        // Primary color
        const primaryColor = document.createElement('div');
        primaryColor.className = 'color-preview';
        primaryColor.style.backgroundColor = theme.primaryColor;
        primaryColor.title = 'Primary Color';
        cardColors.appendChild(primaryColor);

        // Secondary color
        const secondaryColor = document.createElement('div');
        secondaryColor.className = 'color-preview';
        secondaryColor.style.backgroundColor = theme.secondaryColor;
        secondaryColor.title = 'Secondary Color';
        cardColors.appendChild(secondaryColor);

        // Background color
        const backgroundColor = document.createElement('div');
        backgroundColor.className = 'color-preview';
        backgroundColor.style.backgroundColor = theme.backgroundColor;
        backgroundColor.title = 'Background Color';
        cardColors.appendChild(backgroundColor);

        // Text color
        const textColor = document.createElement('div');
        textColor.className = 'color-preview';
        textColor.style.backgroundColor = theme.textColor;
        textColor.title = 'Text Color';
        cardColors.appendChild(textColor);

        themeCard.appendChild(cardColors);
        themeList.appendChild(themeCard);
      });

      themeManager.appendChild(themeList);

      // Create "Add New Theme" button
      const addThemeBtn = document.createElement('button');
      addThemeBtn.className = 'preview-button';
      addThemeBtn.textContent = 'Add New Theme';
      addThemeBtn.addEventListener('click', () => {
        this.showThemeEditor();
      });
      themeManager.appendChild(addThemeBtn);

      // Add theme manager to container
      this.container.appendChild(themeManager);
    }

    // Show theme editor
    showThemeEditor(theme = null) {
      // Create modal backdrop
      const backdrop = document.createElement('div');
      backdrop.className = 'theme-editor-backdrop';
      backdrop.addEventListener('click', (e) => {
        if (e.target === backdrop) {
          document.body.removeChild(backdrop);
        }
      });

      // Create editor modal
      const modal = document.createElement('div');
      modal.className = 'theme-editor-modal';

      // Modal header
      const modalHeader = document.createElement('div');
      modalHeader.className = 'theme-editor-header';

      const modalTitle = document.createElement('h2');
      modalTitle.textContent = theme ? `Edit Theme: ${theme.name}` : 'Create New Theme';
      modalHeader.appendChild(modalTitle);

      const closeBtn = document.createElement('button');
      closeBtn.className = 'theme-editor-close';
      closeBtn.textContent = '×';
      closeBtn.addEventListener('click', () => {
        document.body.removeChild(backdrop);
      });
      modalHeader.appendChild(closeBtn);

      modal.appendChild(modalHeader);

      // Modal content
      const modalContent = document.createElement('div');
      modalContent.className = 'theme-editor-content';

      // Create form
      const form = document.createElement('form');
      form.className = 'theme-editor-form';
      form.addEventListener('submit', (e) => {
        e.preventDefault();

        const newTheme = {
          id: theme ? theme.id : `theme-${Date.now()}`,
          name: form.elements.themeName.value,
          primaryColor: form.elements.primaryColor.value,
          secondaryColor: form.elements.secondaryColor.value,
          backgroundColor: form.elements.backgroundColor.value,
          textColor: form.elements.textColor.value,
          fontFamily: form.elements.fontFamily.value
        };

        this.addTheme(newTheme);
        document.body.removeChild(backdrop);
      });

      // Theme name
      const nameGroup = document.createElement('div');
      nameGroup.className = 'form-group';

      const nameLabel = document.createElement('label');
      nameLabel.textContent = 'Theme Name';
      nameLabel.htmlFor = 'themeName';
      nameGroup.appendChild(nameLabel);

      const nameInput = document.createElement('input');
      nameInput.type = 'text';
      nameInput.id = 'themeName';
      nameInput.name = 'themeName';
      nameInput.required = true;
      nameInput.value = theme ? theme.name : '';
      nameGroup.appendChild(nameInput);

      form.appendChild(nameGroup);

      // Primary color
      const primaryGroup = document.createElement('div');
      primaryGroup.className = 'form-group';

      const primaryLabel = document.createElement('label');
      primaryLabel.textContent = 'Primary Color';
      primaryLabel.htmlFor = 'primaryColor';
      primaryGroup.appendChild(primaryLabel);

      const primaryInput = document.createElement('input');
      primaryInput.type = 'color';
      primaryInput.id = 'primaryColor';
      primaryInput.name = 'primaryColor';
      primaryInput.value = theme ? theme.primaryColor : '#1890ff';
      primaryGroup.appendChild(primaryInput);

      form.appendChild(primaryGroup);

      // Secondary color
      const secondaryGroup = document.createElement('div');
      secondaryGroup.className = 'form-group';

      const secondaryLabel = document.createElement('label');
      secondaryLabel.textContent = 'Secondary Color';
      secondaryLabel.htmlFor = 'secondaryColor';
      secondaryGroup.appendChild(secondaryLabel);

      const secondaryInput = document.createElement('input');
      secondaryInput.type = 'color';
      secondaryInput.id = 'secondaryColor';
      secondaryInput.name = 'secondaryColor';
      secondaryInput.value = theme ? theme.secondaryColor : '#52c41a';
      secondaryGroup.appendChild(secondaryInput);

      form.appendChild(secondaryGroup);

      // Background color
      const bgGroup = document.createElement('div');
      bgGroup.className = 'form-group';

      const bgLabel = document.createElement('label');
      bgLabel.textContent = 'Background Color';
      bgLabel.htmlFor = 'backgroundColor';
      bgGroup.appendChild(bgLabel);

      const bgInput = document.createElement('input');
      bgInput.type = 'color';
      bgInput.id = 'backgroundColor';
      bgInput.name = 'backgroundColor';
      bgInput.value = theme ? theme.backgroundColor : '#ffffff';
      bgGroup.appendChild(bgInput);

      form.appendChild(bgGroup);

      // Text color
      const textGroup = document.createElement('div');
      textGroup.className = 'form-group';

      const textLabel = document.createElement('label');
      textLabel.textContent = 'Text Color';
      textLabel.htmlFor = 'textColor';
      textGroup.appendChild(textLabel);

      const textInput = document.createElement('input');
      textInput.type = 'color';
      textInput.id = 'textColor';
      textInput.name = 'textColor';
      textInput.value = theme ? theme.textColor : '#000000';
      textGroup.appendChild(textInput);

      form.appendChild(textGroup);

      // Font family
      const fontGroup = document.createElement('div');
      fontGroup.className = 'form-group';

      const fontLabel = document.createElement('label');
      fontLabel.textContent = 'Font Family';
      fontLabel.htmlFor = 'fontFamily';
      fontGroup.appendChild(fontLabel);

      const fontInput = document.createElement('input');
      fontInput.type = 'text';
      fontInput.id = 'fontFamily';
      fontInput.name = 'fontFamily';
      fontInput.value = theme ? theme.fontFamily : 'Inter, sans-serif';
      fontGroup.appendChild(fontInput);

      form.appendChild(fontGroup);

      // Submit button
      const submitBtn = document.createElement('button');
      submitBtn.type = 'submit';
      submitBtn.className = 'preview-button';
      submitBtn.textContent = theme ? 'Update Theme' : 'Create Theme';
      form.appendChild(submitBtn);

      modalContent.appendChild(form);
      modal.appendChild(modalContent);
      backdrop.appendChild(modal);

      document.body.appendChild(backdrop);
    }
  }

  // Expose ThemeManager to window
  window.ThemeManager = ThemeManager;
})(window);
