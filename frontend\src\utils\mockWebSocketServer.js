/**
 * Mock WebSocket Server
 * 
 * This module provides a mock WebSocket server for development and testing.
 * It simulates WebSocket connections and messages when the backend is not available.
 */

// Mock WebSocket class
class MockWebSocket extends EventTarget {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  constructor(url) {
    super();
    this.url = url;
    this.readyState = MockWebSocket.CONNECTING;
    this.protocol = '';
    this.extensions = '';
    this.bufferedAmount = 0;
    this.binaryType = 'blob';
    
    // Simulate connection delay
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      
      // Dispatch open event
      const openEvent = new Event('open');
      this.dispatchEvent(openEvent);
      if (typeof this.onopen === 'function') {
        this.onopen(openEvent);
      }
      
      console.log(`Mock WebSocket connected to ${url}`);
    }, 500);
  }
  
  // Send method
  send(data) {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    
    console.log(`Mock WebSocket sending data:`, data);
    
    // Parse the data
    let parsedData;
    try {
      parsedData = typeof data === 'string' ? JSON.parse(data) : data;
    } catch (error) {
      parsedData = data;
    }
    
    // Handle different message types
    setTimeout(() => {
      if (parsedData.type === 'ping') {
        this._handlePing(parsedData);
      } else if (parsedData.type === 'request_app_data') {
        this._handleAppDataRequest(parsedData);
      } else {
        this._handleGenericMessage(parsedData);
      }
    }, 200);
  }
  
  // Close method
  close(code = 1000, reason = '') {
    if (this.readyState === MockWebSocket.CLOSED) {
      return;
    }
    
    this.readyState = MockWebSocket.CLOSING;
    
    // Simulate closing delay
    setTimeout(() => {
      this.readyState = MockWebSocket.CLOSED;
      
      // Dispatch close event
      const closeEvent = new CloseEvent('close', {
        code,
        reason,
        wasClean: code === 1000
      });
      this.dispatchEvent(closeEvent);
      if (typeof this.onclose === 'function') {
        this.onclose(closeEvent);
      }
      
      console.log(`Mock WebSocket closed: ${code} ${reason}`);
    }, 100);
  }
  
  // Handle ping message
  _handlePing(data) {
    // Respond with pong
    const response = {
      type: 'pong',
      timestamp: new Date().toISOString(),
      originalTimestamp: data.timestamp,
      server: 'MockWebSocketServer'
    };
    
    this._sendMessage(response);
  }
  
  // Handle app data request
  _handleAppDataRequest(data) {
    // Respond with mock app data
    const response = {
      type: 'app_data',
      data: {
        app: {
          name: 'App Builder',
          version: '1.0.0',
          components: [
            { id: 1, type: 'Button', props: { text: 'Click Me', variant: 'primary' } },
            { id: 2, type: 'Input', props: { placeholder: 'Enter text', label: 'Name' } },
            { id: 3, type: 'Text', props: { content: 'Hello World', style: { fontWeight: 'bold' } } }
          ],
          layouts: [
            { id: 1, type: 'Grid', components: [1, 2], styles: { gap: '10px' } },
            { id: 2, type: 'Flex', components: [3], styles: { justifyContent: 'center' } }
          ],
          styles: {
            '.container': { display: 'flex', flexDirection: 'column', gap: '20px' },
            '.header': { fontSize: '24px', fontWeight: 'bold', marginBottom: '16px' }
          },
          status: 'online'
        },
        _meta: {
          source: 'mock-websocket',
          timestamp: new Date().toISOString(),
          requestId: data.id || data.timestamp
        }
      },
      timestamp: new Date().toISOString()
    };
    
    this._sendMessage(response);
  }
  
  // Handle generic message
  _handleGenericMessage(data) {
    // Echo the message back
    const response = {
      type: 'echo',
      originalMessage: data,
      timestamp: new Date().toISOString(),
      server: 'MockWebSocketServer'
    };
    
    this._sendMessage(response);
  }
  
  // Send a message to the client
  _sendMessage(data) {
    const messageData = JSON.stringify(data);
    
    // Create message event
    const messageEvent = new MessageEvent('message', {
      data: messageData,
      origin: this.url,
      lastEventId: '',
      source: null,
      ports: []
    });
    
    // Dispatch message event
    this.dispatchEvent(messageEvent);
    if (typeof this.onmessage === 'function') {
      this.onmessage(messageEvent);
    }
    
    console.log(`Mock WebSocket received data:`, data);
  }
}

/**
 * Initialize the mock WebSocket server
 * @param {Object} options - Configuration options
 * @param {boolean} options.enabled - Whether the mock server is enabled
 * @param {boolean} options.logMessages - Whether to log messages
 */
export function initMockWebSocketServer(options = {}) {
  const {
    enabled = process.env.NODE_ENV === 'development',
    logMessages = true
  } = options;

  if (!enabled) {
    console.log('Mock WebSocket server is disabled');
    return;
  }

  console.log('Initializing mock WebSocket server...');

  // Store the original WebSocket class
  window._originalWebSocket = window.WebSocket;

  // Override the WebSocket class
  window.WebSocket = MockWebSocket;

  console.log('Mock WebSocket server initialized');
}

/**
 * Disable the mock WebSocket server
 */
export function disableMockWebSocketServer() {
  // Restore the original WebSocket class if it exists
  if (window._originalWebSocket) {
    window.WebSocket = window._originalWebSocket;
    console.log('Mock WebSocket server disabled');
  }
}

export default {
  initMockWebSocketServer,
  disableMockWebSocketServer,
  MockWebSocket
};
