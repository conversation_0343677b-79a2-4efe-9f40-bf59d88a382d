/**
 * End-to-End tests for AI-assisted design workflow
 * Tests the complete user journey with AI features
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import main components
import AppBuilderEnhanced from '../../pages/AppBuilderEnhanced';

// Mock services with realistic responses
jest.mock('../../services/aiDesignService', () => ({
  generateLayoutSuggestions: jest.fn().mockResolvedValue({
    suggestions: [
      {
        id: 'grid_layout',
        name: 'Grid Layout',
        description: 'Responsive grid system for organizing content',
        score: 85,
        explanation: 'Grid layout works well for organizing many components',
        structure: { display: 'grid', gap: '16px' },
        use_cases: ['portfolio', 'gallery', 'product_catalog']
      },
      {
        id: 'header_footer',
        name: 'Header-Footer Layout',
        description: 'Classic layout with header, main content, and footer',
        score: 75,
        explanation: 'Traditional layout suitable for most applications',
        structure: { header: true, footer: true },
        use_cases: ['landing_page', 'blog', 'corporate_site']
      }
    ],
    status: 'success'
  }),
  generateComponentCombinations: jest.fn().mockResolvedValue({
    suggestions: [
      {
        id: 'form_pattern',
        name: 'Form with Validation',
        description: 'Complete form setup with validation and submit button',
        score: 90,
        explanation: 'Add input and button to complete this form pattern',
        components: ['form', 'input', 'button'],
        missing_components: ['input'],
        use_cases: ['contact_form', 'registration', 'survey']
      }
    ],
    status: 'success'
  }),
  analyzeAppStructure: jest.fn().mockResolvedValue({
    analysis: {
      component_count: 3,
      component_types: { button: 1, text: 1, form: 1 },
      has_navigation: false,
      has_forms: true,
      has_media: false,
      complexity_score: 8,
      app_type: 'form_heavy'
    },
    status: 'success'
  }),
  clearCache: jest.fn()
}));

jest.mock('../../services/aiWebSocketService', () => ({
  connect: jest.fn().mockResolvedValue(),
  disconnect: jest.fn(),
  requestLayoutSuggestions: jest.fn(),
  requestComponentCombinations: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  getStatus: jest.fn(() => ({ connected: true }))
}));

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: (state = {
        components: [],
        layouts: [],
        styles: {},
        currentApp: null
      }, action) => {
        switch (action.type) {
          case 'ADD_COMPONENT':
            return {
              ...state,
              components: [...state.components, {
                id: `comp-${Date.now()}`,
                type: action.payload.type || action.payload,
                props: action.payload.props || {}
              }]
            };
          case 'APPLY_AI_LAYOUT':
            return {
              ...state,
              layouts: [...state.layouts, action.payload]
            };
          case 'APPLY_AI_COMBINATION':
            return {
              ...state,
              components: [...state.components, ...action.payload.components]
            };
          default:
            return state;
        }
      },
      ui: (state = {
        selectedComponent: null,
        aiSuggestionsVisible: false
      }, action) => {
        switch (action.type) {
          case 'SELECT_COMPONENT':
            return { ...state, selectedComponent: action.payload };
          case 'TOGGLE_AI_SUGGESTIONS':
            return { ...state, aiSuggestionsVisible: !state.aiSuggestionsVisible };
          default:
            return state;
        }
      }
    },
    preloadedState: initialState
  });
};

describe('AI-Assisted Design Workflow E2E Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
  });

  describe('Complete AI Workflow', () => {
    test('user can complete full AI-assisted app building workflow', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Step 1: User starts with empty app
      await waitFor(() => {
        expect(screen.getByText(/app builder/i)).toBeInTheDocument();
      });

      // Step 2: User adds first component
      const buttonComponent = screen.getByText('Button');
      fireEvent.click(buttonComponent);

      // Step 3: AI suggestions should appear
      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(1);
      });

      // Step 4: User adds more components
      const textComponent = screen.getByText('Text');
      fireEvent.click(textComponent);

      const formComponent = screen.getByText('Form');
      fireEvent.click(formComponent);

      // Step 5: AI should provide layout suggestions
      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(3);
      });

      // Step 6: User can view AI suggestions
      // (This would involve clicking AI assistant button or panel)

      // Verify final state
      expect(store.getState().app.components).toHaveLength(3);
    });

    test('AI suggestions adapt to user selections', async () => {
      const storeWithForm = createMockStore({
        app: {
          components: [
            { id: '1', type: 'form', props: {} }
          ],
          layouts: [],
          styles: {}
        },
        ui: {
          selectedComponent: { id: '1', type: 'form', props: {} }
        }
      });

      render(
        <Provider store={storeWithForm}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // AI should suggest form-related components
      await waitFor(() => {
        expect(storeWithForm.getState().app.components).toHaveLength(1);
        expect(storeWithForm.getState().ui.selectedComponent.type).toBe('form');
      });
    });

    test('user can apply AI layout suggestions', async () => {
      const storeWithComponents = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: {} },
            { id: '2', type: 'text', props: {} },
            { id: '3', type: 'card', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Simulate applying AI layout suggestion
      store.dispatch({
        type: 'APPLY_AI_LAYOUT',
        payload: {
          id: 'grid_layout',
          name: 'Grid Layout',
          applied: true
        }
      });

      await waitFor(() => {
        expect(store.getState().app.layouts).toHaveLength(1);
      });
    });

    test('user can apply AI component combinations', async () => {
      const storeWithForm = createMockStore({
        app: {
          components: [
            { id: '1', type: 'form', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithForm}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Simulate applying AI component combination
      store.dispatch({
        type: 'APPLY_AI_COMBINATION',
        payload: {
          components: [
            { id: 'new-input', type: 'input', props: {} }
          ]
        }
      });

      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(2);
      });
    });
  });

  describe('AI Assistant Interaction', () => {
    test('AI assistant button shows and hides suggestions panel', async () => {
      const storeWithComponents = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Look for AI-related buttons or panels
      await waitFor(() => {
        const buttons = screen.getAllByRole('button');
        expect(buttons.length).toBeGreaterThan(0);
      });
    });

    test('AI suggestions update in real-time', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Add component
      const buttonComponent = screen.getByText('Button');
      fireEvent.click(buttonComponent);

      // Add another component
      const textComponent = screen.getByText('Text');
      fireEvent.click(textComponent);

      // AI suggestions should update
      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(2);
      });
    });
  });

  describe('Error Handling and Fallbacks', () => {
    test('app continues to work when AI services fail', async () => {
      // Mock AI service failure
      const aiDesignService = require('../../services/aiDesignService');
      aiDesignService.generateLayoutSuggestions.mockRejectedValue(new Error('AI Service Error'));

      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // App should still work
      await waitFor(() => {
        expect(screen.getByText(/app builder/i)).toBeInTheDocument();
      });

      // User can still add components manually
      const buttonComponent = screen.getByText('Button');
      fireEvent.click(buttonComponent);

      expect(store.getState().app.components).toHaveLength(1);
    });

    test('fallback suggestions are provided when AI is unavailable', async () => {
      // Mock AI service to return fallback
      const aiDesignService = require('../../services/aiDesignService');
      aiDesignService.generateLayoutSuggestions.mockResolvedValue({
        suggestions: [
          {
            id: 'simple_flex',
            name: 'Simple Flexbox Layout',
            description: 'Basic vertical layout for simple apps',
            score: 80,
            explanation: 'Perfect for apps with few components'
          }
        ],
        status: 'fallback'
      });

      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Add components to trigger suggestions
      const buttonComponent = screen.getByText('Button');
      fireEvent.click(buttonComponent);

      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(1);
      });
    });
  });

  describe('Performance and Responsiveness', () => {
    test('AI features do not block user interactions', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Rapidly add multiple components
      const buttonComponent = screen.getByText('Button');
      const textComponent = screen.getByText('Text');
      const formComponent = screen.getByText('Form');

      fireEvent.click(buttonComponent);
      fireEvent.click(textComponent);
      fireEvent.click(formComponent);

      // All components should be added despite AI processing
      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(3);
      });
    });

    test('app remains responsive during AI processing', async () => {
      // Mock slow AI response
      const aiDesignService = require('../../services/aiDesignService');
      aiDesignService.generateLayoutSuggestions.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            suggestions: [],
            status: 'success'
          }), 2000)
        )
      );

      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Add component to trigger AI processing
      const buttonComponent = screen.getByText('Button');
      fireEvent.click(buttonComponent);

      // App should remain responsive
      const textComponent = screen.getByText('Text');
      fireEvent.click(textComponent);

      // Both components should be added
      await waitFor(() => {
        expect(store.getState().app.components).toHaveLength(2);
      });
    });
  });

  describe('Accessibility', () => {
    test('AI features are accessible via keyboard navigation', async () => {
      render(
        <Provider store={store}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Test keyboard navigation
      const focusableElements = screen.getAllByRole('button');
      
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
        expect(document.activeElement).toBe(focusableElements[0]);

        // Test tab navigation
        fireEvent.keyDown(document.activeElement, { key: 'Tab' });
      }
    });

    test('AI suggestions have proper screen reader support', async () => {
      const storeWithComponents = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: {} }
          ],
          layouts: [],
          styles: {}
        }
      });

      render(
        <Provider store={storeWithComponents}>
          <BrowserRouter>
            <AppBuilderEnhanced />
          </BrowserRouter>
        </Provider>
      );

      // Check for ARIA labels and roles
      await waitFor(() => {
        const buttons = screen.getAllByRole('button');
        buttons.forEach(button => {
          expect(button).toBeInTheDocument();
        });
      });
    });
  });
});
