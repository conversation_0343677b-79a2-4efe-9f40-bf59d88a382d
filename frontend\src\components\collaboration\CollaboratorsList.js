/**
 * Collaborators List Component
 * 
 * Displays a list of active collaborators with their presence status
 */

import React, { useState } from 'react';
import { Avatar, Badge, Tooltip, Popover, Typography, Space, Tag } from 'antd';
import { UserOutlined, EyeOutlined, EditOutlined, CursorOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Text } = Typography;

const CollaboratorsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
`;

const CollaboratorAvatar = styled(Avatar)`
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid ${props => props.statusColor || '#d9d9d9'};
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
`;

const CollaboratorInfo = styled.div`
  padding: 12px;
  min-width: 200px;
`;

const PresenceIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => props.color || '#d9d9d9'};
`;

const CollaboratorsList = ({ 
  collaborators = [], 
  currentUserId = null,
  showPresence = true,
  maxVisible = 5,
  size = 'default'
}) => {
  const [hoveredCollaborator, setHoveredCollaborator] = useState(null);

  /**
   * Get status color based on presence
   */
  const getStatusColor = (collaborator) => {
    if (!collaborator.presence) return '#d9d9d9';
    
    const { status, lastSeen } = collaborator.presence;
    const now = Date.now();
    const timeDiff = now - (lastSeen || 0);
    
    if (status === 'active' && timeDiff < 30000) { // Active within 30 seconds
      return '#52c41a'; // Green
    } else if (status === 'idle' || timeDiff < 300000) { // Idle or within 5 minutes
      return '#faad14'; // Yellow
    } else {
      return '#f5222d'; // Red (away/offline)
    }
  };

  /**
   * Get status text
   */
  const getStatusText = (collaborator) => {
    if (!collaborator.presence) return 'Unknown';
    
    const { status, activity, lastSeen } = collaborator.presence;
    const now = Date.now();
    const timeDiff = now - (lastSeen || 0);
    
    if (status === 'active' && timeDiff < 30000) {
      return activity || 'Active';
    } else if (status === 'idle' || timeDiff < 300000) {
      return 'Idle';
    } else {
      return 'Away';
    }
  };

  /**
   * Get activity icon
   */
  const getActivityIcon = (collaborator) => {
    if (!collaborator.presence || !collaborator.presence.activity) {
      return <EyeOutlined />;
    }
    
    const activity = collaborator.presence.activity;
    
    if (activity.includes('editing') || activity.includes('typing')) {
      return <EditOutlined />;
    } else if (activity.includes('cursor') || activity.includes('selecting')) {
      return <CursorOutlined />;
    } else {
      return <EyeOutlined />;
    }
  };

  /**
   * Format last seen time
   */
  const formatLastSeen = (lastSeen) => {
    if (!lastSeen) return 'Unknown';
    
    const now = Date.now();
    const diff = now - lastSeen;
    
    if (diff < 60000) { // Less than 1 minute
      return 'Just now';
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000);
      return `${minutes}m ago`;
    } else if (diff < 86400000) { // Less than 1 day
      const hours = Math.floor(diff / 3600000);
      return `${hours}h ago`;
    } else {
      const days = Math.floor(diff / 86400000);
      return `${days}d ago`;
    }
  };

  /**
   * Render collaborator popover content
   */
  const renderCollaboratorInfo = (collaborator) => (
    <CollaboratorInfo>
      <Space direction="vertical" size="small">
        <div>
          <Text strong>{collaborator.username}</Text>
          {collaborator.id === currentUserId && (
            <Tag color="blue" size="small" style={{ marginLeft: 8 }}>You</Tag>
          )}
        </div>
        
        {showPresence && (
          <PresenceIndicator>
            <StatusDot color={getStatusColor(collaborator)} />
            {getActivityIcon(collaborator)}
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {getStatusText(collaborator)}
            </Text>
          </PresenceIndicator>
        )}
        
        {collaborator.presence && collaborator.presence.lastSeen && (
          <Text type="secondary" style={{ fontSize: '11px' }}>
            Last seen: {formatLastSeen(collaborator.presence.lastSeen)}
          </Text>
        )}
        
        {collaborator.email && (
          <Text type="secondary" style={{ fontSize: '11px' }}>
            {collaborator.email}
          </Text>
        )}
      </Space>
    </CollaboratorInfo>
  );

  /**
   * Render collaborator avatar
   */
  const renderCollaboratorAvatar = (collaborator, index) => {
    const statusColor = getStatusColor(collaborator);
    const isCurrentUser = collaborator.id === currentUserId;
    
    return (
      <Popover
        key={collaborator.id}
        content={renderCollaboratorInfo(collaborator)}
        title={null}
        trigger="hover"
        placement="bottom"
      >
        <Badge
          dot
          status={statusColor === '#52c41a' ? 'success' : statusColor === '#faad14' ? 'warning' : 'error'}
          offset={[-2, 2]}
        >
          <CollaboratorAvatar
            size={size}
            statusColor={statusColor}
            src={collaborator.avatar}
            icon={<UserOutlined />}
            style={{
              backgroundColor: isCurrentUser ? '#1890ff' : undefined,
              zIndex: maxVisible - index // Stack avatars properly
            }}
            onMouseEnter={() => setHoveredCollaborator(collaborator.id)}
            onMouseLeave={() => setHoveredCollaborator(null)}
          >
            {!collaborator.avatar && collaborator.username?.charAt(0)?.toUpperCase()}
          </CollaboratorAvatar>
        </Badge>
      </Popover>
    );
  };

  // Filter out current user from visible collaborators if needed
  const visibleCollaborators = collaborators.slice(0, maxVisible);
  const hiddenCount = Math.max(0, collaborators.length - maxVisible);

  if (collaborators.length === 0) {
    return null;
  }

  return (
    <CollaboratorsContainer>
      <Space size="small" align="center">
        <Text type="secondary" style={{ fontSize: '12px', marginRight: 4 }}>
          {collaborators.length === 1 ? '1 collaborator' : `${collaborators.length} collaborators`}
        </Text>
        
        {visibleCollaborators.map((collaborator, index) => 
          renderCollaboratorAvatar(collaborator, index)
        )}
        
        {hiddenCount > 0 && (
          <Tooltip title={`${hiddenCount} more collaborator${hiddenCount > 1 ? 's' : ''}`}>
            <Avatar size={size} style={{ backgroundColor: '#f0f0f0', color: '#666' }}>
              +{hiddenCount}
            </Avatar>
          </Tooltip>
        )}
      </Space>
    </CollaboratorsContainer>
  );
};

export default CollaboratorsList;
