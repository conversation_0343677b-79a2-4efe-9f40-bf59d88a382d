<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        #log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Direct WebSocket Test</h1>
    
    <div class="card">
        <h2>WebSocket Connection Test</h2>
        <p>This page tests a direct WebSocket connection to the backend server.</p>
        <div id="log"></div>
    </div>
    
    <script>
        const logDiv = document.getElementById('log');
        
        // Log a message to the log div
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = type;
            
            const timestamp = new Date().toISOString();
            entry.innerHTML = `<span>[${timestamp}]</span> ${message}`;
            
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Test WebSocket connection
        function testWebSocket() {
            log('Starting WebSocket connection test...');
            
            // Test different WebSocket URLs
            const urls = [
                'ws://localhost:8000/ws/',
                'ws://localhost:8000/ws/test/',
                'ws://localhost:8000/ws/echo/',
                'ws://localhost:8000/ws/simple/',
                'ws://127.0.0.1:8000/ws/',
                'ws://127.0.0.1:8000/ws/test/'
            ];
            
            let successCount = 0;
            let failCount = 0;
            
            urls.forEach((url, index) => {
                setTimeout(() => {
                    log(`Testing connection to ${url}...`);
                    
                    try {
                        const socket = new WebSocket(url);
                        
                        socket.onopen = function() {
                            log(`✅ Successfully connected to ${url}`, 'success');
                            successCount++;
                            
                            // Send a test message
                            try {
                                socket.send(JSON.stringify({
                                    type: 'ping',
                                    timestamp: new Date().toISOString()
                                }));
                                log(`Sent ping message to ${url}`, 'success');
                            } catch (e) {
                                log(`Error sending message to ${url}: ${e.message}`, 'error');
                            }
                            
                            // Close after 2 seconds
                            setTimeout(() => {
                                socket.close(1000, 'Test completed');
                            }, 2000);
                        };
                        
                        socket.onmessage = function(event) {
                            try {
                                const data = JSON.parse(event.data);
                                log(`Received message from ${url}: ${JSON.stringify(data)}`, 'success');
                            } catch (e) {
                                log(`Received non-JSON message from ${url}: ${event.data}`, 'success');
                            }
                        };
                        
                        socket.onerror = function(error) {
                            log(`❌ Error connecting to ${url}`, 'error');
                            console.error(`WebSocket error for ${url}:`, error);
                            failCount++;
                        };
                        
                        socket.onclose = function(event) {
                            const reason = event.reason ? ` (${event.reason})` : '';
                            log(`Connection to ${url} closed with code ${event.code}${reason}`, 'warning');
                            
                            // Check if this is the last test
                            if (index === urls.length - 1) {
                                setTimeout(() => {
                                    log(`Test summary: ${successCount} successful, ${failCount} failed`, 
                                        successCount > 0 ? 'success' : 'error');
                                }, 1000);
                            }
                        };
                    } catch (error) {
                        log(`❌ Error creating WebSocket for ${url}: ${error.message}`, 'error');
                        failCount++;
                    }
                }, index * 3000); // Stagger the connections by 3 seconds
            });
        }
        
        // Start the test when the page loads
        window.addEventListener('load', testWebSocket);
    </script>
</body>
</html>
