<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Debug - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 React Application Debug Tool</h1>
        <p>This tool helps diagnose React application loading issues.</p>
        
        <div id="status-container">
            <div class="status info">
                <strong>🔄 Running diagnostics...</strong>
            </div>
        </div>

        <div class="grid">
            <div>
                <h3>📦 Bundle Status</h3>
                <div id="bundle-status"></div>
            </div>
            <div>
                <h3>⚛️ React Status</h3>
                <div id="react-status"></div>
            </div>
        </div>

        <div>
            <h3>🌐 Network Requests</h3>
            <div id="network-status"></div>
        </div>

        <div>
            <h3>📋 Console Logs</h3>
            <pre id="console-logs"></pre>
        </div>

        <div>
            <button onclick="runDiagnostics()">🔄 Re-run Diagnostics</button>
            <button onclick="testMainApp()">🚀 Test Main App</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
    </div>

    <script>
        let consoleLogs = [];
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            consoleLogs.push(`[LOG] ${args.join(' ')}`);
            originalLog.apply(console, args);
            updateConsoleLogs();
        };
        
        console.error = function(...args) {
            consoleLogs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
            updateConsoleLogs();
        };
        
        console.warn = function(...args) {
            consoleLogs.push(`[WARN] ${args.join(' ')}`);
            originalWarn.apply(console, args);
            updateConsoleLogs();
        };

        function updateConsoleLogs() {
            document.getElementById('console-logs').textContent = consoleLogs.slice(-20).join('\n');
        }

        function clearLogs() {
            consoleLogs = [];
            updateConsoleLogs();
        }

        async function checkBundleStatus() {
            const bundleContainer = document.getElementById('bundle-status');
            let html = '';
            
            // Check for main bundle
            try {
                const response = await fetch('/static/js/main.c2e876e4.js');
                if (response.ok) {
                    html += '<div class="status success">✅ Main bundle found and accessible</div>';
                    const size = response.headers.get('content-length');
                    if (size) {
                        html += `<div class="status info">📊 Bundle size: ${(size / 1024 / 1024).toFixed(2)} MB</div>`;
                    }
                } else {
                    html += '<div class="status error">❌ Main bundle not accessible</div>';
                }
            } catch (error) {
                html += `<div class="status error">❌ Bundle check failed: ${error.message}</div>`;
            }
            
            // Check for CSS
            try {
                const response = await fetch('/static/css/main.eb8080eb.css');
                if (response.ok) {
                    html += '<div class="status success">✅ Main CSS found and accessible</div>';
                } else {
                    html += '<div class="status warning">⚠️ Main CSS not accessible</div>';
                }
            } catch (error) {
                html += `<div class="status warning">⚠️ CSS check failed: ${error.message}</div>`;
            }
            
            bundleContainer.innerHTML = html;
        }

        function checkReactStatus() {
            const reactContainer = document.getElementById('react-status');
            let html = '';
            
            // Check if React is available
            if (typeof window.React !== 'undefined') {
                html += '<div class="status success">✅ React is available globally</div>';
                html += `<div class="status info">📋 React version: ${window.React.version || 'Unknown'}</div>`;
            } else {
                html += '<div class="status error">❌ React not available globally</div>';
            }
            
            // Check if ReactDOM is available
            if (typeof window.ReactDOM !== 'undefined') {
                html += '<div class="status success">✅ ReactDOM is available globally</div>';
            } else {
                html += '<div class="status error">❌ ReactDOM not available globally</div>';
            }
            
            // Check root element
            const rootElement = document.getElementById('root');
            if (rootElement) {
                html += '<div class="status success">✅ Root element found</div>';
                html += `<div class="status info">📋 Root content: ${rootElement.innerHTML.length > 0 ? 'Has content' : 'Empty'}</div>`;
            } else {
                html += '<div class="status error">❌ Root element not found</div>';
            }
            
            reactContainer.innerHTML = html;
        }

        function checkNetworkStatus() {
            const networkContainer = document.getElementById('network-status');
            let html = '';
            
            // Check if we can reach the main page
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        html += '<div class="status success">✅ Main page accessible</div>';
                    } else {
                        html += '<div class="status error">❌ Main page not accessible</div>';
                    }
                    networkContainer.innerHTML = html;
                })
                .catch(error => {
                    html += `<div class="status error">❌ Network error: ${error.message}</div>`;
                    networkContainer.innerHTML = html;
                });
        }

        function testMainApp() {
            console.log('🧪 Testing main app access...');
            window.location.href = '/';
        }

        async function runDiagnostics() {
            console.log('🔍 Running React diagnostics...');
            
            document.getElementById('status-container').innerHTML = 
                '<div class="status info"><strong>🔄 Running diagnostics...</strong></div>';
            
            await checkBundleStatus();
            checkReactStatus();
            checkNetworkStatus();
            
            document.getElementById('status-container').innerHTML = 
                '<div class="status success"><strong>✅ Diagnostics complete!</strong></div>';
        }

        // Run diagnostics on page load
        window.addEventListener('load', runDiagnostics);
    </script>
</body>
</html>
