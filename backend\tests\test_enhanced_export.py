"""
Comprehensive tests for enhanced export functionality
Tests all export formats, validation, and quality assurance
"""

import json
import unittest
from unittest.mock import patch, MagicMock
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from core.enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat, StyleFramework
from my_app.models import App, LayoutTemplate, AppTemplate
from my_app.services.export_template_service import ExportTemplateService


class EnhancedCodeGeneratorTests(TestCase):
    """Test cases for the enhanced code generator"""
    
    def setUp(self):
        self.generator = EnhancedCodeGenerator()
        self.sample_app_data = {
            'components': [
                {
                    'id': 'comp1',
                    'type': 'Button',
                    'props': {'text': 'Click me', 'className': 'btn-primary'}
                },
                {
                    'id': 'comp2',
                    'type': 'Text',
                    'props': {'content': 'Hello World', 'className': 'text-large'}
                }
            ],
            'layouts': [
                {
                    'id': 'layout1',
                    'name': 'main',
                    'type': 'container',
                    'components': ['comp1', 'comp2']
                }
            ],
            'styles': {
                '.btn-primary': {'background-color': '#007bff', 'color': 'white'},
                '.text-large': {'font-size': '18px', 'font-weight': 'bold'}
            },
            'data': {
                'title': 'My App',
                'version': '1.0.0'
            }
        }
    
    def test_react_code_generation(self):
        """Test React code generation"""
        options = ExportOptions(
            format=ExportFormat.REACT,
            typescript=False,
            include_accessibility=True,
            project_structure='single-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, str)
        self.assertIn('import React', result)
        self.assertIn('const App = () => {', result)
        self.assertIn('export default App', result)
        self.assertIn('role="main"', result)  # Accessibility
    
    def test_react_typescript_generation(self):
        """Test React with TypeScript generation"""
        options = ExportOptions(
            format=ExportFormat.REACT_TS,
            typescript=True,
            include_accessibility=True,
            project_structure='single-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, str)
        self.assertIn('interface', result)  # TypeScript interfaces
        self.assertIn('ButtonProps', result)
        self.assertIn('TextProps', result)
    
    def test_vue_code_generation(self):
        """Test Vue.js code generation"""
        options = ExportOptions(
            format=ExportFormat.VUE,
            typescript=False,
            project_structure='single-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, str)
        self.assertIn('<template>', result)
        self.assertIn('<script>', result)
        self.assertIn('<style scoped>', result)
        self.assertIn('defineComponent', result)
    
    def test_angular_code_generation(self):
        """Test Angular code generation"""
        options = ExportOptions(
            format=ExportFormat.ANGULAR,
            typescript=True,
            project_structure='multi-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, dict)
        self.assertIn('app.component.ts', result)
        self.assertIn('app.component.html', result)
        self.assertIn('app.component.css', result)
        self.assertIn('@Component', result['app.component.ts'])
    
    def test_svelte_code_generation(self):
        """Test Svelte code generation"""
        options = ExportOptions(
            format=ExportFormat.SVELTE,
            typescript=False,
            project_structure='single-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, str)
        self.assertIn('<script>', result)
        self.assertIn('<style>', result)
        self.assertIn('let title', result)  # Reactive variables
    
    def test_nextjs_code_generation(self):
        """Test Next.js code generation"""
        options = ExportOptions(
            format=ExportFormat.NEXT_JS,
            typescript=True,
            project_structure='multi-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, dict)
        self.assertIn('pages/index.tsx', result)
        self.assertIn('styles/Home.module.css', result)
        self.assertIn('import Head from \'next/head\'', result['pages/index.tsx'])
    
    def test_react_native_generation(self):
        """Test React Native code generation"""
        options = ExportOptions(
            format=ExportFormat.REACT_NATIVE,
            typescript=False,
            project_structure='single-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, str)
        self.assertIn('import React', result)
        self.assertIn('SafeAreaView', result)
        self.assertIn('StyleSheet', result)
        self.assertIn('TouchableOpacity', result)  # Button mapping
    
    def test_flutter_code_generation(self):
        """Test Flutter code generation"""
        options = ExportOptions(
            format=ExportFormat.FLUTTER,
            project_structure='single-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, str)
        self.assertIn('import \'package:flutter/material.dart\'', result)
        self.assertIn('class MyApp extends StatelessWidget', result)
        self.assertIn('ElevatedButton', result)  # Button mapping
    
    def test_html_generation(self):
        """Test HTML code generation"""
        options = ExportOptions(
            format=ExportFormat.HTML,
            include_accessibility=True,
            project_structure='single-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, str)
        self.assertIn('<!DOCTYPE html>', result)
        self.assertIn('<html lang="en">', result)
        self.assertIn('<button', result)
        self.assertIn('<p', result)
    
    def test_express_api_generation(self):
        """Test Express.js API generation"""
        options = ExportOptions(
            format=ExportFormat.EXPRESS_API,
            project_structure='multi-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, dict)
        self.assertIn('server.js', result)
        self.assertIn('package.json', result)
        self.assertIn('const express = require', result['server.js'])
        self.assertIn('app.listen', result['server.js'])
    
    def test_fastapi_generation(self):
        """Test FastAPI code generation"""
        options = ExportOptions(
            format=ExportFormat.FASTAPI,
            project_structure='multi-file'
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, dict)
        self.assertIn('main.py', result)
        self.assertIn('requirements.txt', result)
        self.assertIn('from fastapi import FastAPI', result['main.py'])
        self.assertIn('fastapi==', result['requirements.txt'])
    
    def test_full_project_structure(self):
        """Test full project structure generation"""
        options = ExportOptions(
            format=ExportFormat.REACT,
            typescript=True,
            project_structure='full-project',
            include_docker=True,
            include_ci_cd=True,
            include_tests=True
        )
        
        result = self.generator.generate_code(self.sample_app_data, options)
        
        self.assertIsInstance(result, dict)
        self.assertIn('package.json', result)
        self.assertIn('README.md', result)
        self.assertIn('tsconfig.json', result)
        self.assertIn('Dockerfile', result)
        self.assertIn('.github/workflows/ci.yml', result)
        self.assertIn('.eslintrc.json', result)
    
    def test_style_framework_integration(self):
        """Test different style framework integrations"""
        frameworks = [
            StyleFramework.STYLED_COMPONENTS,
            StyleFramework.EMOTION,
            StyleFramework.TAILWIND,
            StyleFramework.MATERIAL_UI
        ]
        
        for framework in frameworks:
            options = ExportOptions(
                format=ExportFormat.REACT,
                style_framework=framework,
                project_structure='single-file'
            )
            
            result = self.generator.generate_code(self.sample_app_data, options)
            self.assertIsInstance(result, str)
            
            if framework == StyleFramework.STYLED_COMPONENTS:
                self.assertIn('styled-components', result)
            elif framework == StyleFramework.MATERIAL_UI:
                self.assertIn('ThemeProvider', result)


class ExportAPITests(TestCase):
    """Test cases for export API endpoints"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.app = App.objects.create(
            name='Test App',
            user=self.user,
            app_data=json.dumps({
                'components': [{'id': 'comp1', 'type': 'Button', 'props': {'text': 'Test'}}],
                'layouts': [{'id': 'layout1', 'components': ['comp1']}]
            })
        )
    
    def test_enhanced_export_endpoint(self):
        """Test the enhanced export API endpoint"""
        self.client.force_login(self.user)
        
        response = self.client.post('/api/enhanced-export/', {
            'app_id': self.app.id,
            'format': 'react',
            'options': {
                'typescript': True,
                'include_accessibility': True,
                'project_structure': 'single-file'
            }
        }, content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertIn('code', data)
    
    def test_export_formats_endpoint(self):
        """Test the export formats information endpoint"""
        self.client.force_login(self.user)
        
        response = self.client.get('/api/export-formats/')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('formats', data)
        self.assertIn('style_frameworks', data)
        self.assertIn('state_management', data)
    
    def test_unauthorized_export(self):
        """Test export without authentication"""
        response = self.client.post('/api/enhanced-export/', {
            'app_id': self.app.id,
            'format': 'react'
        }, content_type='application/json')
        
        self.assertEqual(response.status_code, 401)


class ExportTemplateServiceTests(TestCase):
    """Test cases for export template service"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.service = ExportTemplateService()
        self.layout_template = LayoutTemplate.objects.create(
            name='Test Layout',
            user=self.user,
            layout_type='container',
            components=json.dumps({
                'components': [{'id': 'comp1', 'type': 'Button'}]
            })
        )
    
    def test_template_export(self):
        """Test exporting a template as a project"""
        result = self.service.export_template_as_project(
            self.layout_template.id,
            'layout',
            'react',
            {'typescript': True, 'project_structure': 'full-project'},
            self.user
        )
        
        self.assertEqual(result['type'], 'project')
        self.assertIn('files', result)
        self.assertIn('metadata', result)
        self.assertEqual(result['metadata']['template_type'], 'layout')


class CodeValidationTests(TestCase):
    """Test cases for code validation and quality assurance"""
    
    def setUp(self):
        self.generator = EnhancedCodeGenerator()
        self.sample_data = {
            'components': [
                {'id': 'comp1', 'type': 'Button', 'props': {'text': 'Test'}}
            ],
            'layouts': [
                {'id': 'layout1', 'components': ['comp1']}
            ]
        }
    
    def test_react_code_syntax_validation(self):
        """Test that generated React code has valid syntax"""
        options = ExportOptions(format=ExportFormat.REACT)
        result = self.generator.generate_code(self.sample_data, options)
        
        # Basic syntax checks
        self.assertIn('import React', result)
        self.assertIn('export default', result)
        self.assertEqual(result.count('('), result.count(')'))  # Balanced parentheses
        self.assertEqual(result.count('{'), result.count('}'))  # Balanced braces
    
    def test_typescript_interface_validation(self):
        """Test that TypeScript interfaces are properly generated"""
        options = ExportOptions(format=ExportFormat.REACT_TS, typescript=True)
        result = self.generator.generate_code(self.sample_data, options)
        
        self.assertIn('interface', result)
        self.assertIn('ButtonProps', result)
        # Check that interfaces have proper structure
        self.assertRegex(result, r'interface \w+Props \{[\s\S]*?\}')
    
    def test_accessibility_attributes(self):
        """Test that accessibility attributes are included when enabled"""
        options = ExportOptions(
            format=ExportFormat.REACT,
            include_accessibility=True
        )
        result = self.generator.generate_code(self.sample_data, options)
        
        self.assertIn('role=', result)
        self.assertIn('aria-', result)
    
    def test_package_json_validation(self):
        """Test that generated package.json is valid JSON"""
        options = ExportOptions(
            format=ExportFormat.REACT,
            project_structure='full-project'
        )
        result = self.generator.generate_code(self.sample_data, options)
        
        self.assertIn('package.json', result)
        # Validate JSON syntax
        package_data = json.loads(result['package.json'])
        self.assertIn('name', package_data)
        self.assertIn('version', package_data)
        self.assertIn('dependencies', package_data)
    
    def test_html_validation(self):
        """Test that generated HTML is well-formed"""
        options = ExportOptions(format=ExportFormat.HTML)
        result = self.generator.generate_code(self.sample_data, options)
        
        self.assertIn('<!DOCTYPE html>', result)
        self.assertIn('<html', result)
        self.assertIn('</html>', result)
        self.assertIn('<head>', result)
        self.assertIn('</head>', result)
        self.assertIn('<body>', result)
        self.assertIn('</body>', result)


if __name__ == '__main__':
    unittest.main()
