import React from 'react';
import { Card, Typography, Button, Space, Divider } from 'antd';
import { useEnhancedTheme } from '../../contexts/EnhancedThemeContext';
import DarkModeToggle from '../ui/DarkModeToggle';
import styled from 'styled-components';

const { Title, Paragraph, Text } = Typography;

const TestContainer = styled.div`
  padding: var(--spacing-lg);
  background-color: var(--color-background);
  min-height: 100vh;
  transition: all 0.3s ease;
`;

const TestCard = styled(Card)`
  margin-bottom: var(--spacing-lg);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
`;

const ColorSwatch = styled.div`
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-md);
  margin-right: var(--spacing-sm);
  border: 1px solid var(--color-border);
  vertical-align: middle;
`;

const ThemeInfo = styled.div`
  background-color: var(--color-background-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border-light);
  margin: var(--spacing-md) 0;
`;

const DarkModeTest = () => {
  const { isDarkMode, themeMode, colors, systemPrefersDark } = useEnhancedTheme();

  return (
    <TestContainer>
      <TestCard>
        <Title level={2}>Dark Mode Test</Title>
        <Paragraph>
          This component tests the dark mode functionality and theme switching.
        </Paragraph>
        
        <Space size="large" wrap>
          <DarkModeToggle showDropdown={true} />
          <DarkModeToggle showDropdown={false} />
        </Space>

        <Divider />

        <ThemeInfo>
          <Title level={4}>Current Theme Information</Title>
          <Space direction="vertical" size="small">
            <Text><strong>Theme Mode:</strong> {themeMode}</Text>
            <Text><strong>Is Dark Mode:</strong> {isDarkMode ? 'Yes' : 'No'}</Text>
            <Text><strong>System Prefers Dark:</strong> {systemPrefersDark ? 'Yes' : 'No'}</Text>
          </Space>
        </ThemeInfo>

        <Divider />

        <Title level={4}>Color Palette</Title>
        <Space direction="vertical" size="small">
          <div>
            <ColorSwatch style={{ backgroundColor: colors.primary }} />
            <Text>Primary: {colors.primary}</Text>
          </div>
          <div>
            <ColorSwatch style={{ backgroundColor: colors.secondary }} />
            <Text>Secondary: {colors.secondary}</Text>
          </div>
          <div>
            <ColorSwatch style={{ backgroundColor: colors.background }} />
            <Text>Background: {colors.background}</Text>
          </div>
          <div>
            <ColorSwatch style={{ backgroundColor: colors.surface }} />
            <Text>Surface: {colors.surface}</Text>
          </div>
          <div>
            <ColorSwatch style={{ backgroundColor: colors.text }} />
            <Text>Text: {colors.text}</Text>
          </div>
        </Space>

        <Divider />

        <Title level={4}>Interactive Elements</Title>
        <Space wrap>
          <Button type="primary">Primary Button</Button>
          <Button type="default">Default Button</Button>
          <Button type="dashed">Dashed Button</Button>
          <Button type="text">Text Button</Button>
          <Button type="link">Link Button</Button>
        </Space>

        <Divider />

        <Title level={4}>Text Variations</Title>
        <Space direction="vertical">
          <Text>Default text color</Text>
          <Text type="secondary">Secondary text color</Text>
          <Text type="success">Success text color</Text>
          <Text type="warning">Warning text color</Text>
          <Text type="danger">Danger text color</Text>
          <Text disabled>Disabled text color</Text>
        </Space>
      </TestCard>

      <TestCard>
        <Title level={3}>Nested Card Example</Title>
        <Paragraph>
          This card demonstrates how nested components look in the current theme.
        </Paragraph>
        
        <Card type="inner" title="Inner Card">
          <Paragraph>
            This is an inner card that should adapt to the theme as well.
          </Paragraph>
          <Button type="primary" size="small">
            Inner Button
          </Button>
        </Card>
      </TestCard>
    </TestContainer>
  );
};

export default DarkModeTest;
