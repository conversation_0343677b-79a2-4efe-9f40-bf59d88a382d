import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { login } from '../../utils/auth';

/**
 * Login Component
 * 
 * This component provides a login form for user authentication.
 */
const Login = ({ onSuccess }) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [rememberMe, setRememberMe] = useState(false);
  
  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.username || !formData.password) {
      setError('Please enter both username and password');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Attempt login
      const result = await login(formData.username, formData.password);
      
      if (result.success) {
        // Save remember me preference
        if (rememberMe) {
          localStorage.setItem('remember_me', 'true');
        } else {
          localStorage.removeItem('remember_me');
        }
        
        // Call success callback if provided
        if (onSuccess) {
          onSuccess(result.user);
        }
        
        // Redirect to dashboard
        navigate('/dashboard');
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h2>Login</h2>
          <p>Welcome back! Please login to your account.</p>
        </div>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              placeholder="Enter your username"
              disabled={loading}
              autoComplete="username"
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter your password"
              disabled={loading}
              autoComplete="current-password"
              required
            />
          </div>
          
          <div className="form-options">
            <div className="remember-me">
              <input
                type="checkbox"
                id="remember-me"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                disabled={loading}
              />
              <label htmlFor="remember-me">Remember me</label>
            </div>
            
            <div className="forgot-password">
              <Link to="/forgot-password">Forgot password?</Link>
            </div>
          </div>
          
          <button
            type="submit"
            className="login-button"
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
        
        <div className="login-footer">
          <p>
            Don't have an account?{' '}
            <Link to="/register">Register</Link>
          </p>
        </div>
      </div>
      
      <style jsx>{`
        .login-container {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          padding: var(--spacing-md);
          background-color: var(--color-background);
        }
        
        .login-card {
          width: 100%;
          max-width: 400px;
          padding: var(--spacing-lg);
          background-color: var(--color-surface);
          border-radius: var(--border-radius-lg);
          box-shadow: var(--shadow-md);
        }
        
        .login-header {
          margin-bottom: var(--spacing-lg);
          text-align: center;
        }
        
        .login-header h2 {
          margin-bottom: var(--spacing-xs);
          color: var(--color-text);
        }
        
        .login-header p {
          color: var(--color-textSecondary);
        }
        
        .error-message {
          padding: var(--spacing-sm);
          margin-bottom: var(--spacing-md);
          background-color: rgba(var(--color-error-rgb), 0.1);
          border: 1px solid var(--color-error);
          border-radius: var(--border-radius-md);
          color: var(--color-error);
          font-size: var(--font-size-sm);
        }
        
        .login-form {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-md);
        }
        
        .form-group {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-xs);
        }
        
        .form-group label {
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .form-group input {
          padding: var(--spacing-sm) var(--spacing-md);
          border: 1px solid var(--color-border);
          border-radius: var(--border-radius-md);
          background-color: var(--color-background);
          color: var(--color-text);
          font-size: var(--font-size-md);
          transition: border-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .form-group input:focus {
          outline: none;
          border-color: var(--color-primary);
        }
        
        .form-options {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: var(--font-size-sm);
        }
        
        .remember-me {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
        }
        
        .forgot-password a {
          color: var(--color-primary);
          text-decoration: none;
        }
        
        .forgot-password a:hover {
          text-decoration: underline;
        }
        
        .login-button {
          padding: var(--spacing-sm) var(--spacing-md);
          background-color: var(--color-primary);
          color: white;
          border: none;
          border-radius: var(--border-radius-md);
          font-size: var(--font-size-md);
          font-weight: var(--font-weight-medium);
          cursor: pointer;
          transition: background-color var(--transition-fast) var(--transition-timing-function);
        }
        
        .login-button:hover {
          background-color: color-mix(in srgb, var(--color-primary) 80%, black);
        }
        
        .login-button:disabled {
          background-color: var(--color-border);
          color: var(--color-textSecondary);
          cursor: not-allowed;
        }
        
        .login-footer {
          margin-top: var(--spacing-lg);
          text-align: center;
          font-size: var(--font-size-sm);
          color: var(--color-textSecondary);
        }
        
        .login-footer a {
          color: var(--color-primary);
          text-decoration: none;
        }
        
        .login-footer a:hover {
          text-decoration: underline;
        }
      `}</style>
    </div>
  );
};

export default Login;
