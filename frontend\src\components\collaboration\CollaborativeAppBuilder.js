import React, { useState, useRef, useEffect } from 'react';
import { Layout, message, Spin } from 'antd';
import { CollaborationProvider, useCollaboration } from '../../contexts/CollaborationContext';
import { CollaborationToolbar } from './CollaborationToolbar';
import { CommentPanel, CommentBubble } from './CommentSystem';
import { PresenceOverlay, usePresenceTracking } from './UserPresence';
import { useCollaborativeDragDrop, CollaborativeDropZone, CollaborativeComponent } from './CollaborativeDragDrop';
import ComponentPalette from '../builder/ComponentPalette';
import PropertyEditor from '../builder/PropertyEditor';

const { Content, Sider } = Layout;

// Main collaborative app builder component
const CollaborativeAppBuilderInner = ({ appId, initialComponents = [] }) => {
  const [components, setComponents] = useState(initialComponents);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [showComments, setShowComments] = useState(false);
  const [showPresence, setShowPresence] = useState(true);
  const canvasRef = useRef();

  const {
    currentSession,
    isConnected,
    isJoining,
    comments,
    createComment,
    updateComment,
    resolveComment,
    joinSession
  } = useCollaboration();

  // Set up collaborative drag and drop
  const collaborativeDragDrop = useCollaborativeDragDrop(components, setComponents);
  
  // Set up presence tracking
  const presenceTracking = usePresenceTracking(canvasRef);

  // Join collaboration session on mount
  useEffect(() => {
    if (appId && !currentSession) {
      // In a real app, you would first create or find a collaboration session for this app
      const sessionId = `app-${appId}-session`;
      joinSession(sessionId);
    }
  }, [appId, currentSession, joinSession]);

  // Handle component selection
  const handleComponentSelect = (component) => {
    setSelectedComponent(component);
    presenceTracking.selectComponent(component.id, {
      x: component.position?.x || 0,
      y: component.position?.y || 0,
      width: 100, // Would be calculated from actual component bounds
      height: 50
    });
  };

  // Handle adding component from palette
  const handleAddComponent = (componentType) => {
    const newComponent = collaborativeDragDrop.addComponent({
      type: componentType,
      props: getDefaultProps(componentType),
      fromPalette: true
    });
    
    message.success(`${componentType} component added`);
    return newComponent;
  };

  // Handle component updates
  const handleUpdateComponent = (componentId, updates) => {
    collaborativeDragDrop.updateComponent(componentId, updates);
  };

  // Handle component deletion
  const handleDeleteComponent = (componentId) => {
    collaborativeDragDrop.deleteComponent(componentId);
    if (selectedComponent?.id === componentId) {
      setSelectedComponent(null);
      presenceTracking.clearSelection();
    }
    message.success('Component deleted');
  };

  // Handle canvas click (deselect)
  const handleCanvasClick = (event) => {
    if (event.target === event.currentTarget) {
      setSelectedComponent(null);
      presenceTracking.clearSelection();
    }
  };

  // Comment handlers
  const handleAddComment = (content, componentId = null, position = null) => {
    createComment(content, componentId, position);
  };

  const handleEditComment = (commentId, content) => {
    updateComment(commentId, content);
  };

  const handleResolveComment = (commentId) => {
    resolveComment(commentId);
  };

  const handleReplyToComment = (parentId, content) => {
    createComment(content, null, null, parentId);
  };

  // Get default props for component type
  const getDefaultProps = (type) => {
    const defaults = {
      button: { text: 'Button', variant: 'primary' },
      input: { placeholder: 'Enter text...', type: 'text' },
      text: { content: 'Sample text', fontSize: 14 },
      image: { src: '', alt: 'Image', width: 200, height: 150 },
      container: { padding: 16, backgroundColor: '#f5f5f5' }
    };
    return defaults[type] || {};
  };

  // Render component based on type
  const renderComponent = (component) => {
    const { type, props } = component;
    
    switch (type) {
      case 'button':
        return (
          <button style={{ padding: '8px 16px', ...props.style }}>
            {props.text || 'Button'}
          </button>
        );
      case 'input':
        return (
          <input 
            type={props.type || 'text'}
            placeholder={props.placeholder || 'Enter text...'}
            style={{ padding: '8px', border: '1px solid #d9d9d9', ...props.style }}
          />
        );
      case 'text':
        return (
          <div style={{ fontSize: props.fontSize || 14, ...props.style }}>
            {props.content || 'Sample text'}
          </div>
        );
      case 'container':
        return (
          <div style={{ 
            minWidth: 100, 
            minHeight: 50, 
            backgroundColor: props.backgroundColor || '#f5f5f5',
            padding: props.padding || 16,
            border: '1px dashed #d9d9d9',
            ...props.style 
          }}>
            Container
          </div>
        );
      default:
        return <div>Unknown component: {type}</div>;
    }
  };

  if (isJoining) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="Joining collaboration session..." />
      </div>
    );
  }

  return (
    <Layout style={{ height: '100vh' }}>
      {/* Collaboration toolbar */}
      <CollaborationToolbar
        onToggleComments={() => setShowComments(!showComments)}
        onTogglePresence={() => setShowPresence(!showPresence)}
        commentsVisible={showComments}
        presenceVisible={showPresence}
      />

      <Layout>
        {/* Component palette */}
        <Sider width={250} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
          <ComponentPalette onAddComponent={handleAddComponent} />
        </Sider>

        {/* Main canvas area */}
        <Content style={{ position: 'relative' }}>
          <CollaborativeDropZone
            collaborativeDragDrop={collaborativeDragDrop}
            style={{ height: '100%', border: 'none' }}
          >
            <div
              ref={canvasRef}
              onClick={handleCanvasClick}
              style={{
                width: '100%',
                height: '100%',
                position: 'relative',
                background: '#fafafa'
              }}
            >
              {/* Render components */}
              {components.map(component => (
                <CollaborativeComponent
                  key={component.id}
                  component={component}
                  onUpdate={handleUpdateComponent}
                  onDelete={() => handleDeleteComponent(component.id)}
                  onSelect={handleComponentSelect}
                  isSelected={selectedComponent?.id === component.id}
                  collaborativeDragDrop={collaborativeDragDrop}
                >
                  {renderComponent(component)}
                </CollaborativeComponent>
              ))}

              {/* Comment bubbles */}
              {comments.map(comment => {
                if (!comment.canvas_position || !comment.canvas_position.x) return null;
                
                return (
                  <CommentBubble
                    key={comment.id}
                    comment={comment}
                    position={comment.canvas_position}
                    onClick={() => {
                      // Show comment details or open comment panel
                      setShowComments(true);
                    }}
                    hasUnread={comment.status === 'open'}
                  />
                );
              })}

              {/* User presence overlay */}
              {showPresence && isConnected && (
                <PresenceOverlay containerRef={canvasRef} />
              )}
            </div>
          </CollaborativeDropZone>
        </Content>

        {/* Property editor */}
        {selectedComponent && (
          <Sider width={300} style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}>
            <PropertyEditor
              component={selectedComponent}
              onUpdate={(updates) => handleUpdateComponent(selectedComponent.id, updates)}
              onDelete={() => handleDeleteComponent(selectedComponent.id)}
            />
          </Sider>
        )}

        {/* Comments panel */}
        {showComments && (
          <Sider width={350} style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}>
            <CommentPanel
              comments={comments}
              onAddComment={handleAddComment}
              onEditComment={handleEditComment}
              onDeleteComment={(commentId) => {
                // In a real app, you would call a delete API
                message.success('Comment deleted');
              }}
              onResolveComment={handleResolveComment}
              onReplyToComment={handleReplyToComment}
              visible={showComments}
              onClose={() => setShowComments(false)}
            />
          </Sider>
        )}
      </Layout>
    </Layout>
  );
};

// Wrapper component that provides collaboration context
export const CollaborativeAppBuilder = ({ appId, initialComponents }) => {
  return (
    <CollaborationProvider>
      <CollaborativeAppBuilderInner 
        appId={appId} 
        initialComponents={initialComponents} 
      />
    </CollaborationProvider>
  );
};

export default CollaborativeAppBuilder;
