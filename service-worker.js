const CACHE_NAME = 'app-cache-v1';
const FILES_TO_CACHE = [
    '/',
    '/index.html',
    '/logo192.png',
    // Add your main JS and CSS bundles here
    // Example if using the root webpack.config.js output:
    '/bundle.js' 
    // '/static/css/main.[contenthash:8].css' // Example if using hashed CSS
];

// Install event: cache files and handle errors
self.addEventListener('install', (event) => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                return cache.addAll(FILES_TO_CACHE);
            })
            .catch((error) => {
                console.error('[Service Worker] Install error:', error);
            })
    );
    self.skipWaiting();
});

self.addEventListener('fetch', (event) => {
    if (event.request.mode === 'navigate') {
        event.respondWith(
            fetch(event.request)
                .catch(() => {
                    return caches.match('/index.html');
                })
        );
        return;
    }
    event.respondWith(
        caches.match(event.request)
            .then((response) => {
                // Cache hit - return response
                if (response) {
                    return response;
                }

                // Clone the request
                const fetchRequest = event.request.clone();

                return fetch(fetchRequest)
                    .then((response) => {
                        // Check if we received a valid response
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // IMPORTANT: Clone the response
                        const responseToCache = response.clone();

                        caches.open(CACHE_NAME)
                            .then((cache) => {
                                cache.put(event.request, responseToCache);
                            });

                        return response;
                    })
                    .catch((error) => {
                        console.error('Fetch failed:', error);
                        // Return a fallback response to avoid unhandled promise rejection
                        return new Response('<h1>Network error</h1><p>Unable to fetch resource and no cache available.</p>', {
                            status: 503,
                            headers: { 'Content-Type': 'text/html' }
                        });
                    });
            })
    );
});