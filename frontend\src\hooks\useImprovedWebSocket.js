/**
 * useImprovedWebSocket Hook
 *
 * A comprehensive React hook for WebSocket communication with enhanced features:
 * - Automatic reconnection with exponential backoff
 * - Message batching for performance
 * - Offline message queueing
 * - Structured message handling
 * - Detailed connection state management
 * - Performance metrics
 */
import { useState, useEffect, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { getWebSocketUrl } from '../config/env';

// Connection states
export const ConnectionState = {
  CONNECTING: 'connecting',
  OPEN: 'open',
  CLOSING: 'closing',
  CLOSED: 'closed',
  RECONNECTING: 'reconnecting',
  ERROR: 'error'
};

/**
 * Enhanced WebSocket hook
 * @param {Object} options - Configuration options
 * @returns {Object} WebSocket state and methods
 */
function useImprovedWebSocket(options = {}) {
  // Extract options with defaults
  const {
    url = null,
    endpoint = 'app_builder',
    autoConnect = true,
    autoReconnect = true,
    reconnectInterval = 1000,
    maxReconnectInterval = 30000,
    reconnectDecay = 1.5,
    maxReconnectAttempts = 10,
    heartbeatInterval = 30000,
    debug = false,
    updateRedux = false,
    batchInterval = 50,
    maxBatchSize = 100,
    onOpen = null,
    onClose = null,
    onMessage = null,
    onError = null
  } = options;

  // Determine WebSocket URL
  const wsUrl = url || (endpoint ? getWebSocketUrl(endpoint) : null);
  
  // Redux integration
  const dispatch = useDispatch();
  
  // State
  const [connectionState, setConnectionState] = useState(ConnectionState.CLOSED);
  const [lastMessage, setLastMessage] = useState(null);
  const [messages, setMessages] = useState([]);
  const [lastError, setLastError] = useState(null);
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const [metrics, setMetrics] = useState({
    latency: null,
    messagesSent: 0,
    messagesReceived: 0,
    bytesReceived: 0,
    bytesSent: 0,
    lastActivity: null
  });
  
  // Refs
  const socketRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);
  const messageQueueRef = useRef([]);
  const batchTimeoutRef = useRef(null);
  const metricsRef = useRef(metrics);
  
  // Update metrics ref when state changes
  useEffect(() => {
    metricsRef.current = metrics;
  }, [metrics]);

  // Debug logging
  const log = useCallback((...args) => {
    if (debug) {
      console.log(`[WebSocket][${new Date().toISOString()}]`, ...args);
    }
  }, [debug]);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!wsUrl) {
      setLastError(new Error('WebSocket URL not provided'));
      return;
    }
    
    if (socketRef.current && (socketRef.current.readyState === WebSocket.OPEN || socketRef.current.readyState === WebSocket.CONNECTING)) {
      log('WebSocket already connected or connecting');
      return;
    }
    
    try {
      log(`Connecting to WebSocket: ${wsUrl}`);
      setConnectionState(ConnectionState.CONNECTING);
      
      // Create new WebSocket
      const socket = new WebSocket(wsUrl);
      socketRef.current = socket;
      
      // Set up event handlers
      socket.onopen = (event) => handleOpen(event);
      socket.onclose = (event) => handleClose(event);
      socket.onmessage = (event) => handleMessage(event);
      socket.onerror = (event) => handleError(event);
    } catch (error) {
      log('Error creating WebSocket:', error);
      setLastError(error);
      setConnectionState(ConnectionState.ERROR);
    }
  }, [wsUrl]);

  // Handle WebSocket open event
  const handleOpen = useCallback((event) => {
    log('WebSocket connected');
    setConnectionState(ConnectionState.OPEN);
    setReconnectAttempt(0);
    setLastError(null);
    
    // Start heartbeat
    if (heartbeatInterval > 0) {
      startHeartbeat();
    }
    
    // Process any queued messages
    processQueue();
    
    // Update metrics
    setMetrics(prev => ({
      ...prev,
      lastActivity: new Date()
    }));
    
    // Call onOpen callback
    if (onOpen) {
      onOpen(event);
    }
    
    // Dispatch Redux action
    if (updateRedux) {
      dispatch({ type: 'WEBSOCKET_CONNECTED' });
    }
  }, [onOpen, heartbeatInterval, updateRedux, dispatch]);

  // Handle WebSocket close event
  const handleClose = useCallback((event) => {
    log(`WebSocket closed: ${event.code} ${event.reason}`);
    setConnectionState(ConnectionState.CLOSED);
    
    // Stop heartbeat
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    
    // Update metrics
    setMetrics(prev => ({
      ...prev,
      lastActivity: new Date()
    }));
    
    // Call onClose callback
    if (onClose) {
      onClose(event);
    }
    
    // Dispatch Redux action
    if (updateRedux) {
      dispatch({ type: 'WEBSOCKET_DISCONNECTED' });
    }
    
    // Attempt to reconnect if enabled
    if (autoReconnect && reconnectAttempt < maxReconnectAttempts) {
      attemptReconnect();
    }
  }, [onClose, autoReconnect, reconnectAttempt, maxReconnectAttempts, updateRedux, dispatch]);

  // Handle WebSocket message event
  const handleMessage = useCallback((event) => {
    log('WebSocket message received:', event.data);
    
    try {
      // Parse message data
      const data = JSON.parse(event.data);
      
      // Update state
      setLastMessage(data);
      setMessages(prev => [...prev, data]);
      
      // Update metrics
      setMetrics(prev => ({
        ...prev,
        messagesReceived: prev.messagesReceived + 1,
        bytesReceived: prev.bytesReceived + event.data.length,
        lastActivity: new Date()
      }));
      
      // Handle special message types
      if (data.type === 'pong' && data.timestamp) {
        const latency = Date.now() - data.timestamp;
        setMetrics(prev => ({
          ...prev,
          latency
        }));
      }
      
      // Call onMessage callback
      if (onMessage) {
        onMessage(data);
      }
      
      // Dispatch Redux action
      if (updateRedux) {
        dispatch({ 
          type: 'WEBSOCKET_MESSAGE_RECEIVED', 
          payload: data 
        });
      }
    } catch (error) {
      log('Error parsing WebSocket message:', error);
      setLastError(error);
    }
  }, [onMessage, updateRedux, dispatch]);

  // Handle WebSocket error event
  const handleError = useCallback((event) => {
    log('WebSocket error:', event);
    
    const error = new Error('WebSocket error');
    setLastError(error);
    setConnectionState(ConnectionState.ERROR);
    
    // Call onError callback
    if (onError) {
      onError(error);
    }
    
    // Dispatch Redux action
    if (updateRedux) {
      dispatch({ 
        type: 'WEBSOCKET_ERROR', 
        payload: error 
      });
    }
  }, [onError, updateRedux, dispatch]);

  // Attempt to reconnect
  const attemptReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    const nextAttempt = reconnectAttempt + 1;
    setReconnectAttempt(nextAttempt);
    setConnectionState(ConnectionState.RECONNECTING);
    
    // Calculate backoff delay
    const delay = Math.min(
      reconnectInterval * Math.pow(reconnectDecay, nextAttempt),
      maxReconnectInterval
    );
    
    log(`Attempting to reconnect (attempt ${nextAttempt}) in ${delay}ms`);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [reconnectAttempt, reconnectInterval, reconnectDecay, maxReconnectInterval, connect]);

  // Start heartbeat interval
  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }
    
    heartbeatIntervalRef.current = setInterval(() => {
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        const pingMessage = {
          type: 'ping',
          timestamp: Date.now()
        };
        
        try {
          socketRef.current.send(JSON.stringify(pingMessage));
          
          // Update metrics
          setMetrics(prev => ({
            ...prev,
            messagesSent: prev.messagesSent + 1,
            bytesSent: prev.bytesSent + JSON.stringify(pingMessage).length
          }));
        } catch (error) {
          log('Error sending heartbeat:', error);
        }
      }
    }, heartbeatInterval);
  }, [heartbeatInterval]);

  // Process message queue
  const processQueue = useCallback(() => {
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN && messageQueueRef.current.length > 0) {
      const queue = [...messageQueueRef.current];
      messageQueueRef.current = [];
      
      log(`Processing ${queue.length} queued messages`);
      
      queue.forEach(message => {
        try {
          const messageStr = JSON.stringify(message);
          socketRef.current.send(messageStr);
          
          // Update metrics
          setMetrics(prev => ({
            ...prev,
            messagesSent: prev.messagesSent + 1,
            bytesSent: prev.bytesSent + messageStr.length,
            lastActivity: new Date()
          }));
        } catch (error) {
          log('Error sending queued message:', error);
          messageQueueRef.current.push(message);
        }
      });
    }
  }, []);

  // Send a message
  const send = useCallback((message) => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      log('WebSocket not connected, queueing message');
      messageQueueRef.current.push(message);
      return false;
    }
    
    try {
      const messageStr = JSON.stringify(message);
      socketRef.current.send(messageStr);
      
      // Update metrics
      setMetrics(prev => ({
        ...prev,
        messagesSent: prev.messagesSent + 1,
        bytesSent: prev.bytesSent + messageStr.length,
        lastActivity: new Date()
      }));
      
      return true;
    } catch (error) {
      log('Error sending message:', error);
      setLastError(error);
      return false;
    }
  }, []);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    log('Disconnecting WebSocket');
    
    // Clear timeouts and intervals
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    
    if (batchTimeoutRef.current) {
      clearTimeout(batchTimeoutRef.current);
      batchTimeoutRef.current = null;
    }
    
    // Close socket if it exists
    if (socketRef.current) {
      try {
        setConnectionState(ConnectionState.CLOSING);
        socketRef.current.close();
      } catch (error) {
        log('Error closing WebSocket:', error);
      }
      
      socketRef.current = null;
    }
  }, []);

  // Clear messages
  const clearMessages = useCallback(() => {
    setMessages([]);
    setLastMessage(null);
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && wsUrl) {
      connect();
    }
    
    // Clean up on unmount
    return () => {
      disconnect();
    };
  }, [autoConnect, wsUrl, connect, disconnect]);

  // Return hook API
  return {
    // State
    connectionState,
    connected: connectionState === ConnectionState.OPEN,
    connecting: connectionState === ConnectionState.CONNECTING || connectionState === ConnectionState.RECONNECTING,
    lastMessage,
    messages,
    error: lastError,
    reconnectAttempt,
    metrics,
    
    // Methods
    connect,
    disconnect,
    send,
    clearMessages,
    clearError: () => setLastError(null),
    resetMetrics: () => setMetrics({
      latency: null,
      messagesSent: 0,
      messagesReceived: 0,
      bytesReceived: 0,
      bytesSent: 0,
      lastActivity: null
    })
  };
}

export default useImprovedWebSocket;
