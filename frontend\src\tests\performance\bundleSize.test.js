/**
 * Bundle size monitoring tests
 * Ensures the application bundle stays within acceptable size limits
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Bundle size thresholds (in KB)
const BUNDLE_SIZE_LIMITS = {
  main: 244, // Main bundle should be under 244KB (as per user preference)
  vendor: 500, // Vendor bundle can be larger
  total: 800, // Total bundle size limit
  gzipped: {
    main: 80, // Gzipped main bundle
    vendor: 150, // Gzipped vendor bundle
    total: 250, // Total gzipped size
  }
};

// File patterns to analyze
const BUNDLE_PATTERNS = {
  main: /main\.[a-f0-9]+\.js$/,
  vendor: /vendor\.[a-f0-9]+\.js$/,
  chunk: /chunk\.[a-f0-9]+\.js$/,
  css: /\.css$/,
};

describe('Bundle Size Tests', () => {
  let buildDir;
  let bundleStats;

  beforeAll(async () => {
    // Build the application for production
    console.log('Building application for bundle analysis...');
    
    try {
      execSync('npm run build', { 
        stdio: 'pipe',
        cwd: process.cwd(),
        timeout: 300000 // 5 minutes timeout
      });
    } catch (error) {
      console.error('Build failed:', error.message);
      throw new Error('Failed to build application for bundle analysis');
    }

    buildDir = path.join(process.cwd(), 'build', 'static');
    bundleStats = await analyzeBundles();
  });

  test('main bundle size is within limits', () => {
    const mainBundle = bundleStats.bundles.find(b => b.type === 'main');
    
    expect(mainBundle).toBeDefined();
    expect(mainBundle.size).toBeLessThanOrEqual(BUNDLE_SIZE_LIMITS.main * 1024);
    
    console.log(`Main bundle size: ${(mainBundle.size / 1024).toFixed(2)}KB (limit: ${BUNDLE_SIZE_LIMITS.main}KB)`);
  });

  test('vendor bundle size is within limits', () => {
    const vendorBundle = bundleStats.bundles.find(b => b.type === 'vendor');
    
    if (vendorBundle) {
      expect(vendorBundle.size).toBeLessThanOrEqual(BUNDLE_SIZE_LIMITS.vendor * 1024);
      console.log(`Vendor bundle size: ${(vendorBundle.size / 1024).toFixed(2)}KB (limit: ${BUNDLE_SIZE_LIMITS.vendor}KB)`);
    }
  });

  test('total bundle size is within limits', () => {
    const totalSize = bundleStats.totalSize;
    
    expect(totalSize).toBeLessThanOrEqual(BUNDLE_SIZE_LIMITS.total * 1024);
    
    console.log(`Total bundle size: ${(totalSize / 1024).toFixed(2)}KB (limit: ${BUNDLE_SIZE_LIMITS.total}KB)`);
  });

  test('gzipped bundle sizes are within limits', () => {
    const gzippedStats = bundleStats.gzipped;
    
    if (gzippedStats.main) {
      expect(gzippedStats.main).toBeLessThanOrEqual(BUNDLE_SIZE_LIMITS.gzipped.main * 1024);
      console.log(`Gzipped main bundle: ${(gzippedStats.main / 1024).toFixed(2)}KB`);
    }
    
    if (gzippedStats.vendor) {
      expect(gzippedStats.vendor).toBeLessThanOrEqual(BUNDLE_SIZE_LIMITS.gzipped.vendor * 1024);
      console.log(`Gzipped vendor bundle: ${(gzippedStats.vendor / 1024).toFixed(2)}KB`);
    }
    
    expect(gzippedStats.total).toBeLessThanOrEqual(BUNDLE_SIZE_LIMITS.gzipped.total * 1024);
    console.log(`Total gzipped size: ${(gzippedStats.total / 1024).toFixed(2)}KB`);
  });

  test('no duplicate dependencies in bundles', () => {
    const duplicates = findDuplicateDependencies();
    
    if (duplicates.length > 0) {
      console.warn('Duplicate dependencies found:', duplicates);
      // Allow some common duplicates but warn about them
      const criticalDuplicates = duplicates.filter(dep => 
        !['react', 'react-dom', 'lodash'].includes(dep.name)
      );
      expect(criticalDuplicates).toHaveLength(0);
    }
  });

  test('tree shaking is effective', () => {
    // Check that unused exports are removed
    const bundleContent = getBundleContent();
    
    // These should not appear in production bundles
    const devOnlyPatterns = [
      /console\.log\(/g,
      /console\.debug\(/g,
      /debugger;/g,
      /__DEV__/g,
    ];

    devOnlyPatterns.forEach(pattern => {
      const matches = bundleContent.match(pattern);
      if (matches) {
        console.warn(`Found ${matches.length} instances of dev-only code: ${pattern}`);
        // Allow some console.log but warn if too many
        if (pattern.toString().includes('console.log') && matches.length > 10) {
          throw new Error(`Too many console.log statements in production bundle: ${matches.length}`);
        }
      }
    });
  });

  test('critical dependencies are properly chunked', () => {
    const chunks = bundleStats.chunks;
    
    // Ant Design should be in vendor chunk or separate chunk
    const antdInMain = bundleStats.bundles.find(b => 
      b.type === 'main' && b.dependencies.some(dep => dep.includes('antd'))
    );
    
    if (antdInMain) {
      console.warn('Ant Design found in main bundle - consider code splitting');
    }

    // React should be in vendor chunk
    const reactChunk = chunks.find(chunk => 
      chunk.dependencies.some(dep => dep.includes('react'))
    );
    
    expect(reactChunk).toBeDefined();
    expect(reactChunk.type).not.toBe('main');
  });

  test('lazy loading chunks are properly sized', () => {
    const lazyChunks = bundleStats.chunks.filter(chunk => chunk.isLazy);
    
    lazyChunks.forEach(chunk => {
      // Lazy chunks should be reasonably sized (not too big, not too small)
      expect(chunk.size).toBeGreaterThan(1024); // At least 1KB
      expect(chunk.size).toBeLessThan(100 * 1024); // Less than 100KB
      
      console.log(`Lazy chunk ${chunk.name}: ${(chunk.size / 1024).toFixed(2)}KB`);
    });
  });

  test('CSS bundle size is reasonable', () => {
    const cssFiles = bundleStats.css;
    const totalCssSize = cssFiles.reduce((total, file) => total + file.size, 0);
    
    // CSS should be less than 50KB
    expect(totalCssSize).toBeLessThan(50 * 1024);
    
    console.log(`Total CSS size: ${(totalCssSize / 1024).toFixed(2)}KB`);
  });

  test('source maps are not included in production', () => {
    const sourceMapFiles = findSourceMaps();
    
    // Source maps should not be in production build
    expect(sourceMapFiles).toHaveLength(0);
  });

  test('bundle analysis report is generated', () => {
    // Generate bundle analysis report
    const report = generateBundleReport(bundleStats);
    
    // Save report for CI/CD
    const reportPath = path.join(process.cwd(), 'test-results', 'bundle-analysis.json');
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`Bundle analysis report saved to: ${reportPath}`);
    
    // Verify report contains expected data
    expect(report.timestamp).toBeDefined();
    expect(report.bundles).toBeDefined();
    expect(report.recommendations).toBeDefined();
  });
});

/**
 * Analyze bundle files and return statistics
 */
async function analyzeBundles() {
  const jsDir = path.join(buildDir, 'js');
  const cssDir = path.join(buildDir, 'css');
  
  if (!fs.existsSync(jsDir)) {
    throw new Error('Build directory not found. Run npm run build first.');
  }

  const jsFiles = fs.readdirSync(jsDir);
  const cssFiles = fs.existsSync(cssDir) ? fs.readdirSync(cssDir) : [];
  
  const bundles = [];
  const chunks = [];
  let totalSize = 0;

  // Analyze JavaScript files
  for (const file of jsFiles) {
    const filePath = path.join(jsDir, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalSize += size;

    const bundleInfo = {
      name: file,
      size,
      path: filePath,
      type: getBundleType(file),
      dependencies: extractDependencies(filePath),
      isLazy: file.includes('chunk'),
    };

    if (bundleInfo.isLazy) {
      chunks.push(bundleInfo);
    } else {
      bundles.push(bundleInfo);
    }
  }

  // Analyze CSS files
  const css = cssFiles.map(file => {
    const filePath = path.join(cssDir, file);
    const stats = fs.statSync(filePath);
    return {
      name: file,
      size: stats.size,
      path: filePath,
    };
  });

  // Calculate gzipped sizes
  const gzipped = await calculateGzippedSizes(bundles);

  return {
    bundles,
    chunks,
    css,
    totalSize,
    gzipped,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Determine bundle type from filename
 */
function getBundleType(filename) {
  if (BUNDLE_PATTERNS.main.test(filename)) return 'main';
  if (BUNDLE_PATTERNS.vendor.test(filename)) return 'vendor';
  if (BUNDLE_PATTERNS.chunk.test(filename)) return 'chunk';
  return 'unknown';
}

/**
 * Extract dependencies from bundle (simplified)
 */
function extractDependencies(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const dependencies = [];
    
    // Look for common dependency patterns
    const patterns = [
      /node_modules\/([^\/]+)/g,
      /from\s+['"]([^'"]+)['"]/g,
      /require\(['"]([^'"]+)['"]\)/g,
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const dep = match[1];
        if (dep && !dependencies.includes(dep)) {
          dependencies.push(dep);
        }
      }
    });

    return dependencies;
  } catch (error) {
    console.warn(`Failed to extract dependencies from ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Calculate gzipped sizes
 */
async function calculateGzippedSizes(bundles) {
  const gzipped = {
    main: 0,
    vendor: 0,
    total: 0,
  };

  for (const bundle of bundles) {
    try {
      const gzipSize = execSync(`gzip -c "${bundle.path}" | wc -c`, { 
        encoding: 'utf8',
        stdio: 'pipe'
      }).trim();
      
      const size = parseInt(gzipSize, 10);
      gzipped.total += size;
      
      if (bundle.type === 'main') {
        gzipped.main += size;
      } else if (bundle.type === 'vendor') {
        gzipped.vendor += size;
      }
    } catch (error) {
      console.warn(`Failed to calculate gzip size for ${bundle.name}:`, error.message);
    }
  }

  return gzipped;
}

/**
 * Find duplicate dependencies across bundles
 */
function findDuplicateDependencies() {
  // This would require more sophisticated bundle analysis
  // For now, return empty array
  return [];
}

/**
 * Get combined bundle content for analysis
 */
function getBundleContent() {
  const jsDir = path.join(buildDir, 'js');
  const files = fs.readdirSync(jsDir);
  
  let content = '';
  for (const file of files.slice(0, 3)) { // Analyze first 3 files to avoid memory issues
    try {
      content += fs.readFileSync(path.join(jsDir, file), 'utf8');
    } catch (error) {
      console.warn(`Failed to read ${file}:`, error.message);
    }
  }
  
  return content;
}

/**
 * Find source map files
 */
function findSourceMaps() {
  const jsDir = path.join(buildDir, 'js');
  const files = fs.readdirSync(jsDir);
  
  return files.filter(file => file.endsWith('.map'));
}

/**
 * Generate comprehensive bundle report
 */
function generateBundleReport(bundleStats) {
  const recommendations = [];
  
  // Check main bundle size
  const mainBundle = bundleStats.bundles.find(b => b.type === 'main');
  if (mainBundle && mainBundle.size > BUNDLE_SIZE_LIMITS.main * 1024) {
    recommendations.push({
      type: 'warning',
      message: `Main bundle (${(mainBundle.size / 1024).toFixed(2)}KB) exceeds limit (${BUNDLE_SIZE_LIMITS.main}KB)`,
      suggestions: [
        'Implement code splitting for large components',
        'Use dynamic imports for non-critical features',
        'Consider lazy loading for routes',
      ]
    });
  }

  // Check for optimization opportunities
  if (bundleStats.gzipped.total > BUNDLE_SIZE_LIMITS.gzipped.total * 1024) {
    recommendations.push({
      type: 'optimization',
      message: 'Total gzipped size is large',
      suggestions: [
        'Enable tree shaking for unused exports',
        'Use webpack-bundle-analyzer to identify large dependencies',
        'Consider using lighter alternatives for heavy libraries',
      ]
    });
  }

  return {
    timestamp: bundleStats.timestamp,
    bundles: bundleStats.bundles.map(b => ({
      name: b.name,
      type: b.type,
      size: b.size,
      sizeKB: (b.size / 1024).toFixed(2),
    })),
    summary: {
      totalSize: bundleStats.totalSize,
      totalSizeKB: (bundleStats.totalSize / 1024).toFixed(2),
      gzippedSize: bundleStats.gzipped.total,
      gzippedSizeKB: (bundleStats.gzipped.total / 1024).toFixed(2),
      compressionRatio: ((1 - bundleStats.gzipped.total / bundleStats.totalSize) * 100).toFixed(1),
    },
    limits: BUNDLE_SIZE_LIMITS,
    recommendations,
  };
}
