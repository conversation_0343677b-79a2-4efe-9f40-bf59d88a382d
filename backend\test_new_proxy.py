#!/usr/bin/env python3
"""
Test the new WebSocket proxy configuration
"""

import json
import time
from websocket import create_connection, WebSocketTimeoutException

def test_new_proxy():
    """Test the new /api-ws proxy configuration"""
    
    print("Testing new WebSocket proxy configuration...")
    print("=" * 50)
    
    # Test the new proxy path
    proxy_urls = [
        "ws://localhost:3000/api-ws",
        "ws://localhost:3000/api-ws/",
        "ws://localhost:3000/api-ws/app_builder",
        "ws://localhost:3000/api-ws/simple",
        "ws://localhost:3000/api-ws/test"
    ]
    
    for url in proxy_urls:
        print(f"\nTesting: {url}")
        try:
            ws = create_connection(url, timeout=10)
            print(f"✅ Connection successful!")
            
            # Send test message
            test_msg = json.dumps({"type": "ping", "message": f"Test from {url}"})
            ws.send(test_msg)
            
            # Wait for response
            response = ws.recv()
            print(f"✅ Response: {response[:100]}...")
            
            ws.close()
            print(f"✅ Connection closed successfully")
            
        except Exception as e:
            print(f"❌ Failed: {e}")
    
    # Also test direct backend connection for comparison
    print(f"\n" + "=" * 50)
    print("Testing direct backend connection for comparison:")
    direct_url = "ws://localhost:8000/ws/"
    try:
        ws = create_connection(direct_url, timeout=5)
        print(f"✅ Direct connection successful!")
        
        test_msg = json.dumps({"type": "ping", "message": "Direct test"})
        ws.send(test_msg)
        response = ws.recv()
        print(f"✅ Direct response: {response[:100]}...")
        ws.close()
        
    except Exception as e:
        print(f"❌ Direct connection failed: {e}")

if __name__ == "__main__":
    test_new_proxy()
