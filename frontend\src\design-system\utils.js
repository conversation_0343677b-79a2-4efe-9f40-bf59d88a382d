/**
 * Design System Utilities
 * 
 * This file provides utility functions for working with the design system theme.
 * It includes functions for color manipulation, responsive design, accessibility, etc.
 */

import theme from './theme';

/**
 * Color utilities
 */
export const colorUtils = {
  /**
   * Get color value from theme
   * @param {string} colorPath - Path to color in theme (e.g., 'primary.main')
   * @returns {string} Color value
   */
  getColor: (colorPath) => {
    const keys = colorPath.split('.');
    let color = theme.colors;
    
    for (const key of keys) {
      if (color && typeof color === 'object' && key in color) {
        color = color[key];
      } else {
        console.warn(`Color path "${colorPath}" not found in theme`);
        return theme.colors.neutral[500]; // Fallback color
      }
    }
    
    return color;
  },

  /**
   * Convert hex color to rgba
   * @param {string} hex - Hex color value
   * @param {number} alpha - Alpha value (0-1)
   * @returns {string} RGBA color string
   */
  hexToRgba: (hex, alpha = 1) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  },

  /**
   * Lighten a color by a percentage
   * @param {string} color - Color value
   * @param {number} amount - Amount to lighten (0-100)
   * @returns {string} Lightened color
   */
  lighten: (color, amount) => {
    // Simple implementation - in production, use a color manipulation library
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * amount);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  },

  /**
   * Darken a color by a percentage
   * @param {string} color - Color value
   * @param {number} amount - Amount to darken (0-100)
   * @returns {string} Darkened color
   */
  darken: (color, amount) => {
    return colorUtils.lighten(color, -amount);
  },

  /**
   * Check if a color meets WCAG contrast requirements
   * @param {string} foreground - Foreground color
   * @param {string} background - Background color
   * @param {string} level - WCAG level ('AA' or 'AAA')
   * @returns {boolean} Whether contrast is sufficient
   */
  checkContrast: (foreground, background, level = 'AA') => {
    // Simplified implementation - in production, use a proper contrast checker
    const minRatio = level === 'AAA' ? 7 : 4.5;
    // This is a placeholder - implement proper contrast calculation
    return true; // For now, assume all colors pass
  },
};

/**
 * Spacing utilities
 */
export const spacingUtils = {
  /**
   * Get spacing value from theme
   * @param {string|number} space - Spacing key or multiplier
   * @returns {string} Spacing value
   */
  getSpacing: (space) => {
    if (typeof space === 'number') {
      return `${space * 0.25}rem`; // 4px base unit
    }
    return theme.spacing[space] || space;
  },

  /**
   * Create responsive spacing
   * @param {object} values - Breakpoint values
   * @returns {string} CSS with media queries
   */
  responsive: (values) => {
    let css = '';
    Object.entries(values).forEach(([breakpoint, value]) => {
      if (breakpoint === 'base') {
        css += `${spacingUtils.getSpacing(value)};`;
      } else {
        css += `${theme.mediaQueries[breakpoint]} { ${spacingUtils.getSpacing(value)}; }`;
      }
    });
    return css;
  },
};

/**
 * Typography utilities
 */
export const typographyUtils = {
  /**
   * Get text style from theme
   * @param {string} style - Text style name
   * @returns {object} Style object
   */
  getTextStyle: (style) => {
    return theme.typography.textStyles[style] || {};
  },

  /**
   * Create responsive typography
   * @param {object} values - Breakpoint values
   * @returns {object} Style object with media queries
   */
  responsiveText: (values) => {
    const styles = {};
    Object.entries(values).forEach(([breakpoint, value]) => {
      if (breakpoint === 'base') {
        Object.assign(styles, typographyUtils.getTextStyle(value));
      } else {
        styles[theme.mediaQueries[breakpoint]] = typographyUtils.getTextStyle(value);
      }
    });
    return styles;
  },
};

/**
 * Accessibility utilities
 */
export const a11yUtils = {
  /**
   * Get focus ring styles
   * @param {string} color - Focus color (optional)
   * @returns {object} Focus ring styles
   */
  focusRing: (color = theme.colors.primary.main) => ({
    outline: `${theme.accessibility.focusRing.width} ${theme.accessibility.focusRing.style} ${color}`,
    outlineOffset: theme.accessibility.focusRing.offset,
  }),

  /**
   * Screen reader only styles
   * @returns {object} SR-only styles
   */
  srOnly: () => theme.accessibility.srOnly,

  /**
   * Ensure minimum touch target size
   * @returns {object} Touch target styles
   */
  touchTarget: () => ({
    minWidth: theme.accessibility.minTouchTarget.width,
    minHeight: theme.accessibility.minTouchTarget.height,
  }),

  /**
   * High contrast mode styles
   * @returns {object} High contrast styles
   */
  highContrast: () => ({
    '@media (prefers-contrast: high)': theme.accessibility.highContrast,
  }),
};

/**
 * Animation utilities
 */
export const animationUtils = {
  /**
   * Get animation from theme
   * @param {string} name - Animation name
   * @returns {string} Animation value
   */
  getAnimation: (name) => {
    return theme.animations[name] || name;
  },

  /**
   * Create reduced motion variant
   * @param {string} animation - Animation value
   * @returns {object} Animation with reduced motion support
   */
  withReducedMotion: (animation) => ({
    animation,
    [theme.mediaQueries.reducedMotion]: {
      animation: 'none',
    },
  }),
};

/**
 * Component utilities
 */
export const componentUtils = {
  /**
   * Get component design tokens
   * @param {string} component - Component name
   * @returns {object} Component tokens
   */
  getComponentTokens: (component) => {
    return theme.components[component] || {};
  },

  /**
   * Create component variant styles
   * @param {string} component - Component name
   * @param {string} variant - Variant name
   * @param {string} size - Size variant
   * @returns {object} Component styles
   */
  getVariantStyles: (component, variant = 'primary', size = 'md') => {
    const tokens = componentUtils.getComponentTokens(component);
    
    // Base styles for the component
    const baseStyles = {
      height: tokens.height?.[size],
      padding: tokens.padding?.[size],
      fontSize: tokens.fontSize?.[size],
      borderRadius: theme.borderRadius[component] || theme.borderRadius.md,
      transition: theme.transitions.default,
    };

    // Variant-specific styles
    const variantStyles = {
      primary: {
        backgroundColor: theme.colors.primary.main,
        color: theme.colors.primary.contrastText,
        border: `1px solid ${theme.colors.primary.main}`,
        '&:hover': {
          backgroundColor: theme.colors.primary.dark,
        },
        '&:focus': {
          ...a11yUtils.focusRing(),
        },
      },
      secondary: {
        backgroundColor: 'transparent',
        color: theme.colors.primary.main,
        border: `1px solid ${theme.colors.primary.main}`,
        '&:hover': {
          backgroundColor: theme.colors.primary.light,
        },
        '&:focus': {
          ...a11yUtils.focusRing(),
        },
      },
      ghost: {
        backgroundColor: 'transparent',
        color: theme.colors.text.primary,
        border: '1px solid transparent',
        '&:hover': {
          backgroundColor: theme.colors.interactive.hover,
        },
        '&:focus': {
          ...a11yUtils.focusRing(),
        },
      },
    };

    return {
      ...baseStyles,
      ...variantStyles[variant],
    };
  },
};

/**
 * Responsive design utilities
 */
export const responsiveUtils = {
  /**
   * Create responsive styles
   * @param {object} styles - Breakpoint styles
   * @returns {object} Responsive styles object
   */
  responsive: (styles) => {
    const responsiveStyles = {};
    
    Object.entries(styles).forEach(([breakpoint, style]) => {
      if (breakpoint === 'base') {
        Object.assign(responsiveStyles, style);
      } else {
        responsiveStyles[theme.mediaQueries[breakpoint]] = style;
      }
    });
    
    return responsiveStyles;
  },

  /**
   * Check if current screen matches breakpoint
   * @param {string} breakpoint - Breakpoint name
   * @returns {boolean} Whether breakpoint matches
   */
  matchesBreakpoint: (breakpoint) => {
    if (typeof window === 'undefined') return false;
    
    const breakpointValue = parseInt(theme.breakpoints[breakpoint]);
    return window.innerWidth >= breakpointValue;
  },
};

// Export all utilities
export default {
  color: colorUtils,
  spacing: spacingUtils,
  typography: typographyUtils,
  a11y: a11yUtils,
  animation: animationUtils,
  component: componentUtils,
  responsive: responsiveUtils,
};
