import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Tag,
  Input,
  Modal,
  Form,
  Select,
  DatePicker,
  Tooltip,
  Typography,
  Popconfirm,
  message,
  Drawer,
  Tabs,
  Divider,
  Empty,
  Skeleton
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  EyeOutlined,
  CopyOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  TeamOutlined,
  SettingOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import usePerformanceMonitor from '../hooks/usePerformanceMonitor';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// Styled components
const ProjectsContainer = styled.div`
  padding: 24px;
`;

const ProjectHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
`;

const SearchContainer = styled.div`
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
`;

const ProjectsPage = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // Initialize performance monitoring
  const performance = usePerformanceMonitor({
    enabled: process.env.NODE_ENV === 'development',
  });

  // Set component name for performance monitoring
  performance.setComponentName('ProjectsPage');

  // Fetch projects
  useEffect(() => {
    performance.mark('fetch-projects-start');
    const fetchProjects = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data
        const mockProjects = [
          {
            id: '1',
            name: 'E-commerce Dashboard',
            description: 'A dashboard for an e-commerce website',
            status: 'active',
            createdAt: '2023-05-15T10:30:00Z',
            updatedAt: '2023-05-20T14:45:00Z',
            owner: 'John Doe',
            team: ['John Doe', 'Jane Smith'],
            tags: ['dashboard', 'e-commerce'],
            template: 'dashboard',
          },
          {
            id: '2',
            name: 'Blog Template',
            description: 'A template for a blog website',
            status: 'completed',
            createdAt: '2023-04-10T09:15:00Z',
            updatedAt: '2023-04-25T16:20:00Z',
            owner: 'Jane Smith',
            team: ['Jane Smith', 'Bob Johnson'],
            tags: ['blog', 'template'],
            template: 'blog',
          },
          {
            id: '3',
            name: 'Mobile App UI',
            description: 'UI design for a mobile app',
            status: 'archived',
            createdAt: '2023-03-05T11:45:00Z',
            updatedAt: '2023-03-15T13:10:00Z',
            owner: 'Bob Johnson',
            team: ['Bob Johnson'],
            tags: ['mobile', 'ui'],
            template: 'mobile',
          },
          {
            id: '4',
            name: 'Landing Page',
            description: 'A landing page for a product',
            status: 'active',
            createdAt: '2023-05-01T08:30:00Z',
            updatedAt: '2023-05-10T15:45:00Z',
            owner: 'John Doe',
            team: ['John Doe', 'Alice Williams'],
            tags: ['landing', 'marketing'],
            template: 'landing',
          },
          {
            id: '5',
            name: 'Admin Panel',
            description: 'An admin panel for a web application',
            status: 'active',
            createdAt: '2023-04-20T13:15:00Z',
            updatedAt: '2023-05-05T10:30:00Z',
            owner: 'Alice Williams',
            team: ['Alice Williams', 'John Doe'],
            tags: ['admin', 'dashboard'],
            template: 'admin',
          },
        ];

        setProjects(mockProjects);
      } catch (error) {
        console.error('Error fetching projects:', error);
        message.error('Failed to load projects');
      } finally {
        setLoading(false);
        performance.mark('fetch-projects-end');
        performance.measure('fetch-projects', 'fetch-projects-start', 'fetch-projects-end');
      }
    };

    fetchProjects();
  }, [performance]);

  // Handle create project
  const handleCreateProject = async (values) => {
    performance.mark('create-project-start');
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create new project
      const newProject = {
        id: String(projects.length + 1),
        name: values.name,
        description: values.description,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        owner: 'Current User',
        team: ['Current User'],
        tags: values.tags || [],
        template: values.template,
      };

      // Add to projects
      setProjects([...projects, newProject]);

      // Close modal
      setModalVisible(false);

      // Reset form
      form.resetFields();

      // Show success message
      message.success('Project created successfully');

      // Navigate to the new project
      navigate(`/app-builder?project=${newProject.id}`);
    } catch (error) {
      console.error('Error creating project:', error);
      message.error('Failed to create project');
    } finally {
      performance.mark('create-project-end');
      performance.measure('create-project', 'create-project-start', 'create-project-end');
    }
  };

  // Handle delete project
  const handleDeleteProject = async (id) => {
    performance.mark('delete-project-start');
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Remove from projects
      setProjects(projects.filter(project => project.id !== id));

      // Show success message
      message.success('Project deleted successfully');
    } catch (error) {
      console.error('Error deleting project:', error);
      message.error('Failed to delete project');
    } finally {
      performance.mark('delete-project-end');
      performance.measure('delete-project', 'delete-project-start', 'delete-project-end');
    }
  };

  // Handle view project
  const handleViewProject = (project) => {
    setCurrentProject(project);
    setDrawerVisible(true);
  };

  // Handle edit project
  const handleEditProject = (project) => {
    navigate(`/app-builder?project=${project.id}`);
  };

  // Handle export project
  const handleExportProject = (project) => {
    message.info(`Exporting project: ${project.name}`);
  };

  // Handle duplicate project
  const handleDuplicateProject = async (project) => {
    performance.mark('duplicate-project-start');
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create duplicate project
      const duplicateProject = {
        ...project,
        id: String(projects.length + 1),
        name: `${project.name} (Copy)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Add to projects
      setProjects([...projects, duplicateProject]);

      // Show success message
      message.success('Project duplicated successfully');
    } catch (error) {
      console.error('Error duplicating project:', error);
      message.error('Failed to duplicate project');
    } finally {
      performance.mark('duplicate-project-end');
      performance.measure('duplicate-project', 'duplicate-project-start', 'duplicate-project-end');
    }
  };

  // Filter projects by search text
  const filteredProjects = projects.filter(project => {
    const searchLower = searchText.toLowerCase();
    return (
      project.name.toLowerCase().includes(searchLower) ||
      project.description.toLowerCase().includes(searchLower) ||
      project.owner.toLowerCase().includes(searchLower) ||
      project.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  });

  // Table columns
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: status => (
        <Tag color={
          status === 'active' ? 'green' :
            status === 'completed' ? 'blue' :
              'default'
        }>
          {status.toUpperCase()}
        </Tag>
      ),
      filters: [
        { text: 'Active', value: 'active' },
        { text: 'Completed', value: 'completed' },
        { text: 'Archived', value: 'archived' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: date => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      render: tags => (
        <>
          {tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewProject(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditProject(record)}
            />
          </Tooltip>
          <Tooltip title="Export">
            <Button
              icon={<ExportOutlined />}
              size="small"
              onClick={() => handleExportProject(record)}
            />
          </Tooltip>
          <Tooltip title="Duplicate">
            <Button
              icon={<CopyOutlined />}
              size="small"
              onClick={() => handleDuplicateProject(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Popconfirm
              title="Are you sure you want to delete this project?"
              onConfirm={() => handleDeleteProject(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <ProjectsContainer>
      <ProjectHeader>
        <Title level={2}>Projects</Title>
        <SearchContainer>
          <Input
            placeholder="Search projects"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setModalVisible(true)}
          >
            New Project
          </Button>
        </SearchContainer>
      </ProjectHeader>

      <Card>
        {loading ? (
          <Skeleton active paragraph={{ rows: 10 }} />
        ) : (
          <Table
            dataSource={filteredProjects}
            columns={columns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            locale={{
              emptyText: (
                <Empty
                  description="No projects found"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              ),
            }}
          />
        )}
      </Card>

      {/* Create Project Modal */}
      <Modal
        title="Create New Project"
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateProject}
        >
          <Form.Item
            name="name"
            label="Project Name"
            rules={[{ required: true, message: 'Please enter a project name' }]}
          >
            <Input placeholder="Enter project name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea
              placeholder="Enter project description"
              rows={4}
            />
          </Form.Item>

          <Form.Item
            name="template"
            label="Template"
            rules={[{ required: true, message: 'Please select a template' }]}
          >
            <Select placeholder="Select a template">
              <Option value="blank">Blank</Option>
              <Option value="dashboard">Dashboard</Option>
              <Option value="blog">Blog</Option>
              <Option value="ecommerce">E-commerce</Option>
              <Option value="landing">Landing Page</Option>
              <Option value="admin">Admin Panel</Option>
              <Option value="mobile">Mobile App</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="tags"
            label="Tags"
          >
            <Select
              mode="tags"
              placeholder="Add tags"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              Create Project
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Project Details Drawer */}
      <Drawer
        title={currentProject?.name}
        width={600}
        placement="right"
        onClose={() => setDrawerVisible(false)}
        visible={drawerVisible}
      >
        {currentProject && (
          <Tabs defaultActiveKey="details">
            <TabPane
              tab={<span><EyeOutlined /> Details</span>}
              key="details"
            >
              <div>
                <Title level={4}>Description</Title>
                <Text>{currentProject.description}</Text>

                <Divider />

                <Title level={4}>Status</Title>
                <Tag color={
                  currentProject.status === 'active' ? 'green' :
                    currentProject.status === 'completed' ? 'blue' :
                      'default'
                }>
                  {currentProject.status.toUpperCase()}
                </Tag>

                <Divider />

                <Title level={4}>Created</Title>
                <Text>{new Date(currentProject.createdAt).toLocaleString()}</Text>

                <Divider />

                <Title level={4}>Last Updated</Title>
                <Text>{new Date(currentProject.updatedAt).toLocaleString()}</Text>

                <Divider />

                <Title level={4}>Tags</Title>
                {currentProject.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}

                <Divider />

                <Title level={4}>Template</Title>
                <Text>{currentProject.template}</Text>
              </div>
            </TabPane>

            <TabPane
              tab={<span><TeamOutlined /> Team</span>}
              key="team"
            >
              <div>
                <Title level={4}>Owner</Title>
                <Text>{currentProject.owner}</Text>

                <Divider />

                <Title level={4}>Team Members</Title>
                <ul>
                  {currentProject.team.map(member => (
                    <li key={member}>{member}</li>
                  ))}
                </ul>
              </div>
            </TabPane>

            <TabPane
              tab={<span><HistoryOutlined /> History</span>}
              key="history"
            >
              <Empty description="No history available" />
            </TabPane>

            <TabPane
              tab={<span><SettingOutlined /> Settings</span>}
              key="settings"
            >
              <Empty description="No settings available" />
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </ProjectsContainer>
  );
};

export default ProjectsPage;
