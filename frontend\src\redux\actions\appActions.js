import * as types from '../actionTypes';
import fallbackApiService from '../../services/fallbackApiService';
import EnhancedWebSocketClient from '../../services/EnhancedWebSocketClient';
import { getWebSocketUrl } from '../../config/env';
import { unifiedPerformanceMonitor } from '../../utils/performance';

// WebSocket client instance
let wsClient = null;

/**
 * Initialize WebSocket connection with performance monitoring
 * @param {Object} options - WebSocket options
 * @returns {Function} Thunk function
 */
export const initializeWebSocket = (options = {}) => async (dispatch) => {
  dispatch({ type: types.WEBSOCKET_INIT_REQUEST });

  try {
    // Close existing connection if any
    if (wsClient) {
      wsClient.close();
      wsClient = null;
    }

    // Get WebSocket URL
    const endpoint = options.endpoint || 'app_builder';
    const url = options.url || getWebSocketUrl(endpoint);

    // Create new WebSocket client
    wsClient = new EnhancedWebSocketClient({
      url,
      autoConnect: false,
      autoReconnect: true,
      debug: options.debug || false,
      reconnectInterval: 2000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      ...options
    });

    // Set up event listeners with performance monitoring
    wsClient.addEventListener('open', () => {
      dispatch({ type: types.WEBSOCKET_CONNECTED });
      
      // Update WebSocket metrics
      if (unifiedPerformanceMonitor.metrics) {
        unifiedPerformanceMonitor.metrics.websocket.connected = true;
      }

      // Fetch app data via WebSocket after connection
      dispatch(fetchAppDataViaWebSocket());
    });

    wsClient.addEventListener('close', (event) => {
      // Update WebSocket metrics
      if (unifiedPerformanceMonitor.metrics) {
        unifiedPerformanceMonitor.metrics.websocket.connected = false;
      }
      
      dispatch({
        type: types.WEBSOCKET_DISCONNECTED,
        payload: {
          code: event.closeInfo?.code,
          reason: event.closeInfo?.reason,
          willReconnect: event.willReconnect
        }
      });

      // If not reconnecting, use fallback
      if (!event.willReconnect) {
        dispatch(fetchAppDataViaFallback());
      }
    });

    wsClient.addEventListener('error', (error) => {
      dispatch({
        type: types.WEBSOCKET_ERROR,
        payload: error.message || 'WebSocket error'
      });
    });

    wsClient.addEventListener('message', (event) => {
      const { data } = event;

      // Handle app data response
      if (data && data.type === 'app_data') {
        dispatch({
          type: types.FETCH_APP_DATA_SUCCESS,
          payload: data.data,
          source: 'websocket'
        });

        // Cache the data
        localStorage.setItem('app_data', JSON.stringify(data.data));
      }
    });

    // Connect to WebSocket
    await wsClient.open();

    dispatch({
      type: types.WEBSOCKET_INIT_SUCCESS,
      payload: { url }
    });

    return wsClient;
  } catch (error) {
    console.error('Failed to initialize WebSocket:', error);

    dispatch({
      type: types.WEBSOCKET_INIT_FAILURE,
      payload: error.message
    });

    // Use fallback on initialization failure
    dispatch(fetchAppDataViaFallback());

    return null;
  }
};

/**
 * Fetch app data via WebSocket
 * @returns {Function} Thunk function
 */
export const fetchAppDataViaWebSocket = () => async (dispatch) => {
  dispatch({ type: types.FETCH_APP_DATA_REQUEST, source: 'websocket' });

  try {
    // Check if WebSocket is connected
    if (!wsClient || wsClient.connectionState !== 1) { // 1 = OPEN
      throw new Error('WebSocket not connected');
    }

    // Request app data via WebSocket
    wsClient.send({
      type: 'request_app_data',
      timestamp: Date.now()
    });

    // Note: The response will be handled by the message event listener
    // We don't need to dispatch success here as it will be done in the listener

    return true;
  } catch (error) {
    console.error('Error fetching app data via WebSocket:', error);

    // Fall back to HTTP API
    dispatch(fetchAppDataViaFallback());

    return false;
  }
};

/**
 * Fetch app data via fallback HTTP API
 * @returns {Function} Thunk function
 */
export const fetchAppDataViaFallback = () => async (dispatch) => {
  dispatch({ type: types.FETCH_APP_DATA_REQUEST, source: 'fallback' });

  try {
    // Try to get cached data first
    const cachedData = localStorage.getItem('app_data');
    if (cachedData) {
      try {
        const parsedData = JSON.parse(cachedData);
        dispatch({
          type: types.FETCH_APP_DATA_SUCCESS,
          payload: parsedData,
          source: 'cache'
        });
      } catch (e) {
        console.warn('Failed to parse cached data');
      }
    }

    // Use fallback service that doesn't depend on WebSocket
    const data = await fallbackApiService.fetchAppData();

    // Cache the data
    localStorage.setItem('app_data', JSON.stringify(data));

    dispatch({
      type: types.FETCH_APP_DATA_SUCCESS,
      payload: data,
      source: 'fallback'
    });

    return data;
  } catch (error) {
    console.error('Error fetching app data via fallback:', error);
    dispatch({
      type: types.FETCH_APP_DATA_FAILURE,
      payload: error.message,
      source: 'fallback'
    });

    return null;
  }
};

/**
 * Main fetch app data function that tries WebSocket first, then falls back to HTTP
 * @returns {Function} Thunk function
 */
export const fetchAppData = () => async (dispatch, getState) => {
  dispatch({ type: types.FETCH_APP_DATA_REQUEST });

  try {
    // First, try to get cached data for immediate display
    const cachedData = localStorage.getItem('app_data');
    if (cachedData) {
      try {
        const parsedData = JSON.parse(cachedData);
        console.log('Using cached data while fetching fresh data');
        dispatch({
          type: types.FETCH_APP_DATA_SUCCESS,
          payload: parsedData,
          source: 'cache'
        });
      } catch (e) {
        console.warn('Failed to parse cached data:', e);
      }
    }

    // Check if WebSocket is initialized and connected
    const { websocket } = getState();
    const isWebSocketConnected = websocket && websocket.connected;

    let success = false;

    // Try WebSocket if connected
    if (isWebSocketConnected && wsClient) {
      console.log('Attempting to fetch data via WebSocket');
      try {
        success = await dispatch(fetchAppDataViaWebSocket());
        if (success) {
          console.log('Successfully fetched data via WebSocket');
          return;
        }
      } catch (wsError) {
        console.warn('WebSocket fetch failed, falling back to HTTP:', wsError);
      }
    } else {
      console.log('WebSocket not connected, using HTTP fallback');
    }

    // Fall back to HTTP API if WebSocket is not available or failed
    try {
      const data = await dispatch(fetchAppDataViaFallback());
      if (data) {
        console.log('Successfully fetched data via HTTP fallback');
        success = true;
      }
    } catch (httpError) {
      console.error('HTTP fallback failed:', httpError);
      throw httpError; // Re-throw to be caught by outer catch
    }

    if (!success) {
      console.warn('All fetch attempts failed');
      dispatch({
        type: types.FETCH_APP_DATA_FAILURE,
        payload: 'All fetch attempts failed',
        source: 'all'
      });
    }
  } catch (error) {
    console.error('Error in fetchAppData:', error);
    dispatch({
      type: types.FETCH_APP_DATA_FAILURE,
      payload: error.message || 'Unknown error'
    });
  }
};
