/**
 * WebSocket Integration Test
 *
 * This file contains integration tests for the WebSocketService with the backend.
 * It tests connection, reconnection, and message exchange.
 */

import WebSocketService from '../../services/WebSocketService';

class WebSocketIntegrationTest {
  constructor() {
    this.testResults = {
      connection: { success: false, message: '', time: 0 },
      reconnection: { success: false, message: '', time: 0 },
      messaging: { success: false, message: '', time: 0 },
      compression: { success: false, message: '', time: 0 },
      batching: { success: false, message: '', time: 0 },
      authentication: { success: false, message: '', time: 0 },
      rateLimiting: { success: false, message: '', time: 0 }
    };
    this.testPromises = [];
    this.testTimeout = 10000; // 10 seconds
    this.testInProgress = false;
  }

  /**
   * Run all integration tests
   * @returns {Promise<Object>} Test results
   */
  async runAllTests() {
    if (this.testInProgress) {
      return { error: 'Tests already in progress' };
    }

    this.testInProgress = true;
    this.resetResults();

    try {
      // Initialize WebSocketService with test configuration
      WebSocketService.init({
        autoConnect: false,
        autoReconnect: true,
        reconnectInterval: 1000,
        maxReconnectAttempts: 3,
        debug: true
      });

      // Configure security options
      WebSocketService.configureSecurityOptions({
        validateMessages: true,
        sanitizeMessages: true,
        rateLimiting: {
          enabled: true,
          maxMessagesPerSecond: 20,
          burstSize: 50
        },
        authToken: 'test-token'
      });

      // Configure performance options
      WebSocketService.configurePerformanceOptions({
        compression: {
          enabled: true,
          threshold: 100, // Lower threshold for testing
          level: 6
        },
        batchingEnabled: true,
        batchInterval: 50,
        maxBatchSize: 5
      });

      // Run tests in sequence
      await this.testConnection();
      await this.testReconnection();
      await this.testMessaging();
      await this.testCompression();
      await this.testBatching();
      await this.testAuthentication();
      await this.testRateLimiting();

      return this.testResults;
    } catch (error) {
      console.error('Integration test error:', error);
      return { error: error.message };
    } finally {
      // Clean up
      WebSocketService.disconnect();
      this.testInProgress = false;
    }
  }

  /**
   * Reset test results
   */
  resetResults() {
    for (const key in this.testResults) {
      this.testResults[key] = { success: false, message: '', time: 0 };
    }
  }

  /**
   * Test WebSocket connection
   */
  async testConnection() {
    console.log('Testing WebSocket connection...');
    const startTime = performance.now();

    try {
      // Connect to WebSocket server
      await WebSocketService.connect();

      // Check if connected
      if (WebSocketService.isConnected()) {
        this.testResults.connection = {
          success: true,
          message: 'Successfully connected to WebSocket server',
          time: performance.now() - startTime
        };
      } else {
        this.testResults.connection = {
          success: false,
          message: 'Failed to connect to WebSocket server',
          time: performance.now() - startTime
        };
      }
    } catch (error) {
      this.testResults.connection = {
        success: false,
        message: `Connection error: ${error.message}`,
        time: performance.now() - startTime
      };
    }

    console.log(`Connection test result: ${this.testResults.connection.success ? 'PASS' : 'FAIL'}`);
    return this.testResults.connection;
  }

  /**
   * Test WebSocket reconnection
   */
  async testReconnection() {
    console.log('Testing WebSocket reconnection...');
    const startTime = performance.now();

    try {
      // Ensure we're connected first
      if (!WebSocketService.isConnected()) {
        await WebSocketService.connect();
      }

      // Set up reconnection event listener
      const reconnectPromise = new Promise((resolve) => {
        const listener = () => {
          WebSocketService.removeEventListener('connect', listener);
          resolve();
        };
        WebSocketService.addEventListener('connect', listener);
      });

      // Force disconnect and reconnect
      WebSocketService.disconnect();

      // Wait for reconnection with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Reconnection timeout')), this.testTimeout);
      });

      await Promise.race([reconnectPromise, timeoutPromise]);

      this.testResults.reconnection = {
        success: WebSocketService.isConnected(),
        message: WebSocketService.isConnected()
          ? 'Successfully reconnected to WebSocket server'
          : 'Failed to reconnect to WebSocket server',
        time: performance.now() - startTime
      };
    } catch (error) {
      this.testResults.reconnection = {
        success: false,
        message: `Reconnection error: ${error.message}`,
        time: performance.now() - startTime
      };
    }

    console.log(`Reconnection test result: ${this.testResults.reconnection.success ? 'PASS' : 'FAIL'}`);
    return this.testResults.reconnection;
  }

  /**
   * Test WebSocket messaging
   */
  async testMessaging() {
    console.log('Testing WebSocket messaging...');
    const startTime = performance.now();

    try {
      // Ensure we're connected first
      if (!WebSocketService.isConnected()) {
        await WebSocketService.connect();
      }

      // Set up message event listener
      const messagePromise = new Promise((resolve) => {
        const listener = (data) => {
          if (data.type === 'test_response') {
            WebSocketService.removeEventListener('message', listener);
            resolve(data);
          }
        };
        WebSocketService.addEventListener('message', listener);
      });

      // Send test message
      const testMessage = {
        type: 'test_request',
        data: 'Hello, server!',
        timestamp: Date.now()
      };

      await WebSocketService.send(testMessage);

      // Wait for response with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Message response timeout')), this.testTimeout);
      });

      const response = await Promise.race([messagePromise, timeoutPromise]);

      this.testResults.messaging = {
        success: response && response.type === 'test_response',
        message: response && response.type === 'test_response'
          ? 'Successfully sent and received messages'
          : 'Failed to receive response message',
        time: performance.now() - startTime
      };
    } catch (error) {
      this.testResults.messaging = {
        success: false,
        message: `Messaging error: ${error.message}`,
        time: performance.now() - startTime
      };
    }

    console.log(`Messaging test result: ${this.testResults.messaging.success ? 'PASS' : 'FAIL'}`);
    return this.testResults.messaging;
  }

  /**
   * Test WebSocket message compression
   */
  async testCompression() {
    console.log('Testing WebSocket compression...');
    const startTime = performance.now();

    try {
      // Ensure we're connected first
      if (!WebSocketService.isConnected()) {
        await WebSocketService.connect();
      }

      // Create a large message that will trigger compression
      const largeData = 'A'.repeat(2000); // 2KB of data
      const testMessage = {
        type: 'test_compression',
        data: largeData,
        timestamp: Date.now()
      };

      // Send with compression
      const result = await WebSocketService.send(testMessage, { compress: true });

      this.testResults.compression = {
        success: result && result.compressed,
        message: result && result.compressed
          ? 'Successfully compressed message'
          : 'Failed to compress message',
        time: performance.now() - startTime
      };
    } catch (error) {
      this.testResults.compression = {
        success: false,
        message: `Compression error: ${error.message}`,
        time: performance.now() - startTime
      };
    }

    console.log(`Compression test result: ${this.testResults.compression.success ? 'PASS' : 'FAIL'}`);
    return this.testResults.compression;
  }

  /**
   * Test WebSocket message batching
   */
  async testBatching() {
    console.log('Testing WebSocket batching...');
    const startTime = performance.now();

    try {
      // Ensure we're connected first
      if (!WebSocketService.isConnected()) {
        await WebSocketService.connect();
      }

      // Set up batch event listener
      const batchPromise = new Promise((resolve) => {
        const listener = (data) => {
          if (data.type === 'batch') {
            WebSocketService.removeEventListener('batch', listener);
            resolve(data);
          }
        };
        WebSocketService.addEventListener('batch', listener);
      });

      // Send multiple messages quickly to trigger batching
      for (let i = 0; i < 5; i++) {
        WebSocketService.send({
          type: 'test_batch',
          index: i,
          timestamp: Date.now()
        });
      }

      // Wait for batch with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Batch timeout')), this.testTimeout);
      });

      const batchResult = await Promise.race([batchPromise, timeoutPromise]);

      this.testResults.batching = {
        success: batchResult && batchResult.type === 'batch' && batchResult.messages.length > 1,
        message: batchResult && batchResult.type === 'batch' && batchResult.messages.length > 1
          ? `Successfully batched ${batchResult.messages.length} messages`
          : 'Failed to batch messages',
        time: performance.now() - startTime
      };
    } catch (error) {
      this.testResults.batching = {
        success: false,
        message: `Batching error: ${error.message}`,
        time: performance.now() - startTime
      };
    }

    console.log(`Batching test result: ${this.testResults.batching.success ? 'PASS' : 'FAIL'}`);
    return this.testResults.batching;
  }

  /**
   * Test WebSocket authentication
   */
  async testAuthentication() {
    console.log('Testing WebSocket authentication...');
    const startTime = performance.now();

    try {
      // Ensure we're connected first
      if (!WebSocketService.isConnected()) {
        await WebSocketService.connect();
      }

      // Set up auth response event listener
      const authPromise = new Promise((resolve) => {
        const listener = (data) => {
          if (data.type === 'auth_response') {
            WebSocketService.removeEventListener('message', listener);
            resolve(data);
          }
        };
        WebSocketService.addEventListener('message', listener);
      });

      // Configure authentication
      WebSocketService.configureSecurityOptions({
        authToken: 'test-auth-token'
      });

      // Send auth request
      await WebSocketService.send({
        type: 'auth_request',
        timestamp: Date.now()
      });

      // Wait for auth response with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Authentication timeout')), this.testTimeout);
      });

      const authResult = await Promise.race([authPromise, timeoutPromise]);

      this.testResults.authentication = {
        success: authResult && authResult.type === 'auth_response' && authResult.authenticated === true,
        message: authResult && authResult.type === 'auth_response' && authResult.authenticated === true
          ? 'Successfully authenticated with server'
          : 'Failed to authenticate with server',
        time: performance.now() - startTime
      };
    } catch (error) {
      this.testResults.authentication = {
        success: false,
        message: `Authentication error: ${error.message}`,
        time: performance.now() - startTime
      };
    }

    console.log(`Authentication test result: ${this.testResults.authentication.success ? 'PASS' : 'FAIL'}`);
    return this.testResults.authentication;
  }

  /**
   * Test WebSocket rate limiting
   */
  async testRateLimiting() {
    console.log('Testing WebSocket rate limiting...');
    const startTime = performance.now();

    try {
      // Ensure we're connected first
      if (!WebSocketService.isConnected()) {
        await WebSocketService.connect();
      }

      // Set up rate limit event listener
      const rateLimitPromise = new Promise((resolve) => {
        const listener = (data) => {
          WebSocketService.removeEventListener('rate_limited', listener);
          resolve(data);
        };
        WebSocketService.addEventListener('rate_limited', listener);
      });

      // Configure aggressive rate limiting for testing
      WebSocketService.configureSecurityOptions({
        rateLimiting: {
          enabled: true,
          maxMessagesPerSecond: 5,
          burstSize: 3
        }
      });

      // Send many messages quickly to trigger rate limiting
      const sendPromises = [];
      for (let i = 0; i < 20; i++) {
        sendPromises.push(
          WebSocketService.send({
            type: 'test_rate_limit',
            index: i,
            timestamp: Date.now()
          })
        );
      }

      // Wait for rate limit event with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Rate limit timeout')), this.testTimeout);
      });

      const rateLimitResult = await Promise.race([rateLimitPromise, timeoutPromise]);

      // Wait for all send promises to complete
      const sendResults = await Promise.allSettled(sendPromises);
      const queuedCount = sendResults.filter(r => r.status === 'fulfilled' && r.value.rateLimited).length;

      this.testResults.rateLimiting = {
        success: rateLimitResult && queuedCount > 0,
        message: rateLimitResult && queuedCount > 0
          ? `Successfully rate limited ${queuedCount} messages`
          : 'Failed to trigger rate limiting',
        time: performance.now() - startTime
      };
    } catch (error) {
      this.testResults.rateLimiting = {
        success: false,
        message: `Rate limiting error: ${error.message}`,
        time: performance.now() - startTime
      };
    }

    console.log(`Rate limiting test result: ${this.testResults.rateLimiting.success ? 'PASS' : 'FAIL'}`);
    return this.testResults.rateLimiting;
  }
}

export default WebSocketIntegrationTest;
