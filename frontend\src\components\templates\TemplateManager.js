import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Button, Badge, Tooltip, Space, Typography, Modal, List, Tabs } from 'antd';
import { 
  AppstoreOutlined, 
  SaveOutlined, 
  DownloadOutlined,
  EyeOutlined,
  DeleteOutlined,
  PlusOutlined,
  LayoutOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import useTemplates from '../../hooks/useTemplates';
import TemplateGallery from './TemplateGallery';
import TemplateEditor from './TemplateEditor';

const { Text } = Typography;
const { TabPane } = Tabs;

/**
 * Template Manager
 * 
 * Main template management component that provides template creation,
 * editing, saving, and loading functionality.
 */

const TemplateContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CompactTemplate = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: #e6f7ff;
    border-color: #40a9ff;
  }
  
  .template-icon {
    color: #1890ff;
    font-size: 16px;
  }
  
  .template-text {
    flex: 1;
    font-size: 13px;
    color: #262626;
  }
  
  .template-count {
    background: #1890ff;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 600;
  }
`;

const TemplateManager = ({
  templates = [],
  loading = false,
  onSave,
  onLoad,
  onDelete,
  compact = false,
  components = [],
  selectedComponent = null
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('gallery');
  const [saveModalVisible, setSaveModalVisible] = useState(false);

  // Templates hook
  const {
    templates: hookTemplates,
    loading: hookLoading,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
    refreshTemplates
  } = useTemplates({
    enabled: true,
    autoRefresh: true
  });

  // Use hook data if no props provided
  const activeTemplates = templates.length > 0 ? templates : hookTemplates;
  const isLoading = loading || hookLoading;
  const templateCount = activeTemplates.length;

  // Handle template operations
  const handleSave = useCallback((templateData) => {
    if (onSave) {
      onSave(templateData);
    } else {
      saveAsTemplate(templateData);
    }
    setSaveModalVisible(false);
  }, [onSave, saveAsTemplate]);

  const handleLoad = useCallback((template) => {
    if (onLoad) {
      onLoad(template);
    } else {
      loadTemplate(template);
    }
    setModalVisible(false);
  }, [onLoad, loadTemplate]);

  const handleDelete = useCallback((template) => {
    if (onDelete) {
      onDelete(template);
    } else {
      deleteTemplate(template.id);
    }
  }, [onDelete, deleteTemplate]);

  // Compact mode for header/toolbar
  if (compact) {
    return (
      <TemplateContainer>
        <CompactTemplate onClick={() => setModalVisible(true)}>
          <AppstoreOutlined className="template-icon" />
          <span className="template-text">Templates</span>
          {templateCount > 0 && (
            <span className="template-count">{templateCount}</span>
          )}
        </CompactTemplate>

        <Tooltip title="Save as Template">
          <Button
            type="text"
            icon={<SaveOutlined />}
            onClick={() => setSaveModalVisible(true)}
            disabled={!components || components.length === 0}
          />
        </Tooltip>

        {/* Template Gallery Modal */}
        <Modal
          title="Template Manager"
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={null}
          width={800}
          style={{ top: 20 }}
        >
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="Gallery" key="gallery">
              <TemplateGallery
                templates={activeTemplates}
                loading={isLoading}
                onLoad={handleLoad}
                onDelete={handleDelete}
                showActions={true}
              />
            </TabPane>
            <TabPane tab="Create" key="create">
              <TemplateEditor
                components={components}
                onSave={handleSave}
                mode="create"
              />
            </TabPane>
          </Tabs>
        </Modal>

        {/* Save Template Modal */}
        <Modal
          title="Save as Template"
          open={saveModalVisible}
          onCancel={() => setSaveModalVisible(false)}
          footer={null}
          width={600}
        >
          <TemplateEditor
            components={components}
            onSave={handleSave}
            onCancel={() => setSaveModalVisible(false)}
            mode="save"
          />
        </Modal>
      </TemplateContainer>
    );
  }

  // Full mode for dedicated template management area
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <AppstoreOutlined style={{ color: '#1890ff' }} />
            <Text strong>Templates</Text>
            {templateCount > 0 && (
              <Badge count={templateCount} style={{ backgroundColor: '#1890ff' }} />
            )}
          </Space>
          
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setSaveModalVisible(true)}
              disabled={!components || components.length === 0}
            >
              Save Template
            </Button>
            <Button
              icon={<AppstoreOutlined />}
              onClick={() => setModalVisible(true)}
            >
              Browse
            </Button>
          </Space>
        </div>

        {isLoading ? (
          <Card loading={true} />
        ) : templateCount > 0 ? (
          <List
            grid={{ gutter: 16, column: 2 }}
            dataSource={activeTemplates.slice(0, 4)}
            renderItem={(template) => (
              <List.Item>
                <Card
                  size="small"
                  cover={
                    template.thumbnail && (
                      <img
                        alt={template.name}
                        src={template.thumbnail}
                        style={{ height: 120, objectFit: 'cover' }}
                      />
                    )
                  }
                  actions={[
                    <Tooltip key="load" title="Load Template">
                      <Button
                        type="text"
                        icon={<DownloadOutlined />}
                        onClick={() => handleLoad(template)}
                      />
                    </Tooltip>,
                    <Tooltip key="preview" title="Preview">
                      <Button
                        type="text"
                        icon={<EyeOutlined />}
                        onClick={() => {
                          setModalVisible(true);
                          setActiveTab('gallery');
                        }}
                      />
                    </Tooltip>,
                    <Tooltip key="delete" title="Delete">
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDelete(template)}
                      />
                    </Tooltip>
                  ]}
                >
                  <Card.Meta
                    title={template.name}
                    description={template.description}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Space>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {template.category}
                      </Text>
                      {template.isPublic && (
                        <Badge status="success" text="Public" />
                      )}
                    </Space>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        ) : (
          <Card>
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <LayoutOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">No templates available</Text>
              </div>
              <div style={{ marginTop: 8 }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setSaveModalVisible(true)}
                  disabled={!components || components.length === 0}
                >
                  Create Your First Template
                </Button>
              </div>
            </div>
          </Card>
        )}
      </Space>

      {/* Template Gallery Modal */}
      <Modal
        title="Template Manager"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={900}
        style={{ top: 20 }}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Gallery" key="gallery">
            <TemplateGallery
              templates={activeTemplates}
              loading={isLoading}
              onLoad={handleLoad}
              onDelete={handleDelete}
              showActions={true}
            />
          </TabPane>
          <TabPane tab="Create" key="create">
            <TemplateEditor
              components={components}
              onSave={handleSave}
              mode="create"
            />
          </TabPane>
        </Tabs>
      </Modal>

      {/* Save Template Modal */}
      <Modal
        title="Save as Template"
        open={saveModalVisible}
        onCancel={() => setSaveModalVisible(false)}
        footer={null}
        width={600}
      >
        <TemplateEditor
          components={components}
          onSave={handleSave}
          onCancel={() => setSaveModalVisible(false)}
          mode="save"
        />
      </Modal>
    </div>
  );
};

export default TemplateManager;
