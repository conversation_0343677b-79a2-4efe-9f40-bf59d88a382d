<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final WebSocket Test - Issue Resolution</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success { background-color: #d4edda; border-color: #28a745; color: #155724; }
        .error { background-color: #f8d7da; border-color: #dc3545; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        .warning { background-color: #fff3cd; border-color: #ffc107; color: #856404; }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .connection-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .status-connecting { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSocket Connection Test - Final Verification</h1>
        
        <div class="status info">
            <strong>✅ Issue Resolution Summary:</strong><br>
            • Fixed: "Invalid frame header" error by switching from Django runserver to ASGI server (Daphne)<br>
            • Backend: Running on port 8000 with WebSocket support<br>
            • Frontend: Running on port 3000 with WebSocket proxy configured<br>
            • Ready to test both direct and proxied connections
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h3>🎯 Direct Backend Connection</h3>
                <p><strong>URL:</strong> ws://localhost:8000/ws/test/</p>
                <p><strong>Status:</strong> <span class="connection-status status-disconnected" id="direct-status"></span><span id="direct-text">Disconnected</span></p>
                <button id="direct-connect" class="btn-primary">Test Direct Connection</button>
                <button id="direct-send" class="btn-success" disabled>Send Test Message</button>
            </div>
            
            <div class="test-section">
                <h3>🔄 Frontend Proxy Connection</h3>
                <p><strong>URL:</strong> ws://localhost:3000/ws/test/</p>
                <p><strong>Status:</strong> <span class="connection-status status-disconnected" id="proxy-status"></span><span id="proxy-text">Disconnected</span></p>
                <button id="proxy-connect" class="btn-primary">Test Proxy Connection</button>
                <button id="proxy-send" class="btn-success" disabled>Send Test Message</button>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button id="test-all" class="btn-warning">🚀 Run All Tests</button>
            <button id="clear-log" class="btn-danger">🗑️ Clear Log</button>
        </div>

        <h3>📋 Test Results Log</h3>
        <div id="log"></div>
    </div>

    <script>
        let directWs = null;
        let proxyWs = null;
        const log = document.getElementById('log');

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            log.textContent += logEntry;
            log.scrollTop = log.scrollHeight;
            console.log(`WebSocket Test: ${message}`);
        }

        function updateStatus(type, status) {
            const statusEl = document.getElementById(`${type}-status`);
            const textEl = document.getElementById(`${type}-text`);
            const connectBtn = document.getElementById(`${type}-connect`);
            const sendBtn = document.getElementById(`${type}-send`);
            
            statusEl.className = `connection-status status-${status}`;
            
            switch(status) {
                case 'connected':
                    textEl.textContent = 'Connected ✅';
                    connectBtn.textContent = 'Disconnect';
                    connectBtn.className = 'btn-danger';
                    sendBtn.disabled = false;
                    break;
                case 'connecting':
                    textEl.textContent = 'Connecting...';
                    connectBtn.textContent = 'Connecting...';
                    connectBtn.disabled = true;
                    sendBtn.disabled = true;
                    break;
                case 'disconnected':
                    textEl.textContent = 'Disconnected';
                    connectBtn.textContent = `Test ${type === 'direct' ? 'Direct' : 'Proxy'} Connection`;
                    connectBtn.className = 'btn-primary';
                    connectBtn.disabled = false;
                    sendBtn.disabled = true;
                    break;
            }
        }

        function testConnection(type, url) {
            addLog(`🔌 Testing ${type} connection to: ${url}`);
            updateStatus(type, 'connecting');
            
            const ws = new WebSocket(url);
            
            ws.onopen = () => {
                addLog(`✅ ${type} connection SUCCESSFUL!`, 'success');
                updateStatus(type, 'connected');
                
                // Send initial ping
                ws.send(JSON.stringify({type: 'ping', message: `Hello from ${type} connection!`}));
            };
            
            ws.onmessage = (event) => {
                addLog(`📥 ${type} received: ${event.data}`, 'success');
            };
            
            ws.onclose = (event) => {
                addLog(`🔌 ${type} connection closed: code=${event.code}, reason="${event.reason}"`, 'warning');
                updateStatus(type, 'disconnected');
            };
            
            ws.onerror = (error) => {
                addLog(`❌ ${type} connection ERROR: ${error.message || 'Connection failed'}`, 'error');
                updateStatus(type, 'disconnected');
            };
            
            // Store reference
            if (type === 'direct') {
                directWs = ws;
            } else {
                proxyWs = ws;
            }
            
            // Timeout after 10 seconds
            setTimeout(() => {
                if (ws.readyState === WebSocket.CONNECTING) {
                    addLog(`⏰ ${type} connection timeout`, 'error');
                    ws.close();
                }
            }, 10000);
        }

        function sendTestMessage(type) {
            const ws = type === 'direct' ? directWs : proxyWs;
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'test_message',
                    content: `Test message from ${type} connection`,
                    timestamp: new Date().toISOString()
                };
                ws.send(JSON.stringify(message));
                addLog(`📤 ${type} sent: ${JSON.stringify(message)}`);
            }
        }

        // Event listeners
        document.getElementById('direct-connect').addEventListener('click', () => {
            if (directWs && directWs.readyState === WebSocket.OPEN) {
                directWs.close();
            } else {
                testConnection('direct', 'ws://localhost:8000/ws/test/');
            }
        });

        document.getElementById('proxy-connect').addEventListener('click', () => {
            if (proxyWs && proxyWs.readyState === WebSocket.OPEN) {
                proxyWs.close();
            } else {
                testConnection('proxy', 'ws://localhost:3000/ws/test/');
            }
        });

        document.getElementById('direct-send').addEventListener('click', () => {
            sendTestMessage('direct');
        });

        document.getElementById('proxy-send').addEventListener('click', () => {
            sendTestMessage('proxy');
        });

        document.getElementById('test-all').addEventListener('click', () => {
            addLog('🚀 Starting comprehensive WebSocket test...', 'info');
            addLog('='.repeat(50));
            
            // Test direct connection first
            setTimeout(() => testConnection('direct', 'ws://localhost:8000/ws/test/'), 500);
            
            // Test proxy connection after direct
            setTimeout(() => testConnection('proxy', 'ws://localhost:3000/ws/test/'), 2000);
        });

        document.getElementById('clear-log').addEventListener('click', () => {
            log.textContent = '';
        });

        // Initial log
        addLog('🚀 WebSocket Test Page Loaded');
        addLog('📋 Ready to test WebSocket connections');
        addLog('🎯 Click "Run All Tests" to test both connections automatically');
        addLog('');
    </script>
</body>
</html>
