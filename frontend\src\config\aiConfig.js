/**
 * AI Configuration
 * Centralized configuration for AI features and services
 */

// Environment variables
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const AI_ENABLED = process.env.REACT_APP_AI_ENABLED !== 'false'; // Default to true
const AI_FALLBACK_ENABLED = process.env.REACT_APP_AI_FALLBACK_ENABLED !== 'false'; // Default to true

// AI Service Configuration
export const aiConfig = {
  // Core settings
  enabled: AI_ENABLED,
  fallbackEnabled: AI_FALLBACK_ENABLED,
  
  // API settings
  baseUrl: `${API_URL}/api/ai`,
  timeout: 10000, // 10 seconds
  retryAttempts: 2,
  retryDelay: 1000, // 1 second
  
  // Cache settings
  cacheEnabled: true,
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
  
  // WebSocket settings
  websocketEnabled: true,
  websocketTimeout: 10000, // 10 seconds
  
  // Feature flags
  features: {
    layoutSuggestions: true,
    componentCombinations: true,
    appAnalysis: true,
    realTimeUpdates: true,
    collaborativeAI: false, // Experimental
  },
  
  // Fallback behavior
  fallback: {
    showWarnings: false, // Set to false to reduce console noise
    useBasicAnalysis: true,
    provideFallbackSuggestions: true,
    gracefulDegradation: true,
  },
  
  // Performance settings
  performance: {
    debounceDelay: 500, // Debounce AI requests
    maxConcurrentRequests: 3,
    backgroundRefresh: true,
    lazyLoading: true,
  }
};

// Helper functions
export const isAIEnabled = () => aiConfig.enabled;
export const isFeatureEnabled = (feature) => aiConfig.enabled && aiConfig.features[feature];
export const shouldShowWarnings = () => aiConfig.fallback.showWarnings;

// Service availability checker
export const checkAIServiceAvailability = async () => {
  if (!aiConfig.enabled) {
    return { available: false, reason: 'AI features disabled' };
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), aiConfig.timeout);

    const response = await fetch(`${aiConfig.baseUrl}/health/`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      return { available: true, status: response.status };
    } else {
      return { available: false, reason: `HTTP ${response.status}` };
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      return { available: false, reason: 'Timeout' };
    }
    return { available: false, reason: error.message };
  }
};

// Error handling configuration
export const errorConfig = {
  // Log levels: 'error', 'warn', 'info', 'debug'
  logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'warn',
  
  // Error reporting
  reportErrors: false, // Set to true to enable error reporting
  
  // User-facing messages
  messages: {
    serviceUnavailable: 'AI suggestions are temporarily unavailable. Using basic recommendations.',
    networkError: 'Unable to connect to AI service. Check your internet connection.',
    timeout: 'AI service is taking too long to respond. Using cached results.',
    fallback: 'Using offline AI suggestions.',
  }
};

// Development helpers
export const devConfig = {
  mockResponses: process.env.NODE_ENV === 'development',
  debugMode: process.env.REACT_APP_AI_DEBUG === 'true',
  verboseLogging: process.env.REACT_APP_AI_VERBOSE === 'true',
};

export default aiConfig;
