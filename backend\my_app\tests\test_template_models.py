"""
Tests for template models (LayoutTemplate and AppTemplate).
"""
from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from my_app.models import LayoutTemplate, AppTemplate
import json


class LayoutTemplateModelTest(TestCase):
    """Test cases for LayoutTemplate model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.layout_data = {
            'name': 'Test Layout',
            'description': 'A test layout template',
            'layout_type': 'grid',
            'components': {
                'structure': 'grid',
                'columns': 12,
                'rows': 4
            },
            'default_props': {
                'responsive': True,
                'theme': 'light'
            },
            'user': self.user,
            'is_public': False
        }
    
    def test_create_layout_template(self):
        """Test creating a layout template"""
        layout = LayoutTemplate.objects.create(**self.layout_data)
        
        self.assertEqual(layout.name, 'Test Layout')
        self.assertEqual(layout.layout_type, 'grid')
        self.assertEqual(layout.user, self.user)
        self.assertFalse(layout.is_public)
        self.assertIsNotNone(layout.created_at)
    
    def test_layout_template_str_method(self):
        """Test string representation of layout template"""
        layout = LayoutTemplate.objects.create(**self.layout_data)
        self.assertEqual(str(layout), 'Test Layout')
    
    def test_get_components_json_method(self):
        """Test get_components_json method"""
        layout = LayoutTemplate.objects.create(**self.layout_data)
        components = layout.get_components_json()
        
        self.assertIsInstance(components, dict)
        self.assertEqual(components['structure'], 'grid')
        self.assertEqual(components['columns'], 12)
    
    def test_get_default_props_json_method(self):
        """Test get_default_props_json method"""
        layout = LayoutTemplate.objects.create(**self.layout_data)
        props = layout.get_default_props_json()
        
        self.assertIsInstance(props, dict)
        self.assertTrue(props['responsive'])
        self.assertEqual(props['theme'], 'light')
    
    def test_layout_template_ordering(self):
        """Test layout template ordering by created_at"""
        layout1 = LayoutTemplate.objects.create(
            name='Layout 1',
            layout_type='grid',
            user=self.user
        )
        layout2 = LayoutTemplate.objects.create(
            name='Layout 2',
            layout_type='flex',
            user=self.user
        )
        
        layouts = LayoutTemplate.objects.all()
        self.assertEqual(layouts[0], layout2)  # Most recent first
        self.assertEqual(layouts[1], layout1)
    
    def test_layout_template_indexes(self):
        """Test that database indexes are created"""
        # This test ensures the model is properly configured
        # The actual index creation is tested during migration
        layout = LayoutTemplate.objects.create(**self.layout_data)
        self.assertIsNotNone(layout.id)


class AppTemplateModelTest(TestCase):
    """Test cases for AppTemplate model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.app_data = {
            'name': 'Test App',
            'description': 'A test app template',
            'app_category': 'business',
            'components': {
                'pages': [
                    {'name': 'home', 'components': ['header', 'content']},
                    {'name': 'about', 'components': ['header', 'text']}
                ]
            },
            'default_props': {
                'theme': 'modern',
                'primaryColor': '#1890ff'
            },
            'required_components': ['header', 'button', 'form'],
            'preview_image': 'https://example.com/preview.jpg',
            'user': self.user,
            'is_public': True
        }
    
    def test_create_app_template(self):
        """Test creating an app template"""
        app = AppTemplate.objects.create(**self.app_data)
        
        self.assertEqual(app.name, 'Test App')
        self.assertEqual(app.app_category, 'business')
        self.assertEqual(app.user, self.user)
        self.assertTrue(app.is_public)
        self.assertIsNotNone(app.created_at)
    
    def test_app_template_str_method(self):
        """Test string representation of app template"""
        app = AppTemplate.objects.create(**self.app_data)
        self.assertEqual(str(app), 'Test App')
    
    def test_app_category_choices(self):
        """Test app category choices"""
        valid_categories = [choice[0] for choice in AppTemplate.APP_CATEGORIES]
        
        self.assertIn('business', valid_categories)
        self.assertIn('ecommerce', valid_categories)
        self.assertIn('portfolio', valid_categories)
        self.assertIn('dashboard', valid_categories)
        self.assertIn('other', valid_categories)
    
    def test_get_components_json_method(self):
        """Test get_components_json method"""
        app = AppTemplate.objects.create(**self.app_data)
        components = app.get_components_json()
        
        self.assertIsInstance(components, dict)
        self.assertIn('pages', components)
        self.assertEqual(len(components['pages']), 2)
    
    def test_get_default_props_json_method(self):
        """Test get_default_props_json method"""
        app = AppTemplate.objects.create(**self.app_data)
        props = app.get_default_props_json()
        
        self.assertIsInstance(props, dict)
        self.assertEqual(props['theme'], 'modern')
        self.assertEqual(props['primaryColor'], '#1890ff')
    
    def test_get_required_components_list_method(self):
        """Test get_required_components_list method"""
        app = AppTemplate.objects.create(**self.app_data)
        components = app.get_required_components_list()
        
        self.assertIsInstance(components, list)
        self.assertIn('header', components)
        self.assertIn('button', components)
        self.assertIn('form', components)
    
    def test_app_template_with_empty_fields(self):
        """Test app template with empty optional fields"""
        minimal_data = {
            'name': 'Minimal App',
            'app_category': 'other',
            'user': self.user
        }
        
        app = AppTemplate.objects.create(**minimal_data)
        
        self.assertEqual(app.name, 'Minimal App')
        self.assertEqual(app.description, '')
        self.assertEqual(app.get_components_json(), {})
        self.assertEqual(app.get_default_props_json(), {})
        self.assertEqual(app.get_required_components_list(), [])
    
    def test_app_template_ordering(self):
        """Test app template ordering by created_at"""
        app1 = AppTemplate.objects.create(
            name='App 1',
            app_category='business',
            user=self.user
        )
        app2 = AppTemplate.objects.create(
            name='App 2',
            app_category='ecommerce',
            user=self.user
        )
        
        apps = AppTemplate.objects.all()
        self.assertEqual(apps[0], app2)  # Most recent first
        self.assertEqual(apps[1], app1)


class TemplateModelIntegrationTest(TestCase):
    """Integration tests for template models"""
    
    def setUp(self):
        """Set up test data"""
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_user_template_relationships(self):
        """Test user relationships with templates"""
        # Create templates for user1
        layout1 = LayoutTemplate.objects.create(
            name='User1 Layout',
            layout_type='grid',
            user=self.user1
        )
        app1 = AppTemplate.objects.create(
            name='User1 App',
            app_category='business',
            user=self.user1
        )
        
        # Create templates for user2
        layout2 = LayoutTemplate.objects.create(
            name='User2 Layout',
            layout_type='flex',
            user=self.user2
        )
        
        # Test related managers
        self.assertEqual(self.user1.layout_templates.count(), 1)
        self.assertEqual(self.user1.app_templates.count(), 1)
        self.assertEqual(self.user2.layout_templates.count(), 1)
        self.assertEqual(self.user2.app_templates.count(), 0)
        
        # Test specific templates
        self.assertEqual(self.user1.layout_templates.first(), layout1)
        self.assertEqual(self.user1.app_templates.first(), app1)
        self.assertEqual(self.user2.layout_templates.first(), layout2)
    
    def test_public_vs_private_templates(self):
        """Test public vs private template filtering"""
        # Create public and private templates
        LayoutTemplate.objects.create(
            name='Public Layout',
            layout_type='grid',
            user=self.user1,
            is_public=True
        )
        LayoutTemplate.objects.create(
            name='Private Layout',
            layout_type='flex',
            user=self.user1,
            is_public=False
        )
        
        AppTemplate.objects.create(
            name='Public App',
            app_category='business',
            user=self.user1,
            is_public=True
        )
        AppTemplate.objects.create(
            name='Private App',
            app_category='ecommerce',
            user=self.user1,
            is_public=False
        )
        
        # Test filtering
        public_layouts = LayoutTemplate.objects.filter(is_public=True)
        private_layouts = LayoutTemplate.objects.filter(is_public=False)
        public_apps = AppTemplate.objects.filter(is_public=True)
        private_apps = AppTemplate.objects.filter(is_public=False)
        
        self.assertEqual(public_layouts.count(), 1)
        self.assertEqual(private_layouts.count(), 1)
        self.assertEqual(public_apps.count(), 1)
        self.assertEqual(private_apps.count(), 1)
        
        self.assertEqual(public_layouts.first().name, 'Public Layout')
        self.assertEqual(private_layouts.first().name, 'Private Layout')
        self.assertEqual(public_apps.first().name, 'Public App')
        self.assertEqual(private_apps.first().name, 'Private App')
