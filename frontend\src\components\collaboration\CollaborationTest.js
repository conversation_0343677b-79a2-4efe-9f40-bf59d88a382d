import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Alert, 
  List, 
  Tag,
  message,
  Input,
  Divider
} from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  LoadingOutlined,
  SendOutlined,
  CommentOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { CollaborationProvider, useCollaboration } from '../../contexts/CollaborationContext';
import { useAuth } from '../../contexts/AuthContext';

const { Title, Text } = Typography;
const { TextArea } = Input;

// Test component for collaboration features
const CollaborationTestInner = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testMessage, setTestMessage] = useState('');
  
  const {
    currentSession,
    isConnected,
    isJoining,
    participants,
    comments,
    joinSession,
    leaveSession,
    createComment,
    sendOperation,
    updatePresence
  } = useCollaboration();
  
  const { user, isAuthenticated } = useAuth();

  const tests = [
    {
      id: 'auth',
      name: 'User Authentication',
      description: 'Check if user is authenticated',
      test: () => isAuthenticated && user,
      status: isAuthenticated && user ? 'success' : 'error'
    },
    {
      id: 'websocket',
      name: 'WebSocket Connection',
      description: 'Test WebSocket connection to collaboration service',
      test: () => isConnected,
      status: isConnected ? 'success' : isJoining ? 'loading' : 'error'
    },
    {
      id: 'session',
      name: 'Collaboration Session',
      description: 'Join and manage collaboration session',
      test: () => currentSession !== null,
      status: currentSession ? 'success' : 'error'
    },
    {
      id: 'presence',
      name: 'User Presence',
      description: 'Track user presence and cursor position',
      test: () => participants.length >= 0,
      status: 'success'
    },
    {
      id: 'comments',
      name: 'Comment System',
      description: 'Create and manage comments',
      test: () => true, // Will be tested manually
      status: 'pending'
    },
    {
      id: 'operations',
      name: 'Real-time Operations',
      description: 'Send and receive component operations',
      test: () => true, // Will be tested manually
      status: 'pending'
    }
  ];

  useEffect(() => {
    // Auto-join test session
    if (isAuthenticated && !currentSession && !isJoining) {
      joinSession('test-session-123');
    }
  }, [isAuthenticated, currentSession, isJoining, joinSession]);

  const runAllTests = async () => {
    setIsRunningTests(true);
    const results = {};

    for (const test of tests) {
      try {
        const result = await test.test();
        results[test.id] = {
          passed: !!result,
          error: null
        };
      } catch (error) {
        results[test.id] = {
          passed: false,
          error: error.message
        };
      }
    }

    setTestResults(results);
    setIsRunningTests(false);
    
    const passedCount = Object.values(results).filter(r => r.passed).length;
    message.info(`Tests completed: ${passedCount}/${tests.length} passed`);
  };

  const testComment = () => {
    if (!isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }

    const testContent = `Test comment from ${user?.username || 'Anonymous'} at ${new Date().toLocaleTimeString()}`;
    createComment(testContent, null, { x: 100, y: 100 });
    message.success('Test comment created');
  };

  const testOperation = () => {
    if (!isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }

    sendOperation('component_add', `test-component-${Date.now()}`, {
      type: 'button',
      props: { text: 'Test Button' },
      position: { x: Math.random() * 200, y: Math.random() * 200 }
    });
    message.success('Test operation sent');
  };

  const testPresence = () => {
    if (!isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }

    updatePresence({
      cursor_position: { 
        x: Math.random() * 300, 
        y: Math.random() * 300 
      }
    });
    message.success('Presence updated');
  };

  const sendTestMessage = () => {
    if (!testMessage.trim()) return;
    
    createComment(testMessage);
    setTestMessage('');
    message.success('Message sent to all collaborators');
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'loading': return <LoadingOutlined style={{ color: '#1890ff' }} />;
      default: return <LoadingOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <Title level={2}>
        <TeamOutlined style={{ marginRight: 8 }} />
        Collaboration Features Test
      </Title>

      {/* Connection Status */}
      <Alert
        message={
          isConnected 
            ? `Connected to collaboration session: ${currentSession?.name || 'Unknown'}`
            : isJoining 
            ? 'Connecting to collaboration session...'
            : 'Not connected to collaboration session'
        }
        type={isConnected ? 'success' : isJoining ? 'info' : 'warning'}
        style={{ marginBottom: 24 }}
        showIcon
      />

      {/* Test Results */}
      <Card title="Automated Tests" style={{ marginBottom: 24 }}>
        <Space style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            onClick={runAllTests}
            loading={isRunningTests}
          >
            Run All Tests
          </Button>
        </Space>

        <List
          dataSource={tests}
          renderItem={(test) => (
            <List.Item>
              <List.Item.Meta
                avatar={getStatusIcon(test.status)}
                title={test.name}
                description={test.description}
              />
              {testResults[test.id] && (
                <Tag color={testResults[test.id].passed ? 'success' : 'error'}>
                  {testResults[test.id].passed ? 'PASS' : 'FAIL'}
                </Tag>
              )}
            </List.Item>
          )}
        />
      </Card>

      {/* Manual Tests */}
      <Card title="Manual Tests" style={{ marginBottom: 24 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Test Comments:</Text>
            <br />
            <Button 
              onClick={testComment}
              disabled={!isConnected}
              style={{ marginTop: 8 }}
            >
              <CommentOutlined /> Create Test Comment
            </Button>
          </div>

          <div>
            <Text strong>Test Operations:</Text>
            <br />
            <Button 
              onClick={testOperation}
              disabled={!isConnected}
              style={{ marginTop: 8 }}
            >
              Send Test Operation
            </Button>
          </div>

          <div>
            <Text strong>Test Presence:</Text>
            <br />
            <Button 
              onClick={testPresence}
              disabled={!isConnected}
              style={{ marginTop: 8 }}
            >
              Update Cursor Position
            </Button>
          </div>
        </Space>
      </Card>

      {/* Live Collaboration */}
      <Card title="Live Collaboration Test">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Active Participants ({participants.length}):</Text>
            <div style={{ marginTop: 8 }}>
              {participants.map(participant => (
                <Tag key={participant.user_id} color="blue">
                  {participant.username}
                </Tag>
              ))}
            </div>
          </div>

          <Divider />

          <div>
            <Text strong>Recent Comments ({comments.length}):</Text>
            <List
              size="small"
              dataSource={comments.slice(0, 5)}
              renderItem={(comment) => (
                <List.Item>
                  <Text strong>{comment.author?.username || 'Unknown'}:</Text> {comment.content}
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    {new Date(comment.created_at).toLocaleTimeString()}
                  </Text>
                </List.Item>
              )}
              style={{ marginTop: 8 }}
            />
          </div>

          <Divider />

          <div>
            <Text strong>Send Message to All Collaborators:</Text>
            <Space.Compact style={{ width: '100%', marginTop: 8 }}>
              <TextArea
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="Type a message to send to all collaborators..."
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
              <Button 
                type="primary"
                icon={<SendOutlined />}
                onClick={sendTestMessage}
                disabled={!isConnected || !testMessage.trim()}
              >
                Send
              </Button>
            </Space.Compact>
          </div>
        </Space>
      </Card>

      {/* Instructions */}
      <Alert
        message="Multi-User Testing Instructions"
        description={
          <div>
            <p>To test collaboration features with multiple users:</p>
            <ol>
              <li>Open this page in multiple browser tabs or share with team members</li>
              <li>Run the automated tests to verify basic functionality</li>
              <li>Use the manual test buttons to trigger specific features</li>
              <li>Send messages and watch them appear in real-time across all sessions</li>
              <li>Check the browser console for detailed WebSocket logs</li>
            </ol>
          </div>
        }
        type="info"
        style={{ marginTop: 24 }}
      />
    </div>
  );
};

// Wrapper with collaboration provider
export const CollaborationTest = () => {
  return (
    <CollaborationProvider>
      <CollaborationTestInner />
    </CollaborationProvider>
  );
};

export default CollaborationTest;
