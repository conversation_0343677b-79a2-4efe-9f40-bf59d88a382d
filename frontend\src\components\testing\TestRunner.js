import React, { useState, useEffect } from 'react';

/**
 * Test Runner Component
 * 
 * This component runs tests and displays the results.
 */
const TestRunner = ({ onComplete }) => {
  const [results, setResults] = useState(null);
  const [running, setRunning] = useState(false);
  const [error, setError] = useState(null);
  const [selectedCategories, setSelectedCategories] = useState(['app_loading', 'services', 'api', 'websocket']);
  const [options, setOptions] = useState({
    runParallel: false,
    stopOnFail: false,
    retries: 1
  });

  // Run tests
  const runTests = async () => {
    try {
      setRunning(true);
      setError(null);
      
      // Simulate running tests
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate test results
      const testResults = {
        totalTests: 10,
        passedTests: 8,
        failedTests: 2,
        skippedTests: 0,
        duration: 2000,
        categories: {
          app_loading: { total: 2, passed: 2, failed: 0, skipped: 0 },
          services: { total: 2, passed: 1, failed: 1, skipped: 0 },
          api: { total: 3, passed: 2, failed: 1, skipped: 0 },
          websocket: { total: 3, passed: 3, failed: 0, skipped: 0 }
        },
        tests: [
          {
            name: 'App Loading Test',
            category: 'app_loading',
            status: 'passed',
            duration: 500,
            result: { success: true }
          },
          {
            name: 'Service Discovery Test',
            category: 'services',
            status: 'passed',
            duration: 300,
            result: { success: true }
          },
          {
            name: 'Connection Manager Test',
            category: 'services',
            status: 'failed',
            duration: 200,
            error: { message: 'Failed to establish connections' }
          },
          {
            name: 'API Client Initialization Test',
            category: 'api',
            status: 'passed',
            duration: 100,
            result: { success: true }
          },
          {
            name: 'API Status Test',
            category: 'api',
            status: 'passed',
            duration: 150,
            result: { success: true }
          },
          {
            name: 'API Authentication Test',
            category: 'api',
            status: 'failed',
            duration: 200,
            error: { message: 'Authentication failed' }
          },
          {
            name: 'WebSocket Client Initialization Test',
            category: 'websocket',
            status: 'passed',
            duration: 100,
            result: { success: true }
          },
          {
            name: 'WebSocket Connection Test',
            category: 'websocket',
            status: 'passed',
            duration: 300,
            result: { success: true }
          },
          {
            name: 'WebSocket Message Test',
            category: 'websocket',
            status: 'passed',
            duration: 250,
            result: { success: true }
          },
          {
            name: 'App Loading Performance Test',
            category: 'app_loading',
            status: 'passed',
            duration: 150,
            result: { success: true }
          }
        ]
      };
      
      setResults(testResults);
      
      // Call onComplete callback if provided
      if (onComplete) {
        onComplete(testResults);
      }
    } catch (error) {
      console.error('Error running tests:', error);
      setError(error.message);
    } finally {
      setRunning(false);
    }
  };
  
  // Toggle category selection
  const toggleCategory = (category) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter(c => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };
  
  // Update options
  const updateOption = (name, value) => {
    setOptions({
      ...options,
      [name]: value
    });
  };
  
  // Format duration
  const formatDuration = (duration) => {
    if (duration < 1000) {
      return `${duration}ms`;
    }
    
    return `${(duration / 1000).toFixed(2)}s`;
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'passed':
        return '#2e7d32'; // Green
      case 'failed':
        return '#c62828'; // Red
      case 'running':
        return '#1976d2'; // Blue
      case 'skipped':
        return '#f57c00'; // Orange
      default:
        return '#757575'; // Gray
    }
  };
  
  return (
    <div className="test-runner">
      <h3>Test Runner</h3>
      
      <div className="test-options">
        <div className="categories">
          <h4>Test Categories</h4>
          <div className="category-list">
            {['app_loading', 'services', 'api', 'websocket', 'components', 'performance'].map(category => (
              <label key={category} className="category-item">
                <input
                  type="checkbox"
                  checked={selectedCategories.includes(category)}
                  onChange={() => toggleCategory(category)}
                  disabled={running}
                />
                {category.replace('_', ' ')}
              </label>
            ))}
          </div>
        </div>
        
        <div className="run-options">
          <h4>Run Options</h4>
          <label className="option-item">
            <input
              type="checkbox"
              checked={options.runParallel}
              onChange={(e) => updateOption('runParallel', e.target.checked)}
              disabled={running}
            />
            Run in parallel
          </label>
          <label className="option-item">
            <input
              type="checkbox"
              checked={options.stopOnFail}
              onChange={(e) => updateOption('stopOnFail', e.target.checked)}
              disabled={running}
            />
            Stop on fail
          </label>
          <div className="option-item">
            <label>
              Retries:
              <select
                value={options.retries}
                onChange={(e) => updateOption('retries', parseInt(e.target.value))}
                disabled={running}
              >
                <option value="0">0</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
              </select>
            </label>
          </div>
        </div>
      </div>
      
      <div className="test-actions">
        <button
          className="run-button"
          onClick={runTests}
          disabled={running || selectedCategories.length === 0}
        >
          {running ? 'Running Tests...' : 'Run Tests'}
        </button>
      </div>
      
      {error && (
        <div className="test-error">
          <h4>Error</h4>
          <div className="error-message">{error}</div>
        </div>
      )}
      
      {results && (
        <div className="test-results">
          <h4>Test Results</h4>
          
          <div className="results-summary">
            <div className="summary-item">
              <span className="summary-label">Total:</span>
              <span className="summary-value">{results.totalTests}</span>
            </div>
            <div className="summary-item passed">
              <span className="summary-label">Passed:</span>
              <span className="summary-value">{results.passedTests}</span>
            </div>
            <div className="summary-item failed">
              <span className="summary-label">Failed:</span>
              <span className="summary-value">{results.failedTests}</span>
            </div>
            <div className="summary-item skipped">
              <span className="summary-label">Skipped:</span>
              <span className="summary-value">{results.skippedTests}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Duration:</span>
              <span className="summary-value">{formatDuration(results.duration)}</span>
            </div>
          </div>
          
          <div className="test-details">
            <h5>Test Details</h5>
            <div className="test-list">
              {results.tests.map((test, index) => (
                <div key={index} className="test-item">
                  <div className="test-header">
                    <div 
                      className="test-status"
                      style={{ backgroundColor: getStatusColor(test.status) }}
                    ></div>
                    <div className="test-name">{test.name}</div>
                    <div className="test-category">{test.category.replace('_', ' ')}</div>
                    <div className="test-duration">{formatDuration(test.duration)}</div>
                  </div>
                  {test.status === 'failed' && test.error && (
                    <div className="test-error-details">
                      <div className="error-message">{test.error.message}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
      
      <style jsx>{`
        .test-runner {
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 4px;
          margin: 20px 0;
          background-color: #f9f9f9;
        }
        
        .test-options {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;
        }
        
        .categories, .run-options {
          flex: 1;
        }
        
        .category-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }
        
        .category-item, .option-item {
          display: flex;
          align-items: center;
          gap: 5px;
        }
        
        .test-actions {
          margin-bottom: 20px;
        }
        
        .run-button {
          padding: 10px 20px;
          background-color: #4CAF50;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        
        .run-button:hover {
          background-color: #45a049;
        }
        
        .run-button:disabled {
          background-color: #cccccc;
          cursor: not-allowed;
        }
        
        .test-error {
          margin-bottom: 20px;
          padding: 10px;
          background-color: #ffebee;
          border: 1px solid #ffcdd2;
          border-radius: 4px;
        }
        
        .error-message {
          color: #c62828;
        }
        
        .results-summary {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;
          padding: 10px;
          background-color: #f0f0f0;
          border-radius: 4px;
        }
        
        .summary-item {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        
        .summary-label {
          font-size: 14px;
          color: #666;
        }
        
        .summary-value {
          font-size: 18px;
          font-weight: bold;
        }
        
        .passed .summary-value {
          color: #2e7d32;
        }
        
        .failed .summary-value {
          color: #c62828;
        }
        
        .skipped .summary-value {
          color: #f57c00;
        }
        
        .test-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }
        
        .test-item {
          padding: 10px;
          background-color: #f5f5f5;
          border-radius: 4px;
        }
        
        .test-header {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        
        .test-status {
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
        
        .test-name {
          font-weight: bold;
          flex: 1;
        }
        
        .test-category {
          color: #666;
          font-size: 14px;
        }
        
        .test-duration {
          color: #666;
          font-size: 14px;
        }
        
        .test-error-details {
          margin-top: 10px;
          padding: 10px;
          background-color: #ffebee;
          border: 1px solid #ffcdd2;
          border-radius: 4px;
        }
      `}</style>
    </div>
  );
};

export default TestRunner;
