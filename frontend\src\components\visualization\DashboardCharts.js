import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Spin, 
  Empty, 
  Tabs, 
  Select, 
  Button, 
  Space, 
  Tooltip, 
  Typography,
  Radio,
  Divider
} from 'antd';
import { 
  ReloadOutlined, 
  DownloadOutlined, 
  LineChartOutlined, 
  Bar<PERSON><PERSON>Outlined, 
  Pie<PERSON><PERSON>Outlined, 
  <PERSON><PERSON><PERSON>Outlined,
  TableOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  AreaChart, 
  Area,
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  Brush,
  ReferenceLine
} from 'recharts';
import styled from 'styled-components';
import usePreferences from '../../hooks/usePreferences';
import { downloadChartAsImage, exportChartData } from '../../utils/chartExport';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// Styled components
const ChartContainer = styled.div`
  height: 400px;
  margin-bottom: 24px;
`;

const ChartControls = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const StatsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
`;

const StatCard = styled.div`
  background-color: ${props => props.theme === 'dark' ? '#1f2937' : '#f9fafb'};
  border-radius: 8px;
  padding: 16px;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

// Color schemes
const COLOR_SCHEMES = {
  default: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#fa8c16', '#eb2f96'],
  pastel: ['#74b9ff', '#55efc4', '#ffeaa7', '#fab1a0', '#a29bfe', '#81ecec', '#ffeaa7', '#fd79a8'],
  monochrome: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff', '#e6f7ff', '#f0f9ff', '#e6f7ff'],
  contrast: ['#0050b3', '#52c41a', '#d4b106', '#cf1322', '#531dab', '#006d75', '#ad4e00', '#c41d7f']
};

/**
 * DashboardCharts component
 * Displays various chart types for data visualization
 */
const DashboardCharts = ({ 
  data = [], 
  loading = false,
  title = 'Data Visualization',
  onRefresh = () => {},
  chartTypes = ['line', 'bar', 'pie', 'area', 'scatter']
}) => {
  // State
  const [activeTab, setActiveTab] = useState('line');
  const [timeRange, setTimeRange] = useState('all');
  const [colorScheme, setColorScheme] = useState('default');
  const [showBrush, setShowBrush] = useState(false);
  const [showReferenceLine, setShowReferenceLine] = useState(false);
  const [referenceValue, setReferenceValue] = useState(50);
  const [filteredData, setFilteredData] = useState(data);
  
  // Get theme preferences
  const { theme } = usePreferences();
  const isDarkTheme = theme === 'dark';
  
  // Colors based on theme
  const colors = {
    text: isDarkTheme ? '#f9fafb' : '#1f2937',
    grid: isDarkTheme ? '#374151' : '#e5e7eb',
    background: isDarkTheme ? '#111827' : '#ffffff',
    tooltip: isDarkTheme ? '#1f2937' : '#ffffff',
    tooltipText: isDarkTheme ? '#f9fafb' : '#1f2937',
    ...COLOR_SCHEMES[colorScheme]
  };
  
  // Update filtered data when data or time range changes
  useEffect(() => {
    if (!data || data.length === 0) {
      setFilteredData([]);
      return;
    }
    
    // Filter data based on time range
    let filtered = [...data];
    
    if (timeRange !== 'all') {
      const now = Date.now();
      const ranges = {
        '15m': 15 * 60 * 1000,
        '1h': 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };
      
      filtered = data.filter(item => {
        const timestamp = new Date(item.timestamp).getTime();
        return now - timestamp <= ranges[timeRange];
      });
    }
    
    setFilteredData(filtered);
  }, [data, timeRange]);
  
  // Handle time range change
  const handleTimeRangeChange = (e) => {
    setTimeRange(e.target.value);
  };
  
  // Handle color scheme change
  const handleColorSchemeChange = (value) => {
    setColorScheme(value);
  };
  
  // Format X axis ticks
  const formatXAxis = (value) => {
    const date = new Date(value);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null;
    
    return (
      <div style={{ 
        backgroundColor: colors.tooltip, 
        padding: '10px', 
        border: `1px solid ${colors.grid}`,
        borderRadius: '4px'
      }}>
        <p style={{ color: colors.tooltipText, margin: 0 }}>
          {new Date(label).toLocaleString()}
        </p>
        {payload.map((entry, index) => (
          <p key={index} style={{ color: entry.color, margin: '5px 0 0' }}>
            {`${entry.name}: ${entry.value}`}
          </p>
        ))}
      </div>
    );
  };
  
  // Calculate statistics
  const calculateStats = (dataKey) => {
    if (!filteredData || filteredData.length === 0) return { min: 0, max: 0, avg: 0 };
    
    const values = filteredData.map(item => item[dataKey]);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    
    return { min, max, avg: avg.toFixed(2) };
  };
  
  // Handle chart download
  const handleDownloadChart = (chartId, format = 'png') => {
    downloadChartAsImage(chartId, `${title.toLowerCase().replace(/\s+/g, '-')}-${activeTab}-chart`, format);
  };
  
  // Handle data export
  const handleExportData = (format = 'csv') => {
    exportChartData(filteredData, `${title.toLowerCase().replace(/\s+/g, '-')}-data`, format);
  };
  
  // Render line chart
  const renderLineChart = () => {
    const stats = calculateStats('value');
    
    return (
      <>
        <StatsContainer>
          <StatCard theme={theme}>
            <Text type="secondary">Min</Text>
            <Title level={4}>{stats.min}</Title>
          </StatCard>
          <StatCard theme={theme}>
            <Text type="secondary">Max</Text>
            <Title level={4}>{stats.max}</Title>
          </StatCard>
          <StatCard theme={theme}>
            <Text type="secondary">Average</Text>
            <Title level={4}>{stats.avg}</Title>
          </StatCard>
        </StatsContainer>
        
        <ChartContainer>
          {loading ? (
            <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Spin size="large" />
            </div>
          ) : filteredData.length === 0 ? (
            <Empty description="No data available" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                id="line-chart"
                data={filteredData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={formatXAxis} 
                  stroke={colors.text}
                />
                <YAxis stroke={colors.text} />
                <RechartsTooltip content={<CustomTooltip />} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="value"
                  name="Value"
                  stroke={colors[0]}
                  activeDot={{ r: 8 }}
                />
                {showReferenceLine && (
                  <ReferenceLine 
                    y={referenceValue} 
                    label="Threshold" 
                    stroke={colors[3]} 
                    strokeDasharray="3 3" 
                  />
                )}
                {showBrush && (
                  <Brush 
                    dataKey="timestamp" 
                    height={30} 
                    stroke={colors[0]} 
                    fill={isDarkTheme ? '#374151' : '#f9fafb'}
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          )}
        </ChartContainer>
      </>
    );
  };
  
  // Render bar chart
  const renderBarChart = () => {
    return (
      <ChartContainer>
        {loading ? (
          <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Spin size="large" />
          </div>
        ) : filteredData.length === 0 ? (
          <Empty description="No data available" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              id="bar-chart"
              data={filteredData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxis} 
                stroke={colors.text}
              />
              <YAxis stroke={colors.text} />
              <RechartsTooltip content={<CustomTooltip />} />
              <Legend />
              <Bar dataKey="value" name="Value" fill={colors[0]} />
              {showReferenceLine && (
                <ReferenceLine 
                  y={referenceValue} 
                  label="Threshold" 
                  stroke={colors[3]} 
                  strokeDasharray="3 3" 
                />
              )}
            </BarChart>
          </ResponsiveContainer>
        )}
      </ChartContainer>
    );
  };
  
  // Render pie chart
  const renderPieChart = () => {
    // Group data for pie chart
    const pieData = filteredData.reduce((acc, item) => {
      const category = item.category || 'Uncategorized';
      const existingCategory = acc.find(i => i.name === category);
      
      if (existingCategory) {
        existingCategory.value += item.value;
      } else {
        acc.push({ name: category, value: item.value });
      }
      
      return acc;
    }, []);
    
    return (
      <ChartContainer>
        {loading ? (
          <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Spin size="large" />
          </div>
        ) : filteredData.length === 0 ? (
          <Empty description="No data available" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart id="pie-chart">
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={150}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <RechartsTooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )}
      </ChartContainer>
    );
  };
  
  // Render area chart
  const renderAreaChart = () => {
    return (
      <ChartContainer>
        {loading ? (
          <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Spin size="large" />
          </div>
        ) : filteredData.length === 0 ? (
          <Empty description="No data available" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              id="area-chart"
              data={filteredData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxis} 
                stroke={colors.text}
              />
              <YAxis stroke={colors.text} />
              <RechartsTooltip content={<CustomTooltip />} />
              <Legend />
              <Area
                type="monotone"
                dataKey="value"
                name="Value"
                stroke={colors[0]}
                fill={colors[0]}
                fillOpacity={0.3}
              />
              {showReferenceLine && (
                <ReferenceLine 
                  y={referenceValue} 
                  label="Threshold" 
                  stroke={colors[3]} 
                  strokeDasharray="3 3" 
                />
              )}
              {showBrush && (
                <Brush 
                  dataKey="timestamp" 
                  height={30} 
                  stroke={colors[0]} 
                  fill={isDarkTheme ? '#374151' : '#f9fafb'}
                />
              )}
            </AreaChart>
          </ResponsiveContainer>
        )}
      </ChartContainer>
    );
  };
  
  // Render scatter chart
  const renderScatterChart = () => {
    return (
      <ChartContainer>
        {loading ? (
          <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Spin size="large" />
          </div>
        ) : filteredData.length === 0 ? (
          <Empty description="No data available" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart
              id="scatter-chart"
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
              <XAxis 
                type="number" 
                dataKey="x" 
                name="X Value" 
                stroke={colors.text}
              />
              <YAxis 
                type="number" 
                dataKey="value" 
                name="Y Value" 
                stroke={colors.text}
              />
              <RechartsTooltip cursor={{ strokeDasharray: '3 3' }} />
              <Legend />
              <Scatter 
                name="Values" 
                data={filteredData.map((item, index) => ({ ...item, x: index }))} 
                fill={colors[0]} 
              />
              {showReferenceLine && (
                <ReferenceLine 
                  y={referenceValue} 
                  label="Threshold" 
                  stroke={colors[3]} 
                  strokeDasharray="3 3" 
                />
              )}
            </ScatterChart>
          </ResponsiveContainer>
        )}
      </ChartContainer>
    );
  };
  
  return (
    <Card title={title}>
      <ChartControls>
        <Radio.Group value={timeRange} onChange={handleTimeRangeChange}>
          <Radio.Button value="15m">15m</Radio.Button>
          <Radio.Button value="1h">1h</Radio.Button>
          <Radio.Button value="6h">6h</Radio.Button>
          <Radio.Button value="24h">24h</Radio.Button>
          <Radio.Button value="7d">7d</Radio.Button>
          <Radio.Button value="30d">30d</Radio.Button>
          <Radio.Button value="all">All</Radio.Button>
        </Radio.Group>
        
        <Space>
          <Select
            value={colorScheme}
            onChange={handleColorSchemeChange}
            style={{ width: 120 }}
          >
            <Option value="default">Default</Option>
            <Option value="pastel">Pastel</Option>
            <Option value="monochrome">Monochrome</Option>
            <Option value="contrast">High Contrast</Option>
          </Select>
          
          <Tooltip title="Refresh Data">
            <Button 
              icon={<ReloadOutlined />} 
              onClick={onRefresh}
              loading={loading}
            />
          </Tooltip>
          
          <Tooltip title="Download Chart">
            <Button 
              icon={<DownloadOutlined />} 
              onClick={() => handleDownloadChart(`${activeTab}-chart`)}
              disabled={loading || filteredData.length === 0}
            />
          </Tooltip>
        </Space>
      </ChartControls>
      
      <Space style={{ marginBottom: 16 }}>
        <Radio.Group value={showBrush} onChange={e => setShowBrush(e.target.value)}>
          <Radio.Button value={true}>Show Brush</Radio.Button>
          <Radio.Button value={false}>Hide Brush</Radio.Button>
        </Radio.Group>
        
        <Radio.Group value={showReferenceLine} onChange={e => setShowReferenceLine(e.target.value)}>
          <Radio.Button value={true}>Show Reference Line</Radio.Button>
          <Radio.Button value={false}>Hide Reference Line</Radio.Button>
        </Radio.Group>
      </Space>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {chartTypes.includes('line') && (
          <TabPane 
            tab={<span><LineChartOutlined /> Line Chart</span>} 
            key="line"
          >
            {renderLineChart()}
          </TabPane>
        )}
        
        {chartTypes.includes('bar') && (
          <TabPane 
            tab={<span><BarChartOutlined /> Bar Chart</span>} 
            key="bar"
          >
            {renderBarChart()}
          </TabPane>
        )}
        
        {chartTypes.includes('pie') && (
          <TabPane 
            tab={<span><PieChartOutlined /> Pie Chart</span>} 
            key="pie"
          >
            {renderPieChart()}
          </TabPane>
        )}
        
        {chartTypes.includes('area') && (
          <TabPane 
            tab={<span><AreaChartOutlined /> Area Chart</span>} 
            key="area"
          >
            {renderAreaChart()}
          </TabPane>
        )}
        
        {chartTypes.includes('scatter') && (
          <TabPane 
            tab={<span><TableOutlined /> Scatter Chart</span>} 
            key="scatter"
          >
            {renderScatterChart()}
          </TabPane>
        )}
      </Tabs>
      
      <Divider />
      
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Text type="secondary">
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          {filteredData.length} data points
        </Text>
        
        <Space>
          <Button 
            onClick={() => handleExportData('csv')}
            disabled={loading || filteredData.length === 0}
          >
            Export CSV
          </Button>
          <Button 
            onClick={() => handleExportData('json')}
            disabled={loading || filteredData.length === 0}
          >
            Export JSON
          </Button>
        </Space>
      </div>
    </Card>
  );
};

export default DashboardCharts;
