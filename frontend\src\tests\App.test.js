import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import App from '../App';

// Mock the components to avoid rendering them
jest.mock('../Routes', () => () => <div data-testid="routes">Routes Component</div>);
jest.mock('../components/NetworkStatusIndicator', () => ({ showOfflineOnly }) => (
  <div data-testid="network-status">Network Status Indicator (showOfflineOnly: {showOfflineOnly.toString()})</div>
));
jest.mock('../components/common/LoadingSpinner', () => ({ tip, fullScreen }) => (
  <div data-testid="loading-spinner">Loading Spinner (tip: {tip}, fullScreen: {fullScreen.toString()})</div>
));
jest.mock('../components/common/EnhancedErrorBoundary', () => ({ children, onError }) => (
  <div data-testid="error-boundary" onClick={() => onError(new Error('Test error'), { componentStack: 'Test stack' })}>
    {children}
  </div>
));
jest.mock('../components/debug/DebugInfoPanel', () => (props) => (
  <div data-testid="debug-info-panel">Debug Info Panel</div>
));

// Mock the hooks and utilities
jest.mock('../hooks/useWebSocket', () => () => ({
  connected: true,
  connecting: false,
  messages: [],
  sendMessage: jest.fn(),
  lastMessage: null,
  readyState: 1,
}));

jest.mock('../components/theme/ThemeProviderRedux', () => ({
  useTheme: () => ({
    currentTheme: { primaryColor: '#1890ff' },
    themeMode: 'light',
  }),
}));

jest.mock('../utils/apiEndpointChecker', () => ({
  runApiEndpointCheck: jest.fn().mockResolvedValue({
    anyAvailable: true,
    available: [{ endpoint: '/api/test', status: 200 }],
    unavailable: [],
  }),
}));

jest.mock('../utils/backendStatusChecker', () => ({
  checkBackendStatus: jest.fn().mockResolvedValue({ available: true }),
  checkWebSocketStatus: jest.fn().mockResolvedValue({ available: true }),
}));

jest.mock('../config/env', () => ({
  getWebSocketUrl: jest.fn().mockReturnValue('ws://localhost:8000/ws/app_builder/'),
}));

// Mock the Redux actions
jest.mock('../redux/actions/appActions', () => ({
  initializeWebSocket: jest.fn().mockImplementation((config) => {
    if (config.onSuccess) {
      setTimeout(() => config.onSuccess(), 0);
    }
    return { type: 'INITIALIZE_WEBSOCKET', payload: config };
  }),
  fetchAppData: jest.fn().mockImplementation(() => {
    return { type: 'FETCH_APP_DATA' };
  }),
}));

// Create a mock store
const middlewares = [thunk];
const mockStore = configureStore(middlewares);

describe('App Component', () => {
  let store;
  let originalEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = process.env;
    
    // Mock environment variables
    process.env = {
      ...process.env,
      NODE_ENV: 'development',
      REACT_APP_WS_URL: 'ws://localhost:8000/ws/app_builder/',
      REACT_APP_DEBUG: 'true',
    };

    // Initialize the store with a known state
    store = mockStore({
      websocket: {
        status: 'connected',
        messages: [],
      },
      app: {
        data: {},
        loading: false,
        error: null,
      },
      ui: {
        theme: 'light',
      },
    });

    // Mock fetch
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ status: 'ok' }),
      })
    );

    // Mock console methods
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  test('renders loading spinner initially', async () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    // Check if loading spinner is rendered
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByText('Initializing application...')).toBeInTheDocument();

    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.queryByText('Initializing application...')).not.toBeInTheDocument();
    }, { timeout: 3000 });
  });

  test('renders main app after initialization', async () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.queryByText('Initializing application...')).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Check if main app components are rendered
    expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    expect(screen.getByTestId('network-status')).toBeInTheDocument();
    expect(screen.getByTestId('routes')).toBeInTheDocument();
  });

  test('renders debug info panel in development mode', async () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.queryByText('Initializing application...')).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Check if debug info panel is rendered
    expect(screen.getByTestId('debug-info-panel')).toBeInTheDocument();
  });

  test('does not render debug info panel in production mode', async () => {
    // Set NODE_ENV to production
    process.env.NODE_ENV = 'production';

    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.queryByText('Initializing application...')).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Check if debug info panel is not rendered
    expect(screen.queryByTestId('debug-info-panel')).not.toBeInTheDocument();
  });

  test('handles errors correctly', async () => {
    render(
      <Provider store={store}>
        <App />
      </Provider>
    );

    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.queryByText('Initializing application...')).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Trigger error
    act(() => {
      screen.getByTestId('error-boundary').click();
    });

    // Check if error was logged
    expect(console.error).toHaveBeenCalledWith('App Error:', expect.any(Error), expect.any(Object));
  });
});
