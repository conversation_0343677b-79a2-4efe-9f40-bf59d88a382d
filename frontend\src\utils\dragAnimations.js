/**
 * Animation utilities for drag and drop operations
 */

// CSS-in-JS animation styles
export const dragAnimationStyles = {
  // Smooth drag transition
  dragTransition: {
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  },

  // Drag start animation
  dragStart: {
    transform: 'scale(1.05) rotate(2deg)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.2)',
    zIndex: 1000,
    opacity: 0.9,
  },

  // Drag hover animation
  dragHover: {
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)',
    borderColor: '#1890ff',
  },

  // Drop zone active animation
  dropZoneActive: {
    backgroundColor: 'rgba(82, 196, 26, 0.1)',
    borderColor: '#52c41a',
    borderStyle: 'dashed',
    borderWidth: '2px',
    transform: 'scale(1.02)',
  },

  // Drop zone hover animation
  dropZoneHover: {
    backgroundColor: 'rgba(24, 144, 255, 0.05)',
    borderColor: '#1890ff',
    borderStyle: 'dashed',
    borderWidth: '2px',
  },

  // Invalid drop zone animation
  dropZoneInvalid: {
    backgroundColor: 'rgba(255, 77, 79, 0.1)',
    borderColor: '#ff4d4f',
    borderStyle: 'dashed',
    borderWidth: '2px',
  },

  // Ghost element animation
  ghostElement: {
    opacity: 0.6,
    transform: 'rotate(5deg) scale(0.9)',
    pointerEvents: 'none',
    zIndex: 9999,
    filter: 'blur(1px)',
  },

  // Snap animation
  snapAnimation: {
    transition: 'transform 0.2s ease-out',
  },

  // Pulse animation for drop indicators
  pulseAnimation: {
    animation: 'pulse 1.5s ease-in-out infinite',
  },

  // Shake animation for invalid drops
  shakeAnimation: {
    animation: 'shake 0.5s ease-in-out',
  },

  // Bounce animation for successful drops
  bounceAnimation: {
    animation: 'bounce 0.6s ease-out',
  },

  // Fade in animation
  fadeIn: {
    animation: 'fadeIn 0.3s ease-out',
  },

  // Slide in animation
  slideIn: {
    animation: 'slideIn 0.4s ease-out',
  },
};

// CSS keyframes as strings (to be injected into styled-components or CSS)
export const dragKeyframes = `
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      transform: translate3d(0, -8px, 0);
    }
    70% {
      transform: translate3d(0, -4px, 0);
    }
    90% {
      transform: translate3d(0, -2px, 0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes dragGlow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);
    }
  }

  @keyframes dropIndicator {
    0%, 100% {
      opacity: 0.5;
      transform: scaleY(1);
    }
    50% {
      opacity: 1;
      transform: scaleY(1.2);
    }
  }
`;

/**
 * Apply drag start animation to an element
 * @param {HTMLElement} element - Element to animate
 * @param {Object} options - Animation options
 */
export const applyDragStartAnimation = (element, options = {}) => {
  if (!element) return;

  const {
    scale = 1.05,
    rotation = 2,
    duration = 300,
    easing = 'cubic-bezier(0.4, 0, 0.2, 1)'
  } = options;

  element.style.transition = `all ${duration}ms ${easing}`;
  element.style.transform = `scale(${scale}) rotate(${rotation}deg)`;
  element.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
  element.style.zIndex = '1000';
  element.style.opacity = '0.9';
};

/**
 * Apply drag end animation to an element
 * @param {HTMLElement} element - Element to animate
 * @param {Object} options - Animation options
 */
export const applyDragEndAnimation = (element, options = {}) => {
  if (!element) return;

  const {
    duration = 300,
    easing = 'cubic-bezier(0.4, 0, 0.2, 1)'
  } = options;

  element.style.transition = `all ${duration}ms ${easing}`;
  element.style.transform = '';
  element.style.boxShadow = '';
  element.style.zIndex = '';
  element.style.opacity = '';
};

/**
 * Apply drop zone active animation
 * @param {HTMLElement} element - Element to animate
 * @param {boolean} isValid - Whether the drop is valid
 */
export const applyDropZoneAnimation = (element, isValid = true) => {
  if (!element) return;

  if (isValid) {
    element.style.backgroundColor = 'rgba(82, 196, 26, 0.1)';
    element.style.borderColor = '#52c41a';
    element.style.borderStyle = 'dashed';
    element.style.borderWidth = '2px';
    element.style.transform = 'scale(1.02)';
  } else {
    element.style.backgroundColor = 'rgba(255, 77, 79, 0.1)';
    element.style.borderColor = '#ff4d4f';
    element.style.borderStyle = 'dashed';
    element.style.borderWidth = '2px';
    element.style.transform = 'scale(1.02)';
  }
  
  element.style.transition = 'all 0.3s ease';
};

/**
 * Clear drop zone animation
 * @param {HTMLElement} element - Element to clear
 */
export const clearDropZoneAnimation = (element) => {
  if (!element) return;

  element.style.backgroundColor = '';
  element.style.borderColor = '';
  element.style.borderStyle = '';
  element.style.borderWidth = '';
  element.style.transform = '';
  element.style.transition = '';
};

/**
 * Apply snap animation to an element
 * @param {HTMLElement} element - Element to animate
 * @param {Object} position - Target position {x, y}
 */
export const applySnapAnimation = (element, position) => {
  if (!element || !position) return;

  element.style.transition = 'transform 0.2s ease-out';
  element.style.transform = `translate(${position.x}px, ${position.y}px)`;
};

/**
 * Apply shake animation for invalid drops
 * @param {HTMLElement} element - Element to animate
 */
export const applyShakeAnimation = (element) => {
  if (!element) return;

  element.style.animation = 'shake 0.5s ease-in-out';
  
  // Clear animation after completion
  setTimeout(() => {
    element.style.animation = '';
  }, 500);
};

/**
 * Apply bounce animation for successful drops
 * @param {HTMLElement} element - Element to animate
 */
export const applyBounceAnimation = (element) => {
  if (!element) return;

  element.style.animation = 'bounce 0.6s ease-out';
  
  // Clear animation after completion
  setTimeout(() => {
    element.style.animation = '';
  }, 600);
};

/**
 * Create a ghost element for drag preview
 * @param {HTMLElement} sourceElement - Source element to clone
 * @param {Object} options - Ghost options
 * @returns {HTMLElement} Ghost element
 */
export const createGhostElement = (sourceElement, options = {}) => {
  if (!sourceElement) return null;

  const {
    opacity = 0.6,
    rotation = 5,
    scale = 0.9,
    blur = 1
  } = options;

  const ghost = sourceElement.cloneNode(true);
  
  ghost.style.position = 'absolute';
  ghost.style.top = '-1000px';
  ghost.style.left = '-1000px';
  ghost.style.opacity = opacity;
  ghost.style.transform = `rotate(${rotation}deg) scale(${scale})`;
  ghost.style.pointerEvents = 'none';
  ghost.style.zIndex = '9999';
  ghost.style.filter = `blur(${blur}px)`;
  ghost.style.transition = 'none';
  
  return ghost;
};

/**
 * Animate element entrance
 * @param {HTMLElement} element - Element to animate
 * @param {string} animationType - Type of animation ('fadeIn', 'slideIn', etc.)
 */
export const animateElementEntrance = (element, animationType = 'fadeIn') => {
  if (!element) return;

  element.style.animation = `${animationType} 0.3s ease-out`;
  
  // Clear animation after completion
  setTimeout(() => {
    element.style.animation = '';
  }, 300);
};

/**
 * Animate element exit
 * @param {HTMLElement} element - Element to animate
 * @param {Function} callback - Callback after animation completes
 */
export const animateElementExit = (element, callback) => {
  if (!element) return;

  element.style.transition = 'all 0.3s ease-out';
  element.style.opacity = '0';
  element.style.transform = 'scale(0.8) translateY(10px)';
  
  setTimeout(() => {
    if (callback) callback();
  }, 300);
};

/**
 * Inject keyframes into document head
 */
export const injectDragKeyframes = () => {
  const styleId = 'drag-drop-keyframes';
  
  // Check if already injected
  if (document.getElementById(styleId)) return;
  
  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = dragKeyframes;
  document.head.appendChild(style);
};

// Auto-inject keyframes when module is imported
if (typeof document !== 'undefined') {
  injectDragKeyframes();
}
