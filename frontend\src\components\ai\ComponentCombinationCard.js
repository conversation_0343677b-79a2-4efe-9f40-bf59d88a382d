import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Card, Button, Badge, Tooltip, Modal, Typography, Space, Tag, Avatar, Spin } from 'antd';
import {
  AppstoreOutlined,
  PlusOutlined,
  CheckOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined,
  EyeOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

/**
 * Component Combination Card
 * Displays individual component combination suggestions
 */
const ComponentCombinationCard = ({
  suggestion,
  onApply,
  onPreview,
  applied = false,
  showPreview = true,
  showScore = true,
  compact = false
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [applying, setApplying] = useState(false);

  // Handle apply button click
  const handleApply = async () => {
    if (applied || applying) return;

    setApplying(true);
    try {
      if (onApply) {
        await onApply(suggestion);
      }
    } catch (error) {
      console.error('Error applying component combination:', error);
    } finally {
      setApplying(false);
    }
  };

  // Handle preview button click
  const handlePreview = () => {
    if (onPreview) {
      onPreview(suggestion);
    } else {
      setPreviewVisible(true);
    }
  };

  // Get score color based on value
  const getScoreColor = (score) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#1890ff';
    if (score >= 40) return '#faad14';
    return '#ff4d4f';
  };

  // Get component icon
  const getComponentIcon = (componentType) => {
    const iconMap = {
      button: '🔘',
      form: '📝',
      input: '📝',
      text: '📄',
      image: '🖼️',
      card: '🃏',
      header: '📋',
      nav: '🧭',
      list: '📋',
      divider: '➖',
      section: '📦',
      modal: '🪟',
      table: '📊',
      chart: '📈'
    };
    return iconMap[componentType] || '🔧';
  };

  // Render component combination preview
  const renderCombinationPreview = () => {
    const { components = [], missing_components = [] } = suggestion;
    const allComponents = [...components];

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px',
        background: '#fafafa',
        borderRadius: '6px',
        border: '1px solid #d9d9d9',
        minHeight: '80px'
      }}>
        <Space size="small" wrap>
          {allComponents.map((component, index) => (
            <div key={index} style={{ textAlign: 'center' }}>
              <Avatar
                size="small"
                style={{
                  backgroundColor: missing_components.includes(component) ? '#ff4d4f' : '#1890ff',
                  color: 'white',
                  fontSize: '12px'
                }}
              >
                {getComponentIcon(component)}
              </Avatar>
              <div style={{
                fontSize: '10px',
                marginTop: '2px',
                color: missing_components.includes(component) ? '#ff4d4f' : '#666'
              }}>
                {component}
              </div>
            </div>
          ))}

          {missing_components.length > 0 && (
            <>
              <PlusOutlined style={{ color: '#999', margin: '0 4px' }} />
              {missing_components.map((component, index) => (
                <div key={`missing-${index}`} style={{ textAlign: 'center' }}>
                  <Avatar
                    size="small"
                    style={{
                      backgroundColor: '#52c41a',
                      color: 'white',
                      fontSize: '12px',
                      border: '2px dashed #52c41a'
                    }}
                  >
                    {getComponentIcon(component)}
                  </Avatar>
                  <div style={{ fontSize: '10px', marginTop: '2px', color: '#52c41a' }}>
                    +{component}
                  </div>
                </div>
              ))}
            </>
          )}
        </Space>
      </div>
    );
  };

  // Render detailed preview modal
  const renderPreviewModal = () => (
    <Modal
      title={
        <Space>
          <AppstoreOutlined />
          {suggestion.name}
          {showScore && (
            <Badge
              count={suggestion.score}
              style={{ backgroundColor: getScoreColor(suggestion.score) }}
            />
          )}
        </Space>
      }
      open={previewVisible}
      onCancel={() => setPreviewVisible(false)}
      footer={[
        <Button key="cancel" onClick={() => setPreviewVisible(false)}>
          Close
        </Button>,
        <Button
          key="apply"
          type="primary"
          onClick={() => {
            setPreviewVisible(false);
            handleApply();
          }}
          disabled={applied}
          loading={applying}
        >
          {applied ? 'Applied' : 'Add Components'}
        </Button>
      ]}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ marginBottom: '16px' }}>
          {renderCombinationPreview()}
        </div>

        <Paragraph>{suggestion.description}</Paragraph>

        <div>
          <Text strong>Why this combination?</Text>
          <Paragraph type="secondary">{suggestion.explanation}</Paragraph>
        </div>

        {suggestion.missing_components && suggestion.missing_components.length > 0 && (
          <div>
            <Text strong>Components to add:</Text>
            <div style={{ marginTop: '8px' }}>
              {suggestion.missing_components.map((component, index) => (
                <Tag key={index} color="green" style={{ marginBottom: '4px' }}>
                  <PlusOutlined style={{ marginRight: '4px' }} />
                  {component}
                </Tag>
              ))}
            </div>
          </div>
        )}

        {suggestion.use_cases && (
          <div>
            <Text strong>Best for:</Text>
            <div style={{ marginTop: '8px' }}>
              {suggestion.use_cases.map((useCase, index) => (
                <Tag key={index} color="blue" style={{ marginBottom: '4px' }}>
                  {useCase.replace('_', ' ')}
                </Tag>
              ))}
            </div>
          </div>
        )}
      </Space>
    </Modal>
  );

  if (compact) {
    return (
      <>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          padding: '8px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          marginBottom: '8px',
          background: applied ? '#f6ffed' : 'white'
        }}>
          <div style={{ width: '60px', marginRight: '12px' }}>
            <Space size="small">
              {suggestion.components?.slice(0, 2).map((component, index) => (
                <Avatar
                  key={index}
                  size="small"
                  style={{ backgroundColor: '#1890ff', fontSize: '10px' }}
                >
                  {getComponentIcon(component)}
                </Avatar>
              ))}
              {suggestion.missing_components?.length > 0 && (
                <Avatar
                  size="small"
                  style={{ backgroundColor: '#52c41a', fontSize: '10px' }}
                >
                  +
                </Avatar>
              )}
            </Space>
          </div>

          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>
              <Text strong style={{ fontSize: '12px' }}>{suggestion.name}</Text>
              {showScore && (
                <Badge
                  count={suggestion.score}
                  style={{
                    backgroundColor: getScoreColor(suggestion.score),
                    marginLeft: '8px'
                  }}
                />
              )}
            </div>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {suggestion.missing_components?.length > 0
                ? `Add ${suggestion.missing_components.join(', ')}`
                : suggestion.explanation
              }
            </Text>
          </div>

          <Space>
            {showPreview && (
              <Tooltip title="Preview">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={handlePreview}
                />
              </Tooltip>
            )}
            <Button
              type={applied ? 'default' : 'primary'}
              size="small"
              icon={applied ? <CheckOutlined /> : <PlusOutlined />}
              onClick={handleApply}
              disabled={applied}
              loading={applying}
            >
              {applied ? 'Added' : 'Add'}
            </Button>
          </Space>
        </div>
        {renderPreviewModal()}
      </>
    );
  }

  return (
    <>
      <Card
        size="small"
        style={{
          marginBottom: '12px',
          border: applied ? '2px solid #52c41a' : '1px solid #d9d9d9'
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Space>
              <Text strong>{suggestion.name}</Text>
              {showScore && (
                <Badge
                  count={suggestion.score}
                  style={{ backgroundColor: getScoreColor(suggestion.score) }}
                />
              )}
            </Space>
            {applied && <CheckOutlined style={{ color: '#52c41a' }} />}
          </div>
        }
        extra={
          <Space>
            {showPreview && (
              <Tooltip title="Preview combination">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={handlePreview}
                />
              </Tooltip>
            )}
            <Button
              type={applied ? 'default' : 'primary'}
              size="small"
              icon={applied ? <CheckOutlined /> : <PlusOutlined />}
              onClick={handleApply}
              disabled={applied}
              loading={applying}
            >
              {applied ? 'Added' : 'Add Components'}
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: '12px' }}>
          {renderCombinationPreview()}
        </div>

        <Paragraph style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#666' }}>
          {suggestion.description}
        </Paragraph>

        {suggestion.missing_components && suggestion.missing_components.length > 0 && (
          <div style={{ marginBottom: '8px' }}>
            <Text strong style={{ fontSize: '11px' }}>Missing components: </Text>
            <Space size="small" wrap>
              {suggestion.missing_components.map((component, index) => (
                <Tag key={index} color="green" size="small">
                  {component}
                </Tag>
              ))}
            </Space>
          </div>
        )}

        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InfoCircleOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
          <Text style={{ fontSize: '11px', fontStyle: 'italic' }}>
            {suggestion.explanation}
          </Text>
        </div>
      </Card>
      {renderPreviewModal()}
    </>
  );
};

ComponentCombinationCard.propTypes = {
  suggestion: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    score: PropTypes.number.isRequired,
    explanation: PropTypes.string.isRequired,
    components: PropTypes.array,
    missing_components: PropTypes.array,
    use_cases: PropTypes.array
  }).isRequired,
  onApply: PropTypes.func,
  onPreview: PropTypes.func,
  applied: PropTypes.bool,
  showPreview: PropTypes.bool,
  showScore: PropTypes.bool,
  compact: PropTypes.bool
};

export default ComponentCombinationCard;

/**
 * Component Combinations List Component
 * Container for multiple component combination cards
 */
export const ComponentCombinationsList = ({
  suggestions = [],
  onApply,
  onPreview,
  appliedSuggestions = new Set(),
  loading = false,
  compact = false,
  showScore = true,
  showPreview = true,
  emptyMessage = "No component combinations available"
}) => {
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin tip="Finding component combinations..." />
      </div>
    );
  }

  if (suggestions.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
        <AppstoreOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
        <div>{emptyMessage}</div>
      </div>
    );
  }

  return (
    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
      {suggestions.map((suggestion) => (
        <ComponentCombinationCard
          key={suggestion.id}
          suggestion={suggestion}
          onApply={onApply}
          onPreview={onPreview}
          applied={appliedSuggestions.has(suggestion.id)}
          compact={compact}
          showScore={showScore}
          showPreview={showPreview}
        />
      ))}
    </div>
  );
};

ComponentCombinationsList.propTypes = {
  suggestions: PropTypes.array,
  onApply: PropTypes.func,
  onPreview: PropTypes.func,
  appliedSuggestions: PropTypes.instanceOf(Set),
  loading: PropTypes.bool,
  compact: PropTypes.bool,
  showScore: PropTypes.bool,
  showPreview: PropTypes.bool,
  emptyMessage: PropTypes.string
};
