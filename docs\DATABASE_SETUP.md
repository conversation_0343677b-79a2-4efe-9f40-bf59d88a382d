# Database Setup Guide

This guide explains how to set up the database for the App Builder 201 application.

## Quick Start

### Option 1: Automated Setup (Recommended)

**Windows:**
```bash
scripts\setup-database.bat
```

**Linux/Mac:**
```bash
scripts/setup-database.sh
```

### Option 2: Manual Setup

Choose between SQLite (easier) or PostgreSQL (production-like):

#### SQLite Setup (Recommended for Development)

1. Create a `.env` file:
```env
USE_POSTGRES=false
DJANGO_DEBUG=true
DJANGO_SECRET_KEY=dev-secret-key-change-in-production
```

2. Run the application:
```bash
docker-compose up
```

#### PostgreSQL Setup

1. Create a `.env` file:
```env
USE_POSTGRES=true
POSTGRES_DB=myapp
POSTGRES_USER=myappuser
POSTGRES_PASSWORD=myapppassword
DJANGO_DEBUG=true
DJANGO_SECRET_KEY=dev-secret-key-change-in-production
```

2. Start the database:
```bash
docker-compose up -d db
```

3. Run migrations:
```bash
docker-compose run --rm backend python setup_db.py
```

4. Start the full application:
```bash
docker-compose up
```

## Troubleshooting

### "psql program not installed" Error

This error occurs when Django tries to use PostgreSQL but the PostgreSQL client tools aren't available. Here are the solutions:

#### Solution 1: Use SQLite (Easiest)
Set `USE_POSTGRES=false` in your `.env` file or docker-compose.yml:

```yaml
environment:
  - USE_POSTGRES=false
```

#### Solution 2: Fix PostgreSQL Setup
The Dockerfile has been updated to include PostgreSQL client tools. Rebuild the container:

```bash
docker-compose build backend
docker-compose up
```

#### Solution 3: Use the Setup Script
Run the database setup script which handles the configuration automatically:

```bash
# Windows
scripts\setup-database.bat

# Linux/Mac
scripts/setup-database.sh
```

### Database Connection Issues

If you're having trouble connecting to PostgreSQL:

1. **Check if PostgreSQL container is running:**
   ```bash
   docker-compose ps
   ```

2. **Check PostgreSQL logs:**
   ```bash
   docker-compose logs db
   ```

3. **Try SQLite as fallback:**
   The application automatically falls back to SQLite if PostgreSQL is unavailable.

### Migration Issues

If migrations fail:

1. **Reset the database:**
   ```bash
   docker-compose down -v
   docker-compose up -d db
   docker-compose run --rm backend python setup_db.py
   ```

2. **Check database permissions:**
   Make sure the database user has the correct permissions.

## Database Configuration Details

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `USE_POSTGRES` | Use PostgreSQL if true, SQLite if false | `true` |
| `POSTGRES_DB` | PostgreSQL database name | `myapp` |
| `POSTGRES_USER` | PostgreSQL username | `myappuser` |
| `POSTGRES_PASSWORD` | PostgreSQL password | `myapppassword` |

### SQLite vs PostgreSQL

**SQLite (Recommended for Development):**
- ✅ No external dependencies
- ✅ Easy setup
- ✅ Good for development and testing
- ❌ Not suitable for production with multiple users

**PostgreSQL (Production-like):**
- ✅ Production-ready
- ✅ Better performance for complex queries
- ✅ Supports concurrent users
- ❌ Requires additional setup
- ❌ More complex troubleshooting

## Advanced Configuration

### Custom Database Settings

You can customize database settings in `backend/app_builder_201/settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_DB', 'myapp'),
        'USER': os.environ.get('POSTGRES_USER', 'myappuser'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD', 'myapppassword'),
        'HOST': 'db',
        'PORT': '5432',
        'OPTIONS': {
            'connect_timeout': 10,
        },
    }
}
```

### Database Backup and Restore

**SQLite:**
```bash
# Backup
docker-compose exec backend cp db.sqlite3 /tmp/backup.sqlite3

# Restore
docker-compose exec backend cp /tmp/backup.sqlite3 db.sqlite3
```

**PostgreSQL:**
```bash
# Backup
docker-compose exec db pg_dump -U myappuser myapp > backup.sql

# Restore
docker-compose exec -T db psql -U myappuser myapp < backup.sql
```

## Getting Help

If you're still having issues:

1. Check the application logs:
   ```bash
   docker-compose logs backend
   ```

2. Verify your environment configuration:
   ```bash
   docker-compose config
   ```

3. Try the automated setup script which handles most common issues automatically.

4. Consider using SQLite for development to avoid PostgreSQL-related issues.
