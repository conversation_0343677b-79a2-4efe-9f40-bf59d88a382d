import React, { useState, useEffect } from 'react';
import { InputNumber, Button, Space, Typography, Tooltip } from 'antd';
import { LinkOutlined, UnlockOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';

const { Text } = Typography;

const SpacingContainer = styled.div`
  width: 100%;
`;

const SpacingVisual = styled.div`
  position: relative;
  width: 120px;
  height: 120px;
  margin: 16px auto;
  background: #f5f5f5;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SpacingBox = styled.div`
  width: 60px;
  height: 60px;
  background: #1890ff;
  opacity: 0.3;
  border-radius: 4px;
  position: relative;
`;

const SpacingInput = styled.div`
  position: absolute;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const TopInput = styled(SpacingInput)`
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
`;

const RightInput = styled(SpacingInput)`
  right: -60px;
  top: 50%;
  transform: translateY(-50%);
`;

const BottomInput = styled(SpacingInput)`
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
`;

const LeftInput = styled(SpacingInput)`
  left: -60px;
  top: 50%;
  transform: translateY(-50%);
`;

const ControlsRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
`;

const QuickPresets = styled.div`
  display: flex;
  gap: 4px;
  margin-top: 8px;
`;

const PresetButton = styled(Button)`
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
`;

/**
 * Visual spacing editor for margin and padding properties
 */
const SpacingEditor = ({
  value,
  onChange,
  type = 'margin', // 'margin' or 'padding'
  showVisual = true,
  showPresets = true,
  unit = 'px',
  ...props
}) => {
  const [values, setValues] = useState({ top: 0, right: 0, bottom: 0, left: 0 });
  const [isLinked, setIsLinked] = useState(false);

  // Parse spacing value on mount and when value changes
  useEffect(() => {
    if (value) {
      const parsed = parseSpacingValue(value);
      setValues(parsed);
    }
  }, [value]);

  // Parse spacing value like "10px 20px" or "10px 20px 30px 40px"
  const parseSpacingValue = (val) => {
    if (!val) return { top: 0, right: 0, bottom: 0, left: 0 };
    
    if (typeof val === 'object') {
      return {
        top: parseFloat(val.top) || 0,
        right: parseFloat(val.right) || 0,
        bottom: parseFloat(val.bottom) || 0,
        left: parseFloat(val.left) || 0
      };
    }
    
    const parts = val.toString().split(/\s+/).map(p => parseFloat(p.replace(/[^\d.-]/g, '')) || 0);
    
    switch (parts.length) {
      case 1:
        return { top: parts[0], right: parts[0], bottom: parts[0], left: parts[0] };
      case 2:
        return { top: parts[0], right: parts[1], bottom: parts[0], left: parts[1] };
      case 3:
        return { top: parts[0], right: parts[1], bottom: parts[2], left: parts[1] };
      case 4:
        return { top: parts[0], right: parts[1], bottom: parts[2], left: parts[3] };
      default:
        return { top: 0, right: 0, bottom: 0, left: 0 };
    }
  };

  // Format values for output
  const formatSpacingValue = (vals) => {
    const { top, right, bottom, left } = vals;
    
    // If all values are the same, return single value
    if (top === right && right === bottom && bottom === left) {
      return `${top}${unit}`;
    }
    
    // If top/bottom and left/right are the same
    if (top === bottom && left === right) {
      return `${top}${unit} ${right}${unit}`;
    }
    
    // Return all four values
    return `${top}${unit} ${right}${unit} ${bottom}${unit} ${left}${unit}`;
  };

  // Handle individual value change
  const handleValueChange = (side, newValue) => {
    const newValues = { ...values };
    
    if (isLinked) {
      // Update all sides when linked
      newValues.top = newValue;
      newValues.right = newValue;
      newValues.bottom = newValue;
      newValues.left = newValue;
    } else {
      // Update only the specific side
      newValues[side] = newValue;
    }
    
    setValues(newValues);
    onChange?.(formatSpacingValue(newValues));
  };

  // Handle preset click
  const handlePresetClick = (preset) => {
    const newValues = parseSpacingValue(preset);
    setValues(newValues);
    onChange?.(formatSpacingValue(newValues));
  };

  // Toggle linked state
  const toggleLinked = () => {
    setIsLinked(!isLinked);
  };

  const presets = [
    '0px',
    '4px',
    '8px',
    '12px',
    '16px',
    '24px',
    '32px',
    '8px 16px',
    '16px 24px'
  ];

  return (
    <SpacingContainer>
      <ControlsRow>
        <Text strong style={{ fontSize: '12px' }}>
          {type === 'margin' ? 'Margin' : 'Padding'}
        </Text>
        <Button
          type={isLinked ? 'primary' : 'default'}
          size="small"
          icon={isLinked ? <LinkOutlined /> : <UnlockOutlined />}
          onClick={toggleLinked}
          title={isLinked ? 'Unlink sides' : 'Link all sides'}
        />
      </ControlsRow>

      {showVisual && (
        <SpacingVisual>
          <TopInput>
            <InputNumber
              size="small"
              value={values.top}
              onChange={(val) => handleValueChange('top', val || 0)}
              style={{ width: 50 }}
              min={0}
            />
          </TopInput>
          
          <RightInput>
            <InputNumber
              size="small"
              value={values.right}
              onChange={(val) => handleValueChange('right', val || 0)}
              style={{ width: 50 }}
              min={0}
            />
          </RightInput>
          
          <BottomInput>
            <InputNumber
              size="small"
              value={values.bottom}
              onChange={(val) => handleValueChange('bottom', val || 0)}
              style={{ width: 50 }}
              min={0}
            />
          </BottomInput>
          
          <LeftInput>
            <InputNumber
              size="small"
              value={values.left}
              onChange={(val) => handleValueChange('left', val || 0)}
              style={{ width: 50 }}
              min={0}
            />
          </LeftInput>
          
          <SpacingBox />
        </SpacingVisual>
      )}

      <Space direction="vertical" style={{ width: '100%' }}>
        <ControlsRow>
          <Text style={{ fontSize: '12px', minWidth: '30px' }}>Top:</Text>
          <InputNumber
            value={values.top}
            onChange={(val) => handleValueChange('top', val || 0)}
            style={{ flex: 1 }}
            min={0}
            addonAfter={unit}
          />
        </ControlsRow>
        
        <ControlsRow>
          <Text style={{ fontSize: '12px', minWidth: '30px' }}>Right:</Text>
          <InputNumber
            value={values.right}
            onChange={(val) => handleValueChange('right', val || 0)}
            style={{ flex: 1 }}
            min={0}
            addonAfter={unit}
          />
        </ControlsRow>
        
        <ControlsRow>
          <Text style={{ fontSize: '12px', minWidth: '30px' }}>Bottom:</Text>
          <InputNumber
            value={values.bottom}
            onChange={(val) => handleValueChange('bottom', val || 0)}
            style={{ flex: 1 }}
            min={0}
            addonAfter={unit}
          />
        </ControlsRow>
        
        <ControlsRow>
          <Text style={{ fontSize: '12px', minWidth: '30px' }}>Left:</Text>
          <InputNumber
            value={values.left}
            onChange={(val) => handleValueChange('left', val || 0)}
            style={{ flex: 1 }}
            min={0}
            addonAfter={unit}
          />
        </ControlsRow>
      </Space>

      {showPresets && (
        <QuickPresets>
          <Text style={{ fontSize: '12px', marginRight: '8px' }}>Presets:</Text>
          {presets.map((preset, index) => (
            <PresetButton
              key={index}
              onClick={() => handlePresetClick(preset)}
              title={`Apply ${preset}`}
            >
              {preset}
            </PresetButton>
          ))}
        </QuickPresets>
      )}
    </SpacingContainer>
  );
};

export default SpacingEditor;
