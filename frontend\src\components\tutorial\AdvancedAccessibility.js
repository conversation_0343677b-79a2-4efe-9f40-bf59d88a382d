/**
 * Advanced Accessibility Features for Tutorial System
 * 
 * Enhanced accessibility features including voice navigation, 
 * screen reader optimizations, motor disability accommodations,
 * and cognitive accessibility improvements.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Switch, Slider, Select, Card, Typography, Space } from 'antd';
import {
  SoundOutlined,
  EyeOutlined,
  HandOutlined,
  BrainOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  PauseOutlined
} from '@ant-design/icons';
import { useTutorial } from './TutorialManager';
import { useLocalization } from './TutorialLocalization';

const { Title, Text } = Typography;
const { Option } = Select;

// Voice Navigation System
export class VoiceNavigationSystem {
  constructor(tutorialManager, localization) {
    this.tutorialManager = tutorialManager;
    this.localization = localization;
    this.recognition = null;
    this.synthesis = window.speechSynthesis;
    this.isListening = false;
    this.isEnabled = false;
    this.commands = this.initializeCommands();
    this.setupSpeechRecognition();
  }

  initializeCommands() {
    const { t } = this.localization;
    return {
      // Navigation commands
      'next': () => this.tutorialManager.nextStep(),
      'next step': () => this.tutorialManager.nextStep(),
      'continue': () => this.tutorialManager.nextStep(),
      'previous': () => this.tutorialManager.previousStep(),
      'previous step': () => this.tutorialManager.previousStep(),
      'back': () => this.tutorialManager.previousStep(),
      
      // Control commands
      'pause': () => this.tutorialManager.pauseTutorial(),
      'pause tutorial': () => this.tutorialManager.pauseTutorial(),
      'resume': () => this.tutorialManager.resumeTutorial(),
      'resume tutorial': () => this.tutorialManager.resumeTutorial(),
      'stop': () => this.tutorialManager.skipTutorial(),
      'stop tutorial': () => this.tutorialManager.skipTutorial(),
      'exit': () => this.tutorialManager.skipTutorial(),
      
      // Information commands
      'repeat': () => this.repeatCurrentStep(),
      'help': () => this.announceHelp(),
      'where am i': () => this.announceCurrentPosition(),
      'what step': () => this.announceCurrentPosition(),
      
      // Speed control
      'speak faster': () => this.adjustSpeechRate(0.2),
      'speak slower': () => this.adjustSpeechRate(-0.2),
      'normal speed': () => this.setSpeechRate(1.0)
    };
  }

  setupSpeechRecognition() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.warn('Speech recognition not supported in this browser');
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();
    
    this.recognition.continuous = true;
    this.recognition.interimResults = false;
    this.recognition.lang = this.localization.currentLanguage;
    
    this.recognition.onresult = (event) => {
      const command = event.results[event.results.length - 1][0].transcript.toLowerCase().trim();
      this.processVoiceCommand(command);
    };

    this.recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      if (event.error === 'no-speech') {
        this.speak('No speech detected. Please try again.');
      }
    };

    this.recognition.onend = () => {
      if (this.isListening && this.isEnabled) {
        // Restart recognition if it stops unexpectedly
        setTimeout(() => this.recognition.start(), 100);
      }
    };
  }

  enable() {
    this.isEnabled = true;
    this.startListening();
    this.speak('Voice navigation enabled. Say "help" for available commands.');
  }

  disable() {
    this.isEnabled = false;
    this.stopListening();
    this.speak('Voice navigation disabled.');
  }

  startListening() {
    if (this.recognition && !this.isListening) {
      this.isListening = true;
      this.recognition.start();
    }
  }

  stopListening() {
    if (this.recognition && this.isListening) {
      this.isListening = false;
      this.recognition.stop();
    }
  }

  processVoiceCommand(command) {
    console.log('Voice command received:', command);
    
    // Find matching command
    const matchedCommand = Object.keys(this.commands).find(cmd => 
      command.includes(cmd) || this.fuzzyMatch(command, cmd)
    );

    if (matchedCommand) {
      this.commands[matchedCommand]();
      this.speak(`Executing: ${matchedCommand}`);
    } else {
      this.speak('Command not recognized. Say "help" for available commands.');
    }
  }

  fuzzyMatch(input, target, threshold = 0.7) {
    // Simple fuzzy matching for voice commands
    const distance = this.levenshteinDistance(input, target);
    const similarity = 1 - (distance / Math.max(input.length, target.length));
    return similarity >= threshold;
  }

  levenshteinDistance(str1, str2) {
    const matrix = [];
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    return matrix[str2.length][str1.length];
  }

  speak(text, options = {}) {
    if (!this.synthesis) return;

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = this.localization.currentLanguage;
    utterance.rate = options.rate || 1.0;
    utterance.pitch = options.pitch || 1.0;
    utterance.volume = options.volume || 1.0;

    this.synthesis.speak(utterance);
  }

  repeatCurrentStep() {
    const currentStep = this.tutorialManager.currentStep;
    if (currentStep) {
      this.speak(`Current step: ${currentStep.title}. ${currentStep.content}`);
    } else {
      this.speak('No active tutorial step.');
    }
  }

  announceHelp() {
    const helpText = `Available voice commands: 
      Navigation: next, previous, continue, back.
      Control: pause, resume, stop, exit.
      Information: repeat, help, where am I.
      Speed: speak faster, speak slower, normal speed.`;
    this.speak(helpText);
  }

  announceCurrentPosition() {
    const { currentStepIndex, activeTutorial } = this.tutorialManager;
    if (activeTutorial) {
      const position = `Step ${currentStepIndex + 1} of ${activeTutorial.steps.length} in tutorial: ${activeTutorial.title}`;
      this.speak(position);
    } else {
      this.speak('No active tutorial.');
    }
  }

  adjustSpeechRate(delta) {
    const currentRate = this.synthesis.rate || 1.0;
    const newRate = Math.max(0.5, Math.min(2.0, currentRate + delta));
    this.setSpeechRate(newRate);
  }

  setSpeechRate(rate) {
    // Note: This affects future utterances, not current ones
    this.defaultSpeechRate = rate;
    this.speak(`Speech rate set to ${Math.round(rate * 100)}%`);
  }
}

// Motor Disability Accommodations
export class MotorAccessibilityManager {
  constructor() {
    this.dwellTime = 1000; // Default dwell time for hover actions
    this.clickAlternatives = new Map();
    this.gestureRecognizer = null;
    this.setupMotorAccommodations();
  }

  setupMotorAccommodations() {
    this.setupDwellClicking();
    this.setupKeyboardAlternatives();
    this.setupGestureRecognition();
  }

  setupDwellClicking() {
    let dwellTimer = null;
    let currentTarget = null;

    document.addEventListener('mouseover', (e) => {
      if (e.target.matches('button, [role="button"], .tutorial-interactive')) {
        currentTarget = e.target;
        dwellTimer = setTimeout(() => {
          if (currentTarget === e.target) {
            this.simulateClick(e.target);
          }
        }, this.dwellTime);
      }
    });

    document.addEventListener('mouseout', () => {
      if (dwellTimer) {
        clearTimeout(dwellTimer);
        dwellTimer = null;
      }
      currentTarget = null;
    });
  }

  setupKeyboardAlternatives() {
    // Enhanced keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.altKey) {
        switch (e.key) {
          case '1':
            this.triggerTutorialAction('next');
            break;
          case '2':
            this.triggerTutorialAction('previous');
            break;
          case '3':
            this.triggerTutorialAction('pause');
            break;
          case '4':
            this.triggerTutorialAction('skip');
            break;
        }
      }
    });
  }

  setupGestureRecognition() {
    // Basic gesture recognition for touch devices
    let touchStartX = 0;
    let touchStartY = 0;

    document.addEventListener('touchstart', (e) => {
      touchStartX = e.touches[0].clientX;
      touchStartY = e.touches[0].clientY;
    });

    document.addEventListener('touchend', (e) => {
      const touchEndX = e.changedTouches[0].clientX;
      const touchEndY = e.changedTouches[0].clientY;
      
      const deltaX = touchEndX - touchStartX;
      const deltaY = touchEndY - touchStartY;
      
      const minSwipeDistance = 50;
      
      if (Math.abs(deltaX) > minSwipeDistance && Math.abs(deltaY) < 100) {
        if (deltaX > 0) {
          this.triggerTutorialAction('next'); // Swipe right
        } else {
          this.triggerTutorialAction('previous'); // Swipe left
        }
      } else if (Math.abs(deltaY) > minSwipeDistance && Math.abs(deltaX) < 100) {
        if (deltaY < 0) {
          this.triggerTutorialAction('pause'); // Swipe up
        } else {
          this.triggerTutorialAction('skip'); // Swipe down
        }
      }
    });
  }

  simulateClick(element) {
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(event);
    
    // Visual feedback
    element.style.outline = '3px solid #1890ff';
    setTimeout(() => {
      element.style.outline = '';
    }, 200);
  }

  triggerTutorialAction(action) {
    const event = new CustomEvent('tutorialAction', { detail: { action } });
    window.dispatchEvent(event);
  }

  setDwellTime(time) {
    this.dwellTime = Math.max(500, Math.min(5000, time));
  }
}

// Cognitive Accessibility Enhancements
export class CognitiveAccessibilityManager {
  constructor() {
    this.simplificationLevel = 'normal';
    this.readingSpeed = 'normal';
    this.memoryAids = true;
    this.setupCognitiveSupport();
  }

  setupCognitiveSupport() {
    this.addProgressIndicators();
    this.addMemoryAids();
    this.setupReadingSupport();
  }

  addProgressIndicators() {
    // Enhanced progress indicators for cognitive support
    const progressIndicator = document.createElement('div');
    progressIndicator.className = 'cognitive-progress-indicator';
    progressIndicator.style.cssText = `
      position: fixed;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 20px;
      border-radius: 20px;
      z-index: 10000;
      font-size: 14px;
      display: none;
    `;
    document.body.appendChild(progressIndicator);
  }

  addMemoryAids() {
    // Add breadcrumb navigation
    const breadcrumb = document.createElement('div');
    breadcrumb.className = 'tutorial-breadcrumb';
    breadcrumb.style.cssText = `
      position: fixed;
      bottom: 10px;
      left: 10px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 10px;
      max-width: 300px;
      z-index: 9999;
      display: none;
    `;
    document.body.appendChild(breadcrumb);
  }

  setupReadingSupport() {
    // Add reading guides and highlighting
    this.addReadingGuide();
    this.setupTextHighlighting();
  }

  addReadingGuide() {
    const guide = document.createElement('div');
    guide.className = 'reading-guide';
    guide.style.cssText = `
      position: absolute;
      height: 2px;
      background: #1890ff;
      width: 100%;
      z-index: 9998;
      display: none;
      transition: top 0.3s ease;
    `;
    document.body.appendChild(guide);
  }

  setupTextHighlighting() {
    // Highlight important text as user reads
    document.addEventListener('mouseover', (e) => {
      if (e.target.matches('p, h1, h2, h3, h4, h5, h6, span')) {
        e.target.style.backgroundColor = 'rgba(24, 144, 255, 0.1)';
      }
    });

    document.addEventListener('mouseout', (e) => {
      if (e.target.matches('p, h1, h2, h3, h4, h5, h6, span')) {
        e.target.style.backgroundColor = '';
      }
    });
  }

  simplifyContent(content, level = 'normal') {
    switch (level) {
      case 'simple':
        return this.simplifyText(content);
      case 'minimal':
        return this.extractKeyPoints(content);
      default:
        return content;
    }
  }

  simplifyText(text) {
    // Basic text simplification
    return text
      .replace(/\b(utilize|utilization)\b/gi, 'use')
      .replace(/\b(demonstrate|demonstration)\b/gi, 'show')
      .replace(/\b(accomplish|achievement)\b/gi, 'do')
      .replace(/\b(subsequently|thereafter)\b/gi, 'then')
      .replace(/\b(approximately)\b/gi, 'about');
  }

  extractKeyPoints(text) {
    // Extract key points from longer text
    const sentences = text.split(/[.!?]+/);
    const keyPoints = sentences
      .filter(sentence => sentence.length > 10)
      .slice(0, 2)
      .map(sentence => sentence.trim())
      .join('. ');
    
    return keyPoints + (keyPoints ? '.' : '');
  }

  updateMemoryAids(currentStep, totalSteps, tutorialTitle) {
    const breadcrumb = document.querySelector('.tutorial-breadcrumb');
    if (breadcrumb && this.memoryAids) {
      breadcrumb.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">${tutorialTitle}</div>
        <div>Step ${currentStep} of ${totalSteps}</div>
        <div style="font-size: 12px; color: #666; margin-top: 5px;">
          Previous steps completed ✓
        </div>
      `;
      breadcrumb.style.display = 'block';
    }
  }
}

// Accessibility Settings Panel
export const AccessibilitySettingsPanel = ({ visible, onClose }) => {
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [dwellTime, setDwellTime] = useState(1000);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [simplificationLevel, setSimplificationLevel] = useState('normal');
  const [memoryAids, setMemoryAids] = useState(true);
  const [highContrast, setHighContrast] = useState(false);
  const [largeText, setLargeText] = useState(false);

  const tutorialManager = useTutorial();
  const localization = useLocalization();
  
  const voiceSystemRef = useRef(null);
  const motorManagerRef = useRef(null);
  const cognitiveManagerRef = useRef(null);

  useEffect(() => {
    if (!voiceSystemRef.current) {
      voiceSystemRef.current = new VoiceNavigationSystem(tutorialManager, localization);
    }
    if (!motorManagerRef.current) {
      motorManagerRef.current = new MotorAccessibilityManager();
    }
    if (!cognitiveManagerRef.current) {
      cognitiveManagerRef.current = new CognitiveAccessibilityManager();
    }
  }, [tutorialManager, localization]);

  const handleVoiceToggle = (enabled) => {
    setVoiceEnabled(enabled);
    if (voiceSystemRef.current) {
      if (enabled) {
        voiceSystemRef.current.enable();
      } else {
        voiceSystemRef.current.disable();
      }
    }
  };

  const handleDwellTimeChange = (time) => {
    setDwellTime(time);
    if (motorManagerRef.current) {
      motorManagerRef.current.setDwellTime(time);
    }
  };

  const handleSpeechRateChange = (rate) => {
    setSpeechRate(rate);
    if (voiceSystemRef.current) {
      voiceSystemRef.current.setSpeechRate(rate);
    }
  };

  const handleSimplificationChange = (level) => {
    setSimplificationLevel(level);
    if (cognitiveManagerRef.current) {
      cognitiveManagerRef.current.simplificationLevel = level;
    }
  };

  const handleHighContrastToggle = (enabled) => {
    setHighContrast(enabled);
    document.body.classList.toggle('high-contrast', enabled);
  };

  const handleLargeTextToggle = (enabled) => {
    setLargeText(enabled);
    document.body.classList.toggle('large-text', enabled);
  };

  if (!visible) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      zIndex: 10000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card
        title="Accessibility Settings"
        style={{ width: 500, maxHeight: '80vh', overflow: 'auto' }}
        extra={<Button onClick={onClose}>Close</Button>}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* Voice Navigation */}
          <Card size="small" title={<><SoundOutlined /> Voice Navigation</>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>Enable Voice Commands</Text>
                <Switch checked={voiceEnabled} onChange={handleVoiceToggle} />
              </div>
              
              <div>
                <Text>Speech Rate: {speechRate}x</Text>
                <Slider
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  value={speechRate}
                  onChange={handleSpeechRateChange}
                  disabled={!voiceEnabled}
                />
              </div>
              
              {voiceEnabled && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Say "help" for available voice commands
                </Text>
              )}
            </Space>
          </Card>

          {/* Motor Accessibility */}
          <Card size="small" title={<><HandOutlined /> Motor Accessibility</>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text>Dwell Click Time: {dwellTime}ms</Text>
                <Slider
                  min={500}
                  max={5000}
                  step={100}
                  value={dwellTime}
                  onChange={handleDwellTimeChange}
                />
              </div>
              
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Hover over buttons to activate them automatically
              </Text>
            </Space>
          </Card>

          {/* Cognitive Support */}
          <Card size="small" title={<><BrainOutlined /> Cognitive Support</>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>Content Simplification</Text>
                <Select
                  value={simplificationLevel}
                  onChange={handleSimplificationChange}
                  style={{ width: 120 }}
                >
                  <Option value="normal">Normal</Option>
                  <Option value="simple">Simple</Option>
                  <Option value="minimal">Minimal</Option>
                </Select>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>Memory Aids</Text>
                <Switch checked={memoryAids} onChange={setMemoryAids} />
              </div>
            </Space>
          </Card>

          {/* Visual Accessibility */}
          <Card size="small" title={<><EyeOutlined /> Visual Accessibility</>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>High Contrast Mode</Text>
                <Switch checked={highContrast} onChange={handleHighContrastToggle} />
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>Large Text</Text>
                <Switch checked={largeText} onChange={handleLargeTextToggle} />
              </div>
            </Space>
          </Card>

          {/* Quick Actions */}
          <Card size="small" title="Quick Actions">
            <Space wrap>
              <Button 
                icon={<PlayCircleOutlined />}
                onClick={() => voiceSystemRef.current?.speak('Testing voice output')}
                disabled={!voiceEnabled}
              >
                Test Voice
              </Button>
              
              <Button 
                icon={<SettingOutlined />}
                onClick={() => {
                  // Reset to defaults
                  setVoiceEnabled(false);
                  setDwellTime(1000);
                  setSpeechRate(1.0);
                  setSimplificationLevel('normal');
                  setMemoryAids(true);
                  setHighContrast(false);
                  setLargeText(false);
                }}
              >
                Reset Defaults
              </Button>
            </Space>
          </Card>
        </Space>
      </Card>
    </div>
  );
};

// Accessibility Manager Hook
export const useAdvancedAccessibility = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [settings, setSettings] = useState({
    voiceNavigation: false,
    motorSupport: true,
    cognitiveSupport: true,
    visualEnhancements: false
  });

  const managers = useRef({
    voice: null,
    motor: null,
    cognitive: null
  });

  const tutorialManager = useTutorial();
  const localization = useLocalization();

  useEffect(() => {
    if (isEnabled) {
      // Initialize accessibility managers
      if (!managers.current.voice) {
        managers.current.voice = new VoiceNavigationSystem(tutorialManager, localization);
      }
      if (!managers.current.motor) {
        managers.current.motor = new MotorAccessibilityManager();
      }
      if (!managers.current.cognitive) {
        managers.current.cognitive = new CognitiveAccessibilityManager();
      }
    }
  }, [isEnabled, tutorialManager, localization]);

  const enableAccessibility = useCallback(() => {
    setIsEnabled(true);
  }, []);

  const disableAccessibility = useCallback(() => {
    setIsEnabled(false);
    // Cleanup managers
    if (managers.current.voice) {
      managers.current.voice.disable();
    }
  }, []);

  const updateSettings = useCallback((newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  return {
    isEnabled,
    settings,
    managers: managers.current,
    enableAccessibility,
    disableAccessibility,
    updateSettings
  };
};

// CSS for accessibility enhancements
const accessibilityStyles = `
  .high-contrast {
    filter: contrast(150%) brightness(120%);
  }
  
  .high-contrast .tutorial-overlay {
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
  }
  
  .high-contrast button {
    border: 2px solid white !important;
    background: black !important;
    color: white !important;
  }
  
  .large-text {
    font-size: 120% !important;
  }
  
  .large-text .tutorial-step {
    font-size: 130% !important;
    line-height: 1.6 !important;
  }
  
  .cognitive-progress-indicator {
    font-family: 'Arial', sans-serif !important;
    font-weight: bold !important;
  }
  
  .tutorial-breadcrumb {
    font-family: 'Arial', sans-serif !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }
  
  .reading-guide {
    box-shadow: 0 1px 3px rgba(24, 144, 255, 0.5) !important;
  }
  
  @media (prefers-reduced-motion: reduce) {
    .tutorial-overlay,
    .tutorial-step,
    .tutorial-highlight {
      transition: none !important;
      animation: none !important;
    }
  }
  
  @media (prefers-contrast: high) {
    .tutorial-overlay {
      background: rgba(0, 0, 0, 0.95) !important;
      border: 2px solid white !important;
    }
  }
`;

// Inject accessibility styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = accessibilityStyles;
  document.head.appendChild(styleSheet);
}

export default {
  VoiceNavigationSystem,
  MotorAccessibilityManager,
  CognitiveAccessibilityManager,
  AccessibilitySettingsPanel,
  useAdvancedAccessibility
};
