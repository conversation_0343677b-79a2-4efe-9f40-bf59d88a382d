/**
 * Enhanced WebSocket Service with Exponential Backoff and Jitter
 * Provides robust reconnection logic for WebSocket connections
 */

import { useRef, useCallback, useEffect } from 'react';

// WebSocket connection states
export const ConnectionState = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  RECONNECTING: 'reconnecting',
  FAILED: 'failed'
};

/**
 * Enhanced WebSocket hook with exponential backoff and jitter
 * @param {Object} options - Configuration options
 * @returns {Object} WebSocket state and methods
 */
export function useWebSocketWithBackoff(options = {}) {
  const {
    url,
    autoConnect = true,
    autoReconnect = true,
    maxReconnectAttempts = 10,
    baseDelay = 1000, // 1 second
    maxDelay = 30000, // 30 seconds
    jitterFactor = 0.3, // 30% jitter
    onOpen = null,
    onClose = null,
    onMessage = null,
    onError = null,
    onReconnecting = null,
    debug = false
  } = options;

  // Refs for persistent state
  const socketRef = useRef(null);
  const connectionAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef(null);
  const connectionStateRef = useRef(ConnectionState.DISCONNECTED);
  const isManuallyClosedRef = useRef(false);

  /**
   * Calculate reconnection delay with exponential backoff and jitter
   * @param {number} attempt - Current attempt number
   * @returns {number} Delay in milliseconds
   */
  const calculateReconnectDelay = useCallback((attempt) => {
    // Exponential backoff: baseDelay * 2^attempt
    const exponentialDelay = Math.min(maxDelay, baseDelay * Math.pow(2, attempt));

    // Add jitter to prevent thundering herd problem
    // Jitter range: ±jitterFactor% of the exponential delay
    const jitterRange = exponentialDelay * jitterFactor;
    const jitter = (Math.random() - 0.5) * 2 * jitterRange;

    // Ensure delay is positive and within bounds
    const finalDelay = Math.max(baseDelay, exponentialDelay + jitter);

    if (debug) {
      console.log(`Reconnect delay calculation:`, {
        attempt,
        exponentialDelay,
        jitter,
        finalDelay: Math.round(finalDelay)
      });
    }

    return Math.round(finalDelay);
  }, [baseDelay, maxDelay, jitterFactor, debug]);

  /**
   * Connect to WebSocket server
   */
  const connectWebSocket = useCallback(() => {
    if (!url) {
      console.error('WebSocket URL is required');
      return;
    }

    // Clear any existing reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Don't connect if already connected or connecting
    if (socketRef.current &&
      (socketRef.current.readyState === WebSocket.CONNECTING ||
        socketRef.current.readyState === WebSocket.OPEN)) {
      return;
    }

    try {
      connectionStateRef.current = ConnectionState.CONNECTING;

      if (debug) {
        console.log(`Connecting to WebSocket: ${url} (attempt ${connectionAttemptsRef.current + 1})`);
      }

      socketRef.current = new WebSocket(url);

      socketRef.current.onopen = (event) => {
        connectionStateRef.current = ConnectionState.CONNECTED;
        connectionAttemptsRef.current = 0; // Reset on successful connection
        isManuallyClosedRef.current = false;

        if (debug) {
          console.log('WebSocket connected successfully');
        }

        if (onOpen) {
          onOpen(event);
        }
      };

      socketRef.current.onclose = (event) => {
        const wasConnected = connectionStateRef.current === ConnectionState.CONNECTED;
        connectionStateRef.current = ConnectionState.DISCONNECTED;

        if (debug) {
          console.log('WebSocket closed:', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
            wasConnected,
            isManuallyClosedRef: isManuallyClosedRef.current
          });
        }

        if (onClose) {
          onClose(event);
        }

        // Attempt reconnection if not manually closed and auto-reconnect is enabled
        if (!isManuallyClosedRef.current && autoReconnect) {
          attemptReconnection();
        }
      };

      socketRef.current.onmessage = (event) => {
        if (onMessage) {
          try {
            const data = JSON.parse(event.data);
            onMessage(data);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
            onMessage(event.data);
          }
        }
      };

      socketRef.current.onerror = (event) => {
        if (debug) {
          console.error('WebSocket error:', event);
        }

        if (onError) {
          onError(event);
        }
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      connectionStateRef.current = ConnectionState.FAILED;

      if (onError) {
        onError(error);
      }
    }
  }, [url, autoReconnect, debug, onOpen, onClose, onMessage, onError]);

  /**
   * Attempt reconnection with exponential backoff and jitter
   */
  const attemptReconnection = useCallback(() => {
    // Check if we've exceeded max attempts
    if (connectionAttemptsRef.current >= maxReconnectAttempts) {
      connectionStateRef.current = ConnectionState.FAILED;

      if (debug) {
        console.log(`Max reconnection attempts (${maxReconnectAttempts}) reached`);
      }

      return;
    }

    connectionStateRef.current = ConnectionState.RECONNECTING;
    connectionAttemptsRef.current += 1;

    const delay = calculateReconnectDelay(connectionAttemptsRef.current - 1);

    if (debug) {
      console.log(`Reconnecting in ${Math.round(delay / 1000)}s (attempt ${connectionAttemptsRef.current}/${maxReconnectAttempts})`);
    }

    if (onReconnecting) {
      onReconnecting({
        attempt: connectionAttemptsRef.current,
        maxAttempts: maxReconnectAttempts,
        delay
      });
    }

    reconnectTimeoutRef.current = setTimeout(() => {
      connectWebSocket();
    }, delay);
  }, [maxReconnectAttempts, calculateReconnectDelay, connectWebSocket, debug, onReconnecting]);

  /**
   * Manually disconnect WebSocket
   */
  const disconnect = useCallback(() => {
    isManuallyClosedRef.current = true;

    // Clear reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Close socket
    if (socketRef.current) {
      socketRef.current.close(1000, 'Manual disconnect');
      socketRef.current = null;
    }

    connectionStateRef.current = ConnectionState.DISCONNECTED;
    connectionAttemptsRef.current = 0;
  }, []);

  /**
   * Send message through WebSocket
   */
  const sendMessage = useCallback((data) => {
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      try {
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        socketRef.current.send(message);
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        return false;
      }
    } else {
      if (debug) {
        console.warn('Cannot send message: WebSocket not connected');
      }
      return false;
    }
  }, [debug]);

  /**
   * Get current connection state
   */
  const getConnectionState = useCallback(() => {
    return connectionStateRef.current;
  }, []);

  /**
   * Check if WebSocket is connected
   */
  const isConnected = useCallback(() => {
    return !!(socketRef.current && socketRef.current.readyState === WebSocket.OPEN);
  }, []);

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect) {
      connectWebSocket();
    }

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [autoConnect, connectWebSocket, disconnect]);

  return {
    connect: connectWebSocket,
    disconnect,
    send: sendMessage,
    isConnected,
    getConnectionState,
    connectionAttempts: connectionAttemptsRef.current,
    maxAttempts: maxReconnectAttempts
  };
}