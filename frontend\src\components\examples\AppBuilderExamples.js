import React, { useState } from 'react';
import { <PERSON>, But<PERSON>, Tabs, <PERSON>, Typography, Alert, Divider } from 'antd';
import { 
  PlayCircleOutlined, 
  CodeOutlined, 
  EyeOutlined,
  BulbOutlined,
  RocketOutlined,
  ToolOutlined,
  BgColorsOutlined,
  LayoutOutlined,
  ComponentOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const ExamplesContainer = styled.div`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const ExampleCard = styled(Card)`
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .ant-card-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    
    .ant-card-head-title {
      color: white;
    }
  }
`;

const DemoArea = styled.div`
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 16px 0;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const CodeBlock = styled.pre`
  background: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  margin: 16px 0;
`;

const FeatureHighlight = styled.div`
  background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
  border-left: 4px solid #667eea;
  padding: 16px;
  margin: 16px 0;
  border-radius: 0 8px 8px 0;
`;

const AppBuilderExamples = () => {
  const [activeExample, setActiveExample] = useState('component-builder');
  const [demoComponent, setDemoComponent] = useState(null);

  // Component Builder Example
  const ComponentBuilderExample = () => {
    const [componentType, setComponentType] = useState('button');
    const [componentProps, setComponentProps] = useState({
      text: 'Click Me',
      variant: 'primary',
      size: 'medium'
    });

    const createComponent = () => {
      const newComponent = {
        id: Date.now().toString(),
        type: componentType,
        props: componentProps,
        createdAt: new Date().toISOString()
      };
      setDemoComponent(newComponent);
    };

    return (
      <ExampleCard
        title={
          <Space>
            <ComponentOutlined />
            Component Builder Example
          </Space>
        }
        extra={
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={createComponent}
          >
            Create Component
          </Button>
        }
      >
        <FeatureHighlight>
          <Title level={4}>
            <BulbOutlined style={{ color: '#667eea', marginRight: 8 }} />
            What you'll learn:
          </Title>
          <ul>
            <li>How to create components with custom properties</li>
            <li>Property validation and error handling</li>
            <li>Real-time preview of component changes</li>
            <li>Component type selection and configuration</li>
          </ul>
        </FeatureHighlight>

        <Paragraph>
          The Component Builder allows you to create reusable UI components with custom properties. 
          Try changing the component type and properties below, then click "Create Component" to see it in action.
        </Paragraph>

        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Component Type:</Text>
            <Space style={{ marginLeft: 16 }}>
              {['button', 'input', 'text', 'container'].map(type => (
                <Button
                  key={type}
                  type={componentType === type ? 'primary' : 'default'}
                  size="small"
                  onClick={() => setComponentType(type)}
                >
                  {type}
                </Button>
              ))}
            </Space>
          </div>

          <div>
            <Text strong>Properties:</Text>
            <CodeBlock>
{JSON.stringify(componentProps, null, 2)}
            </CodeBlock>
          </div>
        </Space>

        <DemoArea>
          {demoComponent ? (
            <div>
              <Title level={4}>Created Component:</Title>
              <Card size="small">
                <Text strong>Type:</Text> {demoComponent.type}<br />
                <Text strong>ID:</Text> {demoComponent.id}<br />
                <Text strong>Props:</Text> {JSON.stringify(demoComponent.props)}
              </Card>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#6c757d' }}>
              <ComponentOutlined style={{ fontSize: 48, marginBottom: 16 }} />
              <Text>Click "Create Component" to see your component here</Text>
            </div>
          )}
        </DemoArea>

        <Alert
          message="Pro Tip"
          description="Use the property editor to fine-tune your component's appearance and behavior. The reset button will restore original values if you make a mistake."
          type="info"
          showIcon
        />
      </ExampleCard>
    );
  };

  // Layout Designer Example
  const LayoutDesignerExample = () => {
    const [layoutType, setLayoutType] = useState('grid');
    const [layoutItems, setLayoutItems] = useState([]);

    const addLayoutItem = () => {
      const newItem = {
        id: Date.now().toString(),
        name: `Item ${layoutItems.length + 1}`,
        x: Math.floor(Math.random() * 8) + 1,
        y: Math.floor(Math.random() * 4) + 1,
        width: 2,
        height: 1
      };
      setLayoutItems([...layoutItems, newItem]);
    };

    return (
      <ExampleCard
        title={
          <Space>
            <LayoutOutlined />
            Layout Designer Example
          </Space>
        }
        extra={
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={addLayoutItem}
          >
            Add Layout Item
          </Button>
        }
      >
        <FeatureHighlight>
          <Title level={4}>
            <BulbOutlined style={{ color: '#667eea', marginRight: 8 }} />
            What you'll learn:
          </Title>
          <ul>
            <li>How to create responsive grid layouts</li>
            <li>Drag and drop component positioning</li>
            <li>Layout tools for alignment and distribution</li>
            <li>Responsive breakpoint management</li>
          </ul>
        </FeatureHighlight>

        <Paragraph>
          The Layout Designer helps you create responsive layouts using a visual grid system. 
          Components can be dragged and positioned, and layouts adapt to different screen sizes.
        </Paragraph>

        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Layout Type:</Text>
            <Space style={{ marginLeft: 16 }}>
              {['grid', 'flex', 'stack', 'masonry'].map(type => (
                <Button
                  key={type}
                  type={layoutType === type ? 'primary' : 'default'}
                  size="small"
                  onClick={() => setLayoutType(type)}
                >
                  {type}
                </Button>
              ))}
            </Space>
          </div>
        </Space>

        <DemoArea>
          <div style={{ width: '100%' }}>
            <Title level={4}>Layout Preview ({layoutType})</Title>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(8, 1fr)',
              gap: '8px',
              background: 'white',
              padding: '16px',
              borderRadius: '8px',
              minHeight: '120px'
            }}>
              {layoutItems.map(item => (
                <div
                  key={item.id}
                  style={{
                    gridColumn: `${item.x} / span ${item.width}`,
                    gridRow: `${item.y} / span ${item.height}`,
                    background: '#667eea',
                    color: 'white',
                    padding: '8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {item.name}
                </div>
              ))}
            </div>
            {layoutItems.length === 0 && (
              <div style={{ textAlign: 'center', color: '#6c757d', padding: '40px' }}>
                <LayoutOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <Text>Click "Add Layout Item" to start building your layout</Text>
              </div>
            )}
          </div>
        </DemoArea>

        <Alert
          message="Layout Tools"
          description="Use the alignment tools to distribute items evenly, group related components, and manage z-index for layering effects."
          type="info"
          showIcon
        />
      </ExampleCard>
    );
  };

  // Theme Manager Example
  const ThemeManagerExample = () => {
    const [currentTheme, setCurrentTheme] = useState({
      name: 'Custom Theme',
      primaryColor: '#667eea',
      secondaryColor: '#764ba2',
      backgroundColor: '#ffffff',
      textColor: '#333333',
      fontFamily: 'Inter, sans-serif'
    });

    const applyTheme = (theme) => {
      setCurrentTheme(theme);
    };

    const predefinedThemes = [
      {
        name: 'Ocean Blue',
        primaryColor: '#0066cc',
        secondaryColor: '#00aaff',
        backgroundColor: '#f0f8ff',
        textColor: '#003366',
        fontFamily: 'Arial, sans-serif'
      },
      {
        name: 'Forest Green',
        primaryColor: '#228b22',
        secondaryColor: '#32cd32',
        backgroundColor: '#f0fff0',
        textColor: '#006400',
        fontFamily: 'Georgia, serif'
      },
      {
        name: 'Sunset Orange',
        primaryColor: '#ff6347',
        secondaryColor: '#ffa500',
        backgroundColor: '#fff8dc',
        textColor: '#8b4513',
        fontFamily: 'Verdana, sans-serif'
      }
    ];

    return (
      <ExampleCard
        title={
          <Space>
            <BgColorsOutlined />
            Theme Manager Example
          </Space>
        }
      >
        <FeatureHighlight>
          <Title level={4}>
            <BulbOutlined style={{ color: '#667eea', marginRight: 8 }} />
            What you'll learn:
          </Title>
          <ul>
            <li>How to create and customize themes</li>
            <li>Real-time theme preview</li>
            <li>Color palette management</li>
            <li>Theme import/export functionality</li>
          </ul>
        </FeatureHighlight>

        <Paragraph>
          The Theme Manager allows you to create consistent visual themes across your application. 
          Try applying different themes below to see how they affect the preview.
        </Paragraph>

        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Predefined Themes:</Text>
            <div style={{ marginTop: 8, display: 'flex', gap: 8, flexWrap: 'wrap' }}>
              {predefinedThemes.map((theme, index) => (
                <Button
                  key={index}
                  onClick={() => applyTheme(theme)}
                  style={{
                    background: theme.primaryColor,
                    borderColor: theme.primaryColor,
                    color: 'white'
                  }}
                >
                  {theme.name}
                </Button>
              ))}
            </div>
          </div>
        </Space>

        <DemoArea>
          <div 
            style={{
              width: '100%',
              background: currentTheme.backgroundColor,
              color: currentTheme.textColor,
              fontFamily: currentTheme.fontFamily,
              padding: '20px',
              borderRadius: '8px',
              border: '1px solid #dee2e6'
            }}
          >
            <Title level={3} style={{ color: currentTheme.textColor, margin: '0 0 16px 0' }}>
              {currentTheme.name}
            </Title>
            <Paragraph style={{ color: currentTheme.textColor }}>
              This is a preview of how your theme will look. The colors, typography, and spacing 
              are all applied according to your theme settings.
            </Paragraph>
            <Space>
              <Button 
                style={{ 
                  background: currentTheme.primaryColor, 
                  borderColor: currentTheme.primaryColor,
                  color: 'white'
                }}
              >
                Primary Button
              </Button>
              <Button 
                style={{ 
                  background: currentTheme.secondaryColor, 
                  borderColor: currentTheme.secondaryColor,
                  color: 'white'
                }}
              >
                Secondary Button
              </Button>
            </Space>
            <div style={{ marginTop: 16, display: 'flex', gap: 8 }}>
              <div style={{
                width: 20,
                height: 20,
                borderRadius: '50%',
                background: currentTheme.primaryColor,
                border: '1px solid #ccc'
              }} />
              <div style={{
                width: 20,
                height: 20,
                borderRadius: '50%',
                background: currentTheme.secondaryColor,
                border: '1px solid #ccc'
              }} />
              <div style={{
                width: 20,
                height: 20,
                borderRadius: '50%',
                background: currentTheme.backgroundColor,
                border: '1px solid #ccc'
              }} />
              <div style={{
                width: 20,
                height: 20,
                borderRadius: '50%',
                background: currentTheme.textColor,
                border: '1px solid #ccc'
              }} />
            </div>
          </div>
        </DemoArea>

        <Alert
          message="Theme Consistency"
          description="Themes ensure visual consistency across your entire application. Export themes to share with team members or import community themes."
          type="info"
          showIcon
        />
      </ExampleCard>
    );
  };

  return (
    <ExamplesContainer>
      <div style={{ textAlign: 'center', marginBottom: 32 }}>
        <Title level={1}>
          <RocketOutlined style={{ color: '#667eea', marginRight: 16 }} />
          App Builder Examples
        </Title>
        <Paragraph style={{ fontSize: 18, color: '#6c757d' }}>
          Learn how to use each feature with practical, interactive examples
        </Paragraph>
      </div>

      <Tabs activeKey={activeExample} onChange={setActiveExample} size="large">
        <TabPane 
          tab={
            <Space>
              <ComponentOutlined />
              Component Builder
            </Space>
          } 
          key="component-builder"
        >
          <ComponentBuilderExample />
        </TabPane>
        
        <TabPane 
          tab={
            <Space>
              <LayoutOutlined />
              Layout Designer
            </Space>
          } 
          key="layout-designer"
        >
          <LayoutDesignerExample />
        </TabPane>
        
        <TabPane 
          tab={
            <Space>
              <BgColorsOutlined />
              Theme Manager
            </Space>
          } 
          key="theme-manager"
        >
          <ThemeManagerExample />
        </TabPane>
      </Tabs>

      <Divider />

      <Card style={{ textAlign: 'center', background: 'linear-gradient(135deg, #667eea20 0%, #764ba220 100%)' }}>
        <Title level={3}>
          <ToolOutlined style={{ color: '#667eea', marginRight: 8 }} />
          Ready to Build?
        </Title>
        <Paragraph>
          Now that you've seen how each feature works, you're ready to start building your own application!
        </Paragraph>
        <Space>
          <Button type="primary" size="large" icon={<RocketOutlined />}>
            Start Building
          </Button>
          <Button size="large" icon={<CodeOutlined />}>
            View Documentation
          </Button>
        </Space>
      </Card>
    </ExamplesContainer>
  );
};

export default AppBuilderExamples;
