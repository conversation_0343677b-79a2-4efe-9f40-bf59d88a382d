import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import components and hooks to test
import EnhancedPreviewArea from '../components/builder/EnhancedPreviewArea';
import useRealTimePreview from '../hooks/useRealTimePreview';
import useResponsivePreview from '../hooks/useResponsivePreview';
import useCollaborativePreview from '../hooks/useCollaborativePreview';
import DevicePreviewFrame from '../components/preview/DevicePreviewFrame';

// Mock WebSocket
global.WebSocket = jest.fn(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  send: jest.fn(),
  close: jest.fn(),
  readyState: 1
}));

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 1024 * 1024 * 50 // 50MB
  }
};

// Mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: (state = { components: [] }, action) => state,
      websocket: (state = { connected: false }, action) => state,
      ui: (state = { currentView: 'components' }, action) => state
    },
    preloadedState: initialState
  });
};

// Mock components for testing
const mockComponents = [
  {
    id: '1',
    type: 'button',
    name: 'Test Button',
    props: { text: 'Click me', type: 'primary' },
    style: { margin: '10px' }
  },
  {
    id: '2',
    type: 'text',
    name: 'Test Text',
    props: { content: 'Hello World' },
    style: { fontSize: '16px' }
  }
];

describe('Enhanced Preview Functionality', () => {
  let store;
  let mockOnUpdate;
  let mockOnRealTimeUpdate;

  beforeEach(() => {
    store = createMockStore({
      app: { components: mockComponents },
      websocket: { connected: true }
    });
    mockOnUpdate = jest.fn();
    mockOnRealTimeUpdate = jest.fn();
    jest.clearAllMocks();
  });

  describe('EnhancedPreviewArea Component', () => {
    const renderPreviewArea = (props = {}) => {
      return render(
        <Provider store={store}>
          <EnhancedPreviewArea
            components={mockComponents}
            onUpdateComponent={mockOnUpdate}
            onRealTimeUpdate={mockOnRealTimeUpdate}
            realTimeUpdates={true}
            websocketConnected={true}
            {...props}
          />
        </Provider>
      );
    };

    test('renders preview area with components', () => {
      renderPreviewArea();

      expect(screen.getByText('Click me')).toBeInTheDocument();
      expect(screen.getByText('Hello World')).toBeInTheDocument();
    });

    test('displays device selector buttons', () => {
      renderPreviewArea();

      expect(screen.getByText('Mobile')).toBeInTheDocument();
      expect(screen.getByText('Tablet')).toBeInTheDocument();
      expect(screen.getByText('Desktop')).toBeInTheDocument();
    });

    test('switches between device types', async () => {
      renderPreviewArea();

      const mobileButton = screen.getByText('Mobile');
      fireEvent.click(mobileButton);

      await waitFor(() => {
        // Check if mobile-specific styles are applied
        const canvas = screen.getByRole('main', { hidden: true });
        expect(canvas).toHaveStyle('transform: scale(0.8)');
      });
    });

    test('shows real-time update indicators', () => {
      renderPreviewArea();

      expect(screen.getByText('Live')).toBeInTheDocument();
    });

    test('handles component selection', () => {
      const mockOnSelect = jest.fn();
      renderPreviewArea({ onSelectComponent: mockOnSelect });

      const button = screen.getByText('Click me');
      fireEvent.click(button);

      expect(mockOnSelect).toHaveBeenCalledWith(mockComponents[0]);
    });

    test('displays performance metrics in development mode', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      renderPreviewArea({ realTimeUpdates: true });

      // Should show performance overlay
      expect(screen.getByText(/Render:/)).toBeInTheDocument();
      expect(screen.getByText(/FPS:/)).toBeInTheDocument();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('DevicePreviewFrame Component', () => {
    test('renders device frame with controls', () => {
      const mockOnDeviceChange = jest.fn();

      render(
        <DevicePreviewFrame onDeviceChange={mockOnDeviceChange}>
          <div>Test Content</div>
        </DevicePreviewFrame>
      );

      expect(screen.getByText('Mobile')).toBeInTheDocument();
      expect(screen.getByText('Tablet')).toBeInTheDocument();
      expect(screen.getByText('Desktop')).toBeInTheDocument();
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    test('changes device variant', async () => {
      const mockOnDeviceChange = jest.fn();

      render(
        <DevicePreviewFrame onDeviceChange={mockOnDeviceChange}>
          <div>Test Content</div>
        </DevicePreviewFrame>
      );

      // Click mobile button
      fireEvent.click(screen.getByText('Mobile'));

      await waitFor(() => {
        expect(mockOnDeviceChange).toHaveBeenCalledWith(
          expect.objectContaining({
            device: 'mobile',
            variant: expect.any(String)
          })
        );
      });
    });

    test('toggles orientation for mobile devices', () => {
      render(
        <DevicePreviewFrame initialDevice="mobile">
          <div>Test Content</div>
        </DevicePreviewFrame>
      );

      const orientationButton = screen.getByRole('button', { name: /rotate/i });
      fireEvent.click(orientationButton);

      // Should toggle orientation
      expect(orientationButton).toBeInTheDocument();
    });
  });

  describe('Real-Time Preview Hook', () => {
    const TestComponent = ({ components, onUpdate }) => {
      const {
        isUpdating,
        updateComponent,
        getAllComponents
      } = useRealTimePreview({
        components,
        onUpdateComponent: onUpdate,
        enableWebSocket: true
      });

      return (
        <div>
          <div data-testid="updating">{isUpdating ? 'updating' : 'idle'}</div>
          <button
            onClick={() => updateComponent('1', { text: 'Updated' })}
            data-testid="update-button"
          >
            Update Component
          </button>
          <div data-testid="component-count">{getAllComponents().length}</div>
        </div>
      );
    };

    test('handles component updates', async () => {
      render(
        <TestComponent
          components={mockComponents}
          onUpdate={mockOnUpdate}
        />
      );

      const updateButton = screen.getByTestId('update-button');
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(screen.getByTestId('updating')).toHaveTextContent('updating');
      });

      // Should eventually call the update callback
      await waitFor(() => {
        expect(mockOnUpdate).toHaveBeenCalled();
      }, { timeout: 1000 });
    });
  });

  describe('Responsive Preview Hook', () => {
    const TestComponent = () => {
      const {
        currentDevice,
        handleDeviceChange,
        responsiveStyles,
        isDevice
      } = useResponsivePreview({
        initialDevice: 'desktop'
      });

      return (
        <div>
          <div data-testid="current-device">{currentDevice}</div>
          <div data-testid="is-mobile">{isDevice('mobile') ? 'true' : 'false'}</div>
          <button
            onClick={() => handleDeviceChange('mobile')}
            data-testid="change-to-mobile"
          >
            Switch to Mobile
          </button>
          <div
            data-testid="responsive-styles"
            style={responsiveStyles}
          >
            Styled Content
          </div>
        </div>
      );
    };

    test('manages device state', () => {
      render(<TestComponent />);

      expect(screen.getByTestId('current-device')).toHaveTextContent('desktop');
      expect(screen.getByTestId('is-mobile')).toHaveTextContent('false');
    });

    test('switches device types', async () => {
      render(<TestComponent />);

      const switchButton = screen.getByTestId('change-to-mobile');
      fireEvent.click(switchButton);

      await waitFor(() => {
        expect(screen.getByTestId('current-device')).toHaveTextContent('mobile');
        expect(screen.getByTestId('is-mobile')).toHaveTextContent('true');
      });
    });

    test('applies responsive styles', () => {
      render(<TestComponent />);

      const styledElement = screen.getByTestId('responsive-styles');
      expect(styledElement).toHaveStyle('font-size: 16px'); // Desktop default
    });
  });

  describe('Collaborative Preview Hook', () => {
    const TestComponent = () => {
      const {
        isConnected,
        collaborators,
        sendComponentUpdate
      } = useCollaborativePreview({
        sessionId: 'test-session',
        userId: 'test-user',
        enableCollaboration: true
      });

      return (
        <div>
          <div data-testid="connection-status">{isConnected ? 'connected' : 'disconnected'}</div>
          <div data-testid="collaborator-count">{collaborators.length}</div>
          <button
            onClick={() => sendComponentUpdate('1', { updated: true })}
            data-testid="send-update"
          >
            Send Update
          </button>
        </div>
      );
    };

    test('manages collaboration state', () => {
      render(<TestComponent />);

      expect(screen.getByTestId('connection-status')).toHaveTextContent('disconnected');
      expect(screen.getByTestId('collaborator-count')).toHaveTextContent('0');
    });

    test('sends component updates', () => {
      render(<TestComponent />);

      const sendButton = screen.getByTestId('send-update');
      fireEvent.click(sendButton);

      // Should not throw error
      expect(sendButton).toBeInTheDocument();
    });
  });

  describe('Performance Optimization', () => {
    test('handles large component lists efficiently', async () => {
      const largeComponentList = Array.from({ length: 100 }, (_, i) => ({
        id: `component-${i}`,
        type: 'text',
        name: `Component ${i}`,
        props: { content: `Content ${i}` }
      }));

      const startTime = performance.now();

      render(
        <Provider store={createMockStore({ app: { components: largeComponentList } })}>
          <EnhancedPreviewArea
            components={largeComponentList}
            onUpdateComponent={mockOnUpdate}
            realTimeUpdates={true}
          />
        </Provider>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (less than 100ms)
      expect(renderTime).toBeLessThan(100);
    });

    test('debounces rapid updates', async () => {
      const TestComponent = () => {
        const { updateComponent } = useRealTimePreview({
          components: mockComponents,
          onUpdateComponent: mockOnUpdate,
          updateDelay: 100
        });

        return (
          <button
            onClick={() => {
              // Rapid fire updates
              updateComponent('1', { text: 'Update 1' });
              updateComponent('1', { text: 'Update 2' });
              updateComponent('1', { text: 'Update 3' });
            }}
            data-testid="rapid-update"
          >
            Rapid Update
          </button>
        );
      };

      render(<TestComponent />);

      const rapidButton = screen.getByTestId('rapid-update');
      fireEvent.click(rapidButton);

      // Wait for debounce
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 150));
      });

      // Should only call update once due to debouncing
      expect(mockOnUpdate).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Handling', () => {
    test('handles WebSocket connection errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // Mock WebSocket constructor to throw
      global.WebSocket = jest.fn(() => {
        throw new Error('Connection failed');
      });

      const TestComponent = () => {
        useCollaborativePreview({
          sessionId: 'test-session',
          userId: 'test-user',
          enableCollaboration: true
        });

        return <div>Test</div>;
      };

      expect(() => render(<TestComponent />)).not.toThrow();

      consoleSpy.mockRestore();
    });

    test('handles invalid component data', () => {
      const invalidComponents = [
        { id: null, type: 'button' }, // Invalid ID
        { id: '2', type: null }, // Invalid type
        null // Null component
      ];

      expect(() => {
        render(
          <Provider store={createMockStore()}>
            <EnhancedPreviewArea
              components={invalidComponents}
              onUpdateComponent={mockOnUpdate}
            />
          </Provider>
        );
      }).not.toThrow();
    });
  });

  describe('Integration Tests', () => {
    test('complete preview workflow', async () => {
      const IntegratedTestComponent = () => {
        const [components, setComponents] = React.useState(mockComponents);
        const [device, setDevice] = React.useState('desktop');

        const handleUpdate = (updatedComponent) => {
          setComponents(prev =>
            prev.map(comp =>
              comp.id === updatedComponent.id ? updatedComponent : comp
            )
          );
        };

        return (
          <Provider store={store}>
            <div>
              <DevicePreviewFrame
                initialDevice={device}
                onDeviceChange={(config) => setDevice(config.device)}
              >
                <EnhancedPreviewArea
                  components={components}
                  onUpdateComponent={handleUpdate}
                  realTimeUpdates={true}
                  websocketConnected={true}
                />
              </DevicePreviewFrame>
            </div>
          </Provider>
        );
      };

      render(<IntegratedTestComponent />);

      // Test device switching
      fireEvent.click(screen.getByText('Mobile'));
      await waitFor(() => {
        expect(screen.getByText('Mobile')).toBeInTheDocument();
      });

      // Test component interaction
      const button = screen.getByText('Click me');
      fireEvent.click(button);

      // Should not throw errors
      expect(button).toBeInTheDocument();
    });

    test('real-time collaboration simulation', async () => {
      const CollaborationTestComponent = () => {
        const [components, setComponents] = React.useState(mockComponents);

        const { sendComponentUpdate } = useCollaborativePreview({
          sessionId: 'test-session',
          userId: 'user1',
          enableCollaboration: true
        });

        const { updateComponent } = useRealTimePreview({
          components,
          onUpdateComponent: (updated) => {
            setComponents(prev =>
              prev.map(comp =>
                comp.id === updated.id ? updated : comp
              )
            );
          },
          enableWebSocket: true
        });

        return (
          <div>
            <button
              onClick={() => {
                const updated = { ...mockComponents[0], props: { text: 'Collaborative Update' } };
                updateComponent(updated.id, updated);
                sendComponentUpdate(updated.id, updated);
              }}
              data-testid="collab-update"
            >
              Collaborative Update
            </button>
            <div data-testid="component-text">
              {components.find(c => c.id === '1')?.props?.text || 'Click me'}
            </div>
          </div>
        );
      };

      render(<CollaborationTestComponent />);

      const updateButton = screen.getByTestId('collab-update');
      fireEvent.click(updateButton);

      // Should handle collaborative updates
      expect(updateButton).toBeInTheDocument();
    });
  });
});
