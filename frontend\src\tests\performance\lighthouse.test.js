/**
 * Lighthouse performance tests
 * Measures Core Web Vitals and performance metrics
 */

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  performance: 90,
  accessibility: 95,
  bestPractices: 90,
  seo: 90,
  pwa: 80,
  // Core Web Vitals
  firstContentfulPaint: 1800, // 1.8s
  largestContentfulPaint: 2500, // 2.5s
  cumulativeLayoutShift: 0.1,
  firstInputDelay: 100, // 100ms
  speedIndex: 3000, // 3s
  timeToInteractive: 3800, // 3.8s
};

// Test URLs
const TEST_URLS = [
  {
    name: 'Homepage',
    url: process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000',
    description: 'Main application homepage'
  },
  {
    name: 'App Builder',
    url: (process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000') + '/builder',
    description: 'App builder interface'
  }
];

describe('Lighthouse Performance Tests', () => {
  let chrome;
  let results = [];

  beforeAll(async () => {
    // Launch Chrome
    chrome = await chromeLauncher.launch({
      chromeFlags: [
        '--headless',
        '--disable-gpu',
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-extensions',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    });

    console.log(`Chrome launched on port ${chrome.port}`);
  });

  afterAll(async () => {
    if (chrome) {
      await chrome.kill();
    }

    // Generate comprehensive report
    if (results.length > 0) {
      await generatePerformanceReport(results);
    }
  });

  // Test each URL
  TEST_URLS.forEach(testCase => {
    describe(`${testCase.name} Performance`, () => {
      let lighthouseResult;

      beforeAll(async () => {
        console.log(`Running Lighthouse audit for ${testCase.name}...`);
        
        const options = {
          logLevel: 'info',
          output: 'json',
          onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
          port: chrome.port,
          throttling: {
            rttMs: 40,
            throughputKbps: 10240,
            cpuSlowdownMultiplier: 1,
            requestLatencyMs: 0,
            downloadThroughputKbps: 0,
            uploadThroughputKbps: 0
          },
          screenEmulation: {
            mobile: false,
            width: 1350,
            height: 940,
            deviceScaleFactor: 1,
            disabled: false,
          },
          emulatedFormFactor: 'desktop'
        };

        try {
          lighthouseResult = await lighthouse(testCase.url, options);
          results.push({
            name: testCase.name,
            url: testCase.url,
            result: lighthouseResult
          });
        } catch (error) {
          console.error(`Lighthouse audit failed for ${testCase.name}:`, error);
          throw error;
        }
      });

      test('meets performance score threshold', () => {
        const performanceScore = lighthouseResult.lhr.categories.performance.score * 100;
        
        expect(performanceScore).toBeGreaterThanOrEqual(PERFORMANCE_THRESHOLDS.performance);
        
        console.log(`${testCase.name} Performance Score: ${performanceScore.toFixed(1)}/100`);
      });

      test('meets accessibility score threshold', () => {
        const accessibilityScore = lighthouseResult.lhr.categories.accessibility.score * 100;
        
        expect(accessibilityScore).toBeGreaterThanOrEqual(PERFORMANCE_THRESHOLDS.accessibility);
        
        console.log(`${testCase.name} Accessibility Score: ${accessibilityScore.toFixed(1)}/100`);
      });

      test('meets best practices score threshold', () => {
        const bestPracticesScore = lighthouseResult.lhr.categories['best-practices'].score * 100;
        
        expect(bestPracticesScore).toBeGreaterThanOrEqual(PERFORMANCE_THRESHOLDS.bestPractices);
        
        console.log(`${testCase.name} Best Practices Score: ${bestPracticesScore.toFixed(1)}/100`);
      });

      test('meets SEO score threshold', () => {
        const seoScore = lighthouseResult.lhr.categories.seo.score * 100;
        
        expect(seoScore).toBeGreaterThanOrEqual(PERFORMANCE_THRESHOLDS.seo);
        
        console.log(`${testCase.name} SEO Score: ${seoScore.toFixed(1)}/100`);
      });

      test('First Contentful Paint is within threshold', () => {
        const fcp = lighthouseResult.lhr.audits['first-contentful-paint'].numericValue;
        
        expect(fcp).toBeLessThanOrEqual(PERFORMANCE_THRESHOLDS.firstContentfulPaint);
        
        console.log(`${testCase.name} First Contentful Paint: ${fcp.toFixed(0)}ms`);
      });

      test('Largest Contentful Paint is within threshold', () => {
        const lcp = lighthouseResult.lhr.audits['largest-contentful-paint'].numericValue;
        
        expect(lcp).toBeLessThanOrEqual(PERFORMANCE_THRESHOLDS.largestContentfulPaint);
        
        console.log(`${testCase.name} Largest Contentful Paint: ${lcp.toFixed(0)}ms`);
      });

      test('Cumulative Layout Shift is within threshold', () => {
        const cls = lighthouseResult.lhr.audits['cumulative-layout-shift'].numericValue;
        
        expect(cls).toBeLessThanOrEqual(PERFORMANCE_THRESHOLDS.cumulativeLayoutShift);
        
        console.log(`${testCase.name} Cumulative Layout Shift: ${cls.toFixed(3)}`);
      });

      test('Speed Index is within threshold', () => {
        const speedIndex = lighthouseResult.lhr.audits['speed-index'].numericValue;
        
        expect(speedIndex).toBeLessThanOrEqual(PERFORMANCE_THRESHOLDS.speedIndex);
        
        console.log(`${testCase.name} Speed Index: ${speedIndex.toFixed(0)}ms`);
      });

      test('Time to Interactive is within threshold', () => {
        const tti = lighthouseResult.lhr.audits['interactive'].numericValue;
        
        expect(tti).toBeLessThanOrEqual(PERFORMANCE_THRESHOLDS.timeToInteractive);
        
        console.log(`${testCase.name} Time to Interactive: ${tti.toFixed(0)}ms`);
      });

      test('has no critical performance issues', () => {
        const audits = lighthouseResult.lhr.audits;
        const criticalIssues = [];

        // Check for critical performance audits
        const criticalAudits = [
          'render-blocking-resources',
          'unused-css-rules',
          'unused-javascript',
          'efficient-animated-content',
          'total-byte-weight'
        ];

        criticalAudits.forEach(auditId => {
          const audit = audits[auditId];
          if (audit && audit.score !== null && audit.score < 0.9) {
            criticalIssues.push({
              id: auditId,
              title: audit.title,
              score: audit.score,
              description: audit.description
            });
          }
        });

        if (criticalIssues.length > 0) {
          console.warn(`${testCase.name} Critical Performance Issues:`, criticalIssues);
          // Allow some issues but warn about them
          expect(criticalIssues.length).toBeLessThan(3);
        }
      });

      test('images are optimized', () => {
        const audits = lighthouseResult.lhr.audits;
        const imageAudits = [
          'uses-optimized-images',
          'uses-webp-images',
          'uses-responsive-images',
          'image-size-responsive'
        ];

        imageAudits.forEach(auditId => {
          const audit = audits[auditId];
          if (audit && audit.score !== null) {
            expect(audit.score).toBeGreaterThan(0.8);
          }
        });
      });

      test('uses modern JavaScript features appropriately', () => {
        const audits = lighthouseResult.lhr.audits;
        const jsAudits = [
          'uses-text-compression',
          'legacy-javascript'
        ];

        jsAudits.forEach(auditId => {
          const audit = audits[auditId];
          if (audit && audit.score !== null) {
            expect(audit.score).toBeGreaterThan(0.8);
          }
        });
      });

      test('has proper caching strategy', () => {
        const audits = lighthouseResult.lhr.audits;
        const cachingAudits = [
          'uses-long-cache-ttl',
          'uses-text-compression'
        ];

        cachingAudits.forEach(auditId => {
          const audit = audits[auditId];
          if (audit && audit.score !== null) {
            // Allow some flexibility for caching
            expect(audit.score).toBeGreaterThan(0.6);
          }
        });
      });
    });
  });

  test('performance regression detection', async () => {
    // Compare with previous results if available
    const previousResultsPath = path.join(process.cwd(), 'test-results', 'lighthouse-baseline.json');
    
    if (fs.existsSync(previousResultsPath)) {
      const previousResults = JSON.parse(fs.readFileSync(previousResultsPath, 'utf8'));
      
      results.forEach(currentResult => {
        const previousResult = previousResults.find(r => r.name === currentResult.name);
        
        if (previousResult) {
          const currentPerf = currentResult.result.lhr.categories.performance.score * 100;
          const previousPerf = previousResult.performance;
          
          const regression = previousPerf - currentPerf;
          
          // Allow up to 5 point regression
          if (regression > 5) {
            console.warn(`Performance regression detected for ${currentResult.name}: ${regression.toFixed(1)} points`);
          }
          
          expect(regression).toBeLessThan(10); // Fail if regression is more than 10 points
        }
      });
    }
  });
});

/**
 * Generate comprehensive performance report
 */
async function generatePerformanceReport(results) {
  const reportData = {
    timestamp: new Date().toISOString(),
    thresholds: PERFORMANCE_THRESHOLDS,
    results: results.map(result => {
      const lhr = result.result.lhr;
      
      return {
        name: result.name,
        url: result.url,
        performance: Math.round(lhr.categories.performance.score * 100),
        accessibility: Math.round(lhr.categories.accessibility.score * 100),
        bestPractices: Math.round(lhr.categories['best-practices'].score * 100),
        seo: Math.round(lhr.categories.seo.score * 100),
        metrics: {
          firstContentfulPaint: Math.round(lhr.audits['first-contentful-paint'].numericValue),
          largestContentfulPaint: Math.round(lhr.audits['largest-contentful-paint'].numericValue),
          cumulativeLayoutShift: parseFloat(lhr.audits['cumulative-layout-shift'].numericValue.toFixed(3)),
          speedIndex: Math.round(lhr.audits['speed-index'].numericValue),
          timeToInteractive: Math.round(lhr.audits['interactive'].numericValue),
        },
        opportunities: lhr.audits ? Object.keys(lhr.audits)
          .filter(key => lhr.audits[key].details && lhr.audits[key].details.type === 'opportunity')
          .map(key => ({
            id: key,
            title: lhr.audits[key].title,
            description: lhr.audits[key].description,
            score: lhr.audits[key].score,
            savings: lhr.audits[key].details.overallSavingsMs || 0
          }))
          .filter(opp => opp.savings > 100) // Only significant opportunities
          .sort((a, b) => b.savings - a.savings) : []
      };
    }),
    summary: {
      averagePerformance: Math.round(
        results.reduce((sum, r) => sum + r.result.lhr.categories.performance.score * 100, 0) / results.length
      ),
      averageAccessibility: Math.round(
        results.reduce((sum, r) => sum + r.result.lhr.categories.accessibility.score * 100, 0) / results.length
      ),
      totalOpportunities: results.reduce((sum, r) => {
        const opportunities = r.result.lhr.audits ? Object.keys(r.result.lhr.audits)
          .filter(key => r.result.lhr.audits[key].details && r.result.lhr.audits[key].details.type === 'opportunity')
          .length : 0;
        return sum + opportunities;
      }, 0)
    }
  };

  // Save detailed report
  const reportsDir = path.join(process.cwd(), 'test-results');
  fs.mkdirSync(reportsDir, { recursive: true });
  
  const reportPath = path.join(reportsDir, 'lighthouse-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  
  // Save as baseline for future comparisons
  const baselinePath = path.join(reportsDir, 'lighthouse-baseline.json');
  const baselineData = reportData.results.map(r => ({
    name: r.name,
    performance: r.performance,
    accessibility: r.accessibility,
    bestPractices: r.bestPractices,
    seo: r.seo,
    metrics: r.metrics
  }));
  fs.writeFileSync(baselinePath, JSON.stringify(baselineData, null, 2));

  // Generate HTML report
  const htmlReport = generateHtmlReport(reportData);
  const htmlPath = path.join(reportsDir, 'lighthouse-report.html');
  fs.writeFileSync(htmlPath, htmlReport);

  console.log(`Lighthouse reports generated:`);
  console.log(`- JSON: ${reportPath}`);
  console.log(`- HTML: ${htmlPath}`);
  console.log(`- Baseline: ${baselinePath}`);
}

/**
 * Generate HTML report
 */
function generateHtmlReport(reportData) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lighthouse Performance Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 30px; }
        .metric { background: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; }
        .metric.good { border-left: 4px solid #28a745; }
        .metric.needs-improvement { border-left: 4px solid #ffc107; }
        .metric.poor { border-left: 4px solid #dc3545; }
        .score { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .results { padding: 30px; }
        .result-card { background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .opportunities { margin-top: 20px; }
        .opportunity { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Lighthouse Performance Report</h1>
            <p>Generated on ${new Date(reportData.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="metrics">
            <div class="metric ${reportData.summary.averagePerformance >= 90 ? 'good' : reportData.summary.averagePerformance >= 70 ? 'needs-improvement' : 'poor'}">
                <div class="score">${reportData.summary.averagePerformance}</div>
                <div>Average Performance</div>
            </div>
            <div class="metric ${reportData.summary.averageAccessibility >= 95 ? 'good' : reportData.summary.averageAccessibility >= 80 ? 'needs-improvement' : 'poor'}">
                <div class="score">${reportData.summary.averageAccessibility}</div>
                <div>Average Accessibility</div>
            </div>
            <div class="metric">
                <div class="score">${reportData.summary.totalOpportunities}</div>
                <div>Total Opportunities</div>
            </div>
        </div>

        <div class="results">
            <h2>Detailed Results</h2>
            ${reportData.results.map(result => `
                <div class="result-card">
                    <h3>${result.name}</h3>
                    <p><strong>URL:</strong> ${result.url}</p>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin: 15px 0;">
                        <div>Performance: <strong>${result.performance}</strong></div>
                        <div>Accessibility: <strong>${result.accessibility}</strong></div>
                        <div>Best Practices: <strong>${result.bestPractices}</strong></div>
                        <div>SEO: <strong>${result.seo}</strong></div>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin: 15px 0;">
                        <div>FCP: <strong>${result.metrics.firstContentfulPaint}ms</strong></div>
                        <div>LCP: <strong>${result.metrics.largestContentfulPaint}ms</strong></div>
                        <div>CLS: <strong>${result.metrics.cumulativeLayoutShift}</strong></div>
                    </div>
                    ${result.opportunities.length > 0 ? `
                        <div class="opportunities">
                            <h4>Top Opportunities</h4>
                            ${result.opportunities.slice(0, 3).map(opp => `
                                <div class="opportunity">
                                    <strong>${opp.title}</strong> - Potential savings: ${opp.savings}ms
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>
  `;
}
