import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { Link, useParams, useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

/**
 * ResetPasswordPage component
 * Allows users to reset their password using a token
 */
const ResetPasswordPage = () => {
  const [loading, setLoading] = useState(false);
  const [tokenValid, setTokenValid] = useState(true);
  const [form] = Form.useForm();
  const { token } = useParams();
  const navigate = useNavigate();

  // Validate token on component mount
  useEffect(() => {
    const validateToken = async () => {
      try {
        // Simulate API call to validate token
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // For demo purposes, consider token valid if it exists and has length > 10
        if (!token || token.length < 10) {
          setTokenValid(false);
          message.error('Invalid or expired password reset link');
        }
      } catch (error) {
        setTokenValid(false);
        message.error('Failed to validate reset token');
        console.error('Error validating token:', error);
      }
    };

    validateToken();
  }, [token]);

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('Password has been reset successfully');
      navigate('/login');
    } catch (error) {
      message.error('Failed to reset password');
      console.error('Error resetting password:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!tokenValid) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        padding: '20px'
      }}>
        <Card style={{ width: '100%', maxWidth: 400, textAlign: 'center' }}>
          <Title level={3}>Invalid Reset Link</Title>
          <Text type="secondary">
            The password reset link is invalid or has expired.
          </Text>
          <div style={{ marginTop: 24 }}>
            <Link to="/forgot-password">
              <Button type="primary">Request New Link</Button>
            </Link>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      padding: '20px'
    }}>
      <Card style={{ width: '100%', maxWidth: 400 }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3}>Reset Password</Title>
          <Text type="secondary">
            Enter your new password below
          </Text>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="password"
            rules={[
              { required: true, message: 'Please enter your new password' },
              { min: 8, message: 'Password must be at least 8 characters' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="New Password" 
              size="large" 
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              { required: true, message: 'Please confirm your password' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('The two passwords do not match'));
                },
              }),
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="Confirm Password" 
              size="large" 
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              block
              size="large"
            >
              Reset Password
            </Button>
          </Form.Item>

          <div style={{ textAlign: 'center' }}>
            <Link to="/login">Back to Login</Link>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default ResetPasswordPage;
