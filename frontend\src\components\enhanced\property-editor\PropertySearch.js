import React, { useState, useMemo } from 'react';
import { Input, Select, Space, Typography, Tag } from 'antd';
import { SearchOutlined, FilterOutlined, ClearOutlined } from '@ant-design/icons';
import { styled } from '../../../design-system';

const { Text } = Typography;
const { Option } = Select;

const SearchContainer = styled.div`
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
`;

const FilterRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
`;

const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
`;

/**
 * Property search and filter component
 */
const PropertySearch = ({
  properties = {},
  onFilter,
  showGroupFilter = true,
  showTypeFilter = true,
  placeholder = 'Search properties...',
  ...props
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGroup, setSelectedGroup] = useState('all');
  const [selectedType, setSelectedType] = useState('all');

  // Extract unique groups and types from properties
  const { groups, types } = useMemo(() => {
    const groupSet = new Set();
    const typeSet = new Set();

    Object.values(properties).forEach(property => {
      if (property.group) {
        groupSet.add(property.group);
      }
      if (property.type) {
        typeSet.add(property.type);
      }
    });

    return {
      groups: Array.from(groupSet).sort(),
      types: Array.from(typeSet).sort()
    };
  }, [properties]);

  // Filter properties based on search term, group, and type
  const filteredProperties = useMemo(() => {
    const filtered = {};

    Object.entries(properties).forEach(([key, property]) => {
      const matchesSearch = !searchTerm || 
        key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (property.label && property.label.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (property.description && property.description.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesGroup = selectedGroup === 'all' || property.group === selectedGroup;
      const matchesType = selectedType === 'all' || property.type === selectedType;

      if (matchesSearch && matchesGroup && matchesType) {
        filtered[key] = property;
      }
    });

    return filtered;
  }, [properties, searchTerm, selectedGroup, selectedType]);

  // Handle filter changes
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    onFilter?.(filteredProperties, { searchTerm: value, group: selectedGroup, type: selectedType });
  };

  const handleGroupChange = (value) => {
    setSelectedGroup(value);
    onFilter?.(filteredProperties, { searchTerm, group: value, type: selectedType });
  };

  const handleTypeChange = (value) => {
    setSelectedType(value);
    onFilter?.(filteredProperties, { searchTerm, group: selectedGroup, type: value });
  };

  const handleClear = () => {
    setSearchTerm('');
    setSelectedGroup('all');
    setSelectedType('all');
    onFilter?.(properties, { searchTerm: '', group: 'all', type: 'all' });
  };

  // Get active filter count
  const activeFilters = [
    searchTerm && 'search',
    selectedGroup !== 'all' && 'group',
    selectedType !== 'all' && 'type'
  ].filter(Boolean);

  // Format group and type names for display
  const formatName = (name) => {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
  };

  return (
    <SearchContainer>
      <FilterRow>
        <Input
          prefix={<SearchOutlined />}
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleSearchChange}
          allowClear
          style={{ flex: 1 }}
        />
        
        {showGroupFilter && groups.length > 0 && (
          <Select
            value={selectedGroup}
            onChange={handleGroupChange}
            style={{ minWidth: 120 }}
            size="small"
          >
            <Option value="all">All Groups</Option>
            {groups.map(group => (
              <Option key={group} value={group}>
                {formatName(group)}
              </Option>
            ))}
          </Select>
        )}
        
        {showTypeFilter && types.length > 0 && (
          <Select
            value={selectedType}
            onChange={handleTypeChange}
            style={{ minWidth: 100 }}
            size="small"
          >
            <Option value="all">All Types</Option>
            {types.map(type => (
              <Option key={type} value={type}>
                {formatName(type)}
              </Option>
            ))}
          </Select>
        )}
        
        {activeFilters.length > 0 && (
          <ClearOutlined
            onClick={handleClear}
            style={{ cursor: 'pointer', color: '#8c8c8c' }}
            title="Clear all filters"
          />
        )}
      </FilterRow>

      {activeFilters.length > 0 && (
        <div>
          <Space size={4} style={{ marginBottom: 4 }}>
            <FilterOutlined style={{ fontSize: '12px', color: '#8c8c8c' }} />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Active filters:
            </Text>
          </Space>
          
          <TagContainer>
            {searchTerm && (
              <Tag
                closable
                onClose={() => {
                  setSearchTerm('');
                  onFilter?.(filteredProperties, { searchTerm: '', group: selectedGroup, type: selectedType });
                }}
                size="small"
              >
                Search: "{searchTerm}"
              </Tag>
            )}
            
            {selectedGroup !== 'all' && (
              <Tag
                closable
                onClose={() => {
                  setSelectedGroup('all');
                  onFilter?.(filteredProperties, { searchTerm, group: 'all', type: selectedType });
                }}
                size="small"
              >
                Group: {formatName(selectedGroup)}
              </Tag>
            )}
            
            {selectedType !== 'all' && (
              <Tag
                closable
                onClose={() => {
                  setSelectedType('all');
                  onFilter?.(filteredProperties, { searchTerm, group: selectedGroup, type: 'all' });
                }}
                size="small"
              >
                Type: {formatName(selectedType)}
              </Tag>
            )}
          </TagContainer>
        </div>
      )}

      <Space style={{ marginTop: 8, fontSize: '12px' }}>
        <Text type="secondary">
          Showing {Object.keys(filteredProperties).length} of {Object.keys(properties).length} properties
        </Text>
      </Space>
    </SearchContainer>
  );
};

export default PropertySearch;
