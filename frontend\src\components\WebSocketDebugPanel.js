import React, { useState, useEffect, useRef } from 'react';
import WebSocketService from '../services/WebSocketService';

/**
 * WebSocket Debug Panel
 * 
 * A developer tool for debugging WebSocket connections.
 * Shows connection status, message history, and allows sending test messages.
 */
const WebSocketDebugPanel = () => {
  // State
  const [visible, setVisible] = useState(false);
  const [connected, setConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [customMessage, setCustomMessage] = useState('');
  const [filter, setFilter] = useState('all');
  const [expanded, setExpanded] = useState(false);
  const [stats, setStats] = useState({
    sent: 0,
    received: 0,
    errors: 0,
    reconnects: 0,
    lastLatency: null
  });
  
  // Refs
  const messagesEndRef = useRef(null);
  const lastPingRef = useRef(null);
  
  // Initialize WebSocket service
  useEffect(() => {
    const wsService = WebSocketService.getInstance();
    
    // Set initial state
    setConnected(wsService.isConnected());
    
    // Add event listeners
    const handleOpen = () => {
      setConnected(true);
      addMessage('system', 'Connected to WebSocket server');
      setStats(prev => ({ ...prev, reconnects: prev.reconnects + 1 }));
    };
    
    const handleClose = (event) => {
      setConnected(false);
      addMessage('system', `Disconnected from WebSocket server: ${event.code} ${event.reason || ''}`);
    };
    
    const handleError = (error) => {
      addMessage('error', `WebSocket error: ${error.message || 'Unknown error'}`);
      setStats(prev => ({ ...prev, errors: prev.errors + 1 }));
    };
    
    const handleMessage = (data) => {
      // Handle pong messages for latency calculation
      if (data && data.type === 'pong' && lastPingRef.current) {
        const latency = Date.now() - lastPingRef.current;
        setStats(prev => ({ ...prev, lastLatency: latency }));
        lastPingRef.current = null;
      }
      
      addMessage('received', data);
      setStats(prev => ({ ...prev, received: prev.received + 1 }));
    };
    
    // Add event listeners
    wsService.addEventListener('open', handleOpen);
    wsService.addEventListener('close', handleClose);
    wsService.addEventListener('error', handleError);
    wsService.addEventListener('message', handleMessage);
    
    // Clean up on unmount
    return () => {
      wsService.removeEventListener('open', handleOpen);
      wsService.removeEventListener('close', handleClose);
      wsService.removeEventListener('error', handleError);
      wsService.removeEventListener('message', handleMessage);
    };
  }, []);
  
  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Add a message to the list
  const addMessage = (type, content) => {
    const message = {
      id: Date.now(),
      type,
      content,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev.slice(-99), message]);
  };
  
  // Send a ping message
  const sendPing = () => {
    if (!connected) return;
    
    const wsService = WebSocketService.getInstance();
    const timestamp = Date.now();
    lastPingRef.current = timestamp;
    
    wsService.sendMessage({ type: 'ping', timestamp })
      .then(() => {
        addMessage('sent', { type: 'ping', timestamp });
        setStats(prev => ({ ...prev, sent: prev.sent + 1 }));
      })
      .catch(error => {
        addMessage('error', `Failed to send ping: ${error.message}`);
        setStats(prev => ({ ...prev, errors: prev.errors + 1 }));
      });
  };
  
  // Send a custom message
  const sendCustomMessage = () => {
    if (!connected || !customMessage.trim()) return;
    
    try {
      const wsService = WebSocketService.getInstance();
      const message = JSON.parse(customMessage);
      
      wsService.sendMessage(message)
        .then(() => {
          addMessage('sent', message);
          setStats(prev => ({ ...prev, sent: prev.sent + 1 }));
          setCustomMessage('');
        })
        .catch(error => {
          addMessage('error', `Failed to send message: ${error.message}`);
          setStats(prev => ({ ...prev, errors: prev.errors + 1 }));
        });
    } catch (error) {
      addMessage('error', `Invalid JSON: ${error.message}`);
    }
  };
  
  // Connect to WebSocket
  const connect = () => {
    if (connected) return;
    
    const wsService = WebSocketService.getInstance();
    wsService.connect()
      .catch(error => {
        addMessage('error', `Failed to connect: ${error.message}`);
        setStats(prev => ({ ...prev, errors: prev.errors + 1 }));
      });
  };
  
  // Disconnect from WebSocket
  const disconnect = () => {
    if (!connected) return;
    
    const wsService = WebSocketService.getInstance();
    wsService.close(1000, 'User initiated disconnect');
  };
  
  // Clear messages
  const clearMessages = () => {
    setMessages([]);
  };
  
  // Filter messages
  const filteredMessages = messages.filter(message => {
    if (filter === 'all') return true;
    return message.type === filter;
  });
  
  // Toggle visibility
  const toggleVisibility = () => {
    setVisible(!visible);
  };
  
  // Toggle expanded view
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };
  
  // Format message content
  const formatContent = (content) => {
    if (typeof content === 'string') {
      return content;
    }
    
    try {
      return JSON.stringify(content, null, 2);
    } catch (error) {
      return String(content);
    }
  };
  
  // Styles
  const styles = {
    container: {
      position: 'fixed',
      bottom: visible ? '0' : '-400px',
      right: '20px',
      width: expanded ? '600px' : '400px',
      height: expanded ? '500px' : '300px',
      backgroundColor: '#fff',
      border: '1px solid #d9d9d9',
      borderRadius: '4px 4px 0 0',
      boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.15)',
      transition: 'all 0.3s ease',
      zIndex: 1000,
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    },
    header: {
      padding: '8px 12px',
      borderBottom: '1px solid #d9d9d9',
      backgroundColor: '#f5f5f5',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    title: {
      margin: 0,
      fontSize: '14px',
      fontWeight: 'bold'
    },
    controls: {
      display: 'flex',
      gap: '8px'
    },
    button: {
      padding: '4px 8px',
      fontSize: '12px',
      border: '1px solid #d9d9d9',
      borderRadius: '2px',
      backgroundColor: '#fff',
      cursor: 'pointer'
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    },
    messagesContainer: {
      flex: 1,
      overflowY: 'auto',
      padding: '8px',
      backgroundColor: '#fafafa'
    },
    message: {
      padding: '4px 8px',
      marginBottom: '4px',
      borderRadius: '2px',
      fontSize: '12px',
      fontFamily: 'monospace',
      whiteSpace: 'pre-wrap',
      wordBreak: 'break-word'
    },
    inputContainer: {
      padding: '8px',
      borderTop: '1px solid #d9d9d9',
      display: 'flex',
      gap: '8px'
    },
    input: {
      flex: 1,
      padding: '4px 8px',
      fontSize: '12px',
      border: '1px solid #d9d9d9',
      borderRadius: '2px'
    },
    footer: {
      padding: '4px 8px',
      borderTop: '1px solid #d9d9d9',
      backgroundColor: '#f5f5f5',
      fontSize: '11px',
      display: 'flex',
      justifyContent: 'space-between'
    },
    stats: {
      display: 'flex',
      gap: '8px'
    },
    statItem: {
      display: 'flex',
      alignItems: 'center',
      gap: '4px'
    },
    toggleButton: {
      position: 'fixed',
      bottom: visible ? '300px' : '0',
      right: '20px',
      padding: '4px 12px',
      backgroundColor: '#1890ff',
      color: '#fff',
      border: 'none',
      borderRadius: visible ? '0' : '4px 4px 0 0',
      cursor: 'pointer',
      fontSize: '12px',
      zIndex: 1001,
      transition: 'all 0.3s ease'
    }
  };
  
  return (
    <>
      <button 
        style={styles.toggleButton}
        onClick={toggleVisibility}
      >
        {visible ? 'Hide' : 'WebSocket Debug'}
      </button>
      
      <div style={styles.container}>
        <div style={styles.header}>
          <h3 style={styles.title}>WebSocket Debug Panel</h3>
          <div style={styles.controls}>
            <button 
              style={{
                ...styles.button,
                backgroundColor: expanded ? '#e6f7ff' : '#fff'
              }}
              onClick={toggleExpanded}
            >
              {expanded ? 'Collapse' : 'Expand'}
            </button>
            <button 
              style={{
                ...styles.button,
                backgroundColor: connected ? '#fff2f0' : '#f6ffed'
              }}
              onClick={connected ? disconnect : connect}
            >
              {connected ? 'Disconnect' : 'Connect'}
            </button>
          </div>
        </div>
        
        <div style={styles.content}>
          <div style={styles.messagesContainer}>
            {filteredMessages.map(message => (
              <div 
                key={message.id}
                style={{
                  ...styles.message,
                  backgroundColor: 
                    message.type === 'system' ? '#f5f5f5' :
                    message.type === 'sent' ? '#e6f7ff' :
                    message.type === 'received' ? '#f6ffed' : '#fff2f0'
                }}
              >
                <div style={{ fontSize: '10px', color: '#8c8c8c', marginBottom: '2px' }}>
                  [{new Date(message.timestamp).toLocaleTimeString()}] {message.type.toUpperCase()}
                </div>
                <div>{formatContent(message.content)}</div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
          
          <div style={styles.inputContainer}>
            <input
              type="text"
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              placeholder="Enter JSON message..."
              style={styles.input}
              disabled={!connected}
            />
            <button
              style={styles.button}
              onClick={sendCustomMessage}
              disabled={!connected || !customMessage.trim()}
            >
              Send
            </button>
            <button
              style={styles.button}
              onClick={sendPing}
              disabled={!connected}
            >
              Ping
            </button>
            <button
              style={styles.button}
              onClick={clearMessages}
            >
              Clear
            </button>
          </div>
        </div>
        
        <div style={styles.footer}>
          <div style={styles.stats}>
            <div style={styles.statItem}>
              <span>Sent:</span>
              <span>{stats.sent}</span>
            </div>
            <div style={styles.statItem}>
              <span>Received:</span>
              <span>{stats.received}</span>
            </div>
            <div style={styles.statItem}>
              <span>Errors:</span>
              <span>{stats.errors}</span>
            </div>
          </div>
          <div style={styles.stats}>
            <div style={styles.statItem}>
              <span>Latency:</span>
              <span>{stats.lastLatency ? `${stats.lastLatency}ms` : 'N/A'}</span>
            </div>
            <div style={styles.statItem}>
              <span>Status:</span>
              <span style={{ 
                color: connected ? '#52c41a' : '#f5222d',
                fontWeight: 'bold'
              }}>
                {connected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default WebSocketDebugPanel;
