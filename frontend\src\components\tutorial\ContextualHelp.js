/**
 * Contextual Help System
 * 
 * Provides context-aware help tooltips, smart suggestions, and
 * contextual help panels based on user actions and current UI state.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { 
  Tooltip, 
  Card, 
  Typography, 
  Button, 
  Space, 
  Divider,
  Tag,
  Alert
} from 'antd';
import {
  QuestionCircleOutlined,
  BulbOutlined,
  CloseOutlined,
  BookOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import tutorialStorage from './TutorialStorage';
import { 
  HELP_CONTEXT_TYPES, 
  Z_INDEX,
  createHelpContext 
} from './types';

const { Text, Title } = Typography;

// Styled Components
const HelpTooltip = styled.div`
  position: absolute;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  max-width: 280px;
  z-index: ${Z_INDEX.TUTORIAL_TOOLTIP};
  pointer-events: auto;
  
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 6px 6px 0 6px;
    border-color: #fff transparent transparent transparent;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
  }
`;

const HelpPanel = styled(Card)`
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 320px;
  max-height: 70vh;
  overflow-y: auto;
  z-index: ${Z_INDEX.TUTORIAL_MODAL};
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
`;

const SmartSuggestion = styled.div`
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #1890ff;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  z-index: ${Z_INDEX.TUTORIAL_TOOLTIP};
  max-width: 300px;
  cursor: pointer;
  animation: slideIn 0.3s ease-out;
  
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`;

// Help Context Definitions
const HELP_CONTEXTS = {
  [HELP_CONTEXT_TYPES.COMPONENT_PALETTE]: createHelpContext({
    id: 'component_palette_help',
    type: HELP_CONTEXT_TYPES.COMPONENT_PALETTE,
    title: 'Component Palette',
    content: 'Drag components from here to the preview area to build your application. Each component has different properties you can customize.',
    triggers: ['hover', 'focus'],
    conditions: ['first_visit', 'no_components_added'],
    relatedTutorials: ['getting_started', 'component_basics']
  }),

  [HELP_CONTEXT_TYPES.PREVIEW_AREA]: createHelpContext({
    id: 'preview_area_help',
    type: HELP_CONTEXT_TYPES.PREVIEW_AREA,
    title: 'Preview Area',
    content: 'This is where your application comes to life. Drop components here and see them rendered in real-time.',
    triggers: ['empty_state', 'first_drop'],
    conditions: ['no_components'],
    relatedTutorials: ['getting_started', 'drag_drop_basics']
  }),

  [HELP_CONTEXT_TYPES.PROPERTY_EDITOR]: createHelpContext({
    id: 'property_editor_help',
    type: HELP_CONTEXT_TYPES.PROPERTY_EDITOR,
    title: 'Property Editor',
    content: 'Customize your selected component here. Change colors, text, sizes, and behavior to match your design.',
    triggers: ['component_selected'],
    conditions: ['first_selection'],
    relatedTutorials: ['component_properties', 'styling_basics']
  }),

  [HELP_CONTEXT_TYPES.DRAG_DROP]: createHelpContext({
    id: 'drag_drop_help',
    type: HELP_CONTEXT_TYPES.DRAG_DROP,
    title: 'Drag & Drop',
    content: 'Drag components from the palette and drop them in the preview area. You can also rearrange existing components.',
    triggers: ['drag_start', 'hover_long'],
    conditions: ['struggling_with_drag'],
    relatedTutorials: ['drag_drop_tutorial']
  })
};

const ContextualHelp = ({ userId = 'anonymous' }) => {
  const { startTutorial, preferences } = useTutorial();
  const [activeHelp, setActiveHelp] = useState(null);
  const [helpPosition, setHelpPosition] = useState({ x: 0, y: 0 });
  const [showPanel, setShowPanel] = useState(false);
  const [smartSuggestion, setSmartSuggestion] = useState(null);
  const [userBehavior, setUserBehavior] = useState({
    hoverTime: 0,
    clickCount: 0,
    lastAction: null,
    strugglingWith: null
  });

  const hoverTimerRef = useRef(null);
  const behaviorTimerRef = useRef(null);

  // Track user behavior for smart suggestions
  const trackUserBehavior = useCallback((action, element, duration = 0) => {
    setUserBehavior(prev => ({
      ...prev,
      lastAction: action,
      clickCount: action === 'click' ? prev.clickCount + 1 : prev.clickCount,
      hoverTime: action === 'hover' ? duration : prev.hoverTime
    }));

    // Detect struggling patterns
    if (action === 'hover' && duration > 3000) {
      setUserBehavior(prev => ({ ...prev, strugglingWith: element }));
      showSmartSuggestion(element);
    }
  }, []);

  // Show smart suggestion based on user behavior
  const showSmartSuggestion = useCallback((context) => {
    if (!preferences.showContextualHelp) return;

    const helpContext = HELP_CONTEXTS[context];
    if (!helpContext) return;

    // Check if already shown
    const shownContexts = tutorialStorage.getShownHelpContexts(userId);
    if (helpContext.showOnce && shownContexts.includes(helpContext.id)) {
      return;
    }

    setSmartSuggestion({
      ...helpContext,
      onAccept: () => {
        if (helpContext.relatedTutorials.length > 0) {
          startTutorial(helpContext.relatedTutorials[0]);
        }
        setSmartSuggestion(null);
        tutorialStorage.markHelpContextShown(helpContext.id, userId);
      },
      onDismiss: () => {
        setSmartSuggestion(null);
        tutorialStorage.markHelpContextShown(helpContext.id, userId);
      }
    });
  }, [preferences.showContextualHelp, userId, startTutorial]);

  // Show contextual help tooltip
  const showContextualHelp = useCallback((context, element, position) => {
    if (!preferences.showTooltips) return;

    const helpContext = HELP_CONTEXTS[context];
    if (!helpContext) return;

    setActiveHelp(helpContext);
    setHelpPosition(position);

    // Auto-hide after 5 seconds
    setTimeout(() => {
      setActiveHelp(null);
    }, 5000);
  }, [preferences.showTooltips]);

  // Set up event listeners for contextual help
  useEffect(() => {
    if (!preferences.showContextualHelp) return;

    const handleMouseEnter = (e) => {
      const element = e.target;
      const startTime = Date.now();

      // Component palette help
      if (element.closest('[data-help-context="component-palette"]')) {
        hoverTimerRef.current = setTimeout(() => {
          const duration = Date.now() - startTime;
          trackUserBehavior('hover', HELP_CONTEXT_TYPES.COMPONENT_PALETTE, duration);
          
          const rect = element.getBoundingClientRect();
          showContextualHelp(HELP_CONTEXT_TYPES.COMPONENT_PALETTE, element, {
            x: rect.left + rect.width / 2,
            y: rect.top - 10
          });
        }, 1500);
      }

      // Preview area help
      if (element.closest('[data-help-context="preview-area"]')) {
        const hasComponents = document.querySelectorAll('[data-component-id]').length > 0;
        if (!hasComponents) {
          hoverTimerRef.current = setTimeout(() => {
            const duration = Date.now() - startTime;
            trackUserBehavior('hover', HELP_CONTEXT_TYPES.PREVIEW_AREA, duration);
            
            const rect = element.getBoundingClientRect();
            showContextualHelp(HELP_CONTEXT_TYPES.PREVIEW_AREA, element, {
              x: rect.left + rect.width / 2,
              y: rect.top + 20
            });
          }, 2000);
        }
      }

      // Property editor help
      if (element.closest('[data-help-context="property-editor"]')) {
        hoverTimerRef.current = setTimeout(() => {
          const duration = Date.now() - startTime;
          trackUserBehavior('hover', HELP_CONTEXT_TYPES.PROPERTY_EDITOR, duration);
          
          const rect = element.getBoundingClientRect();
          showContextualHelp(HELP_CONTEXT_TYPES.PROPERTY_EDITOR, element, {
            x: rect.left - 10,
            y: rect.top + rect.height / 2
          });
        }, 1000);
      }
    };

    const handleMouseLeave = () => {
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
        hoverTimerRef.current = null;
      }
    };

    const handleClick = (e) => {
      trackUserBehavior('click', e.target.dataset.helpContext);
      setActiveHelp(null); // Hide help on click
    };

    // Add event listeners
    document.addEventListener('mouseenter', handleMouseEnter, true);
    document.addEventListener('mouseleave', handleMouseLeave, true);
    document.addEventListener('click', handleClick, true);

    return () => {
      document.removeEventListener('mouseenter', handleMouseEnter, true);
      document.removeEventListener('mouseleave', handleMouseLeave, true);
      document.removeEventListener('click', handleClick, true);
      
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
      }
    };
  }, [preferences.showContextualHelp, trackUserBehavior, showContextualHelp]);

  // Detect empty states and show suggestions
  useEffect(() => {
    const checkEmptyStates = () => {
      const hasComponents = document.querySelectorAll('[data-component-id]').length > 0;
      
      if (!hasComponents) {
        behaviorTimerRef.current = setTimeout(() => {
          showSmartSuggestion(HELP_CONTEXT_TYPES.PREVIEW_AREA);
        }, 10000); // Show suggestion after 10 seconds of empty state
      }
    };

    checkEmptyStates();
    
    return () => {
      if (behaviorTimerRef.current) {
        clearTimeout(behaviorTimerRef.current);
      }
    };
  }, [showSmartSuggestion]);

  return (
    <>
      {/* Contextual Help Tooltip */}
      {activeHelp && createPortal(
        <HelpTooltip
          style={{
            left: helpPosition.x,
            top: helpPosition.y
          }}
        >
          <Space direction="vertical" size="small">
            <Space>
              <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              <Text strong>{activeHelp.title}</Text>
            </Space>
            <Text>{activeHelp.content}</Text>
            {activeHelp.relatedTutorials.length > 0 && (
              <Button
                type="link"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => {
                  startTutorial(activeHelp.relatedTutorials[0]);
                  setActiveHelp(null);
                }}
              >
                Start Tutorial
              </Button>
            )}
          </Space>
        </HelpTooltip>,
        document.body
      )}

      {/* Smart Suggestion */}
      {smartSuggestion && (
        <SmartSuggestion onClick={smartSuggestion.onAccept}>
          <Space direction="vertical" size="small">
            <Space>
              <BulbOutlined />
              <Text style={{ color: 'white', fontWeight: 'bold' }}>
                Need help?
              </Text>
              <Button
                type="text"
                size="small"
                icon={<CloseOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  smartSuggestion.onDismiss();
                }}
                style={{ color: 'white', padding: 0 }}
              />
            </Space>
            <Text style={{ color: 'white' }}>
              {smartSuggestion.content}
            </Text>
            <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
              Click to start tutorial
            </Text>
          </Space>
        </SmartSuggestion>
      )}

      {/* Help Panel */}
      {showPanel && (
        <HelpPanel
          title={
            <Space>
              <BookOutlined />
              Contextual Help
            </Space>
          }
          extra={
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setShowPanel(false)}
            />
          }
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="Context-Aware Help"
              description="Get help based on what you're currently doing in the App Builder."
              type="info"
              showIcon
            />
            
            <Divider />
            
            <Title level={5}>Available Help Topics</Title>
            
            {Object.values(HELP_CONTEXTS).map(context => (
              <Card key={context.id} size="small" style={{ marginBottom: 8 }}>
                <Space direction="vertical" size="small">
                  <Text strong>{context.title}</Text>
                  <Text type="secondary">{context.content}</Text>
                  {context.relatedTutorials.length > 0 && (
                    <Space wrap>
                      {context.relatedTutorials.map(tutorialId => (
                        <Tag 
                          key={tutorialId}
                          color="blue"
                          style={{ cursor: 'pointer' }}
                          onClick={() => startTutorial(tutorialId)}
                        >
                          {tutorialId.replace('_', ' ')}
                        </Tag>
                      ))}
                    </Space>
                  )}
                </Space>
              </Card>
            ))}
          </Space>
        </HelpPanel>
      )}
    </>
  );
};

// Hook for components to trigger contextual help
export const useContextualHelp = () => {
  const showHelp = useCallback((context, element) => {
    const event = new CustomEvent('showContextualHelp', {
      detail: { context, element }
    });
    window.dispatchEvent(event);
  }, []);

  return { showHelp };
};

export default ContextualHelp;
