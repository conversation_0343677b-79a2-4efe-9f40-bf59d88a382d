/**
 * Utility functions for exporting charts and chart data
 */

/**
 * Download a chart as an image
 * @param {string} chartId - The ID of the chart element
 * @param {string} fileName - The name of the file to download
 * @param {string} format - The format of the image (png, jpeg, svg)
 */
export const downloadChartAsImage = (chartId, fileName = 'chart', format = 'png') => {
  try {
    // Get the chart element
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
      console.error(`Chart element with ID "${chartId}" not found`);
      return;
    }
    
    // Create a canvas element
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Get the SVG data
    const svgData = new XMLSerializer().serializeToString(chartElement.querySelector('svg'));
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const svgUrl = URL.createObjectURL(svgBlob);
    
    // Create an image element
    const img = new Image();
    
    // Set up the image onload handler
    img.onload = () => {
      // Set canvas dimensions
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw the image on the canvas
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);
      
      // Convert canvas to data URL
      let dataUrl;
      if (format === 'png') {
        dataUrl = canvas.toDataURL('image/png');
      } else if (format === 'jpeg') {
        dataUrl = canvas.toDataURL('image/jpeg', 0.9);
      } else if (format === 'svg') {
        // For SVG, use the original SVG data
        const downloadLink = document.createElement('a');
        downloadLink.href = svgUrl;
        downloadLink.download = `${fileName}.svg`;
        downloadLink.click();
        URL.revokeObjectURL(svgUrl);
        return;
      }
      
      // Create a download link
      const downloadLink = document.createElement('a');
      downloadLink.href = dataUrl;
      downloadLink.download = `${fileName}.${format}`;
      downloadLink.click();
      
      // Clean up
      URL.revokeObjectURL(svgUrl);
    };
    
    // Set the image source
    img.src = svgUrl;
  } catch (error) {
    console.error('Error downloading chart as image:', error);
  }
};

/**
 * Export chart data to a file
 * @param {Array} data - The data to export
 * @param {string} fileName - The name of the file to download
 * @param {string} format - The format of the file (csv, json, excel)
 */
export const exportChartData = (data, fileName = 'chart-data', format = 'csv') => {
  try {
    if (!data || data.length === 0) {
      console.error('No data to export');
      return;
    }
    
    let content;
    let mimeType;
    let fileExtension;
    
    if (format === 'csv') {
      // Convert data to CSV
      const headers = Object.keys(data[0]).join(',');
      const rows = data.map(item => Object.values(item).join(','));
      content = [headers, ...rows].join('\n');
      mimeType = 'text/csv';
      fileExtension = 'csv';
    } else if (format === 'json') {
      // Convert data to JSON
      content = JSON.stringify(data, null, 2);
      mimeType = 'application/json';
      fileExtension = 'json';
    } else if (format === 'excel') {
      // Convert data to Excel (CSV format)
      const headers = Object.keys(data[0]).join(',');
      const rows = data.map(item => Object.values(item).join(','));
      content = [headers, ...rows].join('\n');
      mimeType = 'application/vnd.ms-excel';
      fileExtension = 'xls';
    } else {
      console.error(`Unsupported format: ${format}`);
      return;
    }
    
    // Create a blob
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    // Create a download link
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = `${fileName}.${fileExtension}`;
    downloadLink.click();
    
    // Clean up
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting chart data:', error);
  }
};

/**
 * Generate random chart data for testing
 * @param {number} count - The number of data points to generate
 * @param {number} min - The minimum value
 * @param {number} max - The maximum value
 * @param {Array} categories - The categories to use for pie charts
 * @returns {Array} - The generated data
 */
export const generateRandomChartData = (count = 20, min = 0, max = 100, categories = ['A', 'B', 'C', 'D']) => {
  const data = [];
  const now = Date.now();
  
  for (let i = 0; i < count; i++) {
    const timestamp = new Date(now - (count - i) * 60000).toISOString();
    const value = Math.floor(Math.random() * (max - min + 1)) + min;
    const category = categories[Math.floor(Math.random() * categories.length)];
    
    data.push({
      timestamp,
      value,
      category,
      x: i,
      y: value
    });
  }
  
  return data;
};

/**
 * Format a number with commas
 * @param {number} num - The number to format
 * @returns {string} - The formatted number
 */
export const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * Format a date for display
 * @param {string} dateString - The date string to format
 * @param {string} format - The format to use (short, medium, long)
 * @returns {string} - The formatted date
 */
export const formatDate = (dateString, format = 'medium') => {
  const date = new Date(dateString);
  
  if (format === 'short') {
    return date.toLocaleDateString();
  } else if (format === 'medium') {
    return date.toLocaleString();
  } else if (format === 'long') {
    return date.toLocaleString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }
  
  return date.toLocaleString();
};

/**
 * Calculate statistics for a dataset
 * @param {Array} data - The data to analyze
 * @param {string} key - The key to use for calculations
 * @returns {Object} - The calculated statistics
 */
export const calculateStatistics = (data, key) => {
  if (!data || data.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      sum: 0,
      count: 0,
      median: 0,
      variance: 0,
      stdDev: 0
    };
  }
  
  const values = data.map(item => item[key]).filter(val => !isNaN(val));
  
  if (values.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      sum: 0,
      count: 0,
      median: 0,
      variance: 0,
      stdDev: 0
    };
  }
  
  // Sort values for median calculation
  const sortedValues = [...values].sort((a, b) => a - b);
  
  // Calculate statistics
  const min = Math.min(...values);
  const max = Math.max(...values);
  const sum = values.reduce((a, b) => a + b, 0);
  const count = values.length;
  const avg = sum / count;
  
  // Calculate median
  const mid = Math.floor(sortedValues.length / 2);
  const median = sortedValues.length % 2 === 0
    ? (sortedValues[mid - 1] + sortedValues[mid]) / 2
    : sortedValues[mid];
  
  // Calculate variance and standard deviation
  const squareDiffs = values.map(value => {
    const diff = value - avg;
    return diff * diff;
  });
  const variance = squareDiffs.reduce((a, b) => a + b, 0) / count;
  const stdDev = Math.sqrt(variance);
  
  return {
    min,
    max,
    avg,
    sum,
    count,
    median,
    variance,
    stdDev
  };
};

export default {
  downloadChartAsImage,
  exportChartData,
  generateRandomChartData,
  formatNumber,
  formatDate,
  calculateStatistics
};
