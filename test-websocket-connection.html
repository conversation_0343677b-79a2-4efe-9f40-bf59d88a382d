<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    
    <div class="status info">
        <strong>Testing WebSocket connections to resolve "Invalid frame header" error</strong>
    </div>

    <div>
        <label for="wsUrl">WebSocket URL:</label>
        <select id="wsUrl">
            <option value="ws://localhost:8000/ws/">Direct Backend (ws://localhost:8000/ws/)</option>
            <option value="ws://localhost:8000/ws/test/">Direct Backend Test (ws://localhost:8000/ws/test/)</option>
            <option value="ws://localhost:8000/ws/app_builder/">Direct Backend App Builder (ws://localhost:8000/ws/app_builder/)</option>
            <option value="ws://localhost:3000/ws/">Frontend Proxy (ws://localhost:3000/ws/)</option>
            <option value="ws://localhost:3000/ws/test/">Frontend Proxy Test (ws://localhost:3000/ws/test/)</option>
            <option value="ws://localhost:3000/ws/app_builder/">Frontend Proxy App Builder (ws://localhost:3000/ws/app_builder/)</option>
        </select>
    </div>

    <div style="margin: 20px 0;">
        <button id="connectBtn" class="btn-primary">Connect</button>
        <button id="disconnectBtn" class="btn-danger" disabled>Disconnect</button>
        <button id="sendTestBtn" class="btn-success" disabled>Send Test Message</button>
        <button id="clearLogBtn" class="btn-primary">Clear Log</button>
    </div>

    <div>
        <label for="testMessage">Test Message:</label>
        <input type="text" id="testMessage" value='{"type":"ping","message":"Hello WebSocket!"}' style="width: 100%; padding: 5px;">
    </div>

    <h3>Connection Log:</h3>
    <div id="log"></div>

    <script>
        let ws = null;
        const log = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const sendTestBtn = document.getElementById('sendTestBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const wsUrlSelect = document.getElementById('wsUrl');
        const testMessageInput = document.getElementById('testMessage');

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            log.textContent += logEntry;
            log.scrollTop = log.scrollHeight;
            
            console.log(`WebSocket Test: ${message}`);
        }

        function updateButtons(connected) {
            connectBtn.disabled = connected;
            disconnectBtn.disabled = !connected;
            sendTestBtn.disabled = !connected;
        }

        connectBtn.addEventListener('click', () => {
            const url = wsUrlSelect.value;
            addLog(`🔌 Attempting to connect to: ${url}`);
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = (event) => {
                    addLog('✅ WebSocket connection opened successfully!', 'success');
                    updateButtons(true);
                };
                
                ws.onmessage = (event) => {
                    addLog(`📥 Received: ${event.data}`, 'success');
                };
                
                ws.onclose = (event) => {
                    addLog(`🔌 Connection closed: code=${event.code}, reason="${event.reason}"`, 'warning');
                    updateButtons(false);
                };
                
                ws.onerror = (error) => {
                    addLog(`❌ WebSocket error: ${error.message || 'Unknown error'}`, 'error');
                    updateButtons(false);
                };
                
                // Timeout after 10 seconds
                setTimeout(() => {
                    if (ws && ws.readyState === WebSocket.CONNECTING) {
                        addLog('⏰ Connection timeout after 10 seconds', 'error');
                        ws.close();
                    }
                }, 10000);
                
            } catch (error) {
                addLog(`❌ Exception creating WebSocket: ${error.message}`, 'error');
            }
        });

        disconnectBtn.addEventListener('click', () => {
            if (ws) {
                addLog('🔌 Manually disconnecting...');
                ws.close();
            }
        });

        sendTestBtn.addEventListener('click', () => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = testMessageInput.value;
                addLog(`📤 Sending: ${message}`);
                ws.send(message);
            } else {
                addLog('❌ Cannot send message: WebSocket not connected', 'error');
            }
        });

        clearLogBtn.addEventListener('click', () => {
            log.textContent = '';
        });

        // Initial log
        addLog('🚀 WebSocket Test Page Loaded');
        addLog('📋 Instructions:');
        addLog('1. Select a WebSocket URL from the dropdown');
        addLog('2. Click "Connect" to test the connection');
        addLog('3. If successful, try sending a test message');
        addLog('4. Check for any error messages');
        addLog('');
    </script>
</body>
</html>
