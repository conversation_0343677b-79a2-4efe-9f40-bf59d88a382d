# Docker Connection Troubleshooting Guide

## Current Issue
**Error**: `could not find specified file` when connecting to Docker containers
**Root Cause**: Docker Desktop is not running or not properly configured

## Quick Status Check
Run this command to check Docker status:
```powershell
powershell -ExecutionPolicy Bypass -File scripts/simple-docker-fix.ps1
```

## Solution Steps

### Step 1: Manual Docker Desktop Start
1. **Check System Tray**: Look for Docker whale icon in system tray (bottom-right corner)
2. **Start Manually**: 
   - Press `Win + R`, type `Docker Desktop`, press Enter
   - OR find "Docker Desktop" in Start menu and click it
3. **Wait for Startup**: Docker Desktop can take 2-5 minutes to fully start

### Step 2: Verify Docker is Running
```powershell
# Check Docker status
docker --version
docker info

# If successful, you should see Docker version and system info
```

### Step 3: Start App Builder Containers
Once Docker is running:
```powershell
# Option 1: Use our startup script
powershell -ExecutionPolicy Bypass -File scripts/start-containers.ps1

# Option 2: Manual docker-compose
docker-compose up -d

# Option 3: Build and start fresh
docker-compose up -d --build
```

## Common Issues and Solutions

### Issue 1: Docker Desktop Won't Start
**Symptoms**: Docker Desktop icon appears but service doesn't start
**Solutions**:
1. **Restart Docker Desktop**:
   - Right-click Docker icon in system tray
   - Select "Restart Docker Desktop"

2. **Update WSL2**:
   ```powershell
   wsl --update
   wsl --set-default-version 2
   ```

3. **Enable Windows Features**:
   - Open "Turn Windows features on or off"
   - Enable: "Windows Subsystem for Linux"
   - Enable: "Virtual Machine Platform"
   - Enable: "Hyper-V" (if available)
   - Restart computer

### Issue 2: Permission Errors
**Symptoms**: Access denied or permission errors
**Solutions**:
1. **Run as Administrator**:
   - Right-click PowerShell/Command Prompt
   - Select "Run as administrator"

2. **Add User to Docker Group**:
   - Docker Desktop → Settings → General
   - Check "Use the WSL 2 based engine"

### Issue 3: WSL2 Issues
**Symptoms**: WSL2 backend errors
**Solutions**:
1. **Update WSL2 Kernel**:
   ```powershell
   wsl --update
   wsl --shutdown
   ```

2. **Reset WSL2**:
   ```powershell
   wsl --unregister docker-desktop
   wsl --unregister docker-desktop-data
   ```
   Then restart Docker Desktop

### Issue 4: Port Conflicts
**Symptoms**: Ports 3000, 8000, or 5432 already in use
**Solutions**:
1. **Check Running Processes**:
   ```powershell
   netstat -ano | findstr :3000
   netstat -ano | findstr :8000
   netstat -ano | findstr :5432
   ```

2. **Kill Conflicting Processes**:
   ```powershell
   # Replace PID with actual process ID
   taskkill /PID <PID> /F
   ```

## Alternative: Run Without Docker

If Docker continues to have issues, you can run the application components individually:

### Backend (Django)
```powershell
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver 0.0.0.0:8000
```

### Frontend (React)
```powershell
cd frontend
npm install
npm start
```

### Database (SQLite - No Docker needed)
The backend will automatically use SQLite if PostgreSQL is not available.

## Verification Commands

### Check Docker Status
```powershell
docker --version
docker info
docker-compose --version
```

### Check Containers
```powershell
docker ps -a
docker-compose ps
docker-compose logs
```

### Check Application URLs
- Frontend: http://localhost:3000
- Backend: http://localhost:8000
- Backend Health: http://localhost:8000/health/

## Getting Help

### Check Logs
```powershell
# Docker Desktop logs
# Go to Docker Desktop → Troubleshoot → View logs

# Container logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs db
```

### System Information
```powershell
# Windows version
winver

# WSL version
wsl --version

# Docker version
docker version
```

## Next Steps After Docker is Running

1. **Start Containers**:
   ```powershell
   docker-compose up -d
   ```

2. **Verify Services**:
   - Frontend: http://localhost:3000
   - Backend: http://localhost:8000/health/

3. **View Logs**:
   ```powershell
   docker-compose logs -f
   ```

4. **Stop Containers** (when done):
   ```powershell
   docker-compose down
   ```

## Contact Support
If issues persist:
1. Check Docker Desktop documentation
2. Restart your computer
3. Reinstall Docker Desktop if necessary
4. Consider using the non-Docker alternative above
