import React, { useState, useCallback } from 'react';
import { <PERSON>, But<PERSON>, Badge, Tooltip, Space, Typography, Modal, Select, Tabs, Spin } from 'antd';
import { 
  CodeOutlined, 
  DownloadOutlined, 
  EyeOutlined,
  CopyOutlined,
  SettingOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import useCodeExport from '../../hooks/useCodeExport';
import ExportPreview from './ExportPreview';
import ExportSettings from './ExportSettings';

const { Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * Code Exporter
 * 
 * Main code export component that provides code generation and download
 * functionality for multiple frameworks and formats.
 */

const ExportContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CompactExport = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: #fff1b8;
    border-color: #ffec3d;
  }
  
  .export-icon {
    color: #fa8c16;
    font-size: 16px;
  }
  
  .export-text {
    flex: 1;
    font-size: 13px;
    color: #262626;
  }
  
  .export-count {
    background: #fa8c16;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 600;
  }
`;

const CodeExporter = ({
  formats = [],
  loading = false,
  onExport,
  onDownload,
  compact = false,
  components = [],
  selectedFormat = 'react'
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('export');
  const [currentFormat, setCurrentFormat] = useState(selectedFormat);
  const [exportSettings, setExportSettings] = useState({
    includeStyles: true,
    includeTests: false,
    typescript: false,
    bundled: false
  });

  // Code export hook
  const {
    exportFormats: hookFormats,
    loading: hookLoading,
    exportCode,
    downloadCode,
    previewCode,
    getAvailableFormats
  } = useCodeExport({
    enabled: true,
    components,
    settings: exportSettings
  });

  // Use hook data if no props provided
  const activeFormats = formats.length > 0 ? formats : hookFormats;
  const isLoading = loading || hookLoading;
  const formatCount = activeFormats.length;

  // Handle export operations
  const handleExport = useCallback(async (format, settings) => {
    if (onExport) {
      return onExport(format, settings);
    } else {
      return exportCode(format, settings);
    }
  }, [onExport, exportCode]);

  const handleDownload = useCallback(async (format, settings) => {
    if (onDownload) {
      return onDownload(format, settings);
    } else {
      return downloadCode(format, settings);
    }
  }, [onDownload, downloadCode]);

  const handleQuickExport = useCallback(async () => {
    try {
      await handleDownload(currentFormat, exportSettings);
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, [currentFormat, exportSettings, handleDownload]);

  // Available export formats
  const defaultFormats = [
    { value: 'react', label: 'React', icon: '⚛️' },
    { value: 'vue', label: 'Vue.js', icon: '🟢' },
    { value: 'angular', label: 'Angular', icon: '🔴' },
    { value: 'svelte', label: 'Svelte', icon: '🧡' },
    { value: 'html', label: 'HTML/CSS', icon: '📄' },
    { value: 'react-native', label: 'React Native', icon: '📱' },
    { value: 'flutter', label: 'Flutter', icon: '💙' }
  ];

  const availableFormats = activeFormats.length > 0 ? activeFormats : defaultFormats;

  // Compact mode for header/toolbar
  if (compact) {
    return (
      <ExportContainer>
        <CompactExport onClick={() => setModalVisible(true)}>
          <CodeOutlined className="export-icon" />
          <span className="export-text">Export Code</span>
          {formatCount > 0 && (
            <span className="export-count">{formatCount}</span>
          )}
        </CompactExport>

        <Select
          value={currentFormat}
          onChange={setCurrentFormat}
          style={{ width: 100 }}
          size="small"
        >
          {availableFormats.map(format => (
            <Option key={format.value} value={format.value}>
              {format.icon} {format.label}
            </Option>
          ))}
        </Select>

        <Tooltip title="Quick Export">
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={handleQuickExport}
            loading={isLoading}
            disabled={!components || components.length === 0}
          />
        </Tooltip>

        {/* Export Modal */}
        <Modal
          title="Code Exporter"
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={null}
          width={900}
          style={{ top: 20 }}
        >
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="Export" key="export">
              <ExportPreview
                components={components}
                format={currentFormat}
                settings={exportSettings}
                onExport={handleExport}
                onDownload={handleDownload}
                loading={isLoading}
              />
            </TabPane>
            <TabPane tab="Settings" key="settings">
              <ExportSettings
                settings={exportSettings}
                onChange={setExportSettings}
                format={currentFormat}
              />
            </TabPane>
          </Tabs>
        </Modal>
      </ExportContainer>
    );
  }

  // Full mode for dedicated export area
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <CodeOutlined style={{ color: '#fa8c16' }} />
            <Text strong>Code Export</Text>
            {formatCount > 0 && (
              <Badge count={formatCount} style={{ backgroundColor: '#fa8c16' }} />
            )}
          </Space>
          
          <Space>
            <Select
              value={currentFormat}
              onChange={setCurrentFormat}
              style={{ width: 120 }}
            >
              {availableFormats.map(format => (
                <Option key={format.value} value={format.value}>
                  {format.icon} {format.label}
                </Option>
              ))}
            </Select>
            
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleQuickExport}
              loading={isLoading}
              disabled={!components || components.length === 0}
            >
              Export
            </Button>
            
            <Button
              icon={<SettingOutlined />}
              onClick={() => setModalVisible(true)}
            >
              Advanced
            </Button>
          </Space>
        </div>

        {isLoading ? (
          <Card>
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size="large" />
              <div style={{ marginTop: 16 }}>
                <Text>Generating code...</Text>
              </div>
            </div>
          </Card>
        ) : components && components.length > 0 ? (
          <Card>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Ready to export:</Text>
                <Text type="secondary" style={{ marginLeft: 8 }}>
                  {components.length} component{components.length !== 1 ? 's' : ''}
                </Text>
              </div>
              
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 12 }}>
                {availableFormats.slice(0, 4).map(format => (
                  <Button
                    key={format.value}
                    style={{ height: 'auto', padding: '12px' }}
                    onClick={() => {
                      setCurrentFormat(format.value);
                      handleQuickExport();
                    }}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: 20, marginBottom: 4 }}>{format.icon}</div>
                      <div>{format.label}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </Space>
          </Card>
        ) : (
          <Card>
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <FileTextOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">Add components to enable code export</Text>
              </div>
            </div>
          </Card>
        )}
      </Space>

      {/* Export Modal */}
      <Modal
        title="Code Exporter"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={1000}
        style={{ top: 20 }}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Export" key="export">
            <ExportPreview
              components={components}
              format={currentFormat}
              settings={exportSettings}
              onExport={handleExport}
              onDownload={handleDownload}
              loading={isLoading}
            />
          </TabPane>
          <TabPane tab="Settings" key="settings">
            <ExportSettings
              settings={exportSettings}
              onChange={setExportSettings}
              format={currentFormat}
            />
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  );
};

export default CodeExporter;
