import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Layout, Card, Button, Space, Tooltip, message, Spin } from 'antd';
import {
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  EyeOutlined,
  SettingOutlined,
  FullscreenOutlined,
  CompressOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components';

// Import enhanced components
import EnhancedComponentPalette from './EnhancedComponentPalette';
import EnhancedPreviewArea from './EnhancedPreviewArea';
import DragVisualFeedback from './DragVisualFeedback';
import ContextualMenu from './ContextualMenu';

// Import hooks
import { useUndoRedo, useKeyboardShortcuts, useContextMenu, useLoadingState, useSelection, useClipboard } from '../../hooks/useUndoRedo';
import { useEnhancedDragDrop } from '../../hooks/useEnhancedDragDrop';

// Import Redux actions
import { addComponent, updateComponent, removeComponent } from '../../redux/minimal-store';

const { Sider, Content } = Layout;

// Styled components
const BuilderContainer = styled(Layout)`
  height: 100vh;
  background: #f5f5f5;
`;

const Toolbar = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
`;

const ToolbarSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PaletteContainer = styled(Sider)`
  background: white;
  border-right: 1px solid #e8e8e8;
  overflow: auto;
  
  .ant-layout-sider-children {
    padding: 16px;
  }
`;

const PreviewContainer = styled(Content)`
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  position: relative;
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
`;

const EnhancedComponentBuilder = () => {
  const dispatch = useDispatch();
  const components = useSelector(state => state.app?.components || []);

  // State management
  const [previewMode, setPreviewMode] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [draggedComponent, setDraggedComponent] = useState(null);
  const [showSuccess, setShowSuccess] = useState(false);

  // Hooks
  const { state: componentHistory, pushState, undo, redo, canUndo, canRedo } = useUndoRedo(components);
  const { contextMenu, showContextMenu, hideContextMenu } = useContextMenu();
  const { isLoading, loadingMessage, startLoading, stopLoading } = useLoadingState();
  const { selectedItems, selectItem, clearSelection, isSelected } = useSelection(components);
  const { copy, paste, hasData: clipboardHasData } = useClipboard();



  // Refs
  const builderRef = useRef(null);

  // Handle component drop
  const handleComponentDrop = useCallback((e, dragData, position) => {
    if (!dragData) return;

    startLoading('Adding component...');

    try {
      const newComponent = {
        id: `${dragData.type}-${Date.now()}`,
        type: dragData.type,
        props: {
          x: position.x,
          y: position.y,
          ...getDefaultProps(dragData.type)
        },
        createdAt: new Date().toISOString()
      };

      dispatch(addComponent(newComponent));

      // Show success feedback
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);

      message.success(`${dragData.label || dragData.type} component added`);
    } catch (error) {
      message.error('Failed to add component');
      console.error('Error adding component:', error);
    } finally {
      stopLoading();
    }
  }, [dispatch, startLoading, stopLoading]);

  // Drag and drop with proper callback
  const {
    isDragging,
    isOver,
    validDropZone,
    dropPosition,
    dropZoneRef,
    handleDragStart,
    handleDragEnd
  } = useEnhancedDragDrop({
    onDrop: handleComponentDrop,
    snapToGrid: true,
    gridSize: 20
  });

  // Update history when components change
  useEffect(() => {
    if (JSON.stringify(components) !== JSON.stringify(componentHistory)) {
      pushState(components);
    }
  }, [components, componentHistory, pushState]);

  // Get default props for component type
  const getDefaultProps = (type) => {
    const defaults = {
      text: { content: 'Sample text', fontSize: 14 },
      button: { text: 'Button', type: 'default' },
      header: { text: 'Header', level: 2 },
      card: { title: 'Card Title', content: 'Card content' },
      image: { src: 'https://via.placeholder.com/150', alt: 'Image' },
      input: { placeholder: 'Enter text' },
      form: { layout: 'vertical' }
    };
    return defaults[type] || {};
  };

  // Handle component selection
  const handleComponentSelect = useCallback((component) => {
    if (component) {
      selectItem(component);
    } else {
      clearSelection();
    }
  }, [selectItem, clearSelection]);

  // Handle component deletion
  const handleComponentDelete = useCallback((componentId) => {
    startLoading('Deleting component...');

    try {
      dispatch(removeComponent(componentId));
      clearSelection();
      message.success('Component deleted');
    } catch (error) {
      message.error('Failed to delete component');
    } finally {
      stopLoading();
    }
  }, [dispatch, clearSelection, startLoading, stopLoading]);

  // Handle component update
  const handleComponentUpdate = useCallback((componentId, updates) => {
    startLoading('Updating component...');

    try {
      dispatch(updateComponent(componentId, updates));
      message.success('Component updated');
    } catch (error) {
      message.error('Failed to update component');
    } finally {
      stopLoading();
    }
  }, [dispatch, startLoading, stopLoading]);

  // Handle undo
  const handleUndo = useCallback(() => {
    const previousState = undo();
    if (previousState) {
      // Update Redux store with previous state
      // This would need to be implemented based on your Redux structure
      message.success('Undone');
    }
  }, [undo]);

  // Handle redo
  const handleRedo = useCallback(() => {
    const nextState = redo();
    if (nextState) {
      // Update Redux store with next state
      // This would need to be implemented based on your Redux structure
      message.success('Redone');
    }
  }, [redo]);

  // Handle copy
  const handleCopy = useCallback((component) => {
    copy(component);
    message.success('Component copied');
  }, [copy]);

  // Handle paste
  const handlePaste = useCallback(() => {
    const copiedComponent = paste();
    if (copiedComponent) {
      const newComponent = {
        ...copiedComponent,
        id: `${copiedComponent.type}-${Date.now()}`,
        props: {
          ...copiedComponent.props,
          x: (copiedComponent.props.x || 0) + 20,
          y: (copiedComponent.props.y || 0) + 20
        }
      };
      dispatch(addComponent(newComponent));
      message.success('Component pasted');
    }
  }, [paste, dispatch]);

  // Handle context menu
  const handleContextMenu = useCallback((e, component) => {
    e.preventDefault();

    const menuItems = [
      { key: 'edit', label: 'Edit', icon: 'edit' },
      { key: 'copy', label: 'Copy', icon: 'copy' },
      { key: 'delete', label: 'Delete', icon: 'delete', danger: true }
    ];

    showContextMenu(e, menuItems);
  }, [showContextMenu]);

  // Keyboard shortcuts
  useKeyboardShortcuts({
    'ctrl+z': handleUndo,
    'ctrl+y': handleRedo,
    'ctrl+c': () => {
      if (selectedItems.length > 0) {
        handleCopy(components.find(c => c.id === selectedItems[0]));
      }
    },
    'ctrl+v': handlePaste,
    'delete': () => {
      if (selectedItems.length > 0) {
        selectedItems.forEach(id => handleComponentDelete(id));
      }
    },
    'escape': () => {
      clearSelection();
      hideContextMenu();
    },
    'f11': (e) => {
      e.preventDefault();
      setFullscreen(!fullscreen);
    }
  }, [selectedItems, components, handleUndo, handleRedo, handleCopy, handlePaste, handleComponentDelete, clearSelection, hideContextMenu, fullscreen]);

  return (
    <BuilderContainer ref={builderRef} className={fullscreen ? 'fullscreen' : ''}>
      {/* Toolbar */}
      <Toolbar>
        <ToolbarSection>
          <Tooltip title="Undo (Ctrl+Z)">
            <Button
              icon={<UndoOutlined />}
              disabled={!canUndo}
              onClick={handleUndo}
            />
          </Tooltip>
          <Tooltip title="Redo (Ctrl+Y)">
            <Button
              icon={<RedoOutlined />}
              disabled={!canRedo}
              onClick={handleRedo}
            />
          </Tooltip>
          <Button icon={<SaveOutlined />}>Save</Button>
        </ToolbarSection>

        <ToolbarSection>
          <span style={{ fontSize: 16, fontWeight: 600 }}>Enhanced Component Builder</span>
        </ToolbarSection>

        <ToolbarSection>
          <Tooltip title="Toggle Preview Mode">
            <Button
              icon={<EyeOutlined />}
              type={previewMode ? 'primary' : 'default'}
              onClick={() => setPreviewMode(!previewMode)}
            >
              Preview
            </Button>
          </Tooltip>
          <Tooltip title="Settings">
            <Button icon={<SettingOutlined />} />
          </Tooltip>
          <Tooltip title="Fullscreen (F11)">
            <Button
              icon={fullscreen ? <CompressOutlined /> : <FullscreenOutlined />}
              onClick={() => setFullscreen(!fullscreen)}
            />
          </Tooltip>
        </ToolbarSection>
      </Toolbar>

      <Layout>
        {/* Component Palette */}
        {!previewMode && (
          <PaletteContainer width={300} theme="light">
            <EnhancedComponentPalette
              onAddComponent={(type) => {
                const newComponent = {
                  id: `${type}-${Date.now()}`,
                  type,
                  props: getDefaultProps(type),
                  createdAt: new Date().toISOString()
                };
                dispatch(addComponent(newComponent));
                message.success(`${type} component added`);
              }}
              onDragStart={(component) => {
                setDraggedComponent(component);
                handleDragStart(null, component);
              }}
              onDragEnd={() => {
                setDraggedComponent(null);
                handleDragEnd();
              }}
            />
          </PaletteContainer>
        )}

        {/* Preview Area */}
        <PreviewContainer ref={dropZoneRef}>
          <EnhancedPreviewArea
            components={components}
            onSelectComponent={handleComponentSelect}
            onDeleteComponent={handleComponentDelete}
            onUpdateComponent={handleComponentUpdate}
            previewMode={previewMode}
            selectedComponentId={selectedItems[0]}
            onDrop={handleComponentDrop}
          />

          {/* Loading Overlay */}
          {isLoading && (
            <LoadingOverlay>
              <Space direction="vertical" align="center">
                <Spin size="large" />
                <span>{loadingMessage}</span>
              </Space>
            </LoadingOverlay>
          )}
        </PreviewContainer>
      </Layout>

      {/* Visual Feedback */}
      <DragVisualFeedback
        isDragging={isDragging}
        isOver={isOver}
        isValid={validDropZone}
        dropPosition={dropPosition}
        draggedComponent={draggedComponent}
        showSuccess={showSuccess}
        successMessage="Component added successfully!"
      />

      {/* Context Menu */}
      <ContextualMenu
        visible={contextMenu.visible}
        x={contextMenu.x}
        y={contextMenu.y}
        onClose={hideContextMenu}
        selectedComponent={components.find(c => c.id === selectedItems[0])}
        selectedComponents={components.filter(c => selectedItems.includes(c.id))}
        onCopy={handleCopy}
        onPaste={handlePaste}
        onDelete={handleComponentDelete}
        clipboardHasData={clipboardHasData}
      />
    </BuilderContainer>
  );
};

export default EnhancedComponentBuilder;
