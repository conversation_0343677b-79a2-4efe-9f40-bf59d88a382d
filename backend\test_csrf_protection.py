#!/usr/bin/env python
"""
CSRF Protection Test Script

This script tests the CSRF protection implementation in the App Builder application.
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app_builder_201.settings')
django.setup()

from django.test import Client, RequestFactory
from django.middleware.csrf import get_token
from django.contrib.auth.models import User
import json


def test_csrf_token_generation():
    """Test that CSRF tokens can be generated"""
    print("🔒 Testing CSRF token generation...")
    
    try:
        rf = RequestFactory()
        request = rf.get('/')
        token = get_token(request)
        
        if token:
            print(f"✅ CSRF token generated successfully: {token[:10]}...")
            return True
        else:
            print("❌ Failed to generate CSRF token")
            return False
    except Exception as e:
        print(f"❌ Error generating CSRF token: {e}")
        return False


def test_csrf_endpoint():
    """Test the CSRF token endpoint"""
    print("🔒 Testing CSRF token endpoint...")
    
    try:
        client = Client()
        response = client.get('/api/csrf-token/')
        
        if response.status_code == 200:
            data = json.loads(response.content.decode())
            if 'csrfToken' in data and data['csrfToken']:
                print(f"✅ CSRF endpoint working: {data['csrfToken'][:10]}...")
                return True
            else:
                print("❌ CSRF endpoint response missing token")
                return False
        else:
            print(f"❌ CSRF endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing CSRF endpoint: {e}")
        return False


def test_csrf_protection_enforcement():
    """Test that CSRF protection is enforced"""
    print("🔒 Testing CSRF protection enforcement...")

    try:
        client = Client(enforce_csrf_checks=True)

        # Try to make a POST request to save_app_data without CSRF token
        response = client.post('/api/save_app_data/',
                             data=json.dumps({'app': {'name': 'Test App'}}),
                             content_type='application/json')

        # Should get 403 Forbidden due to missing CSRF token
        if response.status_code == 403:
            print("✅ CSRF protection working - request blocked without token")
            return True
        elif response.status_code == 401:
            print("✅ CSRF protection working - authentication required (CSRF would be checked after auth)")
            return True
        else:
            print(f"⚠️ CSRF protection may not be working - got status {response.status_code}")
            print(f"Response content: {response.content.decode()[:200]}...")
            return False
    except Exception as e:
        print(f"❌ Error testing CSRF protection: {e}")
        return False


def test_csrf_protection_with_token():
    """Test that requests work with valid CSRF token"""
    print("🔒 Testing CSRF protection with valid token...")

    try:
        client = Client(enforce_csrf_checks=True)

        # Get CSRF token first
        response = client.get('/api/csrf-token/')
        if response.status_code != 200:
            print("❌ Failed to get CSRF token")
            return False

        data = json.loads(response.content.decode())
        csrf_token = data['csrfToken']

        # Make a POST request to save_app_data with CSRF token
        response = client.post('/api/save_app_data/',
                             data=json.dumps({'app': {'name': 'Test App'}}),
                             content_type='application/json',
                             HTTP_X_CSRFTOKEN=csrf_token)

        # Should not get 403 (CSRF failure), but might get 401 (auth required)
        if response.status_code == 403:
            print("❌ CSRF protection blocking requests even with valid token")
            return False
        elif response.status_code == 401:
            print("✅ CSRF protection allows requests with valid token (auth required)")
            return True
        else:
            print(f"✅ CSRF protection allows requests with valid token (status: {response.status_code})")
            return True
    except Exception as e:
        print(f"❌ Error testing CSRF with token: {e}")
        return False


def test_csrf_middleware_exemptions():
    """Test that exempt endpoints don't require CSRF tokens"""
    print("🔒 Testing CSRF middleware exemptions...")
    
    try:
        client = Client(enforce_csrf_checks=True)
        
        # Test exempt endpoints
        exempt_endpoints = [
            '/api/health/',
            '/api/status/',
            '/api/errors/',
        ]
        
        results = []
        for endpoint in exempt_endpoints:
            try:
                # Try GET first to see if endpoint exists
                get_response = client.get(endpoint)
                if get_response.status_code == 404:
                    print(f"⚠️ Endpoint {endpoint} not found, skipping")
                    continue
                
                # For endpoints that exist, they should allow requests without CSRF
                print(f"✅ Exempt endpoint {endpoint} accessible")
                results.append(True)
            except Exception as e:
                print(f"⚠️ Error testing exempt endpoint {endpoint}: {e}")
                results.append(False)
        
        return len(results) > 0 and all(results)
    except Exception as e:
        print(f"❌ Error testing CSRF exemptions: {e}")
        return False


def test_csrf_with_authenticated_user():
    """Test CSRF protection with authenticated user"""
    print("🔒 Testing CSRF protection with authenticated user...")

    try:
        from django.contrib.auth.models import User

        # Create a test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={'email': '<EMAIL>'}
        )
        if created:
            user.set_password('testpass123')
            user.save()

        client = Client(enforce_csrf_checks=True)

        # Login the user
        login_success = client.login(username='testuser', password='testpass123')
        if not login_success:
            print("⚠️ Could not login test user")
            return False

        # Get CSRF token
        response = client.get('/api/csrf-token/')
        if response.status_code != 200:
            print("❌ Failed to get CSRF token")
            return False

        data = json.loads(response.content.decode())
        csrf_token = data['csrfToken']

        # Try POST request without CSRF token (should fail)
        response = client.post('/api/save_app_data/',
                             data=json.dumps({'app': {'name': 'Test App'}}),
                             content_type='application/json')

        if response.status_code == 403:
            print("✅ CSRF protection blocks authenticated requests without token")

            # Now try with CSRF token (should work)
            response = client.post('/api/save_app_data/',
                                 data=json.dumps({'app': {'name': 'Test App'}}),
                                 content_type='application/json',
                                 HTTP_X_CSRFTOKEN=csrf_token)

            if response.status_code != 403:
                print("✅ CSRF protection allows authenticated requests with token")
                return True
            else:
                print("❌ CSRF protection blocks authenticated requests even with token")
                return False
        else:
            print(f"⚠️ Expected 403 for request without CSRF token, got {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error testing CSRF with authenticated user: {e}")
        return False


def run_all_tests():
    """Run all CSRF protection tests"""
    print("🔒 Running CSRF Protection Test Suite")
    print("=" * 50)

    tests = [
        ("CSRF Token Generation", test_csrf_token_generation),
        ("CSRF Token Endpoint", test_csrf_endpoint),
        ("CSRF Protection Enforcement", test_csrf_protection_enforcement),
        ("CSRF Protection with Token", test_csrf_protection_with_token),
        ("CSRF Middleware Exemptions", test_csrf_middleware_exemptions),
        ("CSRF with Authenticated User", test_csrf_with_authenticated_user),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        result = test_func()
        results.append(result)
        print()
    
    print("=" * 50)
    print("🔒 CSRF Protection Test Results:")
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(results)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All CSRF protection tests passed!")
        return True
    else:
        print("⚠️ Some CSRF protection tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
