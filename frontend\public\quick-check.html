<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick React Check - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid;
            background: rgba(255, 255, 255, 0.1);
        }
        .success { border-color: #28a745; }
        .error { border-color: #dc3545; }
        .warning { border-color: #ffc107; }
        .info { border-color: #17a2b8; }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        iframe {
            width: 100%;
            height: 400px;
            border: none;
            border-radius: 8px;
            background: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .results {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Quick React Check</h1>
        <p>Fast verification that the React App Builder is working</p>
        
        <div id="status">
            <div class="status info">🔄 Checking React application...</div>
        </div>

        <div>
            <button onclick="quickCheck()">🔄 Quick Check</button>
            <button onclick="openMainApp()">🚀 Open Main App</button>
            <button onclick="viewSource()">📋 View Source</button>
        </div>

        <h3>📱 App Preview</h3>
        <iframe id="app-frame" src="/" title="React App"></iframe>

        <h3>📋 Check Results</h3>
        <div class="results" id="results">
            Click "Quick Check" to start...
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            document.getElementById('status').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
        }

        function log(message) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `[${timestamp}] ${message}<br>`;
            results.scrollTop = results.scrollHeight;
        }

        async function quickCheck() {
            updateStatus('🔄 Running quick check...', 'info');
            document.getElementById('results').innerHTML = '';
            
            let allPassed = true;
            
            try {
                // 1. Check if main page loads
                log('1. Testing main page...');
                const response = await fetch('/');
                if (response.ok) {
                    log('✅ Main page loads successfully');
                    
                    const html = await response.text();
                    
                    // 2. Check for script tags
                    const scriptMatch = html.match(/static\/js\/main\.[a-f0-9]+\.js/);
                    if (scriptMatch) {
                        log(`✅ Main bundle found: ${scriptMatch[0]}`);
                        
                        // 3. Test bundle loading
                        const bundleResponse = await fetch(`/${scriptMatch[0]}`);
                        if (bundleResponse.ok) {
                            log('✅ Bundle loads successfully');
                        } else {
                            log('❌ Bundle failed to load');
                            allPassed = false;
                        }
                    } else {
                        log('❌ No main bundle found in HTML');
                        allPassed = false;
                    }
                    
                    // 4. Check for CSS
                    const cssMatch = html.match(/static\/css\/main\.[a-f0-9]+\.css/);
                    if (cssMatch) {
                        log(`✅ CSS bundle found: ${cssMatch[0]}`);
                    } else {
                        log('⚠️ No CSS bundle found');
                    }
                    
                    // 5. Check for root element
                    if (html.includes('id="root"')) {
                        log('✅ Root element found in HTML');
                    } else {
                        log('❌ Root element not found');
                        allPassed = false;
                    }
                    
                } else {
                    log(`❌ Main page failed to load: ${response.status}`);
                    allPassed = false;
                }
                
                // 6. Check iframe content
                log('6. Checking iframe content...');
                await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for React to load
                
                try {
                    const iframe = document.getElementById('app-frame');
                    const iframeDoc = iframe.contentDocument;
                    
                    if (iframeDoc) {
                        const rootElement = iframeDoc.getElementById('root');
                        if (rootElement && rootElement.children.length > 0) {
                            log(`✅ React app is rendering (${rootElement.children.length} elements)`);
                        } else {
                            log('⚠️ Root element empty or not found');
                        }
                        
                        // Check for React global
                        if (iframe.contentWindow.React) {
                            log('✅ React is available globally');
                        } else {
                            log('⚠️ React not available globally');
                        }
                    } else {
                        log('⚠️ Cannot access iframe content');
                    }
                } catch (error) {
                    log(`⚠️ Iframe check error: ${error.message}`);
                }
                
                // Final result
                if (allPassed) {
                    updateStatus('🎉 All checks passed! React app is working!', 'success');
                    log('🎉 RESULT: React application is working correctly!');
                } else {
                    updateStatus('⚠️ Some issues found, but app may still work', 'warning');
                    log('⚠️ RESULT: Some issues found, check details above');
                }
                
            } catch (error) {
                updateStatus('❌ Check failed with error', 'error');
                log(`❌ ERROR: ${error.message}`);
            }
        }

        function openMainApp() {
            window.open('/', '_blank');
        }

        function viewSource() {
            fetch('/')
                .then(response => response.text())
                .then(html => {
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(`<pre>${html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>`);
                    newWindow.document.title = 'Main Page Source';
                });
        }

        // Auto-run check when page loads
        window.addEventListener('load', () => {
            setTimeout(quickCheck, 1000);
        });
    </script>
</body>
</html>
