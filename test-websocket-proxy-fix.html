<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Proxy Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebSocket Proxy Fix Test</h1>
    <p>This test verifies that the webpack proxy configuration correctly handles WebSocket errors without throwing "res.writeHead is not a function" errors.</p>

    <div class="test-section info">
        <h3>Test Scenarios</h3>
        <button onclick="testValidWebSocket()">Test Valid WebSocket Connection</button>
        <button onclick="testInvalidWebSocket()">Test Invalid WebSocket URL</button>
        <button onclick="testBackendDown()">Test Backend Down Scenario</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testValidWebSocket() {
            log('Testing valid WebSocket connection to /ws/app_builder/', 'info');
            
            try {
                const ws = new WebSocket('ws://localhost:3000/ws/app_builder/');
                
                ws.onopen = function(event) {
                    log('✅ WebSocket connection opened successfully', 'success');
                    ws.close();
                };
                
                ws.onclose = function(event) {
                    log(`WebSocket connection closed: code=${event.code}, reason=${event.reason}`, 'info');
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error.message || 'Unknown error'}`, 'error');
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        log('⏰ WebSocket connection timeout', 'error');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ Exception creating WebSocket: ${error.message}`, 'error');
            }
        }

        function testInvalidWebSocket() {
            log('Testing invalid WebSocket URL to trigger proxy error handling', 'info');
            
            try {
                const ws = new WebSocket('ws://localhost:3000/ws/invalid_endpoint/');
                
                ws.onopen = function(event) {
                    log('⚠️ Unexpected: Invalid WebSocket connection opened', 'error');
                    ws.close();
                };
                
                ws.onclose = function(event) {
                    log(`Invalid WebSocket closed: code=${event.code}, reason=${event.reason}`, 'info');
                };
                
                ws.onerror = function(error) {
                    log(`✅ Expected error for invalid WebSocket: ${error.message || 'Connection failed'}`, 'success');
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        log('⏰ Invalid WebSocket connection timeout (expected)', 'info');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                log(`✅ Expected exception for invalid WebSocket: ${error.message}`, 'success');
            }
        }

        function testBackendDown() {
            log('Testing WebSocket connection when backend might be down', 'info');
            
            try {
                // Try to connect to a port that's likely not running
                const ws = new WebSocket('ws://localhost:3000/ws/test/');
                
                ws.onopen = function(event) {
                    log('⚠️ Unexpected: WebSocket connection opened when backend should be down', 'error');
                    ws.close();
                };
                
                ws.onclose = function(event) {
                    log(`Backend down WebSocket closed: code=${event.code}, reason=${event.reason}`, 'info');
                };
                
                ws.onerror = function(error) {
                    log(`✅ Expected error when backend is down: ${error.message || 'Connection failed'}`, 'success');
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        log('⏰ Backend down WebSocket timeout (expected)', 'info');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                log(`✅ Expected exception when backend is down: ${error.message}`, 'success');
            }
        }

        // Check if we're running through the webpack dev server
        function checkProxyStatus() {
            log('Checking if running through webpack dev server with proxy...', 'info');
            
            fetch('/api/health/')
                .then(response => {
                    if (response.ok) {
                        log('✅ API proxy is working - backend is reachable', 'success');
                    } else {
                        log(`⚠️ API proxy returned status: ${response.status}`, 'error');
                    }
                })
                .catch(error => {
                    log(`❌ API proxy error: ${error.message}`, 'error');
                });
        }

        // Run initial checks
        window.onload = function() {
            log('WebSocket Proxy Fix Test Started', 'info');
            log('This test verifies that webpack proxy handles WebSocket errors correctly', 'info');
            log('The fix ensures res.writeHead is only called on valid HTTP response objects', 'info');
            checkProxyStatus();
        };
    </script>
</body>
</html>
