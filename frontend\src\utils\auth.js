/**
 * Authentication Utilities
 * 
 * This module provides utilities for user authentication.
 */

import apiClient from '../services/ApiClient';

// Local storage keys
const TOKEN_KEY = 'app_auth_token';
const REFRESH_TOKEN_KEY = 'app_refresh_token';
const USER_KEY = 'app_user';

/**
 * Get authentication token
 * @returns {string|null} Authentication token
 */
export const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * Set authentication token
 * @param {string} token - Authentication token
 */
export const setToken = (token) => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * Get refresh token
 * @returns {string|null} Refresh token
 */
export const getRefreshToken = () => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Set refresh token
 * @param {string} token - Refresh token
 */
export const setRefreshToken = (token) => {
  localStorage.setItem(REFRESH_TOKEN_KEY, token);
};

/**
 * Get user data
 * @returns {Object|null} User data
 */
export const getUser = () => {
  const userJson = localStorage.getItem(USER_KEY);

  if (userJson) {
    try {
      return JSON.parse(userJson);
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }

  return null;
};

/**
 * Set user data
 * @param {Object} user - User data
 */
export const setUser = (user) => {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * Clear authentication data
 */
export const clearAuth = () => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(USER_KEY);
};

/**
 * Check if user is authenticated
 * @returns {boolean} Whether the user is authenticated
 */
export const isAuthenticated = () => {
  return !!getToken();
};

/**
 * Login user
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Promise<Object>} Login result
 */
export const login = async (username, password) => {
  try {
    // Try JWT login first
    const response = await apiClient.post('/auth/login/', { username, password });

    if (response.access) {
      // JWT response format
      setToken(response.access);

      if (response.refresh) {
        setRefreshToken(response.refresh);
      }

      // Get user profile after successful login
      try {
        const userResponse = await apiClient.get('/auth/profile/');
        if (userResponse) {
          setUser(userResponse);
        }
      } catch (profileError) {
        console.warn('Failed to fetch user profile:', profileError);
      }

      return {
        success: true,
        user: response.user || { username }
      };
    } else if (response.token) {
      // Token auth response format
      setToken(response.token);

      if (response.user) {
        setUser(response.user);
      }

      return {
        success: true,
        user: response.user
      };
    }

    return {
      success: false,
      error: 'Invalid response from server'
    };
  } catch (error) {
    console.error('Login error:', error);

    return {
      success: false,
      error: error.response?.data?.detail || error.message || 'Login failed'
    };
  }
};

/**
 * Register user
 * @param {Object} userData - User data
 * @returns {Promise<Object>} Registration result
 */
export const register = async (userData) => {
  try {
    // Try JWT register endpoint first
    const response = await apiClient.post('/auth/register/', userData);

    if (response.access) {
      // JWT response format
      setToken(response.access);

      if (response.refresh) {
        setRefreshToken(response.refresh);
      }

      // Get user profile after successful registration
      try {
        const userResponse = await apiClient.get('/auth/profile/');
        if (userResponse) {
          setUser(userResponse);
        }
      } catch (profileError) {
        console.warn('Failed to fetch user profile:', profileError);
      }

      return {
        success: true,
        user: response.user || { username: userData.username }
      };
    } else if (response.token) {
      // Token auth response format
      setToken(response.token);

      if (response.user) {
        setUser(response.user);
      }

      return {
        success: true,
        user: response.user
      };
    } else if (response.success) {
      return {
        success: true,
        user: response.user
      };
    }

    return {
      success: false,
      error: response.error || 'Registration failed'
    };
  } catch (error) {
    console.error('Registration error:', error);

    return {
      success: false,
      error: error.response?.data?.detail || error.response?.data?.message || error.message || 'Registration failed'
    };
  }
};

/**
 * Logout user
 * @returns {Promise<Object>} Logout result
 */
export const logout = async () => {
  try {
    // Try to notify the server about logout
    await apiClient.post('/auth/logout');
  } catch (error) {
    console.warn('Logout notification failed:', error);
  } finally {
    // Clear authentication data
    clearAuth();
  }

  return {
    success: true
  };
};

/**
 * Refresh authentication token
 * @returns {Promise<string|null>} New authentication token
 */
export const refreshToken = async () => {
  try {
    const refreshTokenValue = getRefreshToken();

    if (!refreshTokenValue) {
      throw new Error('No refresh token available');
    }

    // Try JWT refresh endpoint
    const response = await apiClient.post('/auth/token/refresh/', { refresh: refreshTokenValue });

    if (response.access) {
      setToken(response.access);

      if (response.refresh) {
        setRefreshToken(response.refresh);
      }

      return response.access;
    } else if (response.token) {
      // Fallback to token auth format
      setToken(response.token);

      if (response.refreshToken) {
        setRefreshToken(response.refreshToken);
      }

      return response.token;
    }

    return null;
  } catch (error) {
    console.error('Token refresh error:', error);

    // Clear authentication data on refresh failure
    clearAuth();

    return null;
  }
};

/**
 * Get user profile
 * @returns {Promise<Object>} User profile
 */
export const getUserProfile = async () => {
  try {
    return await apiClient.get('/auth/profile');
  } catch (error) {
    console.error('Get user profile error:', error);

    return null;
  }
};

/**
 * Update user profile
 * @param {Object} profileData - Profile data
 * @returns {Promise<Object>} Update result
 */
export const updateUserProfile = async (profileData) => {
  try {
    const response = await apiClient.put('/auth/profile', profileData);

    if (response.user) {
      setUser(response.user);

      return {
        success: true,
        user: response.user
      };
    }

    return {
      success: false,
      error: 'Invalid response from server'
    };
  } catch (error) {
    console.error('Update profile error:', error);

    return {
      success: false,
      error: error.message || 'Update profile failed'
    };
  }
};

/**
 * Change password
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Change result
 */
export const changePassword = async (currentPassword, newPassword) => {
  try {
    const response = await apiClient.post('/auth/change-password', {
      currentPassword,
      newPassword
    });

    return {
      success: response.success,
      error: response.error
    };
  } catch (error) {
    console.error('Change password error:', error);

    return {
      success: false,
      error: error.message || 'Change password failed'
    };
  }
};

/**
 * Request password reset
 * @param {string} email - User email
 * @returns {Promise<Object>} Request result
 */
export const requestPasswordReset = async (email) => {
  try {
    const response = await apiClient.post('/auth/reset-password', { email });

    return {
      success: response.success,
      error: response.error
    };
  } catch (error) {
    console.error('Password reset request error:', error);

    return {
      success: false,
      error: error.message || 'Password reset request failed'
    };
  }
};

/**
 * Reset password
 * @param {string} token - Reset token
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Reset result
 */
export const resetPassword = async (token, newPassword) => {
  try {
    const response = await apiClient.post('/auth/reset-password/confirm', {
      token,
      newPassword
    });

    return {
      success: response.success,
      error: response.error
    };
  } catch (error) {
    console.error('Password reset error:', error);

    return {
      success: false,
      error: error.message || 'Password reset failed'
    };
  }
};
