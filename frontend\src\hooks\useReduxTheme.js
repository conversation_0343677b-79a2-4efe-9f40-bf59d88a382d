import { useSelector, useDispatch } from 'react-redux';
import { 
  setActiveTheme, 
  addTheme, 
  updateTheme, 
  removeTheme, 
  toggleAutoApplyTheme,
  resetThemes,
  selectThemes, 
  selectActiveTheme, 
  selectActiveThemeData,
  selectUserPreferences
} from '../redux/slices/themeSlice';

/**
 * Custom hook to access and manage themes from Redux store
 * @returns {Object} Theme-related data and functions
 */
export const useReduxTheme = () => {
  const dispatch = useDispatch();
  
  // Get theme data from Redux store
  const themes = useSelector(selectThemes);
  const activeThemeId = useSelector(selectActiveTheme);
  const activeTheme = useSelector(selectActiveThemeData);
  const userPreferences = useSelector(selectUserPreferences);
  
  // Theme actions
  const setTheme = (themeId) => dispatch(setActiveTheme(themeId));
  const createTheme = (theme) => dispatch(addTheme(theme));
  const editTheme = (theme) => dispatch(updateTheme(theme));
  const deleteTheme = (themeId) => dispatch(removeTheme(themeId));
  const toggleAutoApply = () => dispatch(toggleAutoApplyTheme());
  const resetToDefaults = () => dispatch(resetThemes());
  
  // Determine if the current theme is light or dark
  const isDarkMode = activeTheme?.backgroundColor === '#1F2937' || 
                    activeTheme?.backgroundColor === '#141414';
  
  // Toggle between light and dark mode
  const toggleDarkMode = () => {
    const darkThemeId = themes.find(theme => 
      theme.backgroundColor === '#1F2937' || theme.backgroundColor === '#141414'
    )?.id;
    
    const lightThemeId = themes.find(theme => 
      theme.backgroundColor === '#FFFFFF' || theme.backgroundColor === '#FFFBEB'
    )?.id;
    
    if (isDarkMode && lightThemeId) {
      dispatch(setActiveTheme(lightThemeId));
    } else if (!isDarkMode && darkThemeId) {
      dispatch(setActiveTheme(darkThemeId));
    }
  };
  
  // Get CSS variables based on the active theme
  const getCssVariables = () => {
    if (!activeTheme) return {};
    
    return {
      '--primary-color': activeTheme.primaryColor,
      '--secondary-color': activeTheme.secondaryColor,
      '--background-color': activeTheme.backgroundColor,
      '--text-color': activeTheme.textColor,
      '--font-family': activeTheme.fontFamily,
    };
  };
  
  return {
    // Theme data
    themes,
    activeThemeId,
    activeTheme,
    userPreferences,
    isDarkMode,
    
    // Theme actions
    setTheme,
    createTheme,
    editTheme,
    deleteTheme,
    toggleAutoApply,
    toggleDarkMode,
    resetToDefaults,
    
    // Utilities
    getCssVariables,
  };
};

export default useReduxTheme;
