#!/usr/bin/env python3
"""
WebSocket Backend Test Script for App Builder 201
Tests WebSocket functionality from the backend perspective
"""

import asyncio
import websockets
import json
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebSocketTester:
    def __init__(self, base_url="ws://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        
    async def test_endpoint(self, endpoint, test_name):
        """Test a specific WebSocket endpoint"""
        url = f"{self.base_url}{endpoint}"
        logger.info(f"Testing {test_name}: {url}")
        
        try:
            # Connect to WebSocket
            async with websockets.connect(url) as websocket:
                logger.info(f"✅ {test_name}: Connected successfully")
                
                # Send test message
                test_message = {
                    "type": "test",
                    "data": {
                        "test_name": test_name,
                        "timestamp": datetime.now().isoformat(),
                        "message": "Hello from Python WebSocket tester"
                    }
                }
                
                await websocket.send(json.dumps(test_message))
                logger.info(f"📤 {test_name}: Test message sent")
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    logger.info(f"📥 {test_name}: Response received: {response_data}")
                    
                    self.test_results.append({
                        "endpoint": endpoint,
                        "test_name": test_name,
                        "status": "success",
                        "response": response_data
                    })
                    
                except asyncio.TimeoutError:
                    logger.warning(f"⏰ {test_name}: No response received within timeout")
                    self.test_results.append({
                        "endpoint": endpoint,
                        "test_name": test_name,
                        "status": "timeout",
                        "response": None
                    })
                
        except Exception as e:
            logger.error(f"❌ {test_name}: Connection failed - {str(e)}")
            self.test_results.append({
                "endpoint": endpoint,
                "test_name": test_name,
                "status": "error",
                "error": str(e)
            })
    
    async def test_echo_functionality(self, endpoint="/ws/echo/"):
        """Test echo functionality"""
        url = f"{self.base_url}{endpoint}"
        logger.info(f"Testing Echo Functionality: {url}")
        
        try:
            async with websockets.connect(url) as websocket:
                # Send multiple echo messages
                for i in range(5):
                    echo_message = {
                        "type": "echo",
                        "data": {
                            "sequence": i,
                            "timestamp": datetime.now().isoformat(),
                            "payload": f"Echo test message {i}"
                        }
                    }
                    
                    await websocket.send(json.dumps(echo_message))
                    logger.info(f"📤 Echo {i}: Message sent")
                    
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        response_data = json.loads(response)
                        logger.info(f"📥 Echo {i}: Response received")
                        
                        # Verify echo response
                        if response_data.get("type") == "echo_response":
                            logger.info(f"✅ Echo {i}: Echo functionality working")
                        else:
                            logger.warning(f"⚠️ Echo {i}: Unexpected response type")
                            
                    except asyncio.TimeoutError:
                        logger.warning(f"⏰ Echo {i}: No response received")
                
                self.test_results.append({
                    "endpoint": endpoint,
                    "test_name": "Echo Functionality",
                    "status": "success",
                    "message": "Echo test completed"
                })
                
        except Exception as e:
            logger.error(f"❌ Echo test failed: {str(e)}")
            self.test_results.append({
                "endpoint": endpoint,
                "test_name": "Echo Functionality",
                "status": "error",
                "error": str(e)
            })
    
    async def test_connection_stability(self, endpoint="/ws/test/"):
        """Test connection stability with multiple messages"""
        url = f"{self.base_url}{endpoint}"
        logger.info(f"Testing Connection Stability: {url}")
        
        try:
            async with websockets.connect(url) as websocket:
                messages_sent = 0
                messages_received = 0
                
                # Send 20 messages rapidly
                for i in range(20):
                    message = {
                        "type": "stability_test",
                        "data": {
                            "sequence": i,
                            "timestamp": datetime.now().isoformat(),
                            "payload": f"Stability test message {i}"
                        }
                    }
                    
                    await websocket.send(json.dumps(message))
                    messages_sent += 1
                    
                    # Small delay to avoid overwhelming
                    await asyncio.sleep(0.1)
                
                logger.info(f"📤 Stability test: {messages_sent} messages sent")
                
                # Try to receive responses
                for i in range(messages_sent):
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        messages_received += 1
                    except asyncio.TimeoutError:
                        break
                
                logger.info(f"📥 Stability test: {messages_received} responses received")
                
                success_rate = (messages_received / messages_sent) * 100 if messages_sent > 0 else 0
                logger.info(f"📊 Stability test: {success_rate:.1f}% success rate")
                
                self.test_results.append({
                    "endpoint": endpoint,
                    "test_name": "Connection Stability",
                    "status": "success",
                    "messages_sent": messages_sent,
                    "messages_received": messages_received,
                    "success_rate": success_rate
                })
                
        except Exception as e:
            logger.error(f"❌ Stability test failed: {str(e)}")
            self.test_results.append({
                "endpoint": endpoint,
                "test_name": "Connection Stability",
                "status": "error",
                "error": str(e)
            })
    
    async def test_app_builder_consumer(self):
        """Test App Builder specific WebSocket functionality"""
        url = f"{self.base_url}/ws/app_builder/"
        logger.info(f"Testing App Builder Consumer: {url}")
        
        try:
            async with websockets.connect(url) as websocket:
                # Test app builder specific messages
                app_messages = [
                    {
                        "type": "app_data_request",
                        "data": {"app_id": 1}
                    },
                    {
                        "type": "component_update",
                        "data": {
                            "component_id": "test-component",
                            "properties": {"text": "Updated text"}
                        }
                    },
                    {
                        "type": "collaboration_join",
                        "data": {"user_id": "test-user"}
                    }
                ]
                
                for i, message in enumerate(app_messages):
                    await websocket.send(json.dumps(message))
                    logger.info(f"📤 App Builder {i}: {message['type']} sent")
                    
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        response_data = json.loads(response)
                        logger.info(f"📥 App Builder {i}: Response received")
                    except asyncio.TimeoutError:
                        logger.warning(f"⏰ App Builder {i}: No response received")
                
                self.test_results.append({
                    "endpoint": "/ws/app_builder/",
                    "test_name": "App Builder Consumer",
                    "status": "success",
                    "message": "App Builder WebSocket test completed"
                })
                
        except Exception as e:
            logger.error(f"❌ App Builder test failed: {str(e)}")
            self.test_results.append({
                "endpoint": "/ws/app_builder/",
                "test_name": "App Builder Consumer",
                "status": "error",
                "error": str(e)
            })
    
    async def run_all_tests(self):
        """Run all WebSocket tests"""
        logger.info("🚀 Starting comprehensive WebSocket tests...")
        
        # Test basic endpoints
        endpoints = [
            ("/ws/test/", "Test Consumer"),
            ("/ws/echo/", "Echo Consumer"),
            ("/ws/health/", "Health Check Consumer"),
            ("/ws/app_builder/", "App Builder Consumer"),
            ("/ws/simple/", "Simple Test Consumer")
        ]
        
        for endpoint, name in endpoints:
            await self.test_endpoint(endpoint, name)
            await asyncio.sleep(1)  # Small delay between tests
        
        # Test specific functionality
        await self.test_echo_functionality()
        await asyncio.sleep(1)
        
        await self.test_connection_stability()
        await asyncio.sleep(1)
        
        await self.test_app_builder_consumer()
        
        # Print summary
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print a summary of all test results"""
        logger.info("\n" + "="*60)
        logger.info("🔌 WEBSOCKET TEST SUMMARY")
        logger.info("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "success"])
        failed_tests = len([r for r in self.test_results if r["status"] == "error"])
        timeout_tests = len([r for r in self.test_results if r["status"] == "timeout"])
        
        logger.info(f"📊 Total Tests: {total_tests}")
        logger.info(f"✅ Successful: {successful_tests}")
        logger.info(f"❌ Failed: {failed_tests}")
        logger.info(f"⏰ Timeouts: {timeout_tests}")
        logger.info(f"📈 Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        logger.info("\n📋 Detailed Results:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "success" else "❌" if result["status"] == "error" else "⏰"
            logger.info(f"{status_icon} {result['test_name']}: {result['status']}")
            if result["status"] == "error":
                logger.info(f"   Error: {result.get('error', 'Unknown error')}")
        
        logger.info("\n🎉 WebSocket testing completed!")

async def main():
    """Main test function"""
    tester = WebSocketTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
