"""
Unit tests for Django views in the App Builder application.
Tests view functionality, authentication, validation, and response handling.
"""

import pytest
import json
from unittest.mock import patch, Mock
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from model_bakery import baker

from my_app.models import App
from my_app import views


@pytest.mark.django_db
class TestBasicViews:
    """Test cases for basic view functions."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_index_view(self):
        """Test the index view."""
        response = self.client.get('/')
        
        assert response.status_code == 200
        assert b'Welcome to the App Builder API' in response.content
        assert b'API documentation' in response.content

    def test_swagger_ui_view(self):
        """Test the swagger UI view."""
        response = self.client.get('/swagger/')
        
        assert response.status_code == 200

    def test_health_check_view(self):
        """Test the health check endpoint."""
        response = self.client.get('/health/')
        
        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'ok'
        assert data['service'] == 'backend-api'
        assert 'timestamp' in data

    def test_api_status_view(self):
        """Test the API status endpoint."""
        response = self.client.get('/api/status/')
        
        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'ok'
        assert data['version'] == '1.0.0'
        assert 'serverTime' in data
        assert 'environment' in data

    def test_get_csrf_token_view(self):
        """Test the CSRF token endpoint."""
        response = self.client.get('/api/csrf-token/')
        
        assert response.status_code == 200
        data = response.json()
        assert 'csrfToken' in data
        assert data['status'] == 'ok'

    def test_get_server_time_view(self):
        """Test the server time endpoint."""
        response = self.client.get('/api/server-time/')
        
        assert response.status_code == 200
        data = response.json()
        assert 'serverTime' in data
        assert 'timestamp' in data


@pytest.mark.django_db
class TestAppDataViews:
    """Test cases for app data related views."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_get_app_data_with_existing_app(self):
        """Test getting app data when app exists."""
        app_data = {
            'components': [{'id': '1', 'type': 'button', 'props': {'text': 'Click me'}}],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        app = App.objects.create(
            name='Test App',
            user=self.user,
            app_data=json.dumps(app_data)
        )
        
        response = self.client.get('/api/app-data/')
        
        assert response.status_code == 200
        data = response.json()
        assert data == app_data

    def test_get_app_data_no_app_exists(self):
        """Test getting app data when no app exists."""
        response = self.client.get('/api/app-data/')
        
        assert response.status_code == 404
        data = response.json()
        assert data['error'] == 'No app data found'

    def test_save_app_data_update_existing(self):
        """Test saving app data to update existing app."""
        # Create existing app
        existing_app = App.objects.create(
            name='Existing App',
            user=self.user,
            app_data='{"components": []}'
        )
        
        new_app_data = {
            'components': [{'id': '1', 'type': 'input', 'props': {'placeholder': 'Enter text'}}],
            'layouts': [],
            'styles': {'theme': 'dark'},
            'data': {}
        }
        
        response = self.client.post(
            '/api/save-app-data/',
            data=json.dumps(new_app_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['message'] == 'App data updated'
        assert data['app_data'] == new_app_data
        
        # Verify app was updated
        existing_app.refresh_from_db()
        assert json.loads(existing_app.app_data) == new_app_data

    def test_save_app_data_create_new(self):
        """Test saving app data to create new app."""
        new_app_data = {
            'components': [{'id': '1', 'type': 'button', 'props': {'text': 'New Button'}}],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        response = self.client.post(
            '/api/save-app-data/',
            data=json.dumps(new_app_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['message'] == 'App data created'
        assert data['app_data'] == new_app_data
        
        # Verify app was created
        app = App.objects.first()
        assert app is not None
        assert app.name == 'New App'
        assert json.loads(app.app_data) == new_app_data

    def test_save_app_data_invalid_json(self):
        """Test saving app data with invalid JSON."""
        response = self.client.post(
            '/api/save-app-data/',
            data='invalid json',
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data['error'] == 'Invalid JSON data'

    def test_save_app_data_invalid_method(self):
        """Test saving app data with invalid HTTP method."""
        response = self.client.get('/api/save-app-data/')
        
        assert response.status_code == 405
        data = response.json()
        assert data['error'] == 'Method not allowed'


@pytest.mark.django_db
class TestExportViews:
    """Test cases for export related views."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    @patch('my_app.views.AppBuilder')
    def test_export_app_data_json_format(self, mock_app_builder):
        """Test exporting app data in JSON format."""
        # Setup mock
        mock_builder_instance = Mock()
        mock_builder_instance.export.return_value = {'exported': 'data'}
        mock_app_builder.return_value = mock_builder_instance
        
        app_data = {
            'components': [{'type': 'button', 'props': {'text': 'Export Me'}}],
            'layouts': [{'type': 'flex', 'components': [], 'styles': {}}],
            'styles': {'button': {'color': 'blue'}},
            'data': {'title': 'Export Test'}
        }
        
        app = App.objects.create(
            name='Export Test App',
            user=self.user,
            app_data=json.dumps(app_data)
        )
        
        response = self.client.get('/api/export/?format=json')
        
        assert response.status_code == 200
        data = response.json()
        assert data == {'exported': 'data'}
        
        # Verify AppBuilder was called correctly
        mock_app_builder.assert_called_once()
        mock_builder_instance.add_component.assert_called_once_with('button', {'text': 'Export Me'})
        mock_builder_instance.add_layout.assert_called_once_with('flex', [], {})
        mock_builder_instance.add_style.assert_called_once_with('button', {'color': 'blue'})
        mock_builder_instance.add_data.assert_called_once_with('title', 'Export Test')
        mock_builder_instance.export.assert_called_once_with(format='json')

    @patch('my_app.views.AppBuilder')
    def test_export_app_data_code_format(self, mock_app_builder):
        """Test exporting app data in code format."""
        # Setup mock
        mock_builder_instance = Mock()
        mock_builder_instance.export.return_value = 'export const App = () => { return <div>Hello</div>; };'
        mock_app_builder.return_value = mock_builder_instance
        
        app_data = {
            'components': [{'type': 'div', 'props': {'children': 'Hello'}}],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        app = App.objects.create(
            name='Code Export Test',
            user=self.user,
            app_data=json.dumps(app_data)
        )
        
        response = self.client.get('/api/export/?format=react')
        
        assert response.status_code == 200
        data = response.json()
        assert 'code' in data
        assert data['code'] == 'export const App = () => { return <div>Hello</div>; };'

    def test_export_app_data_no_app_exists(self):
        """Test exporting when no app exists."""
        response = self.client.get('/api/export/')
        
        assert response.status_code == 404
        data = response.json()
        assert data['error'] == 'No app data found'

    def test_export_app_data_default_format(self):
        """Test exporting with default format (json)."""
        app = App.objects.create(
            name='Default Format Test',
            user=self.user,
            app_data='{"components": []}'
        )
        
        with patch('my_app.views.AppBuilder') as mock_app_builder:
            mock_builder_instance = Mock()
            mock_builder_instance.export.return_value = {'default': 'format'}
            mock_app_builder.return_value = mock_builder_instance
            
            response = self.client.get('/api/export/')
            
            assert response.status_code == 200
            mock_builder_instance.export.assert_called_once_with(format='json')


@pytest.mark.django_db
class TestPluginViews:
    """Test cases for plugin related views."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.client = Client()

    def test_get_plugin_props_ai_plugin(self):
        """Test getting props for AI plugin."""
        response = self.client.get('/api/plugins/AIPlugin/props/')
        
        assert response.status_code == 200
        data = response.json()
        assert data == {"prompt": "", "apiKey": ""}

    def test_get_plugin_props_unknown_plugin(self):
        """Test getting props for unknown plugin."""
        response = self.client.get('/api/plugins/UnknownPlugin/props/')
        
        assert response.status_code == 404
        data = response.json()
        assert data['error'] == 'Plugin not found'


@pytest.mark.django_db
class TestAuthenticationViews:
    """Test cases for authentication related views."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_custom_login_api_valid_credentials(self):
        """Test custom login API with valid credentials."""
        response = self.client.post('/api/custom-login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True

    def test_custom_login_api_invalid_credentials(self):
        """Test custom login API with invalid credentials."""
        response = self.client.post('/api/custom-login/', {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is False
        assert data['error'] == 'Invalid credentials'

    def test_custom_login_api_invalid_method(self):
        """Test custom login API with invalid HTTP method."""
        response = self.client.get('/api/custom-login/')
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is False
        assert data['error'] == 'Invalid request method'

    def test_login_test_view(self):
        """Test login test page view."""
        response = self.client.get('/login-test/')
        
        assert response.status_code == 200

    def test_custom_login_view(self):
        """Test custom login page view."""
        response = self.client.get('/custom-login/')

        assert response.status_code == 200


@pytest.mark.django_db
class TestAIViews:
    """Test cases for AI-related views."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.api_client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.token = Token.objects.create(user=self.user)

    def test_generate_ai_suggestions_unauthenticated(self):
        """Test AI suggestions endpoint without authentication."""
        response = self.api_client.post('/api/ai/suggestions/', {
            'prompt': 'Suggest improvements for my app'
        })

        assert response.status_code == 401

    def test_generate_ai_suggestions_missing_prompt(self):
        """Test AI suggestions endpoint with missing prompt."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        response = self.api_client.post('/api/ai/suggestions/', {})

        assert response.status_code == 400

    @patch.dict('os.environ', {'OPENAI_API_KEY': 'test-api-key'})
    @patch('my_app.views.AIPlugin')
    def test_generate_ai_suggestions_success(self, mock_ai_plugin):
        """Test successful AI suggestions generation."""
        # Setup mock
        mock_plugin_instance = Mock()
        mock_plugin_instance.get_suggestions.return_value = 'Suggestion 1\nSuggestion 2\nSuggestion 3'
        mock_ai_plugin.return_value = mock_plugin_instance

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        response = self.api_client.post('/api/ai/suggestions/', {
            'prompt': 'Suggest improvements for my app'
        })

        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'success'
        assert data['prompt'] == 'Suggest improvements for my app'
        assert data['suggestions'] == ['Suggestion 1', 'Suggestion 2', 'Suggestion 3']

        # Verify AI plugin was called correctly
        mock_ai_plugin.assert_called_once_with(api_key='test-api-key')
        mock_plugin_instance.get_suggestions.assert_called_once_with('Suggest improvements for my app')

    def test_generate_ai_suggestions_missing_api_key(self):
        """Test AI suggestions endpoint without API key."""
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        with patch.dict('os.environ', {}, clear=True):
            response = self.api_client.post('/api/ai/suggestions/', {
                'prompt': 'Test prompt'
            })

        assert response.status_code == 400

    def test_generate_image_unauthenticated(self):
        """Test image generation endpoint without authentication."""
        response = self.api_client.post('/api/ai/generate-image/', {
            'prompt': 'Generate a beautiful landscape'
        })

        assert response.status_code == 401

    @patch.dict('os.environ', {'OPENAI_API_KEY': 'test-api-key'})
    @patch('my_app.views.AIPlugin')
    def test_generate_image_success(self, mock_ai_plugin):
        """Test successful image generation."""
        # Setup mock
        mock_plugin_instance = Mock()
        mock_plugin_instance.generate_image.return_value = 'https://example.com/generated-image.jpg'
        mock_ai_plugin.return_value = mock_plugin_instance

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        response = self.api_client.post('/api/ai/generate-image/', {
            'prompt': 'Generate a beautiful landscape'
        })

        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'success'
        assert data['prompt'] == 'Generate a beautiful landscape'
        assert data['image_url'] == 'https://example.com/generated-image.jpg'
        assert data['api'] == 'openai'

        # Verify AI plugin was called correctly
        mock_ai_plugin.assert_called_once_with(api_key='test-api-key')
        mock_plugin_instance.generate_image.assert_called_once_with('Generate a beautiful landscape', api='openai')

    @patch.dict('os.environ', {'STABILITY_API_KEY': 'stability-key'})
    @patch('my_app.views.AIPlugin')
    def test_generate_image_stability_api(self, mock_ai_plugin):
        """Test image generation with Stability API."""
        # Setup mock
        mock_plugin_instance = Mock()
        mock_plugin_instance.generate_image.return_value = 'https://stability.com/generated-image.jpg'
        mock_ai_plugin.return_value = mock_plugin_instance

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        response = self.api_client.post('/api/ai/generate-image/', {
            'prompt': 'Generate abstract art',
            'api': 'stability'
        })

        assert response.status_code == 200
        data = response.json()
        assert data['api'] == 'stability'
        assert data['image_url'] == 'https://stability.com/generated-image.jpg'

        # Verify AI plugin was called correctly
        mock_ai_plugin.assert_called_once_with(api_key='stability-key')
        mock_plugin_instance.generate_image.assert_called_once_with('Generate abstract art', api='stability')

    @patch.dict('os.environ', {'OPENAI_API_KEY': 'test-api-key'})
    @patch('my_app.views.TutorialAIPlugin')
    def test_generate_tutorial_response_success(self, mock_tutorial_plugin):
        """Test successful tutorial response generation."""
        # Setup mock
        mock_plugin_instance = Mock()
        mock_plugin_instance.generate_response.return_value = 'This is a tutorial response'
        mock_plugin_instance.context = {'current_step': 1}
        mock_tutorial_plugin.return_value = mock_plugin_instance

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        response = self.api_client.post('/api/ai/tutorial/', {
            'prompt': 'How do I create a button?'
        })

        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'success'
        assert data['prompt'] == 'How do I create a button?'
        assert data['response'] == 'This is a tutorial response'
        assert data['nextStep'] == 2

        # Verify tutorial plugin was called correctly
        mock_tutorial_plugin.assert_called_once_with(api_key='test-api-key')
        mock_plugin_instance.generate_response.assert_called_once_with('How do I create a button?')

    @patch.dict('os.environ', {'OPENAI_API_KEY': 'test-api-key'})
    @patch('my_app.views.AILayoutSuggestionsEngine')
    def test_generate_layout_suggestions_success(self, mock_layout_engine):
        """Test successful layout suggestions generation."""
        # Setup mock
        mock_engine_instance = Mock()
        mock_engine_instance.suggest_layouts.return_value = [
            {'type': 'grid', 'columns': 2},
            {'type': 'flex', 'direction': 'column'}
        ]
        mock_layout_engine.return_value = mock_engine_instance

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        components = [
            {'id': '1', 'type': 'button'},
            {'id': '2', 'type': 'input'}
        ]

        response = self.api_client.post('/api/ai/layout-suggestions/', {
            'components': components,
            'layouts': [],
            'context': {'theme': 'modern'}
        })

        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'success'
        assert data['component_count'] == 2
        assert len(data['suggestions']) == 2
        assert 'timestamp' in data

        # Verify layout engine was called correctly
        mock_layout_engine.assert_called_once_with(api_key='test-api-key')
        mock_engine_instance.suggest_layouts.assert_called_once_with(
            components, [], {'theme': 'modern'}
        )

    @patch.dict('os.environ', {'OPENAI_API_KEY': 'test-api-key'})
    @patch('my_app.views.AIComponentCombinationEngine')
    def test_generate_component_combinations_success(self, mock_combination_engine):
        """Test successful component combinations generation."""
        # Setup mock
        mock_engine_instance = Mock()
        mock_engine_instance.suggest_combinations.return_value = [
            {'combination': ['button', 'input'], 'purpose': 'form'},
            {'combination': ['header', 'navigation'], 'purpose': 'layout'}
        ]
        mock_combination_engine.return_value = mock_engine_instance

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        components = [
            {'id': '1', 'type': 'button'},
            {'id': '2', 'type': 'input'},
            {'id': '3', 'type': 'header'}
        ]
        selected_component = {'id': '1', 'type': 'button'}

        response = self.api_client.post('/api/ai/component-combinations/', {
            'components': components,
            'selected_component': selected_component,
            'context': {'purpose': 'dashboard'}
        })

        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'success'
        assert data['component_count'] == 3
        assert data['selected_component'] == 'button'
        assert len(data['suggestions']) == 2

        # Verify combination engine was called correctly
        mock_combination_engine.assert_called_once_with(api_key='test-api-key')
        mock_engine_instance.suggest_combinations.assert_called_once_with(
            components, selected_component, {'purpose': 'dashboard'}
        )

    @patch('my_app.views.AILayoutSuggestionsEngine')
    def test_analyze_app_structure_success(self, mock_layout_engine):
        """Test successful app structure analysis."""
        # Setup mock
        mock_engine_instance = Mock()
        mock_engine_instance.analyze_app_structure.return_value = {
            'complexity': 'medium',
            'suggestions': ['Add navigation', 'Improve spacing'],
            'score': 75
        }
        mock_layout_engine.return_value = mock_engine_instance

        self.api_client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        components = [
            {'id': '1', 'type': 'header'},
            {'id': '2', 'type': 'content'},
            {'id': '3', 'type': 'footer'}
        ]
        layouts = [{'type': 'grid', 'rows': 3}]

        response = self.api_client.post('/api/ai/analyze-structure/', {
            'components': components,
            'layouts': layouts
        })

        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'success'
        assert data['analysis']['complexity'] == 'medium'
        assert data['analysis']['score'] == 75
        assert 'timestamp' in data

        # Verify layout engine was called correctly
        mock_layout_engine.assert_called_once()
        mock_engine_instance.analyze_app_structure.assert_called_once_with(components, layouts)
