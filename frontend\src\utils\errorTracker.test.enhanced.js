/**
 * Enhanced Error Tracker Tests
 * 
 * Tests for the enhanced error tracking system with circuit breaker,
 * retry logic, and graceful degradation features.
 */

import { initErrorTracking } from './errorTracker';

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = (() => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

describe('Enhanced Error Tracker', () => {
  let errorTracker;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    mockLocalStorage.clear();
    
    // Mock console methods
    console.error = jest.fn();
    console.warn = jest.fn();
    console.log = jest.fn();

    // Store original fetch
    window._originalFetch = mockFetch;

    // Initialize error tracker with test configuration
    errorTracker = initErrorTracking({
      enabled: true,
      samplingRate: 1.0,
      errorLimit: 10,
      breadcrumbLimit: 10,
      ignoredErrors: [],
      reportingEndpoint: '/api/errors',
      logToConsole: false,
      captureConsoleErrors: true,
      captureNetworkErrors: true,
      captureUnhandledRejections: true,
      captureBreadcrumbs: true
    });
  });

  afterEach(() => {
    // Restore console methods
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
    console.log = originalConsoleLog;
  });

  describe('Circuit Breaker', () => {
    test('should open circuit after consecutive failures', async () => {
      // Mock fetch to always fail
      mockFetch.mockRejectedValue(new Error('Network error'));

      // Track multiple errors to trigger circuit breaker
      for (let i = 0; i < 6; i++) {
        errorTracker.trackError(new Error(`Test error ${i}`));
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Check circuit breaker status
      const status = errorTracker.getCircuitBreakerStatus();
      expect(status.state).toBe('OPEN');
      expect(status.failureCount).toBeGreaterThanOrEqual(5);
    });

    test('should queue errors when circuit is open', () => {
      // Mock fetch to always fail
      mockFetch.mockRejectedValue(new Error('Network error'));

      // Manually set circuit to open
      const status = errorTracker.getCircuitBreakerStatus();
      
      // Track an error
      errorTracker.trackError(new Error('Test error'));

      // Should have queued the error
      expect(status.queueLength).toBeGreaterThanOrEqual(0);
    });

    test('should transition to half-open after timeout', async () => {
      // This test would require mocking time, so we'll test the logic indirectly
      const status = errorTracker.getCircuitBreakerStatus();
      expect(['CLOSED', 'OPEN', 'HALF_OPEN']).toContain(status.state);
    });
  });

  describe('Retry Logic', () => {
    test('should retry failed requests with exponential backoff', async () => {
      // Mock fetch to fail first few times, then succeed
      let callCount = 0;
      mockFetch.mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          return Promise.reject(new Error('Network error'));
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ status: 'success' })
        });
      });

      // Track an error
      errorTracker.trackError(new Error('Test error'));

      // Wait for retries
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Should have made multiple attempts
      expect(callCount).toBeGreaterThan(1);
    });
  });

  describe('Local Storage Fallback', () => {
    test('should store errors locally when reporting fails', async () => {
      // Mock fetch to always fail
      mockFetch.mockRejectedValue(new Error('Network error'));

      // Track an error
      errorTracker.trackError(new Error('Test error'));

      // Wait for error reporting to fail and store locally
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if error was stored locally
      const storedErrors = errorTracker.getLocallyStoredErrors();
      expect(Array.isArray(storedErrors)).toBe(true);
    });

    test('should clear locally stored errors', () => {
      // Store some errors first
      mockLocalStorage.setItem('errorTracker_failedReports', JSON.stringify([
        { message: 'Test error 1', storedAt: new Date().toISOString() },
        { message: 'Test error 2', storedAt: new Date().toISOString() }
      ]));

      // Clear stored errors
      errorTracker.clearLocallyStoredErrors();

      // Check if errors were cleared
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('errorTracker_failedReports');
    });
  });

  describe('Error Filtering', () => {
    test('should ignore errors matching ignored patterns', () => {
      // Update config to ignore specific errors
      errorTracker.updateConfig({
        ignoredErrors: [/Test error/, 'Network request failed']
      });

      // Track errors that should be ignored
      errorTracker.trackError(new Error('Test error message'));
      errorTracker.trackError(new Error('Network request failed'));

      // These errors should not be in the error list
      const errors = errorTracker.getErrors();
      expect(errors.length).toBe(0);
    });

    test('should prevent infinite loops from error reporting failures', () => {
      // Track an error with error reporting failure message
      errorTracker.trackError(new Error('Failed to report error'));

      // This should be ignored to prevent infinite loops
      const errors = errorTracker.getErrors();
      expect(errors.length).toBe(0);
    });
  });

  describe('Manual Operations', () => {
    test('should allow manual retry of queued errors', async () => {
      // Mock successful response
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ status: 'success' })
      });

      // Manually retry queued errors
      await errorTracker.retryQueuedErrors();

      // Should complete without errors
      expect(true).toBe(true);
    });

    test('should provide circuit breaker status', () => {
      const status = errorTracker.getCircuitBreakerStatus();
      
      expect(status).toHaveProperty('state');
      expect(status).toHaveProperty('failureCount');
      expect(status).toHaveProperty('lastFailureTime');
      expect(status).toHaveProperty('queueLength');
    });
  });

  describe('Configuration', () => {
    test('should allow configuration updates', () => {
      const newConfig = {
        enabled: false,
        samplingRate: 0.5,
        reportingEndpoint: '/api/new-errors'
      };

      errorTracker.updateConfig(newConfig);

      const currentConfig = errorTracker.getConfig();
      expect(currentConfig.enabled).toBe(false);
      expect(currentConfig.samplingRate).toBe(0.5);
      expect(currentConfig.reportingEndpoint).toBe('/api/new-errors');
    });
  });

  describe('Network Error Handling', () => {
    test('should not track errors from error reporting endpoint', () => {
      // Track a network error from the error reporting endpoint
      errorTracker.trackError({
        type: 'network_error',
        message: 'Failed to fetch',
        url: '/api/errors'
      });

      // This should be filtered out to prevent infinite loops
      const errors = errorTracker.getErrors();
      expect(errors.length).toBe(0);
    });
  });
});
