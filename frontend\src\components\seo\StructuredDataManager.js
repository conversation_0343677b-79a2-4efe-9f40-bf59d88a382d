import React from 'react';
import { Helmet } from 'react-helmet';
import PropTypes from 'prop-types';
import {
  generateWebsiteData,
  generateOrganizationData,
  generateSoftwareApplicationData
} from '../../utils/structuredData';

/**
 * StructuredDataManager Component
 * 
 * This component manages structured data (JSON-LD) for SEO.
 * It adds appropriate JSON-LD scripts to the page based on the current route.
 * 
 * @param {Object} props - Component props
 * @returns {React.Component} - Structured data component
 */
const StructuredDataManager = ({ 
  type = 'website',
  options = {},
  additionalData = []
}) => {
  // Generate structured data based on type
  let structuredData = [];

  // Always include website data
  structuredData.push(generateWebsiteData(options.website));

  // Add organization data
  structuredData.push(generateOrganizationData(options.organization));

  // Add type-specific data
  switch (type) {
    case 'software':
      structuredData.push(generateSoftwareApplicationData(options.software));
      break;
    default:
      // Default is website, which is already included
      break;
  }

  // Add any additional structured data
  if (additionalData.length > 0) {
    structuredData = [...structuredData, ...additionalData];
  }

  return (
    <Helmet>
      {structuredData.map((data, index) => (
        <script key={index} type="application/ld+json">
          {JSON.stringify(data)}
        </script>
      ))}
    </Helmet>
  );
};

StructuredDataManager.propTypes = {
  type: PropTypes.oneOf(['website', 'software']),
  options: PropTypes.shape({
    website: PropTypes.object,
    organization: PropTypes.object,
    software: PropTypes.object
  }),
  additionalData: PropTypes.array
};

export default StructuredDataManager;
