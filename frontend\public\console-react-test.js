/**
 * Console React Test Script
 * 
 * Copy and paste this script into the browser console to test React availability
 * and functionality in the App Builder application.
 */

(function consoleReactTest() {
  console.log('🔍 Starting Console React Test...');
  console.log('='.repeat(50));
  
  const results = {
    reactGlobal: false,
    reactDOMGlobal: false,
    reactVersion: null,
    rootElement: false,
    contentRendered: false,
    bundleLoaded: false,
    componentCreation: false
  };
  
  // Test 1: React Global Availability
  console.log('\n1️⃣ Testing React Global Availability...');
  if (typeof window.React !== 'undefined') {
    results.reactGlobal = true;
    results.reactVersion = window.React.version || 'Unknown';
    console.log('✅ React is available globally');
    console.log(`   Version: ${results.reactVersion}`);
    console.log(`   Type: ${typeof window.React}`);
    console.log(`   Keys: ${Object.keys(window.React).slice(0, 10).join(', ')}...`);
  } else {
    console.log('❌ React is NOT available globally');
    console.log('   This might be normal if React is only available in module scope');
  }
  
  // Test 2: ReactDOM Global Availability
  console.log('\n2️⃣ Testing ReactDOM Global Availability...');
  if (typeof window.ReactDOM !== 'undefined') {
    results.reactDOMGlobal = true;
    console.log('✅ ReactDOM is available globally');
    console.log(`   Type: ${typeof window.ReactDOM}`);
    console.log(`   Keys: ${Object.keys(window.ReactDOM).join(', ')}`);
  } else {
    console.log('❌ ReactDOM is NOT available globally');
  }
  
  // Test 3: Root Element Check
  console.log('\n3️⃣ Testing React Root Element...');
  const rootElement = document.getElementById('root');
  if (rootElement) {
    results.rootElement = true;
    console.log('✅ React root element found');
    console.log(`   Children count: ${rootElement.children.length}`);
    console.log(`   innerHTML length: ${rootElement.innerHTML.length}`);
    
    if (rootElement.children.length > 0) {
      results.contentRendered = true;
      console.log('✅ Content is rendered in root element');
    } else {
      console.log('❌ Root element is empty - React app may not be mounted');
    }
  } else {
    console.log('❌ React root element not found');
  }
  
  // Test 4: Bundle Loading Check
  console.log('\n4️⃣ Testing Bundle Loading...');
  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const reactScripts = scripts.filter(script => 
    script.src.includes('main') || 
    script.src.includes('bundle') || 
    script.src.includes('chunk')
  );
  
  if (reactScripts.length > 0) {
    results.bundleLoaded = true;
    console.log(`✅ Found ${reactScripts.length} bundle scripts`);
    reactScripts.forEach((script, index) => {
      console.log(`   ${index + 1}. ${script.src}`);
    });
  } else {
    console.log('❌ No bundle scripts found');
  }
  
  // Test 5: Component Creation Test
  console.log('\n5️⃣ Testing React Component Creation...');
  if (results.reactGlobal && results.reactDOMGlobal) {
    try {
      // Create a test component
      const TestComponent = window.React.createElement('div', {
        style: { 
          padding: '10px', 
          background: '#e8f5e8', 
          border: '2px solid #4caf50',
          borderRadius: '4px',
          margin: '10px 0'
        }
      }, 
        window.React.createElement('strong', null, '🎉 React Test Component'),
        window.React.createElement('br'),
        window.React.createElement('span', null, `React Version: ${results.reactVersion}`)
      );
      
      // Try to render it to a test container
      let testContainer = document.getElementById('console-test-container');
      if (!testContainer) {
        testContainer = document.createElement('div');
        testContainer.id = 'console-test-container';
        testContainer.style.position = 'fixed';
        testContainer.style.top = '20px';
        testContainer.style.right = '20px';
        testContainer.style.zIndex = '9999';
        testContainer.style.maxWidth = '300px';
        document.body.appendChild(testContainer);
      }
      
      const root = window.ReactDOM.createRoot(testContainer);
      root.render(TestComponent);
      
      results.componentCreation = true;
      console.log('✅ React component created and rendered successfully!');
      console.log('   Check the top-right corner of the page for the test component');
      
      // Auto-remove after 10 seconds
      setTimeout(() => {
        if (testContainer && testContainer.parentNode) {
          testContainer.parentNode.removeChild(testContainer);
          console.log('🧹 Test component removed');
        }
      }, 10000);
      
    } catch (error) {
      console.log(`❌ Error creating React component: ${error.message}`);
      console.error(error);
    }
  } else {
    console.log('❌ Cannot test component creation - React or ReactDOM not available');
  }
  
  // Test 6: Check for React DevTools
  console.log('\n6️⃣ Testing React DevTools...');
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('✅ React DevTools detected');
  } else {
    console.log('ℹ️ React DevTools not detected (this is normal)');
  }
  
  // Test 7: Check for React-specific DOM attributes
  console.log('\n7️⃣ Testing React DOM Attributes...');
  const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
  if (reactElements.length > 0) {
    console.log(`✅ Found ${reactElements.length} React DOM attributes`);
  } else {
    console.log('ℹ️ No React DOM attributes found (this might be normal in React 18+)');
  }
  
  // Summary
  console.log('\n📊 REACT TEST SUMMARY');
  console.log('='.repeat(50));
  console.log(`React Global Available: ${results.reactGlobal ? '✅ YES' : '❌ NO'}`);
  console.log(`ReactDOM Global Available: ${results.reactDOMGlobal ? '✅ YES' : '❌ NO'}`);
  console.log(`React Version: ${results.reactVersion || 'Unknown'}`);
  console.log(`Root Element Found: ${results.rootElement ? '✅ YES' : '❌ NO'}`);
  console.log(`Content Rendered: ${results.contentRendered ? '✅ YES' : '❌ NO'}`);
  console.log(`Bundle Scripts Loaded: ${results.bundleLoaded ? '✅ YES' : '❌ NO'}`);
  console.log(`Component Creation: ${results.componentCreation ? '✅ YES' : '❌ NO'}`);
  
  const overallSuccess = results.reactGlobal && results.rootElement && results.bundleLoaded;
  
  console.log(`\n🎯 Overall React Status: ${overallSuccess ? '✅ WORKING' : '❌ ISSUES DETECTED'}`);
  
  if (overallSuccess) {
    console.log('\n🎉 SUCCESS! React is properly loaded and functional!');
    console.log('✅ React framework is available and working correctly');
    console.log('✅ App Builder should be able to render React components');
    console.log('✅ All React-based features should work as expected');
  } else {
    console.log('\n⚠️ Issues detected with React loading:');
    if (!results.reactGlobal) {
      console.log('❌ React not available globally - may be in module scope only');
    }
    if (!results.rootElement) {
      console.log('❌ React root element not found');
    }
    if (!results.bundleLoaded) {
      console.log('❌ Bundle scripts not loaded properly');
    }
    
    console.log('\n🔧 Troubleshooting suggestions:');
    console.log('1. Check browser console for JavaScript errors');
    console.log('2. Verify webpack build completed successfully');
    console.log('3. Check Network tab for failed script loading');
    console.log('4. Try refreshing the page');
    console.log('5. Check if service worker is interfering');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. If React is working, test the App Builder interface');
  console.log('2. Check that components render correctly');
  console.log('3. Verify all React-based features function properly');
  
  return results;
})();

// Make the test function available globally for repeated testing
window.consoleReactTest = consoleReactTest;
