#!/usr/bin/env python3
"""
Comprehensive Performance Test Suite for App Builder 201
Tests performance across the entire application stack
"""

import asyncio
import time
import statistics
import requests
import websockets
import json
import psutil
import concurrent.futures
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceTestSuite:
    def __init__(self, base_url="http://localhost:8000", ws_url="ws://localhost:8000"):
        self.base_url = base_url
        self.ws_url = ws_url
        self.results = {}
        
    def measure_time(self, func):
        """Decorator to measure execution time"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            return result, execution_time
        return wrapper
    
    def test_http_response_times(self):
        """Test HTTP endpoint response times"""
        logger.info("🌐 Testing HTTP Response Times...")
        
        endpoints = [
            "/health/",
            "/api/status/",
            "/api/csrf-token/",
            "/get_app_data/",
            "/v1/apps/"
        ]
        
        results = {}
        
        for endpoint in endpoints:
            url = f"{self.base_url}{endpoint}"
            times = []
            
            # Test each endpoint 10 times
            for i in range(10):
                try:
                    start_time = time.time()
                    response = requests.get(url, timeout=10)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        times.append(end_time - start_time)
                    else:
                        logger.warning(f"⚠️ {endpoint}: HTTP {response.status_code}")
                        
                except Exception as e:
                    logger.error(f"❌ {endpoint}: {str(e)}")
            
            if times:
                results[endpoint] = {
                    "avg_time": statistics.mean(times),
                    "min_time": min(times),
                    "max_time": max(times),
                    "median_time": statistics.median(times),
                    "std_dev": statistics.stdev(times) if len(times) > 1 else 0,
                    "success_rate": len(times) / 10 * 100
                }
                
                logger.info(f"✅ {endpoint}: {results[endpoint]['avg_time']:.3f}s avg")
            else:
                results[endpoint] = {"error": "All requests failed"}
        
        self.results["http_performance"] = results
        return results
    
    def test_concurrent_requests(self):
        """Test performance under concurrent load"""
        logger.info("🚀 Testing Concurrent Request Performance...")
        
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}/health/", timeout=10)
                end_time = time.time()
                return {
                    "success": response.status_code == 200,
                    "time": end_time - start_time,
                    "status_code": response.status_code
                }
            except Exception as e:
                return {"success": False, "error": str(e), "time": 0}
        
        # Test with different concurrency levels
        concurrency_levels = [1, 5, 10, 20]
        results = {}
        
        for concurrency in concurrency_levels:
            logger.info(f"📊 Testing with {concurrency} concurrent requests...")
            
            start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
                futures = [executor.submit(make_request) for _ in range(concurrency)]
                responses = [future.result() for future in concurrent.futures.as_completed(futures)]
            end_time = time.time()
            
            successful_responses = [r for r in responses if r["success"]]
            response_times = [r["time"] for r in successful_responses]
            
            results[f"concurrency_{concurrency}"] = {
                "total_time": end_time - start_time,
                "success_count": len(successful_responses),
                "total_requests": concurrency,
                "success_rate": len(successful_responses) / concurrency * 100,
                "avg_response_time": statistics.mean(response_times) if response_times else 0,
                "requests_per_second": concurrency / (end_time - start_time)
            }
            
            logger.info(f"✅ Concurrency {concurrency}: {results[f'concurrency_{concurrency}']['success_rate']:.1f}% success, {results[f'concurrency_{concurrency}']['requests_per_second']:.2f} req/s")
        
        self.results["concurrent_performance"] = results
        return results
    
    async def test_websocket_performance(self):
        """Test WebSocket performance"""
        logger.info("🔌 Testing WebSocket Performance...")
        
        results = {}
        
        # Test connection time
        start_time = time.time()
        try:
            async with websockets.connect(f"{self.ws_url}/ws/test/") as websocket:
                connection_time = time.time() - start_time
                
                # Test message round-trip times
                round_trip_times = []
                
                for i in range(20):
                    message = {
                        "type": "performance_test",
                        "sequence": i,
                        "timestamp": time.time()
                    }
                    
                    send_time = time.time()
                    await websocket.send(json.dumps(message))
                    
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        receive_time = time.time()
                        round_trip_times.append(receive_time - send_time)
                    except asyncio.TimeoutError:
                        logger.warning(f"⏰ WebSocket message {i} timed out")
                
                results = {
                    "connection_time": connection_time,
                    "messages_sent": 20,
                    "messages_received": len(round_trip_times),
                    "success_rate": len(round_trip_times) / 20 * 100,
                    "avg_round_trip": statistics.mean(round_trip_times) if round_trip_times else 0,
                    "min_round_trip": min(round_trip_times) if round_trip_times else 0,
                    "max_round_trip": max(round_trip_times) if round_trip_times else 0,
                    "median_round_trip": statistics.median(round_trip_times) if round_trip_times else 0
                }
                
                logger.info(f"✅ WebSocket: {results['connection_time']:.3f}s connection, {results['avg_round_trip']:.3f}s avg round-trip")
                
        except Exception as e:
            logger.error(f"❌ WebSocket test failed: {str(e)}")
            results = {"error": str(e)}
        
        self.results["websocket_performance"] = results
        return results
    
    def test_database_performance(self):
        """Test database performance through API"""
        logger.info("🗄️ Testing Database Performance...")
        
        results = {}
        
        # Test app creation performance
        create_times = []
        app_ids = []
        
        for i in range(5):
            app_data = {
                "name": f"Performance Test App {i}",
                "description": f"Test app for performance testing {i}",
                "app_data": json.dumps({
                    "components": [
                        {"id": f"comp_{j}", "type": "button", "props": {"text": f"Button {j}"}}
                        for j in range(10)
                    ]
                })
            }
            
            try:
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/v1/apps/",
                    json=app_data,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                end_time = time.time()
                
                if response.status_code in [200, 201]:
                    create_times.append(end_time - start_time)
                    if response.json().get("id"):
                        app_ids.append(response.json()["id"])
                else:
                    logger.warning(f"⚠️ App creation failed: HTTP {response.status_code}")
                    
            except Exception as e:
                logger.error(f"❌ App creation error: {str(e)}")
        
        # Test app retrieval performance
        read_times = []
        
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/v1/apps/", timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                read_times.append(end_time - start_time)
                app_count = response.json().get("count", 0)
                logger.info(f"📊 Retrieved {app_count} apps")
            
        except Exception as e:
            logger.error(f"❌ App retrieval error: {str(e)}")
        
        results = {
            "create_operations": {
                "count": len(create_times),
                "avg_time": statistics.mean(create_times) if create_times else 0,
                "min_time": min(create_times) if create_times else 0,
                "max_time": max(create_times) if create_times else 0
            },
            "read_operations": {
                "count": len(read_times),
                "avg_time": statistics.mean(read_times) if read_times else 0
            }
        }
        
        logger.info(f"✅ Database: {results['create_operations']['avg_time']:.3f}s avg create, {results['read_operations']['avg_time']:.3f}s avg read")
        
        self.results["database_performance"] = results
        return results
    
    def test_system_resources(self):
        """Test system resource usage"""
        logger.info("💻 Testing System Resource Usage...")
        
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Get Docker container stats if available
        container_stats = {}
        try:
            import docker
            client = docker.from_env()
            
            for container_name in ["app-builder-201-backend-1", "app-builder-201-frontend-1", "app-builder-201-db-1"]:
                try:
                    container = client.containers.get(container_name)
                    stats = container.stats(stream=False)
                    
                    # Calculate CPU percentage
                    cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - stats['precpu_stats']['cpu_usage']['total_usage']
                    system_delta = stats['cpu_stats']['system_cpu_usage'] - stats['precpu_stats']['system_cpu_usage']
                    cpu_percent_container = (cpu_delta / system_delta) * len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100.0
                    
                    # Memory usage
                    memory_usage = stats['memory_stats']['usage']
                    memory_limit = stats['memory_stats']['limit']
                    memory_percent_container = (memory_usage / memory_limit) * 100.0
                    
                    container_stats[container_name] = {
                        "cpu_percent": cpu_percent_container,
                        "memory_usage_mb": memory_usage / (1024 * 1024),
                        "memory_percent": memory_percent_container,
                        "status": container.status
                    }
                    
                except Exception as e:
                    logger.warning(f"⚠️ Could not get stats for {container_name}: {str(e)}")
                    
        except ImportError:
            logger.info("📝 Docker library not available, skipping container stats")
        except Exception as e:
            logger.warning(f"⚠️ Docker stats error: {str(e)}")
        
        results = {
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "disk_percent": disk.percent,
                "disk_free_gb": disk.free / (1024**3)
            },
            "containers": container_stats
        }
        
        logger.info(f"✅ System: {cpu_percent:.1f}% CPU, {memory.percent:.1f}% Memory, {disk.percent:.1f}% Disk")
        
        self.results["system_resources"] = results
        return results
    
    async def run_all_tests(self):
        """Run all performance tests"""
        logger.info("🚀 Starting Comprehensive Performance Test Suite...")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # Run HTTP tests
        self.test_http_response_times()
        
        # Run concurrent tests
        self.test_concurrent_requests()
        
        # Run WebSocket tests
        await self.test_websocket_performance()
        
        # Run database tests
        self.test_database_performance()
        
        # Run system resource tests
        self.test_system_resources()
        
        total_time = time.time() - start_time
        
        # Generate summary report
        self.generate_performance_report(total_time)
    
    def generate_performance_report(self, total_test_time):
        """Generate comprehensive performance report"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 PERFORMANCE TEST SUMMARY REPORT")
        logger.info("=" * 60)
        
        # HTTP Performance Summary
        if "http_performance" in self.results:
            logger.info("\n🌐 HTTP Performance:")
            for endpoint, data in self.results["http_performance"].items():
                if "avg_time" in data:
                    logger.info(f"  {endpoint}: {data['avg_time']:.3f}s avg ({data['success_rate']:.1f}% success)")
        
        # Concurrent Performance Summary
        if "concurrent_performance" in self.results:
            logger.info("\n🚀 Concurrent Performance:")
            for test, data in self.results["concurrent_performance"].items():
                logger.info(f"  {test}: {data['requests_per_second']:.2f} req/s ({data['success_rate']:.1f}% success)")
        
        # WebSocket Performance Summary
        if "websocket_performance" in self.results:
            ws_data = self.results["websocket_performance"]
            if "avg_round_trip" in ws_data:
                logger.info(f"\n🔌 WebSocket Performance:")
                logger.info(f"  Connection: {ws_data['connection_time']:.3f}s")
                logger.info(f"  Round-trip: {ws_data['avg_round_trip']:.3f}s avg ({ws_data['success_rate']:.1f}% success)")
        
        # Database Performance Summary
        if "database_performance" in self.results:
            db_data = self.results["database_performance"]
            logger.info(f"\n🗄️ Database Performance:")
            logger.info(f"  Create: {db_data['create_operations']['avg_time']:.3f}s avg")
            logger.info(f"  Read: {db_data['read_operations']['avg_time']:.3f}s avg")
        
        # System Resources Summary
        if "system_resources" in self.results:
            sys_data = self.results["system_resources"]["system"]
            logger.info(f"\n💻 System Resources:")
            logger.info(f"  CPU: {sys_data['cpu_percent']:.1f}%")
            logger.info(f"  Memory: {sys_data['memory_percent']:.1f}% ({sys_data['memory_available_gb']:.1f}GB available)")
            logger.info(f"  Disk: {sys_data['disk_percent']:.1f}% ({sys_data['disk_free_gb']:.1f}GB free)")
        
        # Overall Summary
        logger.info(f"\n⏱️ Total Test Time: {total_test_time:.2f}s")
        logger.info(f"📅 Test Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Performance Recommendations
        self.generate_recommendations()
        
        logger.info("\n🎉 Performance testing completed!")
    
    def generate_recommendations(self):
        """Generate performance optimization recommendations"""
        logger.info("\n💡 Performance Recommendations:")
        
        recommendations = []
        
        # HTTP Performance Recommendations
        if "http_performance" in self.results:
            slow_endpoints = [
                endpoint for endpoint, data in self.results["http_performance"].items()
                if "avg_time" in data and data["avg_time"] > 1.0
            ]
            if slow_endpoints:
                recommendations.append(f"🐌 Optimize slow endpoints: {', '.join(slow_endpoints)}")
        
        # Concurrent Performance Recommendations
        if "concurrent_performance" in self.results:
            high_concurrency = self.results["concurrent_performance"].get("concurrency_20", {})
            if high_concurrency.get("success_rate", 100) < 95:
                recommendations.append("⚡ Consider implementing connection pooling or rate limiting")
        
        # WebSocket Performance Recommendations
        if "websocket_performance" in self.results:
            ws_data = self.results["websocket_performance"]
            if ws_data.get("avg_round_trip", 0) > 0.5:
                recommendations.append("🔌 WebSocket latency is high - check network configuration")
        
        # System Resource Recommendations
        if "system_resources" in self.results:
            sys_data = self.results["system_resources"]["system"]
            if sys_data["cpu_percent"] > 80:
                recommendations.append("🔥 High CPU usage - consider scaling or optimization")
            if sys_data["memory_percent"] > 80:
                recommendations.append("🧠 High memory usage - check for memory leaks")
            if sys_data["disk_percent"] > 80:
                recommendations.append("💾 Low disk space - consider cleanup or expansion")
        
        if recommendations:
            for rec in recommendations:
                logger.info(f"  {rec}")
        else:
            logger.info("  ✅ All performance metrics look good!")

async def main():
    """Main test function"""
    tester = PerformanceTestSuite()
    await tester.run_all_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Performance testing interrupted by user")
    except Exception as e:
        logger.error(f"Performance testing failed: {str(e)}")
