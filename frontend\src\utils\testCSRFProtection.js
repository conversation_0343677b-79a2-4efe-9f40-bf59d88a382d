/**
 * CSRF Protection Test Suite
 * 
 * This utility tests the CSRF protection implementation across
 * all forms and API endpoints to ensure proper security.
 */

import csrfService from '../services/csrfService';
import { api } from '../services/apiService';
import { API_ENDPOINTS } from '../config/api';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';

/**
 * Test CSRF token retrieval
 */
export const testCSRFTokenRetrieval = async () => {
  console.log('🔒 Testing CSRF token retrieval...');
  
  try {
    // Test getting token from service
    const token = await csrfService.getToken();
    
    if (!token) {
      throw new Error('No CSRF token received');
    }
    
    console.log('✅ CSRF token retrieved successfully:', token.substring(0, 10) + '...');
    
    // Test token from cookie
    const cookieToken = csrfService.getTokenFromCookie();
    console.log('🍪 Cookie token:', cookieToken ? 'Found' : 'Not found');
    
    return {
      success: true,
      token: token.substring(0, 10) + '...',
      cookieToken: !!cookieToken
    };
  } catch (error) {
    console.error('❌ CSRF token retrieval failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Test CSRF protection on API endpoints
 */
export const testCSRFProtection = async () => {
  console.log('🔒 Testing CSRF protection on API endpoints...');
  
  const results = [];
  
  // Test endpoints that should require CSRF protection
  const protectedEndpoints = [
    { url: '/api/app-data/', method: 'POST', data: { test: true } },
    { url: '/api/save_app_data/', method: 'POST', data: { app: { name: 'Test' } } },
  ];
  
  for (const endpoint of protectedEndpoints) {
    try {
      console.log(`Testing ${endpoint.method} ${endpoint.url}...`);
      
      // Test with CSRF token
      const response = await csrfService.request(`${API_BASE_URL}${endpoint.url}`, {
        method: endpoint.method,
        body: JSON.stringify(endpoint.data)
      });
      
      const result = {
        endpoint: endpoint.url,
        method: endpoint.method,
        withCSRF: response.ok,
        status: response.status,
        statusText: response.statusText
      };
      
      if (response.ok) {
        console.log(`✅ ${endpoint.method} ${endpoint.url} - CSRF protection working`);
      } else {
        console.log(`⚠️ ${endpoint.method} ${endpoint.url} - Status: ${response.status}`);
      }
      
      results.push(result);
    } catch (error) {
      console.error(`❌ ${endpoint.method} ${endpoint.url} - Error:`, error.message);
      results.push({
        endpoint: endpoint.url,
        method: endpoint.method,
        withCSRF: false,
        error: error.message
      });
    }
  }
  
  return results;
};

/**
 * Test CSRF protection without token (should fail)
 */
export const testCSRFProtectionWithoutToken = async () => {
  console.log('🔒 Testing CSRF protection without token (should fail)...');
  
  try {
    // Clear any existing token
    csrfService.clearToken();
    
    // Try to make a request without CSRF token
    const response = await fetch(`${API_BASE_URL}/api/app-data/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ test: true })
    });
    
    if (response.status === 403) {
      console.log('✅ CSRF protection working - request blocked without token');
      return {
        success: true,
        message: 'CSRF protection correctly blocked request without token',
        status: response.status
      };
    } else {
      console.log('⚠️ CSRF protection may not be working - request allowed without token');
      return {
        success: false,
        message: 'Request was allowed without CSRF token',
        status: response.status
      };
    }
  } catch (error) {
    console.error('❌ Error testing CSRF protection without token:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Test CSRF token refresh mechanism
 */
export const testCSRFTokenRefresh = async () => {
  console.log('🔒 Testing CSRF token refresh mechanism...');
  
  try {
    // Get initial token
    const initialToken = await csrfService.getToken();
    console.log('Initial token:', initialToken.substring(0, 10) + '...');
    
    // Clear token to force refresh
    csrfService.clearToken();
    
    // Get new token
    const refreshedToken = await csrfService.getToken();
    console.log('Refreshed token:', refreshedToken.substring(0, 10) + '...');
    
    if (refreshedToken && refreshedToken !== initialToken) {
      console.log('✅ CSRF token refresh working');
      return {
        success: true,
        message: 'CSRF token refresh mechanism working correctly'
      };
    } else {
      console.log('⚠️ CSRF token refresh may not be working');
      return {
        success: false,
        message: 'Token refresh did not provide new token'
      };
    }
  } catch (error) {
    console.error('❌ Error testing CSRF token refresh:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Test CSRF protection on authentication endpoints
 */
export const testAuthCSRFProtection = async () => {
  console.log('🔒 Testing CSRF protection on authentication endpoints...');
  
  const authEndpoints = [
    { url: '/api/auth/login/', method: 'POST' },
    { url: '/api/auth/register/', method: 'POST' }
  ];
  
  const results = [];
  
  for (const endpoint of authEndpoints) {
    try {
      // Test with invalid credentials but valid CSRF token
      const response = await csrfService.request(`${API_BASE_URL}${endpoint.url}`, {
        method: endpoint.method,
        body: JSON.stringify({
          username: 'test_user_invalid',
          password: 'test_password_invalid'
        })
      });
      
      // We expect 401 (invalid credentials) not 403 (CSRF error)
      const result = {
        endpoint: endpoint.url,
        method: endpoint.method,
        status: response.status,
        csrfWorking: response.status !== 403
      };
      
      if (response.status === 401) {
        console.log(`✅ ${endpoint.url} - CSRF protection working (got 401 for invalid creds)`);
      } else if (response.status === 403) {
        console.log(`❌ ${endpoint.url} - CSRF protection failed (got 403)`);
      } else {
        console.log(`⚠️ ${endpoint.url} - Unexpected status: ${response.status}`);
      }
      
      results.push(result);
    } catch (error) {
      console.error(`❌ ${endpoint.url} - Error:`, error.message);
      results.push({
        endpoint: endpoint.url,
        method: endpoint.method,
        error: error.message
      });
    }
  }
  
  return results;
};

/**
 * Run comprehensive CSRF protection test suite
 */
export const runCSRFTestSuite = async () => {
  console.log('🔒 Running comprehensive CSRF protection test suite...');
  console.log('================================================');
  
  const results = {
    tokenRetrieval: await testCSRFTokenRetrieval(),
    protectionWithToken: await testCSRFProtection(),
    protectionWithoutToken: await testCSRFProtectionWithoutToken(),
    tokenRefresh: await testCSRFTokenRefresh(),
    authProtection: await testAuthCSRFProtection()
  };
  
  console.log('================================================');
  console.log('🔒 CSRF Protection Test Suite Complete');
  console.log('Results:', results);
  
  // Summary
  const allPassed = Object.values(results).every(result => 
    Array.isArray(result) ? result.every(r => r.success !== false) : result.success
  );
  
  if (allPassed) {
    console.log('✅ All CSRF protection tests passed!');
  } else {
    console.log('⚠️ Some CSRF protection tests failed. Check results above.');
  }
  
  return results;
};

// Export individual test functions
export default {
  testCSRFTokenRetrieval,
  testCSRFProtection,
  testCSRFProtectionWithoutToken,
  testCSRFTokenRefresh,
  testAuthCSRFProtection,
  runCSRFTestSuite
};
