import React, { useState, useEffect, useRef } from 'react';
import useImprovedWebSocket, { ConnectionState } from '../hooks/useImprovedWebSocket';
import { getWebSocketUrl } from '../config/env';

/**
 * WebSocketManager Component
 * 
 * A comprehensive WebSocket management interface with:
 * - Connection status monitoring
 * - Message sending/receiving
 * - Performance metrics
 * - Debugging tools
 */
const WebSocketManager = ({ endpoint = 'app_builder', autoConnect = false }) => {
  // State
  const [messageInput, setMessageInput] = useState('');
  const [messageType, setMessageType] = useState('message');
  const [showMetrics, setShowMetrics] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const messagesEndRef = useRef(null);

  // Get WebSocket URL
  const wsUrl = getWebSocketUrl(endpoint);

  // Use our improved WebSocket hook
  const {
    connectionState,
    connected,
    connecting,
    lastMessage,
    messages,
    error,
    reconnectAttempt,
    metrics,
    connect,
    disconnect,
    send,
    clearMessages,
    clearError,
    resetMetrics
  } = useImprovedWebSocket({
    url: wsUrl,
    autoConnect,
    debug: true,
    heartbeatInterval: 30000,
    onMessage: (data) => {
      console.log('Message received:', data);
    }
  });

  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle connect button click
  const handleConnect = () => {
    connect();
  };

  // Handle disconnect button click
  const handleDisconnect = () => {
    disconnect();
  };

  // Handle message input change
  const handleMessageInputChange = (e) => {
    setMessageInput(e.target.value);
  };

  // Handle message type change
  const handleMessageTypeChange = (e) => {
    setMessageType(e.target.value);
  };

  // Handle send message button click
  const handleSendMessage = () => {
    if (!messageInput.trim()) return;

    try {
      // Try to parse as JSON if it looks like JSON
      let messageData;
      if (messageInput.trim().startsWith('{') && messageInput.trim().endsWith('}')) {
        try {
          messageData = JSON.parse(messageInput);
        } catch (e) {
          // If parsing fails, send as string
          messageData = { type: messageType, message: messageInput };
        }
      } else {
        // Send as structured message
        messageData = { type: messageType, message: messageInput };
      }

      // Add timestamp
      messageData.timestamp = Date.now();

      // Send the message
      send(messageData);

      // Clear input
      setMessageInput('');
    } catch (e) {
      console.error('Error sending message:', e);
    }
  };

  // Handle clear messages button click
  const handleClearMessages = () => {
    clearMessages();
  };

  // Get connection status color
  const getStatusColor = () => {
    switch (connectionState) {
      case ConnectionState.OPEN:
        return '#52c41a'; // Green
      case ConnectionState.CONNECTING:
      case ConnectionState.RECONNECTING:
        return '#faad14'; // Yellow
      case ConnectionState.CLOSING:
      case ConnectionState.CLOSED:
        return '#ff4d4f'; // Red
      case ConnectionState.ERROR:
        return '#ff4d4f'; // Red
      default:
        return '#d9d9d9'; // Grey
    }
  };

  // Get connection status text
  const getStatusText = () => {
    switch (connectionState) {
      case ConnectionState.OPEN:
        return 'Connected';
      case ConnectionState.CONNECTING:
        return 'Connecting...';
      case ConnectionState.RECONNECTING:
        return `Reconnecting (Attempt ${reconnectAttempt})...`;
      case ConnectionState.CLOSING:
        return 'Closing...';
      case ConnectionState.CLOSED:
        return 'Disconnected';
      case ConnectionState.ERROR:
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  // Format date
  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  // Render component
  return (
    <div style={styles.container}>
      <h2 style={styles.title}>WebSocket Manager</h2>
      
      {/* Connection Status */}
      <div style={styles.statusContainer}>
        <div style={styles.statusIndicator}>
          <div 
            style={{
              ...styles.statusDot,
              backgroundColor: getStatusColor()
            }}
          />
          <span style={styles.statusText}>{getStatusText()}</span>
        </div>
        
        <div style={styles.buttonGroup}>
          {!connected && !connecting && (
            <button 
              style={styles.button} 
              onClick={handleConnect}
            >
              Connect
            </button>
          )}
          
          {(connected || connecting) && (
            <button 
              style={{...styles.button, ...styles.dangerButton}} 
              onClick={handleDisconnect}
            >
              Disconnect
            </button>
          )}
        </div>
      </div>
      
      {/* Error Display */}
      {error && (
        <div style={styles.errorContainer}>
          <p style={styles.errorText}>Error: {error.message}</p>
          <button 
            style={{...styles.button, ...styles.smallButton}} 
            onClick={clearError}
          >
            Clear Error
          </button>
        </div>
      )}
      
      {/* Message Input */}
      <div style={styles.inputContainer}>
        <div style={styles.inputRow}>
          <select 
            style={styles.select} 
            value={messageType} 
            onChange={handleMessageTypeChange}
          >
            <option value="message">Message</option>
            <option value="ping">Ping</option>
            <option value="get_app_data">Get App Data</option>
            <option value="update_app_data">Update App Data</option>
            <option value="create_component">Create Component</option>
            <option value="update_component">Update Component</option>
            <option value="delete_component">Delete Component</option>
          </select>
          
          <input 
            type="text" 
            style={styles.input} 
            value={messageInput} 
            onChange={handleMessageInputChange} 
            placeholder="Enter message..." 
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          />
          
          <button 
            style={styles.button} 
            onClick={handleSendMessage} 
            disabled={!connected || !messageInput.trim()}
          >
            Send
          </button>
        </div>
      </div>
      
      {/* Messages */}
      <div style={styles.messagesContainer}>
        <div style={styles.messagesHeader}>
          <h3 style={styles.messagesTitle}>Messages</h3>
          <button 
            style={{...styles.button, ...styles.smallButton}} 
            onClick={handleClearMessages}
          >
            Clear
          </button>
        </div>
        
        <div style={styles.messagesList}>
          {messages.length === 0 ? (
            <p style={styles.emptyMessage}>No messages</p>
          ) : (
            messages.map((msg, index) => (
              <div key={index} style={styles.messageItem}>
                <div style={styles.messageHeader}>
                  <span style={styles.messageType}>{msg.type || 'unknown'}</span>
                  <span style={styles.messageTime}>{formatDate(msg.timestamp)}</span>
                </div>
                <pre style={styles.messageContent}>
                  {JSON.stringify(msg, null, 2)}
                </pre>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      {/* Metrics Toggle */}
      <div style={styles.toggleContainer}>
        <button 
          style={{
            ...styles.button, 
            ...styles.toggleButton,
            backgroundColor: showMetrics ? '#1890ff' : '#f0f0f0',
            color: showMetrics ? 'white' : 'black'
          }} 
          onClick={() => setShowMetrics(!showMetrics)}
        >
          {showMetrics ? 'Hide Metrics' : 'Show Metrics'}
        </button>
        
        <button 
          style={{
            ...styles.button, 
            ...styles.toggleButton,
            backgroundColor: showDebug ? '#1890ff' : '#f0f0f0',
            color: showDebug ? 'white' : 'black'
          }} 
          onClick={() => setShowDebug(!showDebug)}
        >
          {showDebug ? 'Hide Debug' : 'Show Debug'}
        </button>
      </div>
      
      {/* Metrics */}
      {showMetrics && (
        <div style={styles.metricsContainer}>
          <div style={styles.metricsHeader}>
            <h3 style={styles.metricsTitle}>Performance Metrics</h3>
            <button 
              style={{...styles.button, ...styles.smallButton}} 
              onClick={resetMetrics}
            >
              Reset
            </button>
          </div>
          
          <div style={styles.metricsList}>
            <div style={styles.metricItem}>
              <span style={styles.metricLabel}>Latency:</span>
              <span style={styles.metricValue}>{metrics.latency !== null ? `${metrics.latency}ms` : 'N/A'}</span>
            </div>
            <div style={styles.metricItem}>
              <span style={styles.metricLabel}>Messages Sent:</span>
              <span style={styles.metricValue}>{metrics.messagesSent}</span>
            </div>
            <div style={styles.metricItem}>
              <span style={styles.metricLabel}>Messages Received:</span>
              <span style={styles.metricValue}>{metrics.messagesReceived}</span>
            </div>
            <div style={styles.metricItem}>
              <span style={styles.metricLabel}>Bytes Sent:</span>
              <span style={styles.metricValue}>{metrics.bytesSent}</span>
            </div>
            <div style={styles.metricItem}>
              <span style={styles.metricLabel}>Bytes Received:</span>
              <span style={styles.metricValue}>{metrics.bytesReceived}</span>
            </div>
            <div style={styles.metricItem}>
              <span style={styles.metricLabel}>Last Activity:</span>
              <span style={styles.metricValue}>{metrics.lastActivity ? formatDate(metrics.lastActivity) : 'N/A'}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Component styles
const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    padding: '16px',
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    maxWidth: '800px',
    margin: '0 auto'
  },
  title: {
    margin: '0 0 16px 0',
    fontSize: '24px',
    fontWeight: 'bold'
  },
  statusContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px'
  },
  statusIndicator: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  statusDot: {
    width: '12px',
    height: '12px',
    borderRadius: '50%'
  },
  statusText: {
    fontWeight: 'bold'
  },
  buttonGroup: {
    display: 'flex',
    gap: '8px'
  },
  button: {
    padding: '8px 16px',
    backgroundColor: '#1890ff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px'
  },
  smallButton: {
    padding: '4px 8px',
    fontSize: '12px'
  },
  dangerButton: {
    backgroundColor: '#ff4d4f'
  },
  toggleButton: {
    flex: 1
  },
  errorContainer: {
    padding: '12px',
    backgroundColor: '#fff2f0',
    borderRadius: '4px',
    border: '1px solid #ffccc7',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  errorText: {
    color: '#ff4d4f',
    margin: 0
  },
  inputContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  inputRow: {
    display: 'flex',
    gap: '8px'
  },
  input: {
    flex: 1,
    padding: '8px 12px',
    borderRadius: '4px',
    border: '1px solid #d9d9d9',
    fontSize: '14px'
  },
  select: {
    padding: '8px 12px',
    borderRadius: '4px',
    border: '1px solid #d9d9d9',
    fontSize: '14px',
    minWidth: '120px'
  },
  messagesContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  messagesHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  messagesTitle: {
    margin: 0,
    fontSize: '18px',
    fontWeight: 'bold'
  },
  messagesList: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    maxHeight: '300px',
    overflowY: 'auto',
    padding: '8px',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px'
  },
  messageItem: {
    padding: '8px',
    backgroundColor: 'white',
    borderRadius: '4px',
    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
  },
  messageHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '4px'
  },
  messageType: {
    fontWeight: 'bold',
    color: '#1890ff'
  },
  messageTime: {
    fontSize: '12px',
    color: '#8c8c8c'
  },
  messageContent: {
    margin: 0,
    fontSize: '14px',
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word'
  },
  emptyMessage: {
    textAlign: 'center',
    color: '#8c8c8c',
    fontStyle: 'italic'
  },
  toggleContainer: {
    display: 'flex',
    gap: '8px'
  },
  metricsContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  metricsHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  metricsTitle: {
    margin: 0,
    fontSize: '18px',
    fontWeight: 'bold'
  },
  metricsList: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
    gap: '8px',
    padding: '8px',
    backgroundColor: '#f5f5f5',
    borderRadius: '4px'
  },
  metricItem: {
    padding: '8px',
    backgroundColor: 'white',
    borderRadius: '4px',
    display: 'flex',
    flexDirection: 'column'
  },
  metricLabel: {
    fontSize: '12px',
    color: '#8c8c8c'
  },
  metricValue: {
    fontWeight: 'bold'
  }
};

export default WebSocketManager;
