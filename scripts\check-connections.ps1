# Simple PowerShell script to check connections

Write-Host "Checking connections..." -ForegroundColor Cyan

# Check if localhost resolves
Write-Host "`nChecking localhost DNS resolution:" -ForegroundColor Yellow
try {
    $ipAddress = [System.Net.Dns]::GetHostAddresses("localhost")
    Write-Host "localhost resolves to $($ipAddress -join ', ')" -ForegroundColor Green
} catch {
    Write-Host "Error resolving localhost" -ForegroundColor Red
}

# Check if ports are in use
function Test-Port {
    param(
        [int]$Port
    )
    
    Write-Host "`nChecking if port $Port is in use:" -ForegroundColor Yellow
    
    try {
        $listener = New-Object System.Net.Sockets.TcpListener([System.Net.IPAddress]::Loopback, $Port)
        $listener.Start()
        Write-Host "Port $Port is available" -ForegroundColor Green
        $listener.Stop()
    } catch {
        Write-Host "Port $Port is already in use" -ForegroundColor Red
        
        # Try to identify what's using the port
        try {
            $process = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | 
                       Select-Object -First 1 OwningProcess
            
            if ($process) {
                $processInfo = Get-Process -Id $process.OwningProcess -ErrorAction SilentlyContinue
                if ($processInfo) {
                    Write-Host "Process using the port: $($processInfo.Name) (PID: $($process.OwningProcess))" -ForegroundColor Yellow
                }
            }
        } catch {
            Write-Host "Could not identify process" -ForegroundColor Red
        }
    }
}

Test-Port -Port 3000
Test-Port -Port 8000

# Test HTTP connections
Write-Host "`nTesting HTTP connections:" -ForegroundColor Yellow

function Test-HttpConnection {
    param(
        [string]$Url,
        [string]$Name
    )
    
    Write-Host "Testing connection to $Name ($Url)..." -ForegroundColor Yellow
    
    try {
        $request = [System.Net.WebRequest]::Create($Url)
        $request.Timeout = 5000
        $response = $request.GetResponse()
        
        Write-Host "Connected to $Name - Status: $([int]$response.StatusCode)" -ForegroundColor Green
        
        $stream = $response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $data = $reader.ReadToEnd()
        $reader.Close()
        $response.Close()
        
        if ($data.Length -gt 0) {
            $preview = if ($data.Length -gt 100) { $data.Substring(0, 100) + "..." } else { $data }
            Write-Host "Response data (first 100 chars): $preview" -ForegroundColor Gray
        } else {
            Write-Host "No data received in response" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Failed to connect to $Name" -ForegroundColor Red
    }
}

# Test standard connections
Test-HttpConnection -Url "http://localhost:3000" -Name "Frontend"
Test-HttpConnection -Url "http://localhost:8000" -Name "Backend"
Test-HttpConnection -Url "http://localhost:3000/fallback.html" -Name "Fallback page"
Test-HttpConnection -Url "http://localhost:8000/api/health-check" -Name "Backend health check"

# Test Docker container status
Write-Host "`nChecking Docker container status:" -ForegroundColor Yellow
try {
    $containers = docker ps --format "{{.Names}}: {{.Status}}"
    if ($containers) {
        Write-Host "Running containers:" -ForegroundColor Green
        $containers | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
    } else {
        Write-Host "No running containers found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error checking Docker containers" -ForegroundColor Red
}

Write-Host "`nChecks complete!" -ForegroundColor Cyan
