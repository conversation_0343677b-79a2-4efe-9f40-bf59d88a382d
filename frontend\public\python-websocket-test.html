<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #cccccc; cursor: not-allowed; }
        #log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Python WebSocket Test</h1>
    
    <div class="card">
        <h2>WebSocket Connection</h2>
        <button id="connect-btn">Connect to ws://localhost:8765</button>
        <button id="disconnect-btn" disabled>Disconnect</button>
        <button id="ping-btn" disabled>Send Ping</button>
        <button id="clear-btn">Clear Log</button>
        <div id="status">Not connected</div>
    </div>
    
    <div class="card">
        <h2>Log</h2>
        <div id="log"></div>
    </div>
    
    <script>
        // DOM Elements
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const pingBtn = document.getElementById('ping-btn');
        const clearBtn = document.getElementById('clear-btn');
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        
        // WebSocket instance
        let socket = null;
        
        // Log a message to the log div
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = type;
            
            const timestamp = new Date().toISOString();
            entry.innerHTML = `<span>[${timestamp}]</span> ${message}`;
            
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Connect to WebSocket
        function connect() {
            try {
                log('Connecting to ws://localhost:8765...');
                
                // Create WebSocket connection
                socket = new WebSocket('ws://localhost:8765');
                
                // Connection opened
                socket.onopen = function(event) {
                    log('Connection established', 'success');
                    statusDiv.textContent = 'Connected';
                    statusDiv.className = 'success';
                    
                    // Update button states
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    pingBtn.disabled = false;
                };
                
                // Listen for messages
                socket.onmessage = function(event) {
                    try {
                        // Try to parse as JSON
                        const data = JSON.parse(event.data);
                        log(`Received: <pre>${JSON.stringify(data, null, 2)}</pre>`);
                    } catch (e) {
                        // Not JSON, display as text
                        log(`Received: ${event.data}`);
                    }
                };
                
                // Connection closed
                socket.onclose = function(event) {
                    const reason = event.reason ? ` (${event.reason})` : '';
                    log(`Connection closed with code ${event.code}${reason}`, 'warning');
                    statusDiv.textContent = 'Disconnected';
                    statusDiv.className = 'error';
                    
                    // Update button states
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    
                    socket = null;
                };
                
                // Connection error
                socket.onerror = function(event) {
                    log('WebSocket error occurred', 'error');
                    console.error('WebSocket error:', event);
                };
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }
        
        // Disconnect from WebSocket
        function disconnect() {
            if (socket) {
                socket.close(1000, 'User initiated disconnect');
            }
        }
        
        // Send a ping message
        function sendPing() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const ping = JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
                
                try {
                    socket.send(ping);
                    log(`Sent ping: ${ping}`, 'success');
                } catch (error) {
                    log(`Error sending ping: ${error.message}`, 'error');
                }
            } else {
                log('Cannot send ping: WebSocket is not connected', 'error');
            }
        }
        
        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        pingBtn.addEventListener('click', sendPing);
        clearBtn.addEventListener('click', function() {
            logDiv.innerHTML = '';
            log('Log cleared');
        });
        
        // Initial log
        log('Python WebSocket Test loaded. Click "Connect" to start.');
    </script>
</body>
</html>
