import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Steps, 
  Alert, 
  Divider,
  Row,
  Col,
  message,
  Modal
} from 'antd';
import { 
  TeamOutlined, 
  CommentOutlined, 
  DragOutlined, 
  EyeOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { CollaborativeAppBuilder } from './CollaborativeAppBuilder';
import { useCollaboration } from '../../contexts/CollaborationContext';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

// Demo component to showcase collaboration features
export const CollaborationDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showDemo, setShowDemo] = useState(false);
  const [demoComponents, setDemoComponents] = useState([
    {
      id: 'demo-button-1',
      type: 'button',
      props: { text: 'Click Me', variant: 'primary' },
      position: { x: 100, y: 100 }
    },
    {
      id: 'demo-text-1',
      type: 'text',
      props: { content: 'Welcome to Collaborative App Builder!', fontSize: 18 },
      position: { x: 100, y: 200 }
    }
  ]);

  const { isConnected, activeParticipants } = useCollaboration();

  const demoSteps = [
    {
      title: 'Real-time Collaboration',
      description: 'Multiple users can edit the same app simultaneously',
      icon: <TeamOutlined />,
      features: [
        'Live cursor tracking',
        'Component selection indicators',
        'Instant synchronization',
        'Conflict resolution'
      ]
    },
    {
      title: 'Contextual Comments',
      description: 'Add comments to specific components or canvas areas',
      icon: <CommentOutlined />,
      features: [
        'Component-specific comments',
        'Threaded discussions',
        '@mention notifications',
        'Comment resolution'
      ]
    },
    {
      title: 'Collaborative Drag & Drop',
      description: 'Drag and drop components with real-time updates',
      icon: <DragOutlined />,
      features: [
        'Operational transformation',
        'Concurrent editing support',
        'Visual feedback',
        'Undo/redo synchronization'
      ]
    },
    {
      title: 'User Presence',
      description: 'See who\'s online and what they\'re working on',
      icon: <EyeOutlined />,
      features: [
        'Active user indicators',
        'Cursor positions',
        'Selection highlights',
        'Status tracking'
      ]
    }
  ];

  const handleStartDemo = () => {
    setShowDemo(true);
    message.success('Demo started! Try collaborating with multiple browser tabs.');
  };

  const handleStepChange = (step) => {
    setCurrentStep(step);
  };

  const renderFeatureCard = (step, index) => (
    <Card
      key={index}
      hoverable
      style={{ 
        height: '100%',
        cursor: 'pointer',
        border: currentStep === index ? '2px solid #1890ff' : '1px solid #f0f0f0'
      }}
      onClick={() => handleStepChange(index)}
    >
      <div style={{ textAlign: 'center', marginBottom: 16 }}>
        <div style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }}>
          {step.icon}
        </div>
        <Title level={4}>{step.title}</Title>
      </div>
      
      <Paragraph>{step.description}</Paragraph>
      
      <ul style={{ paddingLeft: 20 }}>
        {step.features.map((feature, idx) => (
          <li key={idx} style={{ marginBottom: 4 }}>
            <Text>{feature}</Text>
          </li>
        ))}
      </ul>
    </Card>
  );

  const renderInstructions = () => {
    const instructions = {
      0: {
        title: 'Test Real-time Collaboration',
        steps: [
          'Open this page in multiple browser tabs or share with team members',
          'Move components around and see changes appear instantly',
          'Watch cursor movements and selections from other users',
          'Try editing the same component simultaneously'
        ]
      },
      1: {
        title: 'Try Contextual Comments',
        steps: [
          'Right-click on any component to add a comment',
          'Click the comment bubble to view and reply',
          'Use @username to mention other collaborators',
          'Resolve comments when issues are addressed'
        ]
      },
      2: {
        title: 'Experience Collaborative Drag & Drop',
        steps: [
          'Drag components from the palette to the canvas',
          'Move existing components around',
          'Watch as changes sync across all connected users',
          'Notice how conflicts are automatically resolved'
        ]
      },
      3: {
        title: 'Observe User Presence',
        steps: [
          'See active collaborators in the top toolbar',
          'Watch colored cursors move in real-time',
          'Notice selection highlights around components',
          'View user status (active, idle, away)'
        ]
      }
    };

    const current = instructions[currentStep];

    return (
      <Card title={current.title} style={{ marginTop: 16 }}>
        <Steps direction="vertical" size="small">
          {current.steps.map((step, index) => (
            <Step
              key={index}
              title={step}
              status="process"
              icon={<CheckCircleOutlined />}
            />
          ))}
        </Steps>
      </Card>
    );
  };

  if (showDemo) {
    return (
      <div style={{ height: '100vh' }}>
        <CollaborativeAppBuilder 
          appId="demo-app"
          initialComponents={demoComponents}
        />
        
        {/* Demo controls overlay */}
        <div style={{
          position: 'fixed',
          top: 16,
          right: 16,
          zIndex: 1001
        }}>
          <Button 
            onClick={() => setShowDemo(false)}
            type="primary"
          >
            Exit Demo
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: 24, maxWidth: 1200, margin: '0 auto' }}>
      <div style={{ textAlign: 'center', marginBottom: 48 }}>
        <Title level={1}>
          <TeamOutlined style={{ marginRight: 16, color: '#1890ff' }} />
          Collaborative App Builder Demo
        </Title>
        <Paragraph style={{ fontSize: 18, color: '#666' }}>
          Experience real-time collaboration features including live editing, 
          contextual comments, and user presence tracking.
        </Paragraph>
      </div>

      {/* Connection status */}
      <Alert
        message={
          isConnected 
            ? `Connected to collaboration server (${activeParticipants.length} active users)`
            : 'Not connected to collaboration server'
        }
        type={isConnected ? 'success' : 'warning'}
        style={{ marginBottom: 24 }}
        showIcon
      />

      {/* Feature overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        {demoSteps.map((step, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            {renderFeatureCard(step, index)}
          </Col>
        ))}
      </Row>

      {/* Instructions */}
      {renderInstructions()}

      {/* Demo controls */}
      <div style={{ textAlign: 'center', marginTop: 32 }}>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={handleStartDemo}
          >
            Start Interactive Demo
          </Button>
          
          <Button
            size="large"
            onClick={() => {
              Modal.info({
                title: 'Multi-User Testing',
                content: (
                  <div>
                    <Paragraph>To test collaboration features:</Paragraph>
                    <ol>
                      <li>Open this demo in multiple browser tabs</li>
                      <li>Share the URL with team members</li>
                      <li>Try editing simultaneously</li>
                      <li>Add comments and see real-time updates</li>
                    </ol>
                    <Alert
                      message="Tip: Use incognito/private browsing windows to simulate different users"
                      type="info"
                      style={{ marginTop: 16 }}
                    />
                  </div>
                ),
                width: 500
              });
            }}
          >
            Multi-User Testing Guide
          </Button>
        </Space>
      </div>

      <Divider style={{ margin: '48px 0' }} />

      {/* Technical details */}
      <Card title="Technical Implementation" style={{ marginTop: 24 }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Title level={4}>Backend Features</Title>
            <ul>
              <li>Django WebSocket consumers for real-time communication</li>
              <li>Operational transformation for conflict resolution</li>
              <li>Comment system with threading and mentions</li>
              <li>User presence and session management</li>
              <li>Database models for collaboration data</li>
            </ul>
          </Col>
          <Col xs={24} md={12}>
            <Title level={4}>Frontend Features</Title>
            <ul>
              <li>React context for collaboration state management</li>
              <li>Real-time cursor and selection tracking</li>
              <li>Collaborative drag-and-drop interface</li>
              <li>Comment bubbles and threaded discussions</li>
              <li>User presence indicators and avatars</li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default CollaborationDemo;
