/**
 * Tutorial Manager
 * 
 * Central manager for the tutorial system that handles tutorial registration,
 * execution, progress tracking, and coordination between components.
 */

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { message } from 'antd';
import tutorialStorage from './TutorialStorage';
import {
  TUTORIAL_STATUS,
  TUTORIAL_EVENTS,
  createTutorialProgress
} from './types';

// Tutorial Manager Context
const TutorialContext = createContext();

// Action Types
const TUTORIAL_ACTIONS = {
  REGISTER_TUTORIAL: 'REGISTER_TUTORIAL',
  UNREGISTER_TUTORIAL: 'UNREGISTER_TUTORIAL',
  START_TUTORIAL: 'START_TUTORIAL',
  PAUSE_TUTORIAL: 'PAUSE_TUTORIAL',
  RESUME_TUTORIAL: 'RESUME_TUTORIAL',
  COMPLETE_TUTORIAL: 'COMPLETE_TUTORIAL',
  <PERSON>IP_TUTORIAL: 'SKIP_TUTORIAL',
  NEXT_STEP: 'NEXT_STEP',
  PREVIOUS_STEP: 'PREVIOUS_STEP',
  SKIP_STEP: 'SKIP_STEP',
  SET_CURRENT_STEP: 'SET_CURRENT_STEP',
  UPDATE_PROGRESS: 'UPDATE_PROGRESS',
  LOAD_PROGRESS: 'LOAD_PROGRESS',
  SET_PREFERENCES: 'SET_PREFERENCES',
  SHOW_CONTEXT_HELP: 'SHOW_CONTEXT_HELP',
  HIDE_CONTEXT_HELP: 'HIDE_CONTEXT_HELP',
  SET_ACTIVE_TUTORIAL: 'SET_ACTIVE_TUTORIAL'
};

// Initial State
const initialState = {
  tutorials: new Map(),
  activeTutorial: null,
  currentStep: null,
  currentStepIndex: 0,
  isActive: false,
  isPaused: false,
  progress: new Map(),
  preferences: tutorialStorage.getTutorialPreferences(),
  contextHelp: {
    visible: false,
    content: null,
    position: null
  },
  statistics: {
    totalCompleted: 0,
    totalStarted: 0,
    averageTime: 0
  }
};

// Reducer
function tutorialReducer(state, action) {
  switch (action.type) {
    case TUTORIAL_ACTIONS.REGISTER_TUTORIAL:
      const newTutorials = new Map(state.tutorials);
      newTutorials.set(action.payload.id, action.payload);
      return {
        ...state,
        tutorials: newTutorials
      };

    case TUTORIAL_ACTIONS.UNREGISTER_TUTORIAL:
      const updatedTutorials = new Map(state.tutorials);
      updatedTutorials.delete(action.payload);
      return {
        ...state,
        tutorials: updatedTutorials
      };

    case TUTORIAL_ACTIONS.SET_ACTIVE_TUTORIAL:
      return {
        ...state,
        activeTutorial: action.payload,
        isActive: !!action.payload,
        currentStepIndex: 0,
        currentStep: action.payload?.steps[0] || null,
        isPaused: false
      };

    case TUTORIAL_ACTIONS.START_TUTORIAL:
      return {
        ...state,
        activeTutorial: action.payload.tutorial,
        isActive: true,
        currentStepIndex: action.payload.stepIndex || 0,
        currentStep: action.payload.tutorial.steps[action.payload.stepIndex || 0],
        isPaused: false
      };

    case TUTORIAL_ACTIONS.PAUSE_TUTORIAL:
      return {
        ...state,
        isPaused: true
      };

    case TUTORIAL_ACTIONS.RESUME_TUTORIAL:
      return {
        ...state,
        isPaused: false
      };

    case TUTORIAL_ACTIONS.COMPLETE_TUTORIAL:
    case TUTORIAL_ACTIONS.SKIP_TUTORIAL:
      return {
        ...state,
        activeTutorial: null,
        currentStep: null,
        currentStepIndex: 0,
        isActive: false,
        isPaused: false
      };

    case TUTORIAL_ACTIONS.NEXT_STEP:
      if (!state.activeTutorial) return state;
      
      const nextIndex = state.currentStepIndex + 1;
      const nextStep = state.activeTutorial.steps[nextIndex];
      
      return {
        ...state,
        currentStepIndex: nextIndex,
        currentStep: nextStep || null,
        isActive: !!nextStep
      };

    case TUTORIAL_ACTIONS.PREVIOUS_STEP:
      if (!state.activeTutorial) return state;
      
      const prevIndex = Math.max(0, state.currentStepIndex - 1);
      const prevStep = state.activeTutorial.steps[prevIndex];
      
      return {
        ...state,
        currentStepIndex: prevIndex,
        currentStep: prevStep
      };

    case TUTORIAL_ACTIONS.SET_CURRENT_STEP:
      if (!state.activeTutorial) return state;
      
      const stepIndex = action.payload;
      const step = state.activeTutorial.steps[stepIndex];
      
      return {
        ...state,
        currentStepIndex: stepIndex,
        currentStep: step || null
      };

    case TUTORIAL_ACTIONS.UPDATE_PROGRESS:
      const newProgress = new Map(state.progress);
      newProgress.set(action.payload.tutorialId, action.payload);
      return {
        ...state,
        progress: newProgress
      };

    case TUTORIAL_ACTIONS.LOAD_PROGRESS:
      return {
        ...state,
        progress: new Map(Object.entries(action.payload))
      };

    case TUTORIAL_ACTIONS.SET_PREFERENCES:
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload }
      };

    case TUTORIAL_ACTIONS.SHOW_CONTEXT_HELP:
      return {
        ...state,
        contextHelp: {
          visible: true,
          content: action.payload.content,
          position: action.payload.position
        }
      };

    case TUTORIAL_ACTIONS.HIDE_CONTEXT_HELP:
      return {
        ...state,
        contextHelp: {
          visible: false,
          content: null,
          position: null
        }
      };

    default:
      return state;
  }
}

// Tutorial Manager Provider Component
export const TutorialProvider = ({ children, userId = 'anonymous' }) => {
  const [state, dispatch] = useReducer(tutorialReducer, initialState);

  // Load initial data
  useEffect(() => {
    const progress = tutorialStorage.getAllTutorialProgress(userId);
    dispatch({ type: TUTORIAL_ACTIONS.LOAD_PROGRESS, payload: progress });
  }, [userId]);

  // Tutorial Management Functions
  const registerTutorial = useCallback((tutorial) => {
    dispatch({ type: TUTORIAL_ACTIONS.REGISTER_TUTORIAL, payload: tutorial });
  }, []);

  const unregisterTutorial = useCallback((tutorialId) => {
    dispatch({ type: TUTORIAL_ACTIONS.UNREGISTER_TUTORIAL, payload: tutorialId });
  }, []);

  const startTutorial = useCallback((tutorialId, stepIndex = 0) => {
    const tutorial = state.tutorials.get(tutorialId);
    if (!tutorial) {
      message.error('Tutorial not found');
      return false;
    }

    // Check prerequisites
    if (tutorial.prerequisites.length > 0) {
      const hasPrerequisites = tutorial.prerequisites.every(prereqId => {
        const prereqProgress = state.progress.get(prereqId);
        return prereqProgress?.status === TUTORIAL_STATUS.COMPLETED;
      });

      if (!hasPrerequisites) {
        message.warning('Please complete prerequisite tutorials first');
        return false;
      }
    }

    // Create or update progress
    const progress = createTutorialProgress({
      tutorialId,
      userId,
      status: TUTORIAL_STATUS.IN_PROGRESS,
      currentStepIndex: stepIndex,
      startedAt: new Date().toISOString()
    });

    tutorialStorage.saveTutorialProgress(progress);
    dispatch({ type: TUTORIAL_ACTIONS.UPDATE_PROGRESS, payload: progress });
    dispatch({ type: TUTORIAL_ACTIONS.START_TUTORIAL, payload: { tutorial, stepIndex } });

    // Call tutorial's onStart callback
    if (tutorial.onStart) {
      tutorial.onStart(tutorial, progress);
    }

    // Emit event
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_STARTED, { tutorialId, stepIndex });

    return true;
  }, [state.tutorials, state.progress, userId]);

  const pauseTutorial = useCallback(() => {
    if (!state.activeTutorial) return;

    const progress = state.progress.get(state.activeTutorial.id);
    if (progress) {
      const updatedProgress = {
        ...progress,
        status: TUTORIAL_STATUS.PAUSED,
        pausedAt: new Date().toISOString()
      };

      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({ type: TUTORIAL_ACTIONS.UPDATE_PROGRESS, payload: updatedProgress });
    }

    dispatch({ type: TUTORIAL_ACTIONS.PAUSE_TUTORIAL });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_PAUSED, { tutorialId: state.activeTutorial.id });
  }, [state.activeTutorial, state.progress]);

  const resumeTutorial = useCallback(() => {
    if (!state.activeTutorial) return;

    const progress = state.progress.get(state.activeTutorial.id);
    if (progress) {
      const updatedProgress = {
        ...progress,
        status: TUTORIAL_STATUS.IN_PROGRESS,
        pausedAt: null
      };

      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({ type: TUTORIAL_ACTIONS.UPDATE_PROGRESS, payload: updatedProgress });
    }

    dispatch({ type: TUTORIAL_ACTIONS.RESUME_TUTORIAL });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_RESUMED, { tutorialId: state.activeTutorial.id });
  }, [state.activeTutorial, state.progress]);

  const completeTutorial = useCallback(() => {
    if (!state.activeTutorial) return;

    const tutorial = state.activeTutorial;
    const progress = state.progress.get(tutorial.id);
    
    if (progress) {
      const updatedProgress = {
        ...progress,
        status: TUTORIAL_STATUS.COMPLETED,
        completedAt: new Date().toISOString(),
        completedSteps: tutorial.steps.map((_, index) => index)
      };

      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({ type: TUTORIAL_ACTIONS.UPDATE_PROGRESS, payload: updatedProgress });

      // Add completion badge
      tutorialStorage.addTutorialBadge({
        tutorialId: tutorial.id,
        title: tutorial.title,
        category: tutorial.category,
        difficulty: tutorial.difficulty
      }, userId);
    }

    // Call tutorial's onComplete callback
    if (tutorial.onComplete) {
      tutorial.onComplete(tutorial, progress);
    }

    dispatch({ type: TUTORIAL_ACTIONS.COMPLETE_TUTORIAL });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_COMPLETED, { tutorialId: tutorial.id });

    message.success(`Tutorial "${tutorial.title}" completed!`);
  }, [state.activeTutorial, state.progress, userId]);

  const skipTutorial = useCallback(() => {
    if (!state.activeTutorial) return;

    const tutorial = state.activeTutorial;
    const progress = state.progress.get(tutorial.id);
    
    if (progress) {
      const updatedProgress = {
        ...progress,
        status: TUTORIAL_STATUS.SKIPPED,
        completedAt: new Date().toISOString()
      };

      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({ type: TUTORIAL_ACTIONS.UPDATE_PROGRESS, payload: updatedProgress });
    }

    // Call tutorial's onSkip callback
    if (tutorial.onSkip) {
      tutorial.onSkip(tutorial, progress);
    }

    dispatch({ type: TUTORIAL_ACTIONS.SKIP_TUTORIAL });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_SKIPPED, { tutorialId: tutorial.id });
  }, [state.activeTutorial, state.progress]);

  // Step Navigation
  const nextStep = useCallback(() => {
    if (!state.activeTutorial || !state.currentStep) return;

    const currentProgress = state.progress.get(state.activeTutorial.id);
    if (currentProgress) {
      const updatedProgress = {
        ...currentProgress,
        currentStepIndex: state.currentStepIndex + 1,
        completedSteps: [...new Set([...currentProgress.completedSteps, state.currentStepIndex])]
      };

      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({ type: TUTORIAL_ACTIONS.UPDATE_PROGRESS, payload: updatedProgress });
    }

    // Check if tutorial is complete
    if (state.currentStepIndex >= state.activeTutorial.steps.length - 1) {
      completeTutorial();
      return;
    }

    dispatch({ type: TUTORIAL_ACTIONS.NEXT_STEP });
    emitTutorialEvent(TUTORIAL_EVENTS.STEP_COMPLETED, { 
      tutorialId: state.activeTutorial.id, 
      stepIndex: state.currentStepIndex 
    });
  }, [state.activeTutorial, state.currentStep, state.currentStepIndex, state.progress, completeTutorial]);

  const previousStep = useCallback(() => {
    if (!state.activeTutorial || state.currentStepIndex <= 0) return;

    dispatch({ type: TUTORIAL_ACTIONS.PREVIOUS_STEP });
  }, [state.activeTutorial, state.currentStepIndex]);

  const goToStep = useCallback((stepIndex) => {
    if (!state.activeTutorial || stepIndex < 0 || stepIndex >= state.activeTutorial.steps.length) return;

    dispatch({ type: TUTORIAL_ACTIONS.SET_CURRENT_STEP, payload: stepIndex });
  }, [state.activeTutorial]);

  // Helper function to emit tutorial events
  const emitTutorialEvent = useCallback((eventType, data) => {
    // This could be extended to integrate with analytics services
    console.log('Tutorial Event:', eventType, data);
    
    // Dispatch custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('tutorialEvent', {
      detail: { type: eventType, data }
    }));
  }, []);

  // Context value
  const value = {
    // State
    ...state,
    
    // Tutorial Management
    registerTutorial,
    unregisterTutorial,
    startTutorial,
    pauseTutorial,
    resumeTutorial,
    completeTutorial,
    skipTutorial,
    
    // Step Navigation
    nextStep,
    previousStep,
    goToStep,
    
    // Utilities
    getTutorial: (id) => state.tutorials.get(id),
    getTutorialProgress: (id) => state.progress.get(id),
    getAllTutorials: () => Array.from(state.tutorials.values()),
    getAvailableTutorials: () => Array.from(state.tutorials.values()).filter(tutorial => {
      const progress = state.progress.get(tutorial.id);
      return !progress || progress.status !== TUTORIAL_STATUS.COMPLETED || !state.preferences.skipCompletedTutorials;
    }),
    
    // Statistics
    getStatistics: () => tutorialStorage.getTutorialStatistics(userId),
    getBadges: () => tutorialStorage.getTutorialBadges(userId)
  };

  return (
    <TutorialContext.Provider value={value}>
      {children}
    </TutorialContext.Provider>
  );
};

// Hook to use tutorial context
export const useTutorial = () => {
  const context = useContext(TutorialContext);
  if (!context) {
    throw new Error('useTutorial must be used within a TutorialProvider');
  }
  return context;
};

export default TutorialProvider;
