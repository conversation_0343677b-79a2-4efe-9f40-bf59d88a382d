/**
 * Fallback API Service
 *
 * This service provides HTTP-based API access as a fallback when WebSocket
 * connections are not available or fail. It includes robust error handling,
 * multiple endpoint attempts, and caching.
 */

import API_ENDPOINTS, { getAllEndpointUrls } from '../config/api';

/**
 * Helper function to attempt fetch with multiple endpoints
 * @param {Array<string>} endpoints - Array of endpoint URLs to try
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - Fetched data
 * @throws {Error} - If all endpoints fail
 */
const attemptFetchWithMultipleEndpoints = async (endpoints, options = {}) => {
  const errors = [];

  // Set default options
  const fetchOptions = {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    credentials: 'same-origin',
    ...options
  };

  // Add content type for POST/PUT requests with body
  if (fetchOptions.body && !fetchOptions.headers['Content-Type']) {
    fetchOptions.headers['Content-Type'] = 'application/json';
  }

  console.log(`Attempting to fetch from ${endpoints.length} endpoints with method ${fetchOptions.method}`);

  // Try each endpoint in order
  for (const endpoint of endpoints) {
    try {
      console.log(`Attempting to fetch from: ${endpoint}`);

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(endpoint, {
        ...fetchOptions,
        signal: controller.signal
      });

      // Clear timeout
      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        console.log(`Successfully fetched data from: ${endpoint}`);
        return data;
      }

      // If response is not OK, add to errors and try next endpoint
      errors.push({
        endpoint,
        status: response.status,
        statusText: response.statusText,
        timestamp: new Date().toISOString()
      });

      console.warn(`HTTP error ${response.status} from ${endpoint}: ${response.statusText}`);
    } catch (error) {
      console.warn(`Failed to fetch from ${endpoint}:`, error);

      errors.push({
        endpoint,
        error: error.message || 'Unknown error',
        name: error.name,
        timestamp: new Date().toISOString()
      });

      // Continue to the next endpoint
    }
  }

  // If we get here, all endpoints failed
  const error = new Error('All fetch attempts failed');
  error.endpoints = endpoints;
  error.errors = errors;
  throw error;
};

/**
 * Fallback API Service
 */
const fallbackApiService = {
  /**
   * Fetch app data without WebSocket
   * @returns {Promise<Object>} App data
   */
  fetchAppData: async () => {
    try {
      // Get all possible endpoints for app data
      const endpoints = getAllEndpointUrls(API_ENDPOINTS.APP_DATA);

      // Try to fetch from all endpoints
      const data = await attemptFetchWithMultipleEndpoints(endpoints);

      // Cache the successful data
      try {
        localStorage.setItem('app_data', JSON.stringify(data));
        localStorage.setItem('app_data_timestamp', Date.now().toString());
      } catch (cacheError) {
        console.warn('Failed to cache app data:', cacheError);
      }

      return data;
    } catch (error) {
      console.error('Error fetching app data from all endpoints:', error);

      // Try to get cached data
      try {
        const cachedData = localStorage.getItem('app_data');
        const cachedTimestamp = localStorage.getItem('app_data_timestamp');

        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          const cacheAge = Date.now() - (parseInt(cachedTimestamp) || 0);

          console.log(`Using cached app data (${Math.round(cacheAge / 1000 / 60)} minutes old)`);
          return parsedData;
        }
      } catch (cacheError) {
        console.warn('Failed to retrieve cached data:', cacheError);
      }

      // Return minimal data structure to prevent UI errors
      return {
        app: {
          name: 'App Builder',
          components: [],
          status: 'offline'
        },
        _meta: {
          offline: true,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      };
    }
  },

  /**
   * Save app data
   * @param {Object} data - App data to save
   * @returns {Promise<Object>} Response data
   */
  saveAppData: async (data) => {
    try {
      // Get all possible endpoints for saving app data
      const endpoints = getAllEndpointUrls(API_ENDPOINTS.SAVE_APP_DATA);

      // Try to save to all endpoints
      return await attemptFetchWithMultipleEndpoints(endpoints, {
        method: 'POST',
        body: JSON.stringify(data)
      });
    } catch (error) {
      console.error('Error saving app data to all endpoints:', error);

      // Cache the data locally for later sync
      try {
        localStorage.setItem('app_data_pending_save', JSON.stringify(data));
        localStorage.setItem('app_data_pending_save_timestamp', Date.now().toString());
      } catch (cacheError) {
        console.warn('Failed to cache pending save data:', cacheError);
      }

      throw error;
    }
  },

  /**
   * Check API health
   * @returns {Promise<Object>} Health status
   */
  checkHealth: async () => {
    try {
      // Get all possible endpoints for health check
      const endpoints = getAllEndpointUrls(API_ENDPOINTS.HEALTH);

      // Try to fetch from all endpoints
      return await attemptFetchWithMultipleEndpoints(endpoints);
    } catch (error) {
      console.error('Error checking API health:', error);

      return {
        status: 'error',
        message: 'API health check failed',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  },

  /**
   * Get API status
   * @returns {Promise<Object>} API status
   */
  getStatus: async () => {
    try {
      // Get all possible endpoints for status check
      const endpoints = getAllEndpointUrls(API_ENDPOINTS.STATUS);

      // Try to fetch from all endpoints
      return await attemptFetchWithMultipleEndpoints(endpoints);
    } catch (error) {
      console.error('Error getting API status:', error);

      return {
        status: 'error',
        message: 'API status check failed',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
};

export default fallbackApiService;