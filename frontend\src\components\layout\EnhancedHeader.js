import React, { useState } from 'react';
import { Layout, Typo<PERSON>, <PERSON><PERSON>, Drawer, Space } from 'antd';
import { MenuOutlined, AppstoreOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useEnhancedTheme } from '../../contexts/EnhancedThemeContext';
import DarkModeToggle from '../ui/DarkModeToggle';

const { Header } = Layout;
const { Title } = Typography;

// Styled components with WCAG-compliant contrast ratios
const StyledHeader = styled(Header)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  transition: all 0.3s ease;
  min-height: 64px;

  /* Ensure proper contrast for header background */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-surface);
    opacity: 0.98;
    z-index: -1;
  }

  /* Focus management for accessibility */
  &:focus-within {
    box-shadow: var(--shadow-lg);
  }

  @media (max-width: 768px) {
    padding: 0 16px;
    min-height: 56px;
  }

  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }
`;

const LogoSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;

  /* Ensure proper focus styles for accessibility */
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: 4px;
  }

  &:hover {
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }

  .logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    border-radius: 8px;
    color: white;
    font-size: 20px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;

    /* Ensure icon contrast meets WCAG AA standards */
    filter: contrast(1.1);

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }
  }

  .logo-text {
    color: var(--color-text);
    margin: 0;
    font-weight: 600;
    font-size: 20px;
    line-height: 1.2;

    /* Ensure text contrast meets WCAG AA standards (4.5:1) */
    text-shadow: 0 0 1px var(--color-background);

    @media (max-width: 480px) {
      display: none;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      font-weight: 700;
      text-shadow: 1px 1px 2px var(--color-background);
    }
  }

  /* Keyboard navigation support */
  &[tabindex] {
    border-radius: 4px;
  }

  @media (prefers-reduced-motion: reduce) {
    transition: none;

    &:hover {
      transform: none;
    }
  }
`;

const HeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    gap: 8px;
  }
`;

const MobileMenuButton = styled(Button)`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid var(--color-border);
    background-color: var(--color-surface);
    color: var(--color-text);
    transition: all 0.3s ease;

    /* Ensure button meets WCAG contrast requirements */
    &:hover, &:focus {
      border-color: var(--color-primary);
      color: var(--color-primary);
      background-color: var(--color-background-secondary);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:active {
      transform: scale(0.95);
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      border-width: 2px;
      font-weight: 600;
    }

    /* Keyboard focus indicator */
    &:focus-visible {
      outline: 2px solid var(--color-primary);
      outline-offset: 2px;
    }
  }
`;

const DesktopActions = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;

  @media (max-width: 768px) {
    display: none;
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border-light);
  font-size: 12px;
  color: var(--color-text);
  font-weight: 500;
  transition: all 0.3s ease;

  /* Ensure status text meets WCAG AA contrast ratio (4.5:1) */
  text-shadow: 0 0 1px var(--color-background-secondary);

  /* Enhanced visual hierarchy */
  box-shadow: var(--shadow-sm);

  &:hover {
    background-color: var(--color-background-tertiary);
    box-shadow: var(--shadow-md);
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--color-success);
    box-shadow: 0 0 4px var(--color-success);
    animation: pulse 2s infinite;
    position: relative;

    /* Add a subtle glow for better visibility */
    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: 50%;
      background-color: var(--color-success);
      opacity: 0.3;
      animation: pulse 2s infinite;
    }
  }

  /* High contrast mode adjustments */
  @media (prefers-contrast: high) {
    border-width: 2px;
    font-weight: 600;
    background-color: var(--color-surface);

    .status-dot {
      border: 2px solid var(--color-text);
    }
  }

  @keyframes pulse {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .status-dot {
      animation: none;

      &::after {
        animation: none;
      }
    }
  }
`;

const MobileDrawer = styled(Drawer)`
  .ant-drawer-content {
    background-color: var(--color-surface);
    border-left: 1px solid var(--color-border-light);
  }

  .ant-drawer-header {
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border-light);
    padding: 16px 24px;

    .ant-drawer-title {
      color: var(--color-text);
      font-weight: 600;
      font-size: 18px;
    }

    .ant-drawer-close {
      color: var(--color-text-secondary);
      transition: all 0.3s ease;

      &:hover {
        color: var(--color-primary);
        background-color: var(--color-background-secondary);
      }

      &:focus {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
      }
    }
  }

  .ant-drawer-body {
    padding: 24px;
    background-color: var(--color-surface);
  }

  /* Ensure proper contrast for drawer overlay */
  .ant-drawer-mask {
    background-color: rgba(0, 0, 0, 0.45);
    backdrop-filter: blur(4px);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .ant-drawer-content {
      border-left-width: 3px;
    }

    .ant-drawer-header {
      border-bottom-width: 2px;
    }
  }
`;

const DrawerContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const DrawerSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border-light);
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--color-background-tertiary);
    box-shadow: var(--shadow-sm);
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 8px;

    /* Ensure title text meets WCAG AA contrast */
    text-shadow: 0 0 1px var(--color-background-secondary);
  }

  /* High contrast mode adjustments */
  @media (prefers-contrast: high) {
    border-width: 2px;

    .section-title {
      font-weight: 700;
      text-shadow: 1px 1px 2px var(--color-background);
    }
  }
`;

const EnhancedHeader = ({
  title = "App Builder 201",
  showStatus = true,
  onLogoClick,
  children
}) => {
  const { isDarkMode, themeMode } = useEnhancedTheme();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  const handleLogoClick = (event) => {
    // Handle both click and keyboard events
    if (event.type === 'keydown' && event.key !== 'Enter' && event.key !== ' ') {
      return;
    }

    event.preventDefault();

    if (onLogoClick) {
      onLogoClick();
    } else {
      // Default behavior - scroll to top or navigate to home
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const toggleMobileMenu = () => {
    setMobileMenuVisible(!mobileMenuVisible);
  };

  const closeMobileMenu = () => {
    setMobileMenuVisible(false);
  };

  // Handle escape key to close mobile menu
  const handleKeyDown = (event) => {
    if (event.key === 'Escape' && mobileMenuVisible) {
      closeMobileMenu();
    }
  };

  // Add keyboard event listener
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [mobileMenuVisible]);

  return (
    <>
      <StyledHeader role="banner" aria-label="Main navigation">
        <LogoSection
          onClick={handleLogoClick}
          onKeyDown={handleLogoClick}
          tabIndex={0}
          role="button"
          aria-label={`${title} - Go to homepage`}
        >
          <div className="logo-icon" aria-hidden="true">
            <AppstoreOutlined />
          </div>
          <Title level={4} className="logo-text" aria-hidden="true">
            {title}
          </Title>
        </LogoSection>

        <HeaderActions role="toolbar" aria-label="Header actions">
          <DesktopActions>
            {showStatus && (
              <StatusIndicator
                role="status"
                aria-label="Application status: Online"
                title="Application is online and ready"
              >
                <div className="status-dot" aria-hidden="true" />
                <span>Online</span>
              </StatusIndicator>
            )}
            <div role="group" aria-label="Theme controls">
              <DarkModeToggle />
            </div>
            {children && (
              <div role="group" aria-label="Additional actions">
                {children}
              </div>
            )}
          </DesktopActions>

          <MobileMenuButton
            type="text"
            icon={<MenuOutlined />}
            onClick={toggleMobileMenu}
            aria-label={`${mobileMenuVisible ? 'Close' : 'Open'} mobile menu`}
            aria-expanded={mobileMenuVisible}
            aria-controls="mobile-navigation-drawer"
          />
        </HeaderActions>
      </StyledHeader>

      <MobileDrawer
        title="Navigation Menu"
        placement="right"
        onClose={closeMobileMenu}
        open={mobileMenuVisible}
        width={280}
        id="mobile-navigation-drawer"
        aria-label="Mobile navigation menu"
        closeIcon={<span aria-label="Close menu">×</span>}
        maskClosable={true}
        keyboard={true}
        destroyOnClose={false}
      >
        <DrawerContent role="navigation" aria-label="Mobile menu content">
          <DrawerSection>
            <div className="section-title" id="theme-section">
              Theme Settings
            </div>
            <div role="group" aria-labelledby="theme-section">
              <DarkModeToggle showDropdown={false} />
              <div style={{
                marginTop: '8px',
                fontSize: '12px',
                color: 'var(--color-text-secondary)'
              }}>
                Current: {themeMode === 'system' ? `System (${isDarkMode ? 'Dark' : 'Light'})` :
                  themeMode === 'dark' ? 'Dark' : 'Light'}
              </div>
            </div>
          </DrawerSection>

          {showStatus && (
            <DrawerSection>
              <div className="section-title" id="status-section">
                Application Status
              </div>
              <div role="group" aria-labelledby="status-section">
                <StatusIndicator
                  role="status"
                  aria-label="Application status: Online"
                >
                  <div className="status-dot" aria-hidden="true" />
                  <span>Online</span>
                </StatusIndicator>
              </div>
            </DrawerSection>
          )}

          {children && (
            <DrawerSection>
              <div className="section-title" id="actions-section">
                Additional Actions
              </div>
              <div role="group" aria-labelledby="actions-section">
                {children}
              </div>
            </DrawerSection>
          )}
        </DrawerContent>
      </MobileDrawer>
    </>
  );
};

export default EnhancedHeader;
