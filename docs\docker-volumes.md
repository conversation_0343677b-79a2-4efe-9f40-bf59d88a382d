# Docker Volume Management

This document explains how Docker volumes are used in the App Builder project and how to manage them effectively.

## Overview of Docker Volumes in App Builder

The App Builder project uses several Docker volumes to persist data and improve performance:

1. **Frontend Node Modules Volume**
   - Name: `app-builder-201_frontend_node_modules`
   - Purpose: Stores Node.js dependencies for the frontend
   - Benefits: Improves build performance by caching node_modules

2. **PostgreSQL Data Volume**
   - Name: `app-builder-201_postgres_data`
   - Purpose: Persists database data
   - Benefits: Ensures data is not lost when containers are restarted

## Volume Mount Configuration

The volume mounts are configured in the `docker-compose.yml` file:

### Frontend Volume Mounts

```yaml
volumes:
  - ./frontend:/app
  - frontend_node_modules:/app/node_modules
```

- The first mount maps the local `./frontend` directory to `/app` in the container
- The second mount uses a named volume for node_modules to improve performance

### Backend Volume Mounts

```yaml
volumes:
  - ./backend:/app
```

- Maps the local `./backend` directory to `/app` in the container

### Database Volume Mounts

```yaml
volumes:
  - postgres_data:/var/lib/postgresql/data
```

- Uses a named volume to persist PostgreSQL data

## Common Volume Issues and Solutions

### 1. Node Modules Issues

**Symptoms:**
- Frontend container fails to start
- Missing dependencies errors
- Webpack build errors

**Solutions:**
- Recreate the node_modules volume:
  ```
  docker-compose down
  docker volume rm app-builder-201_frontend_node_modules
  docker-compose up -d
  ```
- Or use the provided script:
  ```
  .\scripts\manage-volumes.ps1 recreate
  ```

### 2. Database Volume Issues

**Symptoms:**
- Database connection errors
- Missing tables or data
- Permission errors in PostgreSQL logs

**Solutions:**
- Check volume permissions:
  ```
  docker-compose exec db ls -la /var/lib/postgresql/data
  ```
- If necessary, recreate the database volume (warning: this will delete all data):
  ```
  docker-compose down
  docker volume rm app-builder-201_postgres_data
  docker-compose up -d
  ```

### 3. Volume Mount Path Issues

**Symptoms:**
- Changes to local files not reflected in the container
- Container using outdated code
- File not found errors

**Solutions:**
- Verify volume mounts:
  ```
  docker inspect app-builder-201-frontend-1
  ```
- Restart the containers:
  ```
  docker-compose restart
  ```
- Check for path issues in docker-compose.yml

## Volume Management Tools

The project includes scripts to help manage Docker volumes:

### PowerShell Script (Windows)

```
.\scripts\manage-volumes.ps1 [action] [-Force]
```

Available actions:
- `list` - List all Docker volumes related to this project
- `clean` - Remove unused Docker volumes
- `verify` - Verify volume mounts are working correctly
- `recreate` - Recreate specific volumes (e.g., node_modules)
- `help` - Show help message

### Bash Script (Linux/macOS)

```
./scripts/manage-volumes.sh [action] [--force]
```

Available actions are the same as the PowerShell script.

## Network and Volume Diagnostics

The project also includes a network diagnostics script that checks volume mounts:

```
.\scripts\diagnose-network.ps1
```

This script:
- Checks network connectivity
- Lists Docker volumes
- Verifies volume mounts in running containers
- Identifies potential issues

## Best Practices

1. **Never modify files directly in named volumes**
   - Named volumes like `frontend_node_modules` should be managed by the container

2. **Use bind mounts for development**
   - Bind mounts (like `./frontend:/app`) allow you to edit files locally

3. **Clean up unused volumes periodically**
   - Run `docker volume prune` or use the management script to clean up

4. **Back up important data volumes**
   - For production, implement a backup strategy for database volumes

5. **Check volume mounts when troubleshooting**
   - Many issues are related to incorrect volume mounts

## Additional Resources

- [Docker Volumes Documentation](https://docs.docker.com/storage/volumes/)
- [Docker Compose Volumes](https://docs.docker.com/compose/compose-file/compose-file-v3/#volumes)
