#!/usr/bin/env python3
"""
Simple WebSocket connection test using websocket-client
"""

import json
import time
from websocket import create_connection, WebSocketTimeoutException

def test_websocket_connection(url):
    """Test WebSocket connection to the given URL"""
    print(f"Testing WebSocket connection to: {url}")
    
    try:
        # Create connection with timeout
        ws = create_connection(url, timeout=10)
        print("✅ WebSocket connection established successfully!")
        
        # Send a test message
        test_message = json.dumps({
            "type": "ping",
            "message": "Hello WebSocket!"
        })
        
        print(f"Sending test message: {test_message}")
        ws.send(test_message)
        
        # Wait for response
        print("Waiting for response...")
        response = ws.recv()
        print(f"✅ Received response: {response}")
        
        # Close connection
        ws.close()
        print("✅ Connection closed successfully")
        return True
        
    except WebSocketTimeoutException:
        print("❌ WebSocket connection timed out")
        return False
    except ConnectionRefusedError:
        print("❌ Connection refused - server may not be running")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Test multiple WebSocket endpoints"""
    base_url = "ws://localhost:8000"
    
    endpoints = [
        "/ws/",
        "/ws/simple/",
        "/ws/test/",
        "/ws/app_builder/",
        "/ws/echo/"
    ]
    
    print("Testing WebSocket endpoints...")
    print("=" * 50)
    
    results = {}
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\nTesting: {url}")
        results[endpoint] = test_websocket_connection(url)
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    for endpoint, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {endpoint}: {status}")
    
    # Test if any endpoint worked
    if any(results.values()):
        print("\n✅ At least one WebSocket endpoint is working!")
    else:
        print("\n❌ No WebSocket endpoints are working!")

if __name__ == "__main__":
    main()
