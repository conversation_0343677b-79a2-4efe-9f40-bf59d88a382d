#!/usr/bin/env node

/**
 * Comprehensive test coverage report generator
 * Combines frontend and backend coverage reports into a unified dashboard
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  frontend: {
    coverageDir: path.join(__dirname, '../frontend/coverage'),
    lcovFile: path.join(__dirname, '../frontend/coverage/lcov.info'),
    jsonFile: path.join(__dirname, '../frontend/coverage/coverage-final.json'),
  },
  backend: {
    coverageDir: path.join(__dirname, '../backend/htmlcov'),
    xmlFile: path.join(__dirname, '../backend/coverage.xml'),
    jsonFile: path.join(__dirname, '../backend/coverage.json'),
  },
  output: {
    dir: path.join(__dirname, '../test-results/coverage'),
    htmlFile: path.join(__dirname, '../test-results/coverage/index.html'),
    jsonFile: path.join(__dirname, '../test-results/coverage/combined-coverage.json'),
    badgeDir: path.join(__dirname, '../test-results/coverage/badges'),
  },
  thresholds: {
    statements: 80,
    branches: 75,
    functions: 80,
    lines: 80,
  }
};

class CoverageReporter {
  constructor() {
    this.frontendCoverage = null;
    this.backendCoverage = null;
    this.combinedCoverage = null;
  }

  async generateReport() {
    console.log('🧪 Generating comprehensive test coverage report...');

    try {
      // Ensure output directory exists
      this.ensureOutputDirectory();

      // Load coverage data
      await this.loadFrontendCoverage();
      await this.loadBackendCoverage();

      // Combine coverage data
      this.combineCoverage();

      // Generate reports
      await this.generateHTMLReport();
      await this.generateJSONReport();
      await this.generateBadges();
      await this.generateSummary();

      console.log('✅ Coverage report generated successfully!');
      console.log(`📊 Report available at: ${CONFIG.output.htmlFile}`);

    } catch (error) {
      console.error('❌ Failed to generate coverage report:', error.message);
      process.exit(1);
    }
  }

  ensureOutputDirectory() {
    if (!fs.existsSync(CONFIG.output.dir)) {
      fs.mkdirSync(CONFIG.output.dir, { recursive: true });
    }
    if (!fs.existsSync(CONFIG.output.badgeDir)) {
      fs.mkdirSync(CONFIG.output.badgeDir, { recursive: true });
    }
  }

  async loadFrontendCoverage() {
    console.log('📱 Loading frontend coverage data...');

    if (fs.existsSync(CONFIG.frontend.jsonFile)) {
      const coverageData = JSON.parse(fs.readFileSync(CONFIG.frontend.jsonFile, 'utf8'));
      this.frontendCoverage = this.processFrontendCoverage(coverageData);
      console.log(`✅ Frontend coverage loaded: ${this.frontendCoverage.summary.lines.pct}% lines`);
    } else {
      console.warn('⚠️ Frontend coverage file not found');
      this.frontendCoverage = this.getEmptyCoverage('frontend');
    }
  }

  async loadBackendCoverage() {
    console.log('🐍 Loading backend coverage data...');

    if (fs.existsSync(CONFIG.backend.xmlFile)) {
      // Parse XML coverage file
      this.backendCoverage = await this.parseBackendXMLCoverage();
      console.log(`✅ Backend coverage loaded: ${this.backendCoverage.summary.lines.pct}% lines`);
    } else {
      console.warn('⚠️ Backend coverage file not found');
      this.backendCoverage = this.getEmptyCoverage('backend');
    }
  }

  processFrontendCoverage(coverageData) {
    const files = Object.keys(coverageData);
    let totalStatements = 0;
    let coveredStatements = 0;
    let totalBranches = 0;
    let coveredBranches = 0;
    let totalFunctions = 0;
    let coveredFunctions = 0;
    let totalLines = 0;
    let coveredLines = 0;

    const fileDetails = {};

    files.forEach(file => {
      const fileCoverage = coverageData[file];
      
      // Statements
      totalStatements += Object.keys(fileCoverage.s).length;
      coveredStatements += Object.values(fileCoverage.s).filter(count => count > 0).length;

      // Branches
      totalBranches += Object.keys(fileCoverage.b).length * 2; // Each branch has 2 paths
      coveredBranches += Object.values(fileCoverage.b).flat().filter(count => count > 0).length;

      // Functions
      totalFunctions += Object.keys(fileCoverage.f).length;
      coveredFunctions += Object.values(fileCoverage.f).filter(count => count > 0).length;

      // Lines
      totalLines += Object.keys(fileCoverage.l).length;
      coveredLines += Object.values(fileCoverage.l).filter(count => count > 0).length;

      fileDetails[file] = {
        statements: this.calculatePercentage(
          Object.values(fileCoverage.s).filter(count => count > 0).length,
          Object.keys(fileCoverage.s).length
        ),
        branches: this.calculatePercentage(
          Object.values(fileCoverage.b).flat().filter(count => count > 0).length,
          Object.keys(fileCoverage.b).length * 2
        ),
        functions: this.calculatePercentage(
          Object.values(fileCoverage.f).filter(count => count > 0).length,
          Object.keys(fileCoverage.f).length
        ),
        lines: this.calculatePercentage(
          Object.values(fileCoverage.l).filter(count => count > 0).length,
          Object.keys(fileCoverage.l).length
        ),
      };
    });

    return {
      type: 'frontend',
      summary: {
        statements: {
          total: totalStatements,
          covered: coveredStatements,
          pct: this.calculatePercentage(coveredStatements, totalStatements)
        },
        branches: {
          total: totalBranches,
          covered: coveredBranches,
          pct: this.calculatePercentage(coveredBranches, totalBranches)
        },
        functions: {
          total: totalFunctions,
          covered: coveredFunctions,
          pct: this.calculatePercentage(coveredFunctions, totalFunctions)
        },
        lines: {
          total: totalLines,
          covered: coveredLines,
          pct: this.calculatePercentage(coveredLines, totalLines)
        }
      },
      files: fileDetails
    };
  }

  async parseBackendXMLCoverage() {
    // Simplified XML parsing - in a real implementation, use a proper XML parser
    const xmlContent = fs.readFileSync(CONFIG.backend.xmlFile, 'utf8');
    
    // Extract coverage metrics using regex (simplified)
    const lineRateMatch = xmlContent.match(/line-rate="([^"]+)"/);
    const branchRateMatch = xmlContent.match(/branch-rate="([^"]+)"/);
    
    const lineRate = lineRateMatch ? parseFloat(lineRateMatch[1]) * 100 : 0;
    const branchRate = branchRateMatch ? parseFloat(branchRateMatch[1]) * 100 : 0;

    return {
      type: 'backend',
      summary: {
        statements: { total: 100, covered: Math.round(lineRate), pct: lineRate },
        branches: { total: 100, covered: Math.round(branchRate), pct: branchRate },
        functions: { total: 100, covered: Math.round(lineRate), pct: lineRate },
        lines: { total: 100, covered: Math.round(lineRate), pct: lineRate }
      },
      files: {}
    };
  }

  combineCoverage() {
    console.log('🔄 Combining frontend and backend coverage...');

    const frontend = this.frontendCoverage.summary;
    const backend = this.backendCoverage.summary;

    this.combinedCoverage = {
      timestamp: new Date().toISOString(),
      frontend: this.frontendCoverage,
      backend: this.backendCoverage,
      combined: {
        statements: {
          total: frontend.statements.total + backend.statements.total,
          covered: frontend.statements.covered + backend.statements.covered,
          pct: this.calculatePercentage(
            frontend.statements.covered + backend.statements.covered,
            frontend.statements.total + backend.statements.total
          )
        },
        branches: {
          total: frontend.branches.total + backend.branches.total,
          covered: frontend.branches.covered + backend.branches.covered,
          pct: this.calculatePercentage(
            frontend.branches.covered + backend.branches.covered,
            frontend.branches.total + backend.branches.total
          )
        },
        functions: {
          total: frontend.functions.total + backend.functions.total,
          covered: frontend.functions.covered + backend.functions.covered,
          pct: this.calculatePercentage(
            frontend.functions.covered + backend.functions.covered,
            frontend.functions.total + backend.functions.total
          )
        },
        lines: {
          total: frontend.lines.total + backend.lines.total,
          covered: frontend.lines.covered + backend.lines.covered,
          pct: this.calculatePercentage(
            frontend.lines.covered + backend.lines.covered,
            frontend.lines.total + backend.lines.total
          )
        }
      },
      thresholds: CONFIG.thresholds,
      qualityGate: this.checkQualityGate()
    };
  }

  checkQualityGate() {
    const combined = this.combinedCoverage.combined;
    const thresholds = CONFIG.thresholds;

    return {
      passed: 
        combined.statements.pct >= thresholds.statements &&
        combined.branches.pct >= thresholds.branches &&
        combined.functions.pct >= thresholds.functions &&
        combined.lines.pct >= thresholds.lines,
      details: {
        statements: combined.statements.pct >= thresholds.statements,
        branches: combined.branches.pct >= thresholds.branches,
        functions: combined.functions.pct >= thresholds.functions,
        lines: combined.lines.pct >= thresholds.lines,
      }
    };
  }

  async generateHTMLReport() {
    console.log('📄 Generating HTML report...');

    const html = this.generateHTMLContent();
    fs.writeFileSync(CONFIG.output.htmlFile, html);
  }

  generateHTMLContent() {
    const combined = this.combinedCoverage.combined;
    const frontend = this.combinedCoverage.frontend.summary;
    const backend = this.combinedCoverage.backend.summary;
    const qualityGate = this.combinedCoverage.qualityGate;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Coverage Report - App Builder</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 30px; }
        .metric { background: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; }
        .metric.good { border-left: 4px solid #28a745; }
        .metric.warning { border-left: 4px solid #ffc107; }
        .metric.poor { border-left: 4px solid #dc3545; }
        .percentage { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .section { padding: 30px; border-top: 1px solid #eee; }
        .quality-gate { padding: 20px; margin: 20px 0; border-radius: 8px; }
        .quality-gate.passed { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .quality-gate.failed { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        .comparison-item { background: #f8f9fa; border-radius: 8px; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Coverage Report</h1>
            <p>App Builder - Generated on ${new Date(this.combinedCoverage.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="quality-gate ${qualityGate.passed ? 'passed' : 'failed'}">
            <h2>${qualityGate.passed ? '✅ Quality Gate: PASSED' : '❌ Quality Gate: FAILED'}</h2>
            <p>Coverage thresholds: Statements ${CONFIG.thresholds.statements}%, Branches ${CONFIG.thresholds.branches}%, Functions ${CONFIG.thresholds.functions}%, Lines ${CONFIG.thresholds.lines}%</p>
        </div>
        
        <div class="section">
            <h2>Combined Coverage</h2>
            <div class="metrics">
                <div class="metric ${this.getMetricClass(combined.statements.pct)}">
                    <div class="percentage">${combined.statements.pct.toFixed(1)}%</div>
                    <div>Statements</div>
                    <div>${combined.statements.covered}/${combined.statements.total}</div>
                </div>
                <div class="metric ${this.getMetricClass(combined.branches.pct)}">
                    <div class="percentage">${combined.branches.pct.toFixed(1)}%</div>
                    <div>Branches</div>
                    <div>${combined.branches.covered}/${combined.branches.total}</div>
                </div>
                <div class="metric ${this.getMetricClass(combined.functions.pct)}">
                    <div class="percentage">${combined.functions.pct.toFixed(1)}%</div>
                    <div>Functions</div>
                    <div>${combined.functions.covered}/${combined.functions.total}</div>
                </div>
                <div class="metric ${this.getMetricClass(combined.lines.pct)}">
                    <div class="percentage">${combined.lines.pct.toFixed(1)}%</div>
                    <div>Lines</div>
                    <div>${combined.lines.covered}/${combined.lines.total}</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Frontend vs Backend Comparison</h2>
            <div class="comparison">
                <div class="comparison-item">
                    <h3>Frontend Coverage</h3>
                    <p>Statements: ${frontend.statements.pct.toFixed(1)}%</p>
                    <p>Branches: ${frontend.branches.pct.toFixed(1)}%</p>
                    <p>Functions: ${frontend.functions.pct.toFixed(1)}%</p>
                    <p>Lines: ${frontend.lines.pct.toFixed(1)}%</p>
                </div>
                <div class="comparison-item">
                    <h3>Backend Coverage</h3>
                    <p>Statements: ${backend.statements.pct.toFixed(1)}%</p>
                    <p>Branches: ${backend.branches.pct.toFixed(1)}%</p>
                    <p>Functions: ${backend.functions.pct.toFixed(1)}%</p>
                    <p>Lines: ${backend.lines.pct.toFixed(1)}%</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    `;
  }

  async generateJSONReport() {
    console.log('📊 Generating JSON report...');
    fs.writeFileSync(CONFIG.output.jsonFile, JSON.stringify(this.combinedCoverage, null, 2));
  }

  async generateBadges() {
    console.log('🏷️ Generating coverage badges...');

    const badges = [
      { name: 'statements', value: this.combinedCoverage.combined.statements.pct },
      { name: 'branches', value: this.combinedCoverage.combined.branches.pct },
      { name: 'functions', value: this.combinedCoverage.combined.functions.pct },
      { name: 'lines', value: this.combinedCoverage.combined.lines.pct },
    ];

    badges.forEach(badge => {
      const color = this.getBadgeColor(badge.value);
      const badgeContent = this.generateBadgeSVG(badge.name, `${badge.value.toFixed(1)}%`, color);
      fs.writeFileSync(path.join(CONFIG.output.badgeDir, `${badge.name}.svg`), badgeContent);
    });
  }

  generateBadgeSVG(label, value, color) {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="104" height="20">
      <linearGradient id="b" x2="0" y2="100%">
        <stop offset="0" stop-color="#bbb" stop-opacity=".1"/>
        <stop offset="1" stop-opacity=".1"/>
      </linearGradient>
      <mask id="a">
        <rect width="104" height="20" rx="3" fill="#fff"/>
      </mask>
      <g mask="url(#a)">
        <path fill="#555" d="M0 0h63v20H0z"/>
        <path fill="${color}" d="M63 0h41v20H63z"/>
        <path fill="url(#b)" d="M0 0h104v20H0z"/>
      </g>
      <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="110">
        <text x="325" y="150" fill="#010101" fill-opacity=".3" transform="scale(.1)" textLength="530">${label}</text>
        <text x="325" y="140" transform="scale(.1)" textLength="530">${label}</text>
        <text x="825" y="150" fill="#010101" fill-opacity=".3" transform="scale(.1)" textLength="310">${value}</text>
        <text x="825" y="140" transform="scale(.1)" textLength="310">${value}</text>
      </g>
    </svg>`;
  }

  async generateSummary() {
    console.log('📋 Generating coverage summary...');

    const summary = `# Test Coverage Summary

Generated on: ${new Date(this.combinedCoverage.timestamp).toLocaleString()}

## Quality Gate: ${this.combinedCoverage.qualityGate.passed ? 'PASSED ✅' : 'FAILED ❌'}

## Combined Coverage
- **Statements**: ${this.combinedCoverage.combined.statements.pct.toFixed(1)}% (${this.combinedCoverage.combined.statements.covered}/${this.combinedCoverage.combined.statements.total})
- **Branches**: ${this.combinedCoverage.combined.branches.pct.toFixed(1)}% (${this.combinedCoverage.combined.branches.covered}/${this.combinedCoverage.combined.branches.total})
- **Functions**: ${this.combinedCoverage.combined.functions.pct.toFixed(1)}% (${this.combinedCoverage.combined.functions.covered}/${this.combinedCoverage.combined.functions.total})
- **Lines**: ${this.combinedCoverage.combined.lines.pct.toFixed(1)}% (${this.combinedCoverage.combined.lines.covered}/${this.combinedCoverage.combined.lines.total})

## Frontend Coverage
- **Statements**: ${this.combinedCoverage.frontend.summary.statements.pct.toFixed(1)}%
- **Branches**: ${this.combinedCoverage.frontend.summary.branches.pct.toFixed(1)}%
- **Functions**: ${this.combinedCoverage.frontend.summary.functions.pct.toFixed(1)}%
- **Lines**: ${this.combinedCoverage.frontend.summary.lines.pct.toFixed(1)}%

## Backend Coverage
- **Statements**: ${this.combinedCoverage.backend.summary.statements.pct.toFixed(1)}%
- **Branches**: ${this.combinedCoverage.backend.summary.branches.pct.toFixed(1)}%
- **Functions**: ${this.combinedCoverage.backend.summary.functions.pct.toFixed(1)}%
- **Lines**: ${this.combinedCoverage.backend.summary.lines.pct.toFixed(1)}%

## Thresholds
- Statements: ${CONFIG.thresholds.statements}%
- Branches: ${CONFIG.thresholds.branches}%
- Functions: ${CONFIG.thresholds.functions}%
- Lines: ${CONFIG.thresholds.lines}%
`;

    fs.writeFileSync(path.join(CONFIG.output.dir, 'summary.md'), summary);
  }

  calculatePercentage(covered, total) {
    return total === 0 ? 100 : Math.round((covered / total) * 100 * 100) / 100;
  }

  getMetricClass(percentage) {
    if (percentage >= 80) return 'good';
    if (percentage >= 60) return 'warning';
    return 'poor';
  }

  getBadgeColor(percentage) {
    if (percentage >= 80) return '#4c1';
    if (percentage >= 60) return '#dfb317';
    return '#e05d44';
  }

  getEmptyCoverage(type) {
    return {
      type,
      summary: {
        statements: { total: 0, covered: 0, pct: 0 },
        branches: { total: 0, covered: 0, pct: 0 },
        functions: { total: 0, covered: 0, pct: 0 },
        lines: { total: 0, covered: 0, pct: 0 }
      },
      files: {}
    };
  }
}

// Main execution
if (require.main === module) {
  const reporter = new CoverageReporter();
  reporter.generateReport().catch(error => {
    console.error('Failed to generate coverage report:', error);
    process.exit(1);
  });
}

module.exports = CoverageReporter;
