"""
Template service for managing template categories and operations.
"""
from django.db.models import Q, Count
from ..models import ComponentTemplate, LayoutTemplate, AppTemplate


class TemplateService:
    """Service class for template operations"""
    
    @staticmethod
    def get_component_categories():
        """Get all component template categories with counts"""
        categories = ComponentTemplate.objects.values('component_type').annotate(
            count=Count('id')
        ).order_by('component_type')
        
        return [
            {
                'value': cat['component_type'],
                'label': cat['component_type'].title(),
                'count': cat['count']
            }
            for cat in categories
        ]
    
    @staticmethod
    def get_layout_categories():
        """Get all layout template categories with counts"""
        categories = LayoutTemplate.objects.values('layout_type').annotate(
            count=Count('id')
        ).order_by('layout_type')
        
        return [
            {
                'value': cat['layout_type'],
                'label': cat['layout_type'].title(),
                'count': cat['count']
            }
            for cat in categories
        ]
    
    @staticmethod
    def get_app_categories():
        """Get all app template categories with counts"""
        categories = AppTemplate.objects.values('app_category').annotate(
            count=Count('id')
        ).order_by('app_category')
        
        category_labels = {
            'business': 'Business Apps',
            'ecommerce': 'E-commerce',
            'portfolio': 'Portfolio',
            'dashboard': 'Dashboard',
            'landing': 'Landing Page',
            'blog': 'Blog',
            'social': 'Social Media',
            'education': 'Education',
            'healthcare': 'Healthcare',
            'finance': 'Finance',
            'other': 'Other'
        }
        
        return [
            {
                'value': cat['app_category'],
                'label': category_labels.get(cat['app_category'], cat['app_category'].title()),
                'count': cat['count']
            }
            for cat in categories
        ]
    
    @staticmethod
    def search_templates(query='', template_type=None, category=None, is_public=None, user=None):
        """Search templates across all types"""
        results = {
            'components': [],
            'layouts': [],
            'apps': []
        }
        
        # Build base filters
        base_filters = Q()
        if is_public is not None:
            base_filters &= Q(is_public=is_public)
        if user:
            base_filters &= Q(user=user)
        
        # Search component templates
        if not template_type or template_type == 'components':
            component_filters = base_filters
            if query:
                component_filters &= (
                    Q(name__icontains=query) | 
                    Q(description__icontains=query) |
                    Q(component_type__icontains=query)
                )
            if category:
                component_filters &= Q(component_type=category)
            
            results['components'] = list(ComponentTemplate.objects.filter(
                component_filters
            ).values(
                'id', 'name', 'description', 'component_type', 
                'is_public', 'created_at', 'user__username'
            ))
        
        # Search layout templates
        if not template_type or template_type == 'layouts':
            layout_filters = base_filters
            if query:
                layout_filters &= (
                    Q(name__icontains=query) | 
                    Q(description__icontains=query) |
                    Q(layout_type__icontains=query)
                )
            if category:
                layout_filters &= Q(layout_type=category)
            
            results['layouts'] = list(LayoutTemplate.objects.filter(
                layout_filters
            ).values(
                'id', 'name', 'description', 'layout_type', 
                'is_public', 'created_at', 'user__username'
            ))
        
        # Search app templates
        if not template_type or template_type == 'apps':
            app_filters = base_filters
            if query:
                app_filters &= (
                    Q(name__icontains=query) | 
                    Q(description__icontains=query) |
                    Q(app_category__icontains=query)
                )
            if category:
                app_filters &= Q(app_category=category)
            
            results['apps'] = list(AppTemplate.objects.filter(
                app_filters
            ).values(
                'id', 'name', 'description', 'app_category', 
                'preview_image', 'is_public', 'created_at', 'user__username'
            ))
        
        return results
    
    @staticmethod
    def get_featured_templates():
        """Get featured templates for the homepage"""
        return {
            'components': ComponentTemplate.objects.filter(
                is_public=True
            ).order_by('-created_at')[:6],
            'layouts': LayoutTemplate.objects.filter(
                is_public=True
            ).order_by('-created_at')[:6],
            'apps': AppTemplate.objects.filter(
                is_public=True
            ).order_by('-created_at')[:6]
        }
    
    @staticmethod
    def get_template_stats():
        """Get template statistics"""
        return {
            'total_templates': (
                ComponentTemplate.objects.count() +
                LayoutTemplate.objects.count() +
                AppTemplate.objects.count()
            ),
            'public_templates': (
                ComponentTemplate.objects.filter(is_public=True).count() +
                LayoutTemplate.objects.filter(is_public=True).count() +
                AppTemplate.objects.filter(is_public=True).count()
            ),
            'component_templates': ComponentTemplate.objects.count(),
            'layout_templates': LayoutTemplate.objects.count(),
            'app_templates': AppTemplate.objects.count(),
            'categories': {
                'components': ComponentTemplate.objects.values('component_type').distinct().count(),
                'layouts': LayoutTemplate.objects.values('layout_type').distinct().count(),
                'apps': AppTemplate.objects.values('app_category').distinct().count()
            }
        }
    
    @staticmethod
    def clone_template(template_id, template_type, user, new_name=None):
        """Clone a template for a user"""
        try:
            if template_type == 'component':
                original = ComponentTemplate.objects.get(id=template_id)
                cloned = ComponentTemplate.objects.create(
                    name=new_name or f"{original.name} (Copy)",
                    description=original.description,
                    component_type=original.component_type,
                    default_props=original.default_props,
                    user=user,
                    is_public=False
                )
            elif template_type == 'layout':
                original = LayoutTemplate.objects.get(id=template_id)
                cloned = LayoutTemplate.objects.create(
                    name=new_name or f"{original.name} (Copy)",
                    description=original.description,
                    layout_type=original.layout_type,
                    components=original.components,
                    default_props=original.default_props,
                    user=user,
                    is_public=False
                )
            elif template_type == 'app':
                original = AppTemplate.objects.get(id=template_id)
                cloned = AppTemplate.objects.create(
                    name=new_name or f"{original.name} (Copy)",
                    description=original.description,
                    app_category=original.app_category,
                    components=original.components,
                    default_props=original.default_props,
                    required_components=original.required_components,
                    preview_image=original.preview_image,
                    user=user,
                    is_public=False
                )
            else:
                raise ValueError(f"Invalid template type: {template_type}")
            
            return cloned
        except Exception as e:
            raise Exception(f"Failed to clone template: {str(e)}")
