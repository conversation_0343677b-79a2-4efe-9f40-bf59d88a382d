/**
 * Higher-Order Component for Tutorial Support
 * 
 * Wraps components with tutorial functionality, adding help context
 * attributes and tutorial trigger capabilities.
 */

import React, { forwardRef, useEffect, useRef } from 'react';
import { useTutorial } from './TutorialManager';
import { useContextualHelp } from './ContextualHelp';
import { HELP_CONTEXT_TYPES } from './types';

/**
 * HOC that adds tutorial support to any component
 * @param {React.Component} WrappedComponent - Component to wrap
 * @param {Object} options - Tutorial configuration options
 */
const withTutorialSupport = (WrappedComponent, options = {}) => {
  const {
    helpContext = null,
    tutorialId = null,
    autoStartTutorial = false,
    helpTriggers = ['hover'],
    helpDelay = 1500,
    trackInteractions = true
  } = options;

  const TutorialSupportedComponent = forwardRef((props, ref) => {
    const { startTutorial, preferences } = useTutorial();
    const { showHelp } = useContextualHelp();
    const elementRef = useRef(null);
    const hoverTimerRef = useRef(null);
    const interactionCountRef = useRef(0);

    // Combine refs
    const combinedRef = (element) => {
      elementRef.current = element;
      if (typeof ref === 'function') {
        ref(element);
      } else if (ref) {
        ref.current = element;
      }
    };

    // Auto-start tutorial on mount if configured
    useEffect(() => {
      if (autoStartTutorial && tutorialId && preferences.autoStartTutorials) {
        const timer = setTimeout(() => {
          startTutorial(tutorialId);
        }, 2000); // Delay to let component settle

        return () => clearTimeout(timer);
      }
    }, [autoStartTutorial, tutorialId, startTutorial, preferences.autoStartTutorials]);

    // Set up help context triggers
    useEffect(() => {
      const element = elementRef.current;
      if (!element || !helpContext || !preferences.showContextualHelp) return;

      const handleMouseEnter = () => {
        if (helpTriggers.includes('hover')) {
          hoverTimerRef.current = setTimeout(() => {
            showHelp(helpContext, element);
          }, helpDelay);
        }
      };

      const handleMouseLeave = () => {
        if (hoverTimerRef.current) {
          clearTimeout(hoverTimerRef.current);
          hoverTimerRef.current = null;
        }
      };

      const handleClick = () => {
        if (trackInteractions) {
          interactionCountRef.current += 1;
        }

        if (helpTriggers.includes('click')) {
          showHelp(helpContext, element);
        }
      };

      const handleFocus = () => {
        if (helpTriggers.includes('focus')) {
          showHelp(helpContext, element);
        }
      };

      // Add event listeners
      if (helpTriggers.includes('hover')) {
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
      }

      if (helpTriggers.includes('click')) {
        element.addEventListener('click', handleClick);
      }

      if (helpTriggers.includes('focus')) {
        element.addEventListener('focus', handleFocus);
      }

      return () => {
        element.removeEventListener('mouseenter', handleMouseEnter);
        element.removeEventListener('mouseleave', handleMouseLeave);
        element.removeEventListener('click', handleClick);
        element.removeEventListener('focus', handleFocus);
        
        if (hoverTimerRef.current) {
          clearTimeout(hoverTimerRef.current);
        }
      };
    }, [helpContext, helpTriggers, helpDelay, showHelp, preferences.showContextualHelp, trackInteractions]);

    // Enhanced props with tutorial support
    const enhancedProps = {
      ...props,
      ref: combinedRef,
      'data-help-context': helpContext,
      'data-tutorial-id': tutorialId,
      'data-tutorial-supported': true,
      // Add tutorial-specific event handlers
      onTutorialStart: tutorialId ? () => startTutorial(tutorialId) : undefined,
      // Merge existing event handlers with tutorial tracking
      onClick: (e) => {
        if (trackInteractions) {
          interactionCountRef.current += 1;
        }
        if (props.onClick) {
          props.onClick(e);
        }
      }
    };

    return <WrappedComponent {...enhancedProps} />;
  });

  // Set display name for debugging
  TutorialSupportedComponent.displayName = 
    `withTutorialSupport(${WrappedComponent.displayName || WrappedComponent.name})`;

  return TutorialSupportedComponent;
};

// Convenience HOCs for common use cases
export const withComponentPaletteHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.COMPONENT_PALETTE,
    tutorialId: 'getting_started',
    helpTriggers: ['hover'],
    helpDelay: 2000
  });

export const withPreviewAreaHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.PREVIEW_AREA,
    tutorialId: 'drag_drop_basics',
    helpTriggers: ['hover', 'click'],
    helpDelay: 3000
  });

export const withPropertyEditorHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.PROPERTY_EDITOR,
    tutorialId: 'component_properties',
    helpTriggers: ['focus', 'hover'],
    helpDelay: 1000
  });

export const withDragDropHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.DRAG_DROP,
    tutorialId: 'drag_drop_basics',
    helpTriggers: ['hover'],
    helpDelay: 2500
  });

export const withThemeManagerHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.THEME_MANAGER,
    tutorialId: 'theme_customization',
    helpTriggers: ['hover', 'focus'],
    helpDelay: 1500
  });

export const withLayoutDesignerHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.LAYOUT_DESIGNER,
    tutorialId: 'layout_design',
    helpTriggers: ['hover'],
    helpDelay: 2000
  });

export const withCodeExportHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.CODE_EXPORT,
    tutorialId: 'code_export',
    helpTriggers: ['hover', 'click'],
    helpDelay: 1000
  });

export const withWebSocketHelp = (Component) => 
  withTutorialSupport(Component, {
    helpContext: HELP_CONTEXT_TYPES.WEBSOCKET,
    tutorialId: 'websocket_features',
    helpTriggers: ['hover', 'focus'],
    helpDelay: 1500
  });

// Utility function to create custom tutorial HOCs
export const createTutorialHOC = (customOptions) => (Component) => 
  withTutorialSupport(Component, customOptions);

export default withTutorialSupport;
