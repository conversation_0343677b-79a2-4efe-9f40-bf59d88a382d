/**
 * Performance tests for AI-assisted design features
 * Ensures AI functionality doesn't impact main App Builder performance
 */

import { performance } from 'perf_hooks';
import aiDesignService from '../../services/aiDesignService';
import aiWebSocketService from '../../services/aiWebSocketService';

// Mock performance.now for consistent testing
const mockPerformanceNow = jest.fn();
global.performance = { now: mockPerformanceNow };

describe('AI Performance Tests', () => {
  beforeEach(() => {
    mockPerformanceNow.mockClear();
    let time = 0;
    mockPerformanceNow.mockImplementation(() => time += 100);
  });

  describe('AI Service Performance', () => {
    test('layout suggestions generation completes within acceptable time', async () => {
      const startTime = performance.now();
      
      const mockComponents = Array.from({ length: 50 }, (_, i) => ({
        id: `comp-${i}`,
        type: i % 5 === 0 ? 'button' : i % 3 === 0 ? 'text' : 'card',
        props: {}
      }));

      try {
        await aiDesignService.generateLayoutSuggestions(mockComponents);
      } catch (error) {
        // Expected to fail in test environment
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within 5 seconds (5000ms)
      expect(duration).toBeLessThan(5000);
    });

    test('component combinations generation is efficient', async () => {
      const startTime = performance.now();
      
      const mockComponents = Array.from({ length: 20 }, (_, i) => ({
        id: `comp-${i}`,
        type: ['button', 'text', 'form', 'input', 'card'][i % 5],
        props: {}
      }));

      const selectedComponent = mockComponents[0];

      try {
        await aiDesignService.generateComponentCombinations(mockComponents, selectedComponent);
      } catch (error) {
        // Expected to fail in test environment
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within 3 seconds (3000ms)
      expect(duration).toBeLessThan(3000);
    });

    test('caching improves subsequent request performance', async () => {
      const mockComponents = [
        { id: '1', type: 'button', props: {} },
        { id: '2', type: 'text', props: {} }
      ];

      // First request
      const startTime1 = performance.now();
      try {
        await aiDesignService.generateLayoutSuggestions(mockComponents);
      } catch (error) {
        // Expected to fail in test environment
      }
      const endTime1 = performance.now();
      const duration1 = endTime1 - startTime1;

      // Second request (should be cached)
      const startTime2 = performance.now();
      try {
        await aiDesignService.generateLayoutSuggestions(mockComponents);
      } catch (error) {
        // Expected to fail in test environment
      }
      const endTime2 = performance.now();
      const duration2 = endTime2 - startTime2;

      // Second request should be faster due to caching
      expect(duration2).toBeLessThanOrEqual(duration1);
    });
  });

  describe('WebSocket Performance', () => {
    test('WebSocket connection establishes quickly', async () => {
      const startTime = performance.now();
      
      try {
        await aiWebSocketService.connect();
      } catch (error) {
        // Expected to fail in test environment
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should connect within 2 seconds
      expect(duration).toBeLessThan(2000);
    });

    test('WebSocket message handling is efficient', () => {
      const startTime = performance.now();
      
      // Simulate handling multiple messages
      for (let i = 0; i < 100; i++) {
        aiWebSocketService.handleMessage({
          type: 'layout_suggestions',
          suggestions: [
            { id: `suggestion-${i}`, name: `Layout ${i}`, score: 80 }
          ]
        });
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should handle 100 messages within 1 second
      expect(duration).toBeLessThan(1000);
    });
  });

  describe('Memory Usage', () => {
    test('AI service cache has reasonable memory footprint', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Generate many cached entries
      for (let i = 0; i < 100; i++) {
        const cacheKey = `test_key_${i}`;
        aiDesignService.cache.set(cacheKey, {
          data: { suggestions: Array.from({ length: 10 }, (_, j) => ({ id: j })) },
          timestamp: Date.now()
        });
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    test('cache cleanup prevents memory leaks', () => {
      const initialCacheSize = aiDesignService.cache.size;
      
      // Add many entries
      for (let i = 0; i < 1000; i++) {
        aiDesignService.cache.set(`key_${i}`, {
          data: { test: true },
          timestamp: Date.now() - (i * 1000) // Older entries
        });
      }

      // Clear cache
      aiDesignService.clearCache();

      expect(aiDesignService.cache.size).toBe(0);
    });
  });

  describe('Concurrent Operations', () => {
    test('multiple simultaneous AI requests are handled efficiently', async () => {
      const startTime = performance.now();
      
      const mockComponents = [
        { id: '1', type: 'button', props: {} },
        { id: '2', type: 'text', props: {} }
      ];

      // Simulate multiple concurrent requests
      const promises = Array.from({ length: 10 }, () => 
        Promise.allSettled([
          aiDesignService.generateLayoutSuggestions(mockComponents),
          aiDesignService.generateComponentCombinations(mockComponents),
          aiDesignService.analyzeAppStructure(mockComponents)
        ])
      );

      await Promise.all(promises);

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should handle 10 concurrent requests within 10 seconds
      expect(duration).toBeLessThan(10000);
    });

    test('AI operations do not block main thread', async () => {
      const startTime = performance.now();
      
      // Start AI operation
      const aiPromise = aiDesignService.generateLayoutSuggestions([
        { id: '1', type: 'button', props: {} }
      ]).catch(() => {}); // Ignore errors

      // Simulate main thread work
      let counter = 0;
      for (let i = 0; i < 1000000; i++) {
        counter += i;
      }

      await aiPromise;

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Main thread work should complete quickly
      expect(counter).toBeGreaterThan(0);
      expect(duration).toBeLessThan(5000);
    });
  });

  describe('Resource Cleanup', () => {
    test('WebSocket connections are properly cleaned up', () => {
      const initialConnections = aiWebSocketService.listeners.size || 0;
      
      // Add event listeners
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      aiWebSocketService.addEventListener('test', handler1);
      aiWebSocketService.addEventListener('test', handler2);
      
      // Remove listeners
      aiWebSocketService.removeEventListener('test', handler1);
      aiWebSocketService.removeEventListener('test', handler2);
      
      const finalConnections = aiWebSocketService.listeners.size || 0;
      
      // Should not have increased connection count
      expect(finalConnections).toBeLessThanOrEqual(initialConnections + 1);
    });

    test('AI service timers are cleared properly', () => {
      const initialTimers = global.setTimeout.mock?.calls?.length || 0;
      
      // Simulate operations that might create timers
      aiWebSocketService.connect().catch(() => {});
      
      // Disconnect should clean up timers
      aiWebSocketService.disconnect();
      
      // Verify no timer leaks (this is a simplified check)
      expect(true).toBe(true); // Placeholder for timer cleanup verification
    });
  });

  describe('Scalability', () => {
    test('performance scales linearly with component count', async () => {
      const componentCounts = [10, 50, 100, 200];
      const durations = [];

      for (const count of componentCounts) {
        const mockComponents = Array.from({ length: count }, (_, i) => ({
          id: `comp-${i}`,
          type: ['button', 'text', 'card'][i % 3],
          props: {}
        }));

        const startTime = performance.now();
        
        try {
          await aiDesignService.generateLayoutSuggestions(mockComponents);
        } catch (error) {
          // Expected to fail in test environment
        }

        const endTime = performance.now();
        durations.push(endTime - startTime);
      }

      // Performance should scale reasonably (not exponentially)
      for (let i = 1; i < durations.length; i++) {
        const ratio = durations[i] / durations[i - 1];
        const componentRatio = componentCounts[i] / componentCounts[i - 1];
        
        // Duration increase should not be more than 3x the component increase
        expect(ratio).toBeLessThan(componentRatio * 3);
      }
    });

    test('handles large numbers of suggestions efficiently', () => {
      const startTime = performance.now();
      
      // Simulate processing large suggestion sets
      const largeSuggestionSet = Array.from({ length: 1000 }, (_, i) => ({
        id: `suggestion-${i}`,
        name: `Layout ${i}`,
        description: `Description for layout ${i}`,
        score: Math.random() * 100,
        explanation: `Explanation for layout ${i}`,
        structure: { type: 'layout', index: i }
      }));

      // Process suggestions (simulate filtering, sorting, etc.)
      const filtered = largeSuggestionSet.filter(s => s.score > 50);
      const sorted = filtered.sort((a, b) => b.score - a.score);
      const top10 = sorted.slice(0, 10);

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(top10).toHaveLength(Math.min(10, filtered.length));
      expect(duration).toBeLessThan(1000); // Should process within 1 second
    });
  });
});

describe('Real-world Performance Scenarios', () => {
  test('typical user workflow performance', async () => {
    const startTime = performance.now();
    
    // Simulate typical user workflow
    const components = [];
    
    // User adds components one by one
    for (let i = 0; i < 5; i++) {
      components.push({
        id: `comp-${i}`,
        type: ['button', 'text', 'form', 'input', 'card'][i],
        props: {}
      });
      
      // AI suggestions triggered after each addition
      try {
        await Promise.all([
          aiDesignService.generateLayoutSuggestions(components),
          aiDesignService.generateComponentCombinations(components, components[i])
        ]);
      } catch (error) {
        // Expected to fail in test environment
      }
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    // Entire workflow should complete within 15 seconds
    expect(duration).toBeLessThan(15000);
  });

  test('heavy usage scenario performance', async () => {
    const startTime = performance.now();
    
    // Simulate heavy usage with many components
    const components = Array.from({ length: 100 }, (_, i) => ({
      id: `comp-${i}`,
      type: ['button', 'text', 'form', 'input', 'card', 'table', 'chart'][i % 7],
      props: {}
    }));

    // Multiple AI operations
    try {
      await Promise.all([
        aiDesignService.generateLayoutSuggestions(components),
        aiDesignService.generateComponentCombinations(components, components[0]),
        aiDesignService.analyzeAppStructure(components)
      ]);
    } catch (error) {
      // Expected to fail in test environment
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    // Should handle heavy usage within 30 seconds
    expect(duration).toBeLessThan(30000);
  });
});
