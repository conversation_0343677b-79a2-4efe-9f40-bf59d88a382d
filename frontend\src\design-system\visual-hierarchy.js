/**
 * Visual Hierarchy System
 * 
 * Comprehensive system for implementing clear information architecture
 * with proper spacing, typography scales, visual grouping, and consistent
 * interaction patterns throughout the application.
 */

import theme from './theme';

/**
 * Typography Hierarchy
 * Semantic typography scales for consistent information hierarchy
 */
export const typographyHierarchy = {
  // Display text for hero sections and major headings
  display: {
    large: {
      fontSize: theme.typography.fontSize['6xl'],
      fontWeight: theme.typography.fontWeight.bold,
      lineHeight: theme.typography.lineHeight.tight,
      letterSpacing: '-0.025em',
      marginBottom: theme.spacing[6],
      color: theme.colors.text.primary,
    },
    medium: {
      fontSize: theme.typography.fontSize['5xl'],
      fontWeight: theme.typography.fontWeight.bold,
      lineHeight: theme.typography.lineHeight.tight,
      letterSpacing: '-0.025em',
      marginBottom: theme.spacing[5],
      color: theme.colors.text.primary,
    },
    small: {
      fontSize: theme.typography.fontSize['4xl'],
      fontWeight: theme.typography.fontWeight.semibold,
      lineHeight: theme.typography.lineHeight.tight,
      letterSpacing: '-0.025em',
      marginBottom: theme.spacing[4],
      color: theme.colors.text.primary,
    },
  },

  // Headings for sections and subsections
  heading: {
    h1: {
      fontSize: theme.typography.fontSize['3xl'],
      fontWeight: theme.typography.fontWeight.bold,
      lineHeight: theme.typography.lineHeight.tight,
      marginBottom: theme.spacing[4],
      color: theme.colors.text.primary,
    },
    h2: {
      fontSize: theme.typography.fontSize['2xl'],
      fontWeight: theme.typography.fontWeight.semibold,
      lineHeight: theme.typography.lineHeight.snug,
      marginBottom: theme.spacing[3],
      color: theme.colors.text.primary,
    },
    h3: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.semibold,
      lineHeight: theme.typography.lineHeight.snug,
      marginBottom: theme.spacing[3],
      color: theme.colors.text.primary,
    },
    h4: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.normal,
      marginBottom: theme.spacing[2],
      color: theme.colors.text.primary,
    },
    h5: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.normal,
      marginBottom: theme.spacing[2],
      color: theme.colors.text.primary,
    },
    h6: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.normal,
      marginBottom: theme.spacing[1],
      color: theme.colors.text.secondary,
      textTransform: 'uppercase',
      letterSpacing: '0.05em',
    },
  },

  // Body text for content
  body: {
    large: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.regular,
      lineHeight: theme.typography.lineHeight.relaxed,
      marginBottom: theme.spacing[4],
      color: theme.colors.text.primary,
    },
    medium: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.regular,
      lineHeight: theme.typography.lineHeight.relaxed,
      marginBottom: theme.spacing[3],
      color: theme.colors.text.primary,
    },
    small: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.regular,
      lineHeight: theme.typography.lineHeight.normal,
      marginBottom: theme.spacing[2],
      color: theme.colors.text.secondary,
    },
  },

  // Labels and captions
  label: {
    large: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.normal,
      color: theme.colors.text.primary,
    },
    medium: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.normal,
      color: theme.colors.text.secondary,
    },
    small: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.regular,
      lineHeight: theme.typography.lineHeight.normal,
      color: theme.colors.text.tertiary,
    },
  },

  // Interactive elements
  interactive: {
    button: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.tight,
      letterSpacing: '0.025em',
    },
    link: {
      fontSize: 'inherit',
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: 'inherit',
      color: theme.colors.primary.main,
      textDecoration: 'none',
    },
    tab: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.normal,
    },
  },
};

/**
 * Spacing Hierarchy
 * Consistent spacing system for visual grouping and rhythm
 */
export const spacingHierarchy = {
  // Component internal spacing
  component: {
    tight: theme.spacing[1],      // 4px - Very tight spacing within components
    normal: theme.spacing[2],     // 8px - Normal internal spacing
    comfortable: theme.spacing[3], // 12px - Comfortable internal spacing
    loose: theme.spacing[4],      // 16px - Loose internal spacing
  },

  // Element spacing (between related elements)
  element: {
    tight: theme.spacing[2],      // 8px - Related elements
    normal: theme.spacing[3],     // 12px - Standard element spacing
    comfortable: theme.spacing[4], // 16px - Comfortable element spacing
    loose: theme.spacing[6],      // 24px - Loose element spacing
  },

  // Section spacing (between content sections)
  section: {
    tight: theme.spacing[6],      // 24px - Related sections
    normal: theme.spacing[8],     // 32px - Standard section spacing
    comfortable: theme.spacing[12], // 48px - Comfortable section spacing
    loose: theme.spacing[16],     // 64px - Major section breaks
  },

  // Layout spacing (major layout areas)
  layout: {
    tight: theme.spacing[8],      // 32px - Tight layout spacing
    normal: theme.spacing[12],    // 48px - Standard layout spacing
    comfortable: theme.spacing[16], // 64px - Comfortable layout spacing
    loose: theme.spacing[24],     // 96px - Loose layout spacing
  },
};

/**
 * Visual Grouping System
 * Consistent patterns for grouping related content
 */
export const visualGrouping = {
  // Card-based grouping
  card: {
    minimal: {
      background: theme.colors.background.paper,
      border: `1px solid ${theme.colors.border.light}`,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing[4],
      boxShadow: 'none',
    },
    elevated: {
      background: theme.colors.background.paper,
      border: `1px solid ${theme.colors.border.light}`,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing[6],
      boxShadow: theme.shadows.md,
    },
    prominent: {
      background: theme.colors.background.paper,
      border: `1px solid ${theme.colors.border.light}`,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing[8],
      boxShadow: theme.shadows.lg,
    },
  },

  // Section-based grouping
  section: {
    subtle: {
      background: theme.colors.background.secondary,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing[4],
    },
    defined: {
      background: theme.colors.background.paper,
      border: `1px solid ${theme.colors.border.light}`,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing[6],
    },
    prominent: {
      background: theme.colors.background.paper,
      border: `2px solid ${theme.colors.primary.light}`,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing[8],
    },
  },

  // List-based grouping
  list: {
    simple: {
      gap: theme.spacing[2],
      divider: 'none',
    },
    divided: {
      gap: theme.spacing[3],
      divider: `1px solid ${theme.colors.border.light}`,
    },
    spaced: {
      gap: theme.spacing[4],
      divider: 'none',
    },
  },
};

/**
 * Interaction Patterns
 * Consistent interaction states and feedback
 */
export const interactionPatterns = {
  // Button states
  button: {
    primary: {
      default: {
        background: theme.colors.primary.main,
        color: theme.colors.primary.contrastText,
        border: `1px solid ${theme.colors.primary.main}`,
        boxShadow: 'none',
      },
      hover: {
        background: theme.colors.primary.dark,
        color: theme.colors.primary.contrastText,
        border: `1px solid ${theme.colors.primary.dark}`,
        boxShadow: theme.shadows.sm,
        transform: 'translateY(-1px)',
      },
      active: {
        background: theme.colors.primary.dark,
        color: theme.colors.primary.contrastText,
        border: `1px solid ${theme.colors.primary.dark}`,
        boxShadow: theme.shadows.inner,
        transform: 'translateY(0)',
      },
      focus: {
        outline: `2px solid ${theme.colors.primary.main}`,
        outlineOffset: '2px',
      },
      disabled: {
        background: theme.colors.neutral[300],
        color: theme.colors.neutral[500],
        border: `1px solid ${theme.colors.neutral[300]}`,
        cursor: 'not-allowed',
        opacity: 0.6,
      },
    },
    secondary: {
      default: {
        background: 'transparent',
        color: theme.colors.primary.main,
        border: `1px solid ${theme.colors.primary.main}`,
        boxShadow: 'none',
      },
      hover: {
        background: theme.colors.primary.light,
        color: theme.colors.primary.dark,
        border: `1px solid ${theme.colors.primary.main}`,
        boxShadow: theme.shadows.sm,
      },
      active: {
        background: theme.colors.primary.light,
        color: theme.colors.primary.dark,
        border: `1px solid ${theme.colors.primary.dark}`,
        boxShadow: theme.shadows.inner,
      },
      focus: {
        outline: `2px solid ${theme.colors.primary.main}`,
        outlineOffset: '2px',
      },
    },
  },

  // Interactive element states
  interactive: {
    default: {
      cursor: 'pointer',
      transition: theme.transitions.default,
    },
    hover: {
      background: theme.colors.interactive.hover,
      transform: 'translateY(-1px)',
      boxShadow: theme.shadows.sm,
    },
    active: {
      background: theme.colors.interactive.pressed,
      transform: 'translateY(0)',
      boxShadow: theme.shadows.inner,
    },
    focus: {
      outline: `2px solid ${theme.colors.primary.main}`,
      outlineOffset: '2px',
    },
    selected: {
      background: theme.colors.interactive.selected,
      border: `1px solid ${theme.colors.primary.main}`,
    },
    disabled: {
      background: theme.colors.interactive.disabled,
      cursor: 'not-allowed',
      opacity: 0.6,
    },
  },

  // Drag and drop states
  dragDrop: {
    draggable: {
      cursor: 'grab',
      transition: theme.transitions.default,
    },
    dragging: {
      cursor: 'grabbing',
      opacity: 0.8,
      transform: 'rotate(2deg) scale(1.02)',
      boxShadow: theme.shadows.lg,
      zIndex: theme.zIndex.dragOverlay,
    },
    dropTarget: {
      background: theme.colors.success.light,
      border: `2px dashed ${theme.colors.success.main}`,
      borderRadius: theme.borderRadius.md,
    },
    dropActive: {
      background: theme.colors.success.light,
      border: `2px solid ${theme.colors.success.main}`,
      borderRadius: theme.borderRadius.md,
      boxShadow: `0 0 0 4px ${theme.colors.success.main}20`,
    },
  },
};

/**
 * Information Architecture Patterns
 * Consistent patterns for organizing and presenting information
 */
export const informationArchitecture = {
  // Navigation patterns
  navigation: {
    primary: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      spacing: theme.spacing[6],
      hierarchy: 'horizontal',
    },
    secondary: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.regular,
      spacing: theme.spacing[4],
      hierarchy: 'vertical',
    },
    breadcrumb: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.regular,
      spacing: theme.spacing[2],
      separator: '/',
    },
  },

  // Content organization
  content: {
    hero: {
      alignment: 'center',
      spacing: spacingHierarchy.layout.comfortable,
      typography: typographyHierarchy.display.large,
    },
    section: {
      alignment: 'left',
      spacing: spacingHierarchy.section.normal,
      typography: typographyHierarchy.heading.h2,
    },
    subsection: {
      alignment: 'left',
      spacing: spacingHierarchy.element.comfortable,
      typography: typographyHierarchy.heading.h3,
    },
    paragraph: {
      alignment: 'left',
      spacing: spacingHierarchy.element.normal,
      typography: typographyHierarchy.body.medium,
    },
  },

  // Form organization
  form: {
    fieldGroup: {
      spacing: spacingHierarchy.element.comfortable,
      grouping: visualGrouping.section.subtle,
    },
    field: {
      spacing: spacingHierarchy.element.normal,
      labelSpacing: theme.spacing[1],
    },
    actions: {
      spacing: spacingHierarchy.element.normal,
      alignment: 'right',
      grouping: 'horizontal',
    },
  },

  // Data presentation
  data: {
    table: {
      headerSpacing: spacingHierarchy.component.comfortable,
      cellSpacing: spacingHierarchy.component.normal,
      rowSpacing: theme.spacing[2],
    },
    list: {
      itemSpacing: spacingHierarchy.element.normal,
      groupSpacing: spacingHierarchy.section.tight,
    },
    card: {
      contentSpacing: spacingHierarchy.component.comfortable,
      actionSpacing: spacingHierarchy.element.normal,
    },
  },
};

/**
 * Utility functions for applying visual hierarchy
 */
export const hierarchyUtils = {
  /**
   * Get typography styles by semantic level
   */
  getTypography: (category, level) => {
    return typographyHierarchy[category]?.[level] || {};
  },

  /**
   * Get spacing by context and level
   */
  getSpacing: (context, level) => {
    return spacingHierarchy[context]?.[level] || theme.spacing[4];
  },

  /**
   * Get visual grouping styles
   */
  getGrouping: (type, level) => {
    return visualGrouping[type]?.[level] || {};
  },

  /**
   * Get interaction pattern styles
   */
  getInteraction: (element, state) => {
    return interactionPatterns[element]?.[state] || {};
  },

  /**
   * Create responsive typography
   */
  createResponsiveTypography: (desktop, tablet, mobile) => ({
    ...hierarchyUtils.getTypography(...desktop),
    [theme.mediaQueries.maxLg]: hierarchyUtils.getTypography(...tablet),
    [theme.mediaQueries.maxMd]: hierarchyUtils.getTypography(...mobile),
  }),

  /**
   * Create responsive spacing
   */
  createResponsiveSpacing: (desktop, tablet, mobile) => ({
    margin: hierarchyUtils.getSpacing(...desktop),
    [theme.mediaQueries.maxLg]: {
      margin: hierarchyUtils.getSpacing(...tablet),
    },
    [theme.mediaQueries.maxMd]: {
      margin: hierarchyUtils.getSpacing(...mobile),
    },
  }),
};

/**
 * Component-specific hierarchy patterns
 * Pre-configured patterns for common UI components
 */
export const componentHierarchy = {
  // App Builder specific patterns
  appBuilder: {
    palette: {
      header: hierarchyUtils.getTypography('heading', 'h4'),
      categoryTitle: hierarchyUtils.getTypography('heading', 'h5'),
      componentLabel: hierarchyUtils.getTypography('label', 'medium'),
      componentDescription: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        header: spacingHierarchy.section.tight,
        category: spacingHierarchy.element.comfortable,
        component: spacingHierarchy.element.normal,
      },
    },

    propertyEditor: {
      header: hierarchyUtils.getTypography('heading', 'h4'),
      groupTitle: hierarchyUtils.getTypography('heading', 'h6'),
      fieldLabel: hierarchyUtils.getTypography('label', 'large'),
      fieldHelp: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        header: spacingHierarchy.section.tight,
        group: spacingHierarchy.element.comfortable,
        field: spacingHierarchy.element.normal,
      },
    },

    preview: {
      toolbar: hierarchyUtils.getTypography('label', 'medium'),
      deviceLabel: hierarchyUtils.getTypography('label', 'small'),
      statusText: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        toolbar: spacingHierarchy.component.comfortable,
        controls: spacingHierarchy.component.normal,
      },
    },
  },

  // Common UI patterns
  common: {
    modal: {
      title: hierarchyUtils.getTypography('heading', 'h3'),
      content: hierarchyUtils.getTypography('body', 'medium'),
      actions: hierarchyUtils.getTypography('interactive', 'button'),
      spacing: {
        title: spacingHierarchy.section.tight,
        content: spacingHierarchy.element.comfortable,
        actions: spacingHierarchy.element.normal,
      },
    },

    card: {
      title: hierarchyUtils.getTypography('heading', 'h4'),
      subtitle: hierarchyUtils.getTypography('body', 'small'),
      content: hierarchyUtils.getTypography('body', 'medium'),
      meta: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        title: spacingHierarchy.element.normal,
        content: spacingHierarchy.element.comfortable,
        actions: spacingHierarchy.element.normal,
      },
    },

    form: {
      title: hierarchyUtils.getTypography('heading', 'h3'),
      sectionTitle: hierarchyUtils.getTypography('heading', 'h5'),
      fieldLabel: hierarchyUtils.getTypography('label', 'large'),
      fieldHelp: hierarchyUtils.getTypography('label', 'small'),
      errorText: {
        ...hierarchyUtils.getTypography('label', 'small'),
        color: theme.colors.error.main,
      },
      spacing: {
        title: spacingHierarchy.section.normal,
        section: spacingHierarchy.element.comfortable,
        field: spacingHierarchy.element.normal,
      },
    },
  },
};

/**
 * Accessibility-enhanced hierarchy patterns
 * Patterns that include accessibility considerations
 */
export const accessibleHierarchy = {
  // Focus management patterns
  focus: {
    ring: {
      outline: `2px solid ${theme.colors.primary.main}`,
      outlineOffset: '2px',
      borderRadius: theme.borderRadius.sm,
    },
    highContrast: {
      '@media (prefers-contrast: high)': {
        outline: `3px solid`,
        outlineOffset: '2px',
      },
    },
  },

  // Screen reader patterns
  screenReader: {
    srOnly: theme.accessibility.srOnly,
    skipLink: {
      position: 'absolute',
      top: '-40px',
      left: theme.spacing[2],
      background: theme.colors.primary.main,
      color: theme.colors.primary.contrastText,
      padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
      borderRadius: theme.borderRadius.md,
      textDecoration: 'none',
      fontWeight: theme.typography.fontWeight.medium,
      zIndex: theme.zIndex.skipLink,
      transition: theme.transitions.default,
      '&:focus': {
        top: theme.spacing[2],
      },
    },
  },

  // Reduced motion patterns
  reducedMotion: {
    '@media (prefers-reduced-motion: reduce)': {
      transition: 'none !important',
      animation: 'none !important',
      transform: 'none !important',
    },
  },

  // High contrast patterns
  highContrast: {
    '@media (prefers-contrast: high)': {
      border: '1px solid',
      outline: '1px solid',
      backgroundColor: 'Canvas',
      color: 'CanvasText',
    },
  },
};

/**
 * Animation and transition hierarchy
 * Consistent animation patterns for different interaction levels
 */
export const animationHierarchy = {
  // Micro-interactions (button hovers, etc.)
  micro: {
    duration: '150ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    properties: ['background-color', 'border-color', 'color', 'box-shadow'],
  },

  // Component transitions (panel slides, etc.)
  component: {
    duration: '250ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    properties: ['transform', 'opacity', 'width', 'height'],
  },

  // Layout transitions (page changes, etc.)
  layout: {
    duration: '350ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    properties: ['transform', 'opacity'],
  },

  // Attention-grabbing animations
  attention: {
    duration: '500ms',
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    properties: ['transform', 'opacity', 'scale'],
  },
};

// Export the complete visual hierarchy system
export default {
  typography: typographyHierarchy,
  spacing: spacingHierarchy,
  grouping: visualGrouping,
  interaction: interactionPatterns,
  architecture: informationArchitecture,
  component: componentHierarchy,
  accessible: accessibleHierarchy,
  animation: animationHierarchy,
  utils: hierarchyUtils,
};
