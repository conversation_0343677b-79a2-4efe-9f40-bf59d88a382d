#!/usr/bin/env python3
"""
Database Operations Test Script for App Builder 201
Tests CRUD operations on the PostgreSQL database through Django ORM
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to the Python path
sys.path.append('/usr/src/app')

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app_builder_201.settings')
django.setup()

from my_app.models import App, ComponentTemplate, AppVersion
from django.contrib.auth.models import User
import json
from datetime import datetime

def test_database_operations():
    """Test all CRUD operations on the database"""
    
    print("🗄️ Testing Database Operations")
    print("=" * 40)
    
    # Test 1: Database Connection
    print("\n1. Testing Database Connection...")
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            print(f"✅ PostgreSQL Version: {version}")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    # Test 2: Create Operations
    print("\n2. Testing CREATE operations...")
    try:
        # Create a test user
        test_user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        print(f"✅ User {'created' if created else 'found'}: {test_user.username}")
        
        # Create a test app
        test_app_data = {
            "components": [
                {"id": "1", "type": "button", "props": {"text": "Test Button"}},
                {"id": "2", "type": "input", "props": {"placeholder": "Test Input"}}
            ],
            "layouts": [
                {"id": "main", "components": ["1", "2"]}
            ],
            "styles": {
                "theme": "default",
                "colors": {"primary": "#007bff"}
            }
        }
        
        test_app = App.objects.create(
            name="Test App",
            description="A test application for database operations",
            user=test_user,
            app_data=json.dumps(test_app_data),
            is_public=True
        )
        print(f"✅ App created: {test_app.name} (ID: {test_app.id})")
        
        # Create a component template
        template_props = {
            "text": "Default Button Text",
            "color": "primary",
            "size": "medium"
        }
        
        component_template = ComponentTemplate.objects.create(
            name="Default Button",
            description="A default button component template",
            component_type="button",
            default_props=json.dumps(template_props),
            user=test_user,
            is_public=True
        )
        print(f"✅ Component Template created: {component_template.name}")
        
    except Exception as e:
        print(f"❌ CREATE operations failed: {e}")
        return False
    
    # Test 3: Read Operations
    print("\n3. Testing READ operations...")
    try:
        # Read all apps
        all_apps = App.objects.all()
        print(f"✅ Total apps in database: {all_apps.count()}")
        
        # Read specific app
        app = App.objects.get(id=test_app.id)
        app_data = app.get_app_data_json()
        print(f"✅ App data retrieved: {len(app_data.get('components', []))} components")
        
        # Read component templates
        templates = ComponentTemplate.objects.filter(user=test_user)
        print(f"✅ User templates: {templates.count()}")
        
        # Test filtering and queries
        public_apps = App.objects.filter(is_public=True)
        print(f"✅ Public apps: {public_apps.count()}")
        
    except Exception as e:
        print(f"❌ READ operations failed: {e}")
        return False
    
    # Test 4: Update Operations
    print("\n4. Testing UPDATE operations...")
    try:
        # Update app
        test_app.description = "Updated test application description"
        test_app.save()
        print(f"✅ App description updated")
        
        # Update app data
        updated_data = app_data.copy()
        updated_data['components'].append({
            "id": "3", 
            "type": "text", 
            "props": {"content": "Updated content"}
        })
        test_app.app_data = json.dumps(updated_data)
        test_app.save()
        print(f"✅ App data updated: {len(updated_data['components'])} components")
        
        # Update component template
        component_template.description = "Updated button template description"
        component_template.save()
        print(f"✅ Component template updated")
        
    except Exception as e:
        print(f"❌ UPDATE operations failed: {e}")
        return False
    
    # Test 5: App Versioning
    print("\n5. Testing App Versioning...")
    try:
        # Create an app version using the model method
        app_version = test_app.create_version(
            commit_message="Test version creation",
            created_by=test_user
        )
        print(f"✅ App version created: v{app_version.version_number}")
        
        # Check version count
        version_count = AppVersion.objects.filter(app=test_app).count()
        print(f"✅ Total versions for app: {version_count}")
        
    except Exception as e:
        print(f"❌ App versioning failed: {e}")
        return False
    
    # Test 6: Complex Queries
    print("\n6. Testing Complex Queries...")
    try:
        # Query with joins
        apps_with_versions = App.objects.filter(
            versions__isnull=False
        ).distinct()
        print(f"✅ Apps with versions: {apps_with_versions.count()}")
        
        # Query with aggregation
        from django.db.models import Count
        user_app_counts = User.objects.annotate(
            app_count=Count('apps')
        ).filter(app_count__gt=0)
        print(f"✅ Users with apps: {user_app_counts.count()}")
        
        # Query with date filtering
        from django.utils import timezone
        from datetime import timedelta
        recent_apps = App.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=1)
        )
        print(f"✅ Recent apps (last hour): {recent_apps.count()}")
        
    except Exception as e:
        print(f"❌ Complex queries failed: {e}")
        return False
    
    # Test 7: Transaction Testing
    print("\n7. Testing Database Transactions...")
    try:
        from django.db import transaction
        
        with transaction.atomic():
            # Create multiple related objects in a transaction
            bulk_app = App.objects.create(
                name="Bulk Test App",
                description="Testing bulk operations",
                user=test_user,
                app_data='{"components": []}'
            )
            
            # Create multiple versions
            for i in range(3):
                bulk_app.create_version(
                    commit_message=f"Bulk version {i+1}",
                    created_by=test_user
                )
            
            print(f"✅ Transaction completed: Created app with 3 versions")
        
    except Exception as e:
        print(f"❌ Transaction testing failed: {e}")
        return False
    
    # Test 8: Performance Test
    print("\n8. Testing Database Performance...")
    try:
        import time
        
        # Bulk create test
        start_time = time.time()
        bulk_templates = []
        for i in range(10):
            bulk_templates.append(ComponentTemplate(
                name=f"Bulk Template {i}",
                description=f"Bulk template number {i}",
                component_type="test",
                default_props='{}',
                user=test_user,
                is_public=False
            ))
        
        ComponentTemplate.objects.bulk_create(bulk_templates)
        bulk_time = time.time() - start_time
        print(f"✅ Bulk created 10 templates in {bulk_time:.3f}s")
        
        # Query performance test
        start_time = time.time()
        all_templates = list(ComponentTemplate.objects.select_related('user').all())
        query_time = time.time() - start_time
        print(f"✅ Queried {len(all_templates)} templates in {query_time:.3f}s")
        
    except Exception as e:
        print(f"❌ Performance testing failed: {e}")
        return False
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Database Operations Summary:")
    print(f"✅ Total Apps: {App.objects.count()}")
    print(f"✅ Total Component Templates: {ComponentTemplate.objects.count()}")
    print(f"✅ Total App Versions: {AppVersion.objects.count()}")
    print(f"✅ Total Users: {User.objects.count()}")
    
    print("\n🎉 All database operations completed successfully!")
    return True

if __name__ == "__main__":
    success = test_database_operations()
    sys.exit(0 if success else 1)
