/**
 * Error Handler Utilities
 * 
 * This module provides utilities for handling errors.
 */

// Import error tracking service
import errorTracker from './errorTracker';

/**
 * Handle API error
 * @param {Error} error - Error object
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Options
 * @returns {Object} Error result
 */
export const handleApiError = (error, endpoint, options = {}) => {
  const {
    fallbackData = null,
    throwError = false,
    logLevel = 'warn',
    showNotification = false
  } = options;
  
  // Get error details
  const errorDetails = getErrorDetails(error);
  
  // Log error
  logError(errorDetails, endpoint, logLevel);
  
  // Track error
  trackError(errorDetails, endpoint);
  
  // Show notification if needed
  if (showNotification) {
    showErrorNotification(errorDetails);
  }
  
  // Throw error if needed
  if (throwError) {
    throw error;
  }
  
  // Return fallback data
  return {
    error: true,
    errorDetails,
    data: fallbackData
  };
};

/**
 * Get error details
 * @param {Error} error - Error object
 * @returns {Object} Error details
 */
export const getErrorDetails = (error) => {
  // Default error details
  const details = {
    message: 'An unknown error occurred',
    code: 'UNKNOWN_ERROR',
    status: 0,
    timestamp: new Date().toISOString(),
    originalError: error
  };
  
  // Extract error details from Axios error
  if (error.response) {
    // Server responded with an error status
    details.message = error.response.data?.message || error.message;
    details.code = error.response.data?.code || `HTTP_${error.response.status}`;
    details.status = error.response.status;
    details.data = error.response.data;
  } else if (error.request) {
    // Request was made but no response was received
    details.message = 'No response received from server';
    details.code = 'NO_RESPONSE';
  } else {
    // Error in setting up the request
    details.message = error.message;
    details.code = error.code || 'REQUEST_SETUP_ERROR';
  }
  
  return details;
};

/**
 * Log error
 * @param {Object} errorDetails - Error details
 * @param {string} endpoint - API endpoint
 * @param {string} level - Log level
 */
export const logError = (errorDetails, endpoint, level = 'warn') => {
  const message = `API Error (${endpoint}): ${errorDetails.message}`;
  
  switch (level) {
    case 'error':
      console.error(message, errorDetails);
      break;
    case 'warn':
      console.warn(message, errorDetails);
      break;
    case 'info':
      console.info(message, errorDetails);
      break;
    default:
      console.log(message, errorDetails);
  }
};

/**
 * Track error
 * @param {Object} errorDetails - Error details
 * @param {string} endpoint - API endpoint
 */
export const trackError = (errorDetails, endpoint) => {
  errorTracker.trackError({
    type: 'api_error',
    endpoint,
    ...errorDetails
  });
};

/**
 * Show error notification
 * @param {Object} errorDetails - Error details
 */
export const showErrorNotification = (errorDetails) => {
  // Check if we have a notification system
  if (window.showNotification) {
    window.showNotification({
      type: 'error',
      title: 'Error',
      message: errorDetails.message,
      duration: 5000
    });
  }
};

/**
 * Handle WebSocket error
 * @param {Error} error - Error object
 * @param {string} context - Error context
 * @param {Object} options - Options
 */
export const handleWebSocketError = (error, context, options = {}) => {
  const {
    logLevel = 'warn',
    showNotification = false
  } = options;
  
  // Get error message
  const message = error.message || 'WebSocket error';
  
  // Log error
  switch (logLevel) {
    case 'error':
      console.error(`WebSocket Error (${context}):`, message, error);
      break;
    case 'warn':
      console.warn(`WebSocket Error (${context}):`, message, error);
      break;
    case 'info':
      console.info(`WebSocket Error (${context}):`, message, error);
      break;
    default:
      console.log(`WebSocket Error (${context}):`, message, error);
  }
  
  // Track error
  errorTracker.trackError({
    type: 'websocket_error',
    context,
    message,
    timestamp: new Date().toISOString(),
    originalError: error
  });
  
  // Show notification if needed
  if (showNotification && window.showNotification) {
    window.showNotification({
      type: 'error',
      title: 'WebSocket Error',
      message,
      duration: 5000
    });
  }
};

/**
 * Handle component error
 * @param {Error} error - Error object
 * @param {Object} errorInfo - React error info
 * @param {string} componentName - Component name
 */
export const handleComponentError = (error, errorInfo, componentName) => {
  console.error(`Error in ${componentName}:`, error);
  
  if (errorInfo) {
    console.error('Component stack:', errorInfo.componentStack);
  }
  
  // Track error
  errorTracker.trackError({
    type: 'component_error',
    component: componentName,
    message: error.message,
    stack: error.stack,
    componentStack: errorInfo?.componentStack,
    timestamp: new Date().toISOString()
  });
};

/**
 * Create error boundary handler
 * @param {string} componentName - Component name
 * @returns {Object} Error boundary handlers
 */
export const createErrorBoundaryHandler = (componentName) => {
  return {
    onError: (error, errorInfo) => {
      handleComponentError(error, errorInfo, componentName);
    },
    fallback: ({ error }) => {
      return (
        <div className="error-boundary">
          <h3>Something went wrong</h3>
          <p>{error.message}</p>
          <button onClick={() => window.location.reload()}>
            Reload page
          </button>
        </div>
      );
    }
  };
};

export default {
  handleApiError,
  handleWebSocketError,
  handleComponentError,
  createErrorBoundaryHandler,
  getErrorDetails,
  logError,
  trackError,
  showErrorNotification
};
