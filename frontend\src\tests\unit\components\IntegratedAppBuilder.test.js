import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import the component to test
import IntegratedAppBuilder from '../../../components/builder/IntegratedAppBuilder';

// Mock external dependencies
jest.mock('../../../hooks/useWebSocket', () => () => ({
  connected: true,
  connecting: false,
  messages: [],
  sendMessage: jest.fn(),
  lastMessage: null,
  readyState: 1,
}));

jest.mock('../../../hooks/useAIDesignSuggestions', () => () => ({
  suggestions: [],
  loading: false,
  generateSuggestions: jest.fn(),
  applySuggestion: jest.fn(),
  dismissSuggestion: jest.fn(),
}));

jest.mock('../../../hooks/useTemplates', () => () => ({
  templates: [],
  loading: false,
  saveAsTemplate: jest.fn(),
  loadTemplate: jest.fn(),
  deleteTemplate: jest.fn(),
}));

jest.mock('../../../hooks/useCodeExport', () => () => ({
  exportCode: jest.fn(),
  exportFormats: ['react', 'vue', 'angular'],
  loading: false,
  downloadCode: jest.fn(),
}));

jest.mock('../../../hooks/useCollaboration', () => () => ({
  collaborators: [],
  comments: [],
  addComment: jest.fn(),
  deleteComment: jest.fn(),
  updateComment: jest.fn(),
}));

// Mock Ant Design components that might cause issues
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  Layout: {
    Header: ({ children }) => <div data-testid="layout-header">{children}</div>,
    Content: ({ children }) => <div data-testid="layout-content">{children}</div>,
    Sider: ({ children }) => <div data-testid="layout-sider">{children}</div>,
  },
  Button: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
  Card: ({ children, title }) => (
    <div data-testid="card">
      {title && <div data-testid="card-title">{title}</div>}
      {children}
    </div>
  ),
  Tabs: ({ children, items, onChange }) => (
    <div data-testid="tabs">
      {items?.map((item, index) => (
        <button key={index} onClick={() => onChange?.(item.key)}>
          {item.label}
        </button>
      ))}
      {children}
    </div>
  ),
}));

// Create mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: (state = {
        components: [],
        selectedComponent: null,
        layouts: [],
        styles: {},
        ...initialState.app
      }, action) => state,
      ui: (state = {
        sidebarOpen: true,
        currentView: 'components',
        previewMode: false,
        ...initialState.ui
      }, action) => state,
      websocket: (state = {
        connected: true,
        connecting: false,
        ...initialState.websocket
      }, action) => state,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
};

// Test wrapper component
const TestWrapper = ({ children, store = createMockStore() }) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('IntegratedAppBuilder', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    test('renders without crashing', () => {
      render(
        <TestWrapper>
          <IntegratedAppBuilder />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });

    test('renders with all features enabled', () => {
      const enableFeatures = {
        websocket: true,
        tutorial: true,
        aiSuggestions: true,
        templates: true,
        codeExport: true,
        collaboration: true,
      };

      render(
        <TestWrapper>
          <IntegratedAppBuilder enableFeatures={enableFeatures} />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });

    test('renders with features disabled', () => {
      const enableFeatures = {
        websocket: false,
        tutorial: false,
        aiSuggestions: false,
        templates: false,
        codeExport: false,
        collaboration: false,
      };

      render(
        <TestWrapper>
          <IntegratedAppBuilder enableFeatures={enableFeatures} />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });
  });

  describe('Component Interaction', () => {
    test('handles component selection', async () => {
      const store = createMockStore({
        app: {
          components: [
            { id: '1', type: 'button', props: { text: 'Test Button' } },
            { id: '2', type: 'input', props: { placeholder: 'Test Input' } },
          ],
        },
      });

      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Component should render with existing components
      expect(store.getState().app.components).toHaveLength(2);
    });

    test('handles drag and drop operations', async () => {
      render(
        <TestWrapper>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Simulate drag start
      const dragEvent = global.testUtils.createMockDragEvent('dragstart', {
        setData: jest.fn(),
      });

      // This would be more complex in a real implementation
      // For now, just verify the component renders
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });
  });

  describe('Feature Integration', () => {
    test('integrates with WebSocket when enabled', () => {
      const enableFeatures = { websocket: true };

      render(
        <TestWrapper>
          <IntegratedAppBuilder enableFeatures={enableFeatures} />
        </TestWrapper>
      );

      // WebSocket hook should be called
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });

    test('integrates with AI suggestions when enabled', () => {
      const enableFeatures = { aiSuggestions: true };

      render(
        <TestWrapper>
          <IntegratedAppBuilder enableFeatures={enableFeatures} />
        </TestWrapper>
      );

      // AI suggestions hook should be called
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });

    test('integrates with templates when enabled', () => {
      const enableFeatures = { templates: true };

      render(
        <TestWrapper>
          <IntegratedAppBuilder enableFeatures={enableFeatures} />
        </TestWrapper>
      );

      // Templates hook should be called
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });

    test('integrates with code export when enabled', () => {
      const enableFeatures = { codeExport: true };

      render(
        <TestWrapper>
          <IntegratedAppBuilder enableFeatures={enableFeatures} />
        </TestWrapper>
      );

      // Code export hook should be called
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });

    test('integrates with collaboration when enabled', () => {
      const enableFeatures = { collaboration: true };

      render(
        <TestWrapper>
          <IntegratedAppBuilder enableFeatures={enableFeatures} />
        </TestWrapper>
      );

      // Collaboration hook should be called
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('handles missing props gracefully', () => {
      render(
        <TestWrapper>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });

    test('handles invalid project data', () => {
      const invalidProject = { invalid: 'data' };

      render(
        <TestWrapper>
          <IntegratedAppBuilder project={invalidProject} />
        </TestWrapper>
      );

      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    test('renders efficiently with many components', () => {
      const manyComponents = Array.from({ length: 100 }, (_, i) => ({
        id: `component-${i}`,
        type: 'button',
        props: { text: `Button ${i}` },
      }));

      const store = createMockStore({
        app: { components: manyComponents },
      });

      const startTime = performance.now();
      
      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(1000); // 1 second
      expect(screen.getByTestId('layout-content')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      const content = screen.getByTestId('layout-content');
      expect(content).toBeInTheDocument();
      
      // Check for basic accessibility structure
      expect(content).toBeVisible();
    });

    test('supports keyboard navigation', async () => {
      render(
        <TestWrapper>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Test tab navigation
      await user.tab();
      
      // Should have focusable elements
      expect(document.activeElement).toBeDefined();
    });
  });
});
