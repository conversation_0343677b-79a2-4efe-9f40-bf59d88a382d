/**
 * Enhanced Global Styles for App Builder
 * 
 * This file provides global styles that implement the design system
 * with accessibility, consistency, and user experience improvements.
 */

/* Import enhanced theme variables */
@import '../theme/variables.css';

/* Import Google Fonts with font-display optimization */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  /* Improve text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  /* Prevent horizontal scroll */
  overflow-x: hidden;
  
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background-default);
  
  /* Improve font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* Prevent horizontal scroll */
  overflow-x: hidden;
  
  /* Minimum height for full viewport */
  min-height: 100vh;
}

/* Enhanced focus styles for accessibility */
:focus {
  outline: 2px solid var(--color-primary-main);
  outline-offset: 2px;
}

/* Remove focus outline for mouse users, keep for keyboard users */
:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary-main);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :focus,
  :focus-visible {
    outline: 3px solid;
    outline-offset: 2px;
  }
}

/* Enhanced typography hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  letter-spacing: -0.025em;
}

h2 {
  font-size: var(--font-size-3xl);
  letter-spacing: -0.025em;
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

/* Enhanced paragraph and text styles */
p {
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-relaxed);
}

/* Link styles with better accessibility */
a {
  color: var(--color-primary-main);
  text-decoration: none;
  transition: var(--transition-default);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-primary-main);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* Button reset and base styles */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  color: inherit;
  transition: var(--transition-default);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Form element improvements */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background-color: var(--color-background-paper);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-3);
  transition: var(--transition-default);
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-primary-main);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

input:invalid,
textarea:invalid,
select:invalid {
  border-color: var(--color-error-main);
}

input:invalid:focus,
textarea:invalid:focus,
select:invalid:focus {
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* List improvements */
ul, ol {
  padding-left: var(--spacing-6);
  margin-bottom: var(--spacing-4);
}

li {
  margin-bottom: var(--spacing-1);
}

/* Table improvements */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-6);
}

th, td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--color-neutral-200);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  background-color: var(--color-neutral-50);
}

/* Code and pre styles */
code {
  font-family: 'Fira Code', 'JetBrains Mono', 'Roboto Mono', monospace;
  font-size: 0.875em;
  background-color: var(--color-neutral-100);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  color: var(--color-text-primary);
}

pre {
  font-family: 'Fira Code', 'JetBrains Mono', 'Roboto Mono', monospace;
  background-color: var(--color-neutral-100);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  overflow-x: auto;
  margin-bottom: var(--spacing-4);
}

pre code {
  background: none;
  padding: 0;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary-main);
  color: var(--color-primary-contrast);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  z-index: var(--z-index-50);
  transition: var(--transition-default);
}

.skip-link:focus {
  top: 6px;
}

/* Loading and animation utilities */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.fade-in {
  animation: fadeIn 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-out {
  animation: fadeOut 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive utilities */
@media (max-width: 767px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .hide-desktop {
    display: none !important;
  }
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  .no-print {
    display: none !important;
  }
}
