/**
 * Tutorial Progress Component
 * 
 * Displays tutorial progress, completion badges, and statistics
 * for the user's tutorial journey.
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Progress, 
  Badge, 
  Typography, 
  Row, 
  Col, 
  Statistic, 
  Timeline,
  Avatar,
  Space,
  Button,
  Tooltip,
  Empty
} from 'antd';
import {
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  StarOutlined,
  BookOutlined,
  FireOutlined,
  TargetOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import { TUTORIAL_STATUS, TUTORIAL_CATEGORIES } from './types';

const { Title, Text } = Typography;

// Styled Components
const ProgressCard = styled(Card)`
  margin-bottom: 16px;
  
  .ant-card-body {
    padding: 20px;
  }
`;

const BadgeContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
`;

const BadgeItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: ${props => props.earned ? '#f6ffed' : '#fafafa'};
  border-color: ${props => props.earned ? '#b7eb8f' : '#d9d9d9'};
  min-width: 80px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const CategoryIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  margin-bottom: 8px;
  background: ${props => {
    switch (props.category) {
      case TUTORIAL_CATEGORIES.BEGINNER: return '#52c41a';
      case TUTORIAL_CATEGORIES.INTERMEDIATE: return '#1890ff';
      case TUTORIAL_CATEGORIES.ADVANCED: return '#f5222d';
      case TUTORIAL_CATEGORIES.FEATURE_SPECIFIC: return '#722ed1';
      default: return '#8c8c8c';
    }
  }};
`;

const StatsContainer = styled.div`
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const TutorialProgress = ({ userId = 'anonymous' }) => {
  const {
    getAllTutorials,
    getTutorialProgress,
    getStatistics,
    getBadges,
    startTutorial
  } = useTutorial();

  const [tutorials] = useState(getAllTutorials());
  const [statistics, setStatistics] = useState(null);
  const [badges, setBadges] = useState([]);

  useEffect(() => {
    setStatistics(getStatistics());
    setBadges(getBadges());
  }, [getStatistics, getBadges]);

  const getTutorialsByCategory = () => {
    const categorized = {};
    tutorials.forEach(tutorial => {
      if (!categorized[tutorial.category]) {
        categorized[tutorial.category] = [];
      }
      categorized[tutorial.category].push(tutorial);
    });
    return categorized;
  };

  const getCategoryProgress = (category) => {
    const categoryTutorials = tutorials.filter(t => t.category === category);
    const completed = categoryTutorials.filter(t => {
      const progress = getTutorialProgress(t.id);
      return progress?.status === TUTORIAL_STATUS.COMPLETED;
    }).length;
    
    return {
      completed,
      total: categoryTutorials.length,
      percentage: categoryTutorials.length > 0 ? (completed / categoryTutorials.length) * 100 : 0
    };
  };

  const getOverallProgress = () => {
    const completed = tutorials.filter(t => {
      const progress = getTutorialProgress(t.id);
      return progress?.status === TUTORIAL_STATUS.COMPLETED;
    }).length;
    
    return {
      completed,
      total: tutorials.length,
      percentage: tutorials.length > 0 ? (completed / tutorials.length) * 100 : 0
    };
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case TUTORIAL_CATEGORIES.BEGINNER: return <BookOutlined />;
      case TUTORIAL_CATEGORIES.INTERMEDIATE: return <TargetOutlined />;
      case TUTORIAL_CATEGORIES.ADVANCED: return <FireOutlined />;
      case TUTORIAL_CATEGORIES.FEATURE_SPECIFIC: return <StarOutlined />;
      default: return <BookOutlined />;
    }
  };

  const getDifficultyStars = (difficulty) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarOutlined 
        key={i} 
        style={{ 
          color: i < difficulty ? '#faad14' : '#d9d9d9',
          fontSize: '12px'
        }} 
      />
    ));
  };

  const overallProgress = getOverallProgress();
  const categorizedTutorials = getTutorialsByCategory();

  if (tutorials.length === 0) {
    return (
      <Card>
        <Empty 
          description="No tutorials available"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <div>
      {/* Overall Statistics */}
      <ProgressCard title="Your Learning Progress">
        <StatsContainer>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="Overall Progress"
                value={overallProgress.percentage}
                precision={1}
                suffix="%"
                prefix={<TrophyOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Completed"
                value={overallProgress.completed}
                suffix={`/ ${overallProgress.total}`}
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Time Spent"
                value={statistics?.totalTimeSpent || 0}
                formatter={(value) => formatDuration(value)}
                prefix={<ClockCircleOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Badges Earned"
                value={badges.length}
                prefix={<StarOutlined />}
              />
            </Col>
          </Row>
        </StatsContainer>

        <Progress 
          percent={overallProgress.percentage} 
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
          showInfo={false}
        />
      </ProgressCard>

      {/* Category Progress */}
      <ProgressCard title="Progress by Category">
        <Row gutter={16}>
          {Object.entries(categorizedTutorials).map(([category, categoryTutorials]) => {
            const progress = getCategoryProgress(category);
            return (
              <Col span={12} key={category}>
                <Card size="small" style={{ marginBottom: 16 }}>
                  <Space align="center">
                    <CategoryIcon category={category}>
                      {getCategoryIcon(category)}
                    </CategoryIcon>
                    <div style={{ flex: 1 }}>
                      <Text strong style={{ textTransform: 'capitalize' }}>
                        {category.replace('_', ' ')}
                      </Text>
                      <br />
                      <Text type="secondary">
                        {progress.completed} / {progress.total} completed
                      </Text>
                      <Progress 
                        percent={progress.percentage} 
                        size="small" 
                        showInfo={false}
                        strokeColor={
                          category === TUTORIAL_CATEGORIES.BEGINNER ? '#52c41a' :
                          category === TUTORIAL_CATEGORIES.INTERMEDIATE ? '#1890ff' :
                          category === TUTORIAL_CATEGORIES.ADVANCED ? '#f5222d' : '#722ed1'
                        }
                      />
                    </div>
                  </Space>
                </Card>
              </Col>
            );
          })}
        </Row>
      </ProgressCard>

      {/* Tutorial Timeline */}
      <ProgressCard title="Tutorial Journey">
        <Timeline>
          {tutorials.map(tutorial => {
            const progress = getTutorialProgress(tutorial.id);
            const isCompleted = progress?.status === TUTORIAL_STATUS.COMPLETED;
            const isInProgress = progress?.status === TUTORIAL_STATUS.IN_PROGRESS;
            const isSkipped = progress?.status === TUTORIAL_STATUS.SKIPPED;
            
            return (
              <Timeline.Item
                key={tutorial.id}
                dot={
                  isCompleted ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : isInProgress ? (
                    <PlayCircleOutlined style={{ color: '#1890ff' }} />
                  ) : (
                    <Avatar size="small" icon={getCategoryIcon(tutorial.category)} />
                  )
                }
                color={isCompleted ? 'green' : isInProgress ? 'blue' : 'gray'}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text strong>{tutorial.title}</Text>
                    <br />
                    <Text type="secondary">{tutorial.description}</Text>
                    <br />
                    <Space size="small">
                      <Badge 
                        color={
                          tutorial.category === TUTORIAL_CATEGORIES.BEGINNER ? 'green' :
                          tutorial.category === TUTORIAL_CATEGORIES.INTERMEDIATE ? 'blue' :
                          tutorial.category === TUTORIAL_CATEGORIES.ADVANCED ? 'red' : 'purple'
                        }
                        text={tutorial.category.replace('_', ' ')}
                      />
                      <Text type="secondary">
                        {getDifficultyStars(tutorial.difficulty)}
                      </Text>
                      <Text type="secondary">
                        ~{tutorial.estimatedDuration}min
                      </Text>
                    </Space>
                    {isSkipped && (
                      <div>
                        <Text type="warning">Skipped</Text>
                      </div>
                    )}
                  </div>
                  <div>
                    {!isCompleted && !isSkipped && (
                      <Button
                        type={isInProgress ? "default" : "primary"}
                        size="small"
                        onClick={() => startTutorial(tutorial.id)}
                      >
                        {isInProgress ? 'Continue' : 'Start'}
                      </Button>
                    )}
                  </div>
                </div>
              </Timeline.Item>
            );
          })}
        </Timeline>
      </ProgressCard>

      {/* Badges */}
      {badges.length > 0 && (
        <ProgressCard title="Achievement Badges">
          <BadgeContainer>
            {badges.map((badge, index) => (
              <Tooltip 
                key={index}
                title={`Earned: ${new Date(badge.earnedAt).toLocaleDateString()}`}
              >
                <BadgeItem earned={true}>
                  <TrophyOutlined style={{ fontSize: 24, color: '#faad14' }} />
                  <Text style={{ fontSize: 12, textAlign: 'center' }}>
                    {badge.title}
                  </Text>
                  <Text type="secondary" style={{ fontSize: 10 }}>
                    {badge.category}
                  </Text>
                </BadgeItem>
              </Tooltip>
            ))}
          </BadgeContainer>
        </ProgressCard>
      )}
    </div>
  );
};

export default TutorialProgress;
