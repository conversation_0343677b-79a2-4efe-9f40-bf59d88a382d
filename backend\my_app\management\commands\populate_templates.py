"""
Management command to populate the database with sample templates.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from my_app.models import ComponentTemplate, LayoutTemplate, AppTemplate
import json


class Command(BaseCommand):
    help = 'Populate the database with sample templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing templates before populating',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing templates...')
            ComponentTemplate.objects.all().delete()
            LayoutTemplate.objects.all().delete()
            AppTemplate.objects.all().delete()

        # Get or create a system user for templates
        system_user, created = User.objects.get_or_create(
            username='system',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'User',
                'is_staff': True
            }
        )

        self.stdout.write('Creating component templates...')
        self.create_component_templates(system_user)

        self.stdout.write('Creating layout templates...')
        self.create_layout_templates(system_user)

        self.stdout.write('Creating app templates...')
        self.create_app_templates(system_user)

        self.stdout.write(
            self.style.SUCCESS('Successfully populated templates!')
        )

    def create_component_templates(self, user):
        """Create sample component templates"""
        component_templates = [
            {
                'name': 'Primary Button',
                'description': 'A primary action button with blue background',
                'component_type': 'button',
                'default_props': {
                    'text': 'Click Me',
                    'type': 'primary',
                    'size': 'medium',
                    'disabled': False
                },
                'is_public': True
            },
            {
                'name': 'Success Button',
                'description': 'A success button with green background',
                'component_type': 'button',
                'default_props': {
                    'text': 'Success',
                    'type': 'success',
                    'size': 'medium',
                    'icon': 'check'
                },
                'is_public': True
            },
            {
                'name': 'Search Input',
                'description': 'An input field with search icon',
                'component_type': 'input',
                'default_props': {
                    'placeholder': 'Search...',
                    'prefix': 'search',
                    'allowClear': True,
                    'size': 'medium'
                },
                'is_public': True
            },
            {
                'name': 'User Profile Card',
                'description': 'A card component for displaying user profiles',
                'component_type': 'card',
                'default_props': {
                    'title': 'User Profile',
                    'bordered': True,
                    'hoverable': True,
                    'size': 'default'
                },
                'is_public': True
            },
            {
                'name': 'Data Table',
                'description': 'A table component for displaying data with pagination',
                'component_type': 'table',
                'default_props': {
                    'bordered': True,
                    'pagination': {'pageSize': 10},
                    'size': 'middle',
                    'scroll': {'x': 800}
                },
                'is_public': True
            }
        ]

        for template_data in component_templates:
            ComponentTemplate.objects.create(
                name=template_data['name'],
                description=template_data['description'],
                component_type=template_data['component_type'],
                default_props=json.dumps(template_data['default_props']),
                is_public=template_data['is_public'],
                user=user
            )

    def create_layout_templates(self, user):
        """Create sample layout templates"""
        layout_templates = [
            {
                'name': 'Two Column Layout',
                'description': 'A responsive two-column layout with sidebar',
                'layout_type': 'sidebar',
                'components': {
                    'structure': 'two-column',
                    'sidebar': {
                        'width': '25%',
                        'position': 'left',
                        'collapsible': True
                    },
                    'main': {
                        'width': '75%',
                        'padding': '24px'
                    }
                },
                'default_props': {
                    'responsive': True,
                    'theme': 'light'
                },
                'is_public': True
            },
            {
                'name': 'Dashboard Grid',
                'description': 'A grid layout perfect for dashboards',
                'layout_type': 'grid',
                'components': {
                    'structure': 'grid',
                    'columns': 12,
                    'gutter': [16, 16],
                    'areas': [
                        {'span': 24, 'type': 'header'},
                        {'span': 6, 'type': 'sidebar'},
                        {'span': 18, 'type': 'content'},
                        {'span': 24, 'type': 'footer'}
                    ]
                },
                'default_props': {
                    'responsive': True,
                    'minHeight': '100vh'
                },
                'is_public': True
            },
            {
                'name': 'Landing Page Hero',
                'description': 'A hero section layout for landing pages',
                'layout_type': 'landing',
                'components': {
                    'structure': 'hero',
                    'sections': [
                        {'type': 'hero', 'height': '100vh'},
                        {'type': 'features', 'columns': 3},
                        {'type': 'testimonials', 'carousel': True},
                        {'type': 'cta', 'centered': True}
                    ]
                },
                'default_props': {
                    'fullWidth': True,
                    'backgroundImage': True
                },
                'is_public': True
            }
        ]

        for template_data in layout_templates:
            LayoutTemplate.objects.create(
                name=template_data['name'],
                description=template_data['description'],
                layout_type=template_data['layout_type'],
                components=template_data['components'],
                default_props=template_data['default_props'],
                is_public=template_data['is_public'],
                user=user
            )

    def create_app_templates(self, user):
        """Create sample app templates"""
        app_templates = [
            {
                'name': 'E-commerce Store',
                'description': 'A complete e-commerce application with product catalog, cart, and checkout',
                'app_category': 'ecommerce',
                'components': {
                    'pages': [
                        {'name': 'home', 'components': ['hero', 'featured-products', 'categories']},
                        {'name': 'products', 'components': ['product-grid', 'filters', 'pagination']},
                        {'name': 'product-detail', 'components': ['product-images', 'product-info', 'reviews']},
                        {'name': 'cart', 'components': ['cart-items', 'summary', 'checkout-button']},
                        {'name': 'checkout', 'components': ['shipping-form', 'payment-form', 'order-summary']}
                    ],
                    'components': {
                        'header': {'type': 'navigation', 'items': ['logo', 'menu', 'cart-icon', 'user-menu']},
                        'footer': {'type': 'footer', 'sections': ['links', 'social', 'newsletter']}
                    }
                },
                'default_props': {
                    'theme': 'modern',
                    'primaryColor': '#1890ff',
                    'currency': 'USD'
                },
                'required_components': ['product-card', 'cart', 'payment-form', 'navigation'],
                'preview_image': 'https://via.placeholder.com/400x300/1890ff/white?text=E-commerce',
                'is_public': True
            },
            {
                'name': 'Business Dashboard',
                'description': 'A comprehensive business dashboard with analytics and reporting',
                'app_category': 'dashboard',
                'components': {
                    'pages': [
                        {'name': 'overview', 'components': ['kpi-cards', 'charts', 'recent-activity']},
                        {'name': 'analytics', 'components': ['advanced-charts', 'filters', 'export-tools']},
                        {'name': 'reports', 'components': ['report-builder', 'saved-reports', 'schedule']},
                        {'name': 'settings', 'components': ['user-settings', 'app-config', 'integrations']}
                    ],
                    'layout': {
                        'type': 'sidebar',
                        'navigation': ['overview', 'analytics', 'reports', 'settings'],
                        'header': ['breadcrumb', 'user-menu', 'notifications']
                    }
                },
                'default_props': {
                    'theme': 'professional',
                    'sidebar': 'collapsible',
                    'charts': 'interactive'
                },
                'required_components': ['chart', 'table', 'card', 'sidebar', 'breadcrumb'],
                'preview_image': 'https://via.placeholder.com/400x300/52c41a/white?text=Dashboard',
                'is_public': True
            },
            {
                'name': 'Portfolio Website',
                'description': 'A modern portfolio website for showcasing work and skills',
                'app_category': 'portfolio',
                'components': {
                    'pages': [
                        {'name': 'home', 'components': ['hero-section', 'about-preview', 'featured-work']},
                        {'name': 'about', 'components': ['bio', 'skills', 'experience', 'education']},
                        {'name': 'portfolio', 'components': ['project-grid', 'filters', 'project-modal']},
                        {'name': 'contact', 'components': ['contact-form', 'social-links', 'location']}
                    ],
                    'design': {
                        'style': 'modern',
                        'animations': True,
                        'responsive': True
                    }
                },
                'default_props': {
                    'theme': 'creative',
                    'animations': 'smooth',
                    'typography': 'modern'
                },
                'required_components': ['hero', 'project-card', 'contact-form', 'image-gallery'],
                'preview_image': 'https://via.placeholder.com/400x300/722ed1/white?text=Portfolio',
                'is_public': True
            }
        ]

        for template_data in app_templates:
            AppTemplate.objects.create(
                name=template_data['name'],
                description=template_data['description'],
                app_category=template_data['app_category'],
                components=template_data['components'],
                default_props=template_data['default_props'],
                required_components=template_data['required_components'],
                preview_image=template_data['preview_image'],
                is_public=template_data['is_public'],
                user=user
            )
