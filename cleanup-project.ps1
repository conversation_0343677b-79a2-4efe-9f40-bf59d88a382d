# Project Cleanup Script
# This script removes duplicate and redundant files to organize the codebase

Write-Host "Starting project cleanup..." -ForegroundColor Green

# Remove duplicate src directory at root level
if (Test-Path "src") {
    Write-Host "Removing duplicate root src directory..." -ForegroundColor Yellow
    Remove-Item -Path "src" -Recurse -Force
    Write-Host "✓ Removed root src directory" -ForegroundColor Green
}

# Remove duplicate node_modules at root level (keep frontend/node_modules)
if (Test-Path "node_modules") {
    Write-Host "Removing duplicate root node_modules..." -ForegroundColor Yellow
    Remove-Item -Path "node_modules" -Recurse -Force
    Write-Host "✓ Removed root node_modules directory" -ForegroundColor Green
}

# Remove redundant files at root level
$redundantFiles = @(
    "app.js",
    "store.js",
    "manage.py",
    "views.py",
    "requirements.txt",
    "pytest.ini"
)

foreach ($file in $redundantFiles) {
    if (Test-Path $file) {
        Write-Host "Removing redundant file: $file" -ForegroundColor Yellow
        Remove-Item -Path $file -Force
        Write-Host "✓ Removed $file" -ForegroundColor Green
    }
}

# Remove test and temporary directories at root level
$redundantDirs = @(
    "tests",
    "__tests__",
    "__pycache__",
    "myenv",
    "venv",
    "public",
    "websocket-test-app",
    "New folder*",
    "claude",
    "folder",
    "path"
)

foreach ($dir in $redundantDirs) {
    if (Test-Path $dir) {
        Write-Host "Removing redundant directory: $dir" -ForegroundColor Yellow
        Remove-Item -Path $dir -Recurse -Force
        Write-Host "✓ Removed $dir" -ForegroundColor Green
    }
}

# Remove test HTML files and other temporary files
$testFiles = Get-ChildItem -Path "." -Filter "*.html" | Where-Object { $_.Name -like "*test*" -or $_.Name -like "*websocket*" -or $_.Name -like "*performance*" }
foreach ($file in $testFiles) {
    Write-Host "Removing test file: $($file.Name)" -ForegroundColor Yellow
    Remove-Item -Path $file.FullName -Force
    Write-Host "✓ Removed $($file.Name)" -ForegroundColor Green
}

# Remove duplicate Python files
$pythonFiles = @(
    "*.py"
)

Get-ChildItem -Path "." -Filter "*.py" | Where-Object { $_.Name -notlike "*setup*" -and $_.Name -notlike "*start*" } | ForEach-Object {
    Write-Host "Removing Python file: $($_.Name)" -ForegroundColor Yellow
    Remove-Item -Path $_.FullName -Force
    Write-Host "✓ Removed $($_.Name)" -ForegroundColor Green
}

# Remove duplicate JavaScript files at root
$jsFiles = Get-ChildItem -Path "." -Filter "*.js" | Where-Object { $_.Name -notlike "*start*" -and $_.Name -notlike "*fix*" }
foreach ($file in $jsFiles) {
    Write-Host "Removing JS file: $($file.Name)" -ForegroundColor Yellow
    Remove-Item -Path $file.FullName -Force
    Write-Host "✓ Removed $($file.Name)" -ForegroundColor Green
}

# Clean up misc files
$miscFiles = @(
    "*.tar.gz",
    "*.vhdx",
    "*.txt",
    "*.yaml",
    "*.jsonc",
    "*.diff",
    "*.conf",
    "*.css",
    "desktop.ini"
)

foreach ($pattern in $miscFiles) {
    Get-ChildItem -Path "." -Filter $pattern | Where-Object {
        $_.Name -ne "README.md" -and
        $_.Name -ne "LICENSE" -and
        $_.Name -ne "DEVELOPMENT.md" -and
        $_.Name -notlike "*docker-compose*"
    } | ForEach-Object {
        Write-Host "Removing misc file: $($_.Name)" -ForegroundColor Yellow
        Remove-Item -Path $_.FullName -Force
        Write-Host "✓ Removed $($_.Name)" -ForegroundColor Green
    }
}

Write-Host "Project cleanup completed!" -ForegroundColor Green
Write-Host "Remaining structure:" -ForegroundColor Cyan
Get-ChildItem -Path "." -Directory | Select-Object Name | Format-Table -AutoSize
