<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Solution</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #cccccc; cursor: not-allowed; }
        input, select {
            padding: 8px;
            margin-bottom: 10px;
            width: 100%;
            box-sizing: border-box;
        }
        #log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            font-family: monospace;
            background-color: #f5f5f5;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .timestamp {
            color: #666;
            font-size: 0.8em;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .controls button {
            flex-grow: 1;
        }
    </style>
</head>
<body>
    <h1>WebSocket Solution</h1>
    
    <div class="card">
        <h2>Connection Settings</h2>
        <div>
            <label for="ws-url">WebSocket URL:</label>
            <select id="ws-url">
                <option value="ws://localhost:8765">Python Server (ws://localhost:8765)</option>
                <option value="ws://localhost:8000/ws/">Django Backend (ws://localhost:8000/ws/)</option>
                <option value="ws://localhost:8000/ws/test/">Django Test (ws://localhost:8000/ws/test/)</option>
                <option value="ws://localhost:8000/ws/echo/">Django Echo (ws://localhost:8000/ws/echo/)</option>
                <option value="ws://localhost:8000/ws/simple/">Django Simple (ws://localhost:8000/ws/simple/)</option>
            </select>
        </div>
        <div class="controls">
            <button id="connect-btn">Connect</button>
            <button id="disconnect-btn" disabled>Disconnect</button>
            <button id="clear-log-btn">Clear Log</button>
        </div>
        <div id="connection-status" class="warning">Not connected</div>
    </div>
    
    <div class="card">
        <h2>Debug Log</h2>
        <div id="log"></div>
        <div class="controls">
            <button id="ping-btn" disabled>Send Ping</button>
            <button id="echo-btn" disabled>Send Echo</button>
            <button id="json-btn" disabled>Send JSON</button>
        </div>
    </div>
    
    <div class="card">
        <h2>Connection Troubleshooting</h2>
        <p>If you're experiencing "invalid frame header" errors, try these solutions:</p>
        <ol>
            <li>Use the Python WebSocket server (ws://localhost:8765) to verify your browser supports WebSockets</li>
            <li>Check if there's a proxy or firewall blocking WebSocket connections</li>
            <li>Ensure the backend server is properly configured to handle WebSocket upgrade requests</li>
            <li>Try using a different browser or network connection</li>
        </ol>
        <div class="controls">
            <button id="test-fetch-btn">Test HTTP Fetch</button>
            <button id="test-headers-btn">Test Headers</button>
        </div>
        <div id="test-result"></div>
    </div>
    
    <script>
        // DOM Elements
        const wsUrlSelect = document.getElementById('ws-url');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const clearLogBtn = document.getElementById('clear-log-btn');
        const connectionStatus = document.getElementById('connection-status');
        const logDiv = document.getElementById('log');
        const pingBtn = document.getElementById('ping-btn');
        const echoBtn = document.getElementById('echo-btn');
        const jsonBtn = document.getElementById('json-btn');
        const testFetchBtn = document.getElementById('test-fetch-btn');
        const testHeadersBtn = document.getElementById('test-headers-btn');
        const testResult = document.getElementById('test-result');
        
        // WebSocket instance
        let socket = null;
        
        // Log a message to the debug log
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toISOString();
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Connect to WebSocket
        function connect() {
            try {
                const url = wsUrlSelect.value;
                
                log(`Connecting to ${url}...`);
                
                // Create WebSocket connection
                socket = new WebSocket(url);
                
                // Connection opened
                socket.onopen = function(event) {
                    log('Connection established', 'success');
                    connectionStatus.textContent = 'Connected';
                    connectionStatus.className = 'success';
                    
                    // Update button states
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    pingBtn.disabled = false;
                    echoBtn.disabled = false;
                    jsonBtn.disabled = false;
                };
                
                // Listen for messages
                socket.onmessage = function(event) {
                    try {
                        // Try to parse as JSON
                        const data = JSON.parse(event.data);
                        log(`Received: <pre>${JSON.stringify(data, null, 2)}</pre>`);
                    } catch (e) {
                        // Not JSON, display as text
                        log(`Received: ${event.data}`);
                    }
                };
                
                // Connection closed
                socket.onclose = function(event) {
                    const reason = event.reason ? ` (${event.reason})` : '';
                    log(`Connection closed with code ${event.code}${reason}`, 'warning');
                    connectionStatus.textContent = 'Disconnected';
                    connectionStatus.className = 'error';
                    
                    // Update button states
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    echoBtn.disabled = true;
                    jsonBtn.disabled = true;
                    
                    socket = null;
                };
                
                // Connection error
                socket.onerror = function(event) {
                    log('WebSocket error occurred', 'error');
                    console.error('WebSocket error:', event);
                };
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }
        
        // Disconnect from WebSocket
        function disconnect() {
            if (socket) {
                socket.close(1000, 'User initiated disconnect');
            }
        }
        
        // Send a ping message
        function sendPing() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const ping = JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
                
                try {
                    socket.send(ping);
                    log(`Sent ping: ${ping}`, 'success');
                } catch (error) {
                    log(`Error sending ping: ${error.message}`, 'error');
                }
            } else {
                log('Cannot send ping: WebSocket is not connected', 'error');
            }
        }
        
        // Send an echo message
        function sendEcho() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const echo = JSON.stringify({
                    type: 'echo',
                    message: 'Hello, WebSocket!',
                    timestamp: new Date().toISOString()
                });
                
                try {
                    socket.send(echo);
                    log(`Sent echo: ${echo}`, 'success');
                } catch (error) {
                    log(`Error sending echo: ${error.message}`, 'error');
                }
            } else {
                log('Cannot send echo: WebSocket is not connected', 'error');
            }
        }
        
        // Send a JSON message
        function sendJson() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const json = JSON.stringify({
                    type: 'json_test',
                    data: {
                        number: 123,
                        string: 'test',
                        boolean: true,
                        array: [1, 2, 3],
                        object: { a: 1, b: 2 }
                    },
                    timestamp: new Date().toISOString()
                });
                
                try {
                    socket.send(json);
                    log(`Sent JSON: ${json}`, 'success');
                } catch (error) {
                    log(`Error sending JSON: ${error.message}`, 'error');
                }
            } else {
                log('Cannot send JSON: WebSocket is not connected', 'error');
            }
        }
        
        // Test HTTP fetch
        function testFetch() {
            const url = wsUrlSelect.value.replace('ws://', 'http://').replace('wss://', 'https://');
            
            testResult.innerHTML = `Testing HTTP fetch to ${url}...`;
            
            fetch(url)
                .then(response => {
                    testResult.innerHTML = `
                        <div class="success">HTTP fetch successful!</div>
                        <div>Status: ${response.status} ${response.statusText}</div>
                        <div>Headers: ${JSON.stringify(Object.fromEntries([...response.headers]), null, 2)}</div>
                    `;
                    return response.text();
                })
                .then(text => {
                    testResult.innerHTML += `<pre>${text.substring(0, 500)}${text.length > 500 ? '...' : ''}</pre>`;
                })
                .catch(error => {
                    testResult.innerHTML = `<div class="error">HTTP fetch failed: ${error.message}</div>`;
                });
        }
        
        // Test headers
        function testHeaders() {
            const url = wsUrlSelect.value.replace('ws://', 'http://').replace('wss://', 'https://');
            
            testResult.innerHTML = `Testing headers for ${url}...`;
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'Upgrade': 'websocket',
                    'Connection': 'Upgrade',
                    'Sec-WebSocket-Key': 'dGhlIHNhbXBsZSBub25jZQ==',
                    'Sec-WebSocket-Version': '13'
                }
            })
                .then(response => {
                    testResult.innerHTML = `
                        <div class="${response.status === 101 ? 'success' : 'warning'}">
                            Status: ${response.status} ${response.statusText}
                        </div>
                        <div>Headers: ${JSON.stringify(Object.fromEntries([...response.headers]), null, 2)}</div>
                    `;
                    return response.text();
                })
                .then(text => {
                    testResult.innerHTML += `<pre>${text.substring(0, 500)}${text.length > 500 ? '...' : ''}</pre>`;
                })
                .catch(error => {
                    testResult.innerHTML = `<div class="error">Header test failed: ${error.message}</div>`;
                });
        }
        
        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearLogBtn.addEventListener('click', () => {
            logDiv.innerHTML = '';
            log('Log cleared');
        });
        pingBtn.addEventListener('click', sendPing);
        echoBtn.addEventListener('click', sendEcho);
        jsonBtn.addEventListener('click', sendJson);
        testFetchBtn.addEventListener('click', testFetch);
        testHeadersBtn.addEventListener('click', testHeaders);
        
        // Initial log
        log('WebSocket Solution loaded. Select a server and click "Connect" to start.');
    </script>
</body>
</html>
