# Generated by Django 5.1.6 on 2025-06-20 11:21

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my_app', '0004_add_template_models'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='app',
            name='allow_collaboration',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='app',
            name='collaboration_mode',
            field=models.CharField(choices=[('private', 'Private'), ('invite_only', 'Invite Only'), ('public', 'Public')], default='private', max_length=20),
        ),
        migrations.CreateModel(
            name='CollaborationSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(default='Collaboration Session', max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('max_participants', models.PositiveIntegerField(default=10)),
                ('allow_anonymous', models.BooleanField(default=False)),
                ('require_approval', models.BooleanField(default=False)),
                ('app', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collaboration_sessions', to='my_app.app')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField()),
                ('content_type', models.CharField(default='text', max_length=20)),
                ('component_id', models.CharField(blank=True, max_length=255, null=True)),
                ('canvas_position', models.JSONField(blank=True, default=dict)),
                ('context_data', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('open', 'Open'), ('resolved', 'Resolved'), ('archived', 'Archived')], default='open', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL)),
                ('mentioned_users', models.ManyToManyField(blank=True, related_name='mentioned_in_comments', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='my_app.comment')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_comments', to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='my_app.collaborationsession')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EditOperation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('operation_type', models.CharField(choices=[('component_add', 'Add Component'), ('component_update', 'Update Component'), ('component_delete', 'Delete Component'), ('component_move', 'Move Component'), ('layout_update', 'Update Layout'), ('style_update', 'Update Style'), ('data_update', 'Update Data')], max_length=20)),
                ('target_id', models.CharField(max_length=255)),
                ('operation_data', models.JSONField()),
                ('vector_clock', models.JSONField(default=dict)),
                ('dependencies', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('applied_at', models.DateTimeField(blank=True, null=True)),
                ('is_applied', models.BooleanField(default=False)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='edit_operations', to='my_app.collaborationsession')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='edit_operations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='SessionParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('owner', 'Owner'), ('editor', 'Editor'), ('viewer', 'Viewer'), ('commenter', 'Commenter')], default='editor', max_length=20)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('last_seen', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('cursor_position', models.JSONField(blank=True, default=dict)),
                ('selected_component_id', models.CharField(blank=True, max_length=255, null=True)),
                ('current_view', models.CharField(blank=True, max_length=100, null=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='my_app.collaborationsession')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collaboration_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_seen'],
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('join', 'Joined Session'), ('leave', 'Left Session'), ('edit', 'Made Edit'), ('comment', 'Added Comment'), ('cursor_move', 'Moved Cursor'), ('component_select', 'Selected Component'), ('view_change', 'Changed View')], max_length=20)),
                ('activity_data', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='my_app.collaborationsession')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collaboration_activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='collaborationsession',
            index=models.Index(fields=['app'], name='my_app_coll_app_id_604e62_idx'),
        ),
        migrations.AddIndex(
            model_name='collaborationsession',
            index=models.Index(fields=['created_by'], name='my_app_coll_created_71f80a_idx'),
        ),
        migrations.AddIndex(
            model_name='collaborationsession',
            index=models.Index(fields=['is_active'], name='my_app_coll_is_acti_9d545f_idx'),
        ),
        migrations.AddIndex(
            model_name='collaborationsession',
            index=models.Index(fields=['created_at'], name='my_app_coll_created_891aa5_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['session', 'status'], name='my_app_comm_session_26b024_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['author'], name='my_app_comm_author__854181_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['component_id'], name='my_app_comm_compone_e8e079_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['parent'], name='my_app_comm_parent__6cdea9_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['created_at'], name='my_app_comm_created_bc5307_idx'),
        ),
        migrations.AddIndex(
            model_name='editoperation',
            index=models.Index(fields=['session', 'created_at'], name='my_app_edit_session_cce6d5_idx'),
        ),
        migrations.AddIndex(
            model_name='editoperation',
            index=models.Index(fields=['user'], name='my_app_edit_user_id_4c496a_idx'),
        ),
        migrations.AddIndex(
            model_name='editoperation',
            index=models.Index(fields=['target_id'], name='my_app_edit_target__c18cd7_idx'),
        ),
        migrations.AddIndex(
            model_name='editoperation',
            index=models.Index(fields=['operation_type'], name='my_app_edit_operati_071f14_idx'),
        ),
        migrations.AddIndex(
            model_name='editoperation',
            index=models.Index(fields=['is_applied'], name='my_app_edit_is_appl_f5f4e3_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionparticipant',
            index=models.Index(fields=['session', 'is_active'], name='my_app_sess_session_ba5249_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionparticipant',
            index=models.Index(fields=['user'], name='my_app_sess_user_id_9dfd50_idx'),
        ),
        migrations.AddIndex(
            model_name='sessionparticipant',
            index=models.Index(fields=['last_seen'], name='my_app_sess_last_se_1fb59e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='sessionparticipant',
            unique_together={('session', 'user')},
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['session', 'timestamp'], name='my_app_user_session_65aa17_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['user', 'timestamp'], name='my_app_user_user_id_65a1c5_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['activity_type'], name='my_app_user_activit_7a93b4_idx'),
        ),
    ]
