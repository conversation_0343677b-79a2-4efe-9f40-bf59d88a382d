/**
 * CSRF Testing Utility
 * 
 * This utility provides functions to test CSRF token functionality
 * and verify that API requests are working correctly.
 */

import csrfService from '../services/csrfService';
import { API_ENDPOINTS } from '../config/api';

/**
 * Test CSRF token retrieval
 * @returns {Promise<Object>} Test result
 */
export async function testCSRFTokenRetrieval() {
  console.log('Testing CSRF token retrieval...');
  
  try {
    const token = await csrfService.getToken();
    
    if (token && typeof token === 'string' && token.length > 0) {
      console.log('✅ CSRF token retrieved successfully:', token.substring(0, 10) + '...');
      return {
        success: true,
        message: 'CSRF token retrieved successfully',
        token: token.substring(0, 10) + '...'
      };
    } else {
      console.error('❌ CSRF token is invalid:', token);
      return {
        success: false,
        message: 'CSRF token is invalid',
        token
      };
    }
  } catch (error) {
    console.error('❌ Error retrieving CSRF token:', error);
    return {
      success: false,
      message: 'Error retrieving CSRF token',
      error: error.message
    };
  }
}

/**
 * Test CSRF token in API request
 * @returns {Promise<Object>} Test result
 */
export async function testCSRFInAPIRequest() {
  console.log('Testing CSRF token in API request...');
  
  try {
    // Test with a simple POST request to a safe endpoint
    const response = await csrfService.request(API_ENDPOINTS.STATUS[0], {
      method: 'POST',
      body: JSON.stringify({ test: true }),
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API request with CSRF token successful:', data);
      return {
        success: true,
        message: 'API request with CSRF token successful',
        data
      };
    } else {
      console.error('❌ API request failed:', response.status, response.statusText);
      return {
        success: false,
        message: `API request failed: ${response.status} ${response.statusText}`,
        status: response.status
      };
    }
  } catch (error) {
    console.error('❌ Error making API request with CSRF token:', error);
    return {
      success: false,
      message: 'Error making API request with CSRF token',
      error: error.message
    };
  }
}

/**
 * Test CSRF cookie presence
 * @returns {Object} Test result
 */
export function testCSRFCookie() {
  console.log('Testing CSRF cookie presence...');
  
  try {
    const token = csrfService.getTokenFromCookie();
    
    if (token && typeof token === 'string' && token.length > 0) {
      console.log('✅ CSRF cookie found:', token.substring(0, 10) + '...');
      return {
        success: true,
        message: 'CSRF cookie found',
        token: token.substring(0, 10) + '...'
      };
    } else {
      console.warn('⚠️ CSRF cookie not found or invalid');
      return {
        success: false,
        message: 'CSRF cookie not found or invalid',
        token
      };
    }
  } catch (error) {
    console.error('❌ Error checking CSRF cookie:', error);
    return {
      success: false,
      message: 'Error checking CSRF cookie',
      error: error.message
    };
  }
}

/**
 * Run all CSRF tests
 * @returns {Promise<Object>} Combined test results
 */
export async function runAllCSRFTests() {
  console.log('🧪 Running all CSRF tests...');
  
  const results = {
    cookieTest: testCSRFCookie(),
    tokenRetrievalTest: await testCSRFTokenRetrieval(),
    apiRequestTest: await testCSRFInAPIRequest()
  };
  
  const allPassed = Object.values(results).every(result => result.success);
  
  console.log('📊 CSRF Test Results:');
  console.log('Cookie Test:', results.cookieTest.success ? '✅' : '❌', results.cookieTest.message);
  console.log('Token Retrieval Test:', results.tokenRetrievalTest.success ? '✅' : '❌', results.tokenRetrievalTest.message);
  console.log('API Request Test:', results.apiRequestTest.success ? '✅' : '❌', results.apiRequestTest.message);
  console.log('Overall:', allPassed ? '✅ All tests passed' : '❌ Some tests failed');
  
  return {
    success: allPassed,
    results,
    summary: {
      total: 3,
      passed: Object.values(results).filter(result => result.success).length,
      failed: Object.values(results).filter(result => !result.success).length
    }
  };
}

/**
 * Display CSRF test results in the UI
 * @param {Object} results - Test results from runAllCSRFTests
 */
export function displayCSRFTestResults(results) {
  // Create a test results element
  const existingResults = document.getElementById('csrf-test-results');
  if (existingResults) {
    existingResults.remove();
  }
  
  const resultsElement = document.createElement('div');
  resultsElement.id = 'csrf-test-results';
  resultsElement.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    font-family: monospace;
    font-size: 12px;
    max-width: 400px;
    max-height: 300px;
    overflow-y: auto;
  `;
  
  const title = document.createElement('h3');
  title.textContent = 'CSRF Test Results';
  title.style.cssText = 'margin: 0 0 12px 0; color: #333;';
  resultsElement.appendChild(title);
  
  // Add summary
  const summary = document.createElement('div');
  summary.style.cssText = `
    background: ${results.success ? '#f6ffed' : '#fff2f0'};
    border: 1px solid ${results.success ? '#b7eb8f' : '#ffccc7'};
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 12px;
  `;
  summary.innerHTML = `
    <strong>Summary:</strong> ${results.summary.passed}/${results.summary.total} tests passed<br>
    <strong>Status:</strong> ${results.success ? '✅ All tests passed' : '❌ Some tests failed'}
  `;
  resultsElement.appendChild(summary);
  
  // Add individual test results
  Object.entries(results.results).forEach(([testName, result]) => {
    const testElement = document.createElement('div');
    testElement.style.cssText = 'margin-bottom: 8px; padding: 4px; border-left: 3px solid ' + (result.success ? '#52c41a' : '#f5222d');
    testElement.innerHTML = `
      <strong>${testName}:</strong> ${result.success ? '✅' : '❌'}<br>
      <span style="color: #666;">${result.message}</span>
    `;
    resultsElement.appendChild(testElement);
  });
  
  // Add close button
  const closeButton = document.createElement('button');
  closeButton.textContent = 'Close';
  closeButton.style.cssText = `
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    margin-top: 8px;
  `;
  closeButton.onclick = () => resultsElement.remove();
  resultsElement.appendChild(closeButton);
  
  document.body.appendChild(resultsElement);
  
  // Auto-remove after 30 seconds
  setTimeout(() => {
    if (resultsElement.parentNode) {
      resultsElement.remove();
    }
  }, 30000);
}

// Export for global access in development
if (process.env.NODE_ENV === 'development') {
  window.testCSRF = {
    testCSRFTokenRetrieval,
    testCSRFInAPIRequest,
    testCSRFCookie,
    runAllCSRFTests,
    displayCSRFTestResults
  };
}

export default {
  testCSRFTokenRetrieval,
  testCSRFInAPIRequest,
  testCSRFCookie,
  runAllCSRFTests,
  displayCSRFTestResults
};
