"""
Unit tests for Django REST Framework serializers in the App Builder application.
Tests serialization, deserialization, validation, and field handling.
"""

import pytest
import json
from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.exceptions import ValidationError
from model_bakery import baker

from my_app.models import (
    App, AppVersion, ComponentTemplate, LayoutTemplate, AppTemplate
)
from my_app.serializers import (
    AppSerializer, AppVersionSerializer, ComponentTemplateSerializer,
    LayoutTemplateSerializer, AppTemplateSerializer
)


@pytest.mark.django_db
class TestAppSerializer:
    """Test cases for the AppSerializer."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_app_serialization(self):
        """Test serializing an App instance."""
        app_data = {
            'components': [{'id': '1', 'type': 'button', 'props': {'text': 'Click me'}}],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        app = App.objects.create(
            name='Test App',
            description='A test application',
            user=self.user,
            app_data=json.dumps(app_data),
            is_public=True
        )
        
        serializer = AppSerializer(app)
        data = serializer.data
        
        assert data['name'] == 'Test App'
        assert data['description'] == 'A test application'
        assert data['user'] == self.user.id
        assert data['is_public'] is True
        assert data['app_data_json'] == app_data
        assert 'created_at' in data
        assert 'updated_at' in data

    def test_app_deserialization_valid_data(self):
        """Test deserializing valid app data."""
        app_data = {
            'components': [{'id': '1', 'type': 'input'}],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        data = {
            'name': 'New App',
            'description': 'A new application',
            'user': self.user.id,
            'app_data': json.dumps(app_data),
            'is_public': False
        }
        
        serializer = AppSerializer(data=data)
        assert serializer.is_valid()
        
        app = serializer.save()
        assert app.name == 'New App'
        assert app.description == 'A new application'
        assert app.user == self.user
        assert app.get_app_data_json() == app_data
        assert app.is_public is False

    def test_app_deserialization_invalid_json(self):
        """Test deserializing app data with invalid JSON."""
        data = {
            'name': 'Invalid App',
            'user': self.user.id,
            'app_data': 'invalid json string'
        }
        
        serializer = AppSerializer(data=data)
        assert not serializer.is_valid()
        assert 'app_data' in serializer.errors
        assert 'Invalid JSON data' in str(serializer.errors['app_data'])

    def test_app_update_serialization(self):
        """Test updating an existing app."""
        app = baker.make(App, user=self.user, name='Original Name')
        
        data = {
            'name': 'Updated Name',
            'description': 'Updated description'
        }
        
        serializer = AppSerializer(app, data=data, partial=True)
        assert serializer.is_valid()
        
        updated_app = serializer.save()
        assert updated_app.name == 'Updated Name'
        assert updated_app.description == 'Updated description'

    def test_app_serializer_read_only_fields(self):
        """Test that read-only fields cannot be updated."""
        app = baker.make(App, user=self.user)
        original_created_at = app.created_at
        
        data = {
            'name': 'Updated Name',
            'created_at': '2020-01-01T00:00:00Z'  # Should be ignored
        }
        
        serializer = AppSerializer(app, data=data, partial=True)
        assert serializer.is_valid()
        
        updated_app = serializer.save()
        assert updated_app.created_at == original_created_at


@pytest.mark.django_db
class TestAppVersionSerializer:
    """Test cases for the AppVersionSerializer."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.app = baker.make(App, user=self.user)

    def test_app_version_serialization(self):
        """Test serializing an AppVersion instance."""
        app_data = {
            'components': [{'id': '1', 'type': 'button'}],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        version = AppVersion.objects.create(
            app=self.app,
            version_number=1,
            app_data=json.dumps(app_data),
            created_by=self.user,
            commit_message='Initial version'
        )
        
        serializer = AppVersionSerializer(version)
        data = serializer.data
        
        assert data['app'] == self.app.id
        assert data['version_number'] == 1
        assert data['app_data_json'] == app_data
        assert data['created_by'] == self.user.id
        assert data['commit_message'] == 'Initial version'
        assert 'created_at' in data

    def test_app_version_deserialization(self):
        """Test deserializing app version data."""
        app_data = {
            'components': [{'id': '1', 'type': 'input'}],
            'layouts': [],
            'styles': {},
            'data': {}
        }
        
        data = {
            'app': self.app.id,
            'app_data': json.dumps(app_data),
            'created_by': self.user.id,
            'commit_message': 'Test version'
        }
        
        serializer = AppVersionSerializer(data=data)
        assert serializer.is_valid()
        
        version = serializer.save()
        assert version.app == self.app
        assert version.get_app_data_json() == app_data
        assert version.created_by == self.user
        assert version.commit_message == 'Test version'

    def test_app_version_read_only_fields(self):
        """Test that read-only fields cannot be updated."""
        version = baker.make(AppVersion, app=self.app, version_number=1)
        
        data = {
            'version_number': 5,  # Should be ignored
            'commit_message': 'Updated message'
        }
        
        serializer = AppVersionSerializer(version, data=data, partial=True)
        assert serializer.is_valid()
        
        updated_version = serializer.save()
        assert updated_version.version_number == 1  # Unchanged
        assert updated_version.commit_message == 'Updated message'


@pytest.mark.django_db
class TestComponentTemplateSerializer:
    """Test cases for the ComponentTemplateSerializer."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_component_template_serialization(self):
        """Test serializing a ComponentTemplate instance."""
        default_props = {'text': 'Click me', 'variant': 'primary'}
        
        template = ComponentTemplate.objects.create(
            name='Primary Button',
            description='A primary button component',
            component_type='button',
            default_props=json.dumps(default_props),
            user=self.user,
            is_public=True
        )
        
        serializer = ComponentTemplateSerializer(template)
        data = serializer.data
        
        assert data['name'] == 'Primary Button'
        assert data['description'] == 'A primary button component'
        assert data['component_type'] == 'button'
        assert data['default_props_json'] == default_props
        assert data['user'] == self.user.id
        assert data['is_public'] is True

    def test_component_template_deserialization(self):
        """Test deserializing component template data."""
        default_props = {'placeholder': 'Enter text', 'type': 'text'}
        
        data = {
            'name': 'Text Input',
            'description': 'A text input component',
            'component_type': 'input',
            'default_props': json.dumps(default_props),
            'user': self.user.id,
            'is_public': False
        }
        
        serializer = ComponentTemplateSerializer(data=data)
        assert serializer.is_valid()
        
        template = serializer.save()
        assert template.name == 'Text Input'
        assert template.component_type == 'input'
        assert template.get_default_props_json() == default_props
        assert template.is_public is False

    def test_component_template_invalid_json(self):
        """Test validation with invalid JSON in default_props."""
        data = {
            'name': 'Invalid Template',
            'component_type': 'button',
            'default_props': 'invalid json',
            'user': self.user.id
        }
        
        serializer = ComponentTemplateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'default_props' in serializer.errors
        assert 'Invalid JSON data' in str(serializer.errors['default_props'])


@pytest.mark.django_db
class TestLayoutTemplateSerializer:
    """Test cases for the LayoutTemplateSerializer."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_layout_template_serialization(self):
        """Test serializing a LayoutTemplate instance."""
        components = {
            'header': {'type': 'header', 'props': {}},
            'footer': {'type': 'footer', 'props': {}}
        }
        default_props = {'theme': 'light', 'spacing': 'medium'}
        
        template = LayoutTemplate.objects.create(
            name='Standard Layout',
            description='A standard layout with header and footer',
            layout_type='business',
            components=components,
            default_props=default_props,
            user=self.user,
            is_public=True
        )
        
        serializer = LayoutTemplateSerializer(template)
        data = serializer.data
        
        assert data['name'] == 'Standard Layout'
        assert data['layout_type'] == 'business'
        assert data['components_json'] == components
        assert data['default_props_json'] == default_props
        assert data['is_public'] is True

    def test_layout_template_deserialization(self):
        """Test deserializing layout template data."""
        components = {'sidebar': {'type': 'sidebar'}}
        default_props = {'width': '200px'}
        
        data = {
            'name': 'Sidebar Layout',
            'description': 'Layout with sidebar',
            'layout_type': 'dashboard',
            'components': components,
            'default_props': default_props,
            'user': self.user.id
        }
        
        serializer = LayoutTemplateSerializer(data=data)
        assert serializer.is_valid()
        
        template = serializer.save()
        assert template.name == 'Sidebar Layout'
        assert template.layout_type == 'dashboard'
        assert template.get_components_json() == components
        assert template.get_default_props_json() == default_props

    def test_layout_template_invalid_components_json(self):
        """Test validation with invalid JSON in components."""
        data = {
            'name': 'Invalid Layout',
            'layout_type': 'business',
            'components': 'invalid json',
            'user': self.user.id
        }
        
        serializer = LayoutTemplateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'components' in serializer.errors

    def test_layout_template_invalid_default_props_json(self):
        """Test validation with invalid JSON in default_props."""
        data = {
            'name': 'Invalid Props Layout',
            'layout_type': 'business',
            'components': {},
            'default_props': 'invalid json',
            'user': self.user.id
        }
        
        serializer = LayoutTemplateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'default_props' in serializer.errors


@pytest.mark.django_db
class TestAppTemplateSerializer:
    """Test cases for the AppTemplateSerializer."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_app_template_serialization(self):
        """Test serializing an AppTemplate instance."""
        components = {
            'app': {
                'type': 'app',
                'children': [
                    {'type': 'header', 'props': {}},
                    {'type': 'main', 'props': {}}
                ]
            }
        }
        default_props = {'theme': 'modern', 'responsive': True}
        required_components = ['header', 'footer']
        
        template = AppTemplate.objects.create(
            name='Modern App',
            description='A modern app template',
            app_category='business',
            components=components,
            default_props=default_props,
            required_components=required_components,
            preview_image='https://example.com/preview.jpg',
            user=self.user,
            is_public=True
        )
        
        serializer = AppTemplateSerializer(template)
        data = serializer.data
        
        assert data['name'] == 'Modern App'
        assert data['app_category'] == 'business'
        assert data['components_json'] == components
        assert data['default_props_json'] == default_props
        assert data['required_components_list'] == required_components
        assert data['preview_image'] == 'https://example.com/preview.jpg'
        assert data['is_public'] is True

    def test_app_template_deserialization(self):
        """Test deserializing app template data."""
        components = {'app': {'type': 'app', 'children': []}}
        default_props = {'layout': 'grid'}
        required_components = ['navigation']
        
        data = {
            'name': 'Grid App',
            'description': 'App with grid layout',
            'app_category': 'dashboard',
            'components': components,
            'default_props': default_props,
            'required_components': required_components,
            'user': self.user.id
        }
        
        serializer = AppTemplateSerializer(data=data)
        assert serializer.is_valid()
        
        template = serializer.save()
        assert template.name == 'Grid App'
        assert template.app_category == 'dashboard'
        assert template.get_components_json() == components
        assert template.get_default_props_json() == default_props
        assert template.get_required_components_list() == required_components

    def test_app_template_invalid_required_components(self):
        """Test validation with invalid required_components."""
        data = {
            'name': 'Invalid Required Components',
            'app_category': 'business',
            'components': {},
            'required_components': 'not a list',
            'user': self.user.id
        }
        
        serializer = AppTemplateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'required_components' in serializer.errors

    def test_app_template_required_components_not_list(self):
        """Test validation when required_components JSON is not a list."""
        data = {
            'name': 'Invalid Required Components Type',
            'app_category': 'business',
            'components': {},
            'required_components': '{"not": "a list"}',
            'user': self.user.id
        }
        
        serializer = AppTemplateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'required_components' in serializer.errors
        assert 'must be a list' in str(serializer.errors['required_components'])
