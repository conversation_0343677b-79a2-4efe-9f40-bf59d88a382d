/**
 * Comprehensive Test Suite for Tutorial System
 * 
 * Tests all major components and functionality of the tutorial system
 * including storage, progress tracking, accessibility, and integration.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Tutorial System Components
import { TutorialProvider, useTutorial } from '../TutorialManager';
import TutorialOverlay from '../TutorialOverlay';
import TutorialProgress from '../TutorialProgress';
import ContextualHelp from '../ContextualHelp';
import TutorialTrigger from '../TutorialTrigger';
import tutorialStorage from '../TutorialStorage';
import { TUTORIAL_DEFINITIONS } from '../TutorialContent';
import {
  TUTORIAL_STATUS,
  TUTORIAL_CATEGORIES,
  createTutorial,
  createTutorialStep
} from '../types';

// Mock localStorage
const mockLocalStorage = {
  store: {},
  getItem: jest.fn((key) => mockLocalStorage.store[key] || null),
  setItem: jest.fn((key, value) => {
    mockLocalStorage.store[key] = value;
  }),
  removeItem: jest.fn((key) => {
    delete mockLocalStorage.store[key];
  }),
  clear: jest.fn(() => {
    mockLocalStorage.store = {};
  })
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Test Tutorial Data
const testTutorial = createTutorial({
  id: 'test_tutorial',
  title: 'Test Tutorial',
  description: 'A tutorial for testing',
  category: TUTORIAL_CATEGORIES.BEGINNER,
  difficulty: 1,
  estimatedDuration: 5,
  steps: [
    createTutorialStep({
      id: 'step1',
      title: 'Step 1',
      content: 'This is step 1',
      targetSelector: '[data-test="step1-target"]'
    }),
    createTutorialStep({
      id: 'step2',
      title: 'Step 2',
      content: 'This is step 2',
      targetSelector: '[data-test="step2-target"]'
    })
  ]
});

// Test Component for Tutorial Context
const TestTutorialComponent = ({ onTutorialChange }) => {
  const tutorial = useTutorial();

  React.useEffect(() => {
    if (onTutorialChange) {
      onTutorialChange(tutorial);
    }
  }, [tutorial, onTutorialChange]);

  return (
    <div>
      <div data-test="step1-target">Step 1 Target</div>
      <div data-test="step2-target">Step 2 Target</div>
      <button onClick={() => tutorial.startTutorial('test_tutorial')}>
        Start Tutorial
      </button>
      <button onClick={() => tutorial.nextStep()}>Next Step</button>
      <button onClick={() => tutorial.previousStep()}>Previous Step</button>
      <button onClick={() => tutorial.skipTutorial()}>Skip Tutorial</button>
      <div data-testid="tutorial-status">
        {tutorial.isActive ? 'Active' : 'Inactive'}
      </div>
      <div data-testid="current-step">
        {tutorial.currentStep?.title || 'No Step'}
      </div>
    </div>
  );
};

const renderWithTutorialProvider = (component, userId = 'test-user') => {
  return render(
    <TutorialProvider userId={userId}>
      {component}
    </TutorialProvider>
  );
};

describe('Tutorial System', () => {
  beforeEach(() => {
    mockLocalStorage.clear();
    jest.clearAllMocks();
  });

  describe('TutorialStorage', () => {
    test('should save and retrieve tutorial progress', () => {
      const progress = {
        tutorialId: 'test_tutorial',
        userId: 'test-user',
        status: TUTORIAL_STATUS.IN_PROGRESS,
        currentStepIndex: 1,
        completedSteps: [0],
        startedAt: new Date().toISOString()
      };

      tutorialStorage.saveTutorialProgress(progress);
      const retrieved = tutorialStorage.getTutorialProgress('test_tutorial', 'test-user');

      expect(retrieved.status).toBe(TUTORIAL_STATUS.IN_PROGRESS);
      expect(retrieved.currentStepIndex).toBe(1);
      expect(retrieved.completedSteps).toEqual([0]);
    });

    test('should save and retrieve tutorial preferences', () => {
      const preferences = {
        autoStartTutorials: true,
        showContextualHelp: false,
        animationSpeed: 'fast'
      };

      tutorialStorage.saveTutorialPreferences(preferences);
      const retrieved = tutorialStorage.getTutorialPreferences();

      expect(retrieved.autoStartTutorials).toBe(true);
      expect(retrieved.showContextualHelp).toBe(false);
      expect(retrieved.animationSpeed).toBe('fast');
    });

    test('should handle localStorage unavailability gracefully', () => {
      // Mock localStorage to throw error
      const originalSetItem = mockLocalStorage.setItem;
      mockLocalStorage.setItem = jest.fn(() => {
        throw new Error('localStorage not available');
      });

      const progress = {
        tutorialId: 'test_tutorial',
        userId: 'test-user',
        status: TUTORIAL_STATUS.COMPLETED
      };

      // Should not throw error
      expect(() => {
        tutorialStorage.saveTutorialProgress(progress);
      }).not.toThrow();

      // Restore original function
      mockLocalStorage.setItem = originalSetItem;
    });
  });

  describe('TutorialManager', () => {
    test('should register and start tutorials', async () => {
      let tutorialContext;

      renderWithTutorialProvider(
        <TestTutorialComponent onTutorialChange={ctx => tutorialContext = ctx} />
      );

      await waitFor(() => {
        expect(tutorialContext).toBeDefined();
      });

      // Register tutorial
      act(() => {
        tutorialContext.registerTutorial(testTutorial);
      });

      // Start tutorial
      const startButton = screen.getByText('Start Tutorial');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Active');
        expect(screen.getByTestId('current-step')).toHaveTextContent('Step 1');
      });
    });

    test('should navigate through tutorial steps', async () => {
      let tutorialContext;

      renderWithTutorialProvider(
        <TestTutorialComponent onTutorialChange={ctx => tutorialContext = ctx} />
      );

      await waitFor(() => {
        expect(tutorialContext).toBeDefined();
      });

      act(() => {
        tutorialContext.registerTutorial(testTutorial);
      });

      // Start tutorial
      fireEvent.click(screen.getByText('Start Tutorial'));

      await waitFor(() => {
        expect(screen.getByTestId('current-step')).toHaveTextContent('Step 1');
      });

      // Go to next step
      fireEvent.click(screen.getByText('Next Step'));

      await waitFor(() => {
        expect(screen.getByTestId('current-step')).toHaveTextContent('Step 2');
      });

      // Go back to previous step
      fireEvent.click(screen.getByText('Previous Step'));

      await waitFor(() => {
        expect(screen.getByTestId('current-step')).toHaveTextContent('Step 1');
      });
    });

    test('should complete tutorial when reaching final step', async () => {
      let tutorialContext;

      renderWithTutorialProvider(
        <TestTutorialComponent onTutorialChange={ctx => tutorialContext = ctx} />
      );

      await waitFor(() => {
        expect(tutorialContext).toBeDefined();
      });

      act(() => {
        tutorialContext.registerTutorial(testTutorial);
      });

      // Start tutorial
      fireEvent.click(screen.getByText('Start Tutorial'));

      // Navigate to last step
      fireEvent.click(screen.getByText('Next Step'));

      await waitFor(() => {
        expect(screen.getByTestId('current-step')).toHaveTextContent('Step 2');
      });

      // Complete tutorial
      fireEvent.click(screen.getByText('Next Step'));

      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Inactive');
      });
    });

    test('should skip tutorial', async () => {
      let tutorialContext;

      renderWithTutorialProvider(
        <TestTutorialComponent onTutorialChange={ctx => tutorialContext = ctx} />
      );

      await waitFor(() => {
        expect(tutorialContext).toBeDefined();
      });

      act(() => {
        tutorialContext.registerTutorial(testTutorial);
      });

      // Start tutorial
      fireEvent.click(screen.getByText('Start Tutorial'));

      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Active');
      });

      // Skip tutorial
      fireEvent.click(screen.getByText('Skip Tutorial'));

      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Inactive');
      });
    });
  });

  describe('TutorialOverlay', () => {
    test('should render overlay when tutorial is active', async () => {
      renderWithTutorialProvider(
        <>
          <TestTutorialComponent />
          <TutorialOverlay />
        </>
      );

      // Wait for tutorial context to be available
      await waitFor(() => {
        expect(screen.getByText('Start Tutorial')).toBeInTheDocument();
      });

      // Register and start tutorial
      const startButton = screen.getByText('Start Tutorial');
      fireEvent.click(startButton);

      // Check if overlay elements are present
      await waitFor(() => {
        expect(document.querySelector('[data-tutorial-overlay]')).toBeInTheDocument();
      });
    });

    test('should handle keyboard navigation', async () => {
      renderWithTutorialProvider(
        <>
          <TestTutorialComponent />
          <TutorialOverlay />
        </>
      );

      // Start tutorial
      fireEvent.click(screen.getByText('Start Tutorial'));

      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Active');
      });

      // Test keyboard navigation
      fireEvent.keyDown(document, { key: 'ArrowRight' });

      await waitFor(() => {
        expect(screen.getByTestId('current-step')).toHaveTextContent('Step 2');
      });

      fireEvent.keyDown(document, { key: 'ArrowLeft' });

      await waitFor(() => {
        expect(screen.getByTestId('current-step')).toHaveTextContent('Step 1');
      });

      fireEvent.keyDown(document, { key: 'Escape' });

      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Inactive');
      });
    });
  });

  describe('TutorialProgress', () => {
    test('should display tutorial progress correctly', async () => {
      // Set up some progress data
      const progress = {
        tutorialId: 'test_tutorial',
        userId: 'test-user',
        status: TUTORIAL_STATUS.COMPLETED,
        completedSteps: [0, 1],
        completedAt: new Date().toISOString()
      };

      tutorialStorage.saveTutorialProgress(progress);

      renderWithTutorialProvider(
        <TutorialProgress userId="test-user" />
      );

      // Should show progress information
      await waitFor(() => {
        expect(screen.getByText(/completed/i)).toBeInTheDocument();
      });
    });
  });

  describe('ContextualHelp', () => {
    test('should show contextual help on hover', async () => {
      const user = userEvent.setup();

      renderWithTutorialProvider(
        <>
          <div data-help-context="component-palette">Component Palette</div>
          <ContextualHelp />
        </>
      );

      const element = screen.getByText('Component Palette');

      // Hover over element
      await user.hover(element);

      // Wait for help to appear (with delay)
      await waitFor(() => {
        // Check for help content or tooltip
        const helpElement = document.querySelector('[data-tutorial-help]') ||
          document.querySelector('.ant-tooltip') ||
          screen.queryByText(/help/i);
        expect(helpElement).toBeInTheDocument();
      }, { timeout: 3000 });
    });
  });

  describe('TutorialTrigger', () => {
    test('should render tutorial trigger button', () => {
      renderWithTutorialProvider(<TutorialTrigger />);

      // Should render floating action button
      expect(document.querySelector('.ant-float-btn')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('should announce tutorial start to screen readers', async () => {
      // Mock screen reader announcement
      const mockAnnounce = jest.fn();
      window.speechSynthesis = {
        speak: mockAnnounce
      };

      renderWithTutorialProvider(
        <TestTutorialComponent />
      );

      // Start tutorial
      fireEvent.click(screen.getByText('Start Tutorial'));

      // Should announce tutorial start
      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Active');
      });
    });

    test('should handle keyboard navigation properly', async () => {
      renderWithTutorialProvider(
        <TestTutorialComponent />
      );

      // Start tutorial
      fireEvent.click(screen.getByText('Start Tutorial'));

      await waitFor(() => {
        expect(screen.getByTestId('tutorial-status')).toHaveTextContent('Active');
      });

      // Test all keyboard shortcuts
      const keyTests = [
        { key: 'ArrowRight', expectedStep: 'Step 2' },
        { key: 'ArrowLeft', expectedStep: 'Step 1' },
        { key: ' ', expectedStatus: 'Active' }, // Pause/resume
        { key: 'Escape', expectedStatus: 'Inactive' } // Exit
      ];

      for (const test of keyTests) {
        fireEvent.keyDown(document, { key: test.key });

        if (test.expectedStep) {
          await waitFor(() => {
            expect(screen.getByTestId('current-step')).toHaveTextContent(test.expectedStep);
          });
        }

        if (test.expectedStatus) {
          await waitFor(() => {
            expect(screen.getByTestId('tutorial-status')).toHaveTextContent(test.expectedStatus);
          });
        }
      }
    });
  });

  describe('Integration with App Builder', () => {
    test('should work with existing Ant Design components', () => {
      const { container } = renderWithTutorialProvider(
        <div>
          <button className="ant-btn">Ant Design Button</button>
          <TutorialTrigger />
        </div>
      );

      // Should not interfere with existing components
      expect(container.querySelector('.ant-btn')).toBeInTheDocument();
      expect(container.querySelector('.ant-float-btn')).toBeInTheDocument();
    });

    test('should handle theme changes', async () => {
      const ThemeTestComponent = () => {
        const [theme, setTheme] = React.useState('light');

        return (
          <div data-theme={theme}>
            <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
              Toggle Theme
            </button>
            <TutorialTrigger />
          </div>
        );
      };

      renderWithTutorialProvider(<ThemeTestComponent />);

      const toggleButton = screen.getByText('Toggle Theme');
      fireEvent.click(toggleButton);

      // Should handle theme changes without errors
      expect(document.querySelector('[data-theme="dark"]')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('should handle missing tutorial gracefully', async () => {
      let tutorialContext;

      renderWithTutorialProvider(
        <TestTutorialComponent onTutorialChange={ctx => tutorialContext = ctx} />
      );

      await waitFor(() => {
        expect(tutorialContext).toBeDefined();
      });

      // Try to start non-existent tutorial
      const result = tutorialContext.startTutorial('non_existent_tutorial');

      expect(result).toBe(false);
    });

    test('should handle corrupted localStorage data', () => {
      // Set corrupted data
      mockLocalStorage.setItem('app_builder_tutorial_progress', 'invalid json');

      // Should not throw error and return default values
      expect(() => {
        const progress = tutorialStorage.getTutorialProgress('test_tutorial');
        expect(progress.status).toBe(TUTORIAL_STATUS.NOT_STARTED);
      }).not.toThrow();
    });
  });
});

// Performance Tests
describe('Tutorial System Performance', () => {
  test('should handle large number of tutorials efficiently', async () => {
    const largeTutorialSet = Array.from({ length: 100 }, (_, i) =>
      createTutorial({
        id: `tutorial_${i}`,
        title: `Tutorial ${i}`,
        description: `Description ${i}`,
        category: TUTORIAL_CATEGORIES.BEGINNER,
        steps: [
          createTutorialStep({
            id: `step_${i}_1`,
            title: `Step 1`,
            content: `Content ${i}`
          })
        ]
      })
    );

    let tutorialContext;

    const start = performance.now();

    renderWithTutorialProvider(
      <TestTutorialComponent onTutorialChange={ctx => tutorialContext = ctx} />
    );

    await waitFor(() => {
      expect(tutorialContext).toBeDefined();
    });

    // Register all tutorials
    act(() => {
      largeTutorialSet.forEach(tutorial => {
        tutorialContext.registerTutorial(tutorial);
      });
    });

    const end = performance.now();

    // Should complete within reasonable time (less than 1 second)
    expect(end - start).toBeLessThan(1000);

    // Should have all tutorials registered
    expect(tutorialContext.getAllTutorials()).toHaveLength(100);
  });
});
