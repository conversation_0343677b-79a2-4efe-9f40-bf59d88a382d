import React, { useState, useEffect } from 'react';

/**
 * Network Status Indicator Component
 * 
 * Displays the current network status (online/offline) and monitors for changes.
 */
const NetworkStatusIndicator = ({ showOfflineOnly = false }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showIndicator, setShowIndicator] = useState(!navigator.onLine);
  
  useEffect(() => {
    // Update online status
    const handleOnline = () => {
      setIsOnline(true);
      
      // Hide the indicator after a delay when coming back online
      setTimeout(() => {
        setShowIndicator(false);
      }, 3000);
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setShowIndicator(true);
    };
    
    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Clean up event listeners
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Don't render anything if we're online and showOfflineOnly is true
  if (showOfflineOnly && isOnline && !showIndicator) {
    return null;
  }
  
  // Styles for the indicator
  const styles = {
    container: {
      position: 'fixed',
      bottom: '20px',
      left: '20px',
      padding: '8px 16px',
      borderRadius: '4px',
      color: 'white',
      fontWeight: 'bold',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
      transition: 'background-color 0.3s ease',
      backgroundColor: isOnline ? '#52c41a' : '#f5222d',
      opacity: showIndicator ? 1 : 0,
      pointerEvents: showIndicator ? 'auto' : 'none',
    },
    dot: {
      width: '10px',
      height: '10px',
      borderRadius: '50%',
      backgroundColor: isOnline ? '#52c41a' : '#f5222d',
      marginRight: '8px',
      animation: isOnline ? 'none' : 'pulse 1.5s infinite'
    },
    '@keyframes pulse': {
      '0%': { opacity: 1 },
      '50%': { opacity: 0.5 },
      '100%': { opacity: 1 }
    }
  };
  
  // Add the keyframes animation to the document
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  
  return (
    <div style={styles.container} role="status" aria-live="polite">
      <div style={styles.dot}></div>
      <span>{isOnline ? 'Online' : 'Offline - Check your connection'}</span>
    </div>
  );
};

export default NetworkStatusIndicator;
