import React, { useState, useEffect } from 'react';
import { disableMockApiServer } from '../utils/mockApiServer';
import { disableMockWebSocketServer } from '../utils/mockWebSocketServer';

const DevTools = () => {
  const [mocksEnabled, setMocksEnabled] = useState(window.MOCK_SERVERS_ENABLED);
  
  const toggleMocks = () => {
    if (mocksEnabled) {
      // Disable mocks
      disableMockApiServer();
      disableMockWebSocketServer();
      window.MOCK_SERVERS_ENABLED = false;
      setMocksEnabled(false);
      
      // Force page reload to ensure clean state
      window.location.reload();
    } else {
      // Enable mocks requires reload with different env
      alert('To enable mocks, please restart the app without REACT_APP_USE_REAL_API');
    }
  };
  
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  return (
    <div className="dev-tools">
      <div className="dev-panel">
        <h3>Developer Tools</h3>
        <div className="toggle-row">
          <label>
            <input 
              type="checkbox" 
              checked={mocksEnabled} 
              onChange={toggleMocks} 
            />
            Use Mock API/WebSocket
          </label>
          <div className="status-indicator" data-status={mocksEnabled ? 'active' : 'inactive'}>
            {mocksEnabled ? 'MOCK' : 'REAL'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DevTools;