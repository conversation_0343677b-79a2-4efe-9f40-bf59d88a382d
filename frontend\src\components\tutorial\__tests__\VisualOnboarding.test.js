/**
 * Visual Onboarding Tutorial Tests
 * 
 * Specific tests for the visual onboarding tutorial implementation
 * including step validation, user interactions, and progress tracking.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import { TutorialProvider } from '../TutorialManager';
import TutorialOverlay from '../TutorialOverlay';
import TutorialEntryPoint from '../TutorialEntryPoint';
import { 
  tutorialValidations, 
  tutorialEventHandlers,
  WelcomeModal,
  SuccessModal,
  CompletionModal,
  isNewUser,
  markOnboardingCompleted
} from '../VisualOnboardingHelpers';
import { TUTORIAL_DEFINITIONS } from '../TutorialContent';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// Mock confetti
jest.mock('canvas-confetti', () => jest.fn());

describe('Visual Onboarding Tutorial', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('Tutorial Definition', () => {
    test('visual_onboarding tutorial is properly defined', () => {
      const tutorial = TUTORIAL_DEFINITIONS.visual_onboarding;
      
      expect(tutorial).toBeDefined();
      expect(tutorial.id).toBe('visual_onboarding');
      expect(tutorial.title).toBe('Visual App Builder Onboarding');
      expect(tutorial.isOnboarding).toBe(true);
      expect(tutorial.steps).toHaveLength(16);
    });

    test('tutorial steps have correct structure', () => {
      const tutorial = TUTORIAL_DEFINITIONS.visual_onboarding;
      
      tutorial.steps.forEach((step, index) => {
        expect(step.id).toBeDefined();
        expect(step.title).toBeDefined();
        expect(step.content).toBeDefined();
        expect(step.type).toBeDefined();
        
        // First step should not have previous button
        if (index === 0) {
          expect(step.showPrevious).toBe(false);
        }
      });
    });

    test('interactive steps have validation functions', () => {
      const tutorial = TUTORIAL_DEFINITIONS.visual_onboarding;
      const interactiveSteps = tutorial.steps.filter(step => 
        step.type === 'interactive'
      );
      
      interactiveSteps.forEach(step => {
        expect(step.validationFn).toBeDefined();
        expect(step.requiredAction).toBeDefined();
        expect(step.targetSelector).toBeDefined();
      });
    });
  });

  describe('Validation Functions', () => {
    test('validateComponentAdded validates correctly', () => {
      // Test with components
      expect(tutorialValidations.validateComponentAdded({
        components: [{ id: 1, type: 'button' }]
      })).toBe(true);

      // Test without components
      expect(tutorialValidations.validateComponentAdded({
        components: []
      })).toBe(false);

      // Test with null/undefined
      expect(tutorialValidations.validateComponentAdded({
        components: null
      })).toBe(false);
    });

    test('validateComponentSelected validates correctly', () => {
      expect(tutorialValidations.validateComponentSelected({
        selectedComponent: { id: 1 }
      })).toBe(true);

      expect(tutorialValidations.validateComponentSelected({
        selectedComponent: null
      })).toBe(false);
    });

    test('validateTextChanged validates correctly', () => {
      // Test with changed text
      expect(tutorialValidations.validateTextChanged({
        selectedComponent: {
          props: { children: 'My First Button' }
        }
      })).toBe(true);

      // Test with text property
      expect(tutorialValidations.validateTextChanged({
        selectedComponent: {
          props: { text: 'My First Button' }
        }
      })).toBe(true);

      // Test with default text
      expect(tutorialValidations.validateTextChanged({
        selectedComponent: {
          props: { children: 'Button' }
        }
      })).toBe(false);

      // Test with no component
      expect(tutorialValidations.validateTextChanged({
        selectedComponent: null
      })).toBe(false);
    });

    test('validateButtonTypeChanged validates correctly', () => {
      expect(tutorialValidations.validateButtonTypeChanged({
        selectedComponent: {
          props: { type: 'primary' }
        }
      })).toBe(true);

      expect(tutorialValidations.validateButtonTypeChanged({
        selectedComponent: {
          props: { type: 'default' }
        }
      })).toBe(false);
    });

    test('validateSecondComponentAdded validates correctly', () => {
      expect(tutorialValidations.validateSecondComponentAdded({
        components: [{ id: 1 }, { id: 2 }]
      })).toBe(true);

      expect(tutorialValidations.validateSecondComponentAdded({
        components: [{ id: 1 }]
      })).toBe(false);
    });

    test('validatePreviewMode validates correctly', () => {
      expect(tutorialValidations.validatePreviewMode({
        previewMode: true
      })).toBe(true);

      expect(tutorialValidations.validatePreviewMode({
        previewMode: false
      })).toBe(false);
    });

    test('validateEditMode validates correctly', () => {
      expect(tutorialValidations.validateEditMode({
        previewMode: false
      })).toBe(true);

      expect(tutorialValidations.validateEditMode({
        previewMode: true
      })).toBe(false);
    });
  });

  describe('Event Handlers', () => {
    beforeEach(() => {
      // Setup DOM for testing
      document.body.innerHTML = `
        <button data-tutorial-target="component-button">Button</button>
        <input data-tutorial-target="property-text-input" value="" />
        <div data-tutorial-target="other-element">Other</div>
      `;
    });

    test('highlightComponentButton adds visual effects', () => {
      const button = document.querySelector('[data-tutorial-target="component-button"]');
      
      tutorialEventHandlers.highlightComponentButton();
      
      expect(button.style.animation).toContain('pulse');
      expect(button.style.boxShadow).toBeTruthy();
    });

    test('clearHighlights removes all effects', () => {
      const elements = document.querySelectorAll('[data-tutorial-target]');
      
      // Add some styles first
      elements.forEach(el => {
        el.style.animation = 'pulse 1s infinite';
        el.style.boxShadow = '0 0 10px blue';
      });
      
      tutorialEventHandlers.clearHighlights();
      
      elements.forEach(el => {
        expect(el.style.animation).toBe('');
        expect(el.style.boxShadow).toBe('');
      });
    });

    test('focusTextInput focuses and selects input', async () => {
      const input = document.querySelector('[data-tutorial-target="property-text-input"]');
      const focusSpy = jest.spyOn(input, 'focus');
      const selectSpy = jest.spyOn(input, 'select');
      
      tutorialEventHandlers.focusTextInput();
      
      await waitFor(() => {
        expect(focusSpy).toHaveBeenCalled();
        expect(selectSpy).toHaveBeenCalled();
      }, { timeout: 1000 });
    });

    test('celebrateCompletion triggers effects', () => {
      // Mock confetti
      const mockConfetti = jest.fn();
      window.confetti = mockConfetti;
      
      // Mock sounds
      window.tutorialSounds = {
        success: { play: jest.fn() }
      };
      
      tutorialEventHandlers.celebrateCompletion();
      
      expect(mockConfetti).toHaveBeenCalledWith({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
      
      expect(window.tutorialSounds.success.play).toHaveBeenCalled();
    });
  });

  describe('Custom Modal Components', () => {
    test('WelcomeModal renders correctly', () => {
      const mockOnNext = jest.fn();
      
      render(<WelcomeModal onNext={mockOnNext} />);
      
      expect(screen.getByText(/Welcome to App Builder!/)).toBeInTheDocument();
      expect(screen.getByText(/Component Palette/)).toBeInTheDocument();
      expect(screen.getByText(/Preview Area/)).toBeInTheDocument();
      expect(screen.getByText(/Property Editor/)).toBeInTheDocument();
    });

    test('SuccessModal renders correctly', () => {
      const mockOnNext = jest.fn();
      
      render(<SuccessModal onNext={mockOnNext} />);
      
      expect(screen.getByText(/Great Job!/)).toBeInTheDocument();
      expect(screen.getByText(/What just happened/)).toBeInTheDocument();
      expect(screen.getByText(/button component was added/)).toBeInTheDocument();
    });

    test('CompletionModal renders correctly', () => {
      const mockOnComplete = jest.fn();
      
      render(<CompletionModal onComplete={mockOnComplete} />);
      
      expect(screen.getByText(/Congratulations!/)).toBeInTheDocument();
      expect(screen.getByText(/Skills Unlocked/)).toBeInTheDocument();
      expect(screen.getByText(/Next Steps/)).toBeInTheDocument();
    });
  });

  describe('User Detection Functions', () => {
    test('isNewUser detects new users correctly', () => {
      // Mock new user
      mockLocalStorage.getItem.mockReturnValue(null);
      expect(isNewUser()).toBe(true);
      
      // Mock returning user
      mockLocalStorage.getItem.mockReturnValue('true');
      expect(isNewUser()).toBe(false);
    });

    test('markOnboardingCompleted sets localStorage', () => {
      markOnboardingCompleted();
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'app_builder_onboarding_completed',
        'true'
      );
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'app_builder_onboarding_completed_at',
        expect.any(String)
      );
    });
  });

  describe('Tutorial Entry Point', () => {
    test('shows welcome card for new users', () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      
      render(
        <TutorialProvider userId="test-user">
          <TutorialEntryPoint autoShow={true} />
        </TutorialProvider>
      );
      
      expect(screen.getByText(/Welcome to App Builder!/)).toBeInTheDocument();
      expect(screen.getByText(/Start Interactive Tutorial/)).toBeInTheDocument();
    });

    test('shows quick start for returning users', () => {
      mockLocalStorage.getItem.mockReturnValue('true');
      
      render(
        <TutorialProvider userId="test-user">
          <TutorialEntryPoint showWelcomeCard={false} />
        </TutorialProvider>
      );
      
      expect(screen.getByText(/New to App Builder/)).toBeInTheDocument();
    });

    test('handles dismiss correctly', async () => {
      const mockOnDismiss = jest.fn();
      mockLocalStorage.getItem.mockReturnValue(null);
      
      render(
        <TutorialProvider userId="test-user">
          <TutorialEntryPoint autoShow={true} onDismiss={mockOnDismiss} />
        </TutorialProvider>
      );
      
      const dismissButton = screen.getByRole('button', { name: /close/i });
      await userEvent.click(dismissButton);
      
      expect(mockOnDismiss).toHaveBeenCalled();
    });
  });

  describe('Integration with Tutorial System', () => {
    test('tutorial starts correctly from entry point', async () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      
      render(
        <TutorialProvider userId="test-user">
          <TutorialEntryPoint autoShow={true} />
          <TutorialOverlay />
        </TutorialProvider>
      );
      
      const startButton = screen.getByText(/Start Interactive Tutorial/);
      await userEvent.click(startButton);
      
      await waitFor(() => {
        expect(document.querySelector('.ant-modal')).toBeInTheDocument();
      });
    });

    test('tutorial progress is tracked correctly', async () => {
      const mockContext = {
        components: [],
        selectedComponent: null,
        previewMode: false
      };
      
      // Simulate tutorial progression
      expect(tutorialValidations.validateComponentAdded(mockContext)).toBe(false);
      
      // Add component
      mockContext.components = [{ id: 1, type: 'button' }];
      expect(tutorialValidations.validateComponentAdded(mockContext)).toBe(true);
      
      // Select component
      mockContext.selectedComponent = { id: 1, props: {} };
      expect(tutorialValidations.validateComponentSelected(mockContext)).toBe(true);
      
      // Change text
      mockContext.selectedComponent.props.children = 'My First Button';
      expect(tutorialValidations.validateTextChanged(mockContext)).toBe(true);
      
      // Change type
      mockContext.selectedComponent.props.type = 'primary';
      expect(tutorialValidations.validateButtonTypeChanged(mockContext)).toBe(true);
      
      // Add second component
      mockContext.components.push({ id: 2, type: 'typography' });
      expect(tutorialValidations.validateSecondComponentAdded(mockContext)).toBe(true);
      
      // Preview mode
      mockContext.previewMode = true;
      expect(tutorialValidations.validatePreviewMode(mockContext)).toBe(true);
      
      // Edit mode
      mockContext.previewMode = false;
      expect(tutorialValidations.validateEditMode(mockContext)).toBe(true);
    });
  });
});
